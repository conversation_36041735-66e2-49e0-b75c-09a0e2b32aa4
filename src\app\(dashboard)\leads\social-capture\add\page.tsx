'use client';

import { useRouter } from 'next/navigation';
import { PageLayout } from '@/components/layout/page-layout';
import { SocialCaptureForm } from '@/components/social-capture/social-capture-form';
import { Button } from '@/components/ui/button';
import { ArrowLeftIcon } from '@heroicons/react/24/outline';

export default function AddSocialCapturePage() {
  const router = useRouter();

  const handleSuccess = () => {
    // Redirect back to social capture list
    router.push('/leads/social-capture');
  };

  const handleCancel = () => {
    router.back();
  };

  const actions = (
    <Button
      variant="outline"
      onClick={() => router.back()}
      className="flex items-center space-x-2"
    >
      <ArrowLeftIcon className="h-4 w-4" />
      <span>Back</span>
    </Button>
  );

  return (
    <PageLayout
      title="Add Social Media Capture"
      description="Manually capture a social media post for lead processing and analysis"
      actions={actions}
    >
      <div className="max-w-4xl mx-auto">
        <SocialCaptureForm
          onSuccess={handleSuccess}
          onCancel={handleCancel}
        />
      </div>
    </PageLayout>
  );
}
