'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { CalendarIcon, Plus, Edit, Trash2, Flag, CheckCircle, Clock, AlertTriangle } from 'lucide-react';
import { DateInput } from '@/components/ui/date-input';
import { cn } from '@/lib/utils';
import { useToast } from '@/hooks/use-toast';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { EmptyState } from '@/components/ui/empty-state';

interface Milestone {
  id: string;
  title: string;
  description?: string;
  status: string;
  dueDate: string;
  progress: number;
  completedAt?: string;
  dependsOnTasks: string[];
}

interface Task {
  id: string;
  title: string;
  status: string;
}

interface MilestoneManagementProps {
  projectId: string;
  canManage?: boolean;
}

export function MilestoneManagement({ projectId, canManage = false }: MilestoneManagementProps) {
  const [milestones, setMilestones] = useState<Milestone[]>([]);
  const [tasks, setTasks] = useState<Task[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [showAddDialog, setShowAddDialog] = useState(false);
  const [editingMilestone, setEditingMilestone] = useState<Milestone | null>(null);
  const { toast } = useToast();

  // Form state
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    dueDate: new Date(),
    dependsOnTasks: [] as string[],
  });

  useEffect(() => {
    fetchData();
  }, [projectId]);

  const fetchData = async () => {
    try {
      setLoading(true);
      
      const [milestonesRes, tasksRes] = await Promise.all([
        fetch(`/api/projects/${projectId}/milestones`),
        fetch(`/api/projects/${projectId}/tasks?limit=100`),
      ]);

      if (milestonesRes.ok) {
        const milestonesData = await milestonesRes.json();
        setMilestones(milestonesData.data.milestones || []);
      }

      if (tasksRes.ok) {
        const tasksData = await tasksRes.json();
        setTasks(tasksData.data.tasks || []);
      }
    } catch (error) {
      console.error('Error fetching data:', error);
      toast({
        title: 'Error',
        description: 'Failed to load milestone data',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const resetForm = () => {
    setFormData({
      title: '',
      description: '',
      dueDate: new Date(),
      dependsOnTasks: [],
    });
    setEditingMilestone(null);
  };

  const handleEdit = (milestone: Milestone) => {
    setFormData({
      title: milestone.title,
      description: milestone.description || '',
      dueDate: new Date(milestone.dueDate),
      dependsOnTasks: milestone.dependsOnTasks,
    });
    setEditingMilestone(milestone);
    setShowAddDialog(true);
  };

  const handleSubmit = async () => {
    try {
      setSaving(true);

      const payload = {
        title: formData.title,
        description: formData.description,
        dueDate: formData.dueDate.toISOString(),
        dependsOnTasks: formData.dependsOnTasks,
      };

      const url = editingMilestone 
        ? `/api/projects/${projectId}/milestones/${editingMilestone.id}`
        : `/api/projects/${projectId}/milestones`;
      
      const method = editingMilestone ? 'PATCH' : 'POST';

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to save milestone');
      }

      toast({
        title: 'Success',
        description: `Milestone ${editingMilestone ? 'updated' : 'created'} successfully`,
      });

      setShowAddDialog(false);
      resetForm();
      fetchData();
    } catch (error) {
      console.error('Error saving milestone:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to save milestone',
        variant: 'destructive',
      });
    } finally {
      setSaving(false);
    }
  };

  const handleDelete = async (milestoneId: string) => {
    if (!confirm('Are you sure you want to delete this milestone?')) {
      return;
    }

    try {
      const response = await fetch(`/api/projects/${projectId}/milestones/${milestoneId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete milestone');
      }

      toast({
        title: 'Success',
        description: 'Milestone deleted successfully',
      });

      fetchData();
    } catch (error) {
      console.error('Error deleting milestone:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to delete milestone',
        variant: 'destructive',
      });
    }
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      pending: { label: 'Pending', color: 'bg-gray-100 text-gray-800', icon: Clock },
      in_progress: { label: 'In Progress', color: 'bg-blue-100 text-blue-800', icon: Clock },
      completed: { label: 'Completed', color: 'bg-green-100 text-green-800', icon: CheckCircle },
      overdue: { label: 'Overdue', color: 'bg-red-100 text-red-800', icon: AlertTriangle },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending;
    const Icon = config.icon;
    
    return (
      <Badge className={config.color}>
        <Icon className="w-3 h-3 mr-1" />
        {config.label}
      </Badge>
    );
  };

  const formatDate = (dateString: string | Date) => {
    const date = typeof dateString === 'string' ? new Date(dateString) : dateString;
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const isOverdue = (milestone: Milestone) => {
    return new Date(milestone.dueDate) < new Date() && milestone.status !== 'completed';
  };

  const getDependentTasksStatus = (dependsOnTasks: string[]) => {
    if (dependsOnTasks.length === 0) return { completed: 0, total: 0 };
    
    const dependentTasks = tasks.filter(task => dependsOnTasks.includes(task.id));
    const completed = dependentTasks.filter(task => task.status === 'done').length;
    
    return { completed, total: dependentTasks.length };
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Project Milestones</h2>
          <p className="text-muted-foreground">Track key project deliverables and deadlines</p>
        </div>
        {canManage && (
          <Dialog open={showAddDialog} onOpenChange={setShowAddDialog}>
            <DialogTrigger asChild>
              <Button onClick={resetForm}>
                <Plus className="h-4 w-4 mr-2" />
                Add Milestone
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-md">
              <DialogHeader>
                <DialogTitle>{editingMilestone ? 'Edit Milestone' : 'Add Milestone'}</DialogTitle>
                <DialogDescription>
                  {editingMilestone ? 'Update milestone details' : 'Create a new project milestone'}
                </DialogDescription>
              </DialogHeader>
              
              <div className="space-y-4">
                <div>
                  <Label htmlFor="title">Title</Label>
                  <Input
                    id="title"
                    value={formData.title}
                    onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                    placeholder="Enter milestone title"
                  />
                </div>

                <div>
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    value={formData.description}
                    onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                    placeholder="Enter milestone description"
                    rows={3}
                  />
                </div>

                <DateInput
                  label="Due Date"
                  value={formData.dueDate}
                  onChange={(date) => setFormData({ ...formData, dueDate: date || new Date() })}
                  placeholder="Select due date"
                  min={new Date().toISOString().split('T')[0]}
                  required
                />

                {tasks.length > 0 && (
                  <div>
                    <Label>Depends on Tasks (Optional)</Label>
                    <div className="mt-2 max-h-32 overflow-y-auto border rounded-md p-2">
                      {tasks.map((task) => (
                        <div key={task.id} className="flex items-center space-x-2 py-1">
                          <input
                            type="checkbox"
                            id={`task-${task.id}`}
                            checked={formData.dependsOnTasks.includes(task.id)}
                            onChange={(e) => {
                              if (e.target.checked) {
                                setFormData({
                                  ...formData,
                                  dependsOnTasks: [...formData.dependsOnTasks, task.id]
                                });
                              } else {
                                setFormData({
                                  ...formData,
                                  dependsOnTasks: formData.dependsOnTasks.filter(id => id !== task.id)
                                });
                              }
                            }}
                            className="rounded"
                          />
                          <label htmlFor={`task-${task.id}`} className="text-sm">
                            {task.title}
                          </label>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                <div className="flex justify-end space-x-2 pt-4">
                  <Button variant="outline" onClick={() => setShowAddDialog(false)}>
                    Cancel
                  </Button>
                  <Button onClick={handleSubmit} disabled={saving || !formData.title}>
                    {saving && <LoadingSpinner size="sm" className="mr-2" />}
                    {editingMilestone ? 'Update' : 'Create'} Milestone
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        )}
      </div>

      {/* Milestone List */}
      {milestones.length === 0 ? (
        <Card>
          <CardContent className="pt-6">
            <EmptyState
              icon={<Flag className="w-12 h-12 text-muted-foreground" />}
              title="No milestones defined"
              description="Create milestones to track key project deliverables"
              action={canManage ? {
                label: 'Add Milestone',
                onClick: () => setShowAddDialog(true),
                variant: 'primary'
              } : undefined}
            />
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-4">
          {milestones.map((milestone) => {
            const dependencyStatus = getDependentTasksStatus(milestone.dependsOnTasks);
            const status = isOverdue(milestone) ? 'overdue' : milestone.status;
            
            return (
              <Card key={milestone.id} className={isOverdue(milestone) ? 'border-red-200' : ''}>
                <CardContent className="pt-6">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <h3 className="font-semibold text-lg">{milestone.title}</h3>
                        {getStatusBadge(status)}
                      </div>
                      
                      {milestone.description && (
                        <p className="text-muted-foreground mb-3">{milestone.description}</p>
                      )}
                      
                      <div className="space-y-3">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2 text-sm">
                            <CalendarIcon className="w-4 h-4" />
                            <span>Due: {formatDate(milestone.dueDate)}</span>
                            {milestone.completedAt && (
                              <span className="text-green-600">
                                (Completed: {formatDate(milestone.completedAt)})
                              </span>
                            )}
                          </div>
                          <div className="flex items-center gap-2">
                            <span className="text-sm font-medium">{Math.round(milestone.progress)}%</span>
                            <Progress value={milestone.progress} className="w-24" />
                          </div>
                        </div>
                        
                        {milestone.dependsOnTasks.length > 0 && (
                          <div className="text-sm">
                            <span className="font-medium">Dependencies: </span>
                            <span className="text-muted-foreground">
                              {dependencyStatus.completed} of {dependencyStatus.total} tasks completed
                            </span>
                            <Progress 
                              value={dependencyStatus.total > 0 ? (dependencyStatus.completed / dependencyStatus.total) * 100 : 0} 
                              className="w-32 mt-1" 
                            />
                          </div>
                        )}
                      </div>
                    </div>
                    
                    {canManage && (
                      <div className="flex items-center gap-2">
                        <Button variant="outline" size="sm" onClick={() => handleEdit(milestone)}>
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button 
                          variant="outline" 
                          size="sm" 
                          onClick={() => handleDelete(milestone.id)}
                          className="text-red-600 hover:text-red-700"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>
      )}
    </div>
  );
}
