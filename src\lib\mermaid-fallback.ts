/**
 * Mermaid Fallback Rendering Utility
 * 
 * This utility provides a fallback mechanism for rendering Mermaid diagrams
 * when the client-side rendering fails. It can use server-side rendering
 * or external services as alternatives.
 */

export interface MermaidRenderOptions {
  theme?: 'default' | 'dark' | 'forest' | 'neutral';
  backgroundColor?: string;
  width?: number;
  height?: number;
}

export interface MermaidRenderResult {
  success: boolean;
  svg?: string;
  error?: string;
  method: 'client' | 'server' | 'fallback';
}

/**
 * Attempts to render a Mermaid diagram using multiple fallback methods
 */
export async function renderMermaidWithFallback(
  code: string,
  options: MermaidRenderOptions = {}
): Promise<MermaidRenderResult> {
  // Note: Server-side rendering is not supported due to Mermaid's DOM dependencies
  // This function provides a placeholder for future alternative rendering methods

  // For now, we'll return a helpful error message
  // In the future, this could integrate with external services like:
  // - Puppeteer for headless browser rendering
  // - External Mermaid rendering services
  // - Static diagram generation tools

  console.warn('Fallback rendering attempted but not implemented');

  return {
    success: false,
    error: 'Fallback rendering not available. Mermaid requires client-side rendering in a browser environment.',
    method: 'fallback',
  };
}

/**
 * Enhanced error information for Mermaid validation
 */
export interface MermaidValidationResult {
  valid: boolean;
  error?: string;
  errorType?: 'syntax' | 'parsing' | 'structure' | 'content';
  suggestions?: string[];
  fixable?: boolean;
  line?: number;
  column?: number;
}

/**
 * Enhanced Mermaid syntax validation with detailed error analysis
 */
export function validateMermaidSyntax(code: string): MermaidValidationResult {
  if (!code || !code.trim()) {
    return {
      valid: false,
      error: 'Empty diagram code',
      errorType: 'content',
      suggestions: ['Add a valid Mermaid diagram starting with a diagram type (e.g., "graph TD", "flowchart LR")'],
      fixable: false
    };
  }

  // Basic syntax validation
  const trimmedCode = code.trim();
  const lines = trimmedCode.split('\n');

  // Check for common diagram types
  const validStarters = [
    'graph',
    'flowchart',
    'sequenceDiagram',
    'classDiagram',
    'stateDiagram',
    'erDiagram',
    'journey',
    'gantt',
    'pie',
    'gitgraph',
    'timeline',
    'mindmap',
    'quadrantChart',
    'requirement',
    'c4Context',
    'c4Container',
    'c4Component',
    'c4Dynamic',
    'c4Deployment',
  ];

  const hasValidStarter = validStarters.some(starter =>
    trimmedCode.toLowerCase().startsWith(starter.toLowerCase())
  );

  if (!hasValidStarter) {
    return {
      valid: false,
      error: 'Invalid diagram type. Must start with a valid Mermaid diagram type.',
      errorType: 'structure',
      suggestions: [
        'Start with a valid diagram type like "graph TD", "flowchart LR", "sequenceDiagram", etc.',
        'Check the Mermaid documentation for supported diagram types'
      ],
      fixable: false,
      line: 1
    };
  }

  // Enhanced problematic pattern detection with specific error messages
  const problematicPatterns = [
    {
      pattern: /\w+\[[^\]]*\([^)]*\)[^\]]*\]/g,
      error: 'Node labels contain parentheses which may cause parsing errors',
      type: 'parsing' as const,
      suggestions: ['Remove parentheses from node labels', 'Use quotes or hyphens instead of parentheses'],
      fixable: true,
      severity: 'medium' as const
    },
    {
      pattern: /\w+\([^)]*\([^)]*\)[^)]*\)/g,
      error: 'Nested parentheses in node labels may cause parsing errors',
      type: 'parsing' as const,
      suggestions: ['Avoid nested parentheses in node labels', 'Use alternative formatting'],
      fixable: true,
      severity: 'medium' as const
    },
    {
      pattern: /\w+\{[^}]*\([^)]*\)[^}]*\}/g,
      error: 'Parentheses in curly bracket labels may cause parsing errors',
      type: 'parsing' as const,
      suggestions: ['Remove parentheses from labels with curly brackets'],
      fixable: true,
      severity: 'medium' as const
    },
    {
      pattern: /\w+\[[^\]]*"[^"]*"[^\]]*\]/g,
      error: 'Quotes in node labels may cause parsing errors',
      type: 'parsing' as const,
      suggestions: ['Remove quotes from node labels', 'Use alternative text formatting'],
      fixable: true,
      severity: 'high' as const
    },
    {
      pattern: /\w+\[[^\]]*'[^']*'[^\]]*\]/g,
      error: 'Single quotes in node labels may cause parsing errors',
      type: 'parsing' as const,
      suggestions: ['Remove single quotes from node labels'],
      fixable: true,
      severity: 'high' as const
    }
  ];

  // Check for problematic patterns and find line numbers
  // Only return validation errors for high-severity issues that are likely to cause actual failures
  for (const { pattern, error, type, suggestions, fixable, severity } of problematicPatterns) {
    const matches = Array.from(trimmedCode.matchAll(pattern));
    if (matches.length > 0 && severity === 'high') {
      // Find the line number of the first match
      const match = matches[0];
      const beforeMatch = trimmedCode.substring(0, match.index);
      const lineNumber = beforeMatch.split('\n').length;

      return {
        valid: false,
        error,
        errorType: type,
        suggestions,
        fixable,
        line: lineNumber
      };
    }
  }

  // Check for unmatched brackets with enhanced error reporting
  const brackets = { '[': 0, '(': 0, '{': 0 };
  const closingBrackets = { ']': '[', ')': '(', '}': '{' };
  let lineNumber = 1;
  let columnNumber = 1;

  for (let i = 0; i < trimmedCode.length; i++) {
    const char = trimmedCode[i];

    if (char === '\n') {
      lineNumber++;
      columnNumber = 1;
      continue;
    }

    if (char in brackets) {
      brackets[char as keyof typeof brackets]++;
    } else if (char in closingBrackets) {
      const opening = closingBrackets[char as keyof typeof closingBrackets];
      brackets[opening as keyof typeof brackets]--;
      if (brackets[opening as keyof typeof brackets] < 0) {
        return {
          valid: false,
          error: `Unmatched closing bracket '${char}' found in diagram`,
          errorType: 'syntax',
          suggestions: [`Add matching opening bracket '${opening}' before this position`],
          fixable: true,
          line: lineNumber,
          column: columnNumber
        };
      }
    }

    columnNumber++;
  }

  // Check for unmatched opening brackets
  for (const [bracket, count] of Object.entries(brackets)) {
    if (count > 0) {
      return {
        valid: false,
        error: `${count} unmatched opening bracket(s) '${bracket}' found in diagram`,
        errorType: 'syntax',
        suggestions: [`Add ${count} closing bracket(s) to match the opening '${bracket}' bracket(s)`],
        fixable: true
      };
    }
  }

  return { valid: true };
}

/**
 * Sanitizes Mermaid code to prevent potential security issues and fix common syntax problems
 */
export function sanitizeMermaidCode(code: string): string {
  // Remove potentially dangerous content
  let sanitized = code
    .replace(/<script[^>]*>.*?<\/script>/gi, '') // Remove script tags
    .replace(/javascript:/gi, '') // Remove javascript: protocols
    .replace(/on\w+\s*=/gi, '') // Remove event handlers
    .trim();

  // Fix common Mermaid syntax issues
  sanitized = fixMermaidSyntaxIssues(sanitized);

  return sanitized;
}

/**
 * Enhanced Mermaid syntax fixing with progressive strategies
 */
function fixMermaidSyntaxIssues(code: string): string {
  let fixed = code;
  let changesMade = false;

  // Strategy 1: Fix quotes in node labels (most common issue)
  const quoteFixes = [
    // Handle patterns like: CloudP[Cloud Provider "AWS or Azure or GCP"]
    {
      pattern: /(\w+)\[([^[\]]*)\s*"([^"]+)"\s*([^[\]]*)\]/g,
      replacement: (match: string, nodeId: string, beforeQuote: string, insideQuote: string, afterQuote: string) => {
        const cleanLabel = `${beforeQuote} ${insideQuote} ${afterQuote}`.replace(/\s+/g, ' ').trim();
        return `${nodeId}[${cleanLabel}]`;
      }
    },
    // Handle single quotes
    {
      pattern: /(\w+)\[([^[\]]*)\s*'([^']+)'\s*([^[\]]*)\]/g,
      replacement: (match: string, nodeId: string, beforeQuote: string, insideQuote: string, afterQuote: string) => {
        const cleanLabel = `${beforeQuote} ${insideQuote} ${afterQuote}`.replace(/\s+/g, ' ').trim();
        return `${nodeId}[${cleanLabel}]`;
      }
    }
  ];

  for (const { pattern, replacement } of quoteFixes) {
    const newFixed = fixed.replace(pattern, replacement);
    if (newFixed !== fixed) {
      fixed = newFixed;
      changesMade = true;
    }
  }

  // Strategy 2: Fix parentheses in node labels
  const parenthesesFixes = [
    // Square brackets with parentheses: [Policy Engine (e.g., OPA)]
    {
      pattern: /(\w+)\[([^\]]*)\(([^)]*)\)([^\]]*)\]/g,
      replacement: (match: string, nodeId: string, beforeParen: string, insideParen: string, afterParen: string) => {
        const cleanLabel = `${beforeParen} ${insideParen} ${afterParen}`.replace(/\s+/g, ' ').trim();
        return `${nodeId}[${cleanLabel}]`;
      }
    },
    // Round brackets with nested parentheses
    {
      pattern: /(\w+)\(([^)]*)\(([^)]*)\)([^)]*)\)/g,
      replacement: (match: string, nodeId: string, beforeParen: string, insideParen: string, afterParen: string) => {
        const cleanLabel = `${beforeParen} ${insideParen} ${afterParen}`.replace(/\s+/g, ' ').trim();
        return `${nodeId}(${cleanLabel})`;
      }
    },
    // Curly braces with parentheses
    {
      pattern: /(\w+)\{([^}]*)\(([^)]*)\)([^}]*)\}/g,
      replacement: (match: string, nodeId: string, beforeParen: string, insideParen: string, afterParen: string) => {
        const cleanLabel = `${beforeParen} ${insideParen} ${afterParen}`.replace(/\s+/g, ' ').trim();
        return `${nodeId}{${cleanLabel}}`;
      }
    }
  ];

  for (const { pattern, replacement } of parenthesesFixes) {
    const newFixed = fixed.replace(pattern, replacement);
    if (newFixed !== fixed) {
      fixed = newFixed;
      changesMade = true;
    }
  }

  // Strategy 3: Fix special characters and symbols
  const specialCharFixes = [
    // Forward slashes that might cause issues
    {
      pattern: /(\w+)\[([^[\]]*\/[^[\]]*)\]/g,
      replacement: (match: string, nodeId: string, label: string) => {
        const cleanLabel = label.replace(/\//g, ' or ').replace(/\s+/g, ' ').trim();
        return `${nodeId}[${cleanLabel}]`;
      }
    },
    // Ampersands that might cause issues
    {
      pattern: /(\w+)\[([^[\]]*&[^[\]]*)\]/g,
      replacement: (match: string, nodeId: string, label: string) => {
        const cleanLabel = label.replace(/&/g, ' and ').replace(/\s+/g, ' ').trim();
        return `${nodeId}[${cleanLabel}]`;
      }
    },
    // Percent signs that might cause issues
    {
      pattern: /(\w+)\[([^[\]]*%[^[\]]*)\]/g,
      replacement: (match: string, nodeId: string, label: string) => {
        const cleanLabel = label.replace(/%/g, ' percent').replace(/\s+/g, ' ').trim();
        return `${nodeId}[${cleanLabel}]`;
      }
    }
  ];

  for (const { pattern, replacement } of specialCharFixes) {
    const newFixed = fixed.replace(pattern, replacement);
    if (newFixed !== fixed) {
      fixed = newFixed;
      changesMade = true;
    }
  }

  // Strategy 4: Clean up any remaining problematic characters in labels
  fixed = fixed.replace(/(\w+)([[\({])([^[\](){}]*)([\])}])/g, (match, nodeId, openBracket, label, closeBracket) => {
    // Remove problematic characters that commonly cause parsing errors
    const cleanLabel = label
      .replace(/["`']/g, '') // Remove quotes
      .replace(/[<>]/g, '') // Remove angle brackets
      .replace(/[|\\]/g, '') // Remove pipes and backslashes
      .replace(/\s+/g, ' ') // Normalize whitespace
      .trim();

    if (cleanLabel !== label) {
      changesMade = true;
      return `${nodeId}${openBracket}${cleanLabel}${closeBracket}`;
    }

    return match;
  });

  // Strategy 5: Normalize whitespace and line endings only if changes were made
  if (changesMade) {
    fixed = fixed
      .replace(/\r\n/g, '\n')
      .replace(/\r/g, '\n')
      .replace(/\n\s*\n/g, '\n')
      .replace(/^\s+|\s+$/gm, '')
      .trim();
  }

  return fixed;
}

/**
 * Generates a unique ID for Mermaid diagrams
 */
export function generateMermaidId(): string {
  return `mermaid-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
}

/**
 * Converts theme name to Mermaid-compatible theme
 */
export function normalizeMermaidTheme(theme: string): string {
  const themeMap: Record<string, string> = {
    'default': 'default',
    'dark': 'dark',
    'forest': 'forest',
    'neutral': 'neutral',
    'base': 'base',
  };

  return themeMap[theme.toLowerCase()] || 'default';
}

/**
 * Apply aggressive sanitization for fallback rendering
 * This removes more content but increases the chance of successful rendering
 */
export function applyAggressiveSanitization(code: string): string {
  let sanitized = sanitizeMermaidCode(code);

  // More aggressive fixes for fallback rendering
  sanitized = sanitized
    // Remove all parentheses and their content
    .replace(/\([^)]*\)/g, '')
    // Remove all quotes and their content
    .replace(/"[^"]*"/g, '')
    .replace(/'[^']*'/g, '')
    // Simplify complex node labels to just the node ID
    .replace(/(\w+)\[[^\]]+\]/g, '$1[Node]')
    .replace(/(\w+)\([^)]+\)/g, '$1(Node)')
    .replace(/(\w+)\{[^}]+\}/g, '$1{Node}')
    // Remove special characters that might cause issues
    .replace(/[&%$#@!]/g, '')
    // Normalize all whitespace
    .replace(/\s+/g, ' ')
    .trim();

  return sanitized;
}

/**
 * Enhanced error information for Mermaid rendering errors
 */
export interface MermaidErrorInfo {
  type: 'syntax' | 'parsing' | 'rendering' | 'timeout' | 'unknown';
  message: string;
  originalError: string;
  suggestions: string[];
  fixable: boolean;
  line?: number;
  column?: number;
  severity: 'low' | 'medium' | 'high';
}

/**
 * Parse and categorize Mermaid rendering errors
 */
export function parseMermaidError(error: Error | string): MermaidErrorInfo {
  const errorMessage = typeof error === 'string' ? error : error.message;
  const originalError = errorMessage;

  // Parse error patterns and categorize them
  const errorPatterns = [
    {
      pattern: /Parse error on line (\d+):/i,
      type: 'parsing' as const,
      severity: 'high' as const,
      suggestions: [
        'Check the syntax on the specified line',
        'Look for missing quotes, brackets, or special characters',
        'Verify node labels and connections are properly formatted'
      ],
      extractLine: (match: RegExpMatchArray) => parseInt(match[1], 10)
    },
    {
      pattern: /Expecting '([^']+)', got '([^']+)'/i,
      type: 'syntax' as const,
      severity: 'high' as const,
      suggestions: [
        'Check for missing or incorrect syntax elements',
        'Verify quotes and brackets are properly matched',
        'Remove special characters from node labels'
      ]
    },
    {
      pattern: /Lexical error/i,
      type: 'syntax' as const,
      severity: 'high' as const,
      suggestions: [
        'Check for invalid characters in the diagram',
        'Verify all quotes and brackets are properly closed',
        'Remove or escape special characters'
      ]
    },
    {
      pattern: /timeout|timed out/i,
      type: 'timeout' as const,
      severity: 'medium' as const,
      suggestions: [
        'Simplify the diagram by reducing the number of nodes',
        'Break complex diagrams into smaller parts',
        'Check for infinite loops in the diagram structure'
      ]
    },
    {
      pattern: /Cannot read properties|undefined/i,
      type: 'rendering' as const,
      severity: 'medium' as const,
      suggestions: [
        'Check the diagram syntax for completeness',
        'Verify all node references are properly defined',
        'Try refreshing the diagram'
      ]
    },
    {
      pattern: /STR/i,
      type: 'parsing' as const,
      severity: 'high' as const,
      suggestions: [
        'Remove quotes from node labels',
        'Check for problematic string syntax in node names',
        'Use simple text without special characters in labels'
      ]
    }
  ];

  // Find matching pattern
  for (const { pattern, type, severity, suggestions, extractLine } of errorPatterns) {
    const match = errorMessage.match(pattern);
    if (match) {
      const line = extractLine ? extractLine(match) : undefined;

      return {
        type,
        message: generateUserFriendlyMessage(type, errorMessage),
        originalError,
        suggestions,
        fixable: type === 'syntax' || type === 'parsing',
        line,
        severity
      };
    }
  }

  // Default case for unknown errors
  return {
    type: 'unknown',
    message: 'An unexpected error occurred while rendering the diagram',
    originalError,
    suggestions: [
      'Try refreshing the diagram',
      'Check the diagram syntax for any obvious issues',
      'Contact support if the problem persists'
    ],
    fixable: false,
    severity: 'medium'
  };
}

/**
 * Generate user-friendly error messages
 */
function generateUserFriendlyMessage(type: string, originalError: string): string {
  const messages = {
    syntax: 'The diagram contains syntax errors that prevent it from being rendered properly.',
    parsing: 'The diagram structure could not be parsed due to formatting issues.',
    rendering: 'The diagram could not be rendered due to a technical issue.',
    timeout: 'The diagram is too complex and timed out during rendering.',
    unknown: 'An unexpected error occurred while processing the diagram.'
  };

  const baseMessage = messages[type as keyof typeof messages] || messages.unknown;

  // Add specific details for certain error types
  if (type === 'parsing' && originalError.includes('line')) {
    const lineMatch = originalError.match(/line (\d+)/i);
    if (lineMatch) {
      return `${baseMessage} Please check line ${lineMatch[1]} for syntax issues.`;
    }
  }

  return baseMessage;
}

/**
 * Estimates the complexity of a Mermaid diagram
 * Returns a score from 1-10 (10 being most complex)
 */
export function estimateDiagramComplexity(code: string): number {
  const lines = code.split('\n').length;
  const nodes = (code.match(/\[.*?\]|\(.*?\)|\{.*?\}/g) || []).length;
  const connections = (code.match(/-->|->|---|-/g) || []).length;

  // Simple scoring algorithm
  let complexity = 1;

  if (lines > 50) complexity += 3;
  else if (lines > 20) complexity += 2;
  else if (lines > 10) complexity += 1;

  if (nodes > 20) complexity += 3;
  else if (nodes > 10) complexity += 2;
  else if (nodes > 5) complexity += 1;

  if (connections > 30) complexity += 3;
  else if (connections > 15) complexity += 2;
  else if (connections > 8) complexity += 1;

  return Math.min(complexity, 10);
}
