import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { handoverDocumentService } from '@/services/handover-documents';
import { z } from 'zod';

// Request validation schemas
const getDocumentsQuerySchema = z.object({
  category: z.string().optional(),
  isRequired: z.coerce.boolean().optional(),
  isApproved: z.coerce.boolean().optional(),
  accessLevel: z.string().optional(),
  isClientVisible: z.coerce.boolean().optional(),
});

const linkDocumentSchema = z.object({
  documentId: z.string().min(1, 'Document ID is required'),
  category: z.enum(['requirements', 'contracts', 'technical', 'legal', 'financial']).default('requirements'),
  isRequired: z.boolean().default(false),
  isClientVisible: z.boolean().default(false),
  accessLevel: z.enum(['internal', 'delivery_team', 'client']).default('internal'),
  orderIndex: z.number().int().min(0).default(0),
  notes: z.string().optional(),
});

const bulkUpdateSchema = z.object({
  documentIds: z.array(z.string()).min(1, 'At least one document ID is required'),
  updates: z.object({
    category: z.enum(['requirements', 'contracts', 'technical', 'legal', 'financial']).optional(),
    isRequired: z.boolean().optional(),
    isApproved: z.boolean().optional(),
    isClientVisible: z.boolean().optional(),
    accessLevel: z.enum(['internal', 'delivery_team', 'client']).optional(),
  }),
});

interface RouteParams {
  params: {
    id: string;
  };
}

/**
 * GET /api/handovers/[id]/documents - Get documents for a handover
 */
export async function GET(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get tenantId from user's tenants array (users have one tenant in this system)
    let tenantId: string | null = null;
    if (session.user.tenants && session.user.tenants.length > 0) {
      tenantId = session.user.tenants[0].id;
    }

    if (!tenantId) {
      return NextResponse.json({ error: 'Tenant not found' }, { status: 400 });
    }

    // Parse and validate query parameters
    const url = new URL(request.url);
    const queryParams = Object.fromEntries(url.searchParams.entries());
    
    const validatedQuery = getDocumentsQuerySchema.parse(queryParams);

    const resolvedParams = await params;
    const [documents, stats] = await Promise.all([
      handoverDocumentService.getHandoverDocuments(tenantId, resolvedParams.id, validatedQuery),
      handoverDocumentService.getDocumentStats(tenantId, resolvedParams.id)
    ]);

    return NextResponse.json({ documents, stats });
  } catch (error) {
    console.error('Error fetching handover documents:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid query parameters', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to fetch handover documents' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/handovers/[id]/documents - Link a document to a handover
 */
export async function POST(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get tenantId from user's tenants array (users have one tenant in this system)
    let tenantId: string | null = null;
    if (session.user.tenants && session.user.tenants.length > 0) {
      tenantId = session.user.tenants[0].id;
    }

    if (!tenantId) {
      return NextResponse.json({ error: 'Tenant not found' }, { status: 400 });
    }

    const body = await request.json();
    const validatedData = linkDocumentSchema.parse(body);

    const handoverDocument = await handoverDocumentService.linkDocumentToHandover(
      tenantId,
      params.id,
      validatedData
    );

    return NextResponse.json(handoverDocument, { status: 201 });
  } catch (error) {
    console.error('Error linking document to handover:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request data', details: error.errors },
        { status: 400 }
      );
    }

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to link document to handover' },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/handovers/[id]/documents - Bulk update documents
 */
export async function PUT(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get tenantId from user's tenants array (users have one tenant in this system)
    let tenantId: string | null = null;
    if (session.user.tenants && session.user.tenants.length > 0) {
      tenantId = session.user.tenants[0].id;
    }

    if (!tenantId) {
      return NextResponse.json({ error: 'Tenant not found' }, { status: 400 });
    }

    const body = await request.json();
    const validatedData = bulkUpdateSchema.parse(body);

    const updatedDocuments = await handoverDocumentService.bulkUpdateDocuments(
      tenantId,
      params.id,
      validatedData,
      session.user.id
    );

    return NextResponse.json({ documents: updatedDocuments });
  } catch (error) {
    console.error('Error bulk updating handover documents:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request data', details: error.errors },
        { status: 400 }
      );
    }

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to bulk update handover documents' },
      { status: 500 }
    );
  }
}
