import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { processAutomationService } from '@/services/process-automation';
import { withPermission, AuthenticatedRequest } from '@/lib/permission-middleware';
import { PermissionResource, PermissionAction } from '@/types';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';

// Validation schemas
const updateTriggerSchema = z.object({
  name: z.string().min(1, 'Name is required').max(255).optional(),
  description: z.string().optional(),
  triggerType: z.enum(['opportunity_status_change', 'handover_completion', 'project_milestone', 'time_based']).optional(),
  triggerConditions: z.record(z.any()).optional(),
  actions: z.array(z.object({
    type: z.enum(['create_handover', 'send_notification', 'create_project', 'assign_team', 'schedule_meeting']),
    config: z.record(z.any())
  })).optional(),
  isActive: z.boolean().optional(),
  priority: z.number().int().min(1).max(10).optional()
});

interface RouteParams {
  params: {
    id: string;
  };
}

/**
 * GET /api/automation/triggers/[id]
 * Get a specific automation trigger
 */
export const GET = withPermission({
  resource: PermissionResource.SETTINGS,
  action: PermissionAction.READ,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest, { params }: RouteParams) => {
  try {
    const triggerId = params.id;

    const trigger = await prisma.automationTrigger.findUnique({
      where: {
        id: triggerId,
        tenantId: req.tenantId
      },
      include: {
        createdBy: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true
          }
        },
        _count: {
          select: {
            executions: true
          }
        }
      }
    });

    if (!trigger) {
      return NextResponse.json(
        { error: 'Automation trigger not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: { trigger }
    });
  } catch (error) {
    console.error('Error fetching automation trigger:', error);
    return NextResponse.json(
      { error: 'Failed to fetch automation trigger' },
      { status: 500 }
    );
  }
});

/**
 * PUT /api/automation/triggers/[id]
 * Update an automation trigger
 */
export const PUT = withPermission({
  resource: PermissionResource.SETTINGS,
  action: PermissionAction.UPDATE,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest, { params }: RouteParams) => {
  try {
    const triggerId = params.id;
    const body = await req.json();
    const validatedData = updateTriggerSchema.parse(body);

    // Check if trigger exists and belongs to tenant
    const existingTrigger = await prisma.automationTrigger.findUnique({
      where: {
        id: triggerId,
        tenantId: req.tenantId
      }
    });

    if (!existingTrigger) {
      return NextResponse.json(
        { error: 'Automation trigger not found' },
        { status: 404 }
      );
    }

    // Update the trigger
    const updatedTrigger = await prisma.automationTrigger.update({
      where: {
        id: triggerId,
        tenantId: req.tenantId
      },
      data: {
        ...validatedData,
        updatedAt: new Date()
      },
      include: {
        createdBy: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true
          }
        },
        _count: {
          select: {
            executions: true
          }
        }
      }
    });

    return NextResponse.json({
      success: true,
      data: { trigger: updatedTrigger },
      message: 'Automation trigger updated successfully'
    });
  } catch (error) {
    console.error('Error updating automation trigger:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request data', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to update automation trigger' },
      { status: 500 }
    );
  }
});

/**
 * DELETE /api/automation/triggers/[id]
 * Delete an automation trigger
 */
export const DELETE = withPermission({
  resource: PermissionResource.SETTINGS,
  action: PermissionAction.DELETE,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest, { params }: RouteParams) => {
  try {
    const triggerId = params.id;

    // Check if trigger exists and belongs to tenant
    const existingTrigger = await prisma.automationTrigger.findUnique({
      where: {
        id: triggerId,
        tenantId: req.tenantId
      }
    });

    if (!existingTrigger) {
      return NextResponse.json(
        { error: 'Automation trigger not found' },
        { status: 404 }
      );
    }

    // Check if trigger has executions
    const executionCount = await prisma.automationExecution.count({
      where: {
        triggerId,
        tenantId: req.tenantId
      }
    });

    if (executionCount > 0) {
      // Instead of deleting, deactivate the trigger to preserve execution history
      await prisma.automationTrigger.update({
        where: {
          id: triggerId,
          tenantId: req.tenantId
        },
        data: {
          isActive: false,
          name: `${existingTrigger.name} (Deleted)`,
          updatedAt: new Date()
        }
      });

      return NextResponse.json({
        success: true,
        message: 'Automation trigger deactivated (execution history preserved)'
      });
    } else {
      // Safe to delete if no executions
      await prisma.automationTrigger.delete({
        where: {
          id: triggerId,
          tenantId: req.tenantId
        }
      });

      return NextResponse.json({
        success: true,
        message: 'Automation trigger deleted successfully'
      });
    }
  } catch (error) {
    console.error('Error deleting automation trigger:', error);
    return NextResponse.json(
      { error: 'Failed to delete automation trigger' },
      { status: 500 }
    );
  }
});

/**
 * POST /api/automation/triggers/[id]/test
 * Test an automation trigger
 */
export async function POST(req: NextRequest, { params }: RouteParams) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const triggerId = params.id;
    const body = await req.json();
    const { testData } = body;

    // Get the trigger
    const trigger = await prisma.automationTrigger.findUnique({
      where: { id: triggerId }
    });

    if (!trigger) {
      return NextResponse.json(
        { error: 'Trigger not found' },
        { status: 404 }
      );
    }

    // Create a test execution record
    const testExecution = await prisma.automationExecution.create({
      data: {
        tenantId: trigger.tenantId,
        triggerId,
        entityType: 'test',
        entityId: 'test-entity',
        triggerData: testData || { test: true },
        status: 'completed',
        executedActions: [
          {
            actionType: 'test',
            status: 'completed',
            result: { message: 'Test execution successful' }
          }
        ],
        completedAt: new Date()
      }
    });

    return NextResponse.json({
      success: true,
      data: {
        testResult: {
          triggerId,
          triggerName: trigger.name,
          testData,
          wouldExecute: trigger.isActive,
          actions: trigger.actions,
          executionId: testExecution.id,
          timestamp: new Date().toISOString()
        }
      },
      message: 'Trigger test completed successfully'
    });
  } catch (error) {
    console.error('Error testing automation trigger:', error);
    return NextResponse.json(
      { error: 'Failed to test automation trigger' },
      { status: 500 }
    );
  }
}

/**
 * GET /api/automation/triggers/[id]/executions
 * Get execution history for a trigger
 */
export async function GET_EXECUTIONS(req: NextRequest, { params }: RouteParams) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const triggerId = params.id;
    const { searchParams } = new URL(req.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');

    const [executions, total] = await Promise.all([
      prisma.automationExecution.findMany({
        where: { triggerId },
        orderBy: { startedAt: 'desc' },
        skip: (page - 1) * limit,
        take: limit
      }),
      prisma.automationExecution.count({ where: { triggerId } })
    ]);

    return NextResponse.json({
      success: true,
      data: {
        executions,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      }
    });
  } catch (error) {
    console.error('Error fetching trigger executions:', error);
    return NextResponse.json(
      { error: 'Failed to fetch trigger executions' },
      { status: 500 }
    );
  }
}
