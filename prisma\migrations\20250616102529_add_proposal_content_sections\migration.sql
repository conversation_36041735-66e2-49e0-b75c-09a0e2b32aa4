-- CreateTable
CREATE TABLE "proposal_content_sections" (
    "id" TEXT NOT NULL,
    "tenant_id" TEXT NOT NULL,
    "section_type" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "description" TEXT,
    "prompt_en" TEXT NOT NULL,
    "prompt_ar" TEXT,
    "system_prompt_en" TEXT NOT NULL,
    "system_prompt_ar" TEXT,
    "order_index" INTEGER NOT NULL,
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    "is_default" BOOLEAN NOT NULL DEFAULT false,
    "created_by" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "proposal_content_sections_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "idx_proposal_content_sections_tenant_active" ON "proposal_content_sections"("tenant_id", "is_active");

-- CreateIndex
CREATE INDEX "idx_proposal_content_sections_tenant_order" ON "proposal_content_sections"("tenant_id", "order_index");

-- CreateIndex
CREATE UNIQUE INDEX "proposal_content_sections_tenant_id_section_type_key" ON "proposal_content_sections"("tenant_id", "section_type");

-- AddForeignKey
ALTER TABLE "proposal_content_sections" ADD CONSTRAINT "proposal_content_sections_created_by_fkey" FOREIGN KEY ("created_by") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "proposal_content_sections" ADD CONSTRAINT "proposal_content_sections_tenant_id_fkey" FOREIGN KEY ("tenant_id") REFERENCES "tenants"("id") ON DELETE CASCADE ON UPDATE CASCADE;
