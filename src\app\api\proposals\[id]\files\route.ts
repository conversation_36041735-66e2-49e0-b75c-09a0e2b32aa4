import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { hasPermission } from '@/lib/permissions';
import { PermissionResource, PermissionAction } from '@/types';
import { prisma } from '@/lib/prisma';
import { FileSelectionData } from '@/types/proposal';

/**
 * GET /api/proposals/[id]/files
 * Get opportunity files available for proposal generation
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user's current tenant
    const currentTenant = session.user.tenants?.[0];
    if (!currentTenant) {
      return NextResponse.json({ error: 'No tenant access' }, { status: 403 });
    }

    // Check permission for proposal reading
    const hasAccess = await hasPermission(
      PermissionResource.PROPOSALS,
      PermissionAction.READ,
      currentTenant.id
    );

    if (!hasAccess) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const proposalId = params.id;

    // Get proposal and verify access
    const proposal = await prisma.proposal.findFirst({
      where: {
        id: proposalId,
        tenantId: currentTenant.id
      },
      include: {
        opportunity: true
      }
    });

    if (!proposal) {
      return NextResponse.json(
        { error: 'Proposal not found or access denied' },
        { status: 404 }
      );
    }

    if (!proposal.opportunityId) {
      return NextResponse.json(
        { error: 'Proposal is not associated with an opportunity' },
        { status: 400 }
      );
    }

    // Get all documents associated with the opportunity
    const documents = await prisma.document.findMany({
      where: {
        tenantId: currentTenant.id,
        relatedToType: 'opportunity',
        relatedToId: proposal.opportunityId
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    // Get currently selected file IDs from proposal
    const selectedFileIds = Array.isArray(proposal.selectedFileIds) 
      ? proposal.selectedFileIds as string[]
      : [];

    // Get Gemini file URIs if available
    const geminiFileUris = proposal.geminiFileUris as Record<string, string> || {};

    // Transform documents to FileSelectionData format
    const fileSelectionData: FileSelectionData[] = documents.map(doc => ({
      documentId: doc.id,
      name: doc.name,
      filePath: doc.filePath,
      mimeType: doc.mimeType || 'application/octet-stream',
      fileSize: doc.fileSize || 0,
      isSelected: selectedFileIds.includes(doc.id),
      geminiFileUri: geminiFileUris[doc.id]
    }));

    // Filter by supported file types for AI processing
    const supportedMimeTypes = [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'text/plain',
      'text/markdown',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    ];

    const supportedFiles = fileSelectionData.filter(file => 
      supportedMimeTypes.includes(file.mimeType)
    );

    const unsupportedFiles = fileSelectionData.filter(file => 
      !supportedMimeTypes.includes(file.mimeType)
    );

    return NextResponse.json({
      success: true,
      data: {
        proposalId: proposal.id,
        opportunityId: proposal.opportunityId,
        opportunityTitle: proposal.opportunity?.title,
        totalFiles: documents.length,
        supportedFiles,
        unsupportedFiles,
        selectedCount: selectedFileIds.length,
        supportedMimeTypes
      },
      message: 'Files retrieved successfully'
    });

  } catch (error) {
    console.error('Error retrieving proposal files:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * @swagger
 * /api/proposals/{id}/files:
 *   get:
 *     summary: Get opportunity files for proposal generation
 *     description: Retrieve all files associated with the proposal's opportunity for RFP context selection
 *     tags: [Proposals]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Proposal ID
 *     responses:
 *       200:
 *         description: Files retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     proposalId:
 *                       type: string
 *                     opportunityId:
 *                       type: string
 *                     opportunityTitle:
 *                       type: string
 *                     totalFiles:
 *                       type: number
 *                     supportedFiles:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           documentId:
 *                             type: string
 *                           name:
 *                             type: string
 *                           filePath:
 *                             type: string
 *                           mimeType:
 *                             type: string
 *                           fileSize:
 *                             type: number
 *                           isSelected:
 *                             type: boolean
 *                           geminiFileUri:
 *                             type: string
 *                     unsupportedFiles:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           documentId:
 *                             type: string
 *                           name:
 *                             type: string
 *                           mimeType:
 *                             type: string
 *                     selectedCount:
 *                       type: number
 *                     supportedMimeTypes:
 *                       type: array
 *                       items:
 *                         type: string
 *                 message:
 *                   type: string
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - insufficient permissions
 *       404:
 *         description: Proposal not found
 *       500:
 *         description: Internal server error
 */
