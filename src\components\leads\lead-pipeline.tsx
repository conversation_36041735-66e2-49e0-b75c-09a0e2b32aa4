'use client';

import { useState, useEffect, useMemo, useRef } from 'react';
import { DragDropContext, Droppable, Draggable, DropResult } from '@hello-pangea/dnd';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import {
  Calendar,
  DollarSign,
  ChevronLeft,
  ChevronRight
} from 'lucide-react';
import { formatDistanceToNow, format } from 'date-fns';
import { useToast } from '@/hooks/use-toast';
import { LeadWithRelations } from '@/services/lead-management';

interface LeadPipelineProps {
  tenantId: string | undefined;
  onLeadClick?: (lead: LeadWithRelations) => void;
}

const LEAD_STATUSES = [
  { id: 'new', name: 'New Leads', color: 'bg-blue-100 text-blue-800' },
  { id: 'contacted', name: 'Contacted', color: 'bg-yellow-100 text-yellow-800' },
  { id: 'qualified', name: 'Qualified', color: 'bg-purple-100 text-purple-800' },
  { id: 'proposal', name: 'Proposal', color: 'bg-orange-100 text-orange-800' },
  { id: 'negotiation', name: 'Negotiation', color: 'bg-indigo-100 text-indigo-800' },
  { id: 'closed_won', name: 'Closed Won', color: 'bg-green-100 text-green-800' },
  { id: 'closed_lost', name: 'Closed Lost', color: 'bg-red-100 text-red-800' }
];

const PRIORITY_COLORS = {
  low: 'bg-gray-100 text-gray-800',
  medium: 'bg-blue-100 text-blue-800',
  high: 'bg-orange-100 text-orange-800',
  urgent: 'bg-red-100 text-red-800'
};

export function LeadPipeline({ tenantId, onLeadClick }: LeadPipelineProps) {
  const [leads, setLeads] = useState<LeadWithRelations[]>([]);
  const [loading, setLoading] = useState(true);
  const [updating, setUpdating] = useState<string | null>(null);
  const [canScrollLeft, setCanScrollLeft] = useState(false);
  const [canScrollRight, setCanScrollRight] = useState(false);
  const { toast } = useToast();
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  // Ensure leads is always an array to prevent runtime errors
  const safeLeads = useMemo(() => {
    return Array.isArray(leads) ? leads : [];
  }, [leads]);

  useEffect(() => {
    if (tenantId) {
      fetchLeads();
    }
  }, [tenantId]);

  // Check scroll position and update scroll indicators
  const checkScrollPosition = () => {
    if (scrollContainerRef.current) {
      const { scrollLeft, scrollWidth, clientWidth } = scrollContainerRef.current;
      setCanScrollLeft(scrollLeft > 0);
      setCanScrollRight(scrollLeft < scrollWidth - clientWidth - 1);
    }
  };

  // Scroll functions
  const scrollLeft = () => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollBy({ left: -320, behavior: 'smooth' });
    }
  };

  const scrollRight = () => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollBy({ left: 320, behavior: 'smooth' });
    }
  };

  // Update scroll indicators when leads change
  useEffect(() => {
    checkScrollPosition();
  }, [safeLeads]);

  // Add scroll event listener
  useEffect(() => {
    const container = scrollContainerRef.current;
    if (container) {
      container.addEventListener('scroll', checkScrollPosition);
      // Initial check
      checkScrollPosition();

      return () => {
        container.removeEventListener('scroll', checkScrollPosition);
      };
    }
  }, []);

  const fetchLeads = async () => {
    if (!tenantId) {
      setLoading(false);
      return;
    }

    try {
      const response = await fetch('/api/leads', {
        headers: {
          'x-tenant-id': tenantId
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch leads');
      }

      const data = await response.json();

      // Ensure we have a valid response structure
      if (data.success && data.data && Array.isArray(data.data.leads)) {
        setLeads(data.data.leads);
      } else {
        console.warn('Invalid leads data structure:', data);
        setLeads([]);
      }
    } catch (error) {
      console.error('Error fetching leads:', error);
      setLeads([]); // Ensure leads is always an array
      toast({
        title: 'Error',
        description: 'Failed to fetch leads',
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  };

  const updateLeadStatus = async (leadId: string, newStatus: string) => {
    if (!tenantId) {
      return;
    }

    setUpdating(leadId);
    try {
      const response = await fetch(`/api/leads/${leadId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'x-tenant-id': tenantId
        },
        body: JSON.stringify({ status: newStatus })
      });

      if (!response.ok) {
        throw new Error('Failed to update lead status');
      }

      // Update local state
      setLeads(prevLeads =>
        prevLeads.map(lead =>
          lead.id === leadId ? { ...lead, status: newStatus } : lead
        )
      );

      toast({
        title: 'Success',
        description: 'Lead status updated successfully'
      });
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to update lead status',
        variant: 'destructive'
      });
    } finally {
      setUpdating(null);
    }
  };

  const onDragEnd = (result: DropResult) => {
    const { destination, source, draggableId } = result;

    if (!destination) {
      return;
    }

    if (
      destination.droppableId === source.droppableId &&
      destination.index === source.index
    ) {
      return;
    }

    const newStatus = destination.droppableId;
    updateLeadStatus(draggableId, newStatus);
  };

  const getLeadsByStatus = (status: string) => {
    return safeLeads.filter(lead => lead.status === status);
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(value);
  };

  if (loading) {
    return (
      <div className="space-y-4">
        <h2 className="text-2xl font-bold">Lead Pipeline</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-7 gap-4">
          {LEAD_STATUSES.map((status) => (
            <Card key={status.id} className="animate-pulse">
              <CardHeader className="pb-3">
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {[1, 2].map((i) => (
                    <div key={i} className="h-20 bg-gray-200 rounded"></div>
                  ))}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold">Lead Pipeline</h2>
          <p className="text-muted-foreground">
            Drag and drop leads between stages to update their status
          </p>
        </div>
        <div className="flex items-center space-x-2 text-sm text-muted-foreground">
          <span>{safeLeads.length} total leads</span>
          <span>•</span>
          <span>
            {formatCurrency(
              safeLeads.reduce((sum, lead) => sum + (Number(lead.value) || 0), 0)
            )} total value
          </span>
        </div>
      </div>

      {/* Scroll Controls */}
      <div className="relative">
        {canScrollLeft && (
          <Button
            variant="outline"
            size="sm"
            className="absolute left-2 top-1/2 -translate-y-1/2 z-10 h-10 w-10 p-0 bg-background/90 backdrop-blur-sm border shadow-lg hover:bg-background hover:shadow-xl transition-all duration-200 animate-fade-in-up"
            onClick={scrollLeft}
          >
            <ChevronLeft className="h-5 w-5" />
          </Button>
        )}

        {canScrollRight && (
          <Button
            variant="outline"
            size="sm"
            className="absolute right-2 top-1/2 -translate-y-1/2 z-10 h-10 w-10 p-0 bg-background/90 backdrop-blur-sm border shadow-lg hover:bg-background hover:shadow-xl transition-all duration-200 animate-fade-in-up"
            onClick={scrollRight}
          >
            <ChevronRight className="h-5 w-5" />
          </Button>
        )}

        {/* Horizontal Scrollable Container */}
        <div
          ref={scrollContainerRef}
          className="overflow-x-auto pb-2 lead-pipeline-scroll"
        >
          <DragDropContext onDragEnd={onDragEnd}>
            <div className="flex gap-4 min-w-max px-1">
              {LEAD_STATUSES.map((status) => {
                const statusLeads = getLeadsByStatus(status.id);
                const statusValue = statusLeads.reduce(
                  (sum, lead) => sum + (Number(lead.value) || 0),
                  0
                );

                return (
                  <Card key={status.id} className={`flex flex-col w-80 flex-shrink-0 pipeline-column ${statusLeads.length > 5 ? 'scrollable' : ''}`}>
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm font-medium flex items-center justify-between">
                    <span className="truncate">{status.name}</span>
                    <div className="flex items-center gap-1">
                      <Badge variant="secondary" className="text-xs">
                        {statusLeads.length}
                      </Badge>
                      {statusLeads.length > 5 && (
                        <Badge variant="outline" className="text-xs bg-blue-50 text-blue-600 border-blue-200">
                          Scroll
                        </Badge>
                      )}
                    </div>
                  </CardTitle>
                  {statusValue > 0 && (
                    <p className="text-xs text-muted-foreground">
                      {formatCurrency(statusValue)}
                    </p>
                  )}
                </CardHeader>

                <Droppable droppableId={status.id}>
                  {(provided) => (
                    <CardContent className="flex-1 p-3 overflow-hidden">
                      <div
                        ref={provided.innerRef}
                        {...provided.droppableProps}
                        className="space-y-3 min-h-[400px] max-h-[600px] overflow-y-auto pr-2 lead-pipeline-scroll"
                      >
                        {statusLeads.map((lead, index) => (
                          <Draggable
                            key={lead.id}
                            draggableId={lead.id}
                            index={index}
                            isDragDisabled={updating === lead.id}
                          >
                            {(provided) => (
                              <div
                                ref={provided.innerRef}
                                {...provided.draggableProps}
                                {...provided.dragHandleProps}
                                className={`cursor-pointer ${updating === lead.id ? 'opacity-50 cursor-not-allowed' : ''}`}
                                onClick={() => onLeadClick?.(lead)}
                              >
                                <Card className="lead-card">
                                  <CardContent className="p-3 space-y-2">
                                <div className="flex items-start justify-between">
                                  <h4 className="font-medium text-sm line-clamp-2">
                                    {lead.title}
                                  </h4>
                                  <Badge
                                    variant="secondary"
                                    className={`text-xs ${
                                      PRIORITY_COLORS[lead.priority as keyof typeof PRIORITY_COLORS]
                                    }`}
                                  >
                                    {lead.priority}
                                  </Badge>
                                </div>

                                {lead.contact && (
                                  <div className="flex items-center space-x-2">
                                    <Avatar className="h-6 w-6">
                                      <AvatarFallback className="text-xs">
                                        {lead.contact.firstName?.[0]}
                                        {lead.contact.lastName?.[0]}
                                      </AvatarFallback>
                                    </Avatar>
                                    <span className="text-xs text-muted-foreground truncate">
                                      {lead.contact.firstName} {lead.contact.lastName}
                                    </span>
                                  </div>
                                )}

                                {lead.value && (
                                  <div className="flex items-center space-x-1 text-xs text-muted-foreground">
                                    <DollarSign className="h-3 w-3" />
                                    <span>{formatCurrency(Number(lead.value))}</span>
                                  </div>
                                )}

                                {lead.expectedCloseDate && (
                                  <div className="flex items-center space-x-1 text-xs text-muted-foreground">
                                    <Calendar className="h-3 w-3" />
                                    <span>
                                      {format(new Date(lead.expectedCloseDate), 'MMM d')}
                                    </span>
                                  </div>
                                )}

                                <div className="flex items-center justify-between text-xs text-muted-foreground">
                                  <span>
                                    {formatDistanceToNow(new Date(lead.createdAt), {
                                      addSuffix: true
                                    })}
                                  </span>
                                  {lead.score > 0 && (
                                    <Badge variant="outline" className="text-xs">
                                      {lead.score}
                                    </Badge>
                                  )}
                                </div>
                                  </CardContent>
                                </Card>
                              </div>
                            )}
                          </Draggable>
                        ))}
                        {provided.placeholder}
                      </div>
                    </CardContent>
                  )}
                </Droppable>
              </Card>
            );
              })}
            </div>
          </DragDropContext>
        </div>
      </div>
    </div>
  );
}
