import { NextRequest, NextResponse } from 'next/server';
import { headers } from 'next/headers';
import { externalIntegrationNotificationService } from '@/services/external-integration-notifications';
import type { IntegrationEvent } from '@/services/external-integration-notifications';

/**
 * POST /api/webhooks/integrations
 * Handle external integration webhooks and events
 */
export async function POST(request: NextRequest) {
  try {
    const headersList = await headers();
    const signature = headersList.get('x-webhook-signature');
    const integrationType = headersList.get('x-integration-type') || 'unknown';
    const tenantId = headersList.get('x-tenant-id') || headersList.get('tenant-id');

    // TODO: Verify webhook signature for security
    // This would depend on your integration provider's signature verification method

    const body = await request.json();
    console.log(`🔗 Received integration webhook from ${integrationType}:`, body);

    if (!tenantId) {
      return NextResponse.json({
        error: 'Tenant ID required in headers (x-tenant-id)'
      }, { status: 400 });
    }

    // Parse integration events based on provider
    const integrationEvents = parseIntegrationWebhook(body, integrationType, tenantId);

    if (!integrationEvents || integrationEvents.length === 0) {
      return NextResponse.json({ error: 'No valid integration events found' }, { status: 400 });
    }

    // Process integration events
    if (integrationEvents.length === 1) {
      await externalIntegrationNotificationService.processIntegrationEvent(integrationEvents[0]);
    } else {
      await externalIntegrationNotificationService.processBulkIntegrationEvents({
        events: integrationEvents
      });
    }

    console.log(`✅ Processed ${integrationEvents.length} integration event(s) successfully`);

    return NextResponse.json({
      success: true,
      message: 'Integration events processed successfully',
      data: {
        eventsProcessed: integrationEvents.length,
        integrationType
      }
    });

  } catch (error) {
    console.error('Integration webhook processing error:', error);
    return NextResponse.json(
      { error: 'Failed to process integration webhook' },
      { status: 500 }
    );
  }
}

/**
 * Parse integration webhook based on provider format
 */
function parseIntegrationWebhook(body: any, integrationType: string, tenantId: string): IntegrationEvent[] | null {
  try {
    switch (integrationType.toLowerCase()) {
      case 'salesforce':
        return parseSalesforceWebhook(body, tenantId);
      case 'hubspot':
        return parseHubSpotWebhook(body, tenantId);
      case 'zapier':
        return parseZapierWebhook(body, tenantId);
      case 'slack':
        return parseSlackWebhook(body, tenantId);
      case 'stripe':
        return parseStripeWebhook(body, tenantId);
      default:
        return parseGenericIntegrationWebhook(body, integrationType, tenantId);
    }
  } catch (error) {
    console.error('Error parsing integration webhook:', error);
    return null;
  }
}

/**
 * Parse Salesforce webhook
 */
function parseSalesforceWebhook(body: any, tenantId: string): IntegrationEvent[] {
  // Salesforce webhook format
  const events: IntegrationEvent[] = [];

  if (body.notifications) {
    for (const notification of body.notifications) {
      const event: IntegrationEvent = {
        tenantId,
        integrationId: body.organizationId || 'salesforce',
        integrationType: 'salesforce',
        eventType: mapSalesforceEventType(notification.type),
        message: notification.message || `Salesforce ${notification.type} event`,
        details: {
          notificationId: notification.id,
          sobjectType: notification.sobjectType,
          sobjectId: notification.sobjectId,
          changeType: notification.changeType
        },
        severity: notification.type === 'error' ? 'error' : 'info',
        timestamp: notification.createdDate ? new Date(notification.createdDate) : new Date()
      };
      events.push(event);
    }
  }

  return events;
}

/**
 * Parse HubSpot webhook
 */
function parseHubSpotWebhook(body: any, tenantId: string): IntegrationEvent[] {
  // HubSpot webhook format
  const events: IntegrationEvent[] = [];

  if (Array.isArray(body)) {
    for (const item of body) {
      const event: IntegrationEvent = {
        tenantId,
        integrationId: item.portalId?.toString() || 'hubspot',
        integrationType: 'hubspot',
        eventType: mapHubSpotEventType(item.subscriptionType),
        message: `HubSpot ${item.subscriptionType} event for ${item.objectType}`,
        details: {
          subscriptionId: item.subscriptionId,
          objectType: item.objectType,
          objectId: item.objectId,
          propertyName: item.propertyName,
          propertyValue: item.propertyValue
        },
        severity: 'info',
        timestamp: item.occurredAt ? new Date(item.occurredAt) : new Date()
      };
      events.push(event);
    }
  }

  return events;
}

/**
 * Parse Zapier webhook
 */
function parseZapierWebhook(body: any, tenantId: string): IntegrationEvent[] {
  const event: IntegrationEvent = {
    tenantId,
    integrationId: body.zap_id || 'zapier',
    integrationType: 'zapier',
    eventType: mapZapierEventType(body.event_type || body.type),
    message: body.message || `Zapier ${body.event_type || 'event'}`,
    details: {
      zapId: body.zap_id,
      stepId: body.step_id,
      taskId: body.task_id,
      status: body.status,
      data: body.data
    },
    severity: body.status === 'error' ? 'error' : 'info',
    timestamp: body.timestamp ? new Date(body.timestamp) : new Date()
  };

  return [event];
}

/**
 * Parse Slack webhook
 */
function parseSlackWebhook(body: any, tenantId: string): IntegrationEvent[] {
  const event: IntegrationEvent = {
    tenantId,
    integrationId: body.team_id || 'slack',
    integrationType: 'slack',
    eventType: mapSlackEventType(body.type || body.event?.type),
    message: body.event?.text || `Slack ${body.type} event`,
    details: {
      teamId: body.team_id,
      eventType: body.event?.type,
      userId: body.event?.user,
      channelId: body.event?.channel,
      timestamp: body.event?.ts
    },
    severity: 'info',
    timestamp: body.event?.event_ts ? new Date(parseInt(body.event.event_ts) * 1000) : new Date()
  };

  return [event];
}

/**
 * Parse Stripe webhook
 */
function parseStripeWebhook(body: any, tenantId: string): IntegrationEvent[] {
  const event: IntegrationEvent = {
    tenantId,
    integrationId: body.account || 'stripe',
    integrationType: 'stripe',
    eventType: mapStripeEventType(body.type),
    message: `Stripe ${body.type} event`,
    details: {
      eventId: body.id,
      objectType: body.data?.object?.object,
      objectId: body.data?.object?.id,
      livemode: body.livemode,
      apiVersion: body.api_version
    },
    severity: body.type.includes('failed') ? 'error' : 'info',
    timestamp: body.created ? new Date(body.created * 1000) : new Date()
  };

  return [event];
}

/**
 * Parse generic integration webhook
 */
function parseGenericIntegrationWebhook(body: any, integrationType: string, tenantId: string): IntegrationEvent[] {
  const event: IntegrationEvent = {
    tenantId,
    integrationId: body.integrationId || body.id || integrationType,
    integrationType,
    eventType: body.eventType || body.type || 'webhook_received',
    message: body.message || `${integrationType} webhook received`,
    details: body.data || body,
    severity: body.severity || (body.error ? 'error' : 'info'),
    timestamp: body.timestamp ? new Date(body.timestamp) : new Date(),
    recordsAffected: body.recordsAffected,
    errorCode: body.errorCode
  };

  return [event];
}

// Event type mapping functions
function mapSalesforceEventType(type: string): string {
  const eventMap: Record<string, string> = {
    'sync_complete': 'sync_completed',
    'sync_error': 'sync_failed',
    'api_limit': 'api_rate_limit',
    'connection_error': 'connection_lost',
    'error': 'api_error'
  };
  return eventMap[type] || 'webhook_received';
}

function mapHubSpotEventType(subscriptionType: string): string {
  const eventMap: Record<string, string> = {
    'contact.creation': 'sync_completed',
    'contact.deletion': 'sync_completed',
    'contact.propertyChange': 'sync_completed',
    'company.creation': 'sync_completed',
    'deal.creation': 'sync_completed'
  };
  return eventMap[subscriptionType] || 'webhook_received';
}

function mapZapierEventType(eventType: string): string {
  const eventMap: Record<string, string> = {
    'task_success': 'sync_completed',
    'task_error': 'sync_failed',
    'zap_turned_off': 'connection_lost',
    'zap_turned_on': 'connection_restored'
  };
  return eventMap[eventType] || 'webhook_received';
}

function mapSlackEventType(eventType: string): string {
  const eventMap: Record<string, string> = {
    'app_uninstalled': 'connection_lost',
    'app_installed': 'connection_restored',
    'message': 'webhook_received'
  };
  return eventMap[eventType] || 'webhook_received';
}

function mapStripeEventType(eventType: string): string {
  const eventMap: Record<string, string> = {
    'invoice.payment_succeeded': 'sync_completed',
    'invoice.payment_failed': 'sync_failed',
    'customer.created': 'sync_completed',
    'charge.failed': 'api_error'
  };
  return eventMap[eventType] || 'webhook_received';
}

// Health check endpoint
export async function GET() {
  return NextResponse.json({
    status: 'ok',
    service: 'integration-webhook',
    timestamp: new Date().toISOString()
  });
}
