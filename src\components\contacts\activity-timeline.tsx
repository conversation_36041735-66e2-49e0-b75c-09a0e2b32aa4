'use client';

import React, { useState, useEffect } from 'react';
import {
  Plus,
  Mail,
  Phone,
  Calendar,
  FileText,
  CheckCircle,
  Clock,
  User,
  Edit,
  Trash2,
  MessageSquare,
  Video,
  Target,
  TrendingUp,
  Activity as ActivityIcon,
  Users
} from 'lucide-react';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { ActivityWithRelations } from '@/services/activity-management';
import { usePermissions } from '@/components/providers/permission-provider';
import { PermissionResource, PermissionAction } from '@/types';
import { PermissionGate } from '@/components/ui/permission-gate';
import { useEnhancedDeleteDialog, deleteConfigurations } from '@/components/ui/enhanced-delete-dialog';
import { cn } from '@/lib/utils';

interface ActivityTimelineProps {
  relatedToType: 'contact' | 'company' | 'lead' | 'opportunity';
  relatedToId: string;
  className?: string;
}

const activityIcons = {
  email: Mail,
  call: Phone,
  meeting: Calendar,
  note: FileText,
  task: CheckCircle,
};

const activityColors = {
  email: 'text-blue-600 bg-blue-100',
  call: 'text-green-600 bg-green-100',
  meeting: 'text-purple-600 bg-purple-100',
  note: 'text-gray-600 bg-gray-100',
  task: 'text-orange-600 bg-orange-100',
};

export function ActivityTimeline({ relatedToType, relatedToId, className }: ActivityTimelineProps) {
  const [activities, setActivities] = useState<ActivityWithRelations[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showAddForm, setShowAddForm] = useState(false);
  const [newActivity, setNewActivity] = useState({
    type: 'note',
    subject: '',
    description: '',
    scheduledAt: '',
  });

  const { hasPermission } = usePermissions();

  // Enhanced delete dialog
  const { showDeleteDialog, DialogComponent } = useEnhancedDeleteDialog();

  // Fetch activities
  const fetchActivities = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        relatedToType,
        relatedToId,
        limit: '20',
        sortBy: 'createdAt',
        sortOrder: 'desc'
      });

      const response = await fetch(`/api/activities?${params}`);
      const data = await response.json();

      if (data.success) {
        setActivities(data.data.activities || []);
      } else {
        setError(data.error || 'Failed to load activities');
      }
    } catch (err) {
      console.error('Error fetching activities:', err);
      setError('Failed to load activities');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchActivities();
  }, [relatedToType, relatedToId]);

  // Handle add activity
  const handleAddActivity = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!newActivity.subject.trim()) {
      alert('Subject is required');
      return;
    }

    try {
      const response = await fetch('/api/activities', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...newActivity,
          relatedToType,
          relatedToId,
          scheduledAt: newActivity.scheduledAt ? new Date(newActivity.scheduledAt).toISOString() : null,
        }),
      });

      const data = await response.json();

      if (data.success) {
        setActivities([data.data.activity, ...activities]);
        setNewActivity({
          type: 'note',
          subject: '',
          description: '',
          scheduledAt: '',
        });
        setShowAddForm(false);
      } else {
        alert(data.error || 'Failed to create activity');
      }
    } catch (err) {
      console.error('Error creating activity:', err);
      alert('Failed to create activity. Please try again.');
    }
  };

  // Handle complete activity
  const handleCompleteActivity = async (activityId: string) => {
    try {
      const response = await fetch(`/api/activities/${activityId}`, {
        method: 'PATCH',
      });

      const data = await response.json();

      if (data.success) {
        setActivities(activities.map(activity => 
          activity.id === activityId 
            ? { ...activity, completedAt: new Date() }
            : activity
        ));
      } else {
        alert(data.error || 'Failed to complete activity');
      }
    } catch (err) {
      console.error('Error completing activity:', err);
      alert('Failed to complete activity. Please try again.');
    }
  };

  // Handle delete activity
  const handleDeleteActivity = async (activity: ActivityWithRelations) => {
    const confirmed = await showDeleteDialog({
      ...deleteConfigurations.activity(activity.subject),
      onConfirm: async () => {
        try {
          const response = await fetch(`/api/activities?activityId=${activity.id}`, {
            method: 'DELETE',
          });

          const data = await response.json();

          if (data.success) {
            setActivities(activities.filter(a => a.id !== activity.id));
          } else {
            throw new Error(data.error || 'Failed to delete activity');
          }
        } catch (err) {
          console.error('Error deleting activity:', err);
          throw err;
        }
      },
    });
  };

  if (loading) {
    return (
      <div className={`space-y-4 ${className}`}>
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div className="space-y-3">
            {[1, 2, 3].map(i => (
              <div key={i} className="flex space-x-3">
                <div className="w-8 h-8 bg-gray-200 rounded-full"></div>
                <div className="flex-1">
                  <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  // Calculate activity statistics
  const totalActivities = activities.length;
  const completedActivities = activities.filter(activity => activity.completedAt).length;
  const pendingActivities = activities.filter(activity => !activity.completedAt && activity.scheduledAt && new Date(activity.scheduledAt) > new Date()).length;
  const overdueActivities = activities.filter(activity => !activity.completedAt && activity.scheduledAt && new Date(activity.scheduledAt) < new Date()).length;

  const activityTypes = activities.reduce((acc, activity) => {
    acc[activity.type] = (acc[activity.type] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  if (loading) {
    return (
      <div className={cn("space-y-6", className)}>
        {/* Loading Statistics */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {Array.from({ length: 4 }).map((_, i) => (
            <div key={i} className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4">
              <Skeleton className="h-4 w-16 mb-2" />
              <Skeleton className="h-6 w-8" />
            </div>
          ))}
        </div>
        {/* Loading Activity Cards */}
        <div className="space-y-4">
          {Array.from({ length: 3 }).map((_, i) => (
            <div key={i} className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4">
              <div className="flex space-x-3">
                <Skeleton className="h-8 w-8 rounded-full" />
                <div className="flex-1 space-y-2">
                  <Skeleton className="h-4 w-3/4" />
                  <Skeleton className="h-3 w-1/2" />
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={cn("text-center py-8", className)}>
        <div className="bg-red-100 dark:bg-red-900/20 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
          <Clock className="h-8 w-8 text-red-500" />
        </div>
        <p className="text-red-600 dark:text-red-400 mb-2">Failed to load activities</p>
        <p className="text-sm text-gray-500 dark:text-gray-400">{error}</p>
        <Button
          variant="outline"
          size="sm"
          onClick={fetchActivities}
          className="mt-4"
        >
          Try Again
        </Button>
      </div>
    );
  }

  return (
    <div className={cn("space-y-6", className)}>
      {/* Activity Statistics */}
      {totalActivities > 0 && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg p-4">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-blue-100 dark:bg-blue-800 rounded-lg">
                <ActivityIcon className="w-5 h-5 text-blue-600 dark:text-blue-300" />
              </div>
              <div>
                <p className="text-sm font-medium text-blue-900 dark:text-blue-100">Total</p>
                <p className="text-2xl font-bold text-blue-700 dark:text-blue-300">{totalActivities}</p>
              </div>
            </div>
          </div>

          <div className="bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-lg p-4">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-green-100 dark:bg-green-800 rounded-lg">
                <CheckCircle className="w-5 h-5 text-green-600 dark:text-green-300" />
              </div>
              <div>
                <p className="text-sm font-medium text-green-900 dark:text-green-100">Completed</p>
                <p className="text-2xl font-bold text-green-700 dark:text-green-300">{completedActivities}</p>
              </div>
            </div>
          </div>

          <div className="bg-gradient-to-br from-yellow-50 to-amber-50 dark:from-yellow-900/20 dark:to-amber-900/20 rounded-lg p-4">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-yellow-100 dark:bg-yellow-800 rounded-lg">
                <Clock className="w-5 h-5 text-yellow-600 dark:text-yellow-300" />
              </div>
              <div>
                <p className="text-sm font-medium text-yellow-900 dark:text-yellow-100">Pending</p>
                <p className="text-2xl font-bold text-yellow-700 dark:text-yellow-300">{pendingActivities}</p>
              </div>
            </div>
          </div>

          <div className="bg-gradient-to-br from-red-50 to-rose-50 dark:from-red-900/20 dark:to-rose-900/20 rounded-lg p-4">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-red-100 dark:bg-red-800 rounded-lg">
                <Target className="w-5 h-5 text-red-600 dark:text-red-300" />
              </div>
              <div>
                <p className="text-sm font-medium text-red-900 dark:text-red-100">Overdue</p>
                <p className="text-2xl font-bold text-red-700 dark:text-red-300">{overdueActivities}</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Activity Type Distribution */}
      {Object.keys(activityTypes).length > 0 && (
        <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4">
          <h4 className="font-medium text-gray-900 dark:text-gray-100 mb-3 flex items-center">
            <TrendingUp className="w-4 h-4 mr-2 text-gray-500" />
            Activity Types
          </h4>
          <div className="flex flex-wrap gap-2">
            {Object.entries(activityTypes).map(([type, count]) => {
              const Icon = activityIcons[type as keyof typeof activityIcons] || FileText;
              return (
                <div key={type} className="flex items-center space-x-2 bg-white dark:bg-gray-600 rounded-lg px-3 py-2">
                  <Icon className="w-4 h-4 text-gray-500" />
                  <span className="text-sm font-medium text-gray-700 dark:text-gray-300 capitalize">{type}</span>
                  <Badge variant="secondary" className="text-xs">{count}</Badge>
                </div>
              );
            })}
          </div>
        </div>
      )}

      {/* Header with Add Button */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <h4 className="font-semibold text-gray-900 dark:text-gray-100">Recent Activities</h4>
          {totalActivities > 0 && (
            <Badge variant="outline">{totalActivities}</Badge>
          )}
        </div>
        <PermissionGate
          resource={PermissionResource.CONTACTS}
          action={PermissionAction.CREATE}
        >
          <Button
            onClick={() => setShowAddForm(!showAddForm)}
            size="sm"
            className="bg-purple-600 hover:bg-purple-700 text-white"
          >
            <Plus className="w-4 h-4 mr-2" />
            Add Activity
          </Button>
        </PermissionGate>
      </div>

      {/* Add Activity Form - Enhanced */}
      {showAddForm && (
        <div className="bg-gradient-to-br from-purple-50 to-indigo-50 dark:from-purple-900/20 dark:to-indigo-900/20 rounded-lg p-6 border border-purple-200 dark:border-purple-700">
          <div className="flex items-center space-x-2 mb-4">
            <div className="p-2 bg-purple-100 dark:bg-purple-800 rounded-lg">
              <Plus className="w-5 h-5 text-purple-600 dark:text-purple-300" />
            </div>
            <h4 className="font-semibold text-purple-900 dark:text-purple-100">Add New Activity</h4>
          </div>

          <form onSubmit={handleAddActivity} className="space-y-4">
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-purple-900 dark:text-purple-100 mb-2">
                  Activity Type
                </label>
                <select
                  value={newActivity.type}
                  onChange={(e) => setNewActivity({ ...newActivity, type: e.target.value })}
                  className="w-full border border-purple-300 dark:border-purple-600 rounded-lg px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-purple-500 focus:border-purple-500"
                >
                  <option value="note">📝 Note</option>
                  <option value="email">📧 Email</option>
                  <option value="call">📞 Call</option>
                  <option value="meeting">🤝 Meeting</option>
                  <option value="task">✅ Task</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-purple-900 dark:text-purple-100 mb-2">
                  Scheduled Date (Optional)
                </label>
                <input
                  type="datetime-local"
                  value={newActivity.scheduledAt}
                  onChange={(e) => setNewActivity({ ...newActivity, scheduledAt: e.target.value })}
                  className="w-full border border-purple-300 dark:border-purple-600 rounded-lg px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-purple-500 focus:border-purple-500"
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-purple-900 dark:text-purple-100 mb-2">
                Subject *
              </label>
              <input
                type="text"
                value={newActivity.subject}
                onChange={(e) => setNewActivity({ ...newActivity, subject: e.target.value })}
                className="w-full border border-purple-300 dark:border-purple-600 rounded-lg px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-purple-500 focus:border-purple-500"
                placeholder="Enter activity subject..."
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-purple-900 dark:text-purple-100 mb-2">
                Description
              </label>
              <textarea
                value={newActivity.description}
                onChange={(e) => setNewActivity({ ...newActivity, description: e.target.value })}
                rows={3}
                className="w-full border border-purple-300 dark:border-purple-600 rounded-lg px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-purple-500 focus:border-purple-500"
                placeholder="Enter activity description..."
              />
            </div>

            <div className="flex justify-end space-x-3 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => setShowAddForm(false)}
                className="border-purple-300 text-purple-700 hover:bg-purple-50 dark:border-purple-600 dark:text-purple-300 dark:hover:bg-purple-900/20"
              >
                Cancel
              </Button>
              <Button
                type="submit"
                className="bg-purple-600 hover:bg-purple-700 text-white"
              >
                <Plus className="w-4 h-4 mr-2" />
                Add Activity
              </Button>
            </div>
          </form>
        </div>
      )}

      {/* Error Message */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="text-red-800">{error}</div>
        </div>
      )}

      {/* Activities List */}
      <div className="flow-root">
        {activities.length === 0 ? (
          <div className="text-center py-12">
            <div className="bg-gray-100 dark:bg-gray-700 rounded-full w-20 h-20 flex items-center justify-center mx-auto mb-6">
              <Clock className="h-10 w-10 text-gray-400" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
              No activities yet
            </h3>
            <p className="text-gray-500 dark:text-gray-400 mb-6 max-w-sm mx-auto">
              Add your first activity to start tracking interactions and building a comprehensive timeline
            </p>
            <PermissionGate
              resource={PermissionResource.CONTACTS}
              action={PermissionAction.CREATE}
            >
              <Button
                onClick={() => setShowAddForm(true)}
                className="bg-purple-600 hover:bg-purple-700 text-white"
              >
                <Plus className="w-4 h-4 mr-2" />
                Add First Activity
              </Button>
            </PermissionGate>
          </div>
        ) : (
          <ul className="space-y-6">
            {activities.map((activity, index) => {
              const Icon = activityIcons[activity.type as keyof typeof activityIcons] || FileText;
              const colorClasses = activityColors[activity.type as keyof typeof activityColors] || 'text-gray-600 bg-gray-100';
              const isCompleted = !!activity.completedAt;
              const isOverdue = !isCompleted && activity.scheduledAt && new Date(activity.scheduledAt) < new Date();
              const isPending = !isCompleted && activity.scheduledAt && new Date(activity.scheduledAt) > new Date();

              return (
                <li key={activity.id} className="relative">
                  {index !== activities.length - 1 && (
                    <div className="absolute left-6 top-12 -bottom-6 w-0.5 bg-gradient-to-b from-gray-300 to-transparent dark:from-gray-600" />
                  )}

                  <div className={cn(
                    "bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm border transition-all duration-200 hover:shadow-md",
                    isCompleted ? "border-green-200 dark:border-green-700 bg-green-50/50 dark:bg-green-900/10" :
                    isOverdue ? "border-red-200 dark:border-red-700 bg-red-50/50 dark:bg-red-900/10" :
                    isPending ? "border-yellow-200 dark:border-yellow-700 bg-yellow-50/50 dark:bg-yellow-900/10" :
                    "border-gray-200 dark:border-gray-700"
                  )}>
                    <div className="flex space-x-4">
                      <div className={cn(
                        "flex-shrink-0 w-12 h-12 rounded-xl flex items-center justify-center shadow-sm",
                        colorClasses,
                        isCompleted && "bg-green-100 text-green-600 dark:bg-green-900/30 dark:text-green-400",
                        isOverdue && "bg-red-100 text-red-600 dark:bg-red-900/30 dark:text-red-400"
                      )}>
                        <Icon className="w-5 h-5" />
                      </div>

                      <div className="flex-1 min-w-0">
                        <div className="flex items-start justify-between mb-3">
                          <div className="flex items-center space-x-3">
                            <h4 className="text-base font-semibold text-gray-900 dark:text-gray-100">
                              {activity.subject}
                            </h4>
                            {isCompleted && (
                              <Badge className="bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-300">
                                <CheckCircle className="w-3 h-3 mr-1" />
                                Completed
                              </Badge>
                            )}
                            {isOverdue && (
                              <Badge className="bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-300">
                                <Clock className="w-3 h-3 mr-1" />
                                Overdue
                              </Badge>
                            )}
                            {isPending && (
                              <Badge className="bg-yellow-100 text-yellow-700 dark:bg-yellow-900/30 dark:text-yellow-300">
                                <Clock className="w-3 h-3 mr-1" />
                                Pending
                              </Badge>
                            )}
                          </div>

                          <div className="flex items-center space-x-1">
                            {!isCompleted && activity.type === 'task' && (
                              <Button
                                size="sm"
                                variant="ghost"
                                onClick={() => handleCompleteActivity(activity.id)}
                                className="h-8 px-2 text-green-600 hover:text-green-700 hover:bg-green-50 dark:hover:bg-green-900/20"
                              >
                                <CheckCircle className="w-4 h-4" />
                              </Button>
                            )}
                            <PermissionGate
                              resource={PermissionResource.CONTACTS}
                              action={PermissionAction.DELETE}
                            >
                              <Button
                                size="sm"
                                variant="ghost"
                                onClick={() => handleDeleteActivity(activity)}
                                className="h-8 px-2 text-red-600 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-900/20"
                              >
                                <Trash2 className="w-4 h-4" />
                              </Button>
                            </PermissionGate>
                          </div>
                        </div>

                        {activity.description && (
                          <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-3 mb-4">
                            <p className="text-sm text-gray-700 dark:text-gray-300 leading-relaxed">
                              {activity.description}
                            </p>
                          </div>
                        )}

                        {/* Activity Details Grid */}
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                          <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-3">
                            <div className="flex items-center space-x-2 mb-1">
                              <Calendar className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                              <span className="text-xs font-medium text-blue-900 dark:text-blue-100 uppercase tracking-wide">Created</span>
                            </div>
                            <p className="text-sm font-medium text-blue-900 dark:text-blue-100">
                              {new Date(activity.createdAt).toLocaleDateString()} at{' '}
                              {new Date(activity.createdAt).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                            </p>
                          </div>

                          {activity.owner && (
                            <div className="bg-indigo-50 dark:bg-indigo-900/20 rounded-lg p-3">
                              <div className="flex items-center space-x-2 mb-1">
                                <User className="w-4 h-4 text-indigo-600 dark:text-indigo-400" />
                                <span className="text-xs font-medium text-indigo-900 dark:text-indigo-100 uppercase tracking-wide">Owner</span>
                              </div>
                              <p className="text-sm font-medium text-indigo-900 dark:text-indigo-100">
                                {activity.owner.firstName} {activity.owner.lastName}
                              </p>
                            </div>
                          )}

                          {activity.scheduledAt && (
                            <div className="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-3">
                              <div className="flex items-center space-x-2 mb-1">
                                <Clock className="w-4 h-4 text-purple-600 dark:text-purple-400" />
                                <span className="text-xs font-medium text-purple-900 dark:text-purple-100 uppercase tracking-wide">Scheduled</span>
                              </div>
                              <p className="text-sm font-medium text-purple-900 dark:text-purple-100">
                                {new Date(activity.scheduledAt).toLocaleDateString()} at{' '}
                                {new Date(activity.scheduledAt).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                              </p>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                </li>
              );
            })}
          </ul>
        )}
      </div>

      {/* Enhanced Delete Dialog */}
      <DialogComponent />
    </div>
  );
}
