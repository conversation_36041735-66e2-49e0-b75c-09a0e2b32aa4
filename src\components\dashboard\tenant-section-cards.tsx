'use client';

import { useEffect, useState } from 'react';
import { 
  UsersIcon, 
  BuildingOfficeIcon, 
  ChartBarIcon, 
  CurrencyDollarIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';
import { cn, formatCurrency, formatNumber } from '@/lib/utils';
import { TenantQuickStats } from '@/services/tenant-dashboard-analytics';
import { usePermissions } from '@/components/providers/permission-provider';
import { PermissionResource, PermissionAction } from '@/types';

interface StatCardProps {
  title: string;
  value: string | number;
  change: {
    value: number;
    type: 'increase' | 'decrease';
    period: string;
  };
  icon: React.ComponentType<{ className?: string }>;
  trend: string;
  loading?: boolean;
  error?: string;
  onClick?: () => void;
}

function StatCard({ 
  title, 
  value, 
  change, 
  icon: Icon, 
  trend, 
  loading = false, 
  error,
  onClick 
}: StatCardProps) {
  const isPositive = change.type === 'increase';
  
  if (loading) {
    return (
      <div className="rounded-lg border bg-card text-card-foreground shadow-sm p-6 animate-pulse">
        <div className="flex items-center justify-between space-y-0 pb-2">
          <div className="h-4 bg-gray-200 rounded w-24"></div>
          <div className="h-4 w-4 bg-gray-200 rounded"></div>
        </div>
        <div className="space-y-1">
          <div className="h-8 bg-gray-200 rounded w-20"></div>
          <div className="h-4 bg-gray-200 rounded w-32"></div>
          <div className="h-3 bg-gray-200 rounded w-28"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="rounded-lg border bg-card text-card-foreground shadow-sm p-6">
        <div className="flex items-center justify-between space-y-0 pb-2">
          <h3 className="tracking-tight text-sm font-medium text-muted-foreground">
            {title}
          </h3>
          <ExclamationTriangleIcon className="h-4 w-4 text-red-500" />
        </div>
        <div className="space-y-1">
          <div className="text-2xl font-bold text-red-500">
            Error
          </div>
          <div className="text-xs text-red-500">
            {error}
          </div>
        </div>
      </div>
    );
  }
  
  return (
    <div 
      className={cn(
        "rounded-lg border bg-card text-card-foreground shadow-sm p-6 transition-all duration-200",
        onClick && "cursor-pointer hover:shadow-md hover:scale-[1.02]"
      )}
      onClick={onClick}
    >
      <div className="flex items-center justify-between space-y-0 pb-2">
        <h3 className="tracking-tight text-sm font-medium text-muted-foreground">
          {title}
        </h3>
        <Icon className="h-4 w-4 text-muted-foreground" />
      </div>
      <div className="space-y-1">
        <div className="text-2xl font-bold">
          {typeof value === 'number' ? formatNumber(value) : value}
        </div>
        <div className="flex items-center space-x-1 text-xs">
          {isPositive ? (
            <ArrowUpIcon className="h-3 w-3 text-green-600" />
          ) : (
            <ArrowDownIcon className="h-3 w-3 text-red-600" />
          )}
          <span className={cn(
            "font-medium",
            isPositive ? "text-green-600" : "text-red-600"
          )}>
            {isPositive ? '+' : ''}{change.value.toFixed(1)}%
          </span>
          <span className="text-muted-foreground">{change.period}</span>
        </div>
        <p className="text-xs text-muted-foreground">
          {trend}
        </p>
      </div>
    </div>
  );
}

interface TenantSectionCardsProps {
  onCardClick?: (cardType: string) => void;
}

export function TenantSectionCards({ onCardClick }: TenantSectionCardsProps) {
  const [stats, setStats] = useState<TenantQuickStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { canAccessResource } = usePermissions();

  useEffect(() => {
    fetchStats();
  }, []);

  const fetchStats = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await fetch('/api/dashboard/analytics?type=quick');
      
      if (!response.ok) {
        throw new Error(`Failed to fetch stats: ${response.statusText}`);
      }
      
      const data = await response.json();
      
      if (!data.success) {
        throw new Error(data.error || 'Failed to fetch dashboard stats');
      }
      
      setStats(data.data);
    } catch (err) {
      console.error('Error fetching dashboard stats:', err);
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  const handleCardClick = (cardType: string) => {
    if (onCardClick) {
      onCardClick(cardType);
    }
  };

  // Define permission requirements for each card type
  const getCardPermissions = (cardType: string): { resource: PermissionResource; action: PermissionAction } | null => {
    switch (cardType) {
      case 'revenue':
        return { resource: PermissionResource.REPORTS, action: PermissionAction.READ };
      case 'customers':
        return { resource: PermissionResource.CONTACTS, action: PermissionAction.READ };
      case 'accounts':
        return { resource: PermissionResource.COMPANIES, action: PermissionAction.READ };
      case 'growth':
        return { resource: PermissionResource.REPORTS, action: PermissionAction.READ };
      default:
        return null;
    }
  };

  // Check if user has permission to view a specific card
  const canViewCard = (cardType: string): boolean => {
    const permissions = getCardPermissions(cardType);
    if (!permissions) return false;
    return canAccessResource(permissions.resource, permissions.action);
  };

  const getStatsData = () => {
    if (!stats) {
      return [];
    }

    const allCards = [
      {
        title: 'Total Revenue',
        value: formatCurrency(stats.totalRevenue),
        change: {
          value: stats.revenueGrowth,
          type: stats.revenueGrowth >= 0 ? 'increase' as const : 'decrease' as const,
          period: 'from last month',
        },
        icon: CurrencyDollarIcon,
        trend: stats.revenueGrowth >= 0 ? 'Revenue trending up' : 'Revenue needs attention',
        cardType: 'revenue'
      },
      {
        title: 'New Customers',
        value: stats.newCustomers,
        change: {
          value: stats.customerGrowth,
          type: stats.customerGrowth >= 0 ? 'increase' as const : 'decrease' as const,
          period: 'from last month',
        },
        icon: UsersIcon,
        trend: stats.customerGrowth >= 0 ? 'Customer growth is strong' : 'Customer acquisition needs focus',
        cardType: 'customers'
      },
      {
        title: 'Active Accounts',
        value: stats.activeAccounts,
        change: {
          value: stats.accountGrowth,
          type: stats.accountGrowth >= 0 ? 'increase' as const : 'decrease' as const,
          period: 'from last month',
        },
        icon: BuildingOfficeIcon,
        trend: stats.accountGrowth >= 0 ? 'Account growth is healthy' : 'Account growth slowing',
        cardType: 'accounts'
      },
      {
        title: 'Growth Rate',
        value: `${stats.growthRate.toFixed(1)}%`,
        change: {
          value: stats.growthRate,
          type: stats.growthRate >= 0 ? 'increase' as const : 'decrease' as const,
          period: 'overall growth',
        },
        icon: ChartBarIcon,
        trend: stats.growthRate >= 0 ? 'Steady performance increase' : 'Performance needs improvement',
        cardType: 'growth'
      },
    ];

    // Filter cards based on user permissions
    return allCards.filter(card => canViewCard(card.cardType));
  };

  const statsData = getStatsData();

  // Dynamic grid classes based on number of visible cards
  const getGridClasses = (cardCount: number) => {
    if (cardCount === 0) return "hidden";
    if (cardCount === 1) return "grid gap-4 md:gap-6 lg:grid-cols-1 px-4 lg:px-6";
    if (cardCount === 2) return "grid gap-4 md:grid-cols-2 md:gap-6 px-4 lg:px-6";
    if (cardCount === 3) return "grid gap-4 md:grid-cols-2 md:gap-6 lg:grid-cols-3 px-4 lg:px-6";
    return "grid gap-4 md:grid-cols-2 md:gap-6 lg:grid-cols-4 px-4 lg:px-6";
  };

  return (
    <div className={getGridClasses(statsData.length)}>
      {statsData.map((stat, index) => (
        <StatCard
          key={index}
          title={stat.title}
          value={stat.value}
          change={stat.change}
          icon={stat.icon}
          trend={stat.trend}
          loading={loading}
          error={error}
          onClick={() => handleCardClick(stat.cardType)}
        />
      ))}
    </div>
  );
}

// Export individual stat card for reuse
export { StatCard };
