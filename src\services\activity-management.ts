import { prisma } from '@/lib/prisma';
import { Activity } from '@prisma/client';
import { z } from 'zod';

// Validation schemas
export const createActivitySchema = z.object({
  relatedToType: z.enum(['contact', 'lead', 'opportunity', 'company']),
  relatedToId: z.string().min(1, 'Related ID is required'),
  type: z.enum(['email', 'call', 'meeting', 'note', 'task']),
  subject: z.string().min(1, 'Subject is required').max(255),
  description: z.string().optional().nullable(),
  scheduledAt: z.string().datetime().optional().nullable(),
  completedAt: z.string().datetime().optional().nullable(),
  ownerId: z.string().optional().nullable(),
});

export const updateActivitySchema = createActivitySchema.partial();

// Types
type UserSummary = {
  id: string;
  firstName: string | null;
  lastName: string | null;
  email: string;
};

export interface ActivityWithRelations extends Activity {
  owner?: UserSummary | null;
  createdBy?: UserSummary | null;
}

export interface ActivityFilters {
  relatedToType?: string;
  relatedToId?: string;
  type?: string;
  ownerId?: string;
  completed?: boolean;
  page?: number;
  limit?: number;
  sortBy?: 'scheduledAt' | 'completedAt' | 'createdAt' | 'updatedAt';
  sortOrder?: 'asc' | 'desc';
}

export class ActivityManagementService {
  /**
   * Create a new activity
   */
  async createActivity(
    tenantId: string,
    activityData: z.infer<typeof createActivitySchema>,
    createdBy: string
  ): Promise<ActivityWithRelations> {
    const validatedData = createActivitySchema.parse(activityData);

    // Verify owner exists if provided
    if (validatedData.ownerId) {
      const owner = await prisma.user.findFirst({
        where: {
          id: validatedData.ownerId,
          tenantUsers: {
            some: {
              tenantId,
              status: 'active'
            }
          }
        }
      });

      if (!owner) {
        throw new Error('Owner not found or not part of tenant');
      }
    }

    // Verify related entity exists
    await this.verifyRelatedEntity(validatedData.relatedToType, validatedData.relatedToId, tenantId);

    const activity = await prisma.activity.create({
      data: {
        ...validatedData,
        scheduledAt: validatedData.scheduledAt ? new Date(validatedData.scheduledAt) : null,
        completedAt: validatedData.completedAt ? new Date(validatedData.completedAt) : null,
        tenantId,
        createdById: createdBy,
        ownerId: validatedData.ownerId || createdBy
      },
      include: {
        owner: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true
          }
        },
        createdBy: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true
          }
        }
      }
    });

    return activity;
  }

  /**
   * Get activity by ID
   */
  async getActivity(activityId: string, tenantId: string): Promise<ActivityWithRelations | null> {
    return await prisma.activity.findFirst({
      where: {
        id: activityId,
        tenantId
      },
      include: {
        owner: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true
          }
        },
        createdBy: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true
          }
        }
      }
    });
  }

  /**
   * Get activities with filtering and pagination
   */
  async getActivities(tenantId: string, filters: ActivityFilters = {}): Promise<{
    activities: ActivityWithRelations[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  }> {
    const {
      relatedToType,
      relatedToId,
      type,
      ownerId,
      completed,
      page = 1,
      limit = 20,
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = filters;

    const skip = (page - 1) * limit;

    // Build where clause
    const where: Record<string, unknown> = {
      tenantId
    };

    if (relatedToType) {
      where.relatedToType = relatedToType;
    }

    if (relatedToId) {
      where.relatedToId = relatedToId;
    }

    if (type) {
      where.type = type;
    }

    if (ownerId) {
      where.ownerId = ownerId;
    }

    if (completed !== undefined) {
      where.completedAt = completed ? { not: null } : null;
    }

    // Get total count
    const total = await prisma.activity.count({ where });

    // Get activities
    const activities = await prisma.activity.findMany({
      where,
      include: {
        owner: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true
          }
        },
        createdBy: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true
          }
        }
      },
      orderBy: {
        [sortBy]: sortOrder
      },
      skip,
      take: limit
    });

    return {
      activities,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit)
    };
  }

  /**
   * Update activity
   */
  async updateActivity(
    activityId: string,
    tenantId: string,
    updateData: z.infer<typeof updateActivitySchema>,
    updatedBy: string
  ): Promise<ActivityWithRelations> {
    const validatedData = updateActivitySchema.parse(updateData);

    // Verify activity exists
    const existingActivity = await prisma.activity.findFirst({
      where: {
        id: activityId,
        tenantId
      }
    });

    if (!existingActivity) {
      throw new Error('Activity not found');
    }

    // Verify owner exists if provided
    if (validatedData.ownerId) {
      const owner = await prisma.user.findFirst({
        where: {
          id: validatedData.ownerId,
          tenantUsers: {
            some: {
              tenantId,
              status: 'active'
            }
          }
        }
      });

      if (!owner) {
        throw new Error('Owner not found or not part of tenant');
      }
    }

    // Verify related entity exists if changed
    if (validatedData.relatedToType && validatedData.relatedToId) {
      await this.verifyRelatedEntity(validatedData.relatedToType, validatedData.relatedToId, tenantId);
    }

    const activity = await prisma.activity.update({
      where: {
        id: activityId
      },
      data: {
        ...validatedData,
        scheduledAt: validatedData.scheduledAt ? new Date(validatedData.scheduledAt) : undefined,
        completedAt: validatedData.completedAt ? new Date(validatedData.completedAt) : undefined,
      },
      include: {
        owner: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true
          }
        },
        createdBy: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true
          }
        }
      }
    });

    return activity;
  }

  /**
   * Delete activity
   */
  async deleteActivity(activityId: string, tenantId: string): Promise<void> {
    const activity = await prisma.activity.findFirst({
      where: {
        id: activityId,
        tenantId
      }
    });

    if (!activity) {
      throw new Error('Activity not found');
    }

    await prisma.activity.delete({
      where: {
        id: activityId
      }
    });
  }

  /**
   * Get activities for a specific entity
   */
  async getActivitiesForEntity(
    relatedToType: string,
    relatedToId: string,
    tenantId: string,
    limit: number = 10
  ): Promise<ActivityWithRelations[]> {
    return await prisma.activity.findMany({
      where: {
        relatedToType,
        relatedToId,
        tenantId
      },
      include: {
        owner: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true
          }
        },
        createdBy: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      },
      take: limit
    });
  }

  /**
   * Mark activity as completed
   */
  async completeActivity(activityId: string, tenantId: string): Promise<ActivityWithRelations> {
    return await this.updateActivity(activityId, tenantId, {
      completedAt: new Date().toISOString()
    }, '');
  }

  /**
   * Verify that the related entity exists
   */
  private async verifyRelatedEntity(relatedToType: string, relatedToId: string, tenantId: string): Promise<void> {
    let entity = null;

    switch (relatedToType) {
      case 'contact':
        entity = await prisma.contact.findFirst({
          where: { id: relatedToId, tenantId }
        });
        break;
      case 'company':
        entity = await prisma.company.findFirst({
          where: { id: relatedToId, tenantId }
        });
        break;
      case 'lead':
        // Will be implemented when leads are added
        break;
      case 'opportunity':
        // Will be implemented when opportunities are added
        break;
      default:
        throw new Error(`Unsupported related entity type: ${relatedToType}`);
    }

    if (!entity && relatedToType !== 'lead' && relatedToType !== 'opportunity') {
      throw new Error(`${relatedToType} not found`);
    }
  }
}
