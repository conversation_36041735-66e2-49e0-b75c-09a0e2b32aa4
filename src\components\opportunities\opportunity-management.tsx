'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import {
  Plus,
  Search,
  Filter,
  MoreHorizontal,
  Edit,
  Trash2,
  Target,
  Users,
  TrendingUp,
  Download,
  Star,
  Clock,
  DollarSign,
  MessageSquare,
  ChartBar,
  Lightbulb,
  ArrowRight,
  FileText,
  CheckCircle
} from 'lucide-react';
import { OpportunityWithRelations } from '@/services/opportunity-management';
import { usePermissions } from '@/components/providers/permission-provider';
import { PermissionResource, PermissionAction } from '@/types';
import { PermissionGate } from '@/components/ui/permission-gate';
import { PermissionAwareTable } from '@/components/ui/permission-aware-table';
import { EmptyState } from '@/components/ui/empty-state';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger 
} from '@/components/ui/dropdown-menu';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';
import { useEnhancedDeleteDialog, deleteConfigurations } from '@/components/ui/enhanced-delete-dialog';
import { toastFunctions as toast } from '@/hooks/use-toast';

interface OpportunityManagementProps {
  className?: string;
}

export function OpportunityManagement({ className }: OpportunityManagementProps) {
  const router = useRouter();
  const { hasPermission } = usePermissions();
  const [opportunities, setOpportunities] = useState<OpportunityWithRelations[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [stageFilter, setStageFilter] = useState<string>('all');
  const [priorityFilter, setPriorityFilter] = useState<string>('all');
  const [ownerFilter, setOwnerFilter] = useState<string>('');
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [stats, setStats] = useState<any>(null);
  const [stages, setStages] = useState<any[]>([]);

  const { showDeleteDialog, DialogComponent } = useEnhancedDeleteDialog();

  // Load opportunities
  const loadOpportunities = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        page: page.toString(),
        limit: '10',
        ...(searchTerm && { search: searchTerm }),
        ...(stageFilter && stageFilter !== 'all' && { stage: stageFilter }),
        ...(priorityFilter && priorityFilter !== 'all' && { priority: priorityFilter }),
        ...(ownerFilter && { ownerId: ownerFilter })
      });

      const response = await fetch(`/api/opportunities?${params}`);
      const data = await response.json();

      if (response.ok) {
        setOpportunities(data.opportunities);
        setTotalPages(data.pagination.pages);
      } else {
        toast.error('Failed to load opportunities');
      }
    } catch (error) {
      console.error('Error loading opportunities:', error);
      toast.error('Failed to load opportunities');
    } finally {
      setLoading(false);
    }
  };

  // Load statistics
  const loadStats = async () => {
    try {
      const response = await fetch('/api/opportunities/analytics');
      const data = await response.json();
      if (response.ok) {
        setStats(data);
      }
    } catch (error) {
      console.error('Error loading opportunity stats:', error);
    }
  };

  // Load sales stages
  const loadStages = async () => {
    try {
      const response = await fetch('/api/opportunities/stages');
      const data = await response.json();
      if (response.ok) {
        setStages(data);
      }
    } catch (error) {
      console.error('Error loading sales stages:', error);
    }
  };

  useEffect(() => {
    loadOpportunities();
    loadStats();
    loadStages();
  }, [page, searchTerm, stageFilter, priorityFilter, ownerFilter]);

  // Handle delete opportunity
  const handleDeleteOpportunity = async (opportunityId: string, opportunityTitle: string) => {
    console.log('Attempting to delete opportunity:', { opportunityId, opportunityTitle });

    try {
      await showDeleteDialog({
        ...deleteConfigurations.opportunity,
        itemName: opportunityTitle,
        onConfirm: async () => {
          console.log('Delete confirmed, making API call...');
          const response = await fetch(`/api/opportunities/${opportunityId}`, {
            method: 'DELETE'
          });

          console.log('Delete API response:', response.status, response.statusText);

          if (response.ok) {
            const data = await response.json();
            console.log('Delete successful:', data);
            toast.success('Opportunity deleted successfully');
            loadOpportunities();
            loadStats();
          } else {
            const errorData = await response.json();
            console.error('Delete failed:', errorData);
            throw new Error(errorData.error || 'Failed to delete opportunity');
          }
        }
      });
    } catch (error) {
      console.error('Error deleting opportunity:', error);
      toast.error('Failed to delete opportunity');
    }
  };

  // Handle bulk delete
  const handleBulkDelete = async (selectedIds: string[]) => {
    console.log('Attempting to bulk delete opportunities:', selectedIds);

    try {
      await showDeleteDialog({
        ...deleteConfigurations.bulkOpportunities,
        itemName: `${selectedIds.length} opportunity(ies)`,
        onConfirm: async () => {
          console.log('Bulk delete confirmed, making API call...');
          const response = await fetch('/api/opportunities', {
            method: 'DELETE',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ opportunityIds: selectedIds })
          });

          console.log('Bulk delete API response:', response.status, response.statusText);

          if (response.ok) {
            const data = await response.json();
            console.log('Bulk delete successful:', data);
            toast.success(`${data.data.deletedCount} opportunity(ies) deleted successfully`);
            loadOpportunities();
            loadStats();
          } else {
            const errorData = await response.json();
            console.error('Bulk delete failed:', errorData);
            throw new Error(errorData.error || 'Failed to delete opportunities');
          }
        }
      });
    } catch (error) {
      console.error('Error deleting opportunities:', error);
      toast.error('Failed to delete opportunities');
    }
  };

  // Convert lead to opportunity
  const handleConvertLead = (leadId: string) => {
    router.push(`/opportunities/new?leadId=${leadId}`);
  };

  // View AI insights
  const handleViewInsights = (opportunityId: string) => {
    router.push(`/opportunities/${opportunityId}/insights`);
  };

  // Start handover process
  const handleStartHandover = (opportunityId: string) => {
    router.push(`/handovers/new?opportunityId=${opportunityId}`);
  };

  // View existing handover
  const handleViewHandover = (opportunityId: string) => {
    // First check if handover exists for this opportunity
    router.push(`/handovers?opportunityId=${opportunityId}`);
  };

  // Create project from opportunity
  const handleCreateProject = (opportunityId: string) => {
    router.push(`/opportunities/${opportunityId}/create-project`);
  };

  // Stage badge component
  const StageBadge = ({ stage }: { stage: string }) => {
    const stageConfig = stages.find(s => s.name === stage);
    const color = stageConfig?.color || '#6B7280';
    
    return (
      <Badge 
        style={{ 
          backgroundColor: `${color}20`, 
          color: color,
          borderColor: color 
        }}
        className="border"
      >
        {stage}
      </Badge>
    );
  };

  // Priority badge component
  const PriorityBadge = ({ priority }: { priority: string }) => {
    const priorityConfig = {
      low: { color: 'bg-gray-100 text-gray-800', label: 'Low' },
      medium: { color: 'bg-blue-100 text-blue-800', label: 'Medium' },
      high: { color: 'bg-orange-100 text-orange-800', label: 'High' },
      urgent: { color: 'bg-red-100 text-red-800', label: 'Urgent' }
    };

    const config = priorityConfig[priority as keyof typeof priorityConfig] || priorityConfig.medium;
    
    return (
      <Badge className={config.color}>
        {config.label}
      </Badge>
    );
  };

  // Format currency
  const formatCurrency = (amount: number | null) => {
    if (!amount) return '-';
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  // Table columns
  const columns = [
    {
      key: 'title',
      label: 'Opportunity',
      render: (opportunity: OpportunityWithRelations) => (
        <div className="flex flex-col">
          <button
            onClick={() => router.push(`/opportunities/${opportunity.id}`)}
            className="font-medium text-gray-900 hover:text-primary-600 dark:hover:text-primary-400 transition-colors hover:underline focus:outline-none focus:underline text-left"
          >
            {opportunity.title}
          </button>
          {opportunity.description && (
            <span className="text-sm text-gray-500 truncate max-w-xs">
              {opportunity.description}
            </span>
          )}
        </div>
      )
    },
    {
      key: 'contact',
      label: 'Contact',
      render: (opportunity: OpportunityWithRelations) => (
        <div className="flex flex-col">
          {opportunity.contact ? (
            <>
              <button
                onClick={() => router.push(`/contacts/${opportunity.contact!.id}`)}
                className="font-medium text-gray-900 hover:text-primary-600 dark:hover:text-primary-400 transition-colors hover:underline focus:outline-none focus:underline text-left"
              >
                {opportunity.contact.firstName} {opportunity.contact.lastName}
              </button>
              <span className="text-sm text-gray-500">{opportunity.contact.email}</span>
            </>
          ) : (
            <span className="text-gray-400">No contact</span>
          )}
        </div>
      )
    },
    {
      key: 'company',
      label: 'Company',
      render: (opportunity: OpportunityWithRelations) => (
        opportunity.company ? (
          <button
            onClick={() => router.push(`/companies/${opportunity.company!.id}`)}
            className="font-medium text-gray-900 hover:text-primary-600 dark:hover:text-primary-400 transition-colors hover:underline focus:outline-none focus:underline text-left"
          >
            {opportunity.company.name}
          </button>
        ) : (
          <span className="text-gray-400">No company</span>
        )
      )
    },
    {
      key: 'stage',
      label: 'Stage',
      render: (opportunity: OpportunityWithRelations) => <StageBadge stage={opportunity.stage} />
    },
    {
      key: 'priority',
      label: 'Priority',
      render: (opportunity: OpportunityWithRelations) => <PriorityBadge priority={opportunity.priority} />
    },
    {
      key: 'value',
      label: 'Value',
      render: (opportunity: OpportunityWithRelations) => (
        <div className="flex items-center space-x-1">
          <DollarSign className="w-4 h-4 text-green-500" />
          <span className="font-medium">{formatCurrency(opportunity.value ? Number(opportunity.value) : null)}</span>
        </div>
      )
    },
    {
      key: 'probability',
      label: 'Probability',
      render: (opportunity: OpportunityWithRelations) => (
        <div className="flex items-center space-x-2">
          <div className="w-16 bg-gray-200 rounded-full h-2">
            <div 
              className="bg-blue-600 h-2 rounded-full" 
              style={{ width: `${opportunity.probability}%` }}
            ></div>
          </div>
          <span className="text-sm font-medium">{opportunity.probability}%</span>
        </div>
      )
    },
    {
      key: 'owner',
      label: 'Owner',
      render: (opportunity: OpportunityWithRelations) => (
        opportunity.owner ? (
          <span className="text-gray-900">
            {opportunity.owner.firstName} {opportunity.owner.lastName}
          </span>
        ) : (
          <span className="text-gray-400">Unassigned</span>
        )
      )
    }
  ];

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Statistics Cards */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Opportunities</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.totalOpportunities}</p>
                </div>
                <Target className="w-8 h-8 text-blue-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Value</p>
                  <p className="text-2xl font-bold text-gray-900">{formatCurrency(stats.totalValue)}</p>
                </div>
                <DollarSign className="w-8 h-8 text-green-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Average Value</p>
                  <p className="text-2xl font-bold text-gray-900">{formatCurrency(stats.averageValue)}</p>
                </div>
                <TrendingUp className="w-8 h-8 text-purple-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Conversion Rate</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.conversionRate.toFixed(1)}%</p>
                </div>
                <ChartBar className="w-8 h-8 text-orange-500" />
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Filters */}
      <Card>
        <CardContent className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div>
              <Input
                placeholder="Search opportunities..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full"
              />
            </div>
            
            <Select value={stageFilter} onValueChange={setStageFilter}>
              <SelectTrigger>
                <SelectValue placeholder="Filter by stage" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Stages</SelectItem>
                {stages.map((stage) => (
                  <SelectItem key={stage.id} value={stage.name}>
                    {stage.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select value={priorityFilter} onValueChange={setPriorityFilter}>
              <SelectTrigger>
                <SelectValue placeholder="Filter by priority" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Priorities</SelectItem>
                <SelectItem value="low">Low</SelectItem>
                <SelectItem value="medium">Medium</SelectItem>
                <SelectItem value="high">High</SelectItem>
                <SelectItem value="urgent">Urgent</SelectItem>
              </SelectContent>
            </Select>

            <Input
              placeholder="Filter by owner..."
              value={ownerFilter}
              onChange={(e) => setOwnerFilter(e.target.value)}
            />
          </div>
        </CardContent>
      </Card>

      {/* Opportunities Table */}
      {opportunities.length === 0 && !loading ? (
        <Card>
          <CardContent>
            <EmptyState
              icon={<Target className="w-12 h-12" />}
              title="No opportunities found"
              description="Get started by creating your first opportunity or converting a lead."
              action={{
                label: 'Add Opportunity',
                onClick: () => router.push('/opportunities/new'),
                variant: 'primary'
              }}
              secondaryAction={{
                label: 'View Pipeline',
                onClick: () => router.push('/opportunities/pipeline')
              }}
            />
          </CardContent>
        </Card>
      ) : (
        <Card>
          <PermissionAwareTable
            data={opportunities}
            columns={columns}
            actions={[
              {
                label: 'AI Insights',
                icon: Lightbulb,
                onClick: (opportunity) => handleViewInsights(opportunity.id),
                permission: { resource: PermissionResource.OPPORTUNITIES, action: PermissionAction.READ },
                tooltip: 'View AI-generated insights and recommendations',
                color: 'blue'
              },
              {
                label: 'Start Handover',
                icon: ArrowRight,
                onClick: (opportunity) => handleStartHandover(opportunity.id),
                permission: { resource: PermissionResource.OPPORTUNITIES, action: PermissionAction.UPDATE },
                variant: 'default',
                condition: (opportunity) => opportunity.stage === 'Closed Won'
              },
              {
                label: 'Create Project',
                icon: FileText,
                onClick: (opportunity) => handleCreateProject(opportunity.id),
                permission: { resource: PermissionResource.OPPORTUNITIES, action: PermissionAction.UPDATE },
                variant: 'default',
                condition: (opportunity) => opportunity.stage === 'Closed Won'
              },
              {
                label: 'Edit',
                icon: Edit,
                onClick: (opportunity) => router.push(`/opportunities/${opportunity.id}/edit`),
                permission: { resource: PermissionResource.OPPORTUNITIES, action: PermissionAction.UPDATE },
                tooltip: 'Edit opportunity information and details',
                color: 'blue'
              },
              {
                label: 'Delete',
                icon: Trash2,
                onClick: (opportunity) => handleDeleteOpportunity(opportunity.id, opportunity.title),
                permission: { resource: PermissionResource.OPPORTUNITIES, action: PermissionAction.DELETE },
                variant: 'destructive',
                tooltip: 'Permanently delete this opportunity',
                color: 'red'
              }
            ]}
            bulkActions={[
              {
                label: 'Delete Selected',
                icon: Trash2,
                onClick: (opportunities) => handleBulkDelete(opportunities.map(o => o.id)),
                permission: { resource: PermissionResource.OPPORTUNITIES, action: PermissionAction.DELETE },
                variant: 'destructive',
                tooltip: 'Permanently delete selected opportunities',
                color: 'red',
                confirmMessage: 'Are you sure you want to delete {count} opportunity(ies)? This action cannot be undone.'
              }
            ]}
            resource={PermissionResource.OPPORTUNITIES}
            idField="id"
            ownershipField="ownerId"
            loading={loading}
            emptyMessage="No opportunities found"
          />
        </Card>
      )}

      {/* Enhanced Delete Dialog */}
      <DialogComponent />
    </div>
  );
}
