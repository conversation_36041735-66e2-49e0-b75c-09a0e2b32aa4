'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { SearchableSelect } from '@/components/ui/searchable-select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  FileText,
  Download,
  Eye,
  EyeOff,
  Check,
  X,
  Plus,
  Filter,
  AlertCircle,
  Upload,
  Trash2,
  ExternalLink,
  Tag
} from 'lucide-react';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';
import { downloadFile } from '@/lib/download-utils';
import { useToast } from '@/hooks/use-toast';
import { HandoverDocumentUploadDialog } from './handover-document-upload-dialog';
import { DocumentLinkDialog } from '@/components/documents/document-link-dialog';
import { DocumentDetailsDialog } from '@/components/documents/document-details-dialog';

// Types
interface HandoverDocument {
  id: string;
  tenantId: string;
  handoverId: string;
  documentId: string;
  category: string;
  isRequired: boolean;
  isApproved: boolean;
  approvedAt?: string;
  approvedById?: string;
  isClientVisible: boolean;
  accessLevel: string;
  orderIndex: number;
  notes?: string;
  createdAt: string;
  updatedAt: string;
  
  document: {
    id: string;
    name: string;
    filePath: string;
    mimeType: string;
    fileSize: number;
    description?: string;
    tags: string[];
    createdAt: string;
    uploadedBy?: {
      id: string;
      firstName?: string;
      lastName?: string;
      email: string;
    };
  };
  approvedBy?: {
    id: string;
    firstName?: string;
    lastName?: string;
    email: string;
  };
}

interface DocumentStats {
  total: number;
  byCategory: Record<string, number>;
  required: number;
  approved: number;
  clientVisible: number;
}

interface HandoverDocumentsResponse {
  documents: HandoverDocument[];
  stats: DocumentStats;
}

interface HandoverDocumentsProps {
  handoverId: string;
}

// Category configurations
const categoryConfig = {
  requirements: { label: 'Requirements', color: 'bg-blue-100 text-blue-800' },
  contracts: { label: 'Contracts', color: 'bg-green-100 text-green-800' },
  technical: { label: 'Technical', color: 'bg-purple-100 text-purple-800' },
  legal: { label: 'Legal', color: 'bg-red-100 text-red-800' },
  financial: { label: 'Financial', color: 'bg-yellow-100 text-yellow-800' },
};

const accessLevelConfig = {
  internal: { label: 'Internal Only', color: 'bg-gray-100 text-gray-800' },
  delivery_team: { label: 'Delivery Team', color: 'bg-blue-100 text-blue-800' },
  client: { label: 'Client Access', color: 'bg-green-100 text-green-800' },
};

export default function HandoverDocuments({ handoverId }: HandoverDocumentsProps) {
  const { toast } = useToast();
  const [documents, setDocuments] = useState<HandoverDocument[]>([]);
  const [stats, setStats] = useState<DocumentStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [updating, setUpdating] = useState<string | null>(null);
  const [uploadDialogOpen, setUploadDialogOpen] = useState(false);
  const [linkDialogOpen, setLinkDialogOpen] = useState(false);
  const [detailsDialogOpen, setDetailsDialogOpen] = useState(false);
  const [selectedDocument, setSelectedDocument] = useState<HandoverDocument | null>(null);
  const [aiAvailable, setAiAvailable] = useState(false);
  const [filters, setFilters] = useState({
    category: 'all',
    isRequired: 'all',
    isApproved: 'all',
    accessLevel: 'all',
    isClientVisible: 'all'
  });

  // Fetch documents
  const fetchDocuments = async () => {
    try {
      setLoading(true);
      setError(null);

      const params = new URLSearchParams();
      Object.entries(filters).forEach(([key, value]) => {
        if (value && value !== 'all') params.append(key, value);
      });

      const response = await fetch(`/api/handovers/${handoverId}/documents?${params}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch handover documents');
      }

      const data: HandoverDocumentsResponse = await response.json();
      setDocuments(data.documents || []);
      setStats(data.stats || null);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  // Check AI availability
  const checkAiAvailability = async () => {
    try {
      const response = await fetch('/api/documents/analyze');
      const data = await response.json();
      setAiAvailable(data.success && data.data.available);
    } catch (error) {
      console.error('Failed to check AI availability:', error);
      setAiAvailable(false);
    }
  };

  useEffect(() => {
    fetchDocuments();
    checkAiAvailability();
  }, [handoverId, filters]);

  // Update document
  const updateDocument = async (docId: string, updates: any) => {
    try {
      setUpdating(docId);
      
      const response = await fetch(`/api/handovers/documents/${docId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updates),
      });

      if (!response.ok) {
        throw new Error('Failed to update document');
      }

      const updatedDoc = await response.json();
      
      setDocuments(prev => prev.map(doc => 
        doc.id === docId ? updatedDoc : doc
      ));
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update document');
    } finally {
      setUpdating(null);
    }
  };

  // Remove document from handover
  const removeDocument = async (docId: string) => {
    try {
      setUpdating(docId);
      
      const response = await fetch(`/api/handovers/documents/${docId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Failed to remove document');
      }

      setDocuments(prev => prev.filter(doc => doc.id !== docId));
      await fetchDocuments(); // Refresh to update stats
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to remove document');
    } finally {
      setUpdating(null);
    }
  };

  // Toggle approval
  const toggleApproval = async (doc: HandoverDocument) => {
    await updateDocument(doc.id, { isApproved: !doc.isApproved });
  };

  // Toggle client visibility
  const toggleClientVisibility = async (doc: HandoverDocument) => {
    await updateDocument(doc.id, { isClientVisible: !doc.isClientVisible });
  };

  // View document details
  const viewDocument = (doc: HandoverDocument) => {
    setSelectedDocument(doc);
    setDetailsDialogOpen(true);
  };

  // Open document file
  const openDocumentFile = (doc: HandoverDocument) => {
    window.open(`/api/documents/${doc.document.id}/download`, '_blank');
  };

  // Download document
  const downloadDocument = async (doc: HandoverDocument) => {
    try {
      await downloadFile(`/api/documents/${doc.document.id}/download`, doc.document.name);
      toast({
        description: 'Document downloaded successfully',
        variant: 'default'
      });
    } catch (error) {
      console.error('Error downloading document:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to download document';
      toast({
        description: errorMessage,
        variant: 'destructive'
      });
    }
  };

  // Format file size
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getCategoryBadge = (category: string) => {
    const config = categoryConfig[category as keyof typeof categoryConfig] || categoryConfig.requirements;
    return (
      <Badge variant="outline" className={config.color}>
        {config.label}
      </Badge>
    );
  };

  const getAccessLevelBadge = (accessLevel: string) => {
    const config = accessLevelConfig[accessLevel as keyof typeof accessLevelConfig] || accessLevelConfig.internal;
    return (
      <Badge variant="outline" className={config.color}>
        {config.label}
      </Badge>
    );
  };

  const getUserDisplayName = (user?: { firstName?: string; lastName?: string; email?: string }) => {
    if (!user) return 'Unknown';
    return user.firstName && user.lastName
      ? `${user.firstName} ${user.lastName}`
      : user.email || 'Unknown';
  };

  // Filter options for SearchableSelect
  const getCategoryOptions = () => [
    { value: 'all', label: 'All categories' },
    { value: 'requirements', label: 'Requirements' },
    { value: 'contracts', label: 'Contracts' },
    { value: 'technical', label: 'Technical' },
    { value: 'legal', label: 'Legal' },
    { value: 'financial', label: 'Financial' },
  ];

  const getRequiredOptions = () => [
    { value: 'all', label: 'All' },
    { value: 'true', label: 'Required only' },
    { value: 'false', label: 'Optional only' },
  ];

  const getApprovalOptions = () => [
    { value: 'all', label: 'All' },
    { value: 'true', label: 'Approved' },
    { value: 'false', label: 'Pending approval' },
  ];

  const getAccessLevelOptions = () => [
    { value: 'all', label: 'All levels' },
    { value: 'internal', label: 'Internal only' },
    { value: 'delivery_team', label: 'Delivery team' },
    { value: 'client', label: 'Client access' },
  ];

  const getClientVisibilityOptions = () => [
    { value: 'all', label: 'All' },
    { value: 'true', label: 'Visible to client' },
    { value: 'false', label: 'Hidden from client' },
  ];

  // Safe date formatting helper
  const formatSafeDate = (dateValue: string | Date | null | undefined): string => {
    if (!dateValue) return 'Unknown date';
    try {
      const date = new Date(dateValue);
      if (isNaN(date.getTime())) return 'Invalid date';
      return format(date, 'MMM d, yyyy');
    } catch (error) {
      console.warn('Date formatting error:', error);
      return 'Invalid date';
    }
  };

  // Handle upload completion
  const handleUploadComplete = (uploadedDocuments: any[]) => {
    toast({
      description: `Successfully uploaded ${uploadedDocuments.length} document(s)`,
      variant: 'default',
    });
    fetchDocuments(); // Refresh the document list
  };

  // Handle upload error
  const handleUploadError = (error: string) => {
    toast({
      description: error,
      variant: 'destructive',
    });
  };

  // Handle link completion
  const handleLinkComplete = (linkedDocuments: any[]) => {
    toast({
      description: `Successfully linked ${linkedDocuments.length} document(s)`,
      variant: 'default',
    });
    fetchDocuments(); // Refresh the document list
  };

  // Handle link error
  const handleLinkError = (error: string) => {
    toast({
      description: error,
      variant: 'destructive',
    });
  };

  if (loading && documents.length === 0) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-sm text-gray-600">Loading documents...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div className="space-y-1">
          <h2 className="text-2xl font-bold tracking-tight">Handover Documents</h2>
          <p className="text-muted-foreground">
            Manage and organize documents for this handover project
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setUploadDialogOpen(true)}
          >
            <Upload className="w-4 h-4 mr-2" />
            Upload New
          </Button>
          <Button onClick={() => setLinkDialogOpen(true)}>
            <Plus className="w-4 h-4 mr-2" />
            Link Existing
          </Button>
        </div>
      </div>

      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Statistics */}
      {stats && (
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center space-x-2">
                <FileText className="w-5 h-5 text-blue-600" />
                <div>
                  <div className="text-2xl font-bold text-blue-600">{stats.total}</div>
                  <div className="text-sm text-muted-foreground">Total Documents</div>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center space-x-2">
                <AlertCircle className="w-5 h-5 text-red-600" />
                <div>
                  <div className="text-2xl font-bold text-red-600">{stats.required}</div>
                  <div className="text-sm text-muted-foreground">Required</div>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center space-x-2">
                <Check className="w-5 h-5 text-green-600" />
                <div>
                  <div className="text-2xl font-bold text-green-600">{stats.approved}</div>
                  <div className="text-sm text-muted-foreground">Approved</div>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center space-x-2">
                <Eye className="w-5 h-5 text-purple-600" />
                <div>
                  <div className="text-2xl font-bold text-purple-600">{stats.clientVisible}</div>
                  <div className="text-sm text-muted-foreground">Client Visible</div>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center space-x-2">
                <Filter className="w-5 h-5 text-orange-600" />
                <div>
                  <div className="text-2xl font-bold text-orange-600">
                    {Object.keys(stats.byCategory).length}
                  </div>
                  <div className="text-sm text-muted-foreground">Categories</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="w-5 h-5" />
            Filters
          </CardTitle>
          <CardDescription>
            Filter documents by category, status, and access level
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-6">
            <div className="space-y-2">
              <Label htmlFor="category-filter">Category</Label>
              <SearchableSelect
                options={getCategoryOptions()}
                value={filters.category}
                onValueChange={(value) => setFilters(prev => ({ ...prev, category: value }))}
                placeholder="All categories"
                searchPlaceholder="Search categories..."
                emptyMessage="No categories found"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="required-filter">Required</Label>
              <SearchableSelect
                options={getRequiredOptions()}
                value={filters.isRequired}
                onValueChange={(value) => setFilters(prev => ({ ...prev, isRequired: value }))}
                placeholder="All"
                searchPlaceholder="Search options..."
                emptyMessage="No options found"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="approval-filter">Approval Status</Label>
              <SearchableSelect
                options={getApprovalOptions()}
                value={filters.isApproved}
                onValueChange={(value) => setFilters(prev => ({ ...prev, isApproved: value }))}
                placeholder="All"
                searchPlaceholder="Search status..."
                emptyMessage="No status found"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="access-filter">Access Level</Label>
              <SearchableSelect
                options={getAccessLevelOptions()}
                value={filters.accessLevel}
                onValueChange={(value) => setFilters(prev => ({ ...prev, accessLevel: value }))}
                placeholder="All levels"
                searchPlaceholder="Search access levels..."
                emptyMessage="No access levels found"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="visibility-filter">Client Visibility</Label>
              <SearchableSelect
                options={getClientVisibilityOptions()}
                value={filters.isClientVisible}
                onValueChange={(value) => setFilters(prev => ({ ...prev, isClientVisible: value }))}
                placeholder="All"
                searchPlaceholder="Search visibility..."
                emptyMessage="No options found"
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Documents List */}
      <div className="space-y-6">
        {documents.map((doc) => {
          const isUpdating = updating === doc.id;

          return (
            <Card key={doc.id}>
              <CardContent className="p-6">
                <div className="flex items-start justify-between gap-6">
                  <div className="flex items-start gap-4 flex-1">
                    <div className="p-2 bg-blue-50 rounded-lg">
                      <FileText className="w-6 h-6 text-blue-600" />
                    </div>

                    <div className="flex-1 space-y-4">
                      <div className="space-y-3">
                        <div className="flex items-center gap-2 flex-wrap">
                          <h3 className="font-semibold text-lg text-gray-900 dark:text-white">{doc.document.name}</h3>
                          {getCategoryBadge(doc.category)}
                          {getAccessLevelBadge(doc.accessLevel)}
                        </div>

                        {/* Document Description */}
                        {doc.document.description && (
                          <div className="bg-gray-50 dark:bg-gray-800 p-3 rounded-lg border border-gray-200 dark:border-gray-700">
                            <p className="text-sm text-gray-700 dark:text-gray-300">{doc.document.description}</p>
                          </div>
                        )}

                        {/* Document Tags */}
                        {doc.document.tags && doc.document.tags.length > 0 && (
                          <div className="space-y-2">
                            <div className="flex items-center gap-2">
                              <span className="text-xs font-medium text-gray-500 dark:text-gray-400">Tags:</span>
                            </div>
                            <div className="flex flex-wrap gap-1">
                              {doc.document.tags.map((tag, index) => (
                                <Badge key={index} variant="secondary" className="text-xs">
                                  {tag}
                                </Badge>
                              ))}
                            </div>
                          </div>
                        )}

                        <div className="flex items-center gap-2 flex-wrap">
                          {doc.isRequired && (
                            <Badge variant="outline" className="text-xs border-red-200 text-red-700">
                              <AlertCircle className="w-3 h-3 mr-1" />
                              Required
                            </Badge>
                          )}
                          {doc.isApproved && (
                            <Badge className="bg-green-100 text-green-800 border-green-200">
                              <Check className="w-3 h-3 mr-1" />
                              Approved
                            </Badge>
                          )}
                          {doc.isClientVisible && (
                            <Badge variant="outline" className="text-xs border-blue-200 text-blue-700">
                              <Eye className="w-3 h-3 mr-1" />
                              Client Visible
                            </Badge>
                          )}
                        </div>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-muted-foreground">
                        <div className="space-y-1">
                          <div className="flex items-center gap-2">
                            <span className="font-medium">Size:</span>
                            <span>{formatFileSize(doc.document.fileSize)}</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <span className="font-medium">Uploaded:</span>
                            <span>
                              {formatSafeDate(doc.document.createdAt)}
                              {doc.document.uploadedBy && (
                                <span className="text-blue-600"> by {getUserDisplayName(doc.document.uploadedBy)}</span>
                              )}
                            </span>
                          </div>
                        </div>
                        {doc.isApproved && doc.approvedAt && doc.approvedBy && (
                          <div className="space-y-1">
                            <div className="flex items-center gap-2">
                              <span className="font-medium">Approved:</span>
                              <span>
                                {formatSafeDate(doc.approvedAt)}
                                <span className="text-green-600"> by {getUserDisplayName(doc.approvedBy)}</span>
                              </span>
                            </div>
                          </div>
                        )}
                      </div>

                      {doc.notes && (
                        <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg text-sm">
                          <div className="font-medium text-blue-900 mb-1">Notes:</div>
                          <div className="text-blue-800">{doc.notes}</div>
                        </div>
                      )}
                    </div>
                  </div>

                  <div className="flex flex-col gap-2">
                    <div className="flex items-center gap-2">
                      <Button
                        size="sm"
                        variant={doc.isApproved ? "default" : "outline"}
                        onClick={() => toggleApproval(doc)}
                        disabled={isUpdating}
                        className={cn(
                          doc.isApproved
                            ? "bg-green-600 text-white"
                            : "border-green-200 text-green-700"
                        )}
                      >
                        {doc.isApproved ? (
                          <>
                            <Check className="w-4 h-4 mr-1" />
                            Approved
                          </>
                        ) : (
                          <>
                            <Check className="w-4 h-4 mr-1" />
                            Approve
                          </>
                        )}
                      </Button>

                      <Button
                        size="sm"
                        variant={doc.isClientVisible ? "default" : "outline"}
                        onClick={() => toggleClientVisibility(doc)}
                        disabled={isUpdating}
                        className={cn(
                          doc.isClientVisible
                            ? "bg-blue-600 text-white"
                            : "border-blue-200 text-blue-700"
                        )}
                      >
                        {doc.isClientVisible ? (
                          <>
                            <Eye className="w-4 h-4 mr-1" />
                            Visible
                          </>
                        ) : (
                          <>
                            <EyeOff className="w-4 h-4 mr-1" />
                            Show to Client
                          </>
                        )}
                      </Button>
                    </div>

                    <div className="flex items-center gap-2">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => viewDocument(doc)}
                        disabled={isUpdating}
                        title="View document details"
                        className="border-blue-200 text-blue-700"
                      >
                        <Eye className="w-4 h-4 mr-1" />
                        View Details
                      </Button>

                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => downloadDocument(doc)}
                        disabled={isUpdating}
                        title="Download document"
                        className="border-gray-200 text-gray-700"
                      >
                        <Download className="w-4 h-4 mr-1" />
                        Download
                      </Button>

                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => removeDocument(doc.id)}
                        disabled={isUpdating}
                        className="border-red-200 text-red-700"
                        title="Remove from handover"
                      >
                        <Trash2 className="w-4 h-4 mr-1" />
                        Remove
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Empty State */}
      {documents.length === 0 && !loading && (
        <Card className="border-dashed border-2">
          <CardContent className="p-12">
            <div className="text-center space-y-4">
              <div className="p-4 bg-blue-50 rounded-full w-fit mx-auto">
                <FileText className="w-8 h-8 text-blue-600" />
              </div>
              <div className="space-y-2">
                <h3 className="text-xl font-semibold">No documents found</h3>
                <p className="text-muted-foreground max-w-md mx-auto">
                  {Object.values(filters).some(f => f && f !== 'all')
                    ? 'No documents match your current filters. Try adjusting your search criteria or add new documents to this handover.'
                    : 'Get started by linking existing documents or uploading new ones to this handover project.'
                  }
                </p>
              </div>
              <div className="flex items-center justify-center gap-3 pt-4">
                <Button
                  variant="outline"
                  onClick={() => setUploadDialogOpen(true)}
                >
                  <Upload className="w-4 h-4 mr-2" />
                  Upload New
                </Button>
                <Button onClick={() => setLinkDialogOpen(true)}>
                  <Plus className="w-4 h-4 mr-2" />
                  Link Existing
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Upload Dialog */}
      <HandoverDocumentUploadDialog
        open={uploadDialogOpen}
        onOpenChange={setUploadDialogOpen}
        handoverId={handoverId}
        onUploadComplete={handleUploadComplete}
        onUploadError={handleUploadError}
      />

      {/* Link Dialog */}
      <DocumentLinkDialog
        open={linkDialogOpen}
        onOpenChange={setLinkDialogOpen}
        relatedToType="handover"
        relatedToId={handoverId}
        onLinkComplete={handleLinkComplete}
        onLinkError={handleLinkError}
      />

      {/* Document Details Dialog */}
      <DocumentDetailsDialog
        open={detailsDialogOpen}
        onOpenChange={setDetailsDialogOpen}
        document={selectedDocument?.document || null}
        onDownload={(doc) => downloadDocument(selectedDocument!)}
        onView={(doc) => openDocumentFile(selectedDocument!)}
        aiAvailable={aiAvailable}
      />
    </div>
  );
}
