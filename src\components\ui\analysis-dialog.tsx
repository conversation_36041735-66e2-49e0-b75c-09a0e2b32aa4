'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { 
  <PERSON><PERSON>, 
  <PERSON>alog<PERSON>ontent, 
  DialogHeader, 
  DialogTitle,
  DialogDescription 
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import {
  Clock,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Sparkles,
  User,
  ChartBar,
  Heart,
  Bolt,
  Target
} from 'lucide-react';
import { cn } from '@/lib/utils';

// Lead scoring analysis result type
export interface LeadAnalysisResult {
  overall: number;
  demographic: number;
  behavioral: number;
  engagement: number;
  intent: number;
  fit: number;
  reasoning: string;
  recommendations: string[];
  nextActions: string[];
  confidence: number;
  qualification?: {
    budget: 'qualified' | 'unqualified' | 'unknown';
    authority: 'qualified' | 'unqualified' | 'unknown';
    need: 'qualified' | 'unqualified' | 'unknown';
    timeline: 'qualified' | 'unqualified' | 'unknown';
    overall: 'qualified' | 'unqualified' | 'unknown';
    reasoning: string;
    questions: string[];
  };
  processingTime?: number;
}

interface AnalysisDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  result: LeadAnalysisResult | null;
  title?: string;
  description?: string;
  loading?: boolean;
}

export function AnalysisDialog({
  open,
  onOpenChange,
  result,
  title = "Lead Analysis Results",
  description = "AI-powered lead scoring and qualification analysis",
  loading = false
}: AnalysisDialogProps) {
  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600 bg-green-50 border-green-200';
    if (score >= 60) return 'text-yellow-600 bg-yellow-50 border-yellow-200';
    if (score >= 40) return 'text-orange-600 bg-orange-50 border-orange-200';
    return 'text-red-600 bg-red-50 border-red-200';
  };

  const getScoreIcon = (score: number) => {
    if (score >= 80) return <CheckCircle className="w-5 h-5 text-green-600" />;
    if (score >= 60) return <Clock className="w-5 h-5 text-yellow-600" />;
    if (score >= 40) return <AlertTriangle className="w-5 h-5 text-orange-600" />;
    return <XCircle className="w-5 h-5 text-red-600" />;
  };

  const getQualificationIcon = (status: string) => {
    switch (status) {
      case 'qualified':
        return <CheckCircle className="w-4 h-4 text-green-600" />;
      case 'unqualified':
        return <XCircle className="w-4 h-4 text-red-600" />;
      default:
        return <Clock className="w-4 h-4 text-gray-400" />;
    }
  };

  const getQualificationColor = (status: string) => {
    switch (status) {
      case 'qualified':
        return 'text-green-700 bg-green-50 border-green-200';
      case 'unqualified':
        return 'text-red-700 bg-red-50 border-red-200';
      default:
        return 'text-gray-700 bg-gray-50 border-gray-200';
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Sparkles className="w-5 h-5 text-blue-600" />
            {title}
          </DialogTitle>
          <DialogDescription>
            {description}
          </DialogDescription>
        </DialogHeader>

        <ScrollArea className="flex-1 max-h-[calc(90vh-200px)]">
          <div className="space-y-6 p-1">
            {loading && (
              <div className="flex items-center justify-center py-12">
                <div className="text-center space-y-4">
                  <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
                  <p className="text-gray-600">Analyzing lead data...</p>
                </div>
              </div>
            )}

            {result && !loading && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3 }}
                className="space-y-6"
              >
                {/* Overall Score */}
                <div className="text-center">
                  <div className={cn(
                    "inline-flex items-center px-6 py-3 rounded-full border",
                    getScoreColor(result.overall)
                  )}>
                    {getScoreIcon(result.overall)}
                    <span className="ml-2 text-2xl font-bold">{result.overall}</span>
                    <span className="ml-1 text-sm">/100</span>
                  </div>
                  <p className="text-sm text-gray-500 mt-2">
                    Overall Lead Score • Confidence: {Math.round(result.confidence * 100)}%
                  </p>
                </div>

                {/* Score Breakdown */}
                <div className="space-y-4">
                  <h4 className="font-medium text-gray-900 mb-4">Score Breakdown</h4>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="bg-gray-50 rounded-lg p-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <User className="w-5 h-5 text-blue-500" />
                          <span className="text-sm font-medium text-gray-700">Demographic</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <div className="text-lg font-bold text-gray-900">{result.demographic}</div>
                          <div className="text-sm text-gray-500">/100</div>
                        </div>
                      </div>
                    </div>

                    <div className="bg-gray-50 rounded-lg p-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <ChartBar className="w-5 h-5 text-green-500" />
                          <span className="text-sm font-medium text-gray-700">Behavioral</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <div className="text-lg font-bold text-gray-900">{result.behavioral}</div>
                          <div className="text-sm text-gray-500">/100</div>
                        </div>
                      </div>
                    </div>

                    <div className="bg-gray-50 rounded-lg p-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <Heart className="w-5 h-5 text-red-500" />
                          <span className="text-sm font-medium text-gray-700">Engagement</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <div className="text-lg font-bold text-gray-900">{result.engagement}</div>
                          <div className="text-sm text-gray-500">/100</div>
                        </div>
                      </div>
                    </div>

                    <div className="bg-gray-50 rounded-lg p-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <Bolt className="w-5 h-5 text-yellow-500" />
                          <span className="text-sm font-medium text-gray-700">Intent</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <div className="text-lg font-bold text-gray-900">{result.intent}</div>
                          <div className="text-sm text-gray-500">/100</div>
                        </div>
                      </div>
                    </div>

                    <div className="bg-gray-50 rounded-lg p-4 md:col-span-2">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <Target className="w-5 h-5 text-purple-500" />
                          <span className="text-sm font-medium text-gray-700">Fit</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <div className="text-lg font-bold text-gray-900">{result.fit}</div>
                          <div className="text-sm text-gray-500">/100</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Reasoning */}
                <div className="bg-gray-50 rounded-lg p-4">
                  <h4 className="font-medium text-gray-900 mb-2">AI Reasoning</h4>
                  <p className="text-sm text-gray-700">{result.reasoning}</p>
                </div>

                {/* Recommendations */}
                {result.recommendations.length > 0 && (
                  <div>
                    <h4 className="font-medium text-gray-900 mb-3">Recommendations</h4>
                    <ul className="space-y-2">
                      {result.recommendations.map((recommendation, index) => (
                        <li key={index} className="flex items-start space-x-2">
                          <CheckCircle className="w-4 h-4 text-green-600 mt-0.5 flex-shrink-0" />
                          <span className="text-sm text-gray-700">{recommendation}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                )}

                {/* Next Actions */}
                {result.nextActions.length > 0 && (
                  <div>
                    <h4 className="font-medium text-gray-900 mb-3">Next Actions</h4>
                    <ul className="space-y-2">
                      {result.nextActions.map((action, index) => (
                        <li key={index} className="flex items-start space-x-2">
                          <Bolt className="w-4 h-4 text-blue-600 mt-0.5 flex-shrink-0" />
                          <span className="text-sm text-gray-700">{action}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                )}

                {/* BANT Qualification */}
                {result.qualification && (
                  <div className="border-t border-gray-200 pt-6">
                    <h4 className="font-medium text-gray-900 mb-4">BANT Qualification</h4>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3 mb-4">
                      <div className={cn("p-4 rounded-lg border", getQualificationColor(result.qualification.budget))}>
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            {getQualificationIcon(result.qualification.budget)}
                            <span className="text-sm font-medium">Budget</span>
                          </div>
                          <div className="text-sm font-semibold capitalize">{result.qualification.budget}</div>
                        </div>
                      </div>

                      <div className={cn("p-4 rounded-lg border", getQualificationColor(result.qualification.authority))}>
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            {getQualificationIcon(result.qualification.authority)}
                            <span className="text-sm font-medium">Authority</span>
                          </div>
                          <div className="text-sm font-semibold capitalize">{result.qualification.authority}</div>
                        </div>
                      </div>

                      <div className={cn("p-4 rounded-lg border", getQualificationColor(result.qualification.need))}>
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            {getQualificationIcon(result.qualification.need)}
                            <span className="text-sm font-medium">Need</span>
                          </div>
                          <div className="text-sm font-semibold capitalize">{result.qualification.need}</div>
                        </div>
                      </div>

                      <div className={cn("p-4 rounded-lg border", getQualificationColor(result.qualification.timeline))}>
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            {getQualificationIcon(result.qualification.timeline)}
                            <span className="text-sm font-medium">Timeline</span>
                          </div>
                          <div className="text-sm font-semibold capitalize">{result.qualification.timeline}</div>
                        </div>
                      </div>
                    </div>

                    <div className="bg-blue-50 rounded-lg p-4">
                      <h5 className="font-medium text-blue-900 mb-2">Qualification Reasoning</h5>
                      <p className="text-sm text-blue-800">{result.qualification.reasoning}</p>
                    </div>

                    {result.qualification.questions.length > 0 && (
                      <div className="mt-4">
                        <h5 className="font-medium text-gray-900 mb-2">Suggested Questions</h5>
                        <ul className="space-y-1">
                          {result.qualification.questions.map((question, index) => (
                            <li key={index} className="text-sm text-gray-700">
                              • {question}
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>
                )}
              </motion.div>
            )}
          </div>
        </ScrollArea>

        <div className="flex items-center justify-between pt-4 border-t">
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            {result?.processingTime && (
              <>
                <Clock className="w-4 h-4" />
                Processed in {Math.round(result.processingTime / 1000)}s
              </>
            )}
          </div>
          
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Close
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
