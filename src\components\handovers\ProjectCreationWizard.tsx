'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  ArrowLeft,
  ArrowRight,
  CheckCircle,
  AlertCircle,
  Calendar,
  Users,
  Settings,
  Building2,
  FileText,
  Globe,
  Loader2,
  Star
} from 'lucide-react';
import { format } from 'date-fns';
import { convertDateStringToISO } from '@/lib/date-conversion';
import { cn } from '@/lib/utils';

// Types
interface HandoverData {
  id: string;
  title: string;
  description?: string;
  checklistProgress: number;
  opportunity: {
    id: string;
    title: string;
    value?: number;
    contact?: {
      firstName: string;
      lastName: string;
      email?: string;
    };
    company?: {
      name: string;
    };
  };
  salesRep?: {
    id: string;
    firstName?: string;
    lastName?: string;
    email: string;
  };
  deliveryManager?: {
    id: string;
    firstName?: string;
    lastName?: string;
    email: string;
  };
}

interface User {
  id: string;
  firstName?: string;
  lastName?: string;
  email: string;
}

interface ProjectFormData {
  name: string;
  description: string;
  startDate: string;
  endDate: string;
  budget?: number;
  managerId: string;
  clientPortalEnabled: boolean;
  customFields: Record<string, any>;
}

interface ProjectCreationWizardProps {
  handoverId: string;
  onSuccess?: (project: any) => void;
  onCancel?: () => void;
}

const STEPS = [
  { id: 'review', title: 'Review Handover', icon: FileText },
  { id: 'project', title: 'Project Details', icon: Settings },
  { id: 'team', title: 'Team Assignment', icon: Users },
  { id: 'timeline', title: 'Timeline & Budget', icon: Calendar },
  { id: 'portal', title: 'Client Portal', icon: Globe },
  { id: 'confirm', title: 'Confirm & Create', icon: CheckCircle },
];

export default function ProjectCreationWizard({
  handoverId,
  onSuccess,
  onCancel
}: ProjectCreationWizardProps) {
  const router = useRouter();
  const [currentStep, setCurrentStep] = useState(0);
  const [loading, setLoading] = useState(true);
  const [creating, setCreating] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [handover, setHandover] = useState<HandoverData | null>(null);
  const [users, setUsers] = useState<User[]>([]);

  const [formData, setFormData] = useState<ProjectFormData>({
    name: '',
    description: '',
    startDate: '',
    endDate: '',
    budget: undefined,
    managerId: '',
    clientPortalEnabled: true,
    customFields: {},
  });

  // Fetch handover and users data
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);

        const [handoverRes, usersRes] = await Promise.all([
          fetch(`/api/handovers/${handoverId}`),
          fetch('/api/users')
        ]);

        if (!handoverRes.ok) {
          throw new Error('Failed to fetch handover details');
        }

        if (!usersRes.ok) {
          throw new Error('Failed to fetch users');
        }

        const handoverData = await handoverRes.json();
        const usersData = await usersRes.json();

        setHandover(handoverData);
        setUsers(usersData.users || []);

        // Pre-fill form data
        setFormData(prev => ({
          ...prev,
          name: `${handoverData.opportunity.title} - Project`,
          description: `Project created from handover: ${handoverData.title}`,
          managerId: handoverData.deliveryManager?.id || '',
          budget: handoverData.opportunity.value || undefined,
          startDate: format(new Date(), 'yyyy-MM-dd'),
          endDate: format(new Date(Date.now() + 90 * 24 * 60 * 60 * 1000), 'yyyy-MM-dd'), // 90 days from now
        }));
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [handoverId]);

  // Navigation functions
  const nextStep = () => {
    if (currentStep < STEPS.length - 1) {
      setCurrentStep(prev => prev + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(prev => prev - 1);
    }
  };

  const goToStep = (stepIndex: number) => {
    setCurrentStep(stepIndex);
  };

  // Create project
  const createProject = async () => {
    try {
      setCreating(true);
      setError(null);

      const response = await fetch(`/api/handovers/${handoverId}/finalize`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          projectName: formData.name,
          projectDescription: formData.description,
          startDate: convertDateStringToISO(formData.startDate),
          endDate: convertDateStringToISO(formData.endDate),
          budget: formData.budget,
          managerId: formData.managerId,
          clientPortalEnabled: formData.clientPortalEnabled,
          customFields: formData.customFields,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create project');
      }

      const result = await response.json();

      // Show appropriate success message
      const message = result.isNewProject
        ? 'Project created successfully!'
        : 'Handover completed and linked to existing project!';

      console.log(message);

      if (onSuccess) {
        onSuccess(result.project);
      } else {
        router.push(`/projects/${result.project.id}`);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create project');
    } finally {
      setCreating(false);
    }
  };

  // Validation functions
  const isStepValid = (stepIndex: number) => {
    switch (stepIndex) {
      case 0: // Review
        return handover && handover.checklistProgress >= 80;
      case 1: // Project Details
        return formData.name.trim() && formData.description.trim();
      case 2: // Team Assignment
        return formData.managerId;
      case 3: // Timeline & Budget
        return formData.startDate && formData.endDate && new Date(formData.startDate) < new Date(formData.endDate);
      case 4: // Client Portal
        return true; // Always valid
      case 5: // Confirm
        return isStepValid(1) && isStepValid(2) && isStepValid(3);
      default:
        return true;
    }
  };

  const canProceed = () => {
    return isStepValid(currentStep);
  };

  const getUserDisplayName = (user: User) => {
    return user.firstName && user.lastName
      ? `${user.firstName} ${user.lastName}`
      : user.email;
  };

  const formatCurrency = (value?: number) => {
    if (!value) return 'N/A';
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
    }).format(value);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-sm text-gray-600">Loading handover details...</p>
        </div>
      </div>
    );
  }

  if (error || !handover) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          {error || 'Handover not found'}
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Create Project from Handover</h1>
          <p className="text-gray-600">
            Set up a new project based on the completed handover
          </p>
        </div>
        {onCancel && (
          <Button variant="outline" onClick={onCancel}>
            Cancel
          </Button>
        )}
      </div>

      {/* Progress Steps */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex items-center justify-between mb-4">
            <div className="text-sm text-gray-600">
              Step {currentStep + 1} of {STEPS.length}
            </div>
            <div className="text-sm text-gray-600">
              {Math.round(((currentStep + 1) / STEPS.length) * 100)}% Complete
            </div>
          </div>
          <Progress value={((currentStep + 1) / STEPS.length) * 100} className="mb-4" />

          <div className="flex items-center justify-between">
            {STEPS.map((step, index) => {
              const Icon = step.icon;
              const isCompleted = index < currentStep;
              const isCurrent = index === currentStep;
              const isValid = isStepValid(index);

              return (
                <div
                  key={step.id}
                  className={cn(
                    "flex flex-col items-center cursor-pointer transition-colors",
                    isCurrent && "text-blue-600",
                    isCompleted && "text-green-600",
                    !isValid && index <= currentStep && "text-red-600"
                  )}
                  onClick={() => goToStep(index)}
                >
                  <div className={cn(
                    "w-8 h-8 rounded-full flex items-center justify-center border-2 mb-2",
                    isCurrent && "border-blue-600 bg-blue-50",
                    isCompleted && "border-green-600 bg-green-50",
                    !isValid && index <= currentStep && "border-red-600 bg-red-50",
                    !isCurrent && !isCompleted && isValid && "border-gray-300"
                  )}>
                    {isCompleted ? (
                      <CheckCircle className="w-4 h-4" />
                    ) : !isValid && index <= currentStep ? (
                      <AlertCircle className="w-4 h-4" />
                    ) : (
                      <Icon className="w-4 h-4" />
                    )}
                  </div>
                  <span className="text-xs text-center">{step.title}</span>
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Step Content */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            {React.createElement(STEPS[currentStep].icon, { className: "w-5 h-5" })}
            {STEPS[currentStep].title}
          </CardTitle>
          <CardDescription>
            {currentStep === 0 && "Review the handover details before creating the project"}
            {currentStep === 1 && "Configure the basic project information"}
            {currentStep === 2 && "Assign team members and project manager"}
            {currentStep === 3 && "Set project timeline and budget"}
            {currentStep === 4 && "Configure client portal access"}
            {currentStep === 5 && "Review and confirm project creation"}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Step 0: Review Handover */}
          {currentStep === 0 && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-6">
                <div>
                  <h3 className="font-medium mb-3">Handover Information</h3>
                  <div className="space-y-2 text-sm">
                    <div><strong>Title:</strong> {handover.title}</div>
                    <div><strong>Description:</strong> {handover.description || 'No description'}</div>
                    <div className="flex items-center gap-2">
                      <strong>Progress:</strong>
                      <Progress value={handover.checklistProgress} className="w-20 h-2" />
                      <span>{handover.checklistProgress}%</span>
                    </div>
                  </div>
                </div>

                <div>
                  <h3 className="font-medium mb-3">Opportunity Details</h3>
                  <div className="space-y-2 text-sm">
                    <div><strong>Title:</strong> {handover.opportunity.title}</div>
                    <div><strong>Value:</strong> {formatCurrency(handover.opportunity.value)}</div>
                    <div>
                      <strong>Client:</strong>{' '}
                      {handover.opportunity.company?.name ||
                       `${handover.opportunity.contact?.firstName} ${handover.opportunity.contact?.lastName}`}
                    </div>
                  </div>
                </div>
              </div>

              <div>
                <h3 className="font-medium mb-3">Team Assignment</h3>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <strong>Sales Rep:</strong>{' '}
                    {handover.salesRep ? getUserDisplayName(handover.salesRep) : 'Not assigned'}
                  </div>
                  <div>
                    <strong>Delivery Manager:</strong>{' '}
                    {handover.deliveryManager ? getUserDisplayName(handover.deliveryManager) : 'Not assigned'}
                  </div>
                </div>
              </div>

              {handover.checklistProgress < 80 && (
                <Alert variant="destructive">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>
                    Handover checklist is only {handover.checklistProgress}% complete.
                    It's recommended to complete at least 80% before creating a project.
                  </AlertDescription>
                </Alert>
              )}
            </div>
          )}

          {/* Step 1: Project Details */}
          {currentStep === 1 && (
            <div className="space-y-4">
              <div>
                <Label htmlFor="projectName">Project Name *</Label>
                <Input
                  id="projectName"
                  value={formData.name}
                  onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                  placeholder="Enter project name"
                />
              </div>

              <div>
                <Label htmlFor="projectDescription">Project Description *</Label>
                <Textarea
                  id="projectDescription"
                  value={formData.description}
                  onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                  placeholder="Describe the project scope and objectives"
                  rows={4}
                />
              </div>
            </div>
          )}

          {/* Step 2: Team Assignment */}
          {currentStep === 2 && (
            <div className="space-y-4">
              <div>
                <Label htmlFor="projectManager">Project Manager *</Label>
                <Select
                  value={formData.managerId}
                  onValueChange={(value) => setFormData(prev => ({ ...prev, managerId: value }))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select project manager" />
                  </SelectTrigger>
                  <SelectContent>
                    {users.map((user) => (
                      <SelectItem key={user.id} value={user.id}>
                        {getUserDisplayName(user)}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {handover.deliveryManager && (
                  <p className="text-sm text-gray-600 mt-1">
                    Recommended: {getUserDisplayName(handover.deliveryManager)} (from handover)
                  </p>
                )}
              </div>

              <div className="p-4 bg-blue-50 rounded-lg">
                <h4 className="font-medium text-blue-900 mb-2">Team Transition</h4>
                <p className="text-sm text-blue-800">
                  The project manager will take over from the delivery manager assigned to the handover.
                  Additional team members can be assigned after project creation.
                </p>
              </div>
            </div>
          )}

          {/* Step 3: Timeline & Budget */}
          {currentStep === 3 && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="startDate">Start Date *</Label>
                  <Input
                    id="startDate"
                    type="date"
                    value={formData.startDate}
                    onChange={(e) => setFormData(prev => ({ ...prev, startDate: e.target.value }))}
                  />
                </div>

                <div>
                  <Label htmlFor="endDate">End Date *</Label>
                  <Input
                    id="endDate"
                    type="date"
                    value={formData.endDate}
                    onChange={(e) => setFormData(prev => ({ ...prev, endDate: e.target.value }))}
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="budget">Project Budget</Label>
                <Input
                  id="budget"
                  type="number"
                  min="0"
                  step="0.01"
                  value={formData.budget || ''}
                  onChange={(e) => setFormData(prev => ({
                    ...prev,
                    budget: e.target.value ? parseFloat(e.target.value) : undefined
                  }))}
                  placeholder="Enter project budget"
                />
                {handover.opportunity.value && (
                  <p className="text-sm text-gray-600 mt-1">
                    Opportunity value: {formatCurrency(handover.opportunity.value)}
                  </p>
                )}
              </div>
            </div>
          )}

          {/* Step 4: Client Portal */}
          {currentStep === 4 && (
            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="clientPortal"
                  checked={formData.clientPortalEnabled}
                  onCheckedChange={(checked) =>
                    setFormData(prev => ({ ...prev, clientPortalEnabled: !!checked }))
                  }
                />
                <Label htmlFor="clientPortal">Enable Client Portal</Label>
              </div>

              <div className="p-4 bg-gray-50 rounded-lg">
                <h4 className="font-medium mb-2">Client Portal Features</h4>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• Project progress tracking</li>
                  <li>• Document sharing and collaboration</li>
                  <li>• Communication with project team</li>
                  <li>• Milestone and deliverable updates</li>
                  <li>• Invoice and payment tracking</li>
                </ul>
              </div>

              {formData.clientPortalEnabled && (
                <Alert>
                  <Globe className="h-4 w-4" />
                  <AlertDescription>
                    The client will receive an invitation to access the project portal after creation.
                  </AlertDescription>
                </Alert>
              )}
            </div>
          )}

          {/* Step 5: Confirm & Create */}
          {currentStep === 5 && (
            <div className="space-y-6">
              <div>
                <h3 className="font-medium mb-4">Project Summary</h3>
                <div className="grid grid-cols-2 gap-6">
                  <div className="space-y-3">
                    <div>
                      <span className="font-medium">Name:</span> {formData.name}
                    </div>
                    <div>
                      <span className="font-medium">Manager:</span>{' '}
                      {users.find(u => u.id === formData.managerId)?.firstName} {users.find(u => u.id === formData.managerId)?.lastName}
                    </div>
                    <div>
                      <span className="font-medium">Timeline:</span>{' '}
                      {format(new Date(formData.startDate), 'MMM d, yyyy')} - {format(new Date(formData.endDate), 'MMM d, yyyy')}
                    </div>
                  </div>

                  <div className="space-y-3">
                    <div>
                      <span className="font-medium">Budget:</span> {formatCurrency(formData.budget)}
                    </div>
                    <div>
                      <span className="font-medium">Client Portal:</span>{' '}
                      <Badge variant={formData.clientPortalEnabled ? "default" : "secondary"}>
                        {formData.clientPortalEnabled ? "Enabled" : "Disabled"}
                      </Badge>
                    </div>
                    <div>
                      <span className="font-medium">Handover Progress:</span>{' '}
                      <Badge variant="outline">{handover.checklistProgress}% Complete</Badge>
                    </div>
                  </div>
                </div>
              </div>

              <div className="p-4 bg-green-50 rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  <CheckCircle className="w-5 h-5 text-green-600" />
                  <h4 className="font-medium text-green-900">Ready to Create Project</h4>
                </div>
                <p className="text-sm text-green-800">
                  All required information has been provided. The project will be created and the handover will be marked as completed.
                </p>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Navigation */}
      <div className="flex justify-between">
        <Button
          variant="outline"
          onClick={prevStep}
          disabled={currentStep === 0}
          className="flex items-center gap-2"
        >
          <ArrowLeft className="w-4 h-4" />
          Previous
        </Button>

        <div className="flex gap-2">
          {currentStep < STEPS.length - 1 ? (
            <Button
              onClick={nextStep}
              disabled={!canProceed()}
              className="flex items-center gap-2"
            >
              Next
              <ArrowRight className="w-4 h-4" />
            </Button>
          ) : (
            <Button
              onClick={createProject}
              disabled={!canProceed() || creating}
              className="flex items-center gap-2"
            >
              {creating && <Loader2 className="w-4 h-4 animate-spin" />}
              Create Project
            </Button>
          )}
        </div>
      </div>
    </div>
  );
}