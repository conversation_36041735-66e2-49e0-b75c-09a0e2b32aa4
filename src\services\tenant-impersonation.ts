import { prisma } from '@/lib/prisma';

export interface ImpersonationSession {
  id: string;
  superAdminId: string;
  tenantId: string;
  userId: string;
  startedAt: Date;
  endedAt: Date | null;
  reason: string | null;
  ipAddress: string | null;
  userAgent: string | null;
}

export interface CreateImpersonationData {
  superAdminId: string;
  tenantId: string;
  userId: string;
  reason?: string;
  ipAddress?: string;
  userAgent?: string;
}

export interface ImpersonationSessionWithDetails extends ImpersonationSession {
  superAdmin: {
    id: string;
    email: string;
    firstName: string | null;
    lastName: string | null;
  };
  tenant: {
    id: string;
    name: string;
    slug: string;
  };
  user: {
    id: string;
    email: string;
    firstName: string | null;
    lastName: string | null;
  };
}

export class TenantImpersonationService {
  // Start impersonation session
  async startImpersonation(data: CreateImpersonationData): Promise<ImpersonationSession> {
    // Verify super admin exists and has platform admin privileges
    const superAdmin = await prisma.user.findUnique({
      where: { id: data.superAdminId }
    });

    if (!superAdmin || !superAdmin.isPlatformAdmin) {
      throw new Error('Only platform administrators can impersonate users');
    }

    // Verify tenant exists
    const tenant = await prisma.tenant.findUnique({
      where: { id: data.tenantId }
    });

    if (!tenant) {
      throw new Error('Tenant not found');
    }

    // Verify user exists and belongs to the tenant
    const user = await prisma.user.findFirst({
      where: {
        id: data.userId,
        tenantUsers: {
          some: {
            tenantId: data.tenantId,
            status: 'active'
          }
        }
      }
    });

    if (!user) {
      throw new Error('User not found in the specified tenant');
    }

    // End any existing active impersonation sessions for this super admin
    await this.endActiveImpersonationSessions(data.superAdminId);

    // Create new impersonation session
    const session = await prisma.impersonationSession.create({
      data: {
        superAdminId: data.superAdminId,
        tenantId: data.tenantId,
        userId: data.userId,
        reason: data.reason,
        ipAddress: data.ipAddress,
        userAgent: data.userAgent,
        startedAt: new Date()
      }
    });

    // Log the impersonation start
    await this.logImpersonationEvent(
      'IMPERSONATION_STARTED',
      data.superAdminId,
      data.tenantId,
      data.userId,
      {
        sessionId: session.id,
        reason: data.reason,
        ipAddress: data.ipAddress,
        userAgent: data.userAgent
      }
    );

    return session;
  }

  // End impersonation session
  async endImpersonation(sessionId: string, superAdminId: string): Promise<void> {
    const session = await prisma.impersonationSession.findUnique({
      where: { id: sessionId }
    });

    if (!session) {
      throw new Error('Impersonation session not found');
    }

    if (session.superAdminId !== superAdminId) {
      throw new Error('Unauthorized to end this impersonation session');
    }

    if (session.endedAt) {
      throw new Error('Impersonation session already ended');
    }

    // End the session
    await prisma.impersonationSession.update({
      where: { id: sessionId },
      data: { endedAt: new Date() }
    });

    // Log the impersonation end
    await this.logImpersonationEvent(
      'IMPERSONATION_ENDED',
      session.superAdminId,
      session.tenantId,
      session.userId,
      {
        sessionId: session.id,
        duration: Date.now() - session.startedAt.getTime()
      }
    );
  }

  // End all active impersonation sessions for a super admin
  async endActiveImpersonationSessions(superAdminId: string): Promise<void> {
    const activeSessions = await prisma.impersonationSession.findMany({
      where: {
        superAdminId,
        endedAt: null
      }
    });

    if (activeSessions.length > 0) {
      await prisma.impersonationSession.updateMany({
        where: {
          superAdminId,
          endedAt: null
        },
        data: { endedAt: new Date() }
      });

      // Log the bulk session end
      for (const session of activeSessions) {
        await this.logImpersonationEvent(
          'IMPERSONATION_ENDED',
          session.superAdminId,
          session.tenantId,
          session.userId,
          {
            sessionId: session.id,
            reason: 'Auto-ended due to new session',
            duration: Date.now() - session.startedAt.getTime()
          }
        );
      }
    }
  }

  // Get active impersonation session for super admin
  async getActiveImpersonationSession(superAdminId: string): Promise<ImpersonationSessionWithDetails | null> {
    return await prisma.impersonationSession.findFirst({
      where: {
        superAdminId,
        endedAt: null
      },
      include: {
        superAdmin: {
          select: {
            id: true,
            email: true,
            firstName: true,
            lastName: true
          }
        },
        tenant: {
          select: {
            id: true,
            name: true,
            slug: true
          }
        },
        user: {
          select: {
            id: true,
            email: true,
            firstName: true,
            lastName: true
          }
        }
      }
    });
  }

  // Get impersonation history
  async getImpersonationHistory(
    page: number = 1,
    limit: number = 20,
    filters?: {
      superAdminId?: string;
      tenantId?: string;
      userId?: string;
      startDate?: Date;
      endDate?: Date;
    }
  ): Promise<{
    sessions: ImpersonationSessionWithDetails[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
    };
  }> {
    const where: any = {};

    if (filters?.superAdminId) {
      where.superAdminId = filters.superAdminId;
    }

    if (filters?.tenantId) {
      where.tenantId = filters.tenantId;
    }

    if (filters?.userId) {
      where.userId = filters.userId;
    }

    if (filters?.startDate || filters?.endDate) {
      where.startedAt = {};
      if (filters.startDate) {
        where.startedAt.gte = filters.startDate;
      }
      if (filters.endDate) {
        where.startedAt.lte = filters.endDate;
      }
    }

    const [sessions, total] = await Promise.all([
      prisma.impersonationSession.findMany({
        where,
        include: {
          superAdmin: {
            select: {
              id: true,
              email: true,
              firstName: true,
              lastName: true
            }
          },
          tenant: {
            select: {
              id: true,
              name: true,
              slug: true
            }
          },
          user: {
            select: {
              id: true,
              email: true,
              firstName: true,
              lastName: true
            }
          }
        },
        orderBy: { startedAt: 'desc' },
        skip: (page - 1) * limit,
        take: limit
      }),
      prisma.impersonationSession.count({ where })
    ]);

    return {
      sessions,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    };
  }

  // Verify if user is currently being impersonated
  async isUserBeingImpersonated(userId: string): Promise<boolean> {
    const activeSession = await prisma.impersonationSession.findFirst({
      where: {
        userId,
        endedAt: null
      }
    });

    return !!activeSession;
  }

  // Get users available for impersonation in a tenant
  async getTenantUsersForImpersonation(tenantId: string): Promise<Array<{
    id: string;
    email: string;
    firstName: string | null;
    lastName: string | null;
    role: string;
    isBeingImpersonated: boolean;
  }>> {
    const tenantUsers = await prisma.tenantUser.findMany({
      where: {
        tenantId,
        status: 'active',
        user: {
          // Exclude platform admins from impersonation lists
          isPlatformAdmin: false
        }
      },
      include: {
        user: {
          select: {
            id: true,
            email: true,
            firstName: true,
            lastName: true
          }
        }
      }
    });

    // Check which users are currently being impersonated
    const userIds = tenantUsers.map(tu => tu.user.id);
    const activeImpersonations = await prisma.impersonationSession.findMany({
      where: {
        userId: { in: userIds },
        endedAt: null
      },
      select: { userId: true }
    });

    const impersonatedUserIds = new Set(activeImpersonations.map(ai => ai.userId));

    return tenantUsers.map(tu => ({
      id: tu.user.id,
      email: tu.user.email,
      firstName: tu.user.firstName,
      lastName: tu.user.lastName,
      role: tu.role,
      isBeingImpersonated: impersonatedUserIds.has(tu.user.id)
    }));
  }

  // Private method to log impersonation events
  private async logImpersonationEvent(
    action: string,
    superAdminId: string,
    tenantId: string,
    userId: string,
    metadata: any
  ): Promise<void> {
    await prisma.auditLog.create({
      data: {
        tenantId: null, // Platform-level action
        userId: superAdminId,
        action,
        resourceType: 'IMPERSONATION',
        resourceId: userId,
        newValues: {
          targetTenantId: tenantId,
          targetUserId: userId,
          ...metadata
        }
      }
    });
  }
}
