import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { hasPermission } from '@/lib/permissions';
import { PermissionResource, PermissionAction } from '@/types';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';

// Validation schemas
const createContentSectionSchema = z.object({
  sectionType: z.string().min(1, 'Section type is required'),
  title: z.string().min(1, 'Title is required').max(255),
  description: z.string().optional(),
  promptEn: z.string().min(1, 'English prompt is required'),
  promptAr: z.string().optional(),
  systemPromptEn: z.string().min(1, 'English system prompt is required'),
  systemPromptAr: z.string().optional(),
  orderIndex: z.number().min(0).optional(),
  isActive: z.boolean().default(true),
  isDefault: z.boolean().default(false)
});

const querySchema = z.object({
  search: z.string().optional(),
  isActive: z.coerce.boolean().optional(),
  page: z.coerce.number().min(1).default(1),
  limit: z.coerce.number().min(1).max(100).default(20),
  sortBy: z.enum(['title', 'sectionType', 'orderIndex', 'createdAt']).default('orderIndex'),
  sortOrder: z.enum(['asc', 'desc']).default('asc')
});

/**
 * GET /api/proposal-content-sections
 * Get all proposal content sections for the current tenant
 */
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user's current tenant
    const currentTenant = session.user.tenants?.[0];
    if (!currentTenant) {
      return NextResponse.json({ error: 'No tenant access' }, { status: 403 });
    }

    // Check permission
    const hasAccess = await hasPermission(
      PermissionResource.PROPOSALS,
      PermissionAction.READ,
      currentTenant.id
    );

    if (!hasAccess) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Parse and validate query parameters
    const { searchParams } = new URL(request.url);
    const queryParams = Object.fromEntries(searchParams.entries());
    const validatedQuery = querySchema.parse(queryParams);

    // Build where clause
    const where: any = {
      tenantId: currentTenant.id
    };

    // Add search filter
    if (validatedQuery.search) {
      where.OR = [
        {
          title: {
            contains: validatedQuery.search,
            mode: 'insensitive'
          }
        },
        {
          sectionType: {
            contains: validatedQuery.search,
            mode: 'insensitive'
          }
        }
      ];
    }

    // Add active filter
    if (validatedQuery.isActive !== undefined) {
      where.isActive = validatedQuery.isActive;
    }

    // Calculate pagination
    const skip = (validatedQuery.page - 1) * validatedQuery.limit;

    // Get total count
    const totalCount = await prisma.proposalContentSection.count({ where });

    // Get content sections
    const contentSections = await prisma.proposalContentSection.findMany({
      where,
      include: {
        createdBy: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true
          }
        }
      },
      orderBy: {
        [validatedQuery.sortBy]: validatedQuery.sortOrder
      },
      skip,
      take: validatedQuery.limit
    });

    return NextResponse.json({
      data: contentSections,
      pagination: {
        page: validatedQuery.page,
        limit: validatedQuery.limit,
        totalCount,
        totalPages: Math.ceil(totalCount / validatedQuery.limit)
      }
    });

  } catch (error) {
    console.error('Error fetching proposal content sections:', error);
    return NextResponse.json(
      { error: 'Failed to fetch proposal content sections' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/proposal-content-sections
 * Create a new proposal content section
 */
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user's current tenant
    const currentTenant = session.user.tenants?.[0];
    if (!currentTenant) {
      return NextResponse.json({ error: 'No tenant access' }, { status: 403 });
    }

    // Check permission
    const hasAccess = await hasPermission(
      PermissionResource.PROPOSALS,
      PermissionAction.CREATE,
      currentTenant.id
    );

    if (!hasAccess) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Parse and validate request body
    const body = await request.json();
    const validatedData = createContentSectionSchema.parse(body);

    // Check if section type already exists for this tenant
    const existingSection = await prisma.proposalContentSection.findUnique({
      where: {
        tenantId_sectionType: {
          tenantId: currentTenant.id,
          sectionType: validatedData.sectionType
        }
      }
    });

    if (existingSection) {
      return NextResponse.json(
        { error: 'A content section with this type already exists' },
        { status: 409 }
      );
    }

    // Get next order index if not provided
    let orderIndex = validatedData.orderIndex;
    if (orderIndex === undefined) {
      const maxOrder = await prisma.proposalContentSection.findFirst({
        where: { tenantId: currentTenant.id },
        orderBy: { orderIndex: 'desc' },
        select: { orderIndex: true }
      });
      orderIndex = (maxOrder?.orderIndex || 0) + 1;
    }

    // Create content section
    const contentSection = await prisma.proposalContentSection.create({
      data: {
        tenantId: currentTenant.id,
        sectionType: validatedData.sectionType,
        title: validatedData.title,
        description: validatedData.description,
        promptEn: validatedData.promptEn,
        promptAr: validatedData.promptAr,
        systemPromptEn: validatedData.systemPromptEn,
        systemPromptAr: validatedData.systemPromptAr,
        orderIndex,
        isActive: validatedData.isActive,
        isDefault: validatedData.isDefault,
        createdById: session.user.id
      },
      include: {
        createdBy: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true
          }
        }
      }
    });

    return NextResponse.json(contentSection, { status: 201 });

  } catch (error) {
    console.error('Error creating proposal content section:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to create proposal content section' },
      { status: 500 }
    );
  }
}
