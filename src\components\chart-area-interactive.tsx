"use client"

import { useState } from 'react'
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"

interface ChartData {
  date: string
  mobile: number
  desktop: number
}

const chartData: ChartData[] = [
  { date: 'Apr 6', mobile: 218, desktop: 292 },
  { date: 'Apr 12', mobile: 210, desktop: 280 },
  { date: 'Apr 18', mobile: 195, desktop: 265 },
  { date: 'Apr 24', mobile: 230, desktop: 310 },
  { date: 'Apr 30', mobile: 245, desktop: 325 },
  { date: 'May 6', mobile: 260, desktop: 340 },
  { date: 'May 12', mobile: 275, desktop: 355 },
  { date: 'May 18', mobile: 290, desktop: 370 },
  { date: 'May 24', mobile: 305, desktop: 385 },
  { date: 'May 30', mobile: 320, desktop: 400 },
  { date: 'Jun 5', mobile: 335, desktop: 415 },
  { date: 'Jun 11', mobile: 350, desktop: 430 },
  { date: 'Jun 17', mobile: 365, desktop: 445 },
  { date: 'Jun 23', mobile: 380, desktop: 460 },
  { date: 'Jun 30', mobile: 395, desktop: 475 },
]

export function ChartAreaInteractive() {
  const [timeRange, setTimeRange] = useState("Last 3 months")
  
  const maxValue = Math.max(...chartData.map(d => Math.max(d.mobile, d.desktop)))
  
  return (
    <Card>
      <CardHeader className="flex items-center gap-2 space-y-0 border-b py-5 sm:flex-row">
        <div className="grid flex-1 gap-1 text-center sm:text-left">
          <CardTitle>Total Visitors</CardTitle>
          <CardDescription>
            Total for the last 3 months
          </CardDescription>
        </div>
        <div className="flex gap-2">
          {["Last 3 months", "Last 30 days", "Last 7 days"].map((range) => (
            <Button
              key={range}
              variant={timeRange === range ? "default" : "outline"}
              size="sm"
              onClick={() => setTimeRange(range)}
            >
              {range}
            </Button>
          ))}
        </div>
      </CardHeader>
      <CardContent className="px-2 pt-4 sm:px-6 sm:pt-6">
        <div className="aspect-auto h-[250px] w-full">
          {/* Simple SVG Chart */}
          <svg className="h-full w-full" viewBox="0 0 800 250">
            {/* Grid lines */}
            {[0, 1, 2, 3, 4].map((i) => (
              <line
                key={i}
                x1="0"
                y1={i * 50}
                x2="800"
                y2={i * 50}
                stroke="hsl(var(--border))"
                strokeWidth="1"
                opacity="0.3"
              />
            ))}
            
            {/* Desktop area */}
            <path
              d={`M 0 ${250 - (chartData[0].desktop / maxValue) * 200} ${chartData
                .map((d, i) => `L ${(i * 800) / (chartData.length - 1)} ${250 - (d.desktop / maxValue) * 200}`)
                .join(' ')} L 800 250 L 0 250 Z`}
              fill="hsl(var(--muted))"
              opacity="0.6"
            />
            
            {/* Mobile area */}
            <path
              d={`M 0 ${250 - (chartData[0].mobile / maxValue) * 200} ${chartData
                .map((d, i) => `L ${(i * 800) / (chartData.length - 1)} ${250 - (d.mobile / maxValue) * 200}`)
                .join(' ')} L 800 250 L 0 250 Z`}
              fill="hsl(var(--primary))"
              opacity="0.8"
            />
            
            {/* Desktop line */}
            <path
              d={`M 0 ${250 - (chartData[0].desktop / maxValue) * 200} ${chartData
                .map((d, i) => `L ${(i * 800) / (chartData.length - 1)} ${250 - (d.desktop / maxValue) * 200}`)
                .join(' ')}`}
              fill="none"
              stroke="hsl(var(--muted-foreground))"
              strokeWidth="2"
            />
            
            {/* Mobile line */}
            <path
              d={`M 0 ${250 - (chartData[0].mobile / maxValue) * 200} ${chartData
                .map((d, i) => `L ${(i * 800) / (chartData.length - 1)} ${250 - (d.mobile / maxValue) * 200}`)
                .join(' ')}`}
              fill="none"
              stroke="hsl(var(--primary))"
              strokeWidth="2"
            />
          </svg>
        </div>
        
        {/* Legend */}
        <div className="flex items-center justify-center gap-6 pt-4">
          <div className="flex items-center gap-2">
            <div className="h-3 w-3 rounded-full bg-primary"></div>
            <span className="text-sm text-muted-foreground">Mobile</span>
            <span className="text-sm font-medium">218</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="h-3 w-3 rounded-full bg-muted-foreground"></div>
            <span className="text-sm text-muted-foreground">Desktop</span>
            <span className="text-sm font-medium">292</span>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
