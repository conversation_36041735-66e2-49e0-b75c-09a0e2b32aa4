"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  <PERSON><PERSON>crumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu"
import { Separator } from "@/components/ui/separator"
import {
  SidebarTrigger,
} from "@/components/ui/sidebar"
import { usePathname, useRouter } from 'next/navigation'
import { useMemo } from 'react'
import {
  PlusIcon,
  ChevronDownIcon,
  UserIcon,
  BuildingOfficeIcon,
  UserGroupIcon,
  ChartBarIcon
} from '@heroicons/react/24/outline'
import { SmoothLink } from '@/components/ui/smooth-link'

export function SuperAdminSiteHeader() {
  const pathname = usePathname()
  const router = useRouter()

  const quickCreateOptions = [
    {
      name: 'Contact',
      description: 'Create a new contact',
      href: '/contacts/new',
      icon: UserIcon,
      color: 'text-blue-600 dark:text-blue-400',
      bgColor: 'bg-blue-50 dark:bg-blue-900/20',
    },
    {
      name: 'Company',
      description: 'Create a new company',
      href: '/companies/new',
      icon: BuildingOfficeIcon,
      color: 'text-green-600 dark:text-green-400',
      bgColor: 'bg-green-50 dark:bg-green-900/20',
    },
    {
      name: 'Lead',
      description: 'Create a new lead',
      href: '/leads/new',
      icon: UserGroupIcon,
      color: 'text-purple-600 dark:text-purple-400',
      bgColor: 'bg-purple-50 dark:bg-purple-900/20',
    },
    {
      name: 'Opportunity',
      description: 'Create a new opportunity',
      href: '/opportunities/new',
      icon: ChartBarIcon,
      color: 'text-orange-600 dark:text-orange-400',
      bgColor: 'bg-orange-50 dark:bg-orange-900/20',
    },
  ]

  const handleQuickCreate = (href: string) => {
    router.push(href)
  }

  const breadcrumbs = useMemo(() => {
    const segments = pathname.split('/').filter(Boolean)
    const breadcrumbItems = []

    // Add Admin Dashboard as root for super admin routes
    breadcrumbItems.push({
      label: 'Admin Dashboard',
      href: '/admin/dashboard',
      isLast: segments.length === 2 && segments[0] === 'admin' && segments[1] === 'dashboard'
    })

    // Add other segments
    let currentPath = ''
    segments.forEach((segment, index) => {
      if (segment === 'admin') return // Skip admin as it's part of the root
      if (segment === 'dashboard' && segments[0] === 'admin') return // Skip dashboard as it's already added

      currentPath += `/${segment}`
      // For admin routes, prepend /admin
      if (segments[0] === 'admin') {
        currentPath = `/admin${currentPath}`
      }
      
      const isLast = index === segments.length - 1

      // Format segment name with special handling for admin routes
      let label = segment
        .split('-')
        .map(word => word.charAt(0).toUpperCase() + word.slice(1))
        .join(' ')

      // Special cases for admin routes
      if (segment === 'health') label = 'System Health'
      if (segment === 'performance') label = 'Performance'
      if (segment === 'tenants') label = 'Tenant Management'
      if (segment === 'users') label = 'User Management'
      if (segment === 'analytics') label = 'Analytics'
      if (segment === 'billing') label = 'Billing & Revenue'
      if (segment === 'settings') label = 'System Settings'

      breadcrumbItems.push({
        label,
        href: currentPath,
        isLast
      })
    })

    return breadcrumbItems
  }, [pathname])

  return (
    <header className="flex h-16 shrink-0 items-center gap-2 transition-[width,height] ease-linear group-has-data-[collapsible=icon]/sidebar-wrapper:h-12">
      <div className="flex items-center gap-2 px-4">
        <SidebarTrigger className="-ml-1" />
        <Separator orientation="vertical" className="mr-2 h-4" />
        <Breadcrumb>
          <BreadcrumbList>
            {breadcrumbs.map((breadcrumb, index) => (
              <div key={breadcrumb.href} className="flex items-center">
                {index > 0 && <BreadcrumbSeparator className="hidden md:block" />}
                <BreadcrumbItem className={index === 0 ? "hidden md:block" : ""}>
                  {breadcrumb.isLast ? (
                    <BreadcrumbPage>{breadcrumb.label}</BreadcrumbPage>
                  ) : (
                    <BreadcrumbLink asChild>
                      <SmoothLink href={breadcrumb.href}>
                        {breadcrumb.label}
                      </SmoothLink>
                    </BreadcrumbLink>
                  )}
                </BreadcrumbItem>
              </div>
            ))}
          </BreadcrumbList>
        </Breadcrumb>
      </div>
    </header>
  )
}
