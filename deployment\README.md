# CRM Platform Deployment

This directory contains all deployment-related files, scripts, and configurations for the CRM platform. The deployment system supports multiple environments (development, staging, production) with Docker containerization.

## 📁 Directory Structure

```
deployment/
├── README.md                    # This file
├── docker/                     # Docker configurations
│   ├── Dockerfile              # Main application Dockerfile
│   ├── docker-compose.yml      # Base Docker Compose configuration
│   ├── environments/           # Environment-specific configurations
│   │   ├── development.yml     # Development overrides
│   │   ├── staging.yml         # Staging configuration
│   │   └── production.yml      # Production configuration
│   └── .dockerignore           # Docker ignore file
├── scripts/                    # Deployment scripts
│   ├── deploy.sh               # Main deployment script (Linux/Mac)
│   ├── deploy.bat              # Main deployment script (Windows)
│   ├── ssl/                    # SSL certificate management
│   │   ├── generate-certs.sh   # Generate SSL certificates (Linux/Mac)
│   │   └── generate-certs.bat  # Generate SSL certificates (Windows)
│   └── utils/                  # Utility scripts
│       ├── health-check.sh     # Health check script
│       ├── backup.sh           # Backup script
│       └── restore.sh          # Restore script
├── config/                     # Configuration files
│   ├── nginx/                  # Nginx configuration
│   │   ├── nginx.conf          # Main Nginx configuration
│   │   ├── sites/              # Site configurations
│   │   │   ├── default.conf    # Default site configuration
│   │   │   ├── staging.conf    # Staging site configuration
│   │   │   └── production.conf # Production site configuration
│   │   └── ssl/                # SSL certificates directory
│   ├── redis/                  # Redis configuration
│   │   └── redis.conf          # Redis configuration file
│   └── postgres/               # PostgreSQL configuration
│       └── init/               # Database initialization scripts
└── docs/                       # Deployment documentation
    ├── deployment-guide.md     # Comprehensive deployment guide
    ├── environment-setup.md    # Environment setup instructions
    ├── troubleshooting.md      # Common issues and solutions
    └── security.md             # Security best practices
```

## 🚀 Quick Start

### Prerequisites
- Docker and Docker Compose installed
- Node.js 18+ (for local development)
- Git

### Deploy to Development
```bash
# Linux/Mac
./deployment/scripts/deploy.sh development

# Windows
deployment\scripts\deploy.bat development
```

### Deploy to Production
```bash
# Linux/Mac
./deployment/scripts/deploy.sh production

# Windows
deployment\scripts\deploy.bat production
```

## 🌍 Supported Environments

- **development**: Local development with hot reloading
- **staging**: Pre-production testing environment
- **production**: Production environment with optimizations

## 📚 Documentation

- [Deployment Guide](./docs/deployment-guide.md) - Complete deployment instructions
- [Environment Setup](./docs/environment-setup.md) - Environment configuration
- [Troubleshooting](./docs/troubleshooting.md) - Common issues and solutions
- [Security](./docs/security.md) - Security best practices

## 🔧 Configuration

All environment-specific configurations are located in:
- `docker/environments/` - Docker Compose overrides
- `config/` - Application and service configurations

## 🛡️ Security

- SSL certificates are automatically generated for staging/production
- Security headers are configured in Nginx
- Database and Redis are secured with authentication
- All services run with minimal privileges

## 📞 Support

For deployment issues, check the troubleshooting guide or contact the development team.
