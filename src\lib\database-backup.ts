import { exec } from 'child_process';
import { promisify } from 'util';
import fs from 'fs/promises';
import path from 'path';
import { prisma } from './prisma';

const execAsync = promisify(exec);

export interface BackupOptions {
  includeData?: boolean;
  includeTenants?: string[]; // Specific tenant IDs to backup
  excludeTenants?: string[]; // Tenant IDs to exclude
  outputPath?: string;
  compress?: boolean;
}

export interface RestoreOptions {
  backupFile: string;
  targetDatabase?: string;
  dropExisting?: boolean;
  tenantMapping?: Record<string, string>; // Map old tenant IDs to new ones
}

export class DatabaseBackupService {
  private readonly backupDir: string;
  private readonly dbUrl: string;

  constructor() {
    this.backupDir = process.env.BACKUP_DIR || './backups';
    this.dbUrl = process.env.DATABASE_URL!;
    
    if (!this.dbUrl) {
      throw new Error('DATABASE_URL environment variable is required');
    }
  }

  // Create full database backup
  async createFullBackup(options: BackupOptions = {}): Promise<string> {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `full-backup-${timestamp}.sql`;
    const outputPath = options.outputPath || path.join(this.backupDir, filename);

    // Ensure backup directory exists
    await this.ensureBackupDirectory();

    try {
      // Parse database URL
      const dbConfig = this.parseDbUrl(this.dbUrl);
      
      // Build pg_dump command
      let command = `pg_dump -h ${dbConfig.host} -p ${dbConfig.port} -U ${dbConfig.username} -d ${dbConfig.database}`;
      
      if (!options.includeData) {
        command += ' --schema-only';
      }
      
      command += ` --file="${outputPath}"`;

      // Set password environment variable
      const env = { ...process.env, PGPASSWORD: dbConfig.password };

      await execAsync(command, { env });

      // Compress if requested
      if (options.compress) {
        await this.compressFile(outputPath);
        return `${outputPath}.gz`;
      }

      // Log backup creation
      await this.logBackupEvent('full_backup_created', {
        filename,
        size: await this.getFileSize(outputPath),
        includeData: options.includeData,
        compressed: options.compress
      });

      return outputPath;
    } catch (error) {
      console.error('Backup creation failed:', error);
      throw new Error(`Backup failed: ${error}`);
    }
  }

  // Create tenant-specific backup
  async createTenantBackup(tenantId: string, options: BackupOptions = {}): Promise<string> {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `tenant-${tenantId}-backup-${timestamp}.sql`;
    const outputPath = options.outputPath || path.join(this.backupDir, filename);

    await this.ensureBackupDirectory();

    try {
      // Verify tenant exists
      const tenant = await prisma.tenant.findUnique({
        where: { id: tenantId }
      });

      if (!tenant) {
        throw new Error(`Tenant ${tenantId} not found`);
      }

      // Create tenant-specific backup using custom SQL
      const backupData = await this.generateTenantBackupSQL(tenantId, options.includeData);
      await fs.writeFile(outputPath, backupData);

      // Compress if requested
      if (options.compress) {
        await this.compressFile(outputPath);
        return `${outputPath}.gz`;
      }

      // Log backup creation
      await this.logBackupEvent('tenant_backup_created', {
        tenantId,
        filename,
        size: await this.getFileSize(outputPath),
        includeData: options.includeData,
        compressed: options.compress
      });

      return outputPath;
    } catch (error) {
      console.error('Tenant backup creation failed:', error);
      throw new Error(`Tenant backup failed: ${error}`);
    }
  }

  // Restore database from backup
  async restoreFromBackup(options: RestoreOptions): Promise<void> {
    try {
      // Verify backup file exists
      const backupExists = await fs.access(options.backupFile).then(() => true).catch(() => false);
      if (!backupExists) {
        throw new Error(`Backup file not found: ${options.backupFile}`);
      }

      const dbConfig = this.parseDbUrl(this.dbUrl);
      const targetDb = options.targetDatabase || dbConfig.database;

      // Drop existing database if requested
      if (options.dropExisting) {
        await this.dropDatabase(targetDb);
        await this.createDatabase(targetDb);
      }

      // Decompress if needed
      let restoreFile = options.backupFile;
      if (options.backupFile.endsWith('.gz')) {
        restoreFile = await this.decompressFile(options.backupFile);
      }

      // Restore database
      const command = `psql -h ${dbConfig.host} -p ${dbConfig.port} -U ${dbConfig.username} -d ${targetDb} -f "${restoreFile}"`;
      const env = { ...process.env, PGPASSWORD: dbConfig.password };

      await execAsync(command, { env });

      // Apply tenant mapping if provided
      if (options.tenantMapping) {
        await this.applyTenantMapping(options.tenantMapping);
      }

      // Log restore event
      await this.logBackupEvent('database_restored', {
        backupFile: options.backupFile,
        targetDatabase: targetDb,
        tenantMapping: options.tenantMapping
      });

    } catch (error) {
      console.error('Database restore failed:', error);
      throw new Error(`Restore failed: ${error}`);
    }
  }

  // Create automated backup schedule
  async scheduleBackups(): Promise<void> {
    // This would typically be implemented with a cron job or task scheduler
    // For now, we'll create a simple interval-based backup
    
    const backupInterval = parseInt(process.env.BACKUP_INTERVAL_HOURS || '24') * 60 * 60 * 1000;
    
    setInterval(async () => {
      try {
        console.log('Starting scheduled backup...');
        const backupPath = await this.createFullBackup({ 
          includeData: true, 
          compress: true 
        });
        console.log(`Scheduled backup completed: ${backupPath}`);
        
        // Clean up old backups (keep last 7 days)
        await this.cleanupOldBackups(7);
      } catch (error) {
        console.error('Scheduled backup failed:', error);
      }
    }, backupInterval);
  }

  // Generate tenant-specific backup SQL
  private async generateTenantBackupSQL(tenantId: string, includeData: boolean = true): Promise<string> {
    const tenantTables = [
      'tenants', 'tenant_users', 'subscriptions', 'roles', 'permissions',
      'user_roles', 'role_permissions', 'contacts', 'companies', 'leads',
      'opportunities', 'proposals', 'projects', 'activities', 'documents',
      'ai_insights', 'tenant_settings', 'usage_metrics', 'billing_events',
      'audit_logs'
    ];

    let sql = `-- Tenant ${tenantId} Backup\n`;
    sql += `-- Generated on ${new Date().toISOString()}\n\n`;

    // Add schema creation
    sql += `-- Schema creation\n`;
    for (const table of tenantTables) {
      const createTableSQL = await this.getTableSchema(table);
      sql += `${createTableSQL}\n\n`;
    }

    if (includeData) {
      sql += `-- Data insertion\n`;
      
      // Export tenant data with proper filtering
      for (const table of tenantTables) {
        const tableData = await this.getTenantTableData(table, tenantId);
        if (tableData) {
          sql += `${tableData}\n\n`;
        }
      }
    }

    return sql;
  }

  // Get table schema
  private async getTableSchema(tableName: string): Promise<string> {
    const result = await prisma.$queryRawUnsafe(`
      SELECT 
        'CREATE TABLE IF NOT EXISTS ' || schemaname||'.'||tablename||' (' ||
        array_to_string(
          array_agg(
            column_name ||' '|| type ||
            case when character_maximum_length is not null 
            then '('||character_maximum_length||')' 
            else '' end ||
            case when is_nullable = 'NO' then ' NOT NULL' else '' end
          ), ', '
        ) || ');' as create_statement
      FROM (
        SELECT 
          schemaname, tablename, column_name, data_type as type,
          character_maximum_length, is_nullable
        FROM information_schema.columns c
        JOIN pg_tables t ON c.table_name = t.tablename
        WHERE t.tablename = $1
        ORDER BY ordinal_position
      ) as columns
      GROUP BY schemaname, tablename;
    `, tableName) as any[];

    return result[0]?.create_statement || '';
  }

  // Get tenant-specific table data
  private async getTenantTableData(tableName: string, tenantId: string): Promise<string> {
    try {
      let whereClause = '';
      
      // Determine how to filter by tenant
      if (tableName === 'tenants') {
        whereClause = `WHERE id = '${tenantId}'`;
      } else if (tableName === 'users') {
        // Include users that belong to this tenant
        whereClause = `WHERE id IN (SELECT user_id FROM tenant_users WHERE tenant_id = '${tenantId}')`;
      } else {
        // Most tables have tenant_id column
        whereClause = `WHERE tenant_id = '${tenantId}'`;
      }

      const data = await prisma.$queryRawUnsafe(`
        SELECT * FROM ${tableName} ${whereClause}
      `);

      if (!Array.isArray(data) || data.length === 0) {
        return '';
      }

      // Generate INSERT statements
      const columns = Object.keys(data[0]);
      const values = data.map(row => 
        '(' + columns.map(col => {
          const value = row[col];
          if (value === null) return 'NULL';
          if (typeof value === 'string') return `'${value.replace(/'/g, "''")}'`;
          if (value instanceof Date) return `'${value.toISOString()}'`;
          return value;
        }).join(', ') + ')'
      ).join(',\n  ');

      return `INSERT INTO ${tableName} (${columns.join(', ')}) VALUES\n  ${values};`;
    } catch (error) {
      console.error(`Error getting data for table ${tableName}:`, error);
      return '';
    }
  }

  // Utility methods
  private parseDbUrl(url: string) {
    const match = url.match(/postgresql:\/\/([^:]+):([^@]+)@([^:]+):(\d+)\/(.+)/);
    if (!match) {
      throw new Error('Invalid database URL format');
    }

    return {
      username: match[1],
      password: match[2],
      host: match[3],
      port: match[4],
      database: match[5]
    };
  }

  private async ensureBackupDirectory(): Promise<void> {
    try {
      await fs.access(this.backupDir);
    } catch {
      await fs.mkdir(this.backupDir, { recursive: true });
    }
  }

  private async compressFile(filePath: string): Promise<void> {
    await execAsync(`gzip "${filePath}"`);
  }

  private async decompressFile(filePath: string): Promise<string> {
    const decompressed = filePath.replace('.gz', '');
    await execAsync(`gunzip -c "${filePath}" > "${decompressed}"`);
    return decompressed;
  }

  private async getFileSize(filePath: string): Promise<number> {
    const stats = await fs.stat(filePath);
    return stats.size;
  }

  private async dropDatabase(dbName: string): Promise<void> {
    const dbConfig = this.parseDbUrl(this.dbUrl);
    const command = `dropdb -h ${dbConfig.host} -p ${dbConfig.port} -U ${dbConfig.username} ${dbName}`;
    const env = { ...process.env, PGPASSWORD: dbConfig.password };
    await execAsync(command, { env });
  }

  private async createDatabase(dbName: string): Promise<void> {
    const dbConfig = this.parseDbUrl(this.dbUrl);
    const command = `createdb -h ${dbConfig.host} -p ${dbConfig.port} -U ${dbConfig.username} ${dbName}`;
    const env = { ...process.env, PGPASSWORD: dbConfig.password };
    await execAsync(command, { env });
  }

  private async applyTenantMapping(mapping: Record<string, string>): Promise<void> {
    for (const [oldId, newId] of Object.entries(mapping)) {
      await prisma.$executeRaw`UPDATE tenants SET id = ${newId} WHERE id = ${oldId}`;
      // Update all related tables with new tenant ID
      const tenantTables = ['tenant_users', 'subscriptions', 'contacts', 'companies', 'leads'];
      for (const table of tenantTables) {
        await prisma.$executeRawUnsafe(`UPDATE ${table} SET tenant_id = $1 WHERE tenant_id = $2`, newId, oldId);
      }
    }
  }

  private async cleanupOldBackups(daysToKeep: number): Promise<void> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);

    try {
      const files = await fs.readdir(this.backupDir);
      for (const file of files) {
        const filePath = path.join(this.backupDir, file);
        const stats = await fs.stat(filePath);
        
        if (stats.mtime < cutoffDate) {
          await fs.unlink(filePath);
          console.log(`Deleted old backup: ${file}`);
        }
      }
    } catch (error) {
      console.error('Error cleaning up old backups:', error);
    }
  }

  private async logBackupEvent(eventType: string, data: any): Promise<void> {
    try {
      await prisma.auditLog.create({
        data: {
          action: eventType,
          resourceType: 'database',
          newValues: data,
          createdAt: new Date()
        }
      });
    } catch (error) {
      console.error('Failed to log backup event:', error);
    }
  }
}
