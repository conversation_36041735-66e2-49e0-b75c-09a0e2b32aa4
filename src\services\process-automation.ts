import { prisma } from '@/lib/prisma';
import { z } from 'zod';
import { HandoverManagementService } from './handover-management';
import { HandoverNotificationService } from './handover-notifications';
import { ProjectCreationService } from './project-creation';

// Validation schemas
export const automationTriggerSchema = z.object({
  id: z.string().optional(),
  name: z.string().min(1, 'Name is required').max(255),
  description: z.string().optional(),
  triggerType: z.enum(['opportunity_status_change', 'handover_completion', 'project_milestone', 'time_based']),
  triggerConditions: z.record(z.any()),
  actions: z.array(z.object({
    type: z.enum(['create_handover', 'send_notification', 'create_project', 'assign_team', 'schedule_meeting']),
    config: z.record(z.any())
  })),
  isActive: z.boolean().default(true),
  priority: z.number().int().min(1).max(10).default(5)
});

export const automationExecutionSchema = z.object({
  triggerId: z.string(),
  entityType: z.enum(['opportunity', 'handover', 'project', 'contact', 'lead']),
  entityId: z.string(),
  triggerData: z.record(z.any()),
  executedActions: z.array(z.object({
    actionType: z.string(),
    status: z.enum(['pending', 'completed', 'failed']),
    result: z.record(z.any()).optional(),
    error: z.string().optional()
  })).default([])
});

// Types
export type AutomationTrigger = z.infer<typeof automationTriggerSchema>;
export type AutomationExecution = z.infer<typeof automationExecutionSchema>;

export interface ProcessMetrics {
  totalExecutions: number;
  successfulExecutions: number;
  failedExecutions: number;
  averageExecutionTime: number;
  handoversCreated: number;
  projectsCreated: number;
  notificationsSent: number;
}

export interface QualityMetrics {
  handoverCompletionTime: {
    average: number;
    median: number;
    min: number;
    max: number;
  };
  processBottlenecks: Array<{
    stage: string;
    averageTime: number;
    count: number;
  }>;
  teamPerformance: Array<{
    userId: string;
    userName: string;
    completedHandovers: number;
    averageTime: number;
    qualityScore: number;
  }>;
}

export class ProcessAutomationService {
  private handoverService: HandoverManagementService;
  private notificationService: HandoverNotificationService;
  private projectService: ProjectCreationService;

  constructor() {
    this.handoverService = new HandoverManagementService();
    this.notificationService = new HandoverNotificationService();
    this.projectService = new ProjectCreationService();
  }

  /**
   * Create automation trigger
   */
  async createAutomationTrigger(
    tenantId: string,
    triggerData: z.infer<typeof automationTriggerSchema>,
    createdBy: string
  ): Promise<AutomationTrigger> {
    const validatedData = automationTriggerSchema.parse(triggerData);

    const trigger = await prisma.automationTrigger.create({
      data: {
        tenantId,
        name: validatedData.name,
        description: validatedData.description,
        triggerType: validatedData.triggerType,
        triggerConditions: validatedData.triggerConditions,
        actions: validatedData.actions,
        isActive: validatedData.isActive,
        priority: validatedData.priority,
        createdById: createdBy
      }
    });

    return trigger as AutomationTrigger;
  }

  /**
   * Execute opportunity status change trigger
   */
  async executeOpportunityStatusTrigger(
    tenantId: string,
    opportunityId: string,
    oldStatus: string,
    newStatus: string,
    changedBy: string
  ): Promise<void> {
    try {
      // Get active triggers for opportunity status changes
      const triggers = await prisma.automationTrigger.findMany({
        where: {
          tenantId,
          triggerType: 'opportunity_status_change',
          isActive: true
        },
        orderBy: { priority: 'desc' }
      });

      for (const trigger of triggers) {
        const conditions = trigger.triggerConditions as any;
        
        // Check if trigger conditions match
        if (this.matchesOpportunityConditions(conditions, oldStatus, newStatus)) {
          await this.executeTriggerActions(
            tenantId,
            trigger.id,
            'opportunity',
            opportunityId,
            {
              oldStatus,
              newStatus,
              changedBy,
              timestamp: new Date().toISOString()
            }
          );
        }
      }
    } catch (error) {
      console.error('Error executing opportunity status trigger:', error);
      throw error;
    }
  }

  /**
   * Execute trigger actions
   */
  private async executeTriggerActions(
    tenantId: string,
    triggerId: string,
    entityType: string,
    entityId: string,
    triggerData: Record<string, any>
  ): Promise<void> {
    const execution = await prisma.automationExecution.create({
      data: {
        tenantId,
        triggerId,
        entityType,
        entityId,
        triggerData,
        status: 'pending'
      }
    });

    try {
      const trigger = await prisma.automationTrigger.findUnique({
        where: { id: triggerId }
      });

      if (!trigger) {
        throw new Error('Trigger not found');
      }

      const actions = trigger.actions as any[];
      const executedActions = [];

      for (const action of actions) {
        try {
          const result = await this.executeAction(tenantId, entityId, action, triggerData);
          executedActions.push({
            actionType: action.type,
            status: 'completed',
            result
          });
        } catch (actionError) {
          console.error(`Action ${action.type} failed:`, actionError);
          executedActions.push({
            actionType: action.type,
            status: 'failed',
            error: actionError instanceof Error ? actionError.message : 'Unknown error'
          });
        }
      }

      // Update execution record
      await prisma.automationExecution.update({
        where: { id: execution.id },
        data: {
          status: 'completed',
          executedActions,
          completedAt: new Date()
        }
      });

    } catch (error) {
      await prisma.automationExecution.update({
        where: { id: execution.id },
        data: {
          status: 'failed',
          error: error instanceof Error ? error.message : 'Unknown error',
          completedAt: new Date()
        }
      });
      throw error;
    }
  }

  /**
   * Execute individual action
   */
  private async executeAction(
    tenantId: string,
    entityId: string,
    action: any,
    triggerData: Record<string, any>
  ): Promise<any> {
    switch (action.type) {
      case 'create_handover':
        return await this.createAutomaticHandover(tenantId, entityId, action.config, triggerData);
      
      case 'send_notification':
        return await this.sendAutomationNotification(tenantId, entityId, action.config, triggerData);
      
      case 'create_project':
        return await this.createAutomaticProject(tenantId, entityId, action.config, triggerData);
      
      case 'assign_team':
        return await this.assignDeliveryTeam(tenantId, entityId, action.config, triggerData);
      
      default:
        throw new Error(`Unknown action type: ${action.type}`);
    }
  }

  /**
   * Create automatic handover when opportunity is closed won
   */
  private async createAutomaticHandover(
    tenantId: string,
    opportunityId: string,
    config: any,
    triggerData: Record<string, any>
  ): Promise<any> {
    // Get opportunity details
    const opportunity = await prisma.opportunity.findUnique({
      where: { id: opportunityId, tenantId },
      include: {
        contact: true,
        company: true,
        owner: true
      }
    });

    if (!opportunity) {
      throw new Error('Opportunity not found');
    }

    // Check if handover already exists
    const existingHandover = await prisma.projectHandover.findFirst({
      where: { opportunityId, tenantId }
    });

    if (existingHandover) {
      return { handoverId: existingHandover.id, created: false };
    }

    // Create handover with default template
    const handoverData = {
      opportunityId,
      templateId: config.defaultTemplateId,
      title: `Handover: ${opportunity.title}`,
      description: `Automated handover created for closed won opportunity: ${opportunity.title}`,
      priority: config.priority || 'high',
      salesRepId: opportunity.ownerId,
      deliveryManagerId: config.defaultDeliveryManagerId,
      assignedTeamIds: config.defaultTeamIds || []
    };

    const handover = await this.handoverService.createHandover(
      tenantId,
      handoverData,
      'system'
    );

    // Send notifications
    await this.notificationService.notifyHandoverCreated(
      tenantId,
      handover.id,
      'system'
    );

    return { handoverId: handover.id, created: true };
  }

  /**
   * Check if opportunity conditions match trigger
   */
  private matchesOpportunityConditions(
    conditions: any,
    oldStatus: string,
    newStatus: string
  ): boolean {
    if (conditions.fromStatus && conditions.fromStatus !== oldStatus) {
      return false;
    }
    
    if (conditions.toStatus && conditions.toStatus !== newStatus) {
      return false;
    }

    return true;
  }

  /**
   * Send automation notification
   */
  private async sendAutomationNotification(
    tenantId: string,
    entityId: string,
    config: any,
    triggerData: Record<string, any>
  ): Promise<any> {
    // Implementation for sending notifications
    return { notificationsSent: config.recipients?.length || 0 };
  }

  /**
   * Create automatic project
   */
  private async createAutomaticProject(
    tenantId: string,
    opportunityId: string,
    config: any,
    triggerData: Record<string, any>
  ): Promise<any> {
    return await this.projectService.autoCreateProjectFromOpportunity(
      tenantId,
      opportunityId,
      'system'
    );
  }

  /**
   * Assign delivery team
   */
  private async assignDeliveryTeam(
    tenantId: string,
    entityId: string,
    config: any,
    triggerData: Record<string, any>
  ): Promise<any> {
    // Implementation for team assignment
    return { teamAssigned: true, teamIds: config.teamIds || [] };
  }

  /**
   * Get process metrics
   */
  async getProcessMetrics(
    tenantId: string,
    dateRange?: { from: Date; to: Date }
  ): Promise<ProcessMetrics> {
    const where: any = { tenantId };
    
    if (dateRange) {
      where.createdAt = {
        gte: dateRange.from,
        lte: dateRange.to
      };
    }

    const executions = await prisma.automationExecution.findMany({
      where,
      include: {
        trigger: true
      }
    });

    const totalExecutions = executions.length;
    const successfulExecutions = executions.filter(e => e.status === 'completed').length;
    const failedExecutions = executions.filter(e => e.status === 'failed').length;

    // Calculate other metrics
    const handoversCreated = executions.filter(e => 
      e.executedActions?.some((a: any) => a.actionType === 'create_handover' && a.status === 'completed')
    ).length;

    const projectsCreated = executions.filter(e => 
      e.executedActions?.some((a: any) => a.actionType === 'create_project' && a.status === 'completed')
    ).length;

    return {
      totalExecutions,
      successfulExecutions,
      failedExecutions,
      averageExecutionTime: 0, // TODO: Calculate from execution times
      handoversCreated,
      projectsCreated,
      notificationsSent: 0 // TODO: Calculate from notification actions
    };
  }
}

export const processAutomationService = new ProcessAutomationService();
