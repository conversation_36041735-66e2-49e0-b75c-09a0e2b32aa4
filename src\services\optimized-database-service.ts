import { prisma } from '@/lib/prisma';
import { Prisma } from '@prisma/client';

/**
 * Optimized Database Service
 * Provides high-performance database operations with proper indexing and query optimization
 */
export class OptimizedDatabaseService {
  /**
   * Optimized dashboard metrics with single aggregation query
   */
  async getDashboardMetrics(tenantId: string, dateRange?: { start: Date; end: Date }) {
    const startDate = dateRange?.start || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000); // 30 days ago
    const endDate = dateRange?.end || new Date();

    const metrics = await prisma.$queryRaw<Array<{
      total_contacts: bigint;
      total_leads: bigint;
      total_opportunities: bigint;
      total_companies: bigint;
      contacts_this_period: bigint;
      leads_this_period: bigint;
      opportunities_this_period: bigint;
      companies_this_period: bigint;
      total_opportunity_value: number;
      average_deal_size: number;
      conversion_rate: number;
    }>>`
      SELECT 
        COUNT(DISTINCT c.id) as total_contacts,
        COUNT(DISTINCT l.id) FILTER (WHERE l.deleted_at IS NULL) as total_leads,
        COUNT(DISTINCT o.id) as total_opportunities,
        COUNT(DISTINCT comp.id) as total_companies,
        COUNT(DISTINCT c.id) FILTER (WHERE c.created_at >= ${startDate}) as contacts_this_period,
        COUNT(DISTINCT l.id) FILTER (WHERE l.created_at >= ${startDate} AND l.deleted_at IS NULL) as leads_this_period,
        COUNT(DISTINCT o.id) FILTER (WHERE o.created_at >= ${startDate}) as opportunities_this_period,
        COUNT(DISTINCT comp.id) FILTER (WHERE comp.created_at >= ${startDate}) as companies_this_period,
        COALESCE(SUM(o.value), 0) as total_opportunity_value,
        COALESCE(AVG(o.value), 0) as average_deal_size,
        CASE 
          WHEN COUNT(DISTINCT l.id) FILTER (WHERE l.deleted_at IS NULL) > 0 
          THEN (COUNT(DISTINCT l.id) FILTER (WHERE l.is_converted = true)::float / COUNT(DISTINCT l.id) FILTER (WHERE l.deleted_at IS NULL)::float) * 100
          ELSE 0 
        END as conversion_rate
      FROM tenants t
      LEFT JOIN contacts c ON t.id = c.tenant_id
      LEFT JOIN leads l ON t.id = l.tenant_id
      LEFT JOIN opportunities o ON t.id = o.tenant_id
      LEFT JOIN companies comp ON t.id = comp.tenant_id
      WHERE t.id = ${tenantId}
    `;

    const result = metrics[0];
    return {
      totalContacts: Number(result.total_contacts),
      totalLeads: Number(result.total_leads),
      totalOpportunities: Number(result.total_opportunities),
      totalCompanies: Number(result.total_companies),
      contactsThisPeriod: Number(result.contacts_this_period),
      leadsThisPeriod: Number(result.leads_this_period),
      opportunitiesThisPeriod: Number(result.opportunities_this_period),
      companiesThisPeriod: Number(result.companies_this_period),
      totalOpportunityValue: result.total_opportunity_value,
      averageDealSize: result.average_deal_size,
      conversionRate: result.conversion_rate
    };
  }

  /**
   * Cursor-based pagination for better performance with large datasets
   */
  async getPaginatedLeads(
    tenantId: string,
    options: {
      cursor?: string;
      limit?: number;
      filters?: {
        status?: string;
        ownerId?: string;
        leadSourceId?: string;
        priority?: string;
        search?: string;
      };
      sortBy?: 'created_at' | 'updated_at' | 'first_name' | 'last_name';
      sortOrder?: 'asc' | 'desc';
    } = {}
  ) {
    const {
      cursor,
      limit = 20,
      filters = {},
      sortBy = 'created_at',
      sortOrder = 'desc'
    } = options;

    // Build where clause
    const where: Prisma.LeadWhereInput = {
      tenantId,
      deletedAt: null,
      ...(cursor && { id: { gt: cursor } }),
      ...(filters.status && { status: filters.status }),
      ...(filters.ownerId && { ownerId: filters.ownerId }),
      ...(filters.leadSourceId && { leadSourceId: filters.leadSourceId }),
      ...(filters.priority && { priority: filters.priority }),
      ...(filters.search && {
        OR: [
          { title: { contains: filters.search, mode: 'insensitive' } },
          { description: { contains: filters.search, mode: 'insensitive' } },
          {
            contact: {
              OR: [
                { firstName: { contains: filters.search, mode: 'insensitive' } },
                { lastName: { contains: filters.search, mode: 'insensitive' } },
                { email: { contains: filters.search, mode: 'insensitive' } }
              ]
            }
          },
          {
            company: {
              name: { contains: filters.search, mode: 'insensitive' }
            }
          }
        ]
      })
    };

    // Use optimized query with proper includes
    const leads = await prisma.lead.findMany({
      where,
      include: {
        owner: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true
          }
        },
        company: {
          select: {
            id: true,
            name: true,
            industry: true
          }
        },
        leadSource: {
          select: {
            id: true,
            name: true,
            category: true
          }
        }
      },
      orderBy: { [sortBy]: sortOrder },
      take: limit + 1 // Take one extra to check if there's a next page
    });

    const hasNextPage = leads.length > limit;
    const items = hasNextPage ? leads.slice(0, -1) : leads;
    const nextCursor = hasNextPage ? items[items.length - 1].id : null;

    return {
      items,
      nextCursor,
      hasNextPage,
      totalCount: await this.getLeadsCount(tenantId, filters)
    };
  }

  /**
   * Optimized leads count with proper indexing
   */
  private async getLeadsCount(
    tenantId: string,
    filters: {
      status?: string;
      ownerId?: string;
      leadSourceId?: string;
      priority?: string;
      search?: string;
    } = {}
  ): Promise<number> {
    const where: Prisma.LeadWhereInput = {
      tenantId,
      deletedAt: null,
      ...(filters.status && { status: filters.status }),
      ...(filters.ownerId && { ownerId: filters.ownerId }),
      ...(filters.leadSourceId && { leadSourceId: filters.leadSourceId }),
      ...(filters.priority && { priority: filters.priority }),
      ...(filters.search && {
        OR: [
          { title: { contains: filters.search, mode: 'insensitive' } },
          { description: { contains: filters.search, mode: 'insensitive' } },
          {
            contact: {
              OR: [
                { firstName: { contains: filters.search, mode: 'insensitive' } },
                { lastName: { contains: filters.search, mode: 'insensitive' } },
                { email: { contains: filters.search, mode: 'insensitive' } }
              ]
            }
          },
          {
            company: {
              name: { contains: filters.search, mode: 'insensitive' }
            }
          }
        ]
      })
    };

    return await prisma.lead.count({ where });
  }

  /**
   * Full-text search using GIN indexes
   */
  async searchContacts(
    tenantId: string,
    searchTerm: string,
    options: {
      limit?: number;
      offset?: number;
    } = {}
  ) {
    const { limit = 20, offset = 0 } = options;

    // Use full-text search with GIN index
    const contacts = await prisma.$queryRaw<Array<{
      id: string;
      first_name: string;
      last_name: string;
      email: string;
      phone: string;
      company_name: string;
      rank: number;
    }>>`
      SELECT 
        c.id,
        c.first_name,
        c.last_name,
        c.email,
        c.phone,
        comp.name as company_name,
        ts_rank(
          to_tsvector('english', 
            coalesce(c.first_name, '') || ' ' || 
            coalesce(c.last_name, '') || ' ' || 
            coalesce(c.email, '') || ' ' || 
            coalesce(c.phone, '')
          ),
          plainto_tsquery('english', ${searchTerm})
        ) as rank
      FROM contacts c
      LEFT JOIN companies comp ON c.company_id = comp.id
      WHERE c.tenant_id = ${tenantId}
        AND to_tsvector('english', 
          coalesce(c.first_name, '') || ' ' || 
          coalesce(c.last_name, '') || ' ' || 
          coalesce(c.email, '') || ' ' || 
          coalesce(c.phone, '')
        ) @@ plainto_tsquery('english', ${searchTerm})
      ORDER BY rank DESC, c.created_at DESC
      LIMIT ${limit} OFFSET ${offset}
    `;

    return contacts;
  }

  /**
   * Batch operations for better performance
   */
  async batchCreateLeads(
    tenantId: string,
    leadsData: Array<{
      title: string;
      description?: string;
      contactId?: string;
      companyId?: string;
      leadSourceId?: string;
      ownerId?: string;
      status?: string;
      priority?: string;
      value?: number;
    }>
  ) {
    return await prisma.$transaction(async (tx) => {
      // Use createMany for better performance
      const createResult = await tx.lead.createMany({
        data: leadsData.map(data => ({
          ...data,
          tenantId,
          createdAt: new Date(),
          updatedAt: new Date()
        })),
        skipDuplicates: true
      });

      // Get created leads with relations in a single query
      const createdLeads = await tx.lead.findMany({
        where: {
          tenantId,
          title: { in: leadsData.map(d => d.title) }
        },
        include: {
          owner: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true
            }
          },
          leadSource: {
            select: {
              id: true,
              name: true,
              category: true
            }
          },
          contact: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true
            }
          },
          company: {
            select: {
              id: true,
              name: true,
              industry: true
            }
          }
        },
        orderBy: { createdAt: 'desc' },
        take: createResult.count
      });

      return {
        count: createResult.count,
        leads: createdLeads
      };
    });
  }

  /**
   * Optimized opportunity update with stage history
   */
  async updateOpportunityWithHistory(
    opportunityId: string,
    updateData: {
      title?: string;
      description?: string;
      value?: number;
      stage?: string;
      expectedCloseDate?: Date;
      probability?: number;
    },
    userId: string
  ) {
    return await prisma.$transaction(async (tx) => {
      // Get current opportunity for history tracking
      const current = await tx.opportunity.findUnique({
        where: { id: opportunityId },
        select: {
          id: true,
          tenantId: true,
          stage: true,
          value: true,
          probability: true
        }
      });

      if (!current) {
        throw new Error('Opportunity not found');
      }

      // Update opportunity
      const updated = await tx.opportunity.update({
        where: { id: opportunityId },
        data: {
          ...updateData,
          updatedAt: new Date()
        },
        include: {
          owner: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true
            }
          },
          company: {
            select: {
              id: true,
              name: true,
              industry: true
            }
          },
          contact: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true
            }
          }
        }
      });

      // Create stage history if stage changed
      if (updateData.stage && current.stage !== updateData.stage) {
        await tx.opportunityStageHistory.create({
          data: {
            tenantId: current.tenantId,
            opportunityId,
            fromStage: current.stage,
            toStage: updateData.stage,
            changedById: userId,
            changedAt: new Date(),
            notes: `Stage changed from ${current.stage || 'none'} to ${updateData.stage}`
          }
        });
      }

      return updated;
    });
  }

  /**
   * Optimized analytics for sales performance
   */
  async getSalesPerformanceMetrics(
    tenantId: string,
    dateRange: { start: Date; end: Date },
    userId?: string
  ) {
    const userFilter = userId ? Prisma.sql`AND o.owner_id = ${userId}` : Prisma.empty;

    const metrics = await prisma.$queryRaw<Array<{
      total_opportunities: bigint;
      won_opportunities: bigint;
      lost_opportunities: bigint;
      total_value: number;
      won_value: number;
      average_deal_size: number;
      win_rate: number;
      average_sales_cycle: number;
    }>>`
      SELECT
        COUNT(o.id) as total_opportunities,
        COUNT(o.id) FILTER (WHERE o.stage = 'won') as won_opportunities,
        COUNT(o.id) FILTER (WHERE o.stage = 'lost') as lost_opportunities,
        COALESCE(SUM(o.value), 0) as total_value,
        COALESCE(SUM(o.value) FILTER (WHERE o.stage = 'won'), 0) as won_value,
        COALESCE(AVG(o.value), 0) as average_deal_size,
        CASE
          WHEN COUNT(o.id) FILTER (WHERE o.stage IN ('won', 'lost')) > 0
          THEN (COUNT(o.id) FILTER (WHERE o.stage = 'won')::float / COUNT(o.id) FILTER (WHERE o.stage IN ('won', 'lost'))::float) * 100
          ELSE 0
        END as win_rate,
        COALESCE(AVG(EXTRACT(EPOCH FROM (o.updated_at - o.created_at)) / 86400) FILTER (WHERE o.stage IN ('won', 'lost')), 0) as average_sales_cycle
      FROM opportunities o
      WHERE o.tenant_id = ${tenantId}
        AND o.created_at >= ${dateRange.start}
        AND o.created_at <= ${dateRange.end}
        ${userFilter}
    `;

    const result = metrics[0];
    return {
      totalOpportunities: Number(result.total_opportunities),
      wonOpportunities: Number(result.won_opportunities),
      lostOpportunities: Number(result.lost_opportunities),
      totalValue: result.total_value,
      wonValue: result.won_value,
      averageDealSize: result.average_deal_size,
      winRate: result.win_rate,
      averageSalesCycle: result.average_sales_cycle
    };
  }
}
