import { NextRequest, NextResponse } from 'next/server';
import { withSuperAdminAuth } from '@/lib/tenant-context';
import { SuperAdminService } from '@/services/super-admin';
import { z } from 'zod';

const updateTenantSchema = z.object({
  name: z.string().min(2, 'Tenant name must be at least 2 characters').optional(),
  status: z.enum(['active', 'suspended', 'cancelled']).optional(),
  settings: z.record(z.any()).optional(),
  branding: z.object({
    logo: z.string().optional(),
    primaryColor: z.string().optional(),
    secondaryColor: z.string().optional()
  }).optional()
});

/**
 * @swagger
 * /api/admin/tenants:
 *   get:
 *     summary: Get all tenants (Super Admin only)
 *     description: Retrieve a paginated list of all tenants with details
 *     tags:
 *       - Super Admin - Tenants
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 20
 *         description: Number of items per page
 *     responses:
 *       200:
 *         description: List of tenants retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     tenants:
 *                       type: array
 *                       items:
 *                         type: object
 *                     pagination:
 *                       type: object
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Super Admin access required
 */
export const GET = withSuperAdminAuth(async (request: NextRequest) => {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');

    const superAdminService = new SuperAdminService();
    const result = await superAdminService.getAllTenants(page, limit);

    return NextResponse.json({
      success: true,
      data: result
    });

  } catch (error) {
    console.error('Get all tenants error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
});

/**
 * @swagger
 * /api/admin/tenants:
 *   put:
 *     summary: Update tenant (Super Admin only)
 *     description: Update tenant information
 *     tags:
 *       - Super Admin - Tenants
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               tenantId:
 *                 type: string
 *               name:
 *                 type: string
 *               status:
 *                 type: string
 *                 enum: [active, suspended, cancelled]
 *               settings:
 *                 type: object
 *               branding:
 *                 type: object
 *     responses:
 *       200:
 *         description: Tenant updated successfully
 *       400:
 *         description: Validation error
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Super Admin access required
 *       404:
 *         description: Tenant not found
 */
export const PUT = withSuperAdminAuth(async (request: NextRequest) => {
  try {
    const body = await request.json();
    const { tenantId, ...updateData } = body;

    if (!tenantId) {
      return NextResponse.json(
        { error: 'Tenant ID is required' },
        { status: 400 }
      );
    }

    // Validate the update data
    const validatedData = updateTenantSchema.parse(updateData);

    const superAdminService = new SuperAdminService();
    const updatedTenant = await superAdminService.updateTenant(tenantId, validatedData);

    return NextResponse.json({
      success: true,
      data: updatedTenant,
      message: 'Tenant updated successfully'
    });

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          error: 'Validation failed',
          details: error.errors.map(e => ({
            field: e.path.join('.'),
            message: e.message
          }))
        },
        { status: 400 }
      );
    }

    console.error('Update tenant error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
});

/**
 * @swagger
 * /api/admin/tenants:
 *   delete:
 *     summary: Delete tenant (Super Admin only)
 *     description: Permanently delete a tenant and all associated data
 *     tags:
 *       - Super Admin - Tenants
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               tenantId:
 *                 type: string
 *             required:
 *               - tenantId
 *     responses:
 *       200:
 *         description: Tenant deleted successfully
 *       400:
 *         description: Validation error
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Super Admin access required
 *       404:
 *         description: Tenant not found
 */
export const DELETE = withSuperAdminAuth(async (request: NextRequest) => {
  try {
    const { tenantId } = await request.json();

    if (!tenantId) {
      return NextResponse.json(
        { error: 'Tenant ID is required' },
        { status: 400 }
      );
    }

    // Note: This is a dangerous operation. In production, you might want to:
    // 1. Soft delete instead of hard delete
    // 2. Require additional confirmation
    // 3. Create a backup before deletion
    // 4. Send notifications to tenant users

    const superAdminService = new SuperAdminService();
    const tenant = await superAdminService.getTenantById(tenantId);

    if (!tenant) {
      return NextResponse.json(
        { error: 'Tenant not found' },
        { status: 404 }
      );
    }

    // For now, we'll just mark as cancelled instead of hard delete
    await superAdminService.updateTenant(tenantId, { status: 'cancelled' });

    return NextResponse.json({
      success: true,
      message: 'Tenant marked as cancelled successfully'
    });

  } catch (error) {
    console.error('Delete tenant error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
});
