import { prisma } from '@/lib/prisma';
import { permissionService } from './permission-service';
import { PermissionBroadcastService } from './permission-broadcast-service';
import { PermissionAction, PermissionResource, PermissionCategory, Permission, Role } from '@/types';

export interface CreateRoleData {
  name: string;
  description?: string;
  parentRoleId?: string;
  permissions: string[]; // Permission IDs or names
}

export interface UpdateRoleData {
  name?: string;
  description?: string;
  parentRoleId?: string;
  permissions?: string[];
}

export interface AssignRoleData {
  userId: string;
  roleId: string;
  expiresAt?: Date;
}

export class RoleManagementService {
  /**
   * Create a new role
   */
  async createRole(
    tenantId: string,
    roleData: CreateRoleData,
    createdBy: string
  ): Promise<Role> {
    return await prisma.$transaction(async (tx) => {
      // Validate parent role exists if specified
      if (roleData.parentRoleId) {
        const parentRole = await tx.role.findUnique({
          where: {
            id: roleData.parentRoleId,
            tenantId
          }
        });

        if (!parentRole) {
          throw new Error('Parent role not found');
        }
      }

      // Create the role
      const role = await tx.role.create({
        data: {
          tenantId,
          name: roleData.name,
          description: roleData.description,
          parentRoleId: roleData.parentRoleId,
          isSystemRole: false,
          createdById: createdBy
        }
      });

      // Assign permissions to the role
      if (roleData.permissions.length > 0) {
        await this.assignPermissionsToRole(
          role.id,
          tenantId,
          roleData.permissions,
          createdBy,
          tx
        );
      }

      // Log role creation
      await tx.auditLog.create({
        data: {
          tenantId,
          userId: createdBy,
          action: 'ROLE_CREATED',
          resourceType: 'role',
          resourceId: role.id,
          newValues: {
            name: role.name,
            description: role.description,
            parentRoleId: role.parentRoleId,
            permissions: roleData.permissions
          }
        }
      });

      return {
        id: role.id,
        name: role.name,
        description: role.description || '',
        isSystemRole: role.isSystemRole,
        parentRoleId: role.parentRoleId || undefined,
        permissions: [],
        childRoles: []
      };
    });
  }

  /**
   * Update an existing role
   */
  async updateRole(
    roleId: string,
    tenantId: string,
    updateData: UpdateRoleData,
    updatedBy: string,
    allowSystemRoleEdit: boolean = false
  ): Promise<Role> {
    return await prisma.$transaction(async (tx) => {
      // Get current role
      const currentRole = await tx.role.findUnique({
        where: { id: roleId, tenantId },
        include: {
          rolePermissions: {
            include: { permission: true }
          }
        }
      });

      if (!currentRole) {
        throw new Error('Role not found');
      }

      if (currentRole.isSystemRole && !allowSystemRoleEdit) {
        throw new Error('Cannot modify system roles');
      }

      // Validate parent role if specified
      if (updateData.parentRoleId) {
        const parentRole = await tx.role.findUnique({
          where: {
            id: updateData.parentRoleId,
            tenantId
          }
        });

        if (!parentRole) {
          throw new Error('Parent role not found');
        }

        // Prevent circular hierarchy
        if (await this.wouldCreateCircularHierarchy(roleId, updateData.parentRoleId, tenantId, tx)) {
          throw new Error('Cannot create circular role hierarchy');
        }
      }

      // Update role
      const updatedRole = await tx.role.update({
        where: { id: roleId },
        data: {
          name: updateData.name,
          description: updateData.description,
          parentRoleId: updateData.parentRoleId
        }
      });

      // Update permissions if specified
      if (updateData.permissions) {
        // Remove existing permissions
        await tx.rolePermission.deleteMany({
          where: { roleId, tenantId }
        });

        // Add new permissions
        if (updateData.permissions.length > 0) {
          await this.assignPermissionsToRole(
            roleId,
            tenantId,
            updateData.permissions,
            updatedBy,
            tx
          );
        }

        // Invalidate permission cache for all users with this role
        await this.invalidateRolePermissionCache(roleId, tenantId);
      }

      // Invalidate permission cache for all users with this role
      await this.invalidateRolePermissionCache(roleId, tenantId);

      // Log role update
      await tx.auditLog.create({
        data: {
          tenantId,
          userId: updatedBy,
          action: 'ROLE_UPDATED',
          resourceType: 'role',
          resourceId: roleId,
          oldValues: {
            name: currentRole.name,
            description: currentRole.description,
            parentRoleId: currentRole.parentRoleId
          },
          newValues: {
            name: updatedRole.name,
            description: updatedRole.description,
            parentRoleId: updatedRole.parentRoleId,
            permissions: updateData.permissions
          }
        }
      });

      return {
        id: updatedRole.id,
        name: updatedRole.name,
        description: updatedRole.description || '',
        isSystemRole: updatedRole.isSystemRole,
        parentRoleId: updatedRole.parentRoleId || undefined,
        permissions: [],
        childRoles: []
      };
    });
  }

  /**
   * Delete a role
   */
  async deleteRole(roleId: string, tenantId: string, deletedBy: string): Promise<void> {
    await prisma.$transaction(async (tx) => {
      const role = await tx.role.findUnique({
        where: { id: roleId, tenantId }
      });

      if (!role) {
        throw new Error('Role not found');
      }

      if (role.isSystemRole) {
        throw new Error('Cannot delete system roles');
      }

      // Check if role is assigned to any users
      const userCount = await tx.userRole.count({
        where: { roleId, tenantId }
      });

      if (userCount > 0) {
        throw new Error('Cannot delete role that is assigned to users');
      }

      // Check if role has child roles
      const childCount = await tx.role.count({
        where: { parentRoleId: roleId, tenantId }
      });

      if (childCount > 0) {
        throw new Error('Cannot delete role that has child roles');
      }

      // Delete role permissions
      await tx.rolePermission.deleteMany({
        where: { roleId, tenantId }
      });

      // Delete the role
      await tx.role.delete({
        where: { id: roleId }
      });

      // Log role deletion
      await tx.auditLog.create({
        data: {
          tenantId,
          userId: deletedBy,
          action: 'ROLE_DELETED',
          resourceType: 'role',
          resourceId: roleId,
          oldValues: {
            name: role.name,
            description: role.description
          }
        }
      });
    });
  }

  /**
   * Assign role to user
   */
  async assignRoleToUser(
    tenantId: string,
    assignData: AssignRoleData,
    assignedBy: string
  ): Promise<void> {
    await prisma.$transaction(async (tx) => {
      // Validate role exists
      const role = await tx.role.findUnique({
        where: { id: assignData.roleId, tenantId }
      });

      if (!role) {
        throw new Error('Role not found');
      }

      // Validate user exists in tenant
      const tenantUser = await tx.tenantUser.findUnique({
        where: {
          tenantId_userId: {
            tenantId,
            userId: assignData.userId
          }
        }
      });

      if (!tenantUser) {
        throw new Error('User not found in tenant');
      }

      // Check if assignment already exists
      const existingAssignment = await tx.userRole.findUnique({
        where: {
          tenantId_userId_roleId: {
            tenantId,
            userId: assignData.userId,
            roleId: assignData.roleId
          }
        }
      });

      if (existingAssignment) {
        throw new Error('Role already assigned to user');
      }

      // Create role assignment
      await tx.userRole.create({
        data: {
          tenantId,
          userId: assignData.userId,
          roleId: assignData.roleId,
          assignedBy,
          expiresAt: assignData.expiresAt
        }
      });

      // Invalidate user permission cache
      await permissionService.invalidateUserPermissions(assignData.userId, tenantId);

      // Log role assignment
      await tx.auditLog.create({
        data: {
          tenantId,
          userId: assignedBy,
          action: 'ROLE_ASSIGNED',
          resourceType: 'user_role',
          newValues: {
            userId: assignData.userId,
            roleId: assignData.roleId,
            roleName: role.name,
            expiresAt: assignData.expiresAt
          }
        }
      });

      // Broadcast real-time role update
      await PermissionBroadcastService.notifyRoleUpdate(
        assignData.userId,
        tenantId,
        `role_assigned:${role.name}`
      );
    });
  }

  /**
   * Remove role from user
   */
  async removeRoleFromUser(
    tenantId: string,
    userId: string,
    roleId: string,
    removedBy: string
  ): Promise<void> {
    await prisma.$transaction(async (tx) => {
      const userRole = await tx.userRole.findUnique({
        where: {
          tenantId_userId_roleId: {
            tenantId,
            userId,
            roleId
          }
        },
        include: { role: true }
      });

      if (!userRole) {
        throw new Error('Role assignment not found');
      }

      // Delete role assignment
      await tx.userRole.delete({
        where: {
          tenantId_userId_roleId: {
            tenantId,
            userId,
            roleId
          }
        }
      });

      // Invalidate user permission cache
      await permissionService.invalidateUserPermissions(userId, tenantId);

      // Log role removal
      await tx.auditLog.create({
        data: {
          tenantId,
          userId: removedBy,
          action: 'ROLE_REMOVED',
          resourceType: 'user_role',
          oldValues: {
            userId,
            roleId,
            roleName: userRole.role.name
          }
        }
      });

      // Broadcast real-time role update
      await PermissionBroadcastService.notifyRoleUpdate(
        userId,
        tenantId,
        `role_removed:${userRole.role.name}`
      );
    });
  }

  /**
   * Get all roles for a tenant
   */
  async getTenantRoles(tenantId: string): Promise<Role[]> {
    const roles = await prisma.role.findMany({
      where: { tenantId },
      include: {
        rolePermissions: {
          include: { permission: true }
        },
        childRoles: true
      },
      orderBy: { name: 'asc' }
    });

    return roles.map(role => ({
      id: role.id,
      name: role.name,
      description: role.description || '',
      isSystemRole: role.isSystemRole,
      parentRoleId: role.parentRoleId || undefined,
      permissions: role.rolePermissions.map(rp => ({
        id: rp.permission.id,
        name: rp.permission.name,
        resource: rp.permission.resource as PermissionResource,
        action: rp.permission.action as PermissionAction,
        category: rp.permission.category as PermissionCategory,
        description: rp.permission.description || ''
      })),
      childRoles: role.childRoles.map(child => ({
        id: child.id,
        name: child.name,
        description: child.description || '',
        isSystemRole: child.isSystemRole,
        parentRoleId: child.parentRoleId || undefined,
        permissions: [],
        childRoles: []
      }))
    }));
  }

  /**
   * Get roles with pagination and filtering
   */
  async getRoles(tenantId: string, options: {
    includePermissions?: boolean;
    includeUserCount?: boolean;
    page?: number;
    limit?: number;
    search?: string;
  } = {}): Promise<{
    roles: (Role & { userCount?: number })[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
    };
  }> {
    const {
      includePermissions = false,
      includeUserCount = false,
      page = 1,
      limit = 50,
      search
    } = options;

    const skip = (page - 1) * limit;

    const where = {
      tenantId,
      ...(search && {
        OR: [
          { name: { contains: search, mode: 'insensitive' as const } },
          { description: { contains: search, mode: 'insensitive' as const } }
        ]
      })
    };

    const [roles, total] = await Promise.all([
      prisma.role.findMany({
        where,
        include: {
          ...(includePermissions && {
            rolePermissions: {
              include: { permission: true }
            }
          }),
          childRoles: true,
          ...(includeUserCount && {
            _count: {
              select: {
                userRoles: true
              }
            }
          })
        },
        orderBy: [
          { isSystemRole: 'desc' },
          { name: 'asc' }
        ],
        skip,
        take: limit
      }),
      prisma.role.count({ where })
    ]);

    const formattedRoles = roles.map(role => ({
      id: role.id,
      name: role.name,
      description: role.description || '',
      isSystemRole: role.isSystemRole,
      parentRoleId: role.parentRoleId || undefined,
      permissions: includePermissions && role.rolePermissions ? role.rolePermissions.map(rp => ({
        id: rp.permission.id,
        name: rp.permission.name,
        resource: rp.permission.resource as PermissionResource,
        action: rp.permission.action as PermissionAction,
        category: rp.permission.category as PermissionCategory,
        description: rp.permission.description || ''
      })) : [],
      childRoles: role.childRoles.map(child => ({
        id: child.id,
        name: child.name,
        description: child.description || '',
        isSystemRole: child.isSystemRole,
        parentRoleId: child.parentRoleId || undefined,
        permissions: [],
        childRoles: []
      })),
      ...(includeUserCount && { userCount: (role as any)._count?.userRoles || 0 })
    }));

    return {
      roles: formattedRoles,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    };
  }

  /**
   * Get user roles
   */
  async getUserRoles(userId: string, tenantId: string): Promise<Role[]> {
    const userRoles = await prisma.userRole.findMany({
      where: {
        userId,
        tenantId,
        OR: [
          { expiresAt: null },
          { expiresAt: { gt: new Date() } }
        ]
      },
      include: {
        role: {
          include: {
            rolePermissions: {
              include: { permission: true }
            }
          }
        }
      }
    });

    return userRoles.map(ur => ({
      id: ur.role.id,
      name: ur.role.name,
      description: ur.role.description || '',
      isSystemRole: ur.role.isSystemRole,
      parentRoleId: ur.role.parentRoleId || undefined,
      permissions: ur.role.rolePermissions.map(rp => ({
        id: rp.permission.id,
        name: rp.permission.name,
        resource: rp.permission.resource as PermissionResource,
        action: rp.permission.action as PermissionAction,
        category: rp.permission.category as PermissionCategory,
        description: rp.permission.description || ''
      })),
      childRoles: []
    }));
  }

  /**
   * Get a specific role by ID
   */
  async getRoleById(roleId: string, tenantId: string): Promise<Role | null> {
    const role = await prisma.role.findUnique({
      where: { id: roleId, tenantId },
      include: {
        rolePermissions: {
          include: { permission: true }
        },
        childRoles: true,
        _count: {
          select: {
            userRoles: true
          }
        }
      }
    });

    if (!role) {
      return null;
    }

    return {
      id: role.id,
      name: role.name,
      description: role.description || '',
      isSystemRole: role.isSystemRole,
      parentRoleId: role.parentRoleId || undefined,
      permissions: role.rolePermissions.map(rp => ({
        id: rp.permission.id,
        name: rp.permission.name,
        resource: rp.permission.resource as PermissionResource,
        action: rp.permission.action as PermissionAction,
        category: rp.permission.category as PermissionCategory,
        description: rp.permission.description || ''
      })),
      childRoles: role.childRoles.map(child => ({
        id: child.id,
        name: child.name,
        description: child.description || '',
        isSystemRole: child.isSystemRole,
        parentRoleId: child.parentRoleId || undefined,
        permissions: [],
        childRoles: []
      })),
      userCount: role._count.userRoles
    };
  }

  /**
   * Get role permissions
   */
  async getRolePermissions(roleId: string, tenantId: string): Promise<Permission[]> {
    const role = await prisma.role.findUnique({
      where: { id: roleId, tenantId },
      include: {
        rolePermissions: {
          include: { permission: true }
        }
      }
    });

    if (!role) {
      throw new Error('Role not found');
    }

    return role.rolePermissions.map(rp => ({
      id: rp.permission.id,
      name: rp.permission.name,
      resource: rp.permission.resource as PermissionResource,
      action: rp.permission.action as PermissionAction,
      category: rp.permission.category as PermissionCategory,
      description: rp.permission.description || ''
    }));
  }

  /**
   * Get users assigned to a specific role
   */
  async getRoleUsers(roleId: string, tenantId: string, options: {
    page?: number;
    limit?: number;
    search?: string;
    includeExpired?: boolean;
  } = {}): Promise<{
    users: Array<{
      id: string;
      email: string;
      firstName: string;
      lastName: string;
      status: string;
      assignedAt: Date;
      expiresAt: Date | null;
      assignedBy: string | null;
      lastLoginAt: Date | null;
    }>;
    pagination: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
    };
  }> {
    const {
      page = 1,
      limit = 20,
      search,
      includeExpired = false
    } = options;

    const skip = (page - 1) * limit;

    // First verify the role exists and belongs to the tenant
    const role = await prisma.role.findUnique({
      where: { id: roleId, tenantId }
    });

    if (!role) {
      throw new Error('Role not found');
    }

    const where = {
      roleId,
      tenantId,
      ...(!includeExpired && {
        OR: [
          { expiresAt: null },
          { expiresAt: { gt: new Date() } }
        ]
      }),
      ...(search && {
        user: {
          OR: [
            { email: { contains: search, mode: 'insensitive' as const } },
            { firstName: { contains: search, mode: 'insensitive' as const } },
            { lastName: { contains: search, mode: 'insensitive' as const } }
          ]
        }
      })
    };

    const [userRoles, total] = await Promise.all([
      prisma.userRole.findMany({
        where,
        include: {
          user: {
            select: {
              id: true,
              email: true,
              firstName: true,
              lastName: true,
              status: true,
              lastLogin: true
            }
          },
          // Temporarily commented out to debug
          // assignedByUser: {
          //   select: {
          //     email: true,
          //     firstName: true,
          //     lastName: true
          //   }
          // }
        },
        orderBy: [
          { assignedAt: 'desc' }
        ],
        skip,
        take: limit
      }),
      prisma.userRole.count({ where })
    ]);

    const users = userRoles.map(ur => ({
      id: ur.user.id,
      email: ur.user.email,
      firstName: ur.user.firstName || '',
      lastName: ur.user.lastName || '',
      status: ur.user.status || 'active',
      assignedAt: ur.assignedAt,
      expiresAt: ur.expiresAt,
      assignedBy: null, // Temporarily disabled for debugging
        // ur.assignedByUser ?
        // `${ur.assignedByUser.firstName || ''} ${ur.assignedByUser.lastName || ''}`.trim() || ur.assignedByUser.email :
        // null,
      lastLoginAt: ur.user.lastLogin
    }));

    return {
      users,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    };
  }

  /**
   * Update role permissions
   */
  async updateRolePermissions(
    roleId: string,
    tenantId: string,
    permissions: string[],
    updatedBy: string,
    allowSystemRoleEdit: boolean = false
  ): Promise<void> {
    await prisma.$transaction(async (tx) => {
      const role = await tx.role.findUnique({
        where: { id: roleId, tenantId }
      });

      if (!role) {
        throw new Error('Role not found');
      }

      if (role.isSystemRole && !allowSystemRoleEdit) {
        throw new Error('Cannot modify permissions for system roles');
      }

      // Remove existing permissions
      await tx.rolePermission.deleteMany({
        where: { roleId, tenantId }
      });

      // Add new permissions
      if (permissions.length > 0) {
        await this.assignPermissionsToRole(
          roleId,
          tenantId,
          permissions,
          updatedBy,
          tx
        );
      }

      // Invalidate permission cache for all users with this role
      await this.invalidateRolePermissionCache(roleId, tenantId);

      // Log permission update
      await tx.auditLog.create({
        data: {
          tenantId,
          userId: updatedBy,
          action: role.isSystemRole ? 'SYSTEM_ROLE_PERMISSIONS_UPDATED' : 'ROLE_PERMISSIONS_UPDATED',
          resourceType: 'role',
          resourceId: roleId,
          newValues: {
            permissions,
            isSystemRole: role.isSystemRole
          }
        }
      });

      // Broadcast real-time permission updates to all users with this role
      await PermissionBroadcastService.notifyRolePermissionUpdate(
        roleId,
        tenantId,
        `role_permissions_updated:${role.name}`
      );
    });
  }

  /**
   * Update system role permissions (requires special permission)
   */
  async updateSystemRolePermissions(
    roleId: string,
    tenantId: string,
    permissions: string[],
    updatedBy: string
  ): Promise<void> {
    return this.updateRolePermissions(roleId, tenantId, permissions, updatedBy, true);
  }

  /**
   * Update system role (requires special permission)
   */
  async updateSystemRole(
    roleId: string,
    tenantId: string,
    updateData: UpdateRoleData,
    updatedBy: string
  ): Promise<Role> {
    return this.updateRole(roleId, tenantId, updateData, updatedBy, true);
  }

  // Private helper methods
  private async assignPermissionsToRole(
    roleId: string,
    tenantId: string,
    permissions: string[],
    grantedBy: string,
    tx: any
  ): Promise<void> {
    for (const permissionIdOrName of permissions) {
      // Try to find permission by ID first, then by name
      const permission = await tx.permission.findFirst({
        where: {
          tenantId,
          OR: [
            { id: permissionIdOrName },
            { name: permissionIdOrName }
          ]
        }
      });

      if (permission) {
        await tx.rolePermission.create({
          data: {
            tenantId,
            roleId,
            permissionId: permission.id,
            grantedBy
          }
        });
      }
    }
  }

  private async wouldCreateCircularHierarchy(
    roleId: string,
    parentRoleId: string,
    tenantId: string,
    tx: any
  ): Promise<boolean> {
    // Check if parentRoleId is a descendant of roleId
    let currentParentId = parentRoleId;
    const visited = new Set<string>();

    while (currentParentId && !visited.has(currentParentId)) {
      if (currentParentId === roleId) {
        return true; // Circular hierarchy detected
      }

      visited.add(currentParentId);

      const parentRole = await tx.role.findUnique({
        where: { id: currentParentId, tenantId }
      });

      currentParentId = parentRole?.parentRoleId || null;
    }

    return false;
  }

  private async invalidateRolePermissionCache(roleId: string, tenantId: string): Promise<void> {
    try {
      // Get all users with this role
      const userRoles = await prisma.userRole.findMany({
        where: { roleId, tenantId },
        select: { userId: true }
      });

      // Invalidate cache for each user
      for (const userRole of userRoles) {
        await permissionService.invalidateUserPermissions(userRole.userId, tenantId);
      }
    } catch (error) {
      console.error('Failed to invalidate role permission cache:', error);
    }
  }
}

// Export singleton instance
export const roleManagementService = new RoleManagementService();
