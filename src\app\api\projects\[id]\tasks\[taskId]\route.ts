import { NextRequest, NextResponse } from 'next/server';
import { withPermission, AuthenticatedRequest } from '@/lib/permission-middleware';
import { PermissionResource, PermissionAction } from '@/types';
import { TaskManagementService, updateTaskSchema } from '@/services/task-management';

const taskService = new TaskManagementService();

interface RouteParams {
  params: Promise<{
    id: string;
    taskId: string;
  }>;
}

/**
 * GET /api/projects/[id]/tasks/[taskId]
 * Get a specific task by ID
 */
export const GET = withPermission({
  resource: PermissionResource.PROJECTS,
  action: PermissionAction.READ,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest, context: RouteParams) => {
  try {
    const { taskId } = await context.params;
    
    const task = await taskService.getTaskById(taskId, req.tenantId);
    
    if (!task) {
      return NextResponse.json(
        {
          success: false,
          error: 'Task not found'
        },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: task,
      message: 'Task retrieved successfully'
    });
  } catch (error) {
    console.error('Error fetching task:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch task'
      },
      { status: 500 }
    );
  }
});

/**
 * PATCH /api/projects/[id]/tasks/[taskId]
 * Update a specific task
 */
export const PATCH = withPermission({
  resource: PermissionResource.PROJECTS,
  action: PermissionAction.UPDATE,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest, context: RouteParams) => {
  try {
    const { taskId } = await context.params;
    const body = await req.json();
    const validatedData = updateTaskSchema.parse(body);

    const task = await taskService.updateTask(
      taskId,
      req.tenantId,
      validatedData,
      req.userId
    );

    return NextResponse.json({
      success: true,
      data: task,
      message: 'Task updated successfully'
    });
  } catch (error) {
    console.error('Error updating task:', error);
    
    if (error instanceof Error && error.message.includes('not found')) {
      return NextResponse.json(
        {
          success: false,
          error: 'Task not found'
        },
        { status: 404 }
      );
    }

    return NextResponse.json(
      {
        success: false,
        error: 'Failed to update task'
      },
      { status: 500 }
    );
  }
});

/**
 * DELETE /api/projects/[id]/tasks/[taskId]
 * Delete a specific task
 */
export const DELETE = withPermission({
  resource: PermissionResource.PROJECTS,
  action: PermissionAction.UPDATE, // Deleting tasks requires project update permission
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest, context: RouteParams) => {
  try {
    const { taskId } = await context.params;
    
    await taskService.deleteTask(taskId, req.tenantId);

    return NextResponse.json({
      success: true,
      message: 'Task deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting task:', error);
    
    if (error instanceof Error && error.message.includes('not found')) {
      return NextResponse.json(
        {
          success: false,
          error: 'Task not found'
        },
        { status: 404 }
      );
    }

    return NextResponse.json(
      {
        success: false,
        error: 'Failed to delete task'
      },
      { status: 500 }
    );
  }
});
