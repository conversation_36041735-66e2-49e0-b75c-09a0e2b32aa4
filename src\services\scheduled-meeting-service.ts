import { z } from 'zod';
import { prisma } from '@/lib/prisma';
import { ScheduledMeeting, User, Lead, Contact } from '@prisma/client';
import { UnifiedNotificationService } from './unified-notification-service';

// Validation schemas
export const createScheduledMeetingSchema = z.object({
  leadId: z.string().optional().nullable(),
  contactId: z.string().optional().nullable(),
  assignedTo: z.string().min(1, 'Assigned user is required'),
  title: z.string().min(1, 'Title is required').max(255),
  description: z.string().optional().nullable(),
  meetingType: z.enum(['phone', 'video', 'in-person']).default('phone'),
  meetingLink: z.string().url('Invalid meeting link').optional().nullable(),
  location: z.string().optional().nullable(),
  scheduledAt: z.string().datetime('Invalid date format'),
  duration: z.number().min(5).max(480).default(30), // 5 minutes to 8 hours
  timezone: z.string().default('UTC'),
  followUpRequired: z.boolean().default(false),
  nextMeetingDate: z.string().datetime().optional().nullable(),
});

export const updateScheduledMeetingSchema = z.object({
  leadId: z.string().optional().nullable(),
  contactId: z.string().optional().nullable(),
  assignedTo: z.string().optional(),
  title: z.string().min(1, 'Title is required').max(255).optional(),
  description: z.string().optional().nullable(),
  meetingType: z.enum(['phone', 'video', 'in-person']).optional(),
  meetingLink: z.string().url('Invalid meeting link').optional().nullable(),
  location: z.string().optional().nullable(),
  scheduledAt: z.union([z.string().datetime('Invalid date format'), z.date()]).optional(),
  duration: z.number().min(5).max(480).optional(),
  timezone: z.string().optional(),
  status: z.enum(['scheduled', 'completed', 'cancelled', 'rescheduled']).optional(),
  completedAt: z.union([z.string().datetime(), z.date()]).optional().nullable(),
  meetingNotes: z.string().optional().nullable(),
  followUpRequired: z.boolean().optional(),
  nextMeetingDate: z.union([z.string().datetime(), z.date()]).optional().nullable(),
  invitationSent: z.boolean().optional(),
});

export const getScheduledMeetingsQuerySchema = z.object({
  leadId: z.string().optional(),
  contactId: z.string().optional(),
  assignedTo: z.string().optional(),
  status: z.enum(['scheduled', 'completed', 'cancelled', 'rescheduled']).optional(),
  meetingType: z.enum(['phone', 'video', 'in-person']).optional(),
  dateFrom: z.string().datetime().optional(),
  dateTo: z.string().datetime().optional(),
  page: z.coerce.number().min(1).default(1).optional(),
  limit: z.coerce.number().min(1).max(100).default(10).optional(),
  sortBy: z.enum(['scheduledAt', 'createdAt', 'title']).default('scheduledAt'),
  sortOrder: z.enum(['asc', 'desc']).default('asc'),
});

// Types
export type CreateScheduledMeetingData = z.infer<typeof createScheduledMeetingSchema>;
export type UpdateScheduledMeetingData = z.infer<typeof updateScheduledMeetingSchema>;
export type GetScheduledMeetingsQuery = z.infer<typeof getScheduledMeetingsQuerySchema>;

export interface ScheduledMeetingWithRelations extends ScheduledMeeting {
  lead?: Lead & {
    contact?: Contact;
  };
  contact?: Contact;
  scheduledByUser: User;
  assignedToUser: User;
}

export interface GetScheduledMeetingsResult {
  meetings: ScheduledMeetingWithRelations[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export class ScheduledMeetingService {
  private notificationService = new UnifiedNotificationService();

  /**
   * Create a new scheduled meeting
   */
  async createScheduledMeeting(
    tenantId: string,
    data: CreateScheduledMeetingData,
    scheduledBy: string
  ): Promise<ScheduledMeetingWithRelations> {
    const validatedData = createScheduledMeetingSchema.parse(data);

    // Verify that either leadId or contactId is provided
    if (!validatedData.leadId && !validatedData.contactId) {
      throw new Error('Either leadId or contactId must be provided');
    }

    // Validate meeting type specific fields
    if (validatedData.meetingType === 'video' && !validatedData.meetingLink) {
      throw new Error('Meeting link is required for video meetings');
    }

    if (validatedData.meetingType === 'in-person' && !validatedData.location) {
      throw new Error('Location is required for in-person meetings');
    }

    // Verify related entities exist and belong to tenant
    if (validatedData.leadId) {
      await this.verifyLeadExists(validatedData.leadId, tenantId);
    }

    if (validatedData.contactId) {
      await this.verifyContactExists(validatedData.contactId, tenantId);
    }

    // Verify assigned user exists and belongs to tenant
    await this.verifyUserBelongsToTenant(validatedData.assignedTo, tenantId);

    const scheduledMeeting = await prisma.scheduledMeeting.create({
      data: {
        ...validatedData,
        scheduledAt: new Date(validatedData.scheduledAt),
        nextMeetingDate: validatedData.nextMeetingDate ? new Date(validatedData.nextMeetingDate) : null,
        tenantId,
        scheduledBy,
      },
      include: this.getIncludeOptions(),
    });

    // Trigger meeting scheduled notification
    await this.triggerMeetingScheduledNotification(scheduledMeeting as ScheduledMeetingWithRelations, tenantId);

    return scheduledMeeting as ScheduledMeetingWithRelations;
  }

  /**
   * Get scheduled meetings with filtering and pagination
   */
  async getScheduledMeetings(
    tenantId: string,
    query: GetScheduledMeetingsQuery
  ): Promise<GetScheduledMeetingsResult> {
    const validatedQuery = getScheduledMeetingsQuerySchema.parse(query);
    const { page = 1, limit = 10, sortBy = 'scheduledAt', sortOrder = 'asc' } = validatedQuery;

    const where: any = { tenantId };

    if (validatedQuery.leadId) {
      where.leadId = validatedQuery.leadId;
    }

    if (validatedQuery.contactId) {
      where.contactId = validatedQuery.contactId;
    }

    if (validatedQuery.assignedTo) {
      where.assignedTo = validatedQuery.assignedTo;
    }

    if (validatedQuery.status) {
      where.status = validatedQuery.status;
    }

    if (validatedQuery.meetingType) {
      where.meetingType = validatedQuery.meetingType;
    }

    if (validatedQuery.dateFrom || validatedQuery.dateTo) {
      where.scheduledAt = {};
      if (validatedQuery.dateFrom) {
        where.scheduledAt.gte = new Date(validatedQuery.dateFrom);
      }
      if (validatedQuery.dateTo) {
        where.scheduledAt.lte = new Date(validatedQuery.dateTo);
      }
    }

    const [meetings, total] = await Promise.all([
      prisma.scheduledMeeting.findMany({
        where,
        include: this.getIncludeOptions(),
        orderBy: { [sortBy]: sortOrder },
        skip: (page - 1) * limit,
        take: limit,
      }),
      prisma.scheduledMeeting.count({ where }),
    ]);

    return {
      meetings: meetings as ScheduledMeetingWithRelations[],
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  /**
   * Get a single scheduled meeting by ID
   */
  async getScheduledMeeting(
    meetingId: string,
    tenantId: string
  ): Promise<ScheduledMeetingWithRelations | null> {
    const meeting = await prisma.scheduledMeeting.findFirst({
      where: {
        id: meetingId,
        tenantId,
      },
      include: this.getIncludeOptions(),
    });

    return meeting as ScheduledMeetingWithRelations | null;
  }

  /**
   * Update a scheduled meeting
   */
  async updateScheduledMeeting(
    meetingId: string,
    tenantId: string,
    data: UpdateScheduledMeetingData
  ): Promise<ScheduledMeetingWithRelations> {
    const validatedData = updateScheduledMeetingSchema.parse(data);

    // Verify meeting exists and belongs to tenant
    const existingMeeting = await this.getScheduledMeeting(meetingId, tenantId);
    if (!existingMeeting) {
      throw new Error('Scheduled meeting not found');
    }

    // Validate meeting type specific fields if meetingType is being updated
    if (validatedData.meetingType) {
      if (validatedData.meetingType === 'video' && !validatedData.meetingLink && !existingMeeting.meetingLink) {
        throw new Error('Meeting link is required for video meetings');
      }
      if (validatedData.meetingType === 'in-person' && !validatedData.location && !existingMeeting.location) {
        throw new Error('Location is required for in-person meetings');
      }
    }

    // Verify related entities if they're being updated
    if (validatedData.leadId) {
      await this.verifyLeadExists(validatedData.leadId, tenantId);
    }

    if (validatedData.contactId) {
      await this.verifyContactExists(validatedData.contactId, tenantId);
    }

    if (validatedData.assignedTo) {
      await this.verifyUserBelongsToTenant(validatedData.assignedTo, tenantId);
    }

    const updateData: any = { ...validatedData };
    if (validatedData.scheduledAt) {
      updateData.scheduledAt = new Date(validatedData.scheduledAt);
    }
    if (validatedData.completedAt) {
      updateData.completedAt = new Date(validatedData.completedAt);
    }
    if (validatedData.nextMeetingDate) {
      updateData.nextMeetingDate = new Date(validatedData.nextMeetingDate);
    }

    const updatedMeeting = await prisma.scheduledMeeting.update({
      where: {
        id: meetingId,
        tenantId,
      },
      data: updateData,
      include: this.getIncludeOptions(),
    });

    // Trigger meeting update notifications
    await this.handleMeetingUpdateNotifications(
      updatedMeeting as ScheduledMeetingWithRelations,
      existingMeeting,
      tenantId
    );

    return updatedMeeting as ScheduledMeetingWithRelations;
  }

  /**
   * Delete a scheduled meeting
   */
  async deleteScheduledMeeting(meetingId: string, tenantId: string): Promise<void> {
    const meeting = await this.getScheduledMeeting(meetingId, tenantId);
    if (!meeting) {
      throw new Error('Scheduled meeting not found');
    }

    await prisma.scheduledMeeting.delete({
      where: {
        id: meetingId,
        tenantId,
      },
    });
  }

  /**
   * Complete a meeting
   */
  async completeMeeting(
    meetingId: string,
    tenantId: string,
    meetingNotes?: string,
    followUpRequired: boolean = false,
    nextMeetingDate?: string
  ): Promise<ScheduledMeetingWithRelations> {
    const meeting = await this.getScheduledMeeting(meetingId, tenantId);
    if (!meeting) {
      throw new Error('Scheduled meeting not found');
    }

    if (meeting.status === 'completed') {
      throw new Error('Meeting is already completed');
    }

    const finalUpdateData = {
      status: 'completed' as const,
      completedAt: new Date(),
      meetingNotes: meetingNotes || null,
      followUpRequired,
      nextMeetingDate: nextMeetingDate ? new Date(nextMeetingDate) : null,
    };

    const updatedMeeting = await prisma.scheduledMeeting.update({
      where: {
        id: meetingId,
        tenantId,
      },
      data: finalUpdateData,
      include: this.getIncludeOptions(),
    });

    // Trigger meeting completion notification
    await this.triggerMeetingCompletedNotification(updatedMeeting as ScheduledMeetingWithRelations, tenantId);

    return updatedMeeting as ScheduledMeetingWithRelations;
  }

  /**
   * Private helper methods
   */
  private getIncludeOptions() {
    return {
      lead: {
        include: {
          contact: true,
        },
      },
      contact: true,
      scheduledByUser: {
        select: {
          id: true,
          firstName: true,
          lastName: true,
          email: true,
        },
      },
      assignedToUser: {
        select: {
          id: true,
          firstName: true,
          lastName: true,
          email: true,
        },
      },
    };
  }

  private async verifyLeadExists(leadId: string, tenantId: string): Promise<void> {
    const lead = await prisma.lead.findFirst({
      where: { id: leadId, tenantId },
    });

    if (!lead) {
      throw new Error('Lead not found or does not belong to tenant');
    }
  }

  private async verifyContactExists(contactId: string, tenantId: string): Promise<void> {
    const contact = await prisma.contact.findFirst({
      where: { id: contactId, tenantId },
    });

    if (!contact) {
      throw new Error('Contact not found or does not belong to tenant');
    }
  }

  private async verifyUserBelongsToTenant(userId: string, tenantId: string): Promise<void> {
    const tenantUser = await prisma.tenantUser.findFirst({
      where: { userId, tenantId },
    });

    if (!tenantUser) {
      throw new Error('User does not belong to tenant');
    }
  }

  /**
   * Trigger meeting scheduled notification
   */
  private async triggerMeetingScheduledNotification(
    meeting: ScheduledMeetingWithRelations,
    tenantId: string
  ): Promise<void> {
    try {
      const targetUsers = [];

      // Notify assigned user
      if (meeting.assignedTo) {
        targetUsers.push(meeting.assignedTo);
      }

      // Notify meeting organizer if different from assigned user
      if (meeting.scheduledBy && meeting.scheduledBy !== meeting.assignedTo) {
        targetUsers.push(meeting.scheduledBy);
      }

      // Get contact/lead information for context
      const contactName = meeting.contact ?
        `${meeting.contact.firstName} ${meeting.contact.lastName}` :
        meeting.lead?.contact ?
          `${meeting.lead.contact.firstName} ${meeting.lead.contact.lastName}` :
          'Unknown Contact';

      const entityType = meeting.leadId ? 'lead' : 'contact';
      const entityId = meeting.leadId || meeting.contactId;

      for (const userId of [...new Set(targetUsers)]) {
        await this.notificationService.createNotification({
          tenantId,
          userId,
          type: 'meeting_scheduled',
          category: 'communication',
          title: 'Meeting Scheduled',
          message: `Meeting "${meeting.title}" scheduled with ${contactName}`,
          data: {
            meetingId: meeting.id,
            meetingTitle: meeting.title,
            meetingType: meeting.meetingType,
            scheduledAt: meeting.scheduledAt.toISOString(),
            duration: meeting.duration,
            location: meeting.location,
            meetingLink: meeting.meetingLink,
            contactName,
            entityType,
            entityId,
            leadId: meeting.leadId,
            contactId: meeting.contactId,
            assignedTo: meeting.assignedTo,
            scheduledBy: meeting.scheduledBy
          },
          actionUrl: `/meetings/${meeting.id}`,
          actionLabel: 'View Meeting'
        });
      }
    } catch (error) {
      console.error('Failed to send meeting scheduled notification:', error);
      // Don't throw error to avoid breaking the main operation
    }
  }

  /**
   * Handle notifications for meeting updates
   */
  private async handleMeetingUpdateNotifications(
    updatedMeeting: ScheduledMeetingWithRelations,
    previousMeeting: ScheduledMeetingWithRelations,
    tenantId: string
  ): Promise<void> {
    try {
      // Check for time/date changes (rescheduling)
      if (updatedMeeting.scheduledAt.getTime() !== previousMeeting.scheduledAt.getTime()) {
        await this.triggerMeetingRescheduledNotification(
          updatedMeeting,
          previousMeeting.scheduledAt,
          tenantId
        );
      }

      // Check for assignment changes
      if (updatedMeeting.assignedTo !== previousMeeting.assignedTo) {
        await this.triggerMeetingReassignedNotification(
          updatedMeeting,
          tenantId,
          previousMeeting.assignedTo
        );
      }

      // Check for status changes
      if (updatedMeeting.status !== previousMeeting.status) {
        await this.triggerMeetingStatusChangeNotification(
          updatedMeeting,
          tenantId,
          previousMeeting.status
        );
      }
    } catch (error) {
      console.error('Failed to handle meeting update notifications:', error);
    }
  }

  /**
   * Trigger meeting rescheduled notification
   */
  private async triggerMeetingRescheduledNotification(
    meeting: ScheduledMeetingWithRelations,
    previousTime: Date,
    tenantId: string
  ): Promise<void> {
    try {
      const targetUsers = [meeting.assignedTo, meeting.scheduledBy].filter(Boolean);

      const contactName = meeting.contact ?
        `${meeting.contact.firstName} ${meeting.contact.lastName}` :
        meeting.lead?.contact ?
          `${meeting.lead.contact.firstName} ${meeting.lead.contact.lastName}` :
          'Unknown Contact';

      for (const userId of [...new Set(targetUsers)]) {
        await this.notificationService.createNotification({
          tenantId,
          userId,
          type: 'meeting_rescheduled',
          category: 'communication',
          title: 'Meeting Rescheduled',
          message: `Meeting "${meeting.title}" with ${contactName} has been rescheduled`,
          data: {
            meetingId: meeting.id,
            meetingTitle: meeting.title,
            previousTime: previousTime.toISOString(),
            newTime: meeting.scheduledAt.toISOString(),
            contactName,
            meetingType: meeting.meetingType,
            location: meeting.location,
            meetingLink: meeting.meetingLink
          },
          actionUrl: `/meetings/${meeting.id}`,
          actionLabel: 'View Meeting'
        });
      }
    } catch (error) {
      console.error('Failed to send meeting rescheduled notification:', error);
    }
  }

  /**
   * Trigger meeting reassigned notification
   */
  private async triggerMeetingReassignedNotification(
    meeting: ScheduledMeetingWithRelations,
    tenantId: string,
    previousAssignee?: string
  ): Promise<void> {
    try {
      // Notify new assignee
      if (meeting.assignedTo) {
        await this.notificationService.createNotification({
          tenantId,
          userId: meeting.assignedTo,
          type: 'meeting_assigned',
          category: 'communication',
          title: 'Meeting Assigned to You',
          message: `Meeting "${meeting.title}" has been assigned to you`,
          data: {
            meetingId: meeting.id,
            meetingTitle: meeting.title,
            scheduledAt: meeting.scheduledAt.toISOString(),
            meetingType: meeting.meetingType,
            previousAssignee
          },
          actionUrl: `/meetings/${meeting.id}`,
          actionLabel: 'View Meeting'
        });
      }

      // Notify previous assignee if different
      if (previousAssignee && previousAssignee !== meeting.assignedTo) {
        await this.notificationService.createNotification({
          tenantId,
          userId: previousAssignee,
          type: 'meeting_reassigned',
          category: 'communication',
          title: 'Meeting Reassigned',
          message: `Meeting "${meeting.title}" has been reassigned to another team member`,
          data: {
            meetingId: meeting.id,
            meetingTitle: meeting.title,
            newAssignee: meeting.assignedToUser ?
              `${meeting.assignedToUser.firstName} ${meeting.assignedToUser.lastName}` : 'Unknown'
          },
          actionUrl: `/meetings/${meeting.id}`,
          actionLabel: 'View Meeting'
        });
      }
    } catch (error) {
      console.error('Failed to send meeting reassigned notification:', error);
    }
  }

  /**
   * Trigger meeting status change notification
   */
  private async triggerMeetingStatusChangeNotification(
    meeting: ScheduledMeetingWithRelations,
    tenantId: string,
    previousStatus: string
  ): Promise<void> {
    try {
      const targetUsers = [meeting.assignedTo, meeting.scheduledBy].filter(Boolean);

      let notificationType = 'meeting_status_changed';
      let title = 'Meeting Status Updated';
      let message = `Meeting "${meeting.title}" status changed from ${previousStatus} to ${meeting.status}`;

      if (meeting.status === 'cancelled') {
        notificationType = 'meeting_cancelled';
        title = 'Meeting Cancelled';
        message = `Meeting "${meeting.title}" has been cancelled`;
      }

      for (const userId of [...new Set(targetUsers)]) {
        await this.notificationService.createNotification({
          tenantId,
          userId,
          type: notificationType,
          category: 'communication',
          title,
          message,
          data: {
            meetingId: meeting.id,
            meetingTitle: meeting.title,
            previousStatus,
            newStatus: meeting.status,
            scheduledAt: meeting.scheduledAt.toISOString()
          },
          actionUrl: `/meetings/${meeting.id}`,
          actionLabel: 'View Meeting'
        });
      }
    } catch (error) {
      console.error('Failed to send meeting status change notification:', error);
    }
  }

  /**
   * Trigger meeting completed notification
   */
  private async triggerMeetingCompletedNotification(
    meeting: ScheduledMeetingWithRelations,
    tenantId: string
  ): Promise<void> {
    try {
      const targetUsers = [meeting.assignedTo, meeting.scheduledBy].filter(Boolean);

      const contactName = meeting.contact ?
        `${meeting.contact.firstName} ${meeting.contact.lastName}` :
        meeting.lead?.contact ?
          `${meeting.lead.contact.firstName} ${meeting.lead.contact.lastName}` :
          'Unknown Contact';

      for (const userId of [...new Set(targetUsers)]) {
        await this.notificationService.createNotification({
          tenantId,
          userId,
          type: 'meeting_completed',
          category: 'communication',
          title: 'Meeting Completed',
          message: `Meeting "${meeting.title}" with ${contactName} has been completed`,
          data: {
            meetingId: meeting.id,
            meetingTitle: meeting.title,
            contactName,
            completedAt: meeting.completedAt?.toISOString(),
            meetingNotes: meeting.meetingNotes,
            followUpRequired: meeting.followUpRequired,
            nextMeetingDate: meeting.nextMeetingDate?.toISOString()
          },
          actionUrl: `/meetings/${meeting.id}`,
          actionLabel: 'View Meeting'
        });
      }
    } catch (error) {
      console.error('Failed to send meeting completed notification:', error);
    }
  }
}

// Export singleton instance
export const scheduledMeetingService = new ScheduledMeetingService();
