import { NextRequest, NextResponse } from 'next/server';
import { withPermission, AuthenticatedRequest } from '@/lib/permission-middleware';
import { PermissionResource, PermissionAction } from '@/types';
import { MilestoneService } from '@/services/milestone-management';
import { updateMilestoneSchema } from '@/services/milestone-management';

const milestoneService = new MilestoneService();

interface RouteParams {
  params: Promise<{
    id: string;
    milestoneId: string;
  }>;
}

/**
 * GET /api/projects/[id]/milestones/[milestoneId]
 * Get a specific milestone by ID
 */
export const GET = withPermission({
  resource: PermissionResource.PROJECTS,
  action: PermissionAction.READ,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest, context: RouteParams) => {
  try {
    const { milestoneId } = await context.params;
    
    const milestone = await milestoneService.getMilestoneById(milestoneId, req.tenantId);
    
    if (!milestone) {
      return NextResponse.json(
        {
          success: false,
          error: 'Milestone not found'
        },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: milestone,
      message: 'Milestone retrieved successfully'
    });
  } catch (error) {
    console.error('Error fetching milestone:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch milestone'
      },
      { status: 500 }
    );
  }
});

/**
 * PATCH /api/projects/[id]/milestones/[milestoneId]
 * Update a specific milestone
 */
export const PATCH = withPermission({
  resource: PermissionResource.PROJECTS,
  action: PermissionAction.UPDATE,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest, context: RouteParams) => {
  try {
    const { milestoneId } = await context.params;
    const body = await req.json();
    const validatedData = updateMilestoneSchema.parse(body);

    const milestone = await milestoneService.updateMilestone(
      milestoneId,
      req.tenantId,
      validatedData,
      req.userId
    );

    return NextResponse.json({
      success: true,
      data: milestone,
      message: 'Milestone updated successfully'
    });
  } catch (error) {
    console.error('Error updating milestone:', error);
    
    if (error instanceof Error && error.message.includes('not found')) {
      return NextResponse.json(
        {
          success: false,
          error: 'Milestone not found'
        },
        { status: 404 }
      );
    }

    return NextResponse.json(
      {
        success: false,
        error: 'Failed to update milestone'
      },
      { status: 500 }
    );
  }
});

/**
 * DELETE /api/projects/[id]/milestones/[milestoneId]
 * Delete a specific milestone
 */
export const DELETE = withPermission({
  resource: PermissionResource.PROJECTS,
  action: PermissionAction.UPDATE,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest, context: RouteParams) => {
  try {
    const { milestoneId } = await context.params;
    
    await milestoneService.deleteMilestone(milestoneId, req.tenantId);

    return NextResponse.json({
      success: true,
      message: 'Milestone deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting milestone:', error);
    
    if (error instanceof Error && error.message.includes('not found')) {
      return NextResponse.json(
        {
          success: false,
          error: 'Milestone not found'
        },
        { status: 404 }
      );
    }

    return NextResponse.json(
      {
        success: false,
        error: 'Failed to delete milestone'
      },
      { status: 500 }
    );
  }
});
