import { NextResponse } from 'next/server';
import { withPermission, AuthenticatedRequest } from '@/lib/permission-middleware';
import { DocumentTemplateManagementService } from '@/services/document-template-management';
import { PermissionResource, PermissionAction } from '@/types';

interface RouteParams {
  params: Promise<{ id: string }>;
}

/**
 * POST /api/document-templates/[id]/set-default
 * Set template as default
 */
export const POST = withPermission({
  resource: PermissionResource.PROPOSALS,
  action: PermissionAction.UPDATE,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest, { params }: RouteParams) => {
  try {
    const { id: templateId } = await params;

    const templateService = new DocumentTemplateManagementService();
    await templateService.setDefaultTemplate(templateId, req.tenantId);

    return NextResponse.json({
      success: true,
      message: 'Template set as default successfully'
    });

  } catch (error) {
    console.error('Set default template error:', error);
    
    if (error instanceof Error && error.message === 'Template not found') {
      return NextResponse.json(
        { error: 'Template not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to set template as default' },
      { status: 500 }
    );
  }
});
