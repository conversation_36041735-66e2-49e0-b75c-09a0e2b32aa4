import { prisma } from '@/lib/prisma';
import { Contact, Company } from '@prisma/client';
import { z } from 'zod';
import { UnifiedNotificationService } from './unified-notification-service';

// Validation schemas
export const createContactSchema = z.object({
  firstName: z.string().min(1, 'First name is required').max(100),
  lastName: z.string().min(1, 'Last name is required').max(100),
  email: z.string().email('Invalid email format').optional().nullable(),
  phone: z.string().max(50).optional().nullable(),
  jobTitle: z.string().max(100).optional().nullable(),
  website: z.string().max(500).optional().nullable().refine(
    (val) => !val || val === '' || /^https?:\/\/.+\..+/.test(val),
    { message: 'Invalid website URL format' }
  ),
  address: z.string().max(500).optional().nullable(),
  companyId: z.string().optional().nullable(),
  ownerId: z.string().optional().nullable(),
});

export const updateContactSchema = createContactSchema.partial();

export const createCompanySchema = z.object({
  name: z.string().min(1, 'Company name is required').max(255),
  website: z.string().max(500).optional().nullable().refine(
    (val) => !val || val === '' || /^https?:\/\/.+\..+/.test(val),
    { message: 'Invalid website URL format' }
  ),
  industry: z.string().max(100).optional().nullable(),
  size: z.string().max(50).optional().nullable(),
  ownerId: z.string().optional().nullable(),
});

export const updateCompanySchema = createCompanySchema.partial();

// Types
type UserSummary = {
  id: string;
  firstName: string | null;
  lastName: string | null;
  email: string;
};

export interface ContactWithRelations extends Contact {
  company?: Company | null;
  owner?: UserSummary | null;
  createdBy?: UserSummary | null;
}

export interface CompanyWithRelations extends Company {
  owner?: UserSummary | null;
  createdBy?: UserSummary | null;
  contacts?: Contact[];
  _count?: {
    contacts: number;
    leads: number;
    opportunities: number;
  };
}

export interface ContactFilters {
  search?: string;
  companyId?: string;
  ownerId?: string;
  hasEmail?: boolean;
  hasPhone?: boolean;
  page?: number;
  limit?: number;
  sortBy?: 'firstName' | 'lastName' | 'email' | 'createdAt' | 'updatedAt';
  sortOrder?: 'asc' | 'desc';
}

export interface CompanyFilters {
  search?: string;
  industry?: string;
  size?: string;
  ownerId?: string;
  page?: number;
  limit?: number;
  sortBy?: 'name' | 'industry' | 'size' | 'createdAt' | 'updatedAt';
  sortOrder?: 'asc' | 'desc';
}

export class ContactManagementService {
  private notificationService = new UnifiedNotificationService();

  /**
   * Create a new contact
   */
  async createContact(
    tenantId: string,
    contactData: z.infer<typeof createContactSchema>,
    createdBy: string
  ): Promise<ContactWithRelations> {
    const validatedData = createContactSchema.parse(contactData);

    // Verify company exists if provided
    if (validatedData.companyId) {
      const company = await prisma.company.findFirst({
        where: {
          id: validatedData.companyId,
          tenantId
        }
      });

      if (!company) {
        throw new Error('Company not found');
      }
    }

    // Verify owner exists if provided
    if (validatedData.ownerId) {
      const owner = await prisma.user.findFirst({
        where: {
          id: validatedData.ownerId,
          tenantUsers: {
            some: {
              tenantId,
              status: 'active'
            }
          }
        }
      });

      if (!owner) {
        throw new Error('Owner not found or not part of tenant');
      }
    }

    const contact = await prisma.contact.create({
      data: {
        ...validatedData,
        tenantId,
        createdById: createdBy,
        ownerId: validatedData.ownerId || createdBy
      },
      include: {
        company: true,
        owner: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true
          }
        },
        createdBy: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true
          }
        }
      }
    });

    // Trigger contact creation notification
    await this.triggerContactCreationNotification(contact, tenantId);

    return contact;
  }

  /**
   * Get contact by ID
   */
  async getContact(contactId: string, tenantId: string): Promise<ContactWithRelations | null> {
    return await prisma.contact.findFirst({
      where: {
        id: contactId,
        tenantId
      },
      include: {
        company: true,
        owner: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true
          }
        },
        createdBy: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true
          }
        }
      }
    });
  }

  /**
   * Get contacts with filtering and pagination
   */
  async getContacts(tenantId: string, filters: ContactFilters = {}): Promise<{
    contacts: ContactWithRelations[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  }> {
    const {
      search,
      companyId,
      ownerId,
      hasEmail,
      hasPhone,
      page = 1,
      limit = 20,
      sortBy = 'firstName',
      sortOrder = 'asc'
    } = filters;

    const skip = (page - 1) * limit;

    // Build where clause
    const where: Record<string, unknown> = {
      tenantId
    };

    if (search) {
      where.OR = [
        { firstName: { contains: search, mode: 'insensitive' } },
        { lastName: { contains: search, mode: 'insensitive' } },
        { email: { contains: search, mode: 'insensitive' } },
        { phone: { contains: search, mode: 'insensitive' } },
        {
          company: {
            name: { contains: search, mode: 'insensitive' }
          }
        }
      ];
    }

    if (companyId) {
      where.companyId = companyId;
    }

    if (ownerId) {
      where.ownerId = ownerId;
    }

    if (hasEmail !== undefined) {
      where.email = hasEmail ? { not: null } : null;
    }

    if (hasPhone !== undefined) {
      where.phone = hasPhone ? { not: null } : null;
    }

    // Get total count
    const total = await prisma.contact.count({ where });

    // Get contacts
    const contacts = await prisma.contact.findMany({
      where,
      include: {
        company: true,
        owner: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true
          }
        },
        createdBy: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true
          }
        }
      },
      orderBy: {
        [sortBy]: sortOrder
      },
      skip,
      take: limit
    });

    return {
      contacts,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit)
    };
  }

  /**
   * Update contact
   */
  async updateContact(
    contactId: string,
    tenantId: string,
    updateData: z.infer<typeof updateContactSchema>,
    updatedBy: string
  ): Promise<ContactWithRelations> {
    const validatedData = updateContactSchema.parse(updateData);

    // Verify contact exists
    const existingContact = await prisma.contact.findFirst({
      where: {
        id: contactId,
        tenantId
      }
    });

    if (!existingContact) {
      throw new Error('Contact not found');
    }

    // Verify company exists if provided
    if (validatedData.companyId) {
      const company = await prisma.company.findFirst({
        where: {
          id: validatedData.companyId,
          tenantId
        }
      });

      if (!company) {
        throw new Error('Company not found');
      }
    }

    // Verify owner exists if provided
    if (validatedData.ownerId) {
      const owner = await prisma.user.findFirst({
        where: {
          id: validatedData.ownerId,
          tenantUsers: {
            some: {
              tenantId,
              status: 'active'
            }
          }
        }
      });

      if (!owner) {
        throw new Error('Owner not found or not part of tenant');
      }
    }

    const contact = await prisma.contact.update({
      where: {
        id: contactId
      },
      data: validatedData,
      include: {
        company: true,
        owner: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true
          }
        },
        createdBy: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true
          }
        }
      }
    });

    return contact;
  }

  /**
   * Delete contact
   */
  async deleteContact(contactId: string, tenantId: string): Promise<void> {
    const contact = await prisma.contact.findFirst({
      where: {
        id: contactId,
        tenantId
      }
    });

    if (!contact) {
      throw new Error('Contact not found');
    }

    await prisma.contact.delete({
      where: {
        id: contactId
      }
    });
  }

  /**
   * Get contacts by company
   */
  async getContactsByCompany(companyId: string, tenantId: string): Promise<ContactWithRelations[]> {
    return await prisma.contact.findMany({
      where: {
        companyId,
        tenantId
      },
      include: {
        company: true,
        owner: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true
          }
        },
        createdBy: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true
          }
        }
      },
      orderBy: {
        firstName: 'asc'
      }
    });
  }

  /**
   * Search contacts by email
   */
  async findContactByEmail(email: string, tenantId: string): Promise<ContactWithRelations | null> {
    return await prisma.contact.findFirst({
      where: {
        email,
        tenantId
      },
      include: {
        company: true,
        owner: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true
          }
        },
        createdBy: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true
          }
        }
      }
    });
  }

  /**
   * Trigger contact creation notification
   */
  private async triggerContactCreationNotification(
    contact: ContactWithRelations,
    tenantId: string
  ): Promise<void> {
    try {
      const targetUsers = [];

      // Notify contact owner if assigned and different from creator
      if (contact.ownerId && contact.ownerId !== contact.createdById) {
        targetUsers.push(contact.ownerId);
      }

      // Notify sales team members and managers
      const salesTeam = await prisma.tenantUser.findMany({
        where: {
          tenantId,
          OR: [
            { role: { in: ['Sales Rep', 'Sales Manager', 'Manager', 'Admin'] } },
            { isTenantAdmin: true }
          ],
          status: 'active'
        },
        select: { userId: true }
      });

      targetUsers.push(...salesTeam.map(member => member.userId));

      // Remove duplicates and exclude creator if they're already in the list
      const uniqueTargetUsers = [...new Set(targetUsers)].filter(userId => userId !== contact.createdById);

      const contactName = `${contact.firstName} ${contact.lastName}`;
      const companyName = contact.company?.name;

      for (const userId of uniqueTargetUsers) {
        await this.notificationService.createNotification({
          tenantId,
          userId,
          type: 'contact_created',
          category: 'system',
          title: 'New Contact Created',
          message: companyName
            ? `New contact ${contactName} from ${companyName} has been added`
            : `New contact ${contactName} has been added`,
          data: {
            contactId: contact.id,
            contactName,
            contactEmail: contact.email,
            contactPhone: contact.phone,
            contactJobTitle: contact.jobTitle,
            companyId: contact.companyId,
            companyName,
            ownerId: contact.ownerId,
            ownerName: contact.owner ?
              `${contact.owner.firstName} ${contact.owner.lastName}` : null,
            createdBy: contact.createdById,
            createdByName: contact.createdBy ?
              `${contact.createdBy.firstName} ${contact.createdBy.lastName}` : null
          },
          actionUrl: `/contacts/${contact.id}`,
          actionLabel: 'View Contact'
        });
      }

      // Notify the contact owner specifically if assigned
      if (contact.ownerId && contact.ownerId !== contact.createdById) {
        await this.notificationService.createNotification({
          tenantId,
          userId: contact.ownerId,
          type: 'contact_assigned',
          category: 'system',
          title: 'Contact Assigned to You',
          message: `Contact ${contactName} has been assigned to you`,
          data: {
            contactId: contact.id,
            contactName,
            contactEmail: contact.email,
            contactPhone: contact.phone,
            companyName,
            assignedBy: contact.createdById
          },
          actionUrl: `/contacts/${contact.id}`,
          actionLabel: 'View Contact'
        });
      }

    } catch (error) {
      console.error('Failed to send contact creation notification:', error);
      // Don't throw error to avoid breaking the main operation
    }
  }
}
