'use client';

import React from 'react';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer
} from 'recharts';
import { BaseChart, BaseChartProps } from './base-chart';

interface RevenueData {
  month: string;
  revenue: number;
  subscriptions: number;
}

interface RevenueBarChartProps extends Omit<BaseChartProps, 'children'> {
  data: RevenueData[];
  showSubscriptions?: boolean;
}

export function RevenueBarChart({
  data,
  showSubscriptions = true,
  ...baseProps
}: RevenueBarChartProps) {
  const formatTooltipValue = (value: number, name: string) => {
    if (name === 'revenue') {
      return [`$${value.toLocaleString()}`, 'Revenue'];
    }
    return [`${value}`, 'Subscriptions'];
  };

  return (
    <BaseChart {...baseProps}>
      <BarChart data={data} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
        <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
        <XAxis 
          dataKey="month" 
          className="text-xs"
        />
        <YAxis 
          yAxisId="revenue"
          orientation="left"
          className="text-xs"
          tickFormatter={(value) => `$${(value / 1000).toFixed(0)}k`}
        />
        {showSubscriptions && (
          <YAxis 
            yAxisId="subscriptions"
            orientation="right"
            className="text-xs"
          />
        )}
        <Tooltip
          formatter={formatTooltipValue}
          contentStyle={{
            backgroundColor: 'hsl(var(--card))',
            border: '1px solid hsl(var(--border))',
            borderRadius: '6px',
            fontSize: '12px'
          }}
        />
        <Legend />
        
        <Bar
          yAxisId="revenue"
          dataKey="revenue"
          fill="hsl(var(--primary))"
          name="Revenue"
          radius={[4, 4, 0, 0]}
        />
        
        {showSubscriptions && (
          <Bar
            yAxisId="subscriptions"
            dataKey="subscriptions"
            fill="hsl(var(--chart-2))"
            name="Subscriptions"
            radius={[4, 4, 0, 0]}
          />
        )}
      </BarChart>
    </BaseChart>
  );
}
