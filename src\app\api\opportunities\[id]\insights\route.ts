import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { opportunityManagementService } from '@/services/opportunity-management';
import { aiOpportunityService } from '@/services/ai-opportunity-service';
import { hasPermission } from '@/lib/permissions';
import { PermissionResource, PermissionAction } from '@/types';

interface RouteParams {
  params: {
    id: string;
  };
}

/**
 * GET /api/opportunities/[id]/insights
 * Get AI insights for an opportunity
 */
export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user's current tenant
    const currentTenant = session.user.tenants?.[0];
    if (!currentTenant) {
      return NextResponse.json({ error: 'No tenant access' }, { status: 403 });
    }

    // Check permission
    const hasAccess = await hasPermission(
      PermissionResource.OPPORTUNITIES,
      PermissionAction.READ
    );

    if (!hasAccess) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Get the opportunity
    const opportunity = await opportunityManagementService.getOpportunity(
      params.id,
      currentTenant.id
    );

    if (!opportunity) {
      return NextResponse.json({ error: 'Opportunity not found' }, { status: 404 });
    }

    // Parse query parameters for insight type and locale
    const { searchParams } = new URL(request.url);
    const insightType = searchParams.get('type') || 'all';
    const locale = (searchParams.get('locale') || 'en') as 'en' | 'ar';

    const insights: any = {};

    // Generate different types of insights based on request
    if (insightType === 'all' || insightType === 'scoring') {
      insights.scoring = await aiOpportunityService.scoreOpportunity(
        opportunity,
        currentTenant.id,
        locale
      );
    }

    if (insightType === 'all' || insightType === 'forecast') {
      insights.forecast = await aiOpportunityService.predictDealOutcome(
        opportunity,
        currentTenant.id,
        locale
      );
    }

    if (insightType === 'all' || insightType === 'competition') {
      insights.competition = await aiOpportunityService.analyzeCompetition(
        opportunity,
        currentTenant.id,
        locale
      );
    }

    if (insightType === 'all' || insightType === 'actions') {
      insights.nextActions = await aiOpportunityService.getNextBestActions(
        opportunity,
        currentTenant.id,
        locale
      );
    }

    return NextResponse.json(insights);
  } catch (error) {
    console.error('Error generating opportunity insights:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
