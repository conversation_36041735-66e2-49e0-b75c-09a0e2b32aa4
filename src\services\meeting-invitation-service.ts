import { ScheduledMeetingWithRelations } from './scheduled-meeting-service';
import { smtpService } from './smtp-service';

export interface MeetingInvitationData {
  meeting: ScheduledMeetingWithRelations;
  organizerName: string;
  organizerEmail: string;
  companyName?: string;
}

export interface GeneratedInvitation {
  subject: string;
  htmlContent: string;
  textContent: string;
}

export class MeetingInvitationService {
  /**
   * Generate AI-powered meeting invitation content
   */
  async generateInvitationContent(data: MeetingInvitationData): Promise<GeneratedInvitation> {
    const { meeting, organizerName, organizerEmail, companyName } = data;
    
    // Format meeting date and time
    const meetingDate = new Date(meeting.scheduledAt);
    const formattedDate = meetingDate.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
    const formattedTime = meetingDate.toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      timeZoneName: 'short'
    });

    // Determine meeting details based on type
    let meetingDetails = '';
    let meetingLocation = '';
    
    switch (meeting.meetingType) {
      case 'video':
        meetingDetails = 'Video Conference';
        meetingLocation = meeting.meetingLink || 'Meeting link will be provided';
        break;
      case 'phone':
        meetingDetails = 'Phone Call';
        meetingLocation = 'Phone details will be shared separately';
        break;
      case 'in-person':
        meetingDetails = 'In-Person Meeting';
        meetingLocation = meeting.location || 'Location to be confirmed';
        break;
    }

    // Generate subject line
    const subject = `Meeting Invitation: ${meeting.title}`;

    // Generate HTML content
    const htmlContent = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Meeting Invitation</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
        .meeting-details { background-color: #e3f2fd; padding: 15px; border-radius: 6px; margin: 20px 0; }
        .detail-row { margin: 10px 0; }
        .label { font-weight: bold; color: #1976d2; }
        .footer { margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee; font-size: 14px; color: #666; }
        .button { display: inline-block; background-color: #1976d2; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="header">
        <h2>📅 Meeting Invitation</h2>
        <p>You're invited to a meeting with ${organizerName}${companyName ? ` from ${companyName}` : ''}.</p>
    </div>

    <div class="meeting-details">
        <h3>${meeting.title}</h3>
        
        <div class="detail-row">
            <span class="label">📅 Date:</span> ${formattedDate}
        </div>
        
        <div class="detail-row">
            <span class="label">🕐 Time:</span> ${formattedTime}
        </div>
        
        <div class="detail-row">
            <span class="label">⏱️ Duration:</span> ${meeting.duration} minutes
        </div>
        
        <div class="detail-row">
            <span class="label">📍 ${meetingDetails}:</span> ${meetingLocation}
        </div>
        
        ${meeting.description ? `
        <div class="detail-row">
            <span class="label">📝 Agenda:</span><br>
            ${meeting.description.replace(/\n/g, '<br>')}
        </div>
        ` : ''}
    </div>

    ${meeting.meetingType === 'video' && meeting.meetingLink ? `
    <div style="text-align: center; margin: 20px 0;">
        <a href="${meeting.meetingLink}" class="button">🎥 Join Video Meeting</a>
    </div>
    ` : ''}

    <p>We look forward to connecting with you and discussing how we can work together.</p>

    <p>If you need to reschedule or have any questions, please don't hesitate to reach out.</p>

    <div class="footer">
        <p>Best regards,<br>
        <strong>${organizerName}</strong><br>
        ${organizerEmail}${companyName ? `<br>${companyName}` : ''}</p>
        
        <p><em>This meeting was scheduled through our CRM system. Please save this information for your records.</em></p>
    </div>
</body>
</html>`;

    // Generate text content
    const textContent = `
MEETING INVITATION

You're invited to a meeting with ${organizerName}${companyName ? ` from ${companyName}` : ''}.

MEETING DETAILS:
Title: ${meeting.title}
Date: ${formattedDate}
Time: ${formattedTime}
Duration: ${meeting.duration} minutes
Type: ${meetingDetails}
${meeting.meetingType === 'video' ? `Meeting Link: ${meeting.meetingLink || 'Will be provided'}` : ''}
${meeting.meetingType === 'in-person' ? `Location: ${meetingLocation}` : ''}

${meeting.description ? `AGENDA:\n${meeting.description}\n` : ''}

We look forward to connecting with you and discussing how we can work together.

If you need to reschedule or have any questions, please don't hesitate to reach out.

Best regards,
${organizerName}
${organizerEmail}
${companyName || ''}

This meeting was scheduled through our CRM system. Please save this information for your records.
`;

    return {
      subject,
      htmlContent,
      textContent: textContent.trim()
    };
  }

  /**
   * Send meeting invitation email
   */
  async sendInvitation(
    data: MeetingInvitationData,
    recipientEmail: string,
    recipientName?: string
  ): Promise<{ success: boolean; messageId?: string; error?: string }> {
    try {
      const invitation = await this.generateInvitationContent(data);
      
      const result = await smtpService.sendEmail({
        to: recipientEmail,
        subject: invitation.subject,
        html: invitation.htmlContent,
        text: invitation.textContent,
        priority: 'normal',
        tags: ['meeting-invitation', 'automated'],
        metadata: {
          meetingId: data.meeting.id,
          meetingType: data.meeting.meetingType,
          organizerEmail: data.organizerEmail
        }
      });

      return {
        success: result.success,
        messageId: result.messageId
      };
    } catch (error) {
      console.error('Error sending meeting invitation:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  /**
   * Generate calendar event content (ICS format)
   */
  generateCalendarEvent(meeting: ScheduledMeetingWithRelations, organizerEmail: string): string {
    const startDate = new Date(meeting.scheduledAt);
    const endDate = new Date(startDate.getTime() + meeting.duration * 60000);
    
    // Format dates for ICS (YYYYMMDDTHHMMSSZ)
    const formatICSDate = (date: Date) => {
      return date.toISOString().replace(/[-:]/g, '').split('.')[0] + 'Z';
    };

    const icsContent = `BEGIN:VCALENDAR
VERSION:2.0
PRODID:-//CRM Platform//Meeting Scheduler//EN
BEGIN:VEVENT
UID:${meeting.id}@crm-platform.com
DTSTAMP:${formatICSDate(new Date())}
DTSTART:${formatICSDate(startDate)}
DTEND:${formatICSDate(endDate)}
SUMMARY:${meeting.title}
DESCRIPTION:${meeting.description || 'Meeting scheduled through CRM platform'}
ORGANIZER:mailto:${organizerEmail}
${meeting.meetingType === 'video' && meeting.meetingLink ? `LOCATION:${meeting.meetingLink}` : ''}
${meeting.meetingType === 'in-person' && meeting.location ? `LOCATION:${meeting.location}` : ''}
STATUS:CONFIRMED
SEQUENCE:0
END:VEVENT
END:VCALENDAR`;

    return icsContent;
  }
}

// Export singleton instance
export const meetingInvitationService = new MeetingInvitationService();
