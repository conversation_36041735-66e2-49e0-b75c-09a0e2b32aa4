import { NextRequest, NextResponse } from 'next/server';
import { withSuperAdminAuth } from '@/lib/tenant-context';
import { SuperAdminService } from '@/services/super-admin';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';

const updateUserSchema = z.object({
  userId: z.string(),
  firstName: z.string().optional(),
  lastName: z.string().optional(),
  status: z.enum(['active', 'suspended', 'inactive']).optional(),
  isPlatformAdmin: z.boolean().optional()
});

/**
 * @swagger
 * /api/admin/platform/users:
 *   get:
 *     summary: Get all users across all tenants (Super Admin only)
 *     description: Retrieve a paginated list of all users in the platform
 *     tags:
 *       - Super Admin - Platform Users
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 20
 *         description: Number of items per page
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search by email, first name, or last name
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [active, suspended, inactive]
 *         description: Filter by user status
 *     responses:
 *       200:
 *         description: Users retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     users:
 *                       type: array
 *                       items:
 *                         type: object
 *                     pagination:
 *                       type: object
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Super Admin access required
 */
export const GET = withSuperAdminAuth(async (request: NextRequest) => {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const search = searchParams.get('search');
    const status = searchParams.get('status');

    const superAdminService = new SuperAdminService();
    
    // If no filters, use the existing method
    if (!search && !status) {
      const result = await superAdminService.getAllUsers(page, limit);
      return NextResponse.json({
        success: true,
        data: result
      });
    }

    // Build where clause for filtering
    const whereClause: any = {};
    
    if (status) {
      whereClause.status = status;
    }

    if (search) {
      whereClause.OR = [
        { email: { contains: search, mode: 'insensitive' } },
        { firstName: { contains: search, mode: 'insensitive' } },
        { lastName: { contains: search, mode: 'insensitive' } }
      ];
    }

    const offset = (page - 1) * limit;
    
    const [users, total] = await Promise.all([
      prisma.user.findMany({
        where: whereClause,
        skip: offset,
        take: limit,
        include: {
          tenantUsers: {
            include: {
              tenant: true
            }
          }
        },
        orderBy: { createdAt: 'desc' }
      }),
      prisma.user.count({ where: whereClause })
    ]);

    const usersWithTenants = users.map(user => ({
      ...user,
      tenants: user.tenantUsers.map(tu => ({
        id: tu.tenant.id,
        name: tu.tenant.name,
        slug: tu.tenant.slug,
        role: tu.role,
        isTenantAdmin: tu.isTenantAdmin
      }))
    }));

    return NextResponse.json({
      success: true,
      data: {
        users: usersWithTenants,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit)
        }
      }
    });

  } catch (error) {
    console.error('Get all platform users error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
});

/**
 * @swagger
 * /api/admin/platform/users:
 *   put:
 *     summary: Update user (Super Admin only)
 *     description: Update user information across the platform
 *     tags:
 *       - Super Admin - Platform Users
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               userId:
 *                 type: string
 *               firstName:
 *                 type: string
 *               lastName:
 *                 type: string
 *               status:
 *                 type: string
 *                 enum: [active, suspended, inactive]
 *               isPlatformAdmin:
 *                 type: boolean
 *             required:
 *               - userId
 *     responses:
 *       200:
 *         description: User updated successfully
 *       400:
 *         description: Validation error
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Super Admin access required
 *       404:
 *         description: User not found
 */
export const PUT = withSuperAdminAuth(async (request: NextRequest) => {
  try {
    const body = await request.json();
    const validatedData = updateUserSchema.parse(body);
    const { userId, ...updateData } = validatedData;

    // Check if user exists
    const existingUser = await prisma.user.findUnique({
      where: { id: userId }
    });

    if (!existingUser) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    // Update user
    const updatedUser = await prisma.user.update({
      where: { id: userId },
      data: {
        ...updateData,
        updatedAt: new Date()
      },
      include: {
        tenantUsers: {
          include: {
            tenant: true
          }
        }
      }
    });

    const userWithTenants = {
      ...updatedUser,
      tenants: updatedUser.tenantUsers.map(tu => ({
        id: tu.tenant.id,
        name: tu.tenant.name,
        slug: tu.tenant.slug,
        role: tu.role,
        isTenantAdmin: tu.isTenantAdmin
      }))
    };

    return NextResponse.json({
      success: true,
      data: userWithTenants,
      message: 'User updated successfully'
    });

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          error: 'Validation failed',
          details: error.errors.map(e => ({
            field: e.path.join('.'),
            message: e.message
          }))
        },
        { status: 400 }
      );
    }

    console.error('Update platform user error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
});

/**
 * @swagger
 * /api/admin/platform/users:
 *   delete:
 *     summary: Delete user (Super Admin only)
 *     description: Permanently delete a user from the platform
 *     tags:
 *       - Super Admin - Platform Users
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               userId:
 *                 type: string
 *             required:
 *               - userId
 *     responses:
 *       200:
 *         description: User deleted successfully
 *       400:
 *         description: Validation error
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Super Admin access required
 *       404:
 *         description: User not found
 */
export const DELETE = withSuperAdminAuth(async (request: NextRequest) => {
  try {
    const { userId } = await request.json();

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      );
    }

    // Check if user exists
    const existingUser = await prisma.user.findUnique({
      where: { id: userId }
    });

    if (!existingUser) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    // Note: This will cascade delete all tenant relationships
    // In production, you might want to soft delete instead
    await prisma.user.delete({
      where: { id: userId }
    });

    return NextResponse.json({
      success: true,
      message: 'User deleted successfully'
    });

  } catch (error) {
    console.error('Delete platform user error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
});
