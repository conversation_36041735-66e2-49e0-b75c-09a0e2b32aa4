import { prisma, DatabasePerformanceMonitor } from '@/lib/prisma';
import { Lead, Contact, Company, Prisma } from '@prisma/client';
import { z } from 'zod';
import { UnifiedNotificationService } from './unified-notification-service';
import { cacheService } from '@/lib/redis';

// Validation schemas
export const createLeadSchema = z.object({
  title: z.string().min(1, 'Title is required').max(255, 'Title too long'),
  contactId: z.string().optional(),
  companyId: z.string().optional(),
  status: z.enum(['new', 'contacted', 'qualified', 'proposal', 'negotiation', 'closed_won', 'closed_lost']).default('new'),
  leadSourceId: z.string().optional(),
  score: z.number().int().min(0).max(100).default(0),
  ownerId: z.string().optional(),
  description: z.string().optional(),
  value: z.number().optional(),
  expectedCloseDate: z.string().optional().transform((val) => val ? new Date(val) : undefined),
  priority: z.enum(['low', 'medium', 'high', 'urgent']).default('medium'),
  tags: z.array(z.string()).default([]),
  customFields: z.record(z.any()).default({})
});

export const updateLeadSchema = createLeadSchema.partial();

export const getLeadsQuerySchema = z.object({
  page: z.string().optional().transform((val) => val ? parseInt(val, 10) : 1),
  limit: z.string().optional().transform((val) => val ? parseInt(val, 10) : 10),
  search: z.string().optional(),
  status: z.string().optional(),
  leadSourceId: z.string().optional(),
  ownerId: z.string().optional(),
  priority: z.string().optional(),
  sortBy: z.enum(['title', 'status', 'score', 'createdAt', 'updatedAt', 'expectedCloseDate']).default('createdAt'),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
  includeArchived: z.string().optional().transform((val) => val === 'true'),
  includeConverted: z.string().optional().transform((val) => val === 'true')
});

// Lead conversion validation schemas
export const leadConversionEligibilitySchema = z.object({
  leadIds: z.array(z.string()).min(1, 'At least one lead ID is required').max(100, 'Maximum 100 leads can be checked at once')
});

export const bulkConvertLeadsSchema = z.object({
  leadIds: z.array(z.string()).min(1, 'At least one lead ID is required').max(50, 'Maximum 50 leads can be converted at once'),
  conversionData: z.object({
    stage: z.string().optional(),
    priority: z.enum(['low', 'medium', 'high', 'urgent']).optional(),
    ownerId: z.string().optional(),
    notes: z.string().optional()
  }).optional()
});

export type CreateLeadData = z.infer<typeof createLeadSchema>;
export type UpdateLeadData = z.infer<typeof updateLeadSchema>;
export type GetLeadsQuery = z.infer<typeof getLeadsQuerySchema>;
export type LeadConversionEligibility = z.infer<typeof leadConversionEligibilitySchema>;
export type BulkConvertLeads = z.infer<typeof bulkConvertLeadsSchema>;

type UserSummary = {
  id: string;
  firstName: string | null;
  lastName: string | null;
  email: string;
  avatarUrl: string | null;
};

export interface LeadWithRelations extends Lead {
  contact?: Contact | null;
  company?: Company | null;
  leadSource?: {
    id: string;
    name: string;
    description: string | null;
    category: string;
    icon: string | null;
    color: string | null;
  } | null;
  owner?: UserSummary | null;
  createdBy?: UserSummary | null;
  convertedBy?: UserSummary | null;
  conversionHistory?: {
    id: string;
    opportunityId: string;
    conversionType: string;
    notes: string | null;
    convertedAt: Date;
    convertedBy: UserSummary;
  }[];
}

export interface LeadWithComprehensiveData extends LeadWithRelations {
  activities?: {
    id: string;
    type: string;
    subject: string | null;
    description: string | null;
    scheduledAt: Date | null;
    completedAt: Date | null;
    createdAt: Date;
    owner?: {
      id: string;
      firstName: string | null;
      lastName: string | null;
      email: string;
    } | null;
  }[];
  scheduledCalls?: {
    id: string;
    title: string;
    description: string | null;
    callType: string;
    scheduledAt: Date;
    duration: number;
    status: string;
    completedAt: Date | null;
    callNotes: string | null;
    followUpRequired: boolean;
    nextCallDate: Date | null;
    assignedToUser: {
      id: string;
      firstName: string | null;
      lastName: string | null;
      email: string;
    };
  }[];
  emailLogs?: {
    id: string;
    subject: string;
    status: string;
    sentAt: Date;
    deliveredAt: Date | null;
    openedAt: Date | null;
    clickedAt: Date | null;
    sentByUser: {
      id: string;
      firstName: string | null;
      lastName: string | null;
      email: string;
    };
  }[];
}

export interface LeadStats {
  total: number;
  byStatus: Record<string, number>;
  bySource: Record<string, number>;
  byPriority: Record<string, number>;
  averageScore: number;
  conversionRate: number;
  totalValue: number;
  conversionStats: {
    totalConverted: number;
    conversionsByMonth: Record<string, number>;
    averageTimeToConversion: number; // in days
  };
}

export interface LeadConversionEligibilityResult {
  leadId: string;
  eligible: boolean;
  reason?: string;
  qualificationScore?: number;
  missingRequirements?: string[];
}

export interface ConversionResult {
  success: boolean;
  leadId: string;
  opportunityId?: string;
  error?: string;
}

export class LeadManagementService {
  private notificationService = new UnifiedNotificationService();

  // Cache configuration
  private readonly CACHE_KEYS = {
    LEAD_LIST: 'lead_list',
    LEAD_DETAIL: 'lead_detail',
    LEAD_STATS: 'lead_stats',
    LEAD_SOURCES: 'lead_sources'
  };

  private readonly CACHE_TTLS = {
    LEAD_LIST: 300, // 5 minutes
    LEAD_DETAIL: 600, // 10 minutes
    LEAD_STATS: 900, // 15 minutes
    LEAD_SOURCES: 1800 // 30 minutes
  };

  /**
   * Get standard include options for lead queries
   */
  private getIncludeOptions() {
    return {
      contact: true,
      company: true,
      leadSource: {
        select: {
          id: true,
          name: true,
          description: true,
          category: true,
          icon: true,
          color: true
        }
      },
      owner: {
        select: {
          id: true,
          firstName: true,
          lastName: true,
          email: true,
          avatarUrl: true
        }
      },
      createdBy: {
        select: {
          id: true,
          firstName: true,
          lastName: true,
          email: true,
          avatarUrl: true
        }
      },
      convertedBy: {
        select: {
          id: true,
          firstName: true,
          lastName: true,
          email: true,
          avatarUrl: true
        }
      },
      conversionHistory: {
        select: {
          id: true,
          opportunityId: true,
          conversionType: true,
          notes: true,
          convertedAt: true,
          convertedBy: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
              avatarUrl: true
            }
          }
        },
        orderBy: {
          convertedAt: 'desc' as const
        }
      }
    };
  }

  /**
   * Get optimized include options for lead list queries (minimal data for performance)
   */
  private getOptimizedIncludeOptions() {
    return {
      contact: {
        select: {
          id: true,
          firstName: true,
          lastName: true,
          email: true
        }
      },
      company: {
        select: {
          id: true,
          name: true
        }
      },
      leadSource: {
        select: {
          id: true,
          name: true
        }
      },
      owner: {
        select: {
          id: true,
          firstName: true,
          lastName: true
        }
      }
      // Skip conversion history and other heavy relations for list view
    };
  }

  /**
   * Generate cache key for lead list queries
   */
  private generateLeadListCacheKey(tenantId: string, query: any): string {
    const queryString = JSON.stringify(query);
    return `${this.CACHE_KEYS.LEAD_LIST}:${tenantId}:${Buffer.from(queryString).toString('base64')}`;
  }

  /**
   * Generate cache key for lead detail
   */
  private generateLeadDetailCacheKey(leadId: string, tenantId: string): string {
    return `${this.CACHE_KEYS.LEAD_DETAIL}:${tenantId}:${leadId}`;
  }

  /**
   * Invalidate lead-related cache entries
   */
  private async invalidateLeadCache(tenantId: string, leadId?: string): Promise<void> {
    try {
      // Invalidate list cache for tenant
      const listPattern = `${this.CACHE_KEYS.LEAD_LIST}:${tenantId}:*`;
      await cacheService.deletePattern(listPattern);

      // Invalidate specific lead detail cache if provided
      if (leadId) {
        const detailKey = this.generateLeadDetailCacheKey(leadId, tenantId);
        await cacheService.delete(detailKey);
      }

      // Invalidate stats cache
      const statsKey = `${this.CACHE_KEYS.LEAD_STATS}:${tenantId}`;
      await cacheService.delete(statsKey);
    } catch (error) {
      console.error('Cache invalidation error:', error);
      // Don't throw - cache errors shouldn't break functionality
    }
  }

  /**
   * Create a new lead
   */
  async createLead(
    tenantId: string,
    leadData: CreateLeadData,
    createdBy: string
  ): Promise<LeadWithRelations> {
    return await prisma.$transaction(async (tx) => {
      // Validate contact and company belong to the same tenant if provided
      if (leadData.contactId) {
        const contact = await tx.contact.findUnique({
          where: { id: leadData.contactId, tenantId }
        });
        if (!contact) {
          throw new Error('Contact not found or access denied');
        }
      }

      if (leadData.companyId) {
        const company = await tx.company.findUnique({
          where: { id: leadData.companyId, tenantId }
        });
        if (!company) {
          throw new Error('Company not found or access denied');
        }
      }

      // Validate lead source belongs to the same tenant if provided
      if (leadData.leadSourceId) {
        const leadSource = await tx.leadSource.findUnique({
          where: { id: leadData.leadSourceId, tenantId }
        });
        if (!leadSource) {
          throw new Error('Lead source not found or access denied');
        }
      }

      // Validate owner belongs to the same tenant if provided
      if (leadData.ownerId) {
        const owner = await tx.user.findFirst({
          where: {
            id: leadData.ownerId,
            tenantUsers: {
              some: { tenantId }
            }
          }
        });
        if (!owner) {
          throw new Error('Owner not found or access denied');
        }
      }

      // Create the lead
      const lead = await tx.lead.create({
        data: {
          tenantId,
          title: leadData.title,
          contactId: leadData.contactId,
          companyId: leadData.companyId,
          status: leadData.status || 'new',
          leadSourceId: leadData.leadSourceId,
          score: leadData.score || 0,
          ownerId: leadData.ownerId,
          createdById: createdBy,
          description: leadData.description,
          value: leadData.value,
          expectedCloseDate: leadData.expectedCloseDate,
          priority: leadData.priority || 'medium',
          tags: leadData.tags || [],
          customFields: leadData.customFields || {}
        },
        include: this.getIncludeOptions()
      });

      // Trigger lead creation notification
      await this.triggerLeadCreationNotification(lead, tenantId);

      return lead;
    });
  }

  /**
   * Get leads with filtering and pagination - OPTIMIZED VERSION WITH CACHING
   */
  async getLeads(
    tenantId: string,
    query: GetLeadsQuery,
    userId?: string
  ): Promise<{
    leads: LeadWithRelations[]; // Using proper type instead of any[]
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  }> {
    const { page, limit, search, status, leadSourceId, ownerId, priority, sortBy, sortOrder, includeArchived, includeConverted } = query;
    const skip = (page - 1) * limit;

    console.log('LeadService.getLeads - Query params:', {
      page, limit, search, status, leadSourceId, ownerId, priority,
      sortBy, sortOrder, includeArchived, includeConverted, tenantId, userId
    });

    // Temporarily disable cache for debugging
    console.log('LeadService.getLeads - Skipping cache for debugging');

    // Check cache first (only for simple queries without search)
    // const cacheKey = this.generateLeadListCacheKey(tenantId, query);
    // if (!search && !includeArchived) {
    //   try {
    //     const cached = await cacheService.get<{
    //       leads: LeadWithRelations[];
    //       total: number;
    //       page: number;
    //       limit: number;
    //       totalPages: number;
    //     }>(cacheKey);
    //     if (cached) {
    //       console.log('LeadService.getLeads - Returning cached result:', cached.leads.length, 'leads');
    //       return cached;
    //     }
    //   } catch (error) {
    //     console.error('Cache read error:', error);
    //     // Continue with database query
    //   }
    // }

    // Build where clause
    const where: Prisma.LeadWhereInput = {
      tenantId,
      ...(includeArchived ? {} : { deletedAt: null }),
      ...(includeConverted ? {} : { isConverted: false })
    };

    console.log('LeadService.getLeads - Base where clause:', where);

    // Add search filter
    if (search) {
      where.OR = [
        { title: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } },
        {
          leadSource: {
            name: { contains: search, mode: 'insensitive' }
          }
        },
        {
          contact: {
            OR: [
              { firstName: { contains: search, mode: 'insensitive' } },
              { lastName: { contains: search, mode: 'insensitive' } },
              { email: { contains: search, mode: 'insensitive' } }
            ]
          }
        },
        {
          company: {
            name: { contains: search, mode: 'insensitive' }
          }
        }
      ];
    }

    // Add filters
    if (status) where.status = status;
    if (leadSourceId) where.leadSourceId = leadSourceId;
    if (ownerId) where.ownerId = ownerId;
    if (priority) where.priority = priority;

    console.log('LeadService.getLeads - Final where clause:', where);

    // Quick test: check if there are any leads at all for this tenant
    const allLeadsCount = await prisma.lead.count({ where: { tenantId } });
    console.log('LeadService.getLeads - Total leads in tenant:', allLeadsCount);

    // OPTIMIZATION: Use parallel queries for count and data
    const [total, leads] = await Promise.all([
      // Get total count with performance monitoring
      DatabasePerformanceMonitor.trackQuery(
        'lead_count',
        () => prisma.lead.count({ where }),
        { tenantId, filters: Object.keys(where).length }
      ),
      // Get leads with optimized relations
      DatabasePerformanceMonitor.trackQuery(
        'lead_list',
        () => prisma.lead.findMany({
          where,
          include: this.getOptimizedIncludeOptions(),
          orderBy: {
            [sortBy]: sortOrder
          },
          skip,
          take: limit
        }),
        { tenantId, page: Math.floor(skip / limit) + 1, limit }
      )
    ]);

    console.log('LeadService.getLeads - Database results:', {
      total,
      leadsCount: leads.length,
      skip,
      limit
    });

    const result = {
      leads,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit)
    };

    // Temporarily disable cache write for debugging
    console.log('LeadService.getLeads - Skipping cache write for debugging');

    // Cache the result (only for simple queries)
    // if (!search && !includeArchived) {
    //   try {
    //     await cacheService.set(cacheKey, result, {
    //       ttl: this.CACHE_TTLS.LEAD_LIST
    //     });
    //   } catch (error) {
    //     console.error('Cache write error:', error);
    //     // Don't throw - cache errors shouldn't break functionality
    //   }
    // }

    return result;
  }

  /**
   * Get leads with optimized cursor-based pagination
   */
  async getLeadsOptimized(
    tenantId: string,
    options: {
      cursor?: string;
      limit?: number;
      status?: string;
      leadSourceId?: string;
      ownerId?: string;
      priority?: string;
      search?: string;
      sortBy?: 'createdAt' | 'updatedAt' | 'firstName' | 'lastName' | 'score';
      sortOrder?: 'asc' | 'desc';
    } = {}
  ): Promise<{
    leads: LeadWithRelations[];
    nextCursor?: string | null;
    hasNextPage: boolean;
    totalCount: number;
  }> {
    const {
      cursor,
      limit = 20,
      status,
      leadSourceId,
      ownerId,
      priority,
      search,
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = options;

    // Build where clause
    const where: Prisma.LeadWhereInput = {
      tenantId,
      deletedAt: null,
      ...(cursor && { id: { gt: cursor } })
    };

    // Add filters
    if (status) where.status = status;
    if (leadSourceId) where.leadSourceId = leadSourceId;
    if (ownerId) where.ownerId = ownerId;
    if (priority) where.priority = priority;

    // Add search filter
    if (search) {
      where.OR = [
        { firstName: { contains: search, mode: 'insensitive' } },
        { lastName: { contains: search, mode: 'insensitive' } },
        { email: { contains: search, mode: 'insensitive' } },
        { companyName: { contains: search, mode: 'insensitive' } }
      ];
    }

    // Get leads with relations (take one extra to check if there's a next page)
    const leads = await DatabasePerformanceMonitor.trackQuery(
      'lead_list_optimized',
      () => prisma.lead.findMany({
        where,
        include: this.getIncludeOptions(),
        orderBy: {
          [sortBy]: sortOrder
        },
        take: limit + 1
      }),
      { tenantId, cursor: !!cursor, limit }
    );

    const hasNextPage = leads.length > limit;
    const items = hasNextPage ? leads.slice(0, -1) : leads;
    const nextCursor = hasNextPage ? items[items.length - 1].id : null;

    // Get total count for the current filters (without cursor)
    const totalCount = await DatabasePerformanceMonitor.trackQuery(
      'lead_count_optimized',
      () => prisma.lead.count({
        where: { ...where, id: undefined } // Remove cursor for total count
      }),
      { tenantId, withFilters: Object.keys(where).length > 2 }
    );

    return {
      leads: items,
      nextCursor,
      hasNextPage,
      totalCount
    };
  }

  /**
   * Full-text search leads using PostgreSQL GIN indexes
   */
  async searchLeads(
    tenantId: string,
    searchTerm: string,
    options: {
      limit?: number;
      offset?: number;
    } = {}
  ): Promise<Array<{
    id: string;
    firstName: string;
    lastName: string;
    email: string;
    companyName: string;
    rank: number;
  }>> {
    const { limit = 20, offset = 0 } = options;

    // Use full-text search with GIN index for better performance
    const leads = await prisma.$queryRaw<Array<{
      id: string;
      first_name: string;
      last_name: string;
      email: string;
      company_name: string;
      rank: number;
    }>>`
      SELECT
        l.id,
        l.first_name,
        l.last_name,
        l.email,
        l.company_name,
        ts_rank(
          to_tsvector('english',
            coalesce(l.first_name, '') || ' ' ||
            coalesce(l.last_name, '') || ' ' ||
            coalesce(l.email, '') || ' ' ||
            coalesce(l.company_name, '')
          ),
          plainto_tsquery('english', ${searchTerm})
        ) as rank
      FROM leads l
      WHERE l.tenant_id = ${tenantId}
        AND l.deleted_at IS NULL
        AND to_tsvector('english',
          coalesce(l.first_name, '') || ' ' ||
          coalesce(l.last_name, '') || ' ' ||
          coalesce(l.email, '') || ' ' ||
          coalesce(l.company_name, '')
        ) @@ plainto_tsquery('english', ${searchTerm})
      ORDER BY rank DESC, l.created_at DESC
      LIMIT ${limit} OFFSET ${offset}
    `;

    return leads.map(lead => ({
      id: lead.id,
      firstName: lead.first_name,
      lastName: lead.last_name,
      email: lead.email,
      companyName: lead.company_name,
      rank: lead.rank
    }));
  }

  /**
   * Get a single lead by ID
   */
  async getLead(leadId: string, tenantId: string): Promise<LeadWithRelations | null> {
    return await prisma.lead.findUnique({
      where: {
        id: leadId,
        tenantId,
        deletedAt: null
      },
      include: this.getIncludeOptions()
    });
  }

  /**
   * Get comprehensive lead data including all related activities, emails, and calls
   */
  async getLeadWithComprehensiveData(leadId: string, tenantId: string): Promise<LeadWithComprehensiveData | null> {
    // First get the basic lead data
    const lead = await this.getLead(leadId, tenantId);
    if (!lead) {
      return null;
    }

    // Fetch activities related to this lead
    const activities = await prisma.activity.findMany({
      where: {
        tenantId,
        OR: [
          { relatedToType: 'lead', relatedToId: leadId },
          { relatedToType: 'contact', relatedToId: lead.contactId || '' },
        ]
      },
      select: {
        id: true,
        type: true,
        subject: true,
        description: true,
        scheduledAt: true,
        completedAt: true,
        createdAt: true,
        owner: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true
          }
        }
      },
      orderBy: { createdAt: 'desc' },
      take: 50 // Limit to recent activities
    });

    // Fetch scheduled calls for this lead
    const scheduledCalls = await prisma.scheduledCall.findMany({
      where: {
        tenantId,
        OR: [
          { leadId: leadId },
          { contactId: lead.contactId || '' }
        ]
      },
      select: {
        id: true,
        title: true,
        description: true,
        callType: true,
        scheduledAt: true,
        duration: true,
        status: true,
        completedAt: true,
        callNotes: true,
        followUpRequired: true,
        nextCallDate: true,
        assignedToUser: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true
          }
        }
      },
      orderBy: { scheduledAt: 'desc' },
      take: 20 // Limit to recent calls
    });

    // Fetch email logs for this lead
    const emailLogs = await prisma.emailLog.findMany({
      where: {
        tenantId,
        OR: [
          { leadId: leadId },
          { contactId: lead.contactId || '' }
        ]
      },
      select: {
        id: true,
        subject: true,
        status: true,
        sentAt: true,
        deliveredAt: true,
        openedAt: true,
        clickedAt: true,
        sentByUser: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true
          }
        }
      },
      orderBy: { sentAt: 'desc' },
      take: 30 // Limit to recent emails
    });

    return {
      ...lead,
      activities,
      scheduledCalls,
      emailLogs
    };
  }

  /**
   * Update a lead
   */
  async updateLead(
    leadId: string,
    tenantId: string,
    leadData: UpdateLeadData
  ): Promise<LeadWithRelations> {
    return await prisma.$transaction(async (tx) => {
      // Check if lead exists and belongs to tenant
      const existingLead = await tx.lead.findUnique({
        where: { id: leadId, tenantId, deletedAt: null }
      });

      if (!existingLead) {
        throw new Error('Lead not found or access denied');
      }

      // Validate contact and company belong to the same tenant if provided
      if (leadData.contactId) {
        const contact = await tx.contact.findUnique({
          where: { id: leadData.contactId, tenantId }
        });
        if (!contact) {
          throw new Error('Contact not found or access denied');
        }
      }

      if (leadData.companyId) {
        const company = await tx.company.findUnique({
          where: { id: leadData.companyId, tenantId }
        });
        if (!company) {
          throw new Error('Company not found or access denied');
        }
      }

      // Validate lead source belongs to the same tenant if provided
      if (leadData.leadSourceId) {
        const leadSource = await tx.leadSource.findUnique({
          where: { id: leadData.leadSourceId, tenantId }
        });
        if (!leadSource) {
          throw new Error('Lead source not found or access denied');
        }
      }

      // Validate owner belongs to the same tenant if provided
      if (leadData.ownerId) {
        const owner = await tx.user.findFirst({
          where: {
            id: leadData.ownerId,
            tenantUsers: {
              some: { tenantId }
            }
          }
        });
        if (!owner) {
          throw new Error('Owner not found or access denied');
        }
      }

      // Update the lead
      const lead = await tx.lead.update({
        where: { id: leadId },
        data: {
          ...leadData,
          updatedAt: new Date()
        },
        include: this.getIncludeOptions()
      });

      // Trigger notifications for changes
      await this.handleLeadUpdateNotifications(lead, existingLead, tenantId);

      return lead;
    });
  }

  /**
   * Delete a lead (soft delete)
   */
  async deleteLead(leadId: string, tenantId: string): Promise<void> {
    const lead = await prisma.lead.findUnique({
      where: { id: leadId, tenantId, deletedAt: null }
    });

    if (!lead) {
      throw new Error('Lead not found or access denied');
    }

    await prisma.lead.update({
      where: { id: leadId },
      data: {
        deletedAt: new Date()
      }
    });
  }

  /**
   * Bulk delete leads
   */
  async bulkDeleteLeads(leadIds: string[], tenantId: string): Promise<number> {
    const result = await prisma.lead.updateMany({
      where: {
        id: { in: leadIds },
        tenantId,
        deletedAt: null
      },
      data: {
        deletedAt: new Date()
      }
    });

    return result.count;
  }



  /**
   * Get lead statistics
   */
  async getLeadStats(tenantId: string): Promise<LeadStats> {
    const [
      total,
      statusCounts,
      sourceCounts,
      priorityCounts,
      scoreStats,
      valueStats
    ] = await Promise.all([
      // Total leads
      prisma.lead.count({
        where: { tenantId, deletedAt: null }
      }),

      // Count by status
      prisma.lead.groupBy({
        by: ['status'],
        where: { tenantId, deletedAt: null },
        _count: { status: true }
      }),

      // Count by lead source
      prisma.lead.groupBy({
        by: ['leadSourceId'],
        where: { tenantId, deletedAt: null, leadSourceId: { not: null } },
        _count: { leadSourceId: true }
      }),

      // Count by priority
      prisma.lead.groupBy({
        by: ['priority'],
        where: { tenantId, deletedAt: null },
        _count: { priority: true }
      }),

      // Average score
      prisma.lead.aggregate({
        where: { tenantId, deletedAt: null },
        _avg: { score: true }
      }),

      // Total value and conversion stats
      prisma.lead.aggregate({
        where: { tenantId, deletedAt: null },
        _sum: { value: true },
        _count: {
          _all: true,
          status: true
        }
      })
    ]);

    // Calculate conversion rate (closed_won / total)
    const closedWonCount = statusCounts.find(s => s.status === 'closed_won')?._count.status || 0;
    const conversionRate = total > 0 ? (closedWonCount / total) * 100 : 0;

    // Get lead source names for the stats
    const leadSourceIds = sourceCounts.map(item => item.leadSourceId).filter(Boolean) as string[];
    const leadSources = await prisma.leadSource.findMany({
      where: { id: { in: leadSourceIds }, tenantId },
      select: { id: true, name: true }
    });

    const leadSourceMap = leadSources.reduce((acc, source) => {
      acc[source.id] = source.name;
      return acc;
    }, {} as Record<string, string>);

    // Get conversion statistics
    const [convertedCount, conversionsByMonth, avgConversionTime] = await Promise.all([
      // Total converted leads
      prisma.lead.count({
        where: { tenantId, deletedAt: null, isConverted: true }
      }),

      // Conversions by month (last 12 months)
      prisma.lead.groupBy({
        by: ['convertedAt'],
        where: {
          tenantId,
          deletedAt: null,
          isConverted: true,
          convertedAt: {
            gte: new Date(new Date().setMonth(new Date().getMonth() - 12))
          }
        },
        _count: { id: true }
      }),

      // Average time to conversion
      prisma.$queryRaw<{ avg_days: number }[]>`
        SELECT AVG(EXTRACT(DAY FROM (converted_at - created_at))) as avg_days
        FROM leads
        WHERE tenant_id = ${tenantId}
        AND deleted_at IS NULL
        AND is_converted = true
        AND converted_at IS NOT NULL
      `
    ]);

    // Process conversions by month
    const conversionsByMonthMap = conversionsByMonth.reduce((acc, item) => {
      if (item.convertedAt) {
        const monthKey = item.convertedAt.toISOString().substring(0, 7); // YYYY-MM
        acc[monthKey] = item._count.id;
      }
      return acc;
    }, {} as Record<string, number>);

    return {
      total,
      byStatus: statusCounts.reduce((acc, item) => {
        acc[item.status] = item._count.status;
        return acc;
      }, {} as Record<string, number>),
      bySource: sourceCounts.reduce((acc, item) => {
        const sourceName = item.leadSourceId ? leadSourceMap[item.leadSourceId] || 'Unknown Source' : 'No Source';
        acc[sourceName] = item._count.leadSourceId;
        return acc;
      }, {} as Record<string, number>),
      byPriority: priorityCounts.reduce((acc, item) => {
        acc[item.priority] = item._count.priority;
        return acc;
      }, {} as Record<string, number>),
      averageScore: Number(scoreStats._avg.score) || 0,
      conversionRate,
      totalValue: Number(valueStats._sum.value) || 0,
      conversionStats: {
        totalConverted: convertedCount,
        conversionsByMonth: conversionsByMonthMap,
        averageTimeToConversion: Number(avgConversionTime[0]?.avg_days) || 0
      }
    };
  }

  /**
   * Assign lead to a user
   */
  async assignLead(
    leadId: string,
    tenantId: string,
    ownerId: string
  ): Promise<LeadWithRelations> {
    return await prisma.$transaction(async (tx) => {
      // Validate lead exists
      const lead = await tx.lead.findUnique({
        where: { id: leadId, tenantId, deletedAt: null }
      });

      if (!lead) {
        throw new Error('Lead not found or access denied');
      }

      // Validate owner belongs to the same tenant
      const owner = await tx.user.findFirst({
        where: {
          id: ownerId,
          tenantUsers: {
            some: { tenantId }
          }
        }
      });

      if (!owner) {
        throw new Error('Owner not found or access denied');
      }

      // Update lead assignment
      const updatedLead = await tx.lead.update({
        where: { id: leadId },
        data: {
          ownerId,
          updatedAt: new Date()
        },
        include: this.getIncludeOptions()
      });

      // Trigger assignment notification
      await this.triggerLeadAssignmentNotification(updatedLead, tenantId, lead.ownerId);

      return updatedLead;
    });
  }

  /**
   * Update lead score
   */
  async updateLeadScore(
    leadId: string,
    tenantId: string,
    score: number
  ): Promise<LeadWithRelations> {
    const lead = await prisma.lead.findUnique({
      where: { id: leadId, tenantId, deletedAt: null }
    });

    if (!lead) {
      throw new Error('Lead not found or access denied');
    }

    return await prisma.lead.update({
      where: { id: leadId },
      data: {
        score: Math.max(0, Math.min(100, score)), // Ensure score is between 0-100
        updatedAt: new Date()
      },
      include: this.getIncludeOptions()
    });
  }

  /**
   * Check if leads are eligible for conversion to opportunities
   */
  async checkConversionEligibility(
    leadIds: string[],
    tenantId: string
  ): Promise<LeadConversionEligibilityResult[]> {
    const leads = await prisma.lead.findMany({
      where: {
        id: { in: leadIds },
        tenantId,
        deletedAt: null
      },
      include: {
        contact: true,
        company: true,
        opportunities: true
      }
    });

    return leads.map(lead => {
      const missingRequirements: string[] = [];
      let qualificationScore = 0;

      // Check if already converted
      if (lead.isConverted) {
        return {
          leadId: lead.id,
          eligible: false,
          reason: 'Lead has already been converted to an opportunity',
          qualificationScore: 0
        };
      }

      // Check if lead has existing opportunities
      if (lead.opportunities && lead.opportunities.length > 0) {
        return {
          leadId: lead.id,
          eligible: false,
          reason: 'Lead already has associated opportunities',
          qualificationScore: 0
        };
      }

      // Basic qualification checks
      if (!lead.contactId && !lead.companyId) {
        missingRequirements.push('Contact or Company information');
      } else {
        qualificationScore += 20;
      }

      if (lead.score < 50) {
        missingRequirements.push('Lead score should be at least 50');
      } else {
        qualificationScore += 30;
      }

      if (!['qualified', 'proposal', 'negotiation'].includes(lead.status)) {
        missingRequirements.push('Lead status should be qualified, proposal, or negotiation');
      } else {
        qualificationScore += 30;
      }

      if (!lead.value || Number(lead.value) <= 0) {
        missingRequirements.push('Lead should have an estimated value');
      } else {
        qualificationScore += 20;
      }

      const eligible = missingRequirements.length === 0;

      return {
        leadId: lead.id,
        eligible,
        reason: eligible ? 'Lead is eligible for conversion' : `Missing requirements: ${missingRequirements.join(', ')}`,
        qualificationScore,
        missingRequirements: eligible ? undefined : missingRequirements
      };
    });
  }

  /**
   * Mark lead as converted (used by opportunity service)
   */
  async markLeadAsConverted(
    leadId: string,
    tenantId: string,
    convertedById: string,
    notes?: string
  ): Promise<void> {
    const lead = await prisma.lead.update({
      where: { id: leadId, tenantId },
      data: {
        isConverted: true,
        convertedAt: new Date(),
        convertedById,
        conversionNotes: notes,
        status: 'converted'
      },
      include: this.getIncludeOptions()
    });

    // Trigger lead conversion notification
    await this.triggerLeadConversionNotification(lead, tenantId);
  }

  /**
   * Handle notifications for lead updates
   */
  private async handleLeadUpdateNotifications(
    updatedLead: LeadWithRelations,
    previousLead: Lead,
    tenantId: string
  ): Promise<void> {
    try {
      // Check for owner change
      if (updatedLead.ownerId !== previousLead.ownerId) {
        await this.triggerLeadAssignmentNotification(updatedLead, tenantId, previousLead.ownerId);
      }

      // Check for status change
      if (updatedLead.status !== previousLead.status) {
        await this.triggerLeadStatusChangeNotification(updatedLead, tenantId, previousLead.status);
      }

      // Check for priority change to urgent
      if (updatedLead.priority === 'urgent' && previousLead.priority !== 'urgent') {
        await this.triggerUrgentLeadNotification(updatedLead, tenantId);
      }

      // Check for value increase to high-value threshold
      const previousValue = Number(previousLead.value) || 0;
      const currentValue = Number(updatedLead.value) || 0;
      if (currentValue > 10000 && previousValue <= 10000) {
        await this.triggerHighValueLeadNotification(updatedLead, tenantId);
      }
    } catch (error) {
      console.error('Failed to handle lead update notifications:', error);
    }
  }

  /**
   * Trigger lead creation notification
   */
  private async triggerLeadCreationNotification(
    lead: LeadWithRelations,
    tenantId: string
  ): Promise<void> {
    try {
      const targetUsers = [];

      // Notify lead owner if assigned
      if (lead.ownerId) {
        targetUsers.push(lead.ownerId);
      }

      // Notify team members with lead management permissions
      // TODO: Add logic to get team members with appropriate permissions

      // Create notification for each target user
      for (const userId of targetUsers) {
        await this.notificationService.createNotification({
          tenantId,
          userId,
          type: 'lead_created',
          category: 'lead',
          title: 'New Lead Created',
          message: `Lead "${lead.title}" has been created and assigned to you`,
          data: {
            leadId: lead.id,
            leadTitle: lead.title,
            leadStatus: lead.status,
            leadPriority: lead.priority,
            leadScore: lead.score,
            leadValue: lead.value?.toString(),
            contactName: lead.contact ? `${lead.contact.firstName} ${lead.contact.lastName}` : null,
            companyName: lead.company?.name,
            leadSource: lead.leadSource?.name,
            createdBy: lead.createdBy ? `${lead.createdBy.firstName} ${lead.createdBy.lastName}` : 'System'
          },
          actionUrl: `/leads/${lead.id}`,
          actionLabel: 'View Lead'
        });
      }

      // Check for high-value lead notification
      if (lead.value && lead.value > 10000) { // Configurable threshold
        await this.triggerHighValueLeadNotification(lead, tenantId);
      }

      // Check for urgent priority notification
      if (lead.priority === 'urgent') {
        await this.triggerUrgentLeadNotification(lead, tenantId);
      }
    } catch (error) {
      console.error('Failed to send lead creation notification:', error);
      // Don't throw error to avoid breaking the main operation
    }
  }

  /**
   * Trigger lead assignment notification
   */
  private async triggerLeadAssignmentNotification(
    lead: LeadWithRelations,
    tenantId: string,
    previousOwnerId?: string | null
  ): Promise<void> {
    try {
      // Notify new owner
      if (lead.ownerId) {
        await this.notificationService.createNotification({
          tenantId,
          userId: lead.ownerId,
          type: 'lead_assigned',
          category: 'lead',
          title: 'Lead Assigned to You',
          message: `Lead "${lead.title}" has been assigned to you`,
          data: {
            leadId: lead.id,
            leadTitle: lead.title,
            leadStatus: lead.status,
            leadPriority: lead.priority,
            leadScore: lead.score,
            contactName: lead.contact ? `${lead.contact.firstName} ${lead.contact.lastName}` : null,
            companyName: lead.company?.name,
            previousOwner: previousOwnerId
          },
          actionUrl: `/leads/${lead.id}`,
          actionLabel: 'View Lead'
        });
      }

      // Notify previous owner if different
      if (previousOwnerId && previousOwnerId !== lead.ownerId) {
        await this.notificationService.createNotification({
          tenantId,
          userId: previousOwnerId,
          type: 'lead_reassigned',
          category: 'lead',
          title: 'Lead Reassigned',
          message: `Lead "${lead.title}" has been reassigned to another team member`,
          data: {
            leadId: lead.id,
            leadTitle: lead.title,
            newOwner: lead.owner ? `${lead.owner.firstName} ${lead.owner.lastName}` : 'Unknown'
          },
          actionUrl: `/leads/${lead.id}`,
          actionLabel: 'View Lead'
        });
      }
    } catch (error) {
      console.error('Failed to send lead assignment notification:', error);
    }
  }

  /**
   * Trigger lead status change notification
   */
  private async triggerLeadStatusChangeNotification(
    lead: LeadWithRelations,
    tenantId: string,
    previousStatus: string
  ): Promise<void> {
    try {
      const targetUsers = [];

      // Notify lead owner
      if (lead.ownerId) {
        targetUsers.push(lead.ownerId);
      }

      // Determine notification type based on status change
      let notificationType = 'lead_status_changed';
      let title = 'Lead Status Updated';
      let message = `Lead "${lead.title}" status changed from ${previousStatus} to ${lead.status}`;

      if (lead.status === 'qualified') {
        notificationType = 'lead_qualified';
        title = 'Lead Qualified';
        message = `Lead "${lead.title}" has been qualified and is ready for conversion`;
      } else if (lead.status === 'closed_lost') {
        notificationType = 'lead_disqualified';
        title = 'Lead Disqualified';
        message = `Lead "${lead.title}" has been marked as closed lost`;
      }

      for (const userId of targetUsers) {
        await this.notificationService.createNotification({
          tenantId,
          userId,
          type: notificationType,
          category: 'lead',
          title,
          message,
          data: {
            leadId: lead.id,
            leadTitle: lead.title,
            previousStatus,
            newStatus: lead.status,
            leadPriority: lead.priority,
            leadScore: lead.score
          },
          actionUrl: `/leads/${lead.id}`,
          actionLabel: 'View Lead'
        });
      }
    } catch (error) {
      console.error('Failed to send lead status change notification:', error);
    }
  }

  /**
   * Trigger lead conversion notification
   */
  private async triggerLeadConversionNotification(
    lead: LeadWithRelations,
    tenantId: string
  ): Promise<void> {
    try {
      const targetUsers = [];

      // Notify lead owner
      if (lead.ownerId) {
        targetUsers.push(lead.ownerId);
      }

      // Notify sales team members
      // TODO: Add logic to get sales team members

      for (const userId of targetUsers) {
        await this.notificationService.createNotification({
          tenantId,
          userId,
          type: 'lead_converted',
          category: 'lead',
          title: 'Lead Converted to Opportunity',
          message: `Lead "${lead.title}" has been successfully converted to an opportunity`,
          data: {
            leadId: lead.id,
            leadTitle: lead.title,
            leadValue: lead.value?.toString(),
            convertedBy: lead.convertedBy ? `${lead.convertedBy.firstName} ${lead.convertedBy.lastName}` : 'System',
            conversionNotes: lead.conversionNotes
          },
          actionUrl: `/leads/${lead.id}`,
          actionLabel: 'View Lead'
        });
      }
    } catch (error) {
      console.error('Failed to send lead conversion notification:', error);
    }
  }

  /**
   * Trigger high-value lead notification
   */
  private async triggerHighValueLeadNotification(
    lead: LeadWithRelations,
    tenantId: string
  ): Promise<void> {
    try {
      // Notify managers and senior sales team
      // TODO: Add logic to get managers and senior sales team members

      const targetUsers = [];
      if (lead.ownerId) {
        targetUsers.push(lead.ownerId);
      }

      for (const userId of targetUsers) {
        await this.notificationService.createNotification({
          tenantId,
          userId,
          type: 'high_value_lead_detected',
          category: 'lead',
          title: 'High-Value Lead Alert',
          message: `High-value lead "${lead.title}" worth $${lead.value} requires immediate attention`,
          data: {
            leadId: lead.id,
            leadTitle: lead.title,
            leadValue: lead.value?.toString(),
            leadPriority: lead.priority,
            leadScore: lead.score,
            contactName: lead.contact ? `${lead.contact.firstName} ${lead.contact.lastName}` : null,
            companyName: lead.company?.name
          },
          actionUrl: `/leads/${lead.id}`,
          actionLabel: 'View Lead'
        });
      }
    } catch (error) {
      console.error('Failed to send high-value lead notification:', error);
    }
  }

  /**
   * Trigger urgent lead notification
   */
  private async triggerUrgentLeadNotification(
    lead: LeadWithRelations,
    tenantId: string
  ): Promise<void> {
    try {
      const targetUsers = [];
      if (lead.ownerId) {
        targetUsers.push(lead.ownerId);
      }

      for (const userId of targetUsers) {
        await this.notificationService.createNotification({
          tenantId,
          userId,
          type: 'urgent_lead_alert',
          category: 'lead',
          title: 'Urgent Lead Alert',
          message: `Urgent lead "${lead.title}" requires immediate follow-up`,
          data: {
            leadId: lead.id,
            leadTitle: lead.title,
            leadPriority: lead.priority,
            expectedCloseDate: lead.expectedCloseDate?.toISOString(),
            contactName: lead.contact ? `${lead.contact.firstName} ${lead.contact.lastName}` : null,
            companyName: lead.company?.name
          },
          actionUrl: `/leads/${lead.id}`,
          actionLabel: 'View Lead'
        });
      }
    } catch (error) {
      console.error('Failed to send urgent lead notification:', error);
    }
  }
}

// Export singleton instance
export const leadManagementService = new LeadManagementService();
