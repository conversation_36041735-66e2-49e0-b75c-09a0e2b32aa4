'use client';

import React from 'react';
import { Menu } from '@headlessui/react';
import {
  ChevronDownIcon,
  PlusIcon,
  PencilIcon,
  TrashIcon
} from '@heroicons/react/24/outline';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { PermissionGate } from '@/components/ui/permission-gate';
import { PermissionResource, PermissionAction } from '@/types';

interface ContactQuickActionsProps {
  contactId: string;
  contactData: any;
  onCreateLeadClick?: () => void;
  onEditClick?: () => void;
  onDeleteClick?: () => void;
}

export function ContactQuickActions({
  contactId,
  contactData,
  onCreateLeadClick,
  onEditClick,
  onDeleteClick
}: ContactQuickActionsProps) {
  const quickActions = [
    {
      name: 'Create Lead',
      description: 'Convert to sales opportunity',
      icon: PlusIcon,
      color: 'text-green-600 dark:text-green-400',
      bgColor: 'bg-green-50 dark:bg-green-900/20',
      onClick: onCreateLeadClick,
      permission: { resource: PermissionResource.LEADS, action: PermissionAction.CREATE },
    },
    {
      name: 'Edit Contact',
      description: 'Update contact information',
      icon: PencilIcon,
      color: 'text-blue-600 dark:text-blue-400',
      bgColor: 'bg-blue-50 dark:bg-blue-900/20',
      onClick: onEditClick,
      permission: { resource: PermissionResource.CONTACTS, action: PermissionAction.UPDATE },
      requireOwnership: true,
    },
    {
      name: 'Delete Contact',
      description: 'Permanently remove contact',
      icon: TrashIcon,
      color: 'text-red-600 dark:text-red-400',
      bgColor: 'bg-red-50 dark:bg-red-900/20',
      onClick: onDeleteClick,
      permission: { resource: PermissionResource.CONTACTS, action: PermissionAction.DELETE },
      requireOwnership: true,
    },
  ];

  return (
    <Menu as="div" className="relative inline-block text-left">
      <div>
        <Menu.Button as={Button} variant="outline" size="sm" className="flex items-center gap-1">
          <PlusIcon className="h-4 w-4" aria-hidden="true" />
          Quick Actions
          <ChevronDownIcon className="h-4 w-4" aria-hidden="true" />
        </Menu.Button>
      </div>

      <Menu.Items className="absolute right-0 z-10 mt-2 w-80 origin-top-right rounded-md bg-white dark:bg-gray-800 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
        <div className="py-1">
          {quickActions.map((action) => {
            const ActionButton = (
              <Menu.Item key={action.name}>
                {({ active }) => (
                  <button
                    onClick={action.onClick}
                    className={cn(
                      active
                        ? 'bg-gray-100 dark:bg-gray-700'
                        : 'bg-white dark:bg-gray-800',
                      'group flex w-full items-center px-4 py-3 text-sm transition-colors'
                    )}
                  >
                    <div className={cn(
                      'flex-shrink-0 p-2 rounded-md mr-3',
                      action.bgColor
                    )}>
                      <action.icon
                        className={cn('h-5 w-5', action.color)}
                        aria-hidden="true"
                      />
                    </div>
                    <div className="flex-1 text-left">
                      <p className="font-medium text-gray-900 dark:text-white">
                        {action.name}
                      </p>
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        {action.description}
                      </p>
                    </div>
                  </button>
                )}
              </Menu.Item>
            );

            // Wrap actions that require permissions with PermissionGate
            if (action.permission) {
              return (
                <PermissionGate
                  key={action.name}
                  resource={action.permission.resource}
                  action={action.permission.action}
                  requireOwnership={action.requireOwnership}
                  ownerId={action.requireOwnership ? contactData?.ownerId : undefined}
                >
                  {ActionButton}
                </PermissionGate>
              );
            }

            return ActionButton;
          })}
        </div>
      </Menu.Items>
    </Menu>
  );
}
