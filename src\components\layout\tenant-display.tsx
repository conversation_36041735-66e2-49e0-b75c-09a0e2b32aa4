'use client';

import React from 'react';
import { BuildingOfficeIcon } from '@heroicons/react/24/outline';
import { useTenant } from '@/hooks/use-tenant';
import { useTranslation } from '@/components/providers/locale-provider';

export function TenantDisplay() {
  const { t } = useTranslation();
  const { currentTenant, isLoading } = useTenant();

  if (isLoading) {
    return (
      <div className="flex items-center space-x-2 px-3 py-2 bg-gray-100 rounded-lg animate-pulse">
        <div className="w-8 h-8 bg-gray-300 rounded-full"></div>
        <div className="w-24 h-4 bg-gray-300 rounded"></div>
      </div>
    );
  }

  if (!currentTenant) {
    return (
      <div className="flex items-center space-x-2 px-3 py-2 bg-gray-100 rounded-lg">
        <BuildingOfficeIcon className="w-5 h-5 text-gray-400" />
        <span className="text-sm text-gray-500">No tenant</span>
      </div>
    );
  }

  return (
    <div className="flex items-center space-x-2 px-3 py-2 bg-white border border-gray-300 rounded-lg shadow-sm">
      {/* Tenant Logo/Icon */}
      <div className="flex-shrink-0">
        {currentTenant.branding?.logo ? (
          <img
            src={currentTenant.branding.logo}
            alt={currentTenant.name}
            className="w-8 h-8 rounded-full object-cover"
          />
        ) : (
          <div 
            className="w-8 h-8 rounded-full flex items-center justify-center text-white text-sm font-medium"
            style={{ 
              backgroundColor: currentTenant.branding?.primaryColor || '#3B82F6' 
            }}
          >
            {currentTenant.name.charAt(0).toUpperCase()}
          </div>
        )}
      </div>

      {/* Tenant Info */}
      <div className="flex-1 min-w-0">
        <div className="text-sm font-medium text-gray-900 truncate">
          {currentTenant.name}
        </div>
        <div className="text-xs text-gray-500">
          {currentTenant.subscription?.planId || 'Free Plan'}
        </div>
      </div>
    </div>
  );
}
