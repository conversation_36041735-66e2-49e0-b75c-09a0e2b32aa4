# CRM Platform Deployment Guide

This comprehensive guide covers deploying the CRM platform across different environments using Docker containerization.

## 📋 Table of Contents

1. [Prerequisites](#prerequisites)
2. [Quick Start](#quick-start)
3. [Environment Configuration](#environment-configuration)
4. [Deployment Process](#deployment-process)
5. [SSL Configuration](#ssl-configuration)
6. [Database Management](#database-management)
7. [Monitoring and Health Checks](#monitoring-and-health-checks)
8. [Backup and Recovery](#backup-and-recovery)
9. [Troubleshooting](#troubleshooting)

## 🔧 Prerequisites

### System Requirements
- **Docker**: Version 20.10 or higher
- **Docker Compose**: Version 2.0 or higher
- **Node.js**: Version 18+ (for local development)
- **Git**: For version control
- **OpenSSL**: For SSL certificate generation

### Hardware Requirements

#### Development
- **CPU**: 2 cores minimum
- **RAM**: 4GB minimum, 8GB recommended
- **Storage**: 10GB free space

#### Staging
- **CPU**: 2-4 cores
- **RAM**: 8GB minimum, 16GB recommended
- **Storage**: 50GB free space

#### Production
- **CPU**: 4+ cores
- **RAM**: 16GB minimum, 32GB recommended
- **Storage**: 100GB+ free space

### Network Requirements
- **Ports**: 3010 (app), 5434 (postgres), 6380 (redis), 8082 (pgadmin)
- **Internet**: Required for Docker image pulls and SSL certificates

## 🚀 Quick Start

### 1. Clone and Setup
```bash
# Clone the repository
git clone <repository-url>
cd crm-platform

# Make deployment scripts executable
chmod +x deployment/scripts/*.sh
chmod +x deployment/scripts/**/*.sh
```

### 2. Environment Configuration
```bash
# Copy environment template
cp .env.example .env.docker

# Edit environment variables
nano .env.docker
```

### 3. Deploy to Development
```bash
# Deploy with default settings
./deployment/scripts/deploy-new.sh development

# Or with custom options
./deployment/scripts/deploy-new.sh development --verbose
```

### 4. Access the Application
- **Application**: http://localhost:3010
- **pgAdmin**: http://localhost:8082 (start with `--profile tools`)

## 🌍 Environment Configuration

### Development Environment
```bash
# Deploy to development
./deployment/scripts/deploy-new.sh development

# Features:
# - Hot reloading enabled
# - Debug mode active
# - Development database
# - Verbose logging
```

### Staging Environment
```bash
# Deploy to staging
./deployment/scripts/deploy-new.sh staging --ssl

# Features:
# - Production-like configuration
# - SSL certificates
# - Performance monitoring
# - Limited debug information
```

### Production Environment
```bash
# Deploy to production
./deployment/scripts/deploy-new.sh production --ssl

# Features:
# - Optimized for performance
# - SSL certificates required
# - Security hardening
# - Comprehensive logging
```

## 📦 Deployment Process

### Standard Deployment
```bash
# Full deployment with all steps
./deployment/scripts/deploy-new.sh [environment]

# Skip building images (faster for code-only changes)
./deployment/scripts/deploy-new.sh [environment] --no-build

# Skip database migrations
./deployment/scripts/deploy-new.sh [environment] --no-migrate

# Force SSL certificate generation
./deployment/scripts/deploy-new.sh [environment] --ssl
```

### Manual Deployment Steps
```bash
# 1. Pull latest images
cd /path/to/project
docker-compose -f deployment/docker/docker-compose.new.yml pull

# 2. Build application images
docker-compose -f deployment/docker/docker-compose.new.yml build

# 3. Start services
docker-compose -f deployment/docker/docker-compose.new.yml up -d

# 4. Run migrations
docker-compose -f deployment/docker/docker-compose.new.yml exec app npx prisma migrate deploy

# 5. Check health
./deployment/scripts/utils/health-check.sh
```

## 🔐 SSL Configuration

### Generate SSL Certificates
```bash
# Generate certificates for development/staging
./deployment/scripts/ssl/generate-certs-new.sh

# Custom certificate settings
./deployment/scripts/ssl/generate-certs-new.sh \
  --days 730 \
  --cn myapp.local \
  --org "My Company"
```

### Production SSL
For production, use proper CA-signed certificates:

1. **Obtain certificates** from a trusted CA (Let's Encrypt, etc.)
2. **Place certificates** in `deployment/config/nginx/ssl/`
3. **Update Nginx configuration** if needed
4. **Restart services** to apply changes

### SSL Certificate Locations
```
deployment/config/nginx/ssl/
├── server.crt          # SSL certificate
├── server.key          # Private key
└── ca-bundle.crt       # Certificate authority bundle (if needed)
```

## 🗄️ Database Management

### Database Operations
```bash
# Run migrations
docker-compose -f deployment/docker/docker-compose.new.yml exec app npx prisma migrate deploy

# Seed database
docker-compose -f deployment/docker/docker-compose.new.yml exec app npm run seed

# Access database directly
docker-compose -f deployment/docker/docker-compose.new.yml exec postgres psql -U admin -d crm_platform

# Database backup
./deployment/scripts/utils/backup.sh production

# Apply performance indexes
docker-compose -f deployment/docker/docker-compose.new.yml exec app psql -f scripts/create-performance-indexes.sql
```

### Database Connection
```bash
# Connection details (from container)
Host: postgres
Port: 5432
Database: crm_platform
Username: admin
Password: admin (change in production)

# External connection
Host: localhost
Port: 5434
Database: crm_platform
Username: admin
Password: admin
```

## 📊 Monitoring and Health Checks

### Health Check Script
```bash
# Check all services
./deployment/scripts/utils/health-check.sh

# Check specific environment
./deployment/scripts/utils/health-check.sh production

# Generate health report
./deployment/scripts/utils/health-check.sh --report
```

### Manual Health Checks
```bash
# Check service status
docker-compose -f deployment/docker/docker-compose.new.yml ps

# View logs
docker-compose -f deployment/docker/docker-compose.new.yml logs -f

# Check application health endpoint
curl http://localhost:3010/api/health

# Monitor resource usage
docker stats
```

### Log Management
```bash
# View all logs
docker-compose -f deployment/docker/docker-compose.new.yml logs

# View specific service logs
docker-compose -f deployment/docker/docker-compose.new.yml logs app
docker-compose -f deployment/docker/docker-compose.new.yml logs postgres
docker-compose -f deployment/docker/docker-compose.new.yml logs redis

# Follow logs in real-time
docker-compose -f deployment/docker/docker-compose.new.yml logs -f app
```

## 💾 Backup and Recovery

### Automated Backup
```bash
# Backup development environment
./deployment/scripts/utils/backup.sh development

# Backup production with custom retention
./deployment/scripts/utils/backup.sh production --retention 30

# Backup to custom location
./deployment/scripts/utils/backup.sh production --backup-dir /custom/backup/path
```

### Manual Backup
```bash
# Database backup
docker-compose -f deployment/docker/docker-compose.new.yml exec postgres pg_dump -U admin crm_platform > backup.sql

# Volume backup
docker run --rm -v crm-postgres-data:/data -v $(pwd):/backup alpine tar czf /backup/postgres-data.tar.gz -C /data .

# Configuration backup
tar czf config-backup.tar.gz deployment/config/ .env.docker
```

### Recovery Process
```bash
# 1. Stop services
docker-compose -f deployment/docker/docker-compose.new.yml down

# 2. Restore database
docker-compose -f deployment/docker/docker-compose.new.yml up -d postgres
docker-compose -f deployment/docker/docker-compose.new.yml exec -T postgres psql -U admin -d crm_platform < backup.sql

# 3. Restore volumes (if needed)
docker run --rm -v crm-postgres-data:/data -v $(pwd):/backup alpine tar xzf /backup/postgres-data.tar.gz -C /data

# 4. Start all services
docker-compose -f deployment/docker/docker-compose.new.yml up -d
```

## 🔧 Maintenance Operations

### Update Application
```bash
# 1. Pull latest code
git pull origin main

# 2. Rebuild and deploy
./deployment/scripts/deploy-new.sh production

# 3. Verify deployment
./deployment/scripts/utils/health-check.sh production
```

### Scale Services
```bash
# Scale application instances
docker-compose -f deployment/docker/docker-compose.new.yml up -d --scale app=3

# Scale down
docker-compose -f deployment/docker/docker-compose.new.yml up -d --scale app=1
```

### Clean Up
```bash
# Remove unused images
docker image prune -f

# Remove unused volumes
docker volume prune -f

# Remove unused networks
docker network prune -f

# Complete cleanup
docker system prune -af
```

## 🎯 Best Practices

### Security
- ✅ Use strong passwords for database and admin accounts
- ✅ Enable SSL/TLS for all environments except development
- ✅ Regularly update Docker images and dependencies
- ✅ Use secrets management for sensitive data
- ✅ Implement proper firewall rules
- ✅ Regular security audits and vulnerability scans

### Performance
- ✅ Apply database performance indexes
- ✅ Monitor resource usage regularly
- ✅ Implement proper caching strategies
- ✅ Use CDN for static assets in production
- ✅ Regular database maintenance and optimization

### Reliability
- ✅ Implement automated backups
- ✅ Set up monitoring and alerting
- ✅ Test disaster recovery procedures
- ✅ Use health checks for all services
- ✅ Implement proper logging and monitoring

### Development
- ✅ Use consistent environments across dev/staging/prod
- ✅ Implement proper CI/CD pipelines
- ✅ Regular testing and quality assurance
- ✅ Document all deployment procedures
- ✅ Version control all configuration files

## 📞 Support and Troubleshooting

For deployment issues:
1. Check the [Troubleshooting Guide](./troubleshooting.md)
2. Review application logs
3. Verify system requirements
4. Check network connectivity
5. Contact the development team

## 📚 Additional Resources

- [Environment Setup Guide](./environment-setup.md)
- [Security Best Practices](./security.md)
- [Troubleshooting Guide](./troubleshooting.md)
- [Docker Documentation](https://docs.docker.com/)
- [Next.js Deployment](https://nextjs.org/docs/deployment)
