import { NextResponse } from 'next/server';
import { withPermission, AuthenticatedRequest } from '@/lib/permission-middleware';
import { UserManagementService } from '@/services/user-management';
import { PermissionAction, PermissionResource } from '@/types';
import { z } from 'zod';

const inviteUserSchema = z.object({
  email: z.string().email('Invalid email address'),
  firstName: z.string().min(1, 'First name is required'),
  lastName: z.string().min(1, 'Last name is required'),
  role: z.string().min(1, 'Role is required')
});

const createUserSchema = z.object({
  email: z.string().email('Invalid email address'),
  password: z.string().min(8, 'Password must be at least 8 characters'),
  firstName: z.string().min(1, 'First name is required'),
  lastName: z.string().min(1, 'Last name is required'),
  phone: z.string().optional(),
  role: z.string().optional(),
  teamId: z.string().optional(),
  department: z.string().optional(),
  jobTitle: z.string().optional(),
  locale: z.string().optional(),
  timezone: z.string().optional(),
  sendWelcomeEmail: z.boolean().optional().default(true)
});

const updateUserSchema = z.object({
  firstName: z.string().min(1, 'First name is required').optional(),
  lastName: z.string().min(1, 'Last name is required').optional(),
  phone: z.string().optional(),
  locale: z.enum(['en', 'ar']).optional(),
  timezone: z.string().optional(),
  avatarUrl: z.string().url().optional()
});

// Get users in tenant
export const GET = withPermission({
  resource: PermissionResource.USERS,
  action: PermissionAction.READ,
  allowSuperAdmin: true
})(async (req) => {
  try {
    const { searchParams } = new URL(req.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const search = searchParams.get('search') || undefined;
    const role = searchParams.get('role') || undefined;
    const status = searchParams.get('status') || undefined;
    const teamId = searchParams.get('teamId') || undefined;

    const userService = new UserManagementService();
    const result = await userService.getTenantUsers(req.tenantId, {
      page,
      limit,
      search,
      role,
      status,
      teamId
    });

    return NextResponse.json({
      success: true,
      data: result.users,
      pagination: result.pagination
    });

  } catch (error) {
    console.error('Get users error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
});

// Create user or invite user to tenant
export const POST = withPermission({
  resource: PermissionResource.USERS,
  action: PermissionAction.CREATE,
  allowSuperAdmin: true
})(async (req) => {
  try {
    const body = await req.json();
    const { action = 'invite' } = body;

    const userService = new UserManagementService();

    if (action === 'create') {
      // Create new user with password
      const validatedData = createUserSchema.parse(body);

      const user = await userService.createUserWithTeam({
        email: validatedData.email,
        password: validatedData.password,
        firstName: validatedData.firstName,
        lastName: validatedData.lastName,
        phone: validatedData.phone,
        role: validatedData.role,
        teamId: validatedData.teamId,
        department: validatedData.department,
        jobTitle: validatedData.jobTitle,
        locale: validatedData.locale,
        timezone: validatedData.timezone,
        sendWelcomeEmail: validatedData.sendWelcomeEmail
      }, req.tenantId, req.userId);

      return NextResponse.json({
        success: true,
        data: user,
        message: 'User created successfully'
      }, { status: 201 });

    } else {
      // Invite existing or new user
      const validatedData = inviteUserSchema.parse(body);

      await userService.inviteUserToTenant({
        email: validatedData.email,
        firstName: validatedData.firstName,
        lastName: validatedData.lastName,
        role: validatedData.role,
        tenantId: req.tenantId,
        invitedBy: req.userId
      });

      return NextResponse.json({
        success: true,
        message: 'User invited successfully'
      }, { status: 201 });
    }

  } catch (error) {
    console.error('User creation/invitation error:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          error: 'Validation failed',
          details: error.errors.map(e => ({
            field: e.path.join('.'),
            message: e.message
          }))
        },
        { status: 400 }
      );
    }

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
});

// Update user
export const PUT = withPermission({
  resource: PermissionResource.USERS,
  action: PermissionAction.UPDATE,
  requireOwnership: true,
  getResourceOwnerId: async (req) => {
    const { searchParams } = new URL(req.url);
    return searchParams.get('userId');
  },
  allowSuperAdmin: true
})(async (req) => {
  try {
    const { searchParams } = new URL(req.url);
    const targetUserId = searchParams.get('userId');

    if (!targetUserId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      );
    }

    const body = await req.json();
    const validatedData = updateUserSchema.parse(body);

    const userService = new UserManagementService();
    const updatedUser = await userService.updateUser(targetUserId, validatedData);

    return NextResponse.json({
      success: true,
      data: {
        id: updatedUser.id,
        email: updatedUser.email,
        firstName: updatedUser.firstName,
        lastName: updatedUser.lastName,
        locale: updatedUser.locale,
        timezone: updatedUser.timezone,
        avatarUrl: updatedUser.avatarUrl,
        updatedAt: updatedUser.updatedAt
      },
      message: 'User updated successfully'
    });

  } catch (error) {
    console.error('Update user error:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          error: 'Validation failed',
          details: error.errors.map(e => ({
            field: e.path.join('.'),
            message: e.message
          }))
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
});

// Remove user from tenant
export const DELETE = withPermission({
  resource: PermissionResource.USERS,
  action: PermissionAction.DELETE,
  allowSuperAdmin: true
})(async (req) => {
  try {
    const { searchParams } = new URL(req.url);
    const targetUserId = searchParams.get('userId');

    if (!targetUserId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      );
    }

    // Users cannot remove themselves
    if (targetUserId === req.userId) {
      return NextResponse.json(
        { error: 'You cannot remove yourself from the tenant' },
        { status: 400 }
      );
    }

    const userService = new UserManagementService();
    await userService.removeUserFromTenant(req.tenantId, targetUserId, req.userId);

    return NextResponse.json({
      success: true,
      message: 'User removed from tenant successfully'
    });

  } catch (error) {
    console.error('Remove user error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
});
