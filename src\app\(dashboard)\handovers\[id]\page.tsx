'use client';

import { Metada<PERSON> } from 'next';
import { notFound, useRouter } from 'next/navigation';
import { useState, useEffect } from 'react';
import HandoverDetails from '@/components/handovers/HandoverDetails';
import { PermissionGate } from '@/components/ui/permission-gate';
import { PermissionResource, PermissionAction } from '@/types';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { EmptyState } from '@/components/ui/empty-state';
import { ExclamationTriangleIcon, PencilIcon, TrashIcon, ArrowLeftIcon } from '@heroicons/react/24/outline';
import { PageLayout } from '@/components/layout/page-layout';
import { useEnhancedDeleteDialog, deleteConfigurations } from '@/components/ui/enhanced-delete-dialog';
import { toastFunctions as toast } from '@/hooks/use-toast';
import { use } from 'react';

interface PageProps {
  params: Promise<{
    id: string;
  }>;
}

interface HandoverSummary {
  id: string;
  title: string;
  status: string;
  priority: string;
  checklistProgress: number;
  documentsCount: number;
  qaThreadsCount: number;
  opportunity: {
    title: string;
  };
}

export default function HandoverDetailsPage({ params }: PageProps) {
  const router = useRouter();
  const resolvedParams = use(params);
  const { showDeleteDialog, DialogComponent } = useEnhancedDeleteDialog();
  const [handover, setHandover] = useState<HandoverSummary | null>(null);
  const [loading, setLoading] = useState(true);

  if (!resolvedParams.id) {
    notFound();
  }

  // Fetch handover summary for delete dialog
  useEffect(() => {
    const fetchHandoverSummary = async () => {
      try {
        const response = await fetch(`/api/handovers/${resolvedParams.id}`);
        if (response.ok) {
          const data = await response.json();
          setHandover(data);
        }
      } catch (error) {
        console.error('Error fetching handover:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchHandoverSummary();
  }, [resolvedParams.id]);

  // Handle delete handover
  const handleDeleteHandover = async () => {
    if (!handover) return;

    const relatedData = [
      `Opportunity: ${handover.opportunity.title}`,
      `Status: ${handover.status}`,
      `Priority: ${handover.priority}`,
      `Progress: ${handover.checklistProgress}%`,
    ];

    const customWarnings = [];
    if (handover.status === 'in_progress') {
      customWarnings.push('This handover is currently in progress');
    }
    if (handover.checklistProgress > 0) {
      customWarnings.push(`${handover.checklistProgress}% of checklist items have been completed`);
    }
    if (handover.documentsCount > 0) {
      customWarnings.push(`${handover.documentsCount} documents are attached to this handover`);
    }
    if (handover.qaThreadsCount > 0) {
      customWarnings.push(`${handover.qaThreadsCount} Q&A threads will be lost`);
    }

    showDeleteDialog({
      ...deleteConfigurations.handover,
      itemName: handover.title,
      relatedData,
      customWarnings,
      onConfirm: async () => {
        try {
          const response = await fetch(`/api/handovers/${resolvedParams.id}`, {
            method: 'DELETE',
          });

          if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || 'Failed to delete handover');
          }

          toast({
            title: 'Handover Deleted',
            description: `"${handover.title}" has been successfully deleted.`,
          });

          router.push('/handovers');
        } catch (err) {
          console.error('Error deleting handover:', err);
          toast({
            title: 'Delete Failed',
            description: err instanceof Error ? err.message : 'Failed to delete handover',
            variant: 'destructive',
          });
        }
      },
    });
  };

  // Page actions
  const actions = [
    <Button
      key="back"
      variant="outline"
      onClick={() => router.push('/handovers')}
      className="gap-2"
    >
      <ArrowLeftIcon className="w-4 h-4" />
      Back to Handovers
    </Button>,
    <Button
      key="edit"
      variant="outline"
      onClick={() => router.push(`/handovers/${resolvedParams.id}/edit`)}
      className="gap-2"
    >
      <PencilIcon className="w-4 h-4" />
      Edit
    </Button>,
    <Button
      key="delete"
      variant="destructive"
      onClick={handleDeleteHandover}
      disabled={!handover}
      className="gap-2"
    >
      <TrashIcon className="w-4 h-4" />
      Delete
    </Button>,
  ];

  // TODO: Re-enable permission gate after running permission seeding
  // Run: node scripts/seed-permissions.js

  return (
    <>
      <DialogComponent />
      <PageLayout
        title={handover ? handover.title : "Handover Details"}
        description="View handover details and progress"
        actions={actions}
      >
        <HandoverDetails handoverId={resolvedParams.id} />
      </PageLayout>
    </>
  );

  // Uncomment this after seeding handover permissions:
  /*
  return (
    <PermissionGate
      resource={PermissionResource.HANDOVERS}
      action={PermissionAction.READ}
      fallback={
        <PageLayout>
          <Card>
            <CardContent className="p-8">
              <EmptyState
                icon={<ExclamationTriangleIcon className="w-12 h-12 text-muted-foreground" />}
                title="Access Denied"
                description="You don't have permission to view handover details. Please contact your administrator for access."
                action={{
                  label: 'Back to Handovers',
                  onClick: () => window.location.href = '/handovers',
                  variant: 'primary'
                }}
              />
            </CardContent>
          </Card>
        </PageLayout>
      }
    >
      <PageLayout
        title="Handover Details"
        description="View handover details and progress"
      >
        <HandoverDetails handoverId={resolvedParams.id} />
      </PageLayout>
    </PermissionGate>
  );
  */
}
