'use client';

import React from 'react';
import {
  ExclamationTriangleIcon,
  InformationCircleIcon,
  CheckCircleIcon,
  XCircleIcon,
  ClockIcon,
  ServerIcon,
  CpuChipIcon,
  CircleStackIcon,
  ChartBarIcon,
} from '@heroicons/react/24/outline';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { cn } from '@/lib/utils';

interface PerformanceData {
  timestamp: number;
  performanceSummary: {
    slowQueries: number;
    cacheHitRate: number;
    averageResponseTime: number;
    errorRate: number;
    memoryUsage: number;
  };
  apiMetrics: {
    averageResponseTime: number;
    p95ResponseTime: number;
    p99ResponseTime: number;
    errorRate: number;
    requestsPerMinute: number;
  };
  memoryMetrics: {
    heapUsedPercentage: number;
  };
}

interface PerformanceAlert {
  id: string;
  type: 'critical' | 'warning' | 'info' | 'success';
  title: string;
  message: string;
  metric: string;
  currentValue: number;
  threshold: number;
  timestamp: Date;
  source: string;
  icon: React.ComponentType<{ className?: string }>;
  actionable?: boolean;
  recommendation?: string;
}

interface PerformanceAlertsPanelProps {
  performanceStatus: string;
  performanceData: PerformanceData | null;
  className?: string;
}

export function PerformanceAlertsPanel({ 
  performanceStatus, 
  performanceData, 
  className 
}: PerformanceAlertsPanelProps) {
  const generatePerformanceAlerts = (): PerformanceAlert[] => {
    const alerts: PerformanceAlert[] = [];
    const now = new Date();

    if (!performanceData) {
      alerts.push({
        id: 'no-performance-data',
        type: 'warning',
        title: 'No Performance Data Available',
        message: 'Unable to retrieve current performance metrics. Check monitoring systems.',
        metric: 'system',
        currentValue: 0,
        threshold: 0,
        timestamp: now,
        source: 'Performance Monitor',
        icon: ExclamationTriangleIcon,
        actionable: true,
        recommendation: 'Check system connectivity and monitoring services.',
      });
      return alerts;
    }

    const { performanceSummary, apiMetrics, memoryMetrics } = performanceData;

    // Critical performance alerts
    if (performanceSummary.averageResponseTime > 2000) {
      alerts.push({
        id: 'critical-response-time',
        type: 'critical',
        title: 'Critical Response Time',
        message: `Average response time is ${Math.round(performanceSummary.averageResponseTime)}ms, severely impacting user experience.`,
        metric: 'response_time',
        currentValue: performanceSummary.averageResponseTime,
        threshold: 2000,
        timestamp: now,
        source: 'API Monitor',
        icon: ClockIcon,
        actionable: true,
        recommendation: 'Scale up resources, optimize database queries, or enable caching.',
      });
    }

    if (performanceSummary.errorRate > 5) {
      alerts.push({
        id: 'critical-error-rate',
        type: 'critical',
        title: 'High Error Rate',
        message: `Error rate is ${performanceSummary.errorRate.toFixed(2)}%, indicating system instability.`,
        metric: 'error_rate',
        currentValue: performanceSummary.errorRate,
        threshold: 5,
        timestamp: now,
        source: 'Error Monitor',
        icon: XCircleIcon,
        actionable: true,
        recommendation: 'Review error logs, check service dependencies, and validate configurations.',
      });
    }

    if (performanceSummary.memoryUsage > 90) {
      alerts.push({
        id: 'critical-memory',
        type: 'critical',
        title: 'Critical Memory Usage',
        message: `Memory usage at ${performanceSummary.memoryUsage.toFixed(1)}%, risk of system failure.`,
        metric: 'memory_usage',
        currentValue: performanceSummary.memoryUsage,
        threshold: 90,
        timestamp: now,
        source: 'Resource Monitor',
        icon: CpuChipIcon,
        actionable: true,
        recommendation: 'Restart services, clear caches, or scale up memory allocation.',
      });
    }

    if (performanceSummary.cacheHitRate < 70) {
      alerts.push({
        id: 'critical-cache-performance',
        type: 'critical',
        title: 'Poor Cache Performance',
        message: `Cache hit rate is ${performanceSummary.cacheHitRate.toFixed(1)}%, causing excessive database load.`,
        metric: 'cache_hit_rate',
        currentValue: performanceSummary.cacheHitRate,
        threshold: 70,
        timestamp: now,
        source: 'Cache Monitor',
        icon: CircleStackIcon,
        actionable: true,
        recommendation: 'Review cache configuration, warm up cache, or increase cache size.',
      });
    }

    // Warning alerts
    if (performanceSummary.averageResponseTime > 1000 && performanceSummary.averageResponseTime <= 2000) {
      alerts.push({
        id: 'warning-response-time',
        type: 'warning',
        title: 'Elevated Response Time',
        message: `Response time is ${Math.round(performanceSummary.averageResponseTime)}ms, above optimal performance.`,
        metric: 'response_time',
        currentValue: performanceSummary.averageResponseTime,
        threshold: 1000,
        timestamp: now,
        source: 'API Monitor',
        icon: ClockIcon,
        recommendation: 'Monitor closely and consider optimization if trend continues.',
      });
    }

    if (performanceSummary.slowQueries > 5) {
      alerts.push({
        id: 'warning-slow-queries',
        type: 'warning',
        title: 'Multiple Slow Queries',
        message: `${performanceSummary.slowQueries} queries are taking longer than 1 second to execute.`,
        metric: 'slow_queries',
        currentValue: performanceSummary.slowQueries,
        threshold: 5,
        timestamp: now,
        source: 'Database Monitor',
        icon: ServerIcon,
        actionable: true,
        recommendation: 'Review and optimize slow queries, add indexes, or update statistics.',
      });
    }

    if (performanceSummary.cacheHitRate < 85 && performanceSummary.cacheHitRate >= 70) {
      alerts.push({
        id: 'warning-cache-hit-rate',
        type: 'warning',
        title: 'Suboptimal Cache Performance',
        message: `Cache hit rate is ${performanceSummary.cacheHitRate.toFixed(1)}%, below optimal levels.`,
        metric: 'cache_hit_rate',
        currentValue: performanceSummary.cacheHitRate,
        threshold: 85,
        timestamp: now,
        source: 'Cache Monitor',
        icon: CircleStackIcon,
        recommendation: 'Review cache strategies and consider cache warming.',
      });
    }

    // Info alerts
    if (apiMetrics.p99ResponseTime > apiMetrics.averageResponseTime * 3) {
      alerts.push({
        id: 'info-response-variance',
        type: 'info',
        title: 'High Response Time Variance',
        message: `99th percentile response time (${Math.round(apiMetrics.p99ResponseTime)}ms) is significantly higher than average.`,
        metric: 'response_variance',
        currentValue: apiMetrics.p99ResponseTime,
        threshold: apiMetrics.averageResponseTime * 3,
        timestamp: now,
        source: 'Performance Monitor',
        icon: ChartBarIcon,
        recommendation: 'Investigate outlier requests and optimize worst-case scenarios.',
      });
    }

    // Success alerts
    if (alerts.length === 0) {
      alerts.push({
        id: 'performance-optimal',
        type: 'success',
        title: 'Optimal Performance',
        message: 'All performance metrics are within acceptable ranges.',
        metric: 'overall',
        currentValue: 100,
        threshold: 100,
        timestamp: now,
        source: 'Performance Monitor',
        icon: CheckCircleIcon,
      });
    }

    return alerts.sort((a, b) => {
      const priority = { critical: 4, warning: 3, info: 2, success: 1 };
      return priority[b.type] - priority[a.type];
    });
  };

  const alerts = generatePerformanceAlerts();

  const getAlertIcon = (alert: PerformanceAlert) => {
    const IconComponent = alert.icon;
    const iconClasses = {
      critical: 'h-5 w-5 text-red-500',
      warning: 'h-5 w-5 text-yellow-500',
      info: 'h-5 w-5 text-blue-500',
      success: 'h-5 w-5 text-green-500',
    };

    return <IconComponent className={iconClasses[alert.type]} />;
  };

  const getAlertBadge = (type: PerformanceAlert['type']) => {
    const variants = {
      critical: 'destructive',
      warning: 'secondary',
      info: 'outline',
      success: 'default',
    } as const;

    return (
      <Badge variant={variants[type]} className="text-xs">
        {type.toUpperCase()}
      </Badge>
    );
  };

  const getProgressColor = (type: PerformanceAlert['type']) => {
    switch (type) {
      case 'critical':
        return 'bg-red-500';
      case 'warning':
        return 'bg-yellow-500';
      case 'info':
        return 'bg-blue-500';
      case 'success':
        return 'bg-green-500';
      default:
        return 'bg-gray-500';
    }
  };

  const formatTimestamp = (timestamp: Date) => {
    return timestamp.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
    });
  };

  const handleAlertAction = (alertId: string) => {
    console.log(`Taking action for performance alert: ${alertId}`);
  };

  return (
    <div className={cn("space-y-6", className)}>
      {/* Performance Alert Summary */}
      <Card className="bg-white dark:bg-gray-800 rounded-lg shadow-soft">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <ChartBarIcon className="h-5 w-5" />
            Performance Alerts
          </CardTitle>
          <CardDescription>
            Performance-related alerts and recommendations
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
            <div className="text-center">
              <div className="text-2xl font-bold text-red-500">
                {alerts.filter(a => a.type === 'critical').length}
              </div>
              <div className="text-xs text-muted-foreground">Critical</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-yellow-500">
                {alerts.filter(a => a.type === 'warning').length}
              </div>
              <div className="text-xs text-muted-foreground">Warning</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-500">
                {alerts.filter(a => a.type === 'info').length}
              </div>
              <div className="text-xs text-muted-foreground">Info</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-500">
                {alerts.filter(a => a.type === 'success').length}
              </div>
              <div className="text-xs text-muted-foreground">Success</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Performance Alert List */}
      <div className="space-y-4">
        {alerts.map((alert) => (
          <Card key={alert.id} className="bg-white dark:bg-gray-800 rounded-lg shadow-soft">
            <CardContent className="pt-6">
              <div className="flex items-start justify-between">
                <div className="flex items-start space-x-3 flex-1">
                  {getAlertIcon(alert)}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-1">
                      <h4 className="text-sm font-semibold">{alert.title}</h4>
                      {getAlertBadge(alert.type)}
                    </div>
                    <p className="text-sm text-muted-foreground mb-2">
                      {alert.message}
                    </p>
                    
                    {/* Metric Progress Bar */}
                    {alert.type !== 'success' && (
                      <div className="mb-2">
                        <div className="flex justify-between text-xs mb-1">
                          <span>Current: {alert.currentValue.toFixed(1)}</span>
                          <span>Threshold: {alert.threshold}</span>
                        </div>
                        <Progress 
                          value={Math.min(100, (alert.currentValue / alert.threshold) * 100)}
                          className="h-2"
                        />
                      </div>
                    )}
                    
                    {alert.recommendation && (
                      <div className="text-xs text-blue-600 dark:text-blue-400 mb-2">
                        💡 {alert.recommendation}
                      </div>
                    )}
                    
                    <div className="flex items-center gap-4 text-xs text-muted-foreground">
                      <span>Source: {alert.source}</span>
                      <span>Time: {formatTimestamp(alert.timestamp)}</span>
                      <span>Metric: {alert.metric}</span>
                    </div>
                  </div>
                </div>
                {alert.actionable && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleAlertAction(alert.id)}
                    className="ml-4"
                  >
                    Take Action
                  </Button>
                )}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Performance Actions */}
      <Card className="bg-white dark:bg-gray-800 rounded-lg shadow-soft">
        <CardHeader>
          <CardTitle className="text-sm">Performance Actions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-2">
            <Button variant="outline" size="sm">
              Clear Performance Cache
            </Button>
            <Button variant="outline" size="sm">
              Restart Slow Services
            </Button>
            <Button variant="outline" size="sm">
              Optimize Database
            </Button>
            <Button variant="outline" size="sm">
              Scale Resources
            </Button>
            <Button variant="outline" size="sm">
              Export Performance Report
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
