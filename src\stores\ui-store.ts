'use client';

import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import React from 'react';

interface UIState {
  // Sidebar state
  sidebarCollapsed: boolean;
  sidebarOpen: boolean; // For mobile

  // Navigation state - minimal tracking only
  currentPage: string;
  previousPage: string;

  // Loading states - only API loading
  apiLoading: boolean;

  // Actions
  toggleSidebar: () => void;
  setSidebarCollapsed: (collapsed: boolean) => void;
  setSidebarOpen: (open: boolean) => void;

  // Navigation actions - minimal
  setCurrentPage: (page: string) => void;
  setApiLoading: (loading: boolean) => void;

  // Responsive breakpoint tracking
  isMobile: boolean;
  setIsMobile: (mobile: boolean) => void;
}

export const useUIStore = create<UIState>()(
  persist(
    (set, get) => ({
      // Initial state
      sidebarCollapsed: false,
      sidebarOpen: false,
      isMobile: false,
      currentPage: '',
      previousPage: '',
      apiLoading: false,

      // Actions
      toggleSidebar: () => {
        const { isMobile } = get();
        if (isMobile) {
          set((state) => ({ sidebarOpen: !state.sidebarOpen }));
        } else {
          set((state) => ({ sidebarCollapsed: !state.sidebarCollapsed }));
        }
      },

      setSidebarCollapsed: (collapsed: boolean) => {
        set({ sidebarCollapsed: collapsed });
      },

      setSidebarOpen: (open: boolean) => {
        set({ sidebarOpen: open });
      },

      setIsMobile: (mobile: boolean) => {
        set({ isMobile: mobile });
        // Close mobile sidebar when switching to desktop
        if (!mobile) {
          set({ sidebarOpen: false });
        }
      },

      // Minimal navigation actions
      setCurrentPage: (page: string) => {
        const state = get();
        // Only update if page actually changes
        if (state.currentPage !== page) {
          set({
            previousPage: state.currentPage,
            currentPage: page
          });
        }
      },

      setApiLoading: (loading: boolean) => {
        const state = get();
        // Only update if state actually changes
        if (state.apiLoading !== loading) {
          set({ apiLoading: loading });
        }
      },
    }),
    {
      name: 'ui-store',
      // Only persist sidebar collapsed state, not mobile states
      partialize: (state) => ({ 
        sidebarCollapsed: state.sidebarCollapsed 
      }),
    }
  )
);

// Hook for responsive breakpoint detection
export function useResponsive() {
  const { isMobile, setIsMobile } = useUIStore();

  React.useEffect(() => {
    const checkIsMobile = () => {
      const mobile = window.innerWidth < 1024; // lg breakpoint
      if (mobile !== isMobile) {
        setIsMobile(mobile);
      }
    };

    // Check on mount
    checkIsMobile();

    // Listen for resize events
    window.addEventListener('resize', checkIsMobile);
    return () => window.removeEventListener('resize', checkIsMobile);
  }, [isMobile, setIsMobile]);

  return { isMobile };
}
