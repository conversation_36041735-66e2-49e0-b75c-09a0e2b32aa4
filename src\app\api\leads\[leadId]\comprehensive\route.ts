import { NextResponse } from 'next/server';
import { withPermission, AuthenticatedRequest } from '@/lib/permission-middleware';
import { PermissionResource, PermissionAction } from '@/types';
import { leadManagementService } from '@/services/lead-management';

/**
 * GET /api/leads/[leadId]/comprehensive
 * Get comprehensive lead data including activities, emails, and calls
 */
export const GET = withPermission({
  resource: PermissionResource.LEADS,
  action: PermissionAction.READ,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest) => {
  try {
    const url = new URL(req.url);
    const pathSegments = url.pathname.split('/');
    const leadId = pathSegments[pathSegments.indexOf('leads') + 1];

    if (!leadId) {
      return NextResponse.json(
        { error: 'Lead ID is required' },
        { status: 400 }
      );
    }

    const lead = await leadManagementService.getLeadWithComprehensiveData(leadId, req.tenantId);

    if (!lead) {
      return NextResponse.json(
        { error: 'Lead not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: { lead },
      message: 'Comprehensive lead data retrieved successfully'
    });

  } catch (error) {
    console.error('Error fetching comprehensive lead data:', error);
    return NextResponse.json(
      { error: 'Failed to fetch comprehensive lead data' },
      { status: 500 }
    );
  }
});
