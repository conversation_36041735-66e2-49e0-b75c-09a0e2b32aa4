import { NextRequest, NextResponse } from 'next/server';
import { withSuperAdminAuth } from '@/lib/tenant-context';
import { SuperAdminService } from '@/services/super-admin';

/**
 * @swagger
 * /api/admin/platform/analytics/activity:
 *   get:
 *     summary: Get real-time activity feed (Super Admin only)
 *     description: Retrieve recent platform activity and events
 *     tags:
 *       - Super Admin - Analytics
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 50
 *         description: Maximum number of activities to retrieve
 *     responses:
 *       200:
 *         description: Activity feed retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: string
 *                       type:
 *                         type: string
 *                         enum: [user_registration, tenant_created, subscription_change, system_alert, error]
 *                       title:
 *                         type: string
 *                       description:
 *                         type: string
 *                       timestamp:
 *                         type: string
 *                         format: date-time
 *                       severity:
 *                         type: string
 *                         enum: [low, medium, high, critical]
 *                       tenantId:
 *                         type: string
 *                         nullable: true
 *                       userId:
 *                         type: string
 *                         nullable: true
 *                       metadata:
 *                         type: object
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Super Admin access required
 */
export const GET = withSuperAdminAuth(async (request: NextRequest) => {
  try {
    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '50');

    // Validate limit parameter
    if (limit < 1 || limit > 200) {
      return NextResponse.json(
        { error: 'Limit parameter must be between 1 and 200' },
        { status: 400 }
      );
    }

    const superAdminService = new SuperAdminService();
    const activities = await superAdminService.getActivityFeed(limit);

    return NextResponse.json({
      success: true,
      data: activities
    });

  } catch (error) {
    console.error('Get activity feed error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
});
