import { useState, useEffect } from 'react';
import { LeadSource, LeadSourceCategoryInfo } from '@/constants/lead-sources';

interface LeadSourcesResponse {
  sources: (LeadSource & { usage: number })[];
  categories: LeadSourceCategoryInfo[];
  totalSources: number;
  customSourcesCount: number;
}

interface UseLeadSourcesReturn {
  sources: (LeadSource & { usage: number })[];
  categories: LeadSourceCategoryInfo[];
  isLoading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
  totalSources: number;
  customSourcesCount: number;
}

export function useLeadSources(): UseLeadSourcesReturn {
  const [sources, setSources] = useState<(LeadSource & { usage: number })[]>([]);
  const [categories, setCategories] = useState<LeadSourceCategoryInfo[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [totalSources, setTotalSources] = useState(0);
  const [customSourcesCount, setCustomSourcesCount] = useState(0);

  const fetchLeadSources = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch('/api/leads/sources');
      
      if (!response.ok) {
        throw new Error(`Failed to fetch lead sources: ${response.status}`);
      }

      const data = await response.json();
      
      if (!data.success) {
        throw new Error(data.error || 'Failed to fetch lead sources');
      }

      const responseData: LeadSourcesResponse = data.data;
      
      setSources(responseData.sources);
      setCategories(responseData.categories);
      setTotalSources(responseData.totalSources);
      setCustomSourcesCount(responseData.customSourcesCount);

    } catch (err) {
      console.error('Error fetching lead sources:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch lead sources');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchLeadSources();
  }, []);

  return {
    sources,
    categories,
    isLoading,
    error,
    refetch: fetchLeadSources,
    totalSources,
    customSourcesCount
  };
}

// Hook for getting sources by category
export function useLeadSourcesByCategory(category?: string) {
  const { sources, isLoading, error } = useLeadSources();
  
  const filteredSources = category 
    ? sources.filter(source => source.category === category)
    : sources;

  return {
    sources: filteredSources,
    isLoading,
    error
  };
}

// Hook for getting popular/most used sources
export function usePopularLeadSources(limit: number = 6) {
  const { sources, isLoading, error } = useLeadSources();
  
  const popularSources = sources
    .sort((a, b) => b.usage - a.usage)
    .slice(0, limit);

  return {
    sources: popularSources,
    isLoading,
    error
  };
}
