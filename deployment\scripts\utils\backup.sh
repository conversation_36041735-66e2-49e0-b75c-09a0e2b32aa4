#!/bin/bash
# =============================================================================
# CRM Platform Backup Script
# Comprehensive backup solution for database, uploads, and configurations
# =============================================================================

set -euo pipefail

# -----------------------------------------------------------------------------
# Configuration
# -----------------------------------------------------------------------------
readonly SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
readonly PROJECT_ROOT="$(cd "${SCRIPT_DIR}/../../.." && pwd)"
readonly DEPLOYMENT_DIR="$(cd "${SCRIPT_DIR}/../.." && pwd)"

COMPOSE_FILE="${DEPLOYMENT_DIR}/docker/docker-compose.new.yml"
BACKUP_DIR="${BACKUP_DIR:-${PROJECT_ROOT}/backups}"
ENVIRONMENT="${1:-development}"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
BACKUP_NAME="crm_backup_${ENVIRONMENT}_${TIMESTAMP}"

# Colors for output
readonly RED='\033[0;31m'
readonly GREEN='\033[0;32m'
readonly YELLOW='\033[1;33m'
readonly BLUE='\033[0;34m'
readonly NC='\033[0m'

# -----------------------------------------------------------------------------
# Utility Functions
# -----------------------------------------------------------------------------
print_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
print_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
print_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
print_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# -----------------------------------------------------------------------------
# Backup Functions
# -----------------------------------------------------------------------------

# Create backup directory structure
setup_backup_directory() {
  print_info "Setting up backup directory..."
  
  local backup_path="${BACKUP_DIR}/${BACKUP_NAME}"
  
  mkdir -p "${backup_path}/database"
  mkdir -p "${backup_path}/uploads"
  mkdir -p "${backup_path}/config"
  mkdir -p "${backup_path}/logs"
  
  echo "$backup_path"
}

# Backup PostgreSQL database
backup_database() {
  local backup_path="$1"
  
  print_info "Backing up PostgreSQL database..."
  
  cd "$PROJECT_ROOT"
  
  # Create database dump
  if docker-compose -f "$COMPOSE_FILE" exec -T postgres pg_dump -U admin -d crm_platform > "${backup_path}/database/crm_platform.sql"; then
    print_success "Database backup completed"
    
    # Compress the SQL dump
    gzip "${backup_path}/database/crm_platform.sql"
    print_success "Database backup compressed"
  else
    print_error "Database backup failed"
    return 1
  fi
  
  # Backup database schema only
  if docker-compose -f "$COMPOSE_FILE" exec -T postgres pg_dump -U admin -d crm_platform --schema-only > "${backup_path}/database/schema.sql"; then
    print_success "Schema backup completed"
  else
    print_warning "Schema backup failed"
  fi
}

# Backup uploaded files
backup_uploads() {
  local backup_path="$1"
  
  print_info "Backing up uploaded files..."
  
  local uploads_dir="${PROJECT_ROOT}/uploads"
  
  if [[ -d "$uploads_dir" ]] && [[ "$(ls -A "$uploads_dir" 2>/dev/null)" ]]; then
    if tar -czf "${backup_path}/uploads/uploads.tar.gz" -C "$uploads_dir" .; then
      print_success "Uploads backup completed"
    else
      print_error "Uploads backup failed"
      return 1
    fi
  else
    print_warning "No uploads directory found or directory is empty"
    touch "${backup_path}/uploads/no_uploads.txt"
  fi
}

# Backup configuration files
backup_config() {
  local backup_path="$1"
  
  print_info "Backing up configuration files..."
  
  # Backup deployment configuration
  cp -r "${DEPLOYMENT_DIR}/config" "${backup_path}/config/deployment"
  
  # Backup environment files
  if [[ -f "${PROJECT_ROOT}/.env.docker" ]]; then
    cp "${PROJECT_ROOT}/.env.docker" "${backup_path}/config/"
  fi
  
  if [[ -f "${PROJECT_ROOT}/.env.local" ]]; then
    cp "${PROJECT_ROOT}/.env.local" "${backup_path}/config/"
  fi
  
  # Backup package.json and lock files
  cp "${PROJECT_ROOT}/package.json" "${backup_path}/config/"
  if [[ -f "${PROJECT_ROOT}/package-lock.json" ]]; then
    cp "${PROJECT_ROOT}/package-lock.json" "${backup_path}/config/"
  fi
  
  # Backup Prisma schema
  if [[ -d "${PROJECT_ROOT}/prisma" ]]; then
    cp -r "${PROJECT_ROOT}/prisma" "${backup_path}/config/"
  fi
  
  print_success "Configuration backup completed"
}

# Backup Docker volumes
backup_volumes() {
  local backup_path="$1"
  
  print_info "Backing up Docker volumes..."
  
  cd "$PROJECT_ROOT"
  
  # Backup PostgreSQL data volume
  if docker volume inspect crm-postgres-data > /dev/null 2>&1; then
    docker run --rm -v crm-postgres-data:/data -v "${backup_path}/database":/backup alpine tar czf /backup/postgres_volume.tar.gz -C /data .
    print_success "PostgreSQL volume backup completed"
  else
    print_warning "PostgreSQL volume not found"
  fi
  
  # Backup Redis data volume
  if docker volume inspect crm-redis-data > /dev/null 2>&1; then
    docker run --rm -v crm-redis-data:/data -v "${backup_path}/database":/backup alpine tar czf /backup/redis_volume.tar.gz -C /data .
    print_success "Redis volume backup completed"
  else
    print_warning "Redis volume not found"
  fi
}

# Backup application logs
backup_logs() {
  local backup_path="$1"
  
  print_info "Backing up application logs..."
  
  cd "$PROJECT_ROOT"
  
  # Get container logs
  local containers=("crm-app" "crm-postgres" "crm-redis")
  
  for container in "${containers[@]}"; do
    if docker ps -a --format '{{.Names}}' | grep -q "^${container}$"; then
      docker logs "$container" > "${backup_path}/logs/${container}.log" 2>&1
      print_success "Logs for $container backed up"
    else
      print_warning "Container $container not found"
    fi
  done
}

# Create backup manifest
create_manifest() {
  local backup_path="$1"
  
  print_info "Creating backup manifest..."
  
  local manifest_file="${backup_path}/backup_manifest.txt"
  
  {
    echo "CRM Platform Backup Manifest"
    echo "============================"
    echo "Backup Name: $BACKUP_NAME"
    echo "Environment: $ENVIRONMENT"
    echo "Timestamp: $(date)"
    echo "Backup Path: $backup_path"
    echo ""
    echo "Contents:"
    find "$backup_path" -type f -exec ls -lh {} \; | awk '{print $9, $5}'
    echo ""
    echo "Total Size: $(du -sh "$backup_path" | cut -f1)"
  } > "$manifest_file"
  
  print_success "Backup manifest created"
}

# Cleanup old backups
cleanup_old_backups() {
  print_info "Cleaning up old backups..."
  
  local retention_days="${RETENTION_DAYS:-7}"
  
  if [[ -d "$BACKUP_DIR" ]]; then
    find "$BACKUP_DIR" -name "crm_backup_${ENVIRONMENT}_*" -type d -mtime +$retention_days -exec rm -rf {} \; 2>/dev/null || true
    print_success "Old backups cleaned up (retention: ${retention_days} days)"
  fi
}

# Verify backup integrity
verify_backup() {
  local backup_path="$1"
  
  print_info "Verifying backup integrity..."
  
  local errors=0
  
  # Check if database backup exists and is not empty
  if [[ -f "${backup_path}/database/crm_platform.sql.gz" ]]; then
    if [[ -s "${backup_path}/database/crm_platform.sql.gz" ]]; then
      print_success "Database backup verified"
    else
      print_error "Database backup is empty"
      ((errors++))
    fi
  else
    print_error "Database backup not found"
    ((errors++))
  fi
  
  # Check if manifest exists
  if [[ -f "${backup_path}/backup_manifest.txt" ]]; then
    print_success "Backup manifest verified"
  else
    print_error "Backup manifest not found"
    ((errors++))
  fi
  
  if [[ $errors -eq 0 ]]; then
    print_success "Backup verification passed"
    return 0
  else
    print_error "Backup verification failed with $errors errors"
    return 1
  fi
}

# -----------------------------------------------------------------------------
# Main Function
# -----------------------------------------------------------------------------

main() {
  print_info "💾 CRM Platform Backup"
  print_info "======================"
  print_info "Environment: $ENVIRONMENT"
  print_info "Backup name: $BACKUP_NAME"
  
  # Setup backup directory
  local backup_path
  backup_path=$(setup_backup_directory)
  
  print_info "Backup path: $backup_path"
  
  # Perform backups
  backup_database "$backup_path"
  backup_uploads "$backup_path"
  backup_config "$backup_path"
  backup_volumes "$backup_path"
  backup_logs "$backup_path"
  
  # Create manifest and verify
  create_manifest "$backup_path"
  verify_backup "$backup_path"
  
  # Cleanup old backups
  cleanup_old_backups
  
  print_success "🎉 Backup completed successfully!"
  print_info "Backup location: $backup_path"
  print_info "Backup size: $(du -sh "$backup_path" | cut -f1)"
}

# -----------------------------------------------------------------------------
# Usage
# -----------------------------------------------------------------------------

usage() {
  cat << EOF
Usage: $0 [ENVIRONMENT] [OPTIONS]

ENVIRONMENTS:
  development    Backup development environment (default)
  staging        Backup staging environment
  production     Backup production environment

OPTIONS:
  --backup-dir   Custom backup directory (default: ./backups)
  --retention    Retention period in days (default: 7)
  --help         Show this help message

EXAMPLES:
  $0                                    # Backup development
  $0 production                         # Backup production
  $0 --backup-dir /custom/path          # Custom backup location
  $0 production --retention 30          # Keep backups for 30 days

ENVIRONMENT VARIABLES:
  BACKUP_DIR=/path/to/backups          # Custom backup directory
  RETENTION_DAYS=30                    # Backup retention period

EOF
  exit 1
}

# Parse arguments
while [[ $# -gt 0 ]]; do
  case $1 in
    --backup-dir)
      BACKUP_DIR="$2"
      shift 2
      ;;
    --retention)
      RETENTION_DAYS="$2"
      shift 2
      ;;
    --help|-h)
      usage
      ;;
    -*)
      print_error "Unknown option: $1"
      usage
      ;;
    *)
      ENVIRONMENT="$1"
      shift
      ;;
  esac
done

# Run main function
main
