'use client';

import { useLocale } from '@/components/providers/locale-provider';
import { useMemo } from 'react';

/**
 * Hook for RTL-aware utilities and helpers
 */
export function useRTL() {
  const { locale, isRTL, sidebarSide } = useLocale();

  const rtlUtils = useMemo(() => ({
    // Text alignment utilities
    textAlign: isRTL ? 'text-right' : 'text-left',
    textAlignReverse: isRTL ? 'text-left' : 'text-right',
    
    // Margin utilities
    marginLeft: isRTL ? 'mr-' : 'ml-',
    marginRight: isRTL ? 'ml-' : 'mr-',
    marginStart: isRTL ? 'mr-' : 'ml-',
    marginEnd: isRTL ? 'ml-' : 'mr-',
    
    // Padding utilities
    paddingLeft: isRTL ? 'pr-' : 'pl-',
    paddingRight: isRTL ? 'pl-' : 'pr-',
    paddingStart: isRTL ? 'pr-' : 'pl-',
    paddingEnd: isRTL ? 'pl-' : 'pr-',
    
    // Positioning utilities
    left: isRTL ? 'right-' : 'left-',
    right: isRTL ? 'left-' : 'right-',
    start: isRTL ? 'right-' : 'left-',
    end: isRTL ? 'left-' : 'right-',
    
    // Flex utilities
    flexDirection: isRTL ? 'flex-row-reverse' : 'flex-row',
    justifyStart: isRTL ? 'justify-end' : 'justify-start',
    justifyEnd: isRTL ? 'justify-start' : 'justify-end',
    
    // Border utilities
    borderLeft: isRTL ? 'border-r' : 'border-l',
    borderRight: isRTL ? 'border-l' : 'border-r',
    borderStart: isRTL ? 'border-r' : 'border-l',
    borderEnd: isRTL ? 'border-l' : 'border-r',
    
    // Transform utilities
    translateX: (value: string) => isRTL ? `translate-x-${value}` : `translate-x-${value}`,
    scaleX: isRTL ? 'scale-x-[-1]' : 'scale-x-1',
    
    // Icon rotation for directional icons
    iconRotation: isRTL ? 'rotate-180' : '',
    
    // Dropdown positioning
    dropdownAlign: isRTL ? 'start' : 'end',
    dropdownSide: isRTL ? 'left' : 'right',
  }), [isRTL]);

  /**
   * Generate RTL-aware class names
   */
  const rtlClass = (ltrClass: string, rtlClass?: string) => {
    if (rtlClass) {
      return isRTL ? rtlClass : ltrClass;
    }
    return ltrClass;
  };

  /**
   * Generate conditional RTL classes
   */
  const conditionalRTL = (baseClass: string, rtlModifier: string) => {
    return `${baseClass} ${isRTL ? rtlModifier : ''}`;
  };

  /**
   * Get appropriate spacing class for RTL
   */
  const getSpacingClass = (property: 'margin' | 'padding', side: 'left' | 'right' | 'start' | 'end', value: string) => {
    const prefix = property === 'margin' ? 'm' : 'p';
    
    switch (side) {
      case 'left':
        return isRTL ? `${prefix}r-${value}` : `${prefix}l-${value}`;
      case 'right':
        return isRTL ? `${prefix}l-${value}` : `${prefix}r-${value}`;
      case 'start':
        return isRTL ? `${prefix}r-${value}` : `${prefix}l-${value}`;
      case 'end':
        return isRTL ? `${prefix}l-${value}` : `${prefix}r-${value}`;
      default:
        return `${prefix}${side}-${value}`;
    }
  };

  /**
   * Get appropriate positioning class for RTL
   */
  const getPositionClass = (side: 'left' | 'right' | 'start' | 'end', value: string) => {
    switch (side) {
      case 'left':
        return isRTL ? `right-${value}` : `left-${value}`;
      case 'right':
        return isRTL ? `left-${value}` : `right-${value}`;
      case 'start':
        return isRTL ? `right-${value}` : `left-${value}`;
      case 'end':
        return isRTL ? `left-${value}` : `right-${value}`;
      default:
        return `${side}-${value}`;
    }
  };

  return {
    locale,
    isRTL,
    sidebarSide,
    rtlUtils,
    rtlClass,
    conditionalRTL,
    getSpacingClass,
    getPositionClass,
  };
}

/**
 * Hook for RTL-aware animations - removed page transition animations
 */
export function useRTLAnimations() {
  const { isRTL } = useLocale();

  return {
    // Removed page transition animations
    isRTL,
  };
}
