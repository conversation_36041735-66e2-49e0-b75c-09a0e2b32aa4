'use client';

import { 
  UserPlusIcon,
  PhoneIcon,
  EnvelopeIcon,
  CalendarIcon,
  DocumentTextIcon,
  CheckCircleIcon,
  ClockIcon
} from '@heroicons/react/24/outline';
import { cn, formatRelativeTime } from '@/lib/utils';

interface Activity {
  id: string;
  type: 'contact_created' | 'call_made' | 'email_sent' | 'meeting_scheduled' | 'proposal_sent' | 'deal_closed' | 'task_completed';
  title: string;
  description: string;
  user: {
    name: string;
    avatar?: string;
  };
  createdAt: Date;
  metadata?: {
    contact?: string;
    company?: string;
    amount?: number;
  };
}

const activityIcons = {
  contact_created: UserPlusIcon,
  call_made: PhoneIcon,
  email_sent: EnvelopeIcon,
  meeting_scheduled: CalendarIcon,
  proposal_sent: DocumentTextIcon,
  deal_closed: CheckCircleIcon,
  task_completed: CheckCircleIcon,
};

const activityColors = {
  contact_created: 'text-blue-600 bg-blue-50 dark:text-blue-400 dark:bg-blue-900/20',
  call_made: 'text-green-600 bg-green-50 dark:text-green-400 dark:bg-green-900/20',
  email_sent: 'text-purple-600 bg-purple-50 dark:text-purple-400 dark:bg-purple-900/20',
  meeting_scheduled: 'text-orange-600 bg-orange-50 dark:text-orange-400 dark:bg-orange-900/20',
  proposal_sent: 'text-indigo-600 bg-indigo-50 dark:text-indigo-400 dark:bg-indigo-900/20',
  deal_closed: 'text-emerald-600 bg-emerald-50 dark:text-emerald-400 dark:bg-emerald-900/20',
  task_completed: 'text-teal-600 bg-teal-50 dark:text-teal-400 dark:bg-teal-900/20',
};

// Mock data
const mockActivities: Activity[] = [
  {
    id: '1',
    type: 'deal_closed',
    title: 'Deal closed',
    description: 'Closed deal with ABC Corporation',
    user: { name: 'John Doe' },
    createdAt: new Date(Date.now() - 5 * 60 * 1000), // 5 minutes ago
    metadata: { company: 'ABC Corporation', amount: 15000 },
  },
  {
    id: '2',
    type: 'meeting_scheduled',
    title: 'Meeting scheduled',
    description: 'Scheduled follow-up meeting with Sarah Johnson',
    user: { name: 'Jane Smith' },
    createdAt: new Date(Date.now() - 15 * 60 * 1000), // 15 minutes ago
    metadata: { contact: 'Sarah Johnson' },
  },
  {
    id: '3',
    type: 'proposal_sent',
    title: 'Proposal sent',
    description: 'Sent proposal for website redesign project',
    user: { name: 'Mike Wilson' },
    createdAt: new Date(Date.now() - 30 * 60 * 1000), // 30 minutes ago
    metadata: { company: 'Tech Startup Inc' },
  },
  {
    id: '4',
    type: 'call_made',
    title: 'Call completed',
    description: 'Had discovery call with potential client',
    user: { name: 'Sarah Davis' },
    createdAt: new Date(Date.now() - 45 * 60 * 1000), // 45 minutes ago
    metadata: { contact: 'Robert Brown' },
  },
  {
    id: '5',
    type: 'contact_created',
    title: 'New contact added',
    description: 'Added new contact from LinkedIn',
    user: { name: 'Tom Anderson' },
    createdAt: new Date(Date.now() - 60 * 60 * 1000), // 1 hour ago
    metadata: { contact: 'Lisa Chen' },
  },
  {
    id: '6',
    type: 'email_sent',
    title: 'Follow-up email sent',
    description: 'Sent follow-up email to warm lead',
    user: { name: 'Emily Johnson' },
    createdAt: new Date(Date.now() - 90 * 60 * 1000), // 1.5 hours ago
    metadata: { contact: 'David Miller' },
  },
];

function getInitials(name: string): string {
  return name
    .split(' ')
    .map(word => word.charAt(0))
    .join('')
    .toUpperCase()
    .slice(0, 2);
}

export function RecentActivities() {
  return (
    <div className="bg-white dark:bg-gray-800 shadow rounded-lg">
      <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            Recent Activities
          </h3>
          <button className="text-sm text-primary-600 hover:text-primary-700 dark:text-primary-400 dark:hover:text-primary-300 font-medium">
            View all
          </button>
        </div>
      </div>

      <div className="flow-root">
        <ul role="list" className="divide-y divide-gray-200 dark:divide-gray-700">
          {mockActivities.map((activity, index) => {
            const Icon = activityIcons[activity.type];
            const colorClasses = activityColors[activity.type];
            
            return (
              <li key={activity.id} className="px-6 py-4">
                <div className="flex items-start space-x-3">
                  {/* Activity Icon */}
                  <div className={cn(
                    'flex-shrink-0 p-2 rounded-full',
                    colorClasses.split(' ').slice(2).join(' ') // Get background classes
                  )}>
                    <Icon className={cn(
                      'h-4 w-4',
                      colorClasses.split(' ').slice(0, 2).join(' ') // Get text color classes
                    )} />
                  </div>

                  {/* Activity Content */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between">
                      <p className="text-sm font-medium text-gray-900 dark:text-white">
                        {activity.title}
                      </p>
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        {formatRelativeTime(activity.createdAt)}
                      </p>
                    </div>
                    
                    <p className="text-sm text-gray-600 dark:text-gray-300 mt-1">
                      {activity.description}
                    </p>

                    {/* Metadata */}
                    {activity.metadata && (
                      <div className="mt-2 flex items-center space-x-4 text-xs text-gray-500 dark:text-gray-400">
                        {activity.metadata.contact && (
                          <span>Contact: {activity.metadata.contact}</span>
                        )}
                        {activity.metadata.company && (
                          <span>Company: {activity.metadata.company}</span>
                        )}
                        {activity.metadata.amount && (
                          <span className="text-green-600 dark:text-green-400 font-medium">
                            ${activity.metadata.amount.toLocaleString()}
                          </span>
                        )}
                      </div>
                    )}

                    {/* User */}
                    <div className="mt-2 flex items-center space-x-2">
                      <div className="h-6 w-6 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center">
                        <span className="text-xs font-medium text-gray-700 dark:text-gray-300">
                          {getInitials(activity.user.name)}
                        </span>
                      </div>
                      <span className="text-xs text-gray-500 dark:text-gray-400">
                        by {activity.user.name}
                      </span>
                    </div>
                  </div>
                </div>
              </li>
            );
          })}
        </ul>
      </div>

      {/* Footer */}
      <div className="px-6 py-3 bg-gray-50 dark:bg-gray-700 border-t border-gray-200 dark:border-gray-600">
        <div className="flex items-center justify-between">
          <span className="text-xs text-gray-500 dark:text-gray-400">
            Showing {mockActivities.length} recent activities
          </span>
          <button className="text-xs text-primary-600 hover:text-primary-700 dark:text-primary-400 dark:hover:text-primary-300 font-medium">
            Load more
          </button>
        </div>
      </div>
    </div>
  );
}
