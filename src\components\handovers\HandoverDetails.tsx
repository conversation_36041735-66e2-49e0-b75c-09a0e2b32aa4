'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Calendar, 
  Clock, 
  Users, 
  Building2, 
  User, 
  FileText, 
  MessageSquare,
  CheckCircle,
  AlertCircle,
  XCircle,
  Edit,
  Play,
  Pause,
  RotateCcw,
  ExternalLink,
  Star
} from 'lucide-react';
import { format } from 'date-fns';
import HandoverChecklist from './HandoverChecklist';
import HandoverDocuments from './HandoverDocuments';
import HandoverQA from './HandoverQA';

// Types
interface HandoverDetails {
  id: string;
  tenantId: string;
  opportunityId: string;
  templateId?: string;
  title: string;
  description?: string;
  status: string;
  priority: string;
  salesRepId?: string;
  deliveryManagerId?: string;
  assignedTeamIds: string[];
  checklistProgress: number;
  documentsCount: number;
  qaThreadsCount: number;
  scheduledDate?: string;
  startedAt?: string;
  completedAt?: string;
  estimatedDuration?: number;
  actualDuration?: number;
  qualityScore?: number;
  clientSatisfaction?: number;
  deliveryFeedback?: string;
  createdById?: string;
  createdAt: string;
  updatedAt: string;
  
  // Relations
  opportunity: {
    id: string;
    title: string;
    value?: number;
    stage: string;
    contact?: {
      id: string;
      firstName: string;
      lastName: string;
      email?: string;
    };
    company?: {
      id: string;
      name: string;
    };
  };
  template?: {
    id: string;
    name: string;
    description?: string;
  };
  salesRep?: {
    id: string;
    firstName?: string;
    lastName?: string;
    email: string;
  };
  deliveryManager?: {
    id: string;
    firstName?: string;
    lastName?: string;
    email: string;
  };
  checklistItems: Array<{
    id: string;
    title: string;
    description?: string;
    category: string;
    orderIndex: number;
    isRequired: boolean;
    isCompleted: boolean;
    completedAt?: string;
    completedBy?: {
      id: string;
      firstName?: string;
      lastName?: string;
    };
    notes?: string;
    dependsOnIds: string[];
  }>;
  qaThreads: Array<{
    id: string;
    title: string;
    category: string;
    priority: string;
    status: string;
    createdBy: {
      id: string;
      firstName?: string;
      lastName?: string;
    };
    assignedTo?: {
      id: string;
      firstName?: string;
      lastName?: string;
    };
    responseTime?: number;
    isEscalated: boolean;
    createdAt: string;
    _count: {
      messages: number;
    };
  }>;
}

interface HandoverDetailsProps {
  handoverId: string;
}

// Status and priority configurations
const statusConfig = {
  pending: { label: 'Pending', color: 'bg-yellow-100 text-yellow-800', icon: Clock },
  in_progress: { label: 'In Progress', color: 'bg-blue-100 text-blue-800', icon: AlertCircle },
  completed: { label: 'Completed', color: 'bg-green-100 text-green-800', icon: CheckCircle },
  cancelled: { label: 'Cancelled', color: 'bg-red-100 text-red-800', icon: XCircle },
};

const priorityConfig = {
  low: { label: 'Low', color: 'bg-gray-100 text-gray-800' },
  medium: { label: 'Medium', color: 'bg-blue-100 text-blue-800' },
  high: { label: 'High', color: 'bg-orange-100 text-orange-800' },
  urgent: { label: 'Urgent', color: 'bg-red-100 text-red-800' },
};

export default function HandoverDetails({ handoverId }: HandoverDetailsProps) {
  const router = useRouter();
  const [handover, setHandover] = useState<HandoverDetails | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [updating, setUpdating] = useState(false);
  const [activeTab, setActiveTab] = useState('overview');
  const [refreshKey, setRefreshKey] = useState(0);

  // Fetch handover details
  const fetchHandover = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`/api/handovers/${handoverId}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch handover details');
      }

      const data = await response.json();
      setHandover(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchHandover();
  }, [handoverId, refreshKey]);

  // Function to refresh handover data (can be called from child components)
  const refreshHandover = () => {
    setRefreshKey(prev => prev + 1);
  };

  // Update handover status
  const updateStatus = async (newStatus: string) => {
    if (!handover) return;

    try {
      setUpdating(true);
      
      const response = await fetch(`/api/handovers/${handoverId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status: newStatus }),
      });

      if (!response.ok) {
        throw new Error('Failed to update handover status');
      }

      const updatedHandover = await response.json();
      setHandover(updatedHandover);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update status');
    } finally {
      setUpdating(false);
    }
  };

  // Finalize handover and create project
  const finalizeHandover = async () => {
    if (!handover) return;

    try {
      setUpdating(true);
      
      const response = await fetch(`/api/handovers/${handoverId}/finalize`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          projectName: `${handover.opportunity.title} - Project`,
          clientPortalEnabled: true,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to finalize handover');
      }

      const result = await response.json();

      // Show appropriate success message
      if (result.isNewProject) {
        // Show success toast for new project
        console.log('New project created successfully');
      } else {
        // Show success toast for existing project
        console.log('Handover completed and linked to existing project');
      }

      // Refresh handover data
      await fetchHandover();

      // Navigate to the project
      if (result.project) {
        router.push(`/projects/${result.project.id}`);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to finalize handover');
    } finally {
      setUpdating(false);
    }
  };

  const getStatusBadge = (status: string) => {
    const config = statusConfig[status as keyof typeof statusConfig];
    if (!config) return null;

    const Icon = config.icon;
    return (
      <Badge className={config.color}>
        <Icon className="w-3 h-3 mr-1" />
        {config.label}
      </Badge>
    );
  };

  const getPriorityBadge = (priority: string) => {
    const config = priorityConfig[priority as keyof typeof priorityConfig];
    if (!config) return null;

    return (
      <Badge variant="outline" className={config.color}>
        {config.label}
      </Badge>
    );
  };

  const formatCurrency = (value?: number) => {
    if (!value) return 'N/A';
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
    }).format(value);
  };

  const getUserDisplayName = (user?: { firstName?: string; lastName?: string; email?: string }) => {
    if (!user) return 'Not assigned';
    return user.firstName && user.lastName 
      ? `${user.firstName} ${user.lastName}`
      : user.email || 'Unknown';
  };

  const getStatusActions = () => {
    if (!handover) return [];

    const actions = [];

    if (handover.status === 'pending') {
      actions.push({
        label: 'Start Handover',
        icon: Play,
        action: () => updateStatus('in_progress'),
        variant: 'default' as const
      });
    }

    if (handover.status === 'in_progress') {
      actions.push({
        label: 'Pause Handover',
        icon: Pause,
        action: () => updateStatus('pending'),
        variant: 'outline' as const
      });

      // Add Finish Handover button (always available when in progress)
      actions.push({
        label: 'Finish Handover',
        icon: CheckCircle,
        action: () => updateStatus('completed'),
        variant: 'default' as const
      });

      // Add Complete & Create Project button only when checklist is sufficiently complete
      if (handover.checklistProgress >= 80) {
        actions.push({
          label: 'Complete & Create Project',
          icon: CheckCircle,
          action: finalizeHandover,
          variant: 'default' as const
        });
      }
    }

    if (handover.status === 'completed') {
      actions.push({
        label: 'Reopen Handover',
        icon: RotateCcw,
        action: () => updateStatus('in_progress'),
        variant: 'outline' as const
      });
    }

    return actions;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-sm text-gray-600">Loading handover details...</p>
        </div>
      </div>
    );
  }

  if (error || !handover) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          {error || 'Handover not found'}
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-6">
      {/* Status and Priority Badges */}
      <div className="flex items-center gap-3">
        {getStatusBadge(handover.status)}
        {getPriorityBadge(handover.priority)}
        <div className="flex items-center gap-4 text-sm text-gray-500">
          <span>Created {format(new Date(handover.createdAt), 'MMM d, yyyy')}</span>
          {handover.scheduledDate && (
            <span>Scheduled for {format(new Date(handover.scheduledDate), 'MMM d, yyyy')}</span>
          )}
        </div>
        <div className="ml-auto flex items-center gap-2">
          {getStatusActions().map((action, index) => {
            const Icon = action.icon;
            return (
              <Button
                key={index}
                variant={action.variant}
                onClick={action.action}
                disabled={updating}
                className="flex items-center gap-2"
              >
                <Icon className="w-4 h-4" />
                {action.label}
              </Button>
            );
          })}
          <Button
            variant="outline"
            onClick={() => router.push(`/handovers/${handoverId}/edit`)}
            className="flex items-center gap-2"
          >
            <Edit className="w-4 h-4" />
            Edit
          </Button>
        </div>
      </div>

      {/* Progress Overview */}
      <Card>
        <CardContent className="pt-6">
          <div className="grid grid-cols-4 gap-6">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">{handover.checklistProgress}%</div>
              <div className="text-sm text-gray-600">Checklist Progress</div>
              <Progress value={handover.checklistProgress} className="mt-2" />
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">{handover.documentsCount}</div>
              <div className="text-sm text-gray-600">Documents</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">{handover.qaThreadsCount}</div>
              <div className="text-sm text-gray-600">Q&A Threads</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600">
                {handover.actualDuration || handover.estimatedDuration || 0}h
              </div>
              <div className="text-sm text-gray-600">
                {handover.actualDuration ? 'Actual' : 'Estimated'} Duration
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Main Content Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="checklist">Checklist</TabsTrigger>
          <TabsTrigger value="documents">Documents</TabsTrigger>
          <TabsTrigger value="qa">Q&A</TabsTrigger>
          <TabsTrigger value="timeline">Timeline</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-2 gap-6">
            {/* Opportunity Details */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Building2 className="w-5 h-5" />
                  Opportunity Details
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div>
                  <span className="font-medium">Title:</span> {handover.opportunity.title}
                </div>
                <div>
                  <span className="font-medium">Client:</span>{' '}
                  {handover.opportunity.company?.name || 
                   `${handover.opportunity.contact?.firstName} ${handover.opportunity.contact?.lastName}`}
                </div>
                <div>
                  <span className="font-medium">Value:</span> {formatCurrency(handover.opportunity.value)}
                </div>
                <div>
                  <span className="font-medium">Stage:</span>{' '}
                  <Badge variant="outline">{handover.opportunity.stage}</Badge>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => router.push(`/opportunities/${handover.opportunityId}`)}
                  className="flex items-center gap-2"
                >
                  <ExternalLink className="w-4 h-4" />
                  View Opportunity
                </Button>
              </CardContent>
            </Card>

            {/* Team Assignment */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Users className="w-5 h-5" />
                  Team Assignment
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div>
                  <span className="font-medium">Sales Rep:</span>{' '}
                  {getUserDisplayName(handover.salesRep)}
                </div>
                <div>
                  <span className="font-medium">Delivery Manager:</span>{' '}
                  {getUserDisplayName(handover.deliveryManager)}
                </div>
                <div>
                  <span className="font-medium">Assigned Teams:</span>{' '}
                  {handover.assignedTeamIds.length > 0 
                    ? `${handover.assignedTeamIds.length} team(s)`
                    : 'None assigned'
                  }
                </div>
                {handover.template && (
                  <div>
                    <span className="font-medium">Template:</span> {handover.template.name}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Quality Metrics */}
          {(handover.qualityScore || handover.clientSatisfaction || handover.deliveryFeedback) && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Star className="w-5 h-5" />
                  Quality Metrics
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {handover.qualityScore && (
                  <div>
                    <span className="font-medium">Quality Score:</span>{' '}
                    {handover.qualityScore}/5 ⭐
                  </div>
                )}
                {handover.clientSatisfaction && (
                  <div>
                    <span className="font-medium">Client Satisfaction:</span>{' '}
                    {handover.clientSatisfaction}/5 ⭐
                  </div>
                )}
                {handover.deliveryFeedback && (
                  <div>
                    <span className="font-medium">Delivery Feedback:</span>
                    <p className="mt-1 text-gray-600">{handover.deliveryFeedback}</p>
                  </div>
                )}
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="checklist">
          <HandoverChecklist handoverId={handoverId} onProgressUpdate={refreshHandover} />
        </TabsContent>

        <TabsContent value="documents">
          <HandoverDocuments handoverId={handoverId} />
        </TabsContent>

        <TabsContent value="qa">
          <HandoverQA handoverId={handoverId} />
        </TabsContent>

        <TabsContent value="timeline">
          <Card>
            <CardHeader>
              <CardTitle>Handover Timeline</CardTitle>
              <CardDescription>
                Track the progress and milestones of this handover
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center gap-3">
                  <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                  <div>
                    <div className="font-medium">Handover Created</div>
                    <div className="text-sm text-gray-500">
                      {format(new Date(handover.createdAt), 'MMM d, yyyy h:mm a')}
                    </div>
                  </div>
                </div>
                
                {handover.startedAt && (
                  <div className="flex items-center gap-3">
                    <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                    <div>
                      <div className="font-medium">Handover Started</div>
                      <div className="text-sm text-gray-500">
                        {format(new Date(handover.startedAt), 'MMM d, yyyy h:mm a')}
                      </div>
                    </div>
                  </div>
                )}
                
                {handover.completedAt && (
                  <div className="flex items-center gap-3">
                    <div className="w-3 h-3 bg-purple-500 rounded-full"></div>
                    <div>
                      <div className="font-medium">Handover Completed</div>
                      <div className="text-sm text-gray-500">
                        {format(new Date(handover.completedAt), 'MMM d, yyyy h:mm a')}
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
