import { NextResponse } from 'next/server';
import { ContactManagementService } from '@/services/contact-management';
import { withPermission, AuthenticatedRequest } from '@/lib/permission-middleware';
import { PermissionResource, PermissionAction } from '@/types';

const contactService = new ContactManagementService();

interface RouteParams {
  params: {
    companyId: string;
  };
}

/**
 * GET /api/companies/[companyId]/contacts
 * Get all contacts for a specific company
 */
async function getHandler(req: AuthenticatedRequest, context: { params: { companyId: string } }) {
  try {
    const { companyId } = context.params;

    const contacts = await contactService.getContactsByCompany(companyId, req.tenantId);

    return NextResponse.json({
      success: true,
      data: { contacts },
      message: 'Company contacts retrieved successfully'
    });

  } catch (error) {
    console.error('Get company contacts error:', error);

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export const GET = withPermission({
  resource: PermissionResource.CONTACTS,
  action: PermissionAction.READ,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest) => {
  // Extract params from URL
  const url = new URL(req.url);
  const pathSegments = url.pathname.split('/');
  const companyId = pathSegments[pathSegments.indexOf('companies') + 1];

  return getHandler(req, { params: { companyId } });
});
