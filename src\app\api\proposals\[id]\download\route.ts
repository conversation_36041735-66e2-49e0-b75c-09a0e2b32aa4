import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { hasPermission } from '@/lib/permissions';
import { PermissionResource, PermissionAction } from '@/types';
import { documentGenerator } from '@/services/document-generation';
import { prisma } from '@/lib/prisma';

/**
 * GET /api/proposals/[id]/download
 * Download generated proposal document
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user's current tenant
    const currentTenant = session.user.tenants?.[0];
    if (!currentTenant) {
      return NextResponse.json({ error: 'No tenant access' }, { status: 403 });
    }

    // Check permission for proposal reading
    const hasAccess = await hasPermission(
      PermissionResource.PROPOSALS,
      PermissionAction.READ,
      currentTenant.id
    );

    if (!hasAccess) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const proposalId = params.id;

    // Get proposal details
    const proposal = await prisma.proposal.findFirst({
      where: {
        id: proposalId,
        tenantId: currentTenant.id
      }
    });

    if (!proposal) {
      return NextResponse.json(
        { error: 'Proposal not found or access denied' },
        { status: 404 }
      );
    }

    if (!proposal.documentPath || !proposal.documentGenerated) {
      return NextResponse.json(
        { error: 'Document not available. Please generate the document first.' },
        { status: 404 }
      );
    }

    // Get the document data
    const documentData = await documentGenerator.getDocument(proposalId, currentTenant.id);

    if (!documentData) {
      return NextResponse.json(
        { error: 'Document file not found' },
        { status: 404 }
      );
    }

    // Determine content type based on file extension
    const fileExtension = proposal.documentPath.split('.').pop()?.toLowerCase();
    let contentType = 'application/octet-stream';
    let fileName = `proposal_${proposalId}`;
    
    switch (fileExtension) {
      case 'docx':
        contentType = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
        fileName += '.docx';
        break;
      case 'pdf':
        contentType = 'application/pdf';
        fileName += '.pdf';
        break;
      case 'html':
        contentType = 'text/html';
        fileName += '.html';
        break;
      default:
        fileName += `.${fileExtension}`;
    }

    // Set headers for file download
    const headers = new Headers({
      'Content-Type': contentType,
      'Content-Length': documentData.length.toString(),
      'Content-Disposition': `attachment; filename="${fileName}"`,
      'Cache-Control': 'private, no-cache, no-store, must-revalidate',
      'Pragma': 'no-cache',
      'Expires': '0'
    });

    // Check if client wants inline display instead of download
    const disposition = request.nextUrl.searchParams.get('disposition');
    if (disposition === 'inline') {
      headers.set('Content-Disposition', `inline; filename="${fileName}"`);
    }

    return new NextResponse(documentData, { headers });

  } catch (error) {
    console.error('Error downloading document:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * @swagger
 * /api/proposals/{id}/download:
 *   get:
 *     summary: Download proposal document
 *     description: Download the generated Word or PDF document for a proposal
 *     tags: [Proposals]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Proposal ID
 *       - in: query
 *         name: disposition
 *         required: false
 *         schema:
 *           type: string
 *           enum: [attachment, inline]
 *           default: attachment
 *         description: Whether to download the file or display it inline
 *     responses:
 *       200:
 *         description: Document file
 *         content:
 *           application/vnd.openxmlformats-officedocument.wordprocessingml.document:
 *             schema:
 *               type: string
 *               format: binary
 *           application/pdf:
 *             schema:
 *               type: string
 *               format: binary
 *           text/html:
 *             schema:
 *               type: string
 *               format: binary
 *         headers:
 *           Content-Type:
 *             description: MIME type of the document
 *             schema:
 *               type: string
 *           Content-Length:
 *             description: Size of the document in bytes
 *             schema:
 *               type: integer
 *           Content-Disposition:
 *             description: Disposition header for download/inline display
 *             schema:
 *               type: string
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - insufficient permissions
 *       404:
 *         description: Proposal or document not found
 *       500:
 *         description: Internal server error
 */
