'use client';

import React from 'react';
import { PageLayout } from '@/components/layout/page-layout';
import { AutomationDashboard } from '@/components/automation/automation-dashboard';
import { PermissionGate } from '@/components/ui/permission-gate';
import { PermissionResource, PermissionAction } from '@/types';
import { Card, CardContent } from '@/components/ui/card';
import { EmptyState } from '@/components/ui/empty-state';
import { ExclamationTriangleIcon } from '@heroicons/react/24/outline';

export default function AutomationPage() {
  return (
    <PermissionGate
      resource={PermissionResource.SETTINGS}
      action={PermissionAction.READ}
      fallback={
        <PageLayout>
          <Card>
            <CardContent className="p-8">
              <EmptyState
                icon={<ExclamationTriangleIcon className="w-12 h-12 text-muted-foreground" />}
                title="Access Denied"
                description="You don't have permission to view automation settings. Please contact your administrator for access."
                action={{
                  label: 'Back to Dashboard',
                  onClick: () => window.location.href = '/dashboard',
                  variant: 'primary'
                }}
              />
            </CardContent>
          </Card>
        </PageLayout>
      }
    >
      <PageLayout
        title="Process Automation"
        description="Manage automated workflows and triggers for your CRM processes"
      >
        <AutomationDashboard />
      </PageLayout>
    </PermissionGate>
  );
}
