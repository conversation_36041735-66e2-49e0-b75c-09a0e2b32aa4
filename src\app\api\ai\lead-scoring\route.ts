import { NextResponse } from 'next/server';
import { withPermission } from '@/lib/permission-middleware';
import { aiLeadScoringService, DatabaseLeadData } from '@/services/ai-lead-scoring';
import { PermissionAction, PermissionResource } from '@/types';
import { AuthenticatedRequest } from '@/lib/permission-middleware';

async function postHandler(req: AuthenticatedRequest) {
  try {
    const body = await req.json();
    const { leadData, includeQualification = false } = body;

    // Validate required fields
    if (!leadData || !leadData.id || !leadData.tenantId) {
      return NextResponse.json(
        { error: 'Lead data with id and tenantId is required' },
        { status: 400 }
      );
    }

    // Ensure the lead belongs to the user's tenant
    if (leadData.tenantId !== req.tenantId) {
      return NextResponse.json(
        { error: 'Access denied to this lead' },
        { status: 403 }
      );
    }

    const result = await aiLeadScoringService.scoreLeadWithAI(
      leadData as DatabaseLeadData,
      req.userId,
      { includeQualification }
    );

    return NextResponse.json({
      success: true,
      data: result
    });

  } catch (error) {
    console.error('Lead scoring API error:', error);
    
    if (error instanceof Error) {
      if (error.message.includes('AI service')) {
        return NextResponse.json(
          { error: 'AI service temporarily unavailable' },
          { status: 503 }
        );
      }
      
      if (error.message.includes('rate limit')) {
        return NextResponse.json(
          { error: 'Rate limit exceeded. Please try again later.' },
          { status: 429 }
        );
      }
    }

    return NextResponse.json(
      { error: 'Failed to score lead' },
      { status: 500 }
    );
  }
}

async function putHandler(req: AuthenticatedRequest) {
  try {
    const body = await req.json();
    const { leads } = body;

    if (!Array.isArray(leads) || leads.length === 0) {
      return NextResponse.json(
        { error: 'Array of leads is required' },
        { status: 400 }
      );
    }

    // Validate all leads belong to the user's tenant
    const invalidLeads = leads.filter(lead => lead.tenantId !== req.tenantId);
    if (invalidLeads.length > 0) {
      return NextResponse.json(
        { error: 'Access denied to some leads' },
        { status: 403 }
      );
    }

    // Limit batch size to prevent abuse
    if (leads.length > 50) {
      return NextResponse.json(
        { error: 'Maximum 50 leads per batch' },
        { status: 400 }
      );
    }

    const results = await aiLeadScoringService.batchScoreLeads(
      leads as DatabaseLeadData[],
      req.userId,
      req.tenantId
    );

    // Convert Map to object for JSON response
    const resultsObject = Object.fromEntries(results);

    return NextResponse.json({
      success: true,
      data: {
        scores: resultsObject,
        processed: results.size,
        total: leads.length
      }
    });

  } catch (error) {
    console.error('Batch lead scoring API error:', error);
    return NextResponse.json(
      { error: 'Failed to score leads' },
      { status: 500 }
    );
  }
}

// Export with permission middleware
export const POST = withPermission({
  resource: PermissionResource.LEADS,
  action: PermissionAction.READ
})(postHandler);

export const PUT = withPermission({
  resource: PermissionResource.LEADS,
  action: PermissionAction.READ
})(putHandler);
