import { z } from 'zod';
import { prisma } from '@/lib/prisma';
import { sendEmail } from '@/lib/email';
import { LeadWithRelations } from './lead-management';

// Validation schemas
export const notificationPreferencesSchema = z.object({
  emailNotifications: z.boolean().default(true),
  browserNotifications: z.boolean().default(true),
  newLeadNotifications: z.boolean().default(true),
  leadStatusChangeNotifications: z.boolean().default(true),
  leadAssignmentNotifications: z.boolean().default(true),
  highValueLeadNotifications: z.boolean().default(true),
  urgentLeadNotifications: z.boolean().default(true),
  nurturingCampaignNotifications: z.boolean().default(true),
  dailyDigest: z.boolean().default(false),
  weeklyReport: z.boolean().default(true)
});

export type NotificationPreferences = z.infer<typeof notificationPreferencesSchema>;

export interface LeadNotification {
  id: string;
  tenantId: string;
  userId: string;
  leadId: string;
  type: NotificationType;
  title: string;
  message: string;
  data?: Record<string, any>;
  isRead: boolean;
  createdAt: Date;
  lead?: LeadWithRelations;
}

export type NotificationType = 
  | 'new_lead'
  | 'lead_assigned'
  | 'status_changed'
  | 'high_value_lead'
  | 'urgent_lead'
  | 'follow_up_due'
  | 'nurturing_email_sent'
  | 'nurturing_email_failed'
  | 'lead_converted'
  | 'lead_lost';

export interface NotificationTemplate {
  type: NotificationType;
  title: (data: any) => string;
  message: (data: any) => string;
  emailSubject?: (data: any) => string;
  emailTemplate?: (data: any) => string;
}

const NOTIFICATION_TEMPLATES: Record<NotificationType, NotificationTemplate> = {
  new_lead: {
    type: 'new_lead',
    title: (data) => 'New Lead Created',
    message: (data) => `A new lead "${data.leadTitle}" has been created from ${data.source || 'unknown source'}`,
    emailSubject: (data) => `New Lead: ${data.leadTitle}`,
    emailTemplate: (data) => `
      <h2>New Lead Created</h2>
      <p>A new lead has been created and needs your attention:</p>
      <ul>
        <li><strong>Title:</strong> ${data.leadTitle}</li>
        <li><strong>Contact:</strong> ${data.contactName}</li>
        <li><strong>Company:</strong> ${data.companyName || 'N/A'}</li>
        <li><strong>Source:</strong> ${data.source || 'Unknown'}</li>
        <li><strong>Value:</strong> ${data.value ? `$${data.value}` : 'Not specified'}</li>
      </ul>
      <p><a href="${data.leadUrl}">View Lead Details</a></p>
    `
  },
  lead_assigned: {
    type: 'lead_assigned',
    title: (data) => 'Lead Assigned to You',
    message: (data) => `Lead "${data.leadTitle}" has been assigned to you`,
    emailSubject: (data) => `Lead Assigned: ${data.leadTitle}`,
    emailTemplate: (data) => `
      <h2>Lead Assigned</h2>
      <p>A lead has been assigned to you:</p>
      <ul>
        <li><strong>Title:</strong> ${data.leadTitle}</li>
        <li><strong>Contact:</strong> ${data.contactName}</li>
        <li><strong>Priority:</strong> ${data.priority}</li>
        <li><strong>Assigned by:</strong> ${data.assignedBy}</li>
      </ul>
      <p><a href="${data.leadUrl}">View Lead Details</a></p>
    `
  },
  status_changed: {
    type: 'status_changed',
    title: (data) => 'Lead Status Updated',
    message: (data) => `Lead "${data.leadTitle}" status changed from ${data.oldStatus} to ${data.newStatus}`,
    emailSubject: (data) => `Lead Status Update: ${data.leadTitle}`,
    emailTemplate: (data) => `
      <h2>Lead Status Updated</h2>
      <p>A lead status has been updated:</p>
      <ul>
        <li><strong>Title:</strong> ${data.leadTitle}</li>
        <li><strong>Old Status:</strong> ${data.oldStatus}</li>
        <li><strong>New Status:</strong> ${data.newStatus}</li>
        <li><strong>Updated by:</strong> ${data.updatedBy}</li>
      </ul>
      <p><a href="${data.leadUrl}">View Lead Details</a></p>
    `
  },
  high_value_lead: {
    type: 'high_value_lead',
    title: (data) => 'High-Value Lead Alert',
    message: (data) => `High-value lead "${data.leadTitle}" worth $${data.value} requires attention`,
    emailSubject: (data) => `High-Value Lead Alert: $${data.value}`,
    emailTemplate: (data) => `
      <h2>High-Value Lead Alert</h2>
      <p>A high-value lead requires immediate attention:</p>
      <ul>
        <li><strong>Title:</strong> ${data.leadTitle}</li>
        <li><strong>Value:</strong> $${data.value}</li>
        <li><strong>Contact:</strong> ${data.contactName}</li>
        <li><strong>Score:</strong> ${data.score}/100</li>
      </ul>
      <p><a href="${data.leadUrl}">View Lead Details</a></p>
    `
  },
  urgent_lead: {
    type: 'urgent_lead',
    title: (data) => 'Urgent Lead Alert',
    message: (data) => `Urgent lead "${data.leadTitle}" needs immediate follow-up`,
    emailSubject: (data) => `Urgent Lead: ${data.leadTitle}`,
    emailTemplate: (data) => `
      <h2>Urgent Lead Alert</h2>
      <p>An urgent lead needs immediate follow-up:</p>
      <ul>
        <li><strong>Title:</strong> ${data.leadTitle}</li>
        <li><strong>Priority:</strong> ${data.priority}</li>
        <li><strong>Expected Close:</strong> ${data.expectedCloseDate}</li>
        <li><strong>Days Overdue:</strong> ${data.daysOverdue}</li>
      </ul>
      <p><a href="${data.leadUrl}">View Lead Details</a></p>
    `
  },
  follow_up_due: {
    type: 'follow_up_due',
    title: (data) => 'Follow-up Due',
    message: (data) => `Follow-up is due for lead "${data.leadTitle}"`,
    emailSubject: (data) => `Follow-up Due: ${data.leadTitle}`,
    emailTemplate: (data) => `
      <h2>Follow-up Due</h2>
      <p>A follow-up is due for one of your leads:</p>
      <ul>
        <li><strong>Title:</strong> ${data.leadTitle}</li>
        <li><strong>Last Contact:</strong> ${data.lastContactDate}</li>
        <li><strong>Days Since Contact:</strong> ${data.daysSinceContact}</li>
      </ul>
      <p><a href="${data.leadUrl}">View Lead Details</a></p>
    `
  },
  nurturing_email_sent: {
    type: 'nurturing_email_sent',
    title: (data) => 'Nurturing Email Sent',
    message: (data) => `Nurturing email sent to lead "${data.leadTitle}"`,
  },
  nurturing_email_failed: {
    type: 'nurturing_email_failed',
    title: (data) => 'Nurturing Email Failed',
    message: (data) => `Failed to send nurturing email to lead "${data.leadTitle}": ${data.error}`,
  },
  lead_converted: {
    type: 'lead_converted',
    title: (data) => 'Lead Converted!',
    message: (data) => `Congratulations! Lead "${data.leadTitle}" has been converted to an opportunity`,
    emailSubject: (data) => `Lead Converted: ${data.leadTitle}`,
    emailTemplate: (data) => `
      <h2>Lead Converted!</h2>
      <p>Congratulations! A lead has been successfully converted:</p>
      <ul>
        <li><strong>Lead:</strong> ${data.leadTitle}</li>
        <li><strong>Opportunity:</strong> ${data.opportunityTitle}</li>
        <li><strong>Value:</strong> $${data.value}</li>
        <li><strong>Converted by:</strong> ${data.convertedBy}</li>
      </ul>
      <p><a href="${data.opportunityUrl}">View Opportunity</a></p>
    `
  },
  lead_lost: {
    type: 'lead_lost',
    title: (data) => 'Lead Lost',
    message: (data) => `Lead "${data.leadTitle}" has been marked as lost`,
    emailSubject: (data) => `Lead Lost: ${data.leadTitle}`,
    emailTemplate: (data) => `
      <h2>Lead Lost</h2>
      <p>A lead has been marked as lost:</p>
      <ul>
        <li><strong>Title:</strong> ${data.leadTitle}</li>
        <li><strong>Reason:</strong> ${data.lostReason || 'Not specified'}</li>
        <li><strong>Value:</strong> $${data.value}</li>
      </ul>
      <p>Consider following up in the future or analyzing what went wrong.</p>
    `
  }
};

export class LeadNotificationService {
  /**
   * Create a notification for a user
   */
  async createNotification(
    tenantId: string,
    userId: string,
    leadId: string,
    type: NotificationType,
    data: Record<string, any> = {}
  ): Promise<LeadNotification> {
    const template = NOTIFICATION_TEMPLATES[type];
    
    const notification = await prisma.leadNotification.create({
      data: {
        tenantId,
        userId,
        leadId,
        type,
        title: template.title(data),
        message: template.message(data),
        data,
        isRead: false
      }
    });

    // Send email notification if user has email notifications enabled
    await this.sendEmailNotification(userId, type, data);

    // TODO: Send browser push notification if enabled
    // await this.sendPushNotification(userId, notification);

    return notification as LeadNotification;
  }

  /**
   * Send email notification
   */
  private async sendEmailNotification(
    userId: string,
    type: NotificationType,
    data: Record<string, any>
  ): Promise<void> {
    try {
      // Get user preferences
      const preferences = await this.getUserNotificationPreferences(userId);
      if (!preferences.emailNotifications) {
        return;
      }

      // Check if this notification type is enabled
      const typeEnabled = this.isNotificationTypeEnabled(type, preferences);
      if (!typeEnabled) {
        return;
      }

      // Get user email
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: { email: true, firstName: true, lastName: true }
      });

      if (!user?.email) {
        return;
      }

      const template = NOTIFICATION_TEMPLATES[type];
      if (!template.emailSubject || !template.emailTemplate) {
        return;
      }

      await sendEmail({
        to: user.email,
        subject: template.emailSubject(data),
        html: template.emailTemplate(data)
      });
    } catch (error) {
      console.error('Error sending email notification:', error);
    }
  }

  /**
   * Get user notification preferences
   */
  async getUserNotificationPreferences(userId: string): Promise<NotificationPreferences> {
    const settings = await prisma.userNotificationPreferences.findUnique({
      where: { userId }
    });

    if (!settings) {
      // Return default preferences
      return notificationPreferencesSchema.parse({});
    }

    return notificationPreferencesSchema.parse(settings.preferences);
  }

  /**
   * Update user notification preferences
   */
  async updateUserNotificationPreferences(
    userId: string,
    preferences: Partial<NotificationPreferences>
  ): Promise<NotificationPreferences> {
    const currentPreferences = await this.getUserNotificationPreferences(userId);
    const updatedPreferences = { ...currentPreferences, ...preferences };

    await prisma.userNotificationPreferences.upsert({
      where: { userId },
      create: {
        userId,
        preferences: updatedPreferences
      },
      update: {
        preferences: updatedPreferences
      }
    });

    return updatedPreferences;
  }

  /**
   * Check if notification type is enabled for user
   */
  private isNotificationTypeEnabled(
    type: NotificationType,
    preferences: NotificationPreferences
  ): boolean {
    switch (type) {
      case 'new_lead':
        return preferences.newLeadNotifications;
      case 'lead_assigned':
        return preferences.leadAssignmentNotifications;
      case 'status_changed':
        return preferences.leadStatusChangeNotifications;
      case 'high_value_lead':
        return preferences.highValueLeadNotifications;
      case 'urgent_lead':
        return preferences.urgentLeadNotifications;
      case 'nurturing_email_sent':
      case 'nurturing_email_failed':
        return preferences.nurturingCampaignNotifications;
      default:
        return true;
    }
  }

  /**
   * Get notifications for a user
   */
  async getUserNotifications(
    userId: string,
    tenantId: string,
    options: {
      limit?: number;
      offset?: number;
      unreadOnly?: boolean;
    } = {}
  ): Promise<{ notifications: LeadNotification[]; total: number }> {
    const { limit = 50, offset = 0, unreadOnly = false } = options;

    const where = {
      userId,
      tenantId,
      ...(unreadOnly && { isRead: false })
    };

    const [notifications, total] = await Promise.all([
      prisma.leadNotification.findMany({
        where,
        include: {
          lead: {
            include: {
              contact: true,
              company: true,
              owner: {
                select: {
                  id: true,
                  firstName: true,
                  lastName: true,
                  email: true,
                  avatarUrl: true
                }
              }
            }
          }
        },
        orderBy: { createdAt: 'desc' },
        take: limit,
        skip: offset
      }),
      prisma.leadNotification.count({ where })
    ]);

    return {
      notifications: notifications as LeadNotification[],
      total
    };
  }

  /**
   * Mark notification as read
   */
  async markAsRead(notificationId: string, userId: string): Promise<void> {
    await prisma.leadNotification.updateMany({
      where: {
        id: notificationId,
        userId
      },
      data: {
        isRead: true
      }
    });
  }

  /**
   * Mark all notifications as read for a user
   */
  async markAllAsRead(userId: string, tenantId: string): Promise<void> {
    await prisma.leadNotification.updateMany({
      where: {
        userId,
        tenantId,
        isRead: false
      },
      data: {
        isRead: true
      }
    });
  }

  /**
   * Trigger notifications for lead events
   */
  async triggerLeadNotification(
    type: NotificationType,
    lead: LeadWithRelations,
    additionalData: Record<string, any> = {}
  ): Promise<void> {
    const data = {
      leadId: lead.id,
      leadTitle: lead.title,
      contactName: lead.contact ? `${lead.contact.firstName} ${lead.contact.lastName}` : 'Unknown',
      companyName: lead.company?.name,
      source: lead.source,
      value: lead.value?.toString(),
      priority: lead.priority,
      score: lead.score,
      leadUrl: `${process.env.NEXTAUTH_URL}/leads/${lead.id}`,
      ...additionalData
    };

    // Notify lead owner
    if (lead.ownerId) {
      await this.createNotification(
        lead.tenantId,
        lead.ownerId,
        lead.id,
        type,
        data
      );
    }

    // Notify tenant admins for high-priority notifications
    if (['high_value_lead', 'urgent_lead', 'lead_converted'].includes(type)) {
      const tenantAdmins = await prisma.tenantUser.findMany({
        where: {
          tenantId: lead.tenantId,
          isTenantAdmin: true
        },
        select: { userId: true }
      });

      for (const admin of tenantAdmins) {
        if (admin.userId !== lead.ownerId) {
          await this.createNotification(
            lead.tenantId,
            admin.userId,
            lead.id,
            type,
            data
          );
        }
      }
    }
  }
}

export const leadNotificationService = new LeadNotificationService();
