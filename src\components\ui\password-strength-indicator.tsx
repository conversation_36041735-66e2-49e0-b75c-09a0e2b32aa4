'use client';

import React from 'react';
import { CheckIcon, XMarkIcon } from '@heroicons/react/24/outline';
import { calculatePasswordStrength as clientCalculatePasswordStrength } from '@/lib/client-password-utils';

export interface PasswordStrength {
  score: number; // 0-4
  level: 'weak' | 'fair' | 'good' | 'strong';
  feedback: string[];
  requirements: {
    minLength: boolean;
    hasUppercase: boolean;
    hasLowercase: boolean;
    hasNumber: boolean;
    hasSpecialChar: boolean;
  };
}

interface PasswordStrengthIndicatorProps {
  password: string;
  minLength?: number;
  showRequirements?: boolean;
  className?: string;
}

export function calculatePasswordStrength(password: string, minLength: number = 8): PasswordStrength {
  return clientCalculatePasswordStrength(password, minLength);
}

export function PasswordStrengthIndicator({
  password,
  minLength = 8,
  showRequirements = true,
  className = '',
}: PasswordStrengthIndicatorProps) {
  const strength = calculatePasswordStrength(password, minLength);

  const getStrengthColor = (level: string) => {
    switch (level) {
      case 'weak':
        return 'bg-red-500';
      case 'fair':
        return 'bg-yellow-500';
      case 'good':
        return 'bg-blue-500';
      case 'strong':
        return 'bg-green-500';
      default:
        return 'bg-gray-300';
    }
  };

  const getStrengthText = (level: string) => {
    switch (level) {
      case 'weak':
        return 'Weak';
      case 'fair':
        return 'Fair';
      case 'good':
        return 'Good';
      case 'strong':
        return 'Strong';
      default:
        return '';
    }
  };

  if (!password) {
    return null;
  }

  return (
    <div className={`space-y-3 ${className}`}>
      {/* Strength Bar */}
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium text-gray-700">Password Strength</span>
          <span className={`text-sm font-medium ${
            strength.level === 'weak' ? 'text-red-600' :
            strength.level === 'fair' ? 'text-yellow-600' :
            strength.level === 'good' ? 'text-blue-600' :
            'text-green-600'
          }`}>
            {getStrengthText(strength.level)}
          </span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div
            className={`h-2 rounded-full transition-all duration-300 ${getStrengthColor(strength.level)}`}
            style={{ width: `${(strength.score / 4) * 100}%` }}
          />
        </div>
      </div>

      {/* Requirements Checklist */}
      {showRequirements && (
        <div className="space-y-2">
          <h4 className="text-sm font-medium text-gray-700">Password Requirements</h4>
          <div className="space-y-1">
            <RequirementItem
              met={strength.requirements.minLength}
              text={`At least ${minLength} characters`}
            />
            <RequirementItem
              met={strength.requirements.hasUppercase}
              text="One uppercase letter"
            />
            <RequirementItem
              met={strength.requirements.hasLowercase}
              text="One lowercase letter"
            />
            <RequirementItem
              met={strength.requirements.hasNumber}
              text="One number"
            />
            <RequirementItem
              met={strength.requirements.hasSpecialChar}
              text="One special character"
            />
          </div>
        </div>
      )}
    </div>
  );
}

interface RequirementItemProps {
  met: boolean;
  text: string;
}

function RequirementItem({ met, text }: RequirementItemProps) {
  return (
    <div className="flex items-center space-x-2">
      {met ? (
        <CheckIcon className="w-4 h-4 text-green-500" />
      ) : (
        <XMarkIcon className="w-4 h-4 text-gray-400" />
      )}
      <span className={`text-sm ${met ? 'text-green-700' : 'text-gray-600'}`}>
        {text}
      </span>
    </div>
  );
}
