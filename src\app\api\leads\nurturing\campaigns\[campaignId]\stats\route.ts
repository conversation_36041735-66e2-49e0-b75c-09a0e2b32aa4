import { NextResponse } from 'next/server';
import { withPermission } from '@/lib/permission-middleware';
import { PermissionAction, PermissionResource } from '@/types';
import { AuthenticatedRequest } from '@/lib/permission-middleware';
import { prisma } from '@/lib/prisma';

/**
 * GET /api/leads/nurturing/campaigns/[campaignId]/stats
 * Get detailed stats for debugging campaign lead counting
 */
export const GET = withPermission({
  resource: PermissionResource.LEADS,
  action: PermissionAction.READ,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest) => {
  try {
    // Extract campaignId from URL
    const url = new URL(req.url);
    const pathSegments = url.pathname.split('/');
    const campaignId = pathSegments[pathSegments.length - 2]; // -2 because last segment is 'stats'

    if (!campaignId) {
      return NextResponse.json({ error: 'Campaign ID required' }, { status: 400 });
    }

    // Get the campaign
    const campaign = await prisma.nurturingCampaign.findUnique({
      where: { id: campaignId, tenantId: req.tenantId }
    });

    if (!campaign) {
      return NextResponse.json({ error: 'Campaign not found' }, { status: 404 });
    }

    // Get campaign criteria
    const targetStatus = campaign.targetStatus as string[];
    const targetPriority = campaign.targetPriority as string[];

    // Build where clause
    const whereClause: any = {
      tenantId: req.tenantId,
      status: {
        in: targetStatus
      }
    };

    if (targetPriority && targetPriority.length > 0) {
      whereClause.priority = {
        in: targetPriority
      };
    }

    // Count total leads matching criteria
    const totalLeads = await prisma.lead.count({
      where: whereClause
    });

    // Get sample leads for debugging
    const sampleLeads = await prisma.lead.findMany({
      where: whereClause,
      take: 5,
      select: {
        id: true,
        status: true,
        priority: true,
        contact: {
          select: {
            firstName: true,
            lastName: true,
            email: true
          }
        },
        company: {
          select: {
            name: true
          }
        }
      }
    });

    // Get all leads in tenant for comparison
    const allLeadsInTenant = await prisma.lead.count({
      where: { tenantId: req.tenantId }
    });

    // Get leads by status breakdown
    const leadsByStatus = await prisma.lead.groupBy({
      by: ['status'],
      where: { tenantId: req.tenantId },
      _count: {
        id: true
      }
    });

    // Get leads by priority breakdown
    const leadsByPriority = await prisma.lead.groupBy({
      by: ['priority'],
      where: { tenantId: req.tenantId },
      _count: {
        id: true
      }
    });

    return NextResponse.json({
      success: true,
      data: {
        campaign: {
          id: campaign.id,
          name: campaign.name,
          targetStatus,
          targetPriority,
          tenantId: campaign.tenantId
        },
        stats: {
          totalLeadsMatchingCriteria: totalLeads,
          allLeadsInTenant,
          whereClause,
          sampleLeads,
          leadsByStatus,
          leadsByPriority
        }
      }
    });
  } catch (error) {
    console.error('Error getting campaign stats:', error);
    return NextResponse.json(
      { error: 'Failed to get campaign stats' },
      { status: 500 }
    );
  }
});
