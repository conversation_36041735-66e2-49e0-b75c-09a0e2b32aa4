#!/usr/bin/env node

/**
 * Master Database Seeding Script for CRM Platform
 * 
 * This script orchestrates all seeding operations in the correct dependency order.
 * It can be run safely multiple times (idempotent) and includes comprehensive logging.
 * 
 * Usage:
 *   node scripts/master-seed.js [options]
 * 
 * Options:
 *   --force          Force re-seeding even if data exists
 *   --sample-data    Include sample data creation
 *   --skip-existing  Skip seeding if any data already exists
 * 
 * Seeding Order:
 * 1. Subscription Plans & Feature Flags
 * 2. Tenants & Subscriptions  
 * 3. Users & Tenant-User relationships
 * 4. Permissions (comprehensive system)
 * 5. Roles & Role-Permission assignments
 * 6. Lead Sources & Sales Stages
 * 7. Sample data (optional)
 */

const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');
const path = require('path');

// Initialize Prisma client
let prisma;
try {
  prisma = new PrismaClient();
  console.log('✅ Prisma client initialized successfully');
} catch (error) {
  console.error('❌ Failed to initialize Prisma client:', error.message);
  console.error('Please ensure you have run "npx prisma generate" before running this script');
  process.exit(1);
}

// Parse command line arguments
const args = process.argv.slice(2);
const options = {
  force: args.includes('--force'),
  sampleData: args.includes('--sample-data'),
  skipExisting: args.includes('--skip-existing')
};

/**
 * Ensure database schema is up to date
 */
async function ensureDatabaseSchema() {
  console.log('🔧 Ensuring database schema is up to date...');

  try {
    // Test basic connectivity
    await prisma.$connect();
    console.log('   ✅ Database connection established');

    // Check if we can access basic tables by testing a simple query
    try {
      await prisma.$queryRaw`SELECT 1`;
      console.log('   ✅ Database is accessible');
    } catch (error) {
      console.log('   ⚠️  Database query failed, may need migrations');
      throw new Error('Database schema may not be initialized. Please run: npx prisma migrate deploy');
    }

  } catch (error) {
    console.error('   ❌ Database connection failed:', error.message);
    throw error;
  }
}

/**
 * Main seeding orchestrator
 */
async function masterSeed() {
  console.log('🌱 Starting Master Database Seeding...\n');
  console.log('📋 Configuration:');
  console.log(`   Force re-seed: ${options.force}`);
  console.log(`   Include sample data: ${options.sampleData}`);
  console.log(`   Skip if existing: ${options.skipExisting}\n`);

  try {
    // Ensure database schema is ready
    await ensureDatabaseSchema();

    // Check existing data
    const existingData = await checkExistingData();
    
    if (options.skipExisting && hasExistingData(existingData)) {
      console.log('⏭️  Database already contains data and --skip-existing flag is set. Exiting.');
      return;
    }
    
    if (!options.force && hasExistingData(existingData)) {
      console.log('⚠️  Database already contains data. Use --force to re-seed or --skip-existing to skip.');
      console.log('   Current data counts:');
      Object.entries(existingData).forEach(([key, count]) => {
        if (count > 0) console.log(`     ${key}: ${count}`);
      });
      return;
    }
    
    // Execute seeding steps in dependency order
    console.log('🚀 Beginning seeding process...\n');
    
    // Step 1: Subscription Plans & Feature Flags
    const { plans } = await seedSubscriptionPlans();
    
    // Step 2: Tenants & Users
    const { tenants, users } = await seedTenantsAndUsers(plans);
    
    // Step 3: Permissions
    await seedPermissions();

    // Step 4: Roles & Role-Permission assignments
    await seedRoles();

    // Step 5: Lead Sources & Sales Stages
    await seedLeadSources();
    await seedSalesStages();
    
    // Step 6: Sample data (optional)
    if (options.sampleData) {
      await seedSampleData(tenants[0], users[0]);
    }
    
    // Final summary
    await printFinalSummary();
    
    console.log('\n🎉 Master database seeding completed successfully!');
    
  } catch (error) {
    console.error('\n❌ Master seeding failed:', error);
    console.error('Stack trace:', error.stack);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

/**
 * Check existing data in database with error handling
 */
async function checkExistingData() {
  const counts = {};

  // Define models to check with their table names for better error messages
  const modelsToCheck = [
    { key: 'subscriptionPlans', model: 'subscriptionPlan', table: 'subscription_plans' },
    { key: 'featureFlags', model: 'featureFlag', table: 'feature_flags' },
    { key: 'tenants', model: 'tenant', table: 'tenants' },
    { key: 'users', model: 'user', table: 'users' },
    { key: 'permissions', model: 'permission', table: 'permissions' },
    { key: 'roles', model: 'role', table: 'roles' },
    { key: 'leadSources', model: 'leadSource', table: 'lead_sources' },
    { key: 'salesStages', model: 'salesStage', table: 'sales_stages' }
  ];

  console.log('📊 Checking database state...');

  for (const { key, model, table } of modelsToCheck) {
    try {
      counts[key] = await prisma[model].count();
      const status = counts[key] > 0 ? '✅' : '⭕';
      console.log(`   ${status} ${key}: ${counts[key]}`);
    } catch (error) {
      if (error.code === 'P2021') {
        console.log(`   ⚠️  ${key}: Table '${table}' does not exist yet`);
        counts[key] = 0;
      } else {
        console.log(`   ❌ ${key}: Error checking (${error.message})`);
        counts[key] = 0;
      }
    }
  }

  console.log('');
  return counts;
}

/**
 * Check if database has existing data
 */
function hasExistingData(counts) {
  return Object.values(counts).some(count => count > 0);
}

/**
 * Enhanced admin user creation using environment variables and bcryptjs
 */
async function createEnhancedAdminUser() {
  // Configuration from environment variables with defaults
  const config = {
    email: process.env.ADMIN_EMAIL || '<EMAIL>',
    password: process.env.ADMIN_PASSWORD || 'Admin123!',
    firstName: process.env.ADMIN_FIRST_NAME || 'Admin',
    lastName: process.env.ADMIN_LAST_NAME || 'User'
  };

  console.log(`   📧 Admin email: ${config.email}`);
  console.log(`   👤 Admin name: ${config.firstName} ${config.lastName}`);

  // Hash password using bcryptjs with salt rounds 12 (same as main app)
  const passwordHash = await bcrypt.hash(config.password, 12);

  // Create or update admin user
  const user = await prisma.user.upsert({
    where: { email: config.email },
    update: {
      passwordHash,
      firstName: config.firstName,
      lastName: config.lastName,
      isPlatformAdmin: true,
      status: 'active',
      emailVerified: true,
      updatedAt: new Date()
    },
    create: {
      id: 'user_admin',
      email: config.email,
      passwordHash,
      firstName: config.firstName,
      lastName: config.lastName,
      locale: 'en',
      timezone: 'UTC',
      emailVerified: true,
      isPlatformAdmin: true,
      status: 'active'
    }
  });

  return user;
}

/**
 * Step 2: Create Tenants, Users, and Subscriptions
 */
async function seedTenantsAndUsers(plans) {
  console.log('🏢 Step 2: Creating tenants, users, and subscriptions...');

  const professionalPlan = plans.find(p => p.name === 'Professional');

  // Create demo tenant
  const tenant = await prisma.tenant.upsert({
    where: { slug: 'demo-company' },
    update: {},
    create: {
      id: 'tenant_default',
      name: 'Demo Company',
      slug: 'demo-company',
      status: 'active',
      settings: {
        locale: 'en',
        timezone: 'UTC',
        currency: 'USD',
        date_format: 'MM/DD/YYYY',
        time_format: '12h'
      },
      branding: {
        logo: null,
        primary_color: '#4f46e5',
        secondary_color: '#10b981',
        company_name: 'Demo Company'
      }
    }
  });
  console.log(`   ✅ Created tenant: ${tenant.name}`);

  // Create subscription for tenant (check if exists first)
  let subscription = await prisma.subscription.findFirst({
    where: {
      tenantId: tenant.id,
      planId: professionalPlan.id
    }
  });

  if (!subscription) {
    subscription = await prisma.subscription.create({
      data: {
        tenantId: tenant.id,
        planId: professionalPlan.id,
        status: 'active',
        currentPeriodStart: new Date(),
        currentPeriodEnd: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days
        stripeSubscriptionId: null,
        stripeCustomerId: null
      }
    });
    console.log(`   ✅ Created subscription: ${professionalPlan.name}`);
  } else {
    console.log(`   ✅ Subscription already exists: ${professionalPlan.name}`);
  }

  // Create admin user using enhanced system
  const user = await createEnhancedAdminUser();
  console.log(`   ✅ Created admin user: ${user.email}`);

  // Connect admin user to tenant
  const tenantUser = await prisma.tenantUser.upsert({
    where: {
      tenantId_userId: {
        tenantId: tenant.id,
        userId: user.id
      }
    },
    update: {},
    create: {
      id: 'tenant_user_admin',
      tenantId: tenant.id,
      userId: user.id,
      isTenantAdmin: true,
      role: 'admin',
      status: 'active'
    }
  });
  console.log(`   ✅ Connected user to tenant as admin`);

  return { tenants: [tenant], users: [user], subscription };
}

/**
 * Print final summary of seeded data
 */
async function printFinalSummary() {
  console.log('\n📊 Final Database Summary:');
  const finalCounts = await checkExistingData();
  
  const totalItems = Object.values(finalCounts).reduce((sum, count) => sum + count, 0);
  console.log(`\n🎯 Total items seeded: ${totalItems}`);
  
  // Additional summary information
  const tenants = await prisma.tenant.findMany({ select: { name: true, slug: true } });
  const plans = await prisma.subscriptionPlan.findMany({ select: { name: true } });
  
  console.log('\n🏢 Tenants created:');
  tenants.forEach(tenant => console.log(`   • ${tenant.name} (${tenant.slug})`));
  
  console.log('\n💳 Subscription plans available:');
  plans.forEach(plan => console.log(`   • ${plan.name}`));
}

/**
 * Step 1: Create Subscription Plans and Feature Flags
 */
async function seedSubscriptionPlans() {
  console.log('🏷️  Step 1: Creating subscription plans and feature flags...');

  const plans = [
    {
      id: 'plan_starter',
      name: 'Starter',
      description: 'Perfect for small teams getting started with CRM',
      priceMonthly: 29.00,
      priceYearly: 290.00,
      features: {
        contacts: 1000,
        leads: 500,
        users: 5,
        ai_requests: 100,
        storage_mb: 1000,
        email_support: true,
        basic_reporting: true,
        mobile_app: true
      },
      limits: {
        contacts: 1000,
        leads: 500,
        users: 5,
        ai_requests_per_month: 100,
        storage_mb: 1000
      },
      isActive: true
    },
    {
      id: 'plan_professional',
      name: 'Professional',
      description: 'Advanced features for growing businesses',
      priceMonthly: 99.00,
      priceYearly: 990.00,
      features: {
        contacts: -1,
        leads: -1,
        users: 25,
        ai_requests: 1000,
        storage_mb: 10000,
        email_support: true,
        priority_support: true,
        advanced_reporting: true,
        ai_features: true,
        mobile_app: true,
        api_access: true
      },
      limits: {
        contacts: -1,
        leads: -1,
        users: 25,
        ai_requests_per_month: 1000,
        storage_mb: 10000
      },
      isActive: true
    },
    {
      id: 'plan_enterprise',
      name: 'Enterprise',
      description: 'Complete solution for large organizations',
      priceMonthly: 299.00,
      priceYearly: 2990.00,
      features: {
        contacts: -1,
        leads: -1,
        users: -1,
        ai_requests: -1,
        storage_mb: -1,
        email_support: true,
        priority_support: true,
        dedicated_support: true,
        advanced_reporting: true,
        ai_features: true,
        mobile_app: true,
        api_access: true,
        custom_integrations: true,
        sla_guarantee: true
      },
      limits: {
        contacts: -1,
        leads: -1,
        users: -1,
        ai_requests_per_month: -1,
        storage_mb: -1
      },
      isActive: true
    }
  ];

  const createdPlans = [];
  for (const planData of plans) {
    const plan = await prisma.subscriptionPlan.upsert({
      where: { id: planData.id },
      update: planData,
      create: planData
    });
    createdPlans.push(plan);
    console.log(`   ✅ Created/updated plan: ${plan.name}`);
  }

  // Create feature flags
  const featureFlags = [
    {
      name: 'ai_features',
      description: 'Enable AI-powered features like lead scoring and content generation',
      isEnabled: true,
      requiredPlanLevel: 2
    },
    {
      name: 'advanced_reporting',
      description: 'Enable advanced analytics and custom reports',
      isEnabled: true,
      requiredPlanLevel: 2
    },
    {
      name: 'api_access',
      description: 'Enable REST API access for integrations',
      isEnabled: true,
      requiredPlanLevel: 2
    },
    {
      name: 'custom_integrations',
      description: 'Enable custom third-party integrations',
      isEnabled: true,
      requiredPlanLevel: 3
    },
    {
      name: 'mobile_app',
      description: 'Enable mobile application access',
      isEnabled: true,
      requiredPlanLevel: 1
    }
  ];

  for (const flag of featureFlags) {
    await prisma.featureFlag.upsert({
      where: { name: flag.name },
      update: flag,
      create: flag
    });
    console.log(`   ✅ Created/updated feature flag: ${flag.name}`);
  }

  return { plans: createdPlans, featureFlags };
}

/**
 * Step 3: Create comprehensive permissions
 */
async function seedPermissions() {
  console.log('🔐 Step 3: Creating comprehensive permissions...');

  try {
    const { spawn } = require('child_process');

    return new Promise((resolve, reject) => {
      const child = spawn('node', ['scripts/seed-permissions.js'], {
        stdio: 'inherit',
        cwd: process.cwd()
      });

      child.on('close', (code) => {
        if (code === 0) {
          console.log('   ✅ Permissions seeding completed');
          resolve();
        } else {
          reject(new Error(`Permissions seeding failed with code ${code}`));
        }
      });
    });
  } catch (error) {
    console.error('   ❌ Error running permissions seeding:', error.message);
    throw error;
  }
}

/**
 * Step 4: Create roles and assign permissions
 */
async function seedRoles() {
  console.log('🎭 Step 4: Creating roles and assigning permissions...');

  try {
    const { spawn } = require('child_process');

    return new Promise((resolve, reject) => {
      const child = spawn('node', ['scripts/seed-rbac.js'], {
        stdio: 'inherit',
        cwd: process.cwd()
      });

      child.on('close', (code) => {
        if (code === 0) {
          console.log('   ✅ RBAC seeding completed');
          resolve();
        } else {
          reject(new Error(`RBAC seeding failed with code ${code}`));
        }
      });
    });
  } catch (error) {
    console.error('   ❌ Error running RBAC seeding:', error.message);
    throw error;
  }
}

/**
 * Step 5a: Create lead sources
 */
async function seedLeadSources() {
  console.log('📊 Step 5a: Creating lead sources...');

  try {
    const { seedLeadSources } = require('./seed-lead-sources.js');
    await seedLeadSources();
    console.log('   ✅ Lead sources seeding completed');
  } catch (error) {
    console.error('   ❌ Error running lead sources seeding:', error.message);
    throw error;
  }
}

/**
 * Step 5b: Create sales stages
 */
async function seedSalesStages() {
  console.log('📈 Step 5b: Creating sales stages...');

  try {
    const { seedSalesStages } = require('./seed-sales-stages.js');
    await seedSalesStages();
    console.log('   ✅ Sales stages seeding completed');
  } catch (error) {
    console.error('   ❌ Error running sales stages seeding:', error.message);
    throw error;
  }
}

/**
 * Step 6: Create sample data (optional)
 */
async function seedSampleData(tenant, user) {
  console.log('📝 Step 6: Creating sample data...');

  const sampleContacts = [
    {
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
      phone: '******-0123',
      title: 'CEO',
      tenantId: tenant.id,
      createdById: user.id
    },
    {
      firstName: 'Jane',
      lastName: 'Smith',
      email: '<EMAIL>',
      phone: '******-0124',
      title: 'CTO',
      tenantId: tenant.id,
      createdById: user.id
    }
  ];

  for (const contactData of sampleContacts) {
    // Check if contact exists first
    const existingContact = await prisma.contact.findFirst({
      where: {
        tenantId: tenant.id,
        email: contactData.email
      }
    });

    if (!existingContact) {
      await prisma.contact.create({
        data: contactData
      });
    }
  }

  console.log(`   ✅ Created ${sampleContacts.length} sample contacts`);
}

/**
 * Print final summary of seeded data
 */
async function printFinalSummary() {
  console.log('\n📊 Final Database Summary:');
  const finalCounts = await checkExistingData();

  const totalItems = Object.values(finalCounts).reduce((sum, count) => sum + count, 0);
  console.log(`\n🎯 Total items seeded: ${totalItems}`);

  const tenants = await prisma.tenant.findMany({ select: { name: true, slug: true } });
  const plans = await prisma.subscriptionPlan.findMany({ select: { name: true } });

  console.log('\n🏢 Tenants created:');
  tenants.forEach(tenant => console.log(`   • ${tenant.name} (${tenant.slug})`));

  console.log('\n💳 Subscription plans available:');
  plans.forEach(plan => console.log(`   • ${plan.name}`));
}

// Run the master seeding if this script is called directly
if (require.main === module) {
  masterSeed().catch(console.error);
}

module.exports = { masterSeed, checkExistingData };
