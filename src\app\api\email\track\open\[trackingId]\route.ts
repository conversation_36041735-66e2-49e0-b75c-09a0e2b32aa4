import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

export async function GET(
  request: NextRequest,
  { params }: { params: { trackingId: string } }
) {
  try {
    const trackingId = params.trackingId;
    
    // Parse tracking ID to extract tenant, user, and timestamp info
    const [tenantId, userId, timestamp, randomId] = trackingId.split('-');
    
    if (!tenantId || !userId) {
      // Return a 1x1 transparent pixel even for invalid tracking IDs
      return new NextResponse(
        Buffer.from('R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7', 'base64'),
        {
          status: 200,
          headers: {
            'Content-Type': 'image/gif',
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'Pragma': 'no-cache',
            'Expires': '0'
          }
        }
      );
    }

    // Try to find and update the email log
    try {
      // Find email logs that match the criteria and haven't been opened yet
      const emailLogs = await prisma.emailLog.findMany({
        where: {
          tenantId,
          sentBy: userId,
          openedAt: null,
          sentAt: {
            gte: new Date(parseInt(timestamp) - 3600000), // Within 1 hour of timestamp
            lte: new Date(parseInt(timestamp) + 3600000)
          }
        },
        orderBy: {
          sentAt: 'desc'
        },
        take: 1
      });

      if (emailLogs.length > 0) {
        const emailLog = emailLogs[0];
        
        // Update the email log with open tracking
        await prisma.emailLog.update({
          where: { id: emailLog.id },
          data: {
            openedAt: new Date(),
            status: 'opened'
          }
        });

        console.log(`Email opened: ${emailLog.id} by tracking ID: ${trackingId}`);
      }
    } catch (error) {
      console.error('Error tracking email open:', error);
      // Continue to return the tracking pixel even if logging fails
    }

    // Return a 1x1 transparent GIF pixel
    const pixel = Buffer.from('R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7', 'base64');
    
    return new NextResponse(pixel, {
      status: 200,
      headers: {
        'Content-Type': 'image/gif',
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0',
        'Content-Length': pixel.length.toString()
      }
    });

  } catch (error) {
    console.error('Email tracking error:', error);
    
    // Always return a tracking pixel, even on error
    const pixel = Buffer.from('R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7', 'base64');
    
    return new NextResponse(pixel, {
      status: 200,
      headers: {
        'Content-Type': 'image/gif',
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    });
  }
}
