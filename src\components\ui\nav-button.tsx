"use client"

import React, { memo } from 'react'
import { Button, type ButtonProps } from './button'
import { SmoothLink } from './smooth-link'

interface NavButtonProps extends Omit<ButtonProps, 'onClick'> {
  href?: string
  onClick?: (e: React.MouseEvent) => void
  replace?: boolean
}

export const NavButton = memo(function NavButton({
  href,
  onClick,
  replace = false,
  children,
  ...props
}: NavButtonProps) {
  const handleClick = (e: React.MouseEvent) => {
    if (onClick) {
      onClick(e)
    }
  }

  if (href) {
    return (
      <SmoothLink href={href} replace={replace}>
        <Button {...props} onClick={handleClick}>
          {children}
        </Button>
      </SmoothLink>
    )
  }

  return (
    <Button {...props} onClick={handleClick}>
      {children}
    </Button>
  )
})
