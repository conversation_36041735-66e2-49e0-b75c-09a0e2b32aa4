import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { PermissionResource, PermissionAction } from '@/types';

export async function hasPermission(
  resource: PermissionResource,
  action: PermissionAction,
  tenantId?: string,
  ownerId?: string | null
): Promise<boolean> {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) return false;

    // Platform admins have all permissions
    if (session.user.isPlatformAdmin) return true;

    // Get user's current tenant
    const currentTenant = session.user.tenants?.[0];
    if (!currentTenant) return false;

    const userTenantId = tenantId || currentTenant.id;

    // Tenant admins have all permissions within their tenant
    if (currentTenant.isTenantAdmin && currentTenant.id === userTenantId) {
      return true;
    }

    // Check specific permissions
    const userPermissions = await prisma.userPermission.findMany({
      where: {
        userId: session.user.id,
        tenantId: userTenantId,
      },
      include: {
        permission: true,
      },
    });

    // Check if user has the specific permission
    const hasDirectPermission = userPermissions.some(up => 
      up.permission.resource === resource && up.permission.action === action
    );

    if (!hasDirectPermission) return false;

    // If ownership is required, check if user owns the resource
    if (ownerId && ownerId !== session.user.id) {
      return false;
    }

    return true;
  } catch (error) {
    console.error('Error checking permissions:', error);
    return false;
  }
}
