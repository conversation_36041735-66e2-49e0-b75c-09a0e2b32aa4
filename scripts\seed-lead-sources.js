const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

const defaultLeadSources = [
  {
    name: 'Website',
    description: 'Leads from company website',
    category: 'digital',
    icon: '🌐',
    color: '#3B82F6',
    isActive: true,
    sortOrder: 1
  },
  {
    name: 'Google Ads',
    description: 'Leads from Google advertising campaigns',
    category: 'paid_advertising',
    icon: '🎯',
    color: '#10B981',
    isActive: true,
    sortOrder: 2
  },
  {
    name: 'Social Media',
    description: 'Leads from social media platforms',
    category: 'digital',
    icon: '📱',
    color: '#8B5CF6',
    isActive: true,
    sortOrder: 3
  },
  {
    name: 'Email Campaign',
    description: 'Leads from email marketing campaigns',
    category: 'digital',
    icon: '📧',
    color: '#F59E0B',
    isActive: true,
    sortOrder: 4
  },
  {
    name: 'Referral',
    description: 'Leads from customer referrals',
    category: 'word_of_mouth',
    icon: '👥',
    color: '#EF4444',
    isActive: true,
    sortOrder: 5
  },
  {
    name: 'Cold Call',
    description: 'Leads from cold calling efforts',
    category: 'outbound',
    icon: '📞',
    color: '#6366F1',
    isActive: true,
    sortOrder: 6
  },
  {
    name: 'Trade Show',
    description: 'Leads from trade shows and events',
    category: 'events',
    icon: '🏢',
    color: '#EC4899',
    isActive: true,
    sortOrder: 7
  },
  {
    name: 'Partner',
    description: 'Leads from business partners',
    category: 'partnerships',
    icon: '🤝',
    color: '#14B8A6',
    isActive: true,
    sortOrder: 8
  }
];

async function seedLeadSources() {
  try {
    console.log('🌱 Seeding lead sources...');

    // Get all tenants
    const tenants = await prisma.tenant.findMany();
    
    if (tenants.length === 0) {
      console.log('❌ No tenants found. Please create a tenant first.');
      return;
    }

    for (const tenant of tenants) {
      console.log(`📦 Seeding lead sources for tenant: ${tenant.name}`);
      
      // Check if lead sources already exist for this tenant
      const existingCount = await prisma.leadSource.count({
        where: { tenantId: tenant.id }
      });

      if (existingCount > 0) {
        console.log(`⚠️  Tenant ${tenant.name} already has ${existingCount} lead sources. Skipping...`);
        continue;
      }

      // Create lead sources for this tenant
      for (const source of defaultLeadSources) {
        await prisma.leadSource.create({
          data: {
            ...source,
            tenantId: tenant.id,
            createdById: null // System created
          }
        });
      }

      console.log(`✅ Created ${defaultLeadSources.length} lead sources for tenant: ${tenant.name}`);
    }

    console.log('🎉 Lead sources seeding completed!');
  } catch (error) {
    console.error('❌ Error seeding lead sources:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the seed function
if (require.main === module) {
  seedLeadSources()
    .catch((error) => {
      console.error(error);
      process.exit(1);
    });
}

module.exports = { seedLeadSources };
