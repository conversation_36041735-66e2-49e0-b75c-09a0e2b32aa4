import { NextResponse } from 'next/server';
import { withPermission, AuthenticatedRequest } from '@/lib/permission-middleware';
import { PermissionAction, PermissionResource } from '@/types';
import { prisma } from '@/lib/prisma';
import { hashPassword, validatePasswordStrength } from '@/lib/password-utils';
import { z } from 'zod';

const updatePasswordSchema = z.object({
  newPassword: z.string().min(8, 'Password must be at least 8 characters'),
});

/**
 * PUT /api/users/[id]/password
 * Update user password (admin action)
 */
export const PUT = withPermission({
  resource: PermissionResource.USERS,
  action: PermissionAction.UPDATE,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest, { params }: { params: { id: string } }) => {
  try {
    const userId = params.id;
    const body = await req.json();
    const validatedData = updatePasswordSchema.parse(body);

    // Validate password strength
    const passwordValidation = validatePasswordStrength(validatedData.newPassword);
    if (!passwordValidation.isValid) {
      return NextResponse.json(
        { 
          error: 'Password does not meet security requirements',
          details: passwordValidation.errors
        },
        { status: 400 }
      );
    }

    // Check if user exists and is in the current tenant
    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: {
        tenantUsers: {
          where: { tenantId: req.tenantId },
        },
      },
    });

    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    if (user.tenantUsers.length === 0) {
      return NextResponse.json(
        { error: 'User not found in this tenant' },
        { status: 404 }
      );
    }

    // Hash the new password
    const hashedPassword = await hashPassword(validatedData.newPassword);

    // Update the password
    await prisma.user.update({
      where: { id: userId },
      data: { 
        passwordHash: hashedPassword,
        // Force password change on next login if this is an admin reset
        // You might want to add a field for this
      },
    });

    // Log the password change
    await prisma.auditLog.create({
      data: {
        tenantId: req.tenantId,
        userId: req.userId,
        action: 'USER_PASSWORD_RESET',
        resourceType: 'user',
        resourceId: userId,
        newValues: {
          resetBy: req.userId,
          resetAt: new Date().toISOString(),
        },
      },
    });

    // Send notification to user about password change
    try {
      const { UserNotificationService } = await import('@/services/user-notification-service');
      const notificationService = new UserNotificationService();
      
      await notificationService.createPortalNotification({
        userId: userId,
        tenantId: req.tenantId,
        type: 'password_reset',
        title: 'Password Changed',
        message: 'Your password has been changed by an administrator. Please log in with your new password.',
        data: {
          changedBy: req.userId,
          changedAt: new Date().toISOString(),
        },
      });
    } catch (error) {
      console.error('Failed to send password change notification:', error);
      // Don't fail the password change if notification fails
    }

    return NextResponse.json({
      success: true,
      message: 'Password updated successfully'
    });

  } catch (error) {
    console.error('Password update error:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          error: 'Invalid request data',
          details: error.errors.map(e => e.message)
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to update password' },
      { status: 500 }
    );
  }
});
