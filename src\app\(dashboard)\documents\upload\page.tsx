'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { PageLayout } from '@/components/layout/page-layout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { PermissionGate } from '@/components/ui/permission-gate';
import { PermissionResource, PermissionAction } from '@/types';
import { useToast } from '@/hooks/use-toast';
interface FileUploadItem {
  file: File;
  id: string;
  progress: number;
  status: 'pending' | 'uploading' | 'completed' | 'error';
  error?: string;
}
import { useDropzone } from 'react-dropzone';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Switch } from '@/components/ui/switch';
import { DocumentAnalysisResponse } from '@/services/ai-document-analysis';
import {
  ArrowLeft,
  Upload,
  FileText,
  X,
  CheckCircle,
  AlertCircle,
  Loader2,
  Sparkles,
  Wand2,
} from 'lucide-react';
import { v4 as uuidv4 } from 'uuid';
import { useCallback, useEffect } from 'react';

interface UploadState {
  files: FileUploadItem[];
  uploading: boolean;
  relatedToType: string;
  relatedToId: string;
  description: string;
  tags: string[];
  tagInput: string;
  isPublic: boolean;
  aiAvailable: boolean;
  enableAiAnalysis: boolean;
  analyzing: boolean;
  aiAnalysis: DocumentAnalysisResponse | null;
}

interface SimpleFileUploadDropzoneProps {
  onFilesAdded: (files: File[]) => void;
  disabled?: boolean;
}

function SimpleFileUploadDropzone({ onFilesAdded, disabled = false }: SimpleFileUploadDropzoneProps) {
  const onDrop = useCallback(
    (acceptedFiles: File[]) => {
      onFilesAdded(acceptedFiles);
    },
    [onFilesAdded]
  );

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    disabled,
    multiple: true,
    accept: {
      'application/pdf': ['.pdf'],
      'application/msword': ['.doc'],
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
      'application/vnd.ms-excel': ['.xls'],
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'],
      'image/*': ['.png', '.jpg', '.jpeg', '.gif', '.webp'],
      'text/plain': ['.txt'],
      'text/csv': ['.csv'],
    },
    maxSize: 50 * 1024 * 1024, // 50MB
  });

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div
      {...getRootProps()}
      className={`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors ${
        isDragActive
          ? 'border-blue-500 bg-blue-50'
          : 'border-gray-300 hover:border-gray-400'
      } ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
    >
      <input {...getInputProps()} />
      <Upload className={`w-12 h-12 mx-auto mb-4 ${isDragActive ? 'text-blue-500' : 'text-gray-400'}`} />
      <h3 className="text-lg font-medium text-gray-900 mb-2">
        {isDragActive ? 'Drop files here' : 'Upload documents'}
      </h3>
      <p className="text-sm text-gray-500 mb-4">
        Drag and drop files here, or click to browse
      </p>
      <div className="text-xs text-gray-400 space-y-1">
        <p>Maximum file size: {formatFileSize(50 * 1024 * 1024)}</p>
        <p>Supported formats: PDF, DOC, XLS, Images, Text files</p>
      </div>
      {!disabled && (
        <Button variant="outline" className="mt-4">
          Choose Files
        </Button>
      )}
    </div>
  );
}

export default function DocumentUploadPage() {
  const router = useRouter();
  const { toast } = useToast();

  const [state, setState] = useState<UploadState>({
    files: [],
    uploading: false,
    relatedToType: 'general',
    relatedToId: 'system',
    description: '',
    tags: [],
    tagInput: '',
    isPublic: false,
    aiAvailable: false,
    enableAiAnalysis: false,
    analyzing: false,
    aiAnalysis: null,
  });

  // Check AI availability
  const checkAiAvailability = async () => {
    try {
      const response = await fetch('/api/documents/analyze');
      const data = await response.json();
      setState(prev => ({ ...prev, aiAvailable: data.success && data.data.available }));
    } catch (error) {
      console.error('Failed to check AI availability:', error);
      setState(prev => ({ ...prev, aiAvailable: false }));
    }
  };

  // Analyze document with AI
  const analyzeDocument = async (file: File) => {
    if (!state.aiAvailable || state.analyzing) return;

    setState(prev => ({ ...prev, analyzing: true, aiAnalysis: null }));

    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('relatedToType', state.relatedToType);
      formData.append('relatedToId', state.relatedToId);

      const response = await fetch('/api/documents/analyze', {
        method: 'POST',
        body: formData,
      });

      const data = await response.json();

      if (response.ok) {
        setState(prev => ({ ...prev, aiAnalysis: data.data }));
        // Auto-populate description and tags
        if (data.data.suggestedDescription && !state.description) {
          setState(prev => ({ ...prev, description: data.data.suggestedDescription }));
        }
        if (data.data.keyTopics && data.data.keyTopics.length > 0) {
          const newTags = [...state.tags, ...data.data.keyTopics.slice(0, 3)];
          const uniqueTags = [...new Set(newTags)];
          setState(prev => ({ ...prev, tags: uniqueTags }));
        }
        toast({
          title: 'Success',
          description: 'Document analyzed successfully',
        });
      } else {
        throw new Error(data.error || 'Analysis failed');
      }
    } catch (error) {
      console.error('Error analyzing document:', error);
      toast({
        title: 'Error',
        description: 'Failed to analyze document with AI',
        variant: 'destructive',
      });
    } finally {
      setState(prev => ({ ...prev, analyzing: false }));
    }
  };

  // Apply AI suggestions manually
  const applyAiSuggestions = () => {
    if (!state.aiAnalysis) return;

    setState(prev => ({
      ...prev,
      description: state.aiAnalysis!.suggestedDescription,
      tags: state.aiAnalysis!.keyTopics ? [...prev.tags, ...state.aiAnalysis!.keyTopics.slice(0, 3)] : prev.tags
    }));

    toast({
      title: 'Applied',
      description: 'AI suggestions have been applied',
    });
  };

  const handleFilesAdded = (files: File[]) => {
    const newItems: FileUploadItem[] = files.map(file => ({
      file,
      id: uuidv4(),
      progress: 0,
      status: 'pending',
    }));

    setState(prev => ({
      ...prev,
      files: [...prev.files, ...newItems],
    }));

    // Trigger AI analysis for the first file if enabled
    if (files.length > 0 && state.aiAvailable && state.enableAiAnalysis) {
      analyzeDocument(files[0]);
    }
  };

  const handleRemoveFile = (id: string) => {
    setState(prev => ({
      ...prev,
      files: prev.files.filter(item => item.id !== id),
    }));
  };

  const handleAddTag = () => {
    if (state.tagInput.trim() && !state.tags.includes(state.tagInput.trim())) {
      setState(prev => ({
        ...prev,
        tags: [...prev.tags, prev.tagInput.trim()],
        tagInput: '',
      }));
    }
  };

  const handleRemoveTag = (tag: string) => {
    setState(prev => ({
      ...prev,
      tags: prev.tags.filter(t => t !== tag),
    }));
  };

  // Check AI availability on mount
  useEffect(() => {
    checkAiAvailability();
  }, []);

  // Trigger analysis when AI is enabled and files are available
  useEffect(() => {
    if (state.enableAiAnalysis && state.files.length > 0 && state.aiAvailable && !state.analyzing && !state.aiAnalysis) {
      analyzeDocument(state.files[0].file);
    }
  }, [state.enableAiAnalysis, state.files.length, state.aiAvailable]);

  const handleUpload = async () => {
    if (state.files.length === 0) {
      toast({
        title: 'Error',
        description: 'Please select at least one file to upload',
        variant: 'destructive',
      });
      return;
    }

    setState(prev => ({ ...prev, uploading: true }));

    try {
      const uploadPromises = state.files.map(async (item) => {
        const formData = new FormData();
        formData.append('file', item.file);
        formData.append('relatedToType', state.relatedToType);
        formData.append('relatedToId', state.relatedToId);
        formData.append('description', state.description);
        formData.append('tags', JSON.stringify(state.tags));
        formData.append('isPublic', state.isPublic.toString());

        // Update progress
        setState(prev => ({
          ...prev,
          files: prev.files.map(f => 
            f.id === item.id 
              ? { ...f, status: 'uploading', progress: 0 }
              : f
          ),
        }));

        const response = await fetch('/api/documents', {
          method: 'POST',
          body: formData,
        });

        if (response.ok) {
          setState(prev => ({
            ...prev,
            files: prev.files.map(f => 
              f.id === item.id 
                ? { ...f, status: 'completed', progress: 100 }
                : f
            ),
          }));
          return { success: true, file: item.file.name };
        } else {
          const errorData = await response.json();
          setState(prev => ({
            ...prev,
            files: prev.files.map(f => 
              f.id === item.id 
                ? { ...f, status: 'error', progress: 0, error: errorData.error }
                : f
            ),
          }));
          return { success: false, file: item.file.name, error: errorData.error };
        }
      });

      const results = await Promise.all(uploadPromises);
      const successful = results.filter(r => r.success).length;
      const failed = results.filter(r => !r.success).length;

      if (successful > 0) {
        toast({
          title: 'Upload Complete',
          description: `${successful} file(s) uploaded successfully${failed > 0 ? `, ${failed} failed` : ''}`,
        });
      }

      if (failed === 0) {
        // All uploads successful, redirect to documents page
        setTimeout(() => {
          router.push('/documents');
        }, 2000);
      }
    } catch (error) {
      console.error('Upload error:', error);
      toast({
        title: 'Upload Error',
        description: 'An unexpected error occurred during upload',
        variant: 'destructive',
      });
    } finally {
      setState(prev => ({ ...prev, uploading: false }));
    }
  };

  const getStatusIcon = (status: FileUploadItem['status']) => {
    switch (status) {
      case 'uploading':
        return <Loader2 className="w-4 h-4 animate-spin text-blue-500" />;
      case 'completed':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'error':
        return <AlertCircle className="w-4 h-4 text-red-500" />;
      default:
        return <FileText className="w-4 h-4 text-gray-400" />;
    }
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const actions = (
    <div className="flex items-center gap-2">
      <Button variant="outline" size="sm" onClick={() => router.back()}>
        <ArrowLeft className="h-4 w-4 mr-2" />
        Back
      </Button>
      <Button 
        size="sm" 
        onClick={handleUpload}
        disabled={state.files.length === 0 || state.uploading}
      >
        <Upload className="h-4 w-4 mr-2" />
        {state.uploading ? 'Uploading...' : `Upload ${state.files.length} File(s)`}
      </Button>
    </div>
  );

  return (
    <PermissionGate
      resource={PermissionResource.DOCUMENTS}
      action={PermissionAction.CREATE}
      fallback={
        <PageLayout>
          <Card>
            <CardContent className="p-8">
              <div className="text-center">
                <AlertCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">Access Denied</h3>
                <p className="text-gray-500">
                  You don't have permission to upload documents.
                </p>
              </div>
            </CardContent>
          </Card>
        </PageLayout>
      }
    >
      <PageLayout
        title="Upload Documents"
        description="Upload and organize documents in your system"
        actions={actions}
      >
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Upload Area */}
          <div className="lg:col-span-2 space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Select Files</CardTitle>
              </CardHeader>
              <CardContent>
                <SimpleFileUploadDropzone
                  onFilesAdded={handleFilesAdded}
                  disabled={state.uploading}
                />
              </CardContent>
            </Card>

            {/* File List */}
            {state.files.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle>Files to Upload ({state.files.length})</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {state.files.map((item) => (
                      <div key={item.id} className="flex items-center space-x-3 p-3 border rounded-lg">
                        <div className="flex-shrink-0">
                          {getStatusIcon(item.status)}
                        </div>
                        
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center justify-between">
                            <h4 className="text-sm font-medium text-gray-900 truncate">
                              {item.file.name}
                            </h4>
                            {!state.uploading && (
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleRemoveFile(item.id)}
                              >
                                <X className="w-4 h-4" />
                              </Button>
                            )}
                          </div>
                          
                          <div className="flex items-center justify-between text-xs text-gray-500 mt-1">
                            <span>{formatFileSize(item.file.size)}</span>
                            <span>{item.file.type}</span>
                          </div>
                          
                          {item.status === 'uploading' && (
                            <Progress value={item.progress} className="mt-2" />
                          )}
                          
                          {item.status === 'error' && item.error && (
                            <p className="text-xs text-red-600 mt-1">{item.error}</p>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}
          </div>

          {/* Metadata */}
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Document Details</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="relatedToType">Related To</Label>
                  <Select 
                    value={state.relatedToType} 
                    onValueChange={(value) => setState(prev => ({ ...prev, relatedToType: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="general">General</SelectItem>
                      <SelectItem value="opportunity">Opportunity</SelectItem>
                      <SelectItem value="contact">Contact</SelectItem>
                      <SelectItem value="company">Company</SelectItem>
                      <SelectItem value="lead">Lead</SelectItem>
                      <SelectItem value="proposal">Proposal</SelectItem>
                      <SelectItem value="project">Project</SelectItem>
                      <SelectItem value="handover">Handover</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    placeholder="Optional description for the documents..."
                    value={state.description}
                    onChange={(e) => setState(prev => ({ ...prev, description: e.target.value }))}
                    rows={3}
                  />
                </div>

                <div>
                  <Label htmlFor="tags">Tags</Label>
                  <div className="flex space-x-2">
                    <Input
                      id="tags"
                      placeholder="Add a tag..."
                      value={state.tagInput}
                      onChange={(e) => setState(prev => ({ ...prev, tagInput: e.target.value }))}
                      onKeyPress={(e) => {
                        if (e.key === 'Enter') {
                          e.preventDefault();
                          handleAddTag();
                        }
                      }}
                    />
                    <Button type="button" onClick={handleAddTag} size="sm">
                      Add
                    </Button>
                  </div>
                  
                  {state.tags.length > 0 && (
                    <div className="flex flex-wrap gap-2 mt-2">
                      {state.tags.map((tag) => (
                        <Badge key={tag} variant="secondary" className="text-xs">
                          {tag}
                          <button
                            onClick={() => handleRemoveTag(tag)}
                            className="ml-1 hover:text-red-600"
                          >
                            <X className="w-3 h-3" />
                          </button>
                        </Badge>
                      ))}
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* AI Analysis Section */}
            {state.aiAvailable && state.files.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Sparkles className="w-5 h-5 text-blue-500" />
                    <span>AI Document Analysis</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <Switch
                        checked={state.enableAiAnalysis}
                        onCheckedChange={(checked) => setState(prev => ({ ...prev, enableAiAnalysis: checked }))}
                        disabled={state.uploading}
                      />
                      <span className="text-sm text-gray-600">
                        {state.enableAiAnalysis ? 'Enabled' : 'Disabled'}
                      </span>
                    </div>
                    {state.enableAiAnalysis && !state.analyzing && !state.aiAnalysis && (
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => analyzeDocument(state.files[0].file)}
                        disabled={state.uploading}
                      >
                        <Wand2 className="w-4 h-4 mr-2" />
                        Analyze Document
                      </Button>
                    )}
                  </div>

                  {!state.enableAiAnalysis && (
                    <div className="text-sm text-gray-600 p-3 bg-gray-50 rounded-lg">
                      Enable AI analysis to get automatic suggestions for document description and tags.
                    </div>
                  )}

                  {state.enableAiAnalysis && state.analyzing && (
                    <div className="flex items-center space-x-2 text-sm text-gray-600 p-3 bg-blue-50 rounded-lg">
                      <div className="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
                      <span>Analyzing document content...</span>
                    </div>
                  )}

                  {state.enableAiAnalysis && state.aiAnalysis && (
                    <div className="space-y-3 p-3 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg border">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <label className="text-xs font-medium text-gray-700">Suggested Title</label>
                          <div className="p-2 bg-white rounded border text-sm">
                            {state.aiAnalysis.suggestedTitle}
                          </div>
                        </div>
                        <div className="space-y-2">
                          <label className="text-xs font-medium text-gray-700">Document Type</label>
                          <div className="p-2 bg-white rounded border text-sm">
                            {state.aiAnalysis.documentType}
                          </div>
                        </div>
                      </div>

                      <div className="space-y-2">
                        <label className="text-xs font-medium text-gray-700">Suggested Description</label>
                        <div className="p-2 bg-white rounded border text-sm">
                          {state.aiAnalysis.suggestedDescription}
                        </div>
                      </div>

                      {state.aiAnalysis.keyTopics && state.aiAnalysis.keyTopics.length > 0 && (
                        <div className="space-y-2">
                          <label className="text-xs font-medium text-gray-700">Key Topics</label>
                          <div className="flex flex-wrap gap-1">
                            {state.aiAnalysis.keyTopics.map((topic, index) => (
                              <Badge key={index} variant="outline" className="text-xs">
                                {topic}
                              </Badge>
                            ))}
                          </div>
                        </div>
                      )}

                      <div className="flex items-center justify-between pt-2">
                        <div className="text-xs text-gray-500">
                          Confidence: {state.aiAnalysis.confidence}% •
                          Processed in {Math.round(state.aiAnalysis.processingTime / 1000)}s
                        </div>
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={applyAiSuggestions}
                          disabled={state.uploading}
                        >
                          Apply Suggestions
                        </Button>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </PageLayout>
    </PermissionGate>
  );
}
