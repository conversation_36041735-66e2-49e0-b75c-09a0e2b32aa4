import { z } from 'zod';
import { prisma } from '@/lib/prisma';
import { leadManagementService, LeadWithRelations } from './lead-management';

// Validation schemas
export const crmConfigSchema = z.object({
  provider: z.enum(['salesforce', 'hubspot', 'pipedrive', 'zoho', 'custom']),
  apiKey: z.string().min(1, 'API key is required'),
  apiSecret: z.string().optional(),
  instanceUrl: z.string().url().optional(),
  refreshToken: z.string().optional(),
  accessToken: z.string().optional(),
  syncDirection: z.enum(['bidirectional', 'import_only', 'export_only']).default('bidirectional'),
  syncFrequency: z.enum(['realtime', 'hourly', 'daily', 'weekly']).default('hourly'),
  fieldMapping: z.record(z.string()).optional(),
  enabled: z.boolean().default(true)
});

export const syncLogSchema = z.object({
  operation: z.enum(['import', 'export', 'update', 'delete']),
  entityType: z.enum(['lead', 'contact', 'company', 'opportunity']),
  entityId: z.string(),
  externalId: z.string().optional(),
  status: z.enum(['success', 'failed', 'skipped']),
  errorMessage: z.string().optional(),
  data: z.record(z.any()).optional()
});

export type CRMConfig = z.infer<typeof crmConfigSchema>;
export type SyncLog = z.infer<typeof syncLogSchema>;

export interface CRMIntegration {
  id: string;
  tenantId: string;
  provider: string;
  config: CRMConfig;
  lastSyncAt?: Date;
  syncStatus: 'active' | 'paused' | 'error';
  errorMessage?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface SyncResult {
  success: boolean;
  imported: number;
  exported: number;
  updated: number;
  errors: string[];
  logs: SyncLog[];
}

export abstract class BaseCRMProvider {
  protected config: CRMConfig;
  protected tenantId: string;

  constructor(config: CRMConfig, tenantId: string) {
    this.config = config;
    this.tenantId = tenantId;
  }

  abstract authenticate(): Promise<boolean>;
  abstract importLeads(lastSync?: Date): Promise<any[]>;
  abstract exportLead(lead: LeadWithRelations): Promise<string>;
  abstract updateLead(externalId: string, lead: LeadWithRelations): Promise<boolean>;
  abstract deleteLead(externalId: string): Promise<boolean>;
  abstract testConnection(): Promise<boolean>;
}

export class SalesforceCRMProvider extends BaseCRMProvider {
  async authenticate(): Promise<boolean> {
    try {
      // Implement Salesforce OAuth authentication
      const response = await fetch(`${this.config.instanceUrl}/services/oauth2/token`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        },
        body: new URLSearchParams({
          grant_type: 'refresh_token',
          client_id: this.config.apiKey,
          client_secret: this.config.apiSecret || '',
          refresh_token: this.config.refreshToken || ''
        })
      });

      if (response.ok) {
        const data = await response.json();
        this.config.accessToken = data.access_token;
        return true;
      }
      return false;
    } catch (error) {
      console.error('Salesforce authentication error:', error);
      return false;
    }
  }

  async importLeads(lastSync?: Date): Promise<any[]> {
    if (!await this.authenticate()) {
      throw new Error('Authentication failed');
    }

    const query = lastSync 
      ? `SELECT Id, FirstName, LastName, Email, Company, Status, CreatedDate FROM Lead WHERE LastModifiedDate > ${lastSync.toISOString()}`
      : `SELECT Id, FirstName, LastName, Email, Company, Status, CreatedDate FROM Lead LIMIT 1000`;

    const response = await fetch(`${this.config.instanceUrl}/services/data/v58.0/query?q=${encodeURIComponent(query)}`, {
      headers: {
        'Authorization': `Bearer ${this.config.accessToken}`,
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      throw new Error(`Salesforce API error: ${response.statusText}`);
    }

    const data = await response.json();
    return data.records || [];
  }

  async exportLead(lead: LeadWithRelations): Promise<string> {
    if (!await this.authenticate()) {
      throw new Error('Authentication failed');
    }

    const salesforceData = this.mapLeadToSalesforce(lead);

    const response = await fetch(`${this.config.instanceUrl}/services/data/v58.0/sobjects/Lead`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.config.accessToken}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(salesforceData)
    });

    if (!response.ok) {
      throw new Error(`Failed to export lead: ${response.statusText}`);
    }

    const result = await response.json();
    return result.id;
  }

  async updateLead(externalId: string, lead: LeadWithRelations): Promise<boolean> {
    if (!await this.authenticate()) {
      throw new Error('Authentication failed');
    }

    const salesforceData = this.mapLeadToSalesforce(lead);

    const response = await fetch(`${this.config.instanceUrl}/services/data/v58.0/sobjects/Lead/${externalId}`, {
      method: 'PATCH',
      headers: {
        'Authorization': `Bearer ${this.config.accessToken}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(salesforceData)
    });

    return response.ok;
  }

  async deleteLead(externalId: string): Promise<boolean> {
    if (!await this.authenticate()) {
      throw new Error('Authentication failed');
    }

    const response = await fetch(`${this.config.instanceUrl}/services/data/v58.0/sobjects/Lead/${externalId}`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${this.config.accessToken}`
      }
    });

    return response.ok;
  }

  async testConnection(): Promise<boolean> {
    try {
      return await this.authenticate();
    } catch (error) {
      return false;
    }
  }

  private mapLeadToSalesforce(lead: LeadWithRelations): any {
    return {
      FirstName: lead.contact?.firstName,
      LastName: lead.contact?.lastName || 'Unknown',
      Email: lead.contact?.email,
      Phone: lead.contact?.phone,
      Company: lead.company?.name || 'Unknown',
      Title: lead.title,
      Description: lead.description,
      Status: this.mapStatusToSalesforce(lead.status),
      LeadSource: lead.source,
      Rating: this.mapPriorityToRating(lead.priority)
    };
  }

  private mapStatusToSalesforce(status: string): string {
    const statusMap: Record<string, string> = {
      'new': 'Open - Not Contacted',
      'contacted': 'Working - Contacted',
      'qualified': 'Qualified',
      'proposal': 'Qualified',
      'negotiation': 'Qualified',
      'closed_won': 'Closed - Converted',
      'closed_lost': 'Closed - Not Converted'
    };
    return statusMap[status] || 'Open - Not Contacted';
  }

  private mapPriorityToRating(priority: string): string {
    const ratingMap: Record<string, string> = {
      'low': 'Cold',
      'medium': 'Warm',
      'high': 'Hot',
      'urgent': 'Hot'
    };
    return ratingMap[priority] || 'Warm';
  }
}

export class CRMIntegrationService {
  /**
   * Create or update CRM integration
   */
  async setupCRMIntegration(
    tenantId: string,
    config: CRMConfig,
    userId: string
  ): Promise<CRMIntegration> {
    // Validate configuration
    const validatedConfig = crmConfigSchema.parse(config);

    // Test connection
    const provider = this.createProvider(validatedConfig, tenantId);
    const connectionTest = await provider.testConnection();

    if (!connectionTest) {
      throw new Error('Failed to connect to CRM. Please check your configuration.');
    }

    // Save integration
    const integration = await prisma.crmIntegration.upsert({
      where: {
        tenantId_provider: {
          tenantId,
          provider: validatedConfig.provider
        }
      },
      create: {
        tenantId,
        provider: validatedConfig.provider,
        config: validatedConfig,
        syncStatus: 'active',
        createdById: userId
      },
      update: {
        config: validatedConfig,
        syncStatus: 'active',
        errorMessage: null,
        updatedById: userId
      }
    });

    return integration as CRMIntegration;
  }

  /**
   * Sync leads with CRM
   */
  async syncLeads(tenantId: string, provider: string): Promise<SyncResult> {
    const integration = await prisma.crmIntegration.findUnique({
      where: {
        tenantId_provider: {
          tenantId,
          provider
        }
      }
    });

    if (!integration || !integration.config) {
      throw new Error('CRM integration not found or not configured');
    }

    const crmProvider = this.createProvider(integration.config as CRMConfig, tenantId);
    const logs: SyncLog[] = [];
    let imported = 0;
    let exported = 0;
    let updated = 0;
    const errors: string[] = [];

    try {
      // Import leads from CRM
      if (integration.config.syncDirection !== 'export_only') {
        const externalLeads = await crmProvider.importLeads(integration.lastSyncAt || undefined);
        
        for (const externalLead of externalLeads) {
          try {
            await this.importLeadFromCRM(tenantId, externalLead, provider);
            imported++;
            logs.push({
              operation: 'import',
              entityType: 'lead',
              entityId: externalLead.Id,
              status: 'success'
            });
          } catch (error) {
            const errorMsg = error instanceof Error ? error.message : 'Unknown error';
            errors.push(`Import failed for ${externalLead.Id}: ${errorMsg}`);
            logs.push({
              operation: 'import',
              entityType: 'lead',
              entityId: externalLead.Id,
              status: 'failed',
              errorMessage: errorMsg
            });
          }
        }
      }

      // Export leads to CRM
      if (integration.config.syncDirection !== 'import_only') {
        const localLeads = await this.getLeadsForExport(tenantId, integration.lastSyncAt || undefined);
        
        for (const lead of localLeads) {
          try {
            const externalId = await crmProvider.exportLead(lead);
            exported++;
            
            // Store external ID mapping
            await this.storeCRMMapping(tenantId, lead.id, provider, externalId);
            
            logs.push({
              operation: 'export',
              entityType: 'lead',
              entityId: lead.id,
              externalId,
              status: 'success'
            });
          } catch (error) {
            const errorMsg = error instanceof Error ? error.message : 'Unknown error';
            errors.push(`Export failed for ${lead.id}: ${errorMsg}`);
            logs.push({
              operation: 'export',
              entityType: 'lead',
              entityId: lead.id,
              status: 'failed',
              errorMessage: errorMsg
            });
          }
        }
      }

      // Update integration status
      await prisma.crmIntegration.update({
        where: { id: integration.id },
        data: {
          lastSyncAt: new Date(),
          syncStatus: 'active',
          errorMessage: null
        }
      });

      // Store sync logs
      await this.storeSyncLogs(tenantId, logs);

      return {
        success: true,
        imported,
        exported,
        updated,
        errors,
        logs
      };
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : 'Unknown error';
      
      // Update integration with error
      await prisma.crmIntegration.update({
        where: { id: integration.id },
        data: {
          syncStatus: 'error',
          errorMessage: errorMsg
        }
      });

      return {
        success: false,
        imported,
        exported,
        updated,
        errors: [errorMsg, ...errors],
        logs
      };
    }
  }

  /**
   * Create CRM provider instance
   */
  private createProvider(config: CRMConfig, tenantId: string): BaseCRMProvider {
    switch (config.provider) {
      case 'salesforce':
        return new SalesforceCRMProvider(config, tenantId);
      // Add other providers here
      default:
        throw new Error(`Unsupported CRM provider: ${config.provider}`);
    }
  }

  /**
   * Import lead from external CRM
   */
  private async importLeadFromCRM(
    tenantId: string,
    externalLead: any,
    provider: string
  ): Promise<void> {
    // Check if lead already exists
    const existingMapping = await prisma.crmMapping.findUnique({
      where: {
        tenantId_provider_externalId: {
          tenantId,
          provider,
          externalId: externalLead.Id
        }
      }
    });

    if (existingMapping) {
      // Update existing lead
      await leadManagementService.updateLead(
        existingMapping.entityId,
        tenantId,
        this.mapExternalLeadToLocal(externalLead),
        null
      );
    } else {
      // Create new lead
      const lead = await leadManagementService.createLead(
        tenantId,
        this.mapExternalLeadToLocal(externalLead),
        null
      );

      // Store mapping
      await this.storeCRMMapping(tenantId, lead.id, provider, externalLead.Id);
    }
  }

  /**
   * Map external lead data to local format
   */
  private mapExternalLeadToLocal(externalLead: any): any {
    return {
      title: externalLead.Company || 'Imported Lead',
      description: externalLead.Description || '',
      source: 'crm_import',
      priority: 'medium',
      // Add more field mappings as needed
    };
  }

  /**
   * Get leads for export
   */
  private async getLeadsForExport(
    tenantId: string,
    lastSync?: Date
  ): Promise<LeadWithRelations[]> {
    return await prisma.lead.findMany({
      where: {
        tenantId,
        deletedAt: null,
        ...(lastSync && { updatedAt: { gt: lastSync } })
      },
      include: {
        contact: true,
        company: true,
        owner: true
      },
      take: 100 // Limit batch size
    });
  }

  /**
   * Store CRM mapping
   */
  private async storeCRMMapping(
    tenantId: string,
    entityId: string,
    provider: string,
    externalId: string
  ): Promise<void> {
    await prisma.crmMapping.upsert({
      where: {
        tenantId_provider_externalId: {
          tenantId,
          provider,
          externalId
        }
      },
      create: {
        tenantId,
        entityType: 'lead',
        entityId,
        provider,
        externalId
      },
      update: {
        entityId
      }
    });
  }

  /**
   * Store sync logs
   */
  private async storeSyncLogs(tenantId: string, logs: SyncLog[]): Promise<void> {
    await prisma.crmSyncLog.createMany({
      data: logs.map(log => ({
        tenantId,
        ...log,
        syncedAt: new Date()
      }))
    });
  }

  /**
   * Get CRM integrations for tenant
   */
  async getCRMIntegrations(tenantId: string): Promise<CRMIntegration[]> {
    const integrations = await prisma.crmIntegration.findMany({
      where: { tenantId }
    });

    return integrations as CRMIntegration[];
  }
}

export const crmIntegrationService = new CRMIntegrationService();
