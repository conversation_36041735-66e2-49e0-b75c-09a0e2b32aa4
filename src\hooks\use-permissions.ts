'use client';

import { useState, useEffect, useCallback, useMemo } from 'react';
import { useAuth } from './use-auth';
import { useTenant } from './use-tenant';
import { PermissionAction, PermissionResource, Permission } from '@/types';
import { useEnhancedPermissions } from '@/components/providers/enhanced-permission-provider';

export interface UsePermissionsReturn {
  permissions: Permission[];
  isLoading: boolean;
  error: string | null;
  hasPermission: (resource: PermissionResource, action: PermissionAction) => boolean;
  hasAnyPermission: (permissions: Array<{ resource: PermissionResource; action: PermissionAction }>) => boolean;
  hasAllPermissions: (permissions: Array<{ resource: PermissionResource; action: PermissionAction }>) => boolean;
  canAccessResource: (resource: PermissionResource, action: PermissionAction, resourceOwnerId?: string) => boolean;
  isTenantAdmin: boolean;
  refreshPermissions: () => Promise<void>;
  // Enhanced caching features (optional for backward compatibility)
  isFromCache?: boolean;
  cacheHitRate?: number;
  clearCache?: () => void;
}

export function usePermissions(): UsePermissionsReturn {
  // Use the enhanced permission provider for caching and real-time updates
  const enhancedPermissions = useEnhancedPermissions();

  // Return the enhanced permissions with backward compatibility
  return {
    permissions: enhancedPermissions.permissions,
    isLoading: enhancedPermissions.isLoading,
    error: enhancedPermissions.error,
    hasPermission: enhancedPermissions.hasPermission,
    hasAnyPermission: enhancedPermissions.hasAnyPermission,
    hasAllPermissions: enhancedPermissions.hasAllPermissions,
    canAccessResource: enhancedPermissions.canAccessResource,
    isTenantAdmin: enhancedPermissions.isTenantAdmin,
    refreshPermissions: enhancedPermissions.refreshPermissions,
    // Enhanced features
    isFromCache: enhancedPermissions.isFromCache,
    cacheHitRate: enhancedPermissions.cacheHitRate,
    clearCache: enhancedPermissions.clearCache,
  };
}
// Hook for checking specific permission with loading state
export function usePermissionCheck(
  resource: PermissionResource,
  action: PermissionAction
): { hasPermission: boolean; isLoading: boolean } {
  const { hasPermission, isLoading } = usePermissions();
  
  return {
    hasPermission: hasPermission(resource, action),
    isLoading
  };
}

// Hook for checking multiple permissions
export function useMultiplePermissions(
  requiredPermissions: Array<{ resource: PermissionResource; action: PermissionAction }>,
  requireAll: boolean = false
): { hasPermissions: boolean; isLoading: boolean } {
  const { hasAnyPermission, hasAllPermissions, isLoading } = usePermissions();
  
  return {
    hasPermissions: requireAll 
      ? hasAllPermissions(requiredPermissions)
      : hasAnyPermission(requiredPermissions),
    isLoading
  };
}

// Hook for resource access checking
export function useResourceAccess(
  resource: PermissionResource,
  action: PermissionAction,
  resourceOwnerId?: string
): { canAccess: boolean; isLoading: boolean } {
  const { canAccessResource, isLoading } = usePermissions();
  
  return {
    canAccess: canAccessResource(resource, action, resourceOwnerId),
    isLoading
  };
}

// Hook for tenant admin checking
export function useTenantAdmin(): { isTenantAdmin: boolean; isLoading: boolean } {
  const { isTenantAdmin, isLoading } = usePermissions();

  return {
    isTenantAdmin,
    isLoading
  };
}

// Hook for bulk operations with permission checking
export function useBulkOperations() {
  const { hasPermission } = usePermissions();

  const canPerformBulkOperation = useCallback((
    resource: PermissionResource,
    action: PermissionAction,
    items: any[],
    options?: { requireOwnership?: boolean; userIdField?: string }
  ) => {
    // Check basic permission
    if (!hasPermission(resource, action)) {
      return { allowed: false, reason: 'Insufficient permissions for bulk operation' };
    }

    // Check ownership if required
    if (options?.requireOwnership && options?.userIdField) {
      const { user } = useAuth();
      const userIdField = options.userIdField;
      const unauthorizedItems = items.filter(item =>
        item[userIdField] !== user?.id &&
        !hasPermission(resource, PermissionAction.MANAGE)
      );

      if (unauthorizedItems.length > 0) {
        return {
          allowed: false,
          reason: `Cannot perform bulk operation on ${unauthorizedItems.length} items you don't own`
        };
      }
    }

    return { allowed: true };
  }, [hasPermission]);

  return {
    canPerformBulkOperation
  };
}

// Hook for permission-aware data fetching
export function usePermissionAwareData<T>(
  resource: PermissionResource,
  action: PermissionAction,
  fetchFn: () => Promise<T>,
  dependencies: any[] = []
) {
  const [data, setData] = useState<T | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { hasPermission } = usePermissions();

  const fetchData = useCallback(async () => {
    if (!hasPermission(resource, action)) {
      setError('Insufficient permissions');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const result = await fetchFn();
      setData(result);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch data');
    } finally {
      setIsLoading(false);
    }
  }, [hasPermission, resource, action, fetchFn]);

  useEffect(() => {
    fetchData();
  }, [fetchData, ...dependencies]);

  return {
    data,
    isLoading,
    error,
    refetch: fetchData,
    hasPermission: hasPermission(resource, action)
  };
}

// Hook for conditional form field rendering
export function usePermissionAwareForm(
  resource: PermissionResource,
  basePermissions: { read: boolean; write: boolean; delete?: boolean }
) {
  const { hasPermission } = usePermissions();

  const fieldPermissions = useMemo(() => ({
    canRead: hasPermission(resource, PermissionAction.READ) || basePermissions.read,
    canWrite: hasPermission(resource, PermissionAction.UPDATE) || basePermissions.write,
    canCreate: hasPermission(resource, PermissionAction.CREATE),
    canDelete: hasPermission(resource, PermissionAction.DELETE) || basePermissions.delete,
    canManage: hasPermission(resource, PermissionAction.MANAGE)
  }), [hasPermission, resource, basePermissions]);

  const getFieldProps = useCallback((fieldName: string, options?: {
    requireWrite?: boolean;
    requireManage?: boolean;
    readOnly?: boolean;
  }) => {
    const requireWrite = options?.requireWrite ?? true;
    const requireManage = options?.requireManage ?? false;
    const readOnly = options?.readOnly ?? false;

    let disabled = readOnly;
    let visible = fieldPermissions.canRead;

    if (requireManage && !fieldPermissions.canManage) {
      disabled = true;
      visible = fieldPermissions.canRead;
    } else if (requireWrite && !fieldPermissions.canWrite) {
      disabled = true;
    }

    return {
      disabled,
      readOnly: disabled,
      hidden: !visible,
      'data-permission-field': fieldName,
      'data-permission-level': requireManage ? 'manage' : requireWrite ? 'write' : 'read'
    };
  }, [fieldPermissions]);

  return {
    ...fieldPermissions,
    getFieldProps
  };
}
