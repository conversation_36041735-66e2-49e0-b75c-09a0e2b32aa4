# Email & Social Media Capture Implementation Guide

## Overview
The CRM system provides multiple ways to capture emails and social media posts, automatically converting them into leads. Here are the available methods:

## 🚀 Quick Start

### Email Capture
1. **Manual Form**: Go to `/leads/email-capture` → Click "Add Email" → Fill form
2. **Sample Data**: Run `npx tsx scripts/add-sample-emails.ts`
3. **API**: POST to `/api/leads/email-capture`

### Social Media Capture
1. **Manual Form**: Go to `/leads/social-capture` → Click "Add Social Post" → Fill form
2. **Sample Data**: Run `npx tsx scripts/add-sample-social-captures.ts`
3. **API**: POST to `/api/leads/social-capture`

---

## 1. Manual Email Capture via API

### Endpoint
```
POST /api/leads/email-capture
```

### Headers
```
Content-Type: application/json
x-tenant-id: YOUR_TENANT_ID
```

**Note**: This API is open for external integrations and does not require authentication.

### Request Body
```json
{
  "fromEmail": "<EMAIL>",
  "fromName": "<PERSON> Doe",
  "subject": "Interested in your CRM solution",
  "content": "Hi, I saw your CRM platform and I'm very interested in learning more about pricing and features. Could we schedule a demo?",
  "receivedAt": "2024-01-15T10:30:00Z",
  "emailProvider": "gmail",
  "messageId": "optional-message-id"
}
```

### Example JavaScript Implementation
```javascript
async function addEmailCapture(emailData) {
  const response = await fetch('/api/leads/email-capture', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'x-tenant-id': 'your-tenant-id'
    },
    body: JSON.stringify(emailData)
  });
  
  const result = await response.json();
  return result;
}

// Usage
const emailData = {
  fromEmail: "<EMAIL>",
  fromName: "Jane Smith",
  subject: "Partnership Inquiry",
  content: "We're interested in partnering with your company...",
  receivedAt: new Date().toISOString()
};

addEmailCapture(emailData);
```

## 2. Email Webhook Integration

### Webhook Endpoint
```
POST /api/webhooks/email
```

**Note**: This webhook endpoint is completely open and does not require authentication. It's designed for external email providers to send data directly.

### Required Headers
```
x-tenant-id: YOUR_TENANT_ID
x-email-provider: sendgrid|mailgun|postmark|custom
```

### Supported Email Providers
- SendGrid
- Mailgun
- Postmark
- Custom SMTP

### Setup Instructions

#### For SendGrid:
1. Configure webhook URL: `https://your-domain.com/api/webhooks/email`
2. Set header: `x-email-provider: sendgrid`
3. Enable inbound email parsing

#### For Mailgun:
1. Configure webhook URL: `https://your-domain.com/api/webhooks/email`
2. Set header: `x-email-provider: mailgun`
3. Set up email forwarding rules

### Webhook Security
Add signature verification in the webhook handler:
```javascript
// In /api/webhooks/email/route.ts
const signature = headersList.get('x-webhook-signature');
// Verify signature based on your email provider's method
```

## 3. Email Forwarding Setup

### Option A: Direct Email Forwarding
1. Set up email forwarding rules in your email client
2. Forward emails to a dedicated address that triggers the webhook
3. Use email filters to forward only relevant emails

### Option B: IMAP Integration
Create a service to monitor an IMAP mailbox:

```javascript
// Example IMAP monitoring service
import { ImapFlow } from 'imapflow';

async function monitorEmailBox() {
  const client = new ImapFlow({
    host: 'imap.gmail.com',
    port: 993,
    secure: true,
    auth: {
      user: '<EMAIL>',
      pass: 'your-app-password'
    }
  });

  await client.connect();
  
  client.on('exists', async (data) => {
    // New email received
    const message = await client.fetchOne(data.seq, {
      envelope: true,
      bodyText: true
    });
    
    // Process email through capture service
    await processEmailCapture(message);
  });
}
```

## 4. Frontend Email Capture Form

Create a form component for manual email entry:

```tsx
// components/email-capture-form.tsx
import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';

export function EmailCaptureForm() {
  const [formData, setFormData] = useState({
    fromEmail: '',
    fromName: '',
    subject: '',
    content: ''
  });

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    const response = await fetch('/api/leads/email-capture', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-tenant-id': tenantId
      },
      body: JSON.stringify({
        ...formData,
        receivedAt: new Date().toISOString()
      })
    });
    
    if (response.ok) {
      // Handle success
      setFormData({ fromEmail: '', fromName: '', subject: '', content: '' });
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <Input
        placeholder="From Email"
        value={formData.fromEmail}
        onChange={(e) => setFormData({...formData, fromEmail: e.target.value})}
        required
      />
      <Input
        placeholder="From Name"
        value={formData.fromName}
        onChange={(e) => setFormData({...formData, fromName: e.target.value})}
      />
      <Input
        placeholder="Subject"
        value={formData.subject}
        onChange={(e) => setFormData({...formData, subject: e.target.value})}
        required
      />
      <Textarea
        placeholder="Email Content"
        value={formData.content}
        onChange={(e) => setFormData({...formData, content: e.target.value})}
        required
      />
      <Button type="submit">Capture Email</Button>
    </form>
  );
}
```

## 5. Automated Processing Features

The system automatically:

### AI-Powered Analysis
- **Sentiment Analysis**: Determines if the email is positive, negative, or neutral
- **Urgency Detection**: Identifies high, medium, or low urgency
- **Intent Recognition**: Categorizes the email intent (inquiry, complaint, etc.)

### Lead Creation
- **Contact Matching**: Checks for existing contacts
- **Lead Generation**: Creates new leads from qualified emails
- **Data Extraction**: Extracts company info, phone numbers, etc.

### Notifications
- **Real-time Alerts**: Notifies team members of new high-priority leads
- **Email Notifications**: Sends summaries of captured emails

## 6. Configuration Options

### Email Processing Rules
Configure in your tenant settings:

```json
{
  "emailCapture": {
    "autoProcessing": true,
    "minimumSentimentScore": 0.3,
    "createLeadThreshold": "medium",
    "notificationSettings": {
      "highPriority": true,
      "realTime": true
    }
  }
}
```

### Filtering Rules
Set up email filters to capture only relevant emails:

```json
{
  "filters": {
    "subjectKeywords": ["inquiry", "demo", "pricing", "partnership"],
    "excludeKeywords": ["unsubscribe", "spam"],
    "domainWhitelist": ["company.com", "business.org"],
    "minimumContentLength": 50
  }
}
```

## 7. Testing Email Capture

### Test with Sample Data
```javascript
const testEmail = {
  fromEmail: "<EMAIL>",
  fromName: "Test User",
  subject: "Test Email Capture",
  content: "This is a test email to verify the capture functionality.",
  receivedAt: new Date().toISOString()
};

// Send test email
fetch('/api/leads/email-capture', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'x-tenant-id': 'your-tenant-id'
  },
  body: JSON.stringify(testEmail)
});
```

### Verify in Dashboard
1. Navigate to `/leads/email-capture`
2. Check for the captured email
3. Verify sentiment analysis and lead creation
4. Test filtering options

## 8. Monitoring and Analytics

### View Captured Emails
- Access the Email Capture dashboard at `/leads/email-capture`
- Filter by processed/unprocessed status
- View sentiment analysis results
- Track lead conversion rates

### API Endpoints for Analytics
```javascript
// Get email capture statistics
GET /api/leads/email-capture?processed=true&limit=100

// Get specific email capture
GET /api/leads/email-capture/{id}
```

## Best Practices

1. **Security**: Always verify webhook signatures
2. **Rate Limiting**: Implement rate limiting for API endpoints
3. **Data Privacy**: Ensure GDPR/privacy compliance
4. **Error Handling**: Implement robust error handling and logging
5. **Testing**: Test with various email formats and providers
6. **Monitoring**: Set up alerts for failed email processing

## Troubleshooting

### Common Issues
1. **Emails not being captured**: Check webhook configuration and email forwarding rules
2. **Low sentiment scores**: Review AI model configuration
3. **Duplicate leads**: Verify contact matching logic
4. **Processing errors**: Check logs in the email capture dashboard

### Debug Mode
Enable debug logging by setting environment variable:
```
EMAIL_CAPTURE_DEBUG=true
```

---

# Social Media Capture

## 1. Manual Social Media Capture via Form

### Access
Navigate to `/leads/social-capture` and click "Add Social Post"

### Supported Platforms
- Twitter/X
- LinkedIn
- Facebook
- Instagram
- YouTube
- TikTok

### Form Fields
```javascript
{
  platform: "twitter",           // Required: Platform selection
  postId: "tweet_123456",        // Required: Unique post identifier
  authorName: "John Doe",        // Required: Author's display name
  authorHandle: "@johndoe",      // Optional: Social media handle
  content: "Post content...",    // Required: Full post text
  url: "https://...",           // Optional: Direct link to post
  engagement: {                  // Optional: Engagement metrics
    likes: 15,
    shares: 3,
    comments: 8,
    views: 245
  }
}
```

## 2. Social Media API Capture

### Endpoint
```
POST /api/leads/social-capture
```

### Headers
```
Content-Type: application/json
x-tenant-id: YOUR_TENANT_ID
```

**Note**: This API is open for external integrations and does not require authentication.

### Request Body
```json
{
  "platform": "twitter",
  "postId": "tweet_123456",
  "authorName": "John Doe",
  "authorHandle": "johndoe",
  "content": "Looking for a new CRM system for our growing team. Any recommendations? #CRM #SaaS",
  "url": "https://twitter.com/johndoe/status/123456",
  "createdAt": "2024-01-15T10:30:00Z",
  "engagement": {
    "likes": 15,
    "shares": 3,
    "comments": 8,
    "views": 245
  }
}
```

### Example Implementation
```javascript
async function addSocialCapture(postData) {
  const response = await fetch('/api/leads/social-capture', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'x-tenant-id': 'your-tenant-id'
    },
    body: JSON.stringify(postData)
  });

  const result = await response.json();
  return result;
}

// Usage
const postData = {
  platform: "linkedin",
  postId: "post_789123",
  authorName: "Sarah Wilson",
  authorHandle: "sarah-wilson-ceo",
  content: "Just implemented a new CRM system at our company...",
  url: "https://linkedin.com/posts/sarah-wilson-ceo_789123",
  createdAt: new Date().toISOString(),
  engagement: {
    likes: 42,
    shares: 12,
    comments: 18,
    views: 1250
  }
};

addSocialCapture(postData);
```

## 3. Social Media Webhook Integration

### Webhook Endpoint
```
POST /api/webhooks/social
```

**Note**: This webhook endpoint is completely open and does not require authentication. It's designed for external social media platforms to send data directly.

### Required Headers
```
x-tenant-id: YOUR_TENANT_ID
x-social-provider: twitter|linkedin|facebook|instagram|youtube|tiktok
```

### Supported Platforms
- Twitter API v2
- LinkedIn API
- Facebook Graph API
- Instagram Basic Display API
- YouTube API
- TikTok API

### Setup Instructions

#### For Twitter API:
1. Configure webhook URL: `https://your-domain.com/api/webhooks/social`
2. Set header: `x-social-provider: twitter`
3. Enable real-time tweet monitoring

#### For LinkedIn:
1. Configure webhook URL: `https://your-domain.com/api/webhooks/social`
2. Set header: `x-social-provider: linkedin`
3. Set up post monitoring for relevant hashtags

## 4. Social Media Monitoring

### Automated Monitoring Features
- **Keyword Tracking**: Monitor mentions of your brand, competitors, or industry terms
- **Hashtag Monitoring**: Track relevant hashtags like #CRM, #SaaS, #BusinessTools
- **Competitor Analysis**: Monitor competitor mentions and customer complaints
- **Sentiment Analysis**: Automatically analyze post sentiment and lead potential

### Configuration
```json
{
  "socialMonitoring": {
    "keywords": ["CRM", "customer management", "sales tools"],
    "hashtags": ["#CRM", "#SaaS", "#BusinessTools"],
    "competitors": ["competitor1", "competitor2"],
    "platforms": ["twitter", "linkedin", "facebook"],
    "minimumEngagement": 5,
    "leadPotentialThreshold": 0.7
  }
}
```

## 5. AI-Powered Social Analysis

### Automatic Analysis Features
- **Sentiment Analysis**: Positive, negative, or neutral sentiment detection
- **Lead Potential Scoring**: High, medium, or low lead potential
- **Intent Recognition**: Product search, complaint, testimonial, etc.
- **Business Relevance**: Relevance score from 0-1
- **Keyword Extraction**: Automatic extraction of relevant keywords

### Analysis Results
```json
{
  "analysis": {
    "sentiment": "positive",
    "leadPotential": "high",
    "intent": "product_search",
    "businessRelevance": 0.89,
    "keywords": ["CRM", "SaaS", "business tools"],
    "urgency": "medium",
    "confidence": 0.92
  }
}
```

## 6. Testing Social Media Capture

### Test with Sample Data
```bash
# Add 6 sample social media posts with different scenarios
npx tsx scripts/add-sample-social-captures.ts
```

### Manual Testing
1. Navigate to `/leads/social-capture/add`
2. Fill out the form with test data
3. Submit and verify processing
4. Check `/leads/social-capture` for results

### API Testing
```javascript
const testPost = {
  platform: "twitter",
  postId: "test_" + Date.now(),
  authorName: "Test User",
  content: "This is a test post about CRM systems #CRM #test",
  createdAt: new Date().toISOString()
};

fetch('/api/leads/social-capture', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'x-tenant-id': 'your-tenant-id'
  },
  body: JSON.stringify(testPost)
});
```

## 7. Social Media Analytics

### Dashboard Features
- View captured posts by platform
- Filter by processing status
- Sort by engagement metrics
- Track lead conversion rates
- Monitor sentiment trends

### API Endpoints
```javascript
// Get social captures with filters
GET /api/leads/social-capture?platform=twitter&processed=true&limit=50

// Get specific social capture
GET /api/leads/social-capture/{id}

// Get analytics
GET /api/analytics/social-capture
```

## 8. Integration Best Practices

### Social Media Monitoring
1. **Rate Limiting**: Respect platform API limits
2. **Authentication**: Use proper OAuth tokens
3. **Data Privacy**: Comply with platform terms and GDPR
4. **Real-time Processing**: Set up webhooks for immediate capture
5. **Duplicate Detection**: Avoid capturing the same post multiple times

### Content Analysis
1. **Relevance Filtering**: Only capture business-relevant posts
2. **Spam Detection**: Filter out spam and irrelevant content
3. **Language Detection**: Handle multiple languages appropriately
4. **Context Understanding**: Consider post context and thread conversations

## 9. Troubleshooting

### Common Issues
1. **Posts not being captured**: Check API credentials and webhook configuration
2. **Low relevance scores**: Adjust keyword filters and monitoring criteria
3. **Duplicate captures**: Verify post ID uniqueness checks
4. **Processing errors**: Check logs in social capture dashboard

### Debug Social Capture
```bash
# Enable debug mode
SOCIAL_CAPTURE_DEBUG=true

# Check recent captures
curl -H "x-tenant-id: your-tenant-id" \
     http://localhost:3000/api/leads/social-capture?limit=10
```
