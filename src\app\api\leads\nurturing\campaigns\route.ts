import { NextResponse } from 'next/server';
import { withPermission } from '@/lib/permission-middleware';
import { leadNurturingService, createNurturingCampaignSchema } from '@/services/lead-nurturing';
import { PermissionAction, PermissionResource } from '@/types';
import { AuthenticatedRequest } from '@/lib/permission-middleware';

/**
 * GET /api/leads/nurturing/campaigns
 * Get all nurturing campaigns for a tenant
 */
export const GET = withPermission({
  resource: PermissionResource.LEADS,
  action: PermissionAction.READ,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest) => {
  try {
    const campaigns = await leadNurturingService.getCampaigns(req.tenantId);

    return NextResponse.json({
      success: true,
      data: campaigns
    });
  } catch (error) {
    return NextResponse.json(
      { error: 'Failed to fetch campaigns' },
      { status: 500 }
    );
  }
});

/**
 * POST /api/leads/nurturing/campaigns
 * Create a new nurturing campaign
 */
export const POST = withPermission({
  resource: PermissionResource.LEADS,
  action: PermissionAction.CREATE,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest) => {
  try {
    const body = await req.json();

    // Validate request body
    const validationResult = createNurturingCampaignSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        {
          error: 'Validation failed',
          details: validationResult.error.errors
        },
        { status: 400 }
      );
    }

    const campaign = await leadNurturingService.createCampaign(
      req.tenantId,
      validationResult.data,
      req.userId
    );

    return NextResponse.json({
      success: true,
      data: campaign
    }, { status: 201 });
  } catch (error) {
    return NextResponse.json(
      { error: 'Failed to create campaign' },
      { status: 500 }
    );
  }
});
