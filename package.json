{"name": "crm-system", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "dev:webpack": "next dev", "build": "next build", "build:webpack": "TURBOPACK=false next build", "start": "next start", "lint": "next lint", "db:migrate": "prisma migrate dev", "db:reset": "prisma migrate reset", "db:generate": "prisma generate", "db:push": "prisma db push", "db:studio": "prisma studio", "db:seed": "node scripts/master-seed.js", "db:seed:force": "node scripts/master-seed.js --force --sample-data", "db:seed:permissions": "node scripts/seed-permissions.js", "db:seed:rbac": "node scripts/seed-rbac.js", "db:seed:lead-sources": "node scripts/seed-lead-sources.js", "db:seed:sales-stages": "node scripts/seed-sales-stages.js", "swagger": "node scripts/generate-swagger.js"}, "dependencies": {"@anthropic-ai/sdk": "^0.53.0", "@auth/prisma-adapter": "^2.9.1", "@formatjs/intl-localematcher": "^0.6.1", "@google/genai": "^1.4.0", "@headlessui/react": "^2.2.4", "@hello-pangea/dnd": "^18.0.1", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^5.1.0", "@mermaid-js/mermaid-cli": "^11.4.0", "@mohtasham/md-to-docx": "^1.4.9", "@monaco-editor/react": "^4.7.0", "@next-auth/prisma-adapter": "^1.0.7", "@prisma/client": "^6.9.0", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-tooltip": "^1.2.7", "@stripe/stripe-js": "^7.3.1", "@tailwindcss/typography": "^0.5.16", "@trpc/client": "^11.3.1", "@trpc/next": "^11.3.1", "@trpc/react-query": "^11.3.1", "@trpc/server": "^11.3.1", "@types/crypto-js": "^4.2.2", "@types/nodemailer": "^6.4.17", "@types/puppeteer": "^5.4.7", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.8", "@types/uuid": "^10.0.0", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "critters": "^0.0.23", "crypto-js": "^4.2.0", "date-fns": "^4.1.0", "docx": "^9.5.0", "framer-motion": "^12.16.0", "ioredis": "^5.6.1", "jose": "^6.0.11", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.513.0", "mermaid": "^11.6.0", "negotiator": "^1.0.0", "next": "15.3.3", "next-auth": "^4.24.11", "next-intl": "^4.1.0", "nodemailer": "^6.10.1", "openai": "^5.1.1", "prisma": "^6.9.0", "puppeteer": "^24.10.0", "react": "^19.0.0", "react-day-picker": "^9.7.0", "react-dom": "^19.0.0", "react-dropzone": "^14.3.8", "react-easy-crop": "^5.4.2", "react-hook-form": "^7.57.0", "react-phone-number-input": "^3.4.12", "reactjs-tiptap-editor": "^0.3.4", "recharts": "^2.15.3", "redis": "^5.5.6", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "sonner": "^2.0.5", "stripe": "^18.2.1", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "tailwind-merge": "^3.3.0", "tailwindcss-rtl": "^0.9.0", "uuid": "^11.1.0", "zod": "^3.25.56", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/bcryptjs": "^2.4.6", "@types/ioredis": "^4.28.10", "@types/jsonwebtoken": "^9.0.9", "@types/negotiator": "^0.6.3", "@types/node": "^20.19.0", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.3", "tailwindcss": "^4", "tw-animate-css": "^1.3.4", "typescript": "^5"}}