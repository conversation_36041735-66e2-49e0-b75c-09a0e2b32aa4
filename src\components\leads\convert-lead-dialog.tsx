'use client';

import { useState, useEffect, useRef } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { toast } from 'sonner';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { SearchableSelect, SearchableSelectOption } from '@/components/ui/searchable-select';
import { Badge } from '@/components/ui/badge';
import { Loader2, CheckCircle, XCircle } from 'lucide-react';

const convertLeadSchema = z.object({
  title: z.string().optional(),
  description: z.string().optional(),
  value: z.number().min(0).optional(),
  stage: z.string().optional(),
  priority: z.enum(['low', 'medium', 'high', 'urgent']).optional(),
  ownerId: z.string().optional(),
  conversionNotes: z.string().optional(),
});

type ConvertLeadFormData = z.infer<typeof convertLeadSchema>;

interface LeadConversionEligibility {
  leadId: string;
  eligible: boolean;
  reason?: string;
  qualificationScore?: number;
  missingRequirements?: string[];
}

interface ConvertLeadDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  leadId: string;
  leadTitle: string;
  leadOwnerId?: string;
  leadValue?: number;
  onSuccess?: () => void;
}

export function ConvertLeadDialog({
  open,
  onOpenChange,
  leadId,
  leadTitle,
  leadOwnerId,
  leadValue,
  onSuccess,
}: ConvertLeadDialogProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [isCheckingEligibility, setIsCheckingEligibility] = useState(false);
  const [eligibility, setEligibility] = useState<LeadConversionEligibility | null>(null);
  const [salesStages, setSalesStages] = useState<Array<{ id: string; name: string }>>([]);
  const [users, setUsers] = useState<Array<{ id: string; firstName: string; lastName: string }>>([]);
  const formInitialized = useRef(false);

  const form = useForm<ConvertLeadFormData>({
    resolver: zodResolver(convertLeadSchema),
    defaultValues: {
      title: leadTitle || '',
      description: '',
      value: leadValue || 0,
      priority: 'medium',
      stage: 'qualification',
      ownerId: leadOwnerId || undefined,
      conversionNotes: '',
    },
  });

  // Check eligibility when dialog opens
  useEffect(() => {
    if (open && leadId) {
      checkEligibility();
      loadSalesStages();
      loadUsers();

      // Reset form with proper defaults only once per dialog open
      if (!formInitialized.current) {
        form.reset({
          title: leadTitle || '',
          description: '',
          value: leadValue || 0,
          priority: 'medium',
          stage: 'qualification',
          ownerId: leadOwnerId || undefined,
          conversionNotes: '',
        });
        formInitialized.current = true;
      }
    } else if (!open) {
      // Reset the flag when dialog closes
      formInitialized.current = false;
    }
  }, [open, leadId, leadOwnerId]);

  const checkEligibility = async () => {
    setIsCheckingEligibility(true);
    try {
      const response = await fetch('/api/leads/conversion/eligibility', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ leadIds: [leadId] }),
      });

      if (!response.ok) {
        throw new Error('Failed to check eligibility');
      }

      const data = await response.json();
      setEligibility(data.data[0]);
    } catch (error) {
      console.error('Error checking eligibility:', error);
      toast.error('Failed to check conversion eligibility');
    } finally {
      setIsCheckingEligibility(false);
    }
  };

  const loadSalesStages = async () => {
    try {
      const response = await fetch('/api/opportunities/stages');
      if (response.ok) {
        const data = await response.json();
        // Opportunity stages API returns array directly
        const stages = Array.isArray(data) ? data : [];
        setSalesStages(stages);

        // Set default stage if stages are available and no stage is currently selected
        if (stages.length > 0 && !form.getValues('stage')) {
          // Find the first stage or a stage named 'qualification', 'prospecting', or 'new'
          const defaultStage = stages.find(s =>
            s.name.toLowerCase() === 'qualification' ||
            s.name.toLowerCase() === 'prospecting' ||
            s.name.toLowerCase() === 'new'
          ) || stages[0];

          if (defaultStage) {
            form.setValue('stage', defaultStage.name);
          }
        }
      } else {
        console.error('Failed to load opportunity stages:', response.status, response.statusText);
        setSalesStages([]);
      }
    } catch (error) {
      console.error('Error loading opportunity stages:', error);
      setSalesStages([]);
    }
  };

  const loadUsers = async () => {
    try {
      const response = await fetch('/api/users');
      if (response.ok) {
        const result = await response.json();
        // Handle the API response format
        if (result.success && result.data) {
          setUsers(result.data);
        } else {
          setUsers([]);
        }
      }
    } catch (error) {
      console.error('Error loading users:', error);
      setUsers([]);
    }
  };

  const onSubmit = async (data: ConvertLeadFormData) => {
    if (!eligibility?.eligible) {
      toast.error('Lead is not eligible for conversion');
      return;
    }

    setIsLoading(true);
    try {
      const response = await fetch('/api/opportunities/convert-from-lead', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          leadId,
          opportunityData: {
            ...data,
            value: data.value && data.value > 0 ? data.value : undefined,
          },
          conversionNotes: data.conversionNotes,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to convert lead');
      }

      toast.success('Lead successfully converted to opportunity');
      onOpenChange(false);
      onSuccess?.();
      form.reset();
    } catch (error) {
      console.error('Error converting lead:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to convert lead');
    } finally {
      setIsLoading(false);
    }
  };

  const getEligibilityIcon = () => {
    if (isCheckingEligibility) return <Loader2 className="h-4 w-4 animate-spin" />;
    if (eligibility?.eligible) return <CheckCircle className="h-4 w-4 text-green-500" />;
    return <XCircle className="h-4 w-4 text-red-500" />;
  };

  const getEligibilityColor = () => {
    if (eligibility?.eligible) return 'bg-green-50 border-green-200';
    return 'bg-red-50 border-red-200';
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Convert Lead to Opportunity</DialogTitle>
          <DialogDescription>
            Convert "{leadTitle}" from a lead to an opportunity in your sales pipeline.
          </DialogDescription>
        </DialogHeader>

        {/* Eligibility Check */}
        <div className={`p-4 rounded-lg border ${getEligibilityColor()}`}>
          <div className="flex items-center gap-2 mb-2">
            {getEligibilityIcon()}
            <span className="font-medium">
              {isCheckingEligibility ? 'Checking eligibility...' : 'Conversion Eligibility'}
            </span>
            {eligibility && (
              <Badge variant={eligibility.eligible ? 'default' : 'destructive'}>
                Score: {eligibility.qualificationScore || 0}/100
              </Badge>
            )}
          </div>
          
          {eligibility && (
            <p className="text-sm text-gray-600 mb-2">{eligibility.reason}</p>
          )}
          
          {eligibility?.missingRequirements && eligibility.missingRequirements.length > 0 && (
            <div className="mt-2">
              <p className="text-sm font-medium text-red-700 mb-1">Missing Requirements:</p>
              <ul className="text-sm text-red-600 list-disc list-inside">
                {eligibility.missingRequirements.map((req, index) => (
                  <li key={index}>{req}</li>
                ))}
              </ul>
            </div>
          )}
        </div>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="title"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Opportunity Title</FormLabel>
                    <FormControl>
                      <Input placeholder={`Default: ${leadTitle}`} {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="value"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Estimated Value</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        placeholder={leadValue ? `Default: $${leadValue.toLocaleString()}` : "0.00"}
                        {...field}
                        value={field.value || ''}
                        onChange={(e) => {
                          const value = e.target.value;
                          field.onChange(value === '' ? 0 : parseFloat(value) || 0);
                        }}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="stage"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Initial Stage</FormLabel>
                    <FormControl>
                      <SearchableSelect
                        options={salesStages.map((stage): SearchableSelectOption => ({
                          value: stage.name,
                          label: stage.name,
                          description: `Stage: ${stage.name}`
                        }))}
                        value={field.value}
                        onValueChange={field.onChange}
                        placeholder={salesStages.length === 0 ? "Loading stages..." : "Select stage"}
                        searchPlaceholder="Search stages..."
                        emptyMessage="No stages found"
                        disabled={salesStages.length === 0}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="priority"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Priority</FormLabel>
                    <FormControl>
                      <SearchableSelect
                        options={[
                          { value: 'low', label: 'Low', description: 'Low priority opportunity' },
                          { value: 'medium', label: 'Medium', description: 'Medium priority opportunity' },
                          { value: 'high', label: 'High', description: 'High priority opportunity' },
                          { value: 'urgent', label: 'Urgent', description: 'Urgent priority opportunity' }
                        ]}
                        value={field.value || 'medium'}
                        onValueChange={field.onChange}
                        placeholder="Select priority"
                        searchPlaceholder="Search priorities..."
                        emptyMessage="No priorities found"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="ownerId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Opportunity Owner</FormLabel>
                  <FormControl>
                    <SearchableSelect
                      options={users.map((user): SearchableSelectOption => ({
                        value: user.id,
                        label: `${user.firstName} ${user.lastName}`,
                        description: (user as any).email ? `Email: ${(user as any).email}` : `User ID: ${user.id}`
                      }))}
                      value={field.value || leadOwnerId || ''}
                      onValueChange={field.onChange}
                      placeholder="Select opportunity owner"
                      searchPlaceholder="Search users..."
                      emptyMessage="No users found"
                      allowClear
                      clearLabel="Clear owner"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Leave empty to use lead description"
                      className="resize-none"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="conversionNotes"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Conversion Notes</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Add notes about this conversion..."
                      className="resize-none"
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    These notes will be saved in the conversion history.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
                disabled={isLoading}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={isLoading || !eligibility?.eligible}
                className="min-w-[120px]"
              >
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Converting...
                  </>
                ) : (
                  'Convert to Opportunity'
                )}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
