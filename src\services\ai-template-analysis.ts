import { aiService } from '@/services/ai-service';
import { AIRequest } from '@/services/ai-service';
import { geminiFileService } from '@/services/gemini-file-service';
import { prisma } from '@/lib/prisma';
import fs from 'fs/promises';
import path from 'path';
import os from 'os';

export interface TemplateAnalysisRequest {
  file: File;
  tenantId: string;
  userId: string;
}

export interface TemplateAnalysisResponse {
  styling: {
    fontFamily: string;
    arabicFontFamily?: string;
    englishFontFamily?: string;
    fontSize: number;
    primaryColor: string;
    secondaryColor: string;
    accentColor?: string;
    headerColor?: string;
    textColor?: string;
    backgroundColor?: string;
    lineSpacing?: number;
    marginTop?: number;
    marginBottom?: number;
    marginLeft?: number;
    marginRight?: number;
    headerFontSize?: number;
    subHeaderFontSize?: number;
    bodyFontSize?: number;
    isRTL?: boolean;
    hasArabicContent?: boolean;
  };
  branding: {
    companyName?: string;
    companyNameArabic?: string;
    logoPosition?: string;
    primaryColor: string;
    secondaryColor: string;
    brandColors?: string[];
    logoUrl?: string;
    watermark?: string;
    headerStyle?: string;
    footerStyle?: string;
    theme?: string;
    hasArabicBranding?: boolean;
    logoPlaceholder?: string;
    sectionTitleStyle?: string;
  };
  variables: string[];
  htmlTemplate?: string;
  confidence: number;
  processingTime: number;
}

export class AITemplateAnalysisService {
  constructor() {
    // Check if AI service is available
    if (!process.env.GOOGLE_AI_API_KEY) {
      throw new Error('Google AI API key not configured');
    }
  }

  /**
   * Analyze a DOCX template using AI to extract styling, branding, and variables
   */
  async analyzeTemplate(request: TemplateAnalysisRequest): Promise<TemplateAnalysisResponse> {
    const startTime = Date.now();

    try {
      // Validate file type
      if (!request.file.name.toLowerCase().endsWith('.docx')) {
        throw new Error('Only DOCX files are supported for template analysis');
      }

      // Upload file to temporary storage and then to Gemini
      const fileUri = await this.uploadFileToGemini(request.file);

      // Generate analysis using AI service
      const analysisPrompt = this.buildAnalysisPrompt();

      const aiRequest: AIRequest = {
        prompt: analysisPrompt,
        systemPrompt: this.getSystemPrompt(),
        fileUris: [fileUri],
        context: {
          mimeType: request.file.type || 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
          fileName: request.file.name,
          fileSize: request.file.size
        },
        tenantId: request.tenantId,
        userId: request.userId,
        feature: 'template_analysis'
      };

      const response = await aiService.generateResponse(aiRequest, {
        temperature: 0.1,
        provider: 'google', // Force Google for file analysis
        model: 'gemini-2.5-pro-preview-06-05' // Force flash model for faster response
      });

      // Parse the AI response
      const analysis = this.parseAnalysisResponse(response.content);

      // Clean up the uploaded file from Gemini
      try {
        await this.cleanupGeminiFile(fileUri);
      } catch (error) {
        console.warn('Failed to clean up Gemini file:', error);
      }

      const processingTime = Date.now() - startTime;

      return {
        ...analysis,
        processingTime
      };

    } catch (error) {
      console.error('Template analysis error:', error);

      // Return fallback analysis
      return this.getFallbackAnalysis(Date.now() - startTime);
    }
  }

  /**
   * Upload file to Gemini for processing using the unified file service approach
   */
  private async uploadFileToGemini(file: File): Promise<string> {
    const buffer = Buffer.from(await file.arrayBuffer());
    const tempPath = path.join(os.tmpdir(), `template_${Date.now()}_${file.name.replace(/[^a-zA-Z0-9.-]/g, '_')}`);

    try {
      // Write file to temporary location
      await fs.writeFile(tempPath, buffer);

      // Create a temporary document record for the upload
      const tempDocument = await prisma.document.create({
        data: {
          tenantId: 'temp', // Temporary tenant ID
          name: file.name,
          filePath: tempPath,
          fileSize: file.size,
          mimeType: file.type || 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
          description: 'Temporary document for template analysis',
          uploadedById: 'system'
        }
      });

      // Use the Gemini file service to upload
      const uploadResult = await geminiFileService.uploadFile({
        documentId: tempDocument.id,
        filePath: tempPath,
        mimeType: file.type || 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        displayName: file.name,
        tenantId: 'temp'
      });

      // Clean up temporary document record
      await prisma.document.delete({
        where: { id: tempDocument.id }
      });

      // Clean up temporary file
      try {
        await fs.unlink(tempPath);
      } catch (error) {
        console.warn('Failed to clean up temporary file:', error);
      }

      if (uploadResult.state === 'FAILED') {
        throw new Error(uploadResult.error || 'File upload to Gemini failed');
      }

      return uploadResult.fileUri;
    } catch (error) {
      // Clean up temporary file on error
      try {
        await fs.unlink(tempPath);
      } catch (unlinkError) {
        console.warn('Failed to clean up temporary file after error:', unlinkError);
      }
      throw error;
    }
  }

  /**
   * Clean up uploaded file from Gemini
   */
  private async cleanupGeminiFile(fileUri: string): Promise<void> {
    try {
      await geminiFileService.deleteFile(fileUri);
    } catch (error) {
      console.warn('Failed to delete Gemini file:', error);
    }
  }

  /**
   * Build the analysis prompt for AI
   */
  private buildAnalysisPrompt(): string {
    return `
You are an expert full-stack developer specializing in creating high-fidelity, data-driven web templates.

Objective: Convert the provided business proposal document (template.docx) into a single, reusable, and visually accurate HTML file. This template will be used to generate new proposals by replacing placeholder tags with specific data.

Detailed Instructions:

1. **Analyze the Source Document**:
   - Carefully parse the structure, content, and layout of the attached template.docx
   - Identify all distinct sections, such as the cover page, executive summary, scope of work, project plan, team CVs, etc.
   - Pay close attention to the visual styling: font choices (specifically 'Tajawal' for Arabic content), color palette (primary green #3A7D5E and accent gold #C7A653), text sizes, and the overall professional aesthetic
   - Note any Arabic/RTL content and ensure proper text direction handling

2. **Identify and Convert to Dynamic Tags**:
   - Read through the document and identify all content that is project-specific and will change for each new proposal
   - Replace this dynamic text with clear, descriptive placeholder tags enclosed in double curly braces (e.g., {{Client_Name}}, {{Project_Name}}, {{Competition_Number}}, {{Approver_Name}})
   - For elements like tables (contact info, timelines, etc.) and lists of team members, replace the entire data section with a single tag (e.g., {{Contact_Table}}, {{Team_CVs}}) that can be populated programmatically later
   - Identify Arabic content placeholders with appropriate naming (e.g., {{Project_Duration_Months}}, {{Submission_Date_Hijri}})

3. **Generate a Single, Self-Contained HTML File**:
   - Produce one complete HTML file containing all the necessary structure, styles, and scripts
   - Use Tailwind CSS for all styling. Load it via the CDN (<script src="https://cdn.tailwindcss.com"></script>)
   - Embed any custom CSS required to match the original document's look and feel (like the 'Tajawal' font from Google Fonts and print-specific styles) within a <style> tag in the <head>
   - Include proper RTL support for Arabic content

4. **Replicate the Visual Identity and Structure**:
   - Layout: Recreate the document's layout, including the full-height cover page and logical section breaks. Use page-break-after: always; in your CSS to ensure a clean, paginated appearance for printing or PDF generation
   - Styling: Your final output must be a high-fidelity replica of the original document's visual design. This includes matching the color scheme, typography, section title styles (with the gold underline), and overall spacing
   - Images and Diagrams: For every image, chart, and diagram in the source document (e.g., client logo, NORA diagram, Gantt chart, team structure, partner logos), you must use placeholder images from https://placehold.co. Configure these placeholders to indicate the intended content and match the approximate dimensions of the originals

5. **Final Code Quality**:
   - The HTML must be clean, well-structured, and semantically commented
   - The template must be fully responsive, ensuring it looks excellent on desktops, tablets, and mobile devices
   - The final code should be easy for another developer to understand and integrate with a back-end system for populating the {{tags}}
   - Include proper Arabic font loading and RTL text direction support

Please provide a comprehensive analysis in the following JSON format:

{
  "styling": {
    "fontFamily": "Tajawal",
    "arabicFontFamily": "Tajawal",
    "englishFontFamily": "Inter",
    "fontSize": 12,
    "primaryColor": "#3A7D5E",
    "secondaryColor": "#C7A653",
    "accentColor": "#C7A653",
    "headerColor": "#3A7D5E",
    "textColor": "#1f2937",
    "backgroundColor": "#ffffff",
    "lineSpacing": 1.6,
    "marginTop": 20,
    "marginBottom": 20,
    "marginLeft": 25,
    "marginRight": 25,
    "headerFontSize": 24,
    "subHeaderFontSize": 18,
    "bodyFontSize": 14,
    "isRTL": true,
    "hasArabicContent": true
  },
  "branding": {
    "companyName": "{{Company_Name}}",
    "companyNameArabic": "{{Company_Name_Arabic}}",
    "logoPosition": "header-center",
    "primaryColor": "#3A7D5E",
    "secondaryColor": "#C7A653",
    "brandColors": ["#3A7D5E", "#C7A653", "#ffffff"],
    "headerStyle": "professional with gold accent underlines",
    "footerStyle": "minimal with company branding",
    "theme": "professional-arabic",
    "hasArabicBranding": true,
    "logoPlaceholder": "{{Company_Logo}}",
    "sectionTitleStyle": "green text with gold underline"
  },
  "variables": [
    "Client_Name",
    "Client_Name_Arabic",
    "Project_Name",
    "Project_Name_Arabic",
    "Competition_Number",
    "Approver_Name",
    "Approver_Title",
    "Project_Duration_Months",
    "Submission_Date_Hijri",
    "Submission_Date_Gregorian",
    "Company_Name",
    "Company_Name_Arabic",
    "Company_Logo",
    "Contact_Table",
    "Team_CVs",
    "Project_Timeline",
    "Technical_Specifications",
    "Financial_Details"
  ],
  "htmlTemplate": "<!DOCTYPE html><html lang=\"ar\" dir=\"rtl\"><head><meta charset=\"UTF-8\"><meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\"><title>{{Project_Name}} - عرض فني</title><script src=\"https://cdn.tailwindcss.com\"></script><link href=\"https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&family=Inter:wght@300;400;500;700&display=swap\" rel=\"stylesheet\"><style>body { font-family: 'Tajawal', sans-serif; } .english { font-family: 'Inter', sans-serif; direction: ltr; } .section-title { color: #3A7D5E; border-bottom: 3px solid #C7A653; padding-bottom: 8px; margin-bottom: 20px; } @media print { .page-break { page-break-after: always; } }</style></head><body class=\"bg-white text-gray-900\"><div class=\"min-h-screen flex flex-col justify-center items-center p-8 page-break\"><img src=\"https://placehold.co/200x100/3A7D5E/ffffff?text={{Company_Logo}}\" alt=\"شعار الشركة\" class=\"mx-auto mb-8\"><h1 class=\"text-4xl font-bold text-[#3A7D5E] mb-4 text-center\">{{Project_Name}}</h1><h2 class=\"text-2xl text-[#C7A653] mb-8 text-center\">عرض فني</h2><div class=\"text-lg text-center space-y-2\"><p>مُقدم إلى: {{Client_Name}}</p><p>من: {{Company_Name}}</p><p>التاريخ: {{Submission_Date_Hijri}}</p></div></div><div class=\"max-w-4xl mx-auto p-8\"><section class=\"mb-12\"><h2 class=\"section-title text-2xl font-bold\">تفاصيل العرض الفني</h2><div class=\"bg-gray-50 p-6 rounded-lg border-r-4 border-[#3A7D5E]\"><ul class=\"space-y-3 text-lg\"><li><strong>مدة المشروع:</strong> {{Project_Duration_Months}} شهرًا</li><li><strong>القائم بالموافقة على العرض:</strong> {{Approver_Name}} <em class=\"text-gray-600\">({{Approver_Title}})</em></li><li><strong>التاريخ:</strong> {{Submission_Date_Hijri}} ({{Submission_Date_Gregorian}})</li></ul></div></section><section class=\"mb-12\"><h2 class=\"section-title text-2xl font-bold\">معلومات الاتصال</h2>{{Contact_Table}}</section><section class=\"mb-12\"><h2 class=\"section-title text-2xl font-bold\">فريق العمل</h2>{{Team_CVs}}</section></div></body></html>",
  "confidence": 0.85
}

Focus on extracting actual styling values, colors, and formatting from the document structure and content.
Generate a complete, self-contained HTML file that replicates the visual design with high fidelity.
`;
  }

  /**
   * Get system prompt for template analysis
   */
  private getSystemPrompt(): string {
    return `You are an expert full-stack developer specializing in creating high-fidelity, data-driven web templates from business documents, with particular expertise in Arabic/RTL content and professional Middle Eastern document design.

Your primary task is to analyze the uploaded template file and generate a complete, self-contained HTML template that replicates the original document with pixel-perfect accuracy.

Focus on these key areas:
1. **Styling Configuration**: Extract fonts (prioritize Tajawal for Arabic, Inter for English), colors, spacing, margins, and RTL support
2. **Branding Elements**: Identify company info, logos, themes, colors, and Arabic branding elements
3. **Template Variables**: Find all dynamic content and replace with descriptive {{placeholder}} tags
4. **HTML Template Generation**: Create a complete, production-ready HTML file

Requirements for HTML template generation:
- Use Tailwind CSS for all styling (loaded via CDN)
- Include proper Arabic font support (Tajawal for Arabic, Inter for English)
- Create a single, self-contained HTML file with RTL support
- Replicate the visual identity and structure of the original document with high fidelity
- Use placeholder images from https://placehold.co for all images/diagrams
- Include page-break-after: always; for clean pagination
- Make it fully responsive and print-ready
- Replace dynamic content with descriptive {{placeholder}} tags
- Support both Arabic and English content with proper text direction
- Include professional styling with green (#3A7D5E) and gold (#C7A653) color scheme
- Add section title styling with gold underlines
- Ensure proper Arabic typography and spacing

Special attention to Arabic content:
- Detect Arabic text and mark with appropriate placeholders
- Use RTL text direction where appropriate
- Include Hijri and Gregorian date placeholders
- Support Arabic company names and project titles
- Maintain professional Arabic document formatting standards

The most important output is the htmlTemplate field - this should be a complete, working HTML file that can be used immediately for document generation.

Always respond with valid JSON format and provide realistic confidence scores based on the clarity and completeness of the extracted information.

For colors, always use hex format (#RRGGBB). For fonts, prioritize Tajawal for Arabic content and Inter for English. For measurements, use points or pixels as appropriate.

If certain information cannot be determined from the template, use reasonable defaults that match professional Arabic business document standards.`;
  }

  /**
   * Parse AI response and extract template analysis
   */
  private parseAnalysisResponse(content: string): Omit<TemplateAnalysisResponse, 'processingTime'> {
    try {
      // Try to extract JSON from the response
      const jsonMatch = content.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No JSON found in AI response');
      }

      const parsed = JSON.parse(jsonMatch[0]);
      
      // Validate and normalize the response
      return {
        styling: {
          fontFamily: parsed.styling?.fontFamily || 'Tajawal',
          arabicFontFamily: parsed.styling?.arabicFontFamily || 'Tajawal',
          englishFontFamily: parsed.styling?.englishFontFamily || 'Inter',
          fontSize: parsed.styling?.fontSize || 14,
          primaryColor: parsed.styling?.primaryColor || '#3A7D5E',
          secondaryColor: parsed.styling?.secondaryColor || '#C7A653',
          accentColor: parsed.styling?.accentColor || '#C7A653',
          headerColor: parsed.styling?.headerColor || '#3A7D5E',
          textColor: parsed.styling?.textColor || '#1f2937',
          backgroundColor: parsed.styling?.backgroundColor || '#ffffff',
          lineSpacing: parsed.styling?.lineSpacing || 1.6,
          marginTop: parsed.styling?.marginTop || 20,
          marginBottom: parsed.styling?.marginBottom || 20,
          marginLeft: parsed.styling?.marginLeft || 25,
          marginRight: parsed.styling?.marginRight || 25,
          headerFontSize: parsed.styling?.headerFontSize || 24,
          subHeaderFontSize: parsed.styling?.subHeaderFontSize || 18,
          bodyFontSize: parsed.styling?.bodyFontSize || 14,
          isRTL: parsed.styling?.isRTL || true,
          hasArabicContent: parsed.styling?.hasArabicContent || true,
        },
        branding: {
          companyName: parsed.branding?.companyName || '{{Company_Name}}',
          companyNameArabic: parsed.branding?.companyNameArabic || '{{Company_Name_Arabic}}',
          logoPosition: parsed.branding?.logoPosition || 'header-center',
          primaryColor: parsed.branding?.primaryColor || '#3A7D5E',
          secondaryColor: parsed.branding?.secondaryColor || '#C7A653',
          brandColors: parsed.branding?.brandColors || ['#3A7D5E', '#C7A653', '#ffffff'],
          logoUrl: parsed.branding?.logoUrl,
          watermark: parsed.branding?.watermark,
          headerStyle: parsed.branding?.headerStyle || 'professional with gold accent underlines',
          footerStyle: parsed.branding?.footerStyle || 'minimal with company branding',
          theme: parsed.branding?.theme || 'professional-arabic',
          hasArabicBranding: parsed.branding?.hasArabicBranding || true,
          logoPlaceholder: parsed.branding?.logoPlaceholder || '{{Company_Logo}}',
          sectionTitleStyle: parsed.branding?.sectionTitleStyle || 'green text with gold underline',
        },
        variables: Array.isArray(parsed.variables) ? parsed.variables : [
          'Client_Name',
          'Client_Name_Arabic',
          'Project_Name',
          'Project_Name_Arabic',
          'Competition_Number',
          'Approver_Name',
          'Approver_Title',
          'Project_Duration_Months',
          'Submission_Date_Hijri',
          'Submission_Date_Gregorian',
          'Company_Name',
          'Company_Name_Arabic',
          'Company_Logo',
          'Contact_Table',
          'Team_CVs'
        ],
        htmlTemplate: parsed.htmlTemplate || undefined,
        confidence: Math.min(Math.max(parsed.confidence || 0.7, 0), 1)
      };

    } catch (error) {
      console.error('Error parsing AI response:', error);
      throw new Error('Failed to parse template analysis response');
    }
  }

  /**
   * Get fallback analysis when AI fails
   */
  private getFallbackAnalysis(processingTime: number): TemplateAnalysisResponse {
    return {
      styling: {
        fontFamily: 'Tajawal',
        arabicFontFamily: 'Tajawal',
        englishFontFamily: 'Inter',
        fontSize: 14,
        primaryColor: '#3A7D5E',
        secondaryColor: '#C7A653',
        accentColor: '#C7A653',
        headerColor: '#3A7D5E',
        textColor: '#1f2937',
        backgroundColor: '#ffffff',
        lineSpacing: 1.6,
        marginTop: 20,
        marginBottom: 20,
        marginLeft: 25,
        marginRight: 25,
        headerFontSize: 24,
        subHeaderFontSize: 18,
        bodyFontSize: 14,
        isRTL: true,
        hasArabicContent: true,
      },
      branding: {
        companyName: '{{Company_Name}}',
        companyNameArabic: '{{Company_Name_Arabic}}',
        logoPosition: 'header-center',
        primaryColor: '#3A7D5E',
        secondaryColor: '#C7A653',
        brandColors: ['#3A7D5E', '#C7A653', '#ffffff'],
        headerStyle: 'professional with gold accent underlines',
        footerStyle: 'minimal with company branding',
        theme: 'professional-arabic',
        hasArabicBranding: true,
        logoPlaceholder: '{{Company_Logo}}',
        sectionTitleStyle: 'green text with gold underline',
      },
      variables: [
        'Client_Name',
        'Client_Name_Arabic',
        'Project_Name',
        'Project_Name_Arabic',
        'Competition_Number',
        'Approver_Name',
        'Approver_Title',
        'Project_Duration_Months',
        'Submission_Date_Hijri',
        'Submission_Date_Gregorian',
        'Company_Name',
        'Company_Name_Arabic',
        'Company_Logo',
        'Contact_Table',
        'Team_CVs'
      ],
      htmlTemplate: this.generateFallbackHtmlTemplate(),
      confidence: 0.5,
      processingTime
    };
  }

  /**
   * Generate a fallback HTML template when AI analysis fails
   */
  private generateFallbackHtmlTemplate(): string {
    return `<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{Project_Name}} - عرض فني</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&family=Inter:wght@300;400;500;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Tajawal', sans-serif; }
        .english { font-family: 'Inter', sans-serif; direction: ltr; }
        .section-title {
            color: #3A7D5E;
            border-bottom: 3px solid #C7A653;
            padding-bottom: 8px;
            margin-bottom: 20px;
        }
        @media print {
            .page-break { page-break-after: always; }
            .no-print { display: none; }
        }
    </style>
</head>
<body class="bg-white text-gray-900">
    <!-- Cover Page -->
    <div class="min-h-screen flex flex-col justify-center items-center p-8 page-break">
        <div class="text-center max-w-4xl">
            <img src="https://placehold.co/200x100/3A7D5E/ffffff?text={{Company_Logo}}" alt="شعار الشركة" class="mx-auto mb-8">
            <h1 class="text-4xl font-bold text-[#3A7D5E] mb-4">{{Project_Name}}</h1>
            <h2 class="text-2xl text-[#C7A653] mb-8">عرض فني</h2>
            <div class="text-lg text-center space-y-2">
                <p>مُقدم إلى: {{Client_Name}}</p>
                <p>من: {{Company_Name}}</p>
                <p>التاريخ: {{Submission_Date_Hijri}}</p>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="max-w-4xl mx-auto p-8">
        <section class="mb-12">
            <h2 class="section-title text-2xl font-bold">تفاصيل العرض الفني</h2>
            <div class="bg-gray-50 p-6 rounded-lg border-r-4 border-[#3A7D5E]">
                <ul class="space-y-3 text-lg">
                    <li><strong>مدة المشروع:</strong> {{Project_Duration_Months}} شهرًا</li>
                    <li><strong>القائم بالموافقة على العرض:</strong> {{Approver_Name}} <em class="text-gray-600">({{Approver_Title}})</em></li>
                    <li><strong>التاريخ:</strong> {{Submission_Date_Hijri}} ({{Submission_Date_Gregorian}})</li>
                </ul>
            </div>
        </section>

        <section class="mb-12">
            <h2 class="section-title text-2xl font-bold">معلومات الاتصال</h2>
            {{Contact_Table}}
        </section>

        <section class="mb-12">
            <h2 class="section-title text-2xl font-bold">فريق العمل</h2>
            {{Team_CVs}}
        </section>
    </div>
</body>
</html>`;
  }
}

export const aiTemplateAnalysisService = new AITemplateAnalysisService();
