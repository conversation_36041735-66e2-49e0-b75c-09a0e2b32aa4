import React, { useState } from 'react';
import { But<PERSON> } from '../ui/button';
import { Badge } from '../ui/badge';
import { Popover, PopoverContent, PopoverTrigger } from '../ui/popover';
import { ScrollArea } from '../ui/scroll-area';
import { Separator } from '../ui/separator';
import {
  Bell,
  MessageSquare,
  Calendar,
  Users,
  Check,
  Settings,
  AlertCircle,
  FileText,
  UserPlus,
  Briefcase
} from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import { SmoothLink } from '../ui/smooth-link';
import { useNotifications, UnifiedNotification } from '@/hooks/use-notifications';

interface NotificationBellProps {
  className?: string;
}

export default function NotificationBell({ className }: NotificationBellProps) {
  const [open, setOpen] = useState(false);
  const {
    notifications,
    unreadCount,
    loading,
    error,
    markAsRead,
    markAllAsRead,
    refresh
  } = useNotifications({
    limit: 20,
    unreadOnly: false,
    autoRefresh: true,
    refreshInterval: 30000, // 30 seconds
  });

  const handleMarkAsRead = async (notificationId: string) => {
    try {
      await markAsRead(notificationId);
    } catch (error) {
      console.error('Failed to mark notification as read:', error);
    }
  };

  const handleMarkAllAsRead = async () => {
    try {
      await markAllAsRead();
    } catch (error) {
      console.error('Failed to mark all notifications as read:', error);
    }
  };

  const getNotificationIcon = (notification: UnifiedNotification) => {
    // Icon based on category and type
    switch (notification.category) {
      case 'lead':
        return <UserPlus className="h-4 w-4" />;
      case 'handover':
        return <Briefcase className="h-4 w-4" />;
      case 'communication':
        switch (notification.type) {
          case 'message':
          case 'mention':
            return <MessageSquare className="h-4 w-4" />;
          case 'event':
            return <Calendar className="h-4 w-4" />;
          case 'channel':
            return <Users className="h-4 w-4" />;
          default:
            return <MessageSquare className="h-4 w-4" />;
        }
      case 'system':
        return <Settings className="h-4 w-4" />;
      case 'general':
      default:
        return <Bell className="h-4 w-4" />;
    }
  };

  const getNotificationColor = (notification: UnifiedNotification) => {
    switch (notification.category) {
      case 'lead':
        return 'text-blue-600';
      case 'handover':
        return 'text-purple-600';
      case 'communication':
        switch (notification.type) {
          case 'message':
            return 'text-green-600';
          case 'mention':
            return 'text-orange-600';
          case 'event':
            return 'text-blue-600';
          case 'channel':
            return 'text-indigo-600';
          default:
            return 'text-green-600';
        }
      case 'system':
        return 'text-gray-600';
      case 'general':
      default:
        return 'text-gray-600';
    }
  };

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button variant="ghost" size="sm" className={`relative ${className}`}>
          <Bell className="h-5 w-5" />
          {unreadCount > 0 && (
            <Badge 
              variant="destructive" 
              className="absolute -top-1 -right-1 h-5 w-5 flex items-center justify-center text-xs p-0"
            >
              {unreadCount > 99 ? '99+' : unreadCount}
            </Badge>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-80 p-0" align="end">
        <div className="flex items-center justify-between p-4 border-b">
          <h3 className="font-semibold">Notifications</h3>
          <div className="flex items-center space-x-2">
            {unreadCount > 0 && (
              <Button
                variant="ghost"
                size="sm"
                onClick={handleMarkAllAsRead}
                className="text-xs"
              >
                <Check className="h-3 w-3 mr-1" />
                Mark all read
              </Button>
            )}
            <SmoothLink href="/settings/notifications">
              <Button variant="ghost" size="sm">
                <Settings className="h-4 w-4" />
              </Button>
            </SmoothLink>
          </div>
        </div>

        <ScrollArea className="h-96">
          {loading ? (
            <div className="flex flex-col items-center justify-center py-8 text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mb-2"></div>
              <p className="text-sm text-muted-foreground">Loading notifications...</p>
            </div>
          ) : error ? (
            <div className="flex flex-col items-center justify-center py-8 text-center">
              <AlertCircle className="h-8 w-8 text-destructive mb-2" />
              <p className="text-sm text-muted-foreground">Failed to load notifications</p>
              <Button
                variant="ghost"
                size="sm"
                onClick={refresh}
                className="mt-2"
              >
                Try again
              </Button>
            </div>
          ) : notifications.length === 0 ? (
            <div className="flex flex-col items-center justify-center py-8 text-center">
              <Bell className="h-8 w-8 text-muted-foreground mb-2" />
              <p className="text-sm text-muted-foreground">No notifications</p>
            </div>
          ) : (
            <div className="divide-y">
              {notifications.map((notification) => (
                <div
                  key={notification.id}
                  className={`p-4 hover:bg-muted/50 cursor-pointer transition-colors ${
                    !notification.isRead ? 'bg-blue-50/50' : ''
                  }`}
                  onClick={() => {
                    if (!notification.isRead) {
                      handleMarkAsRead(notification.id);
                    }
                    if (notification.actionUrl) {
                      window.location.href = notification.actionUrl;
                    }
                    setOpen(false);
                  }}
                >
                  <div className="flex items-start space-x-3">
                    <div className={`p-1 rounded-full ${getNotificationColor(notification)}`}>
                      {getNotificationIcon(notification)}
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between">
                        <p className="text-sm font-medium truncate">
                          {notification.title}
                        </p>
                        {!notification.isRead && (
                          <div className="w-2 h-2 bg-blue-600 rounded-full ml-2 flex-shrink-0" />
                        )}
                      </div>
                      <p className="text-sm text-muted-foreground mt-1 line-clamp-2">
                        {notification.message}
                      </p>
                      <div className="flex items-center justify-between mt-2">
                        <span className="text-xs text-muted-foreground">
                          {formatDistanceToNow(new Date(notification.createdAt), { addSuffix: true })}
                        </span>
                        {notification.relatedEntity && (
                          <span className="text-xs text-muted-foreground">
                            {notification.relatedEntity.name}
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </ScrollArea>

        {notifications.length > 0 && (
          <>
            <Separator />
            <div className="p-2">
              <SmoothLink href="/communication">
                <Button variant="ghost" size="sm" className="w-full">
                  View all in Communication Hub
                </Button>
              </SmoothLink>
            </div>
          </>
        )}
      </PopoverContent>
    </Popover>
  );
}
