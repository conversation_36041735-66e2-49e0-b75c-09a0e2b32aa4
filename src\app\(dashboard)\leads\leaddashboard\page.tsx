'use client';

import { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import {
  ChartBarIcon,
  FunnelIcon,
  BellIcon,
  EnvelopeIcon,
  PlusIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';
import { LeadPipeline } from '@/components/leads/lead-pipeline';
import { NurturingCampaigns } from '@/components/leads/nurturing-campaigns';
import { LeadNotifications } from '@/components/leads/lead-notifications';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { PageLayout } from '@/components/layout/page-layout';
import { PermissionGate } from '@/components/ui/permission-gate';
import { PermissionResource, PermissionAction } from '@/types';
import { EmptyState } from '@/components/ui/empty-state';
import { useToast } from '@/hooks/use-toast';
import { useFormatters } from '@/hooks/use-formatters';

interface LeadStats {
  total: number;
  byStatus: Record<string, number>;
  bySource: Record<string, number>;
  byPriority: Record<string, number>;
  averageScore: number;
  conversionRate: number;
  totalValue: number;
  conversionStats: {
    totalConverted: number;
    conversionsByMonth: Record<string, number>;
    averageTimeToConversion: number;
  };
}

interface CampaignStats {
  total: number;
  active: number;
}

export default function LeadDashboardPage() {
  const { data: session } = useSession();
  const router = useRouter();
  const { toast } = useToast();
  const { formatMoney } = useFormatters();
  const [activeTab, setActiveTab] = useState('pipeline');
  const [leadStats, setLeadStats] = useState<LeadStats | null>(null);
  const [campaignStats, setCampaignStats] = useState<CampaignStats | null>(null);
  const [loading, setLoading] = useState(true);

  const tenantId = session?.user?.tenants?.[0]?.id;

  // Helper function to safely format numbers
  const safeNumber = (value: any, defaultValue: number = 0): number => {
    if (value === null || value === undefined) return defaultValue;
    const num = Number(value);
    return isNaN(num) ? defaultValue : num;
  };

  // Fetch lead statistics
  const fetchLeadStats = async () => {
    if (!tenantId) return;

    try {
      const response = await fetch('/api/leads/stats', {
        headers: {
          'x-tenant-id': tenantId
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch lead statistics');
      }

      const data = await response.json();
      if (data.success) {
        setLeadStats(data.data.stats);
      }
    } catch (error) {
      console.error('Error fetching lead stats:', error);
      toast({
        title: 'Error',
        description: 'Failed to load lead statistics',
        variant: 'destructive'
      });
    }
  };

  // Fetch campaign statistics
  const fetchCampaignStats = async () => {
    if (!tenantId) return;

    try {
      const response = await fetch('/api/leads/nurturing/campaigns', {
        headers: {
          'x-tenant-id': tenantId
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch campaign statistics');
      }

      const data = await response.json();
      if (data.success) {
        const campaigns = data.data;
        const activeCampaigns = campaigns.filter((c: any) => c.isActive).length;
        setCampaignStats({
          total: campaigns.length,
          active: activeCampaigns
        });
      }
    } catch (error) {
      console.error('Error fetching campaign stats:', error);
      toast({
        title: 'Error',
        description: 'Failed to load campaign statistics',
        variant: 'destructive'
      });
    }
  };

  // Load all statistics
  const loadStats = async () => {
    setLoading(true);
    await Promise.all([
      fetchLeadStats(),
      fetchCampaignStats()
    ]);
    setLoading(false);
  };

  useEffect(() => {
    if (tenantId) {
      loadStats();
    }
  }, [tenantId]);

  const handleLeadClick = (leadId: string) => {
    router.push(`/leads/${leadId}`);
  };

  const handlePipelineLeadClick = (lead: { id: string }) => {
    router.push(`/leads/${lead.id}`);
  };

  const actions = (
    <div className="flex items-center gap-2">
      <PermissionGate
        resource={PermissionResource.LEADS}
        action={PermissionAction.CREATE}
      >
        <Button size="sm" onClick={() => router.push('/leads/new')}>
          <PlusIcon className="h-4 w-4 mr-2" />
          New Lead
        </Button>
      </PermissionGate>
    </div>
  );

  if (!tenantId) {
    return (
      <PageLayout>
        <div className="flex items-center justify-center h-64">
          <p className="text-muted-foreground">Loading...</p>
        </div>
      </PageLayout>
    );
  }

  return (
    <PermissionGate
      resource={PermissionResource.LEADS}
      action={PermissionAction.READ}
      fallback={
        <PageLayout>
          <Card>
            <CardContent className="p-8">
              <EmptyState
                icon={<ExclamationTriangleIcon className="w-12 h-12 text-muted-foreground" />}
                title="Access Denied"
                description="You don't have permission to view the lead dashboard. Please contact your administrator for access."
                action={{
                  label: 'Back to Dashboard',
                  onClick: () => router.push('/dashboard'),
                  variant: 'primary'
                }}
              />
            </CardContent>
          </Card>
        </PageLayout>
      }
    >
      <PageLayout
        title="Lead Dashboard"
        description="AI-powered lead management with pipeline visualization, automated nurturing, and real-time notifications"
        actions={actions}
      >
        <div className="space-y-6">
      {/* Quick Stats */}

      {/* Quick Stats */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Leads</CardTitle>
            <FunnelIcon className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            {loading ? (
              <Skeleton className="h-8 w-20" />
            ) : (
              <div className="text-2xl font-bold">{safeNumber(leadStats?.total).toLocaleString()}</div>
            )}
            {loading ? (
              <Skeleton className="h-4 w-32 mt-1" />
            ) : (
              <p className="text-xs text-muted-foreground">
                {safeNumber(leadStats?.conversionStats?.totalConverted)} converted
              </p>
            )}
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Campaigns</CardTitle>
            <EnvelopeIcon className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            {loading ? (
              <Skeleton className="h-8 w-16" />
            ) : (
              <div className="text-2xl font-bold">{campaignStats?.active || 0}</div>
            )}
            {loading ? (
              <Skeleton className="h-4 w-24 mt-1" />
            ) : (
              <p className="text-xs text-muted-foreground">
                {campaignStats?.total || 0} total campaigns
              </p>
            )}
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Conversion Rate</CardTitle>
            <ChartBarIcon className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            {loading ? (
              <Skeleton className="h-8 w-20" />
            ) : (
              <div className="text-2xl font-bold">{safeNumber(leadStats?.conversionRate).toFixed(1)}%</div>
            )}
            {loading ? (
              <Skeleton className="h-4 w-32 mt-1" />
            ) : (
              <p className="text-xs text-muted-foreground">
                {safeNumber(leadStats?.conversionStats?.averageTimeToConversion) > 0 ?
                  `${Math.round(safeNumber(leadStats?.conversionStats?.averageTimeToConversion))} days avg. time` :
                  'No conversion data'
                }
              </p>
            )}
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg. Lead Score</CardTitle>
            <BellIcon className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            {loading ? (
              <Skeleton className="h-8 w-16" />
            ) : (
              <div className="text-2xl font-bold">{Math.round(safeNumber(leadStats?.averageScore))}</div>
            )}
            {loading ? (
              <Skeleton className="h-4 w-28 mt-1" />
            ) : (
              <p className="text-xs text-muted-foreground">
                {formatMoney(safeNumber(leadStats?.totalValue))} total value
              </p>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Main Content Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="pipeline" className="flex items-center space-x-2">
            <FunnelIcon className="h-4 w-4" />
            <span>Pipeline</span>
          </TabsTrigger>
          <TabsTrigger value="nurturing" className="flex items-center space-x-2">
            <EnvelopeIcon className="h-4 w-4" />
            <span>Nurturing</span>
          </TabsTrigger>
          <TabsTrigger value="notifications" className="flex items-center space-x-2">
            <BellIcon className="h-4 w-4" />
            <span>Notifications</span>
          </TabsTrigger>
          <TabsTrigger value="analytics" className="flex items-center space-x-2">
            <ChartBarIcon className="h-4 w-4" />
            <span>Analytics</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="pipeline" className="space-y-6">
          {tenantId ? (
            <LeadPipeline
              tenantId={tenantId}
              onLeadClick={handlePipelineLeadClick}
            />
          ) : (
            <div className="flex items-center justify-center h-64">
              <p className="text-muted-foreground">Loading pipeline...</p>
            </div>
          )}
        </TabsContent>

        <TabsContent value="nurturing" className="space-y-6">
          <NurturingCampaigns tenantId={tenantId} />
        </TabsContent>

        <TabsContent value="notifications" className="space-y-6">
          <div className="grid gap-6 lg:grid-cols-3">
            <div className="lg:col-span-2">
              <LeadNotifications 
                tenantId={tenantId}
                onLeadClick={handleLeadClick}
              />
            </div>
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Notification Settings</CardTitle>
                  <CardDescription>
                    Customize how you receive lead notifications
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Email Notifications</span>
                    <Badge variant="secondary">Enabled</Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Browser Notifications</span>
                    <Badge variant="secondary">Enabled</Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">High-Value Leads</span>
                    <Badge variant="secondary">Enabled</Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Daily Digest</span>
                    <Badge variant="outline">Disabled</Badge>
                  </div>
                  <Button variant="outline" className="w-full">
                    Manage Preferences
                  </Button>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Recent Activity</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3 text-sm">
                    <div className="flex items-center space-x-2">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      <span>Lead converted to opportunity</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                      <span>Nurturing email sent</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                      <span>High-value lead created</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                      <span>Lead assigned to team member</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Lead Sources</CardTitle>
                <CardDescription>
                  Where your leads are coming from
                </CardDescription>
              </CardHeader>
              <CardContent>
                {loading ? (
                  <div className="space-y-4">
                    {[1, 2, 3, 4].map((i) => (
                      <div key={i} className="flex items-center justify-between">
                        <Skeleton className="h-4 w-20" />
                        <div className="flex items-center space-x-2">
                          <Skeleton className="w-20 h-2" />
                          <Skeleton className="h-4 w-8" />
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="space-y-4">
                    {leadStats && leadStats.bySource && Object.entries(leadStats.bySource).length > 0 ? (
                      Object.entries(leadStats.bySource)
                        .sort(([,a], [,b]) => b - a)
                        .slice(0, 5)
                        .map(([source, count]) => {
                          const total = safeNumber(leadStats?.total);
                          const percentage = total > 0 ? (safeNumber(count) / total) * 100 : 0;
                          const colors = ['bg-blue-500', 'bg-green-500', 'bg-orange-500', 'bg-purple-500', 'bg-red-500'];
                          const colorIndex = Object.keys(leadStats.bySource).indexOf(source) % colors.length;

                          return (
                            <div key={source} className="flex items-center justify-between">
                              <span className="text-sm">{source}</span>
                              <div className="flex items-center space-x-2">
                                <div className="w-20 h-2 bg-gray-200 rounded-full">
                                  <div
                                    className={`h-2 ${colors[colorIndex]} rounded-full`}
                                    style={{ width: `${Math.min(percentage, 100)}%` }}
                                  ></div>
                                </div>
                                <span className="text-sm text-muted-foreground w-10 text-right">
                                  {percentage.toFixed(0)}%
                                </span>
                              </div>
                            </div>
                          );
                        })
                    ) : (
                      <p className="text-sm text-muted-foreground">No lead source data available</p>
                    )}
                  </div>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Lead Status Distribution</CardTitle>
                <CardDescription>
                  Current lead status breakdown
                </CardDescription>
              </CardHeader>
              <CardContent>
                {loading ? (
                  <div className="space-y-4">
                    {[1, 2, 3, 4, 5].map((i) => (
                      <div key={i} className="flex items-center justify-between">
                        <Skeleton className="h-4 w-20" />
                        <Skeleton className="h-4 w-12" />
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="space-y-4">
                    {leadStats && leadStats.byStatus && Object.entries(leadStats.byStatus).length > 0 ? (
                      Object.entries(leadStats.byStatus)
                        .sort(([,a], [,b]) => b - a)
                        .map(([status, count]) => {
                          const statusLabels: Record<string, string> = {
                            'new': 'New Leads',
                            'contacted': 'Contacted',
                            'qualified': 'Qualified',
                            'proposal': 'Proposal',
                            'closed_won': 'Closed Won',
                            'closed_lost': 'Closed Lost',
                            'nurturing': 'Nurturing'
                          };

                          return (
                            <div key={status} className="flex items-center justify-between">
                              <span className="text-sm capitalize">
                                {statusLabels[status] || status.replace('_', ' ')}
                              </span>
                              <span className={`font-semibold ${status === 'closed_won' ? 'text-green-600' : status === 'closed_lost' ? 'text-red-600' : ''}`}>
                                {safeNumber(count).toLocaleString()}
                              </span>
                            </div>
                          );
                        })
                    ) : (
                      <p className="text-sm text-muted-foreground">No status data available</p>
                    )}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
        </div>
      </PageLayout>
    </PermissionGate>
  );
}
