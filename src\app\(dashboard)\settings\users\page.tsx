'use client';

import { UserManagement } from '@/components/users/user-management';
import { PermissionGate } from '@/components/ui/permission-gate';
import { PermissionResource, PermissionAction } from '@/types';
import { Card, CardContent } from '@/components/ui/card';
import { EmptyState } from '@/components/ui/empty-state';
import { ExclamationTriangleIcon, PlusIcon, UserPlusIcon } from '@heroicons/react/24/outline';
import { PageLayout } from '@/components/layout/page-layout';
import { Button } from '@/components/ui/button';
import { useRouter } from 'next/navigation';
import { useSmoothNavigation } from '@/hooks/use-smooth-navigation';
import { useTenant } from '@/hooks/use-tenant';

export default function UsersPage() {
  const router = useRouter();
  const { navigate } = useSmoothNavigation();
  const { currentTenant } = useTenant();

  const handleCreateUser = () => {
    router.push('/settings/users/new');
  };

  const handleAddExistingUser = () => {
    router.push('/settings/users/add-existing');
  };

  const actions = (
    <div className="flex items-center gap-2">
      <PermissionGate
        resource={PermissionResource.USERS}
        action={PermissionAction.CREATE}
      >
        <Button
          variant="outline"
          onClick={handleAddExistingUser}
          className="flex items-center gap-2"
        >
          <UserPlusIcon className="w-4 h-4" />
          Add Existing User
        </Button>
      </PermissionGate>
      <PermissionGate
        resource={PermissionResource.USERS}
        action={PermissionAction.CREATE}
      >
        <Button onClick={handleCreateUser} className="flex items-center gap-2">
          <PlusIcon className="w-4 h-4" />
          Create New User
        </Button>
      </PermissionGate>
    </div>
  );

  return (
    <PermissionGate
      resource={PermissionResource.USERS}
      action={PermissionAction.READ}
      fallback={
        <PageLayout>
          <Card>
            <CardContent className="p-8">
              <EmptyState
                icon={<ExclamationTriangleIcon className="w-12 h-12 text-muted-foreground" />}
                title="Access Denied"
                description="You don't have permission to access user management. Please contact your administrator for access."
                action={{
                  label: 'Back to Dashboard',
                  onClick: () => navigate('/dashboard'),
                  variant: 'primary'
                }}
              />
            </CardContent>
          </Card>
        </PageLayout>
      }
    >
      <PageLayout
        title="User Management"
        description={currentTenant ? `Manage users and their permissions for ${currentTenant.name}` : "Manage users and their permissions"}
        actions={actions}
      >
        <UserManagement />
      </PageLayout>
    </PermissionGate>
  );
}
