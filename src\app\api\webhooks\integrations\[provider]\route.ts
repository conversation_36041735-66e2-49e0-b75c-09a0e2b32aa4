import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { 
  IntegrationWebhookService,
  jiraWebhookSchema,
  asanaWebhookSchema,
  trelloWebhookSchema
} from '@/services/integration-webhook';
import { prisma } from '@/lib/prisma';

const webhookService = new IntegrationWebhookService();

interface RouteParams {
  params: Promise<{
    provider: string;
  }>;
}

/**
 * POST /api/webhooks/integrations/[provider]
 * Handle webhooks from external project management tools
 */
export async function POST(req: NextRequest, context: RouteParams) {
  try {
    const { provider } = await context.params;
    
    // Validate provider
    const supportedProviders = ['jira', 'asana', 'trello'];
    if (!supportedProviders.includes(provider)) {
      return NextResponse.json(
        { error: 'Unsupported integration provider' },
        { status: 400 }
      );
    }

    // Get raw body for signature validation
    const rawBody = await req.text();
    
    // Get headers for signature validation
    const signature = req.headers.get('x-hub-signature') || 
                     req.headers.get('x-hook-signature') || 
                     req.headers.get('x-trello-webhook');
    
    // Get tenant ID from query params or headers
    const tenantId = req.nextUrl.searchParams.get('tenant_id') || 
                    req.headers.get('x-tenant-id');
    
    if (!tenantId) {
      return NextResponse.json(
        { error: 'Tenant ID is required' },
        { status: 400 }
      );
    }

    // Verify tenant exists and has integration configured
    const tenant = await prisma.tenant.findUnique({
      where: { id: tenantId },
    });

    if (!tenant) {
      return NextResponse.json(
        { error: 'Tenant not found' },
        { status: 404 }
      );
    }

    // Get integration settings for signature validation
    const integrationSettings = await prisma.tenantSetting.findFirst({
      where: {
        tenantId,
        settingKey: `integration_${provider}_webhook_secret`,
      },
    });

    // Validate webhook signature if secret is configured
    if (integrationSettings?.settingValue && signature) {
      const secret = (integrationSettings.settingValue as any).secret;
      const isValid = webhookService.validateWebhookSignature(
        rawBody,
        signature,
        secret,
        provider
      );

      if (!isValid) {
        console.warn(`Invalid webhook signature for ${provider} from tenant ${tenantId}`);
        return NextResponse.json(
          { error: 'Invalid webhook signature' },
          { status: 401 }
        );
      }
    }

    // Parse JSON payload
    let payload;
    try {
      payload = JSON.parse(rawBody);
    } catch (error) {
      return NextResponse.json(
        { error: 'Invalid JSON payload' },
        { status: 400 }
      );
    }

    // Process webhook based on provider
    let result;
    
    switch (provider) {
      case 'jira':
        try {
          const validatedPayload = jiraWebhookSchema.parse(payload);
          result = await webhookService.processJiraWebhook(tenantId, validatedPayload);
        } catch (error) {
          if (error instanceof z.ZodError) {
            return NextResponse.json(
              { error: 'Invalid Jira webhook payload', details: error.errors },
              { status: 400 }
            );
          }
          throw error;
        }
        break;

      case 'asana':
        try {
          const validatedPayload = asanaWebhookSchema.parse(payload);
          result = await webhookService.processAsanaWebhook(tenantId, validatedPayload);
        } catch (error) {
          if (error instanceof z.ZodError) {
            return NextResponse.json(
              { error: 'Invalid Asana webhook payload', details: error.errors },
              { status: 400 }
            );
          }
          throw error;
        }
        break;

      case 'trello':
        try {
          const validatedPayload = trelloWebhookSchema.parse(payload);
          result = await webhookService.processTrelloWebhook(tenantId, validatedPayload);
        } catch (error) {
          if (error instanceof z.ZodError) {
            return NextResponse.json(
              { error: 'Invalid Trello webhook payload', details: error.errors },
              { status: 400 }
            );
          }
          throw error;
        }
        break;

      default:
        return NextResponse.json(
          { error: 'Unsupported provider' },
          { status: 400 }
        );
    }

    // Log webhook processing result
    await prisma.auditLog.create({
      data: {
        tenantId,
        action: `webhook_${provider}_processed`,
        resourceType: 'integration_webhook',
        resourceId: `${provider}_webhook`,
        newValues: {
          provider,
          success: result.success,
          message: result.message,
          updatedTasks: result.updatedTasks,
          updatedProjects: result.updatedProjects,
          errors: result.errors,
        },
        ipAddress: req.headers.get('x-forwarded-for') || req.headers.get('x-real-ip') || 'unknown',
        userAgent: req.headers.get('user-agent') || 'webhook',
      },
    });

    // Return appropriate response
    if (result.success) {
      return NextResponse.json({
        success: true,
        message: result.message,
        data: {
          updatedTasks: result.updatedTasks,
          updatedProjects: result.updatedProjects,
        },
      });
    } else {
      return NextResponse.json(
        {
          success: false,
          message: result.message,
          errors: result.errors,
        },
        { status: 422 } // Unprocessable Entity
      );
    }

  } catch (error) {
    console.error('Webhook processing error:', error);
    
    // Log error
    try {
      const { provider } = await context.params;
      const tenantId = req.nextUrl.searchParams.get('tenant_id');
      
      if (tenantId) {
        await prisma.auditLog.create({
          data: {
            tenantId,
            action: `webhook_${provider}_error`,
            resourceType: 'integration_webhook',
            resourceId: `${provider}_webhook`,
            newValues: {
              error: error instanceof Error ? error.message : 'Unknown error',
              stack: error instanceof Error ? error.stack : undefined,
            },
            ipAddress: req.headers.get('x-forwarded-for') || req.headers.get('x-real-ip') || 'unknown',
            userAgent: req.headers.get('user-agent') || 'webhook',
          },
        });
      }
    } catch (logError) {
      console.error('Failed to log webhook error:', logError);
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
