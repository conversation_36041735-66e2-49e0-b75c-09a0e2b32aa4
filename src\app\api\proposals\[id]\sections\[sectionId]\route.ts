import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { hasPermission } from '@/lib/permissions';
import { PermissionResource, PermissionAction } from '@/types';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';

// Request validation schema
const updateSectionSchema = z.object({
  title: z.string().min(1, 'Title is required').max(255).optional(),
  content: z.string().optional(),
  status: z.enum(['draft', 'generated', 'reviewed', 'approved']).optional(),
  orderIndex: z.number().min(0).optional()
});

/**
 * GET /api/proposals/[id]/sections/[sectionId]
 * Get a specific section
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string; sectionId: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user's current tenant
    const currentTenant = session.user.tenants?.[0];
    if (!currentTenant) {
      return NextResponse.json({ error: 'No tenant access' }, { status: 403 });
    }

    // Check permission for proposal reading
    const hasAccess = await hasPermission(
      PermissionResource.PROPOSALS,
      PermissionAction.READ,
      currentTenant.id
    );

    if (!hasAccess) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const { id: proposalId, sectionId } = params;

    // Get section with proposal verification
    const section = await prisma.proposalSection.findFirst({
      where: {
        id: sectionId,
        proposalId,
        tenantId: currentTenant.id
      },
      include: {
        proposal: {
          select: {
            id: true,
            title: true,
            generationStatus: true
          }
        },
        createdBy: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true
          }
        }
      }
    });

    if (!section) {
      return NextResponse.json(
        { error: 'Section not found or access denied' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: section,
      message: 'Section retrieved successfully'
    });

  } catch (error) {
    console.error('Error retrieving section:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/proposals/[id]/sections/[sectionId]
 * Update a specific section
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string; sectionId: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user's current tenant
    const currentTenant = session.user.tenants?.[0];
    if (!currentTenant) {
      return NextResponse.json({ error: 'No tenant access' }, { status: 403 });
    }

    // Check permission for proposal updating
    const hasAccess = await hasPermission(
      PermissionResource.PROPOSALS,
      PermissionAction.UPDATE,
      currentTenant.id
    );

    if (!hasAccess) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const { id: proposalId, sectionId } = params;
    const body = await request.json();
    
    // Validate request data
    const validatedData = updateSectionSchema.parse(body);

    // Verify section exists and belongs to tenant
    const existingSection = await prisma.proposalSection.findFirst({
      where: {
        id: sectionId,
        proposalId,
        tenantId: currentTenant.id
      },
      include: {
        proposal: true
      }
    });

    if (!existingSection) {
      return NextResponse.json(
        { error: 'Section not found or access denied' },
        { status: 404 }
      );
    }

    // Check if proposal is in a state that allows editing
    if (existingSection.proposal.generationStatus === 'generating') {
      return NextResponse.json(
        { error: 'Cannot edit section while proposal is being generated' },
        { status: 409 }
      );
    }

    // Calculate word count if content is being updated
    let wordCount = existingSection.wordCount;
    if (validatedData.content !== undefined) {
      wordCount = validatedData.content.trim().split(/\s+/).length;
    }

    // Update section
    const updatedSection = await prisma.proposalSection.update({
      where: { id: sectionId },
      data: {
        ...validatedData,
        wordCount,
        updatedAt: new Date()
      },
      include: {
        proposal: {
          select: {
            id: true,
            title: true,
            generationStatus: true
          }
        },
        createdBy: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true
          }
        }
      }
    });

    return NextResponse.json({
      success: true,
      data: updatedSection,
      message: 'Section updated successfully'
    });

  } catch (error) {
    console.error('Error updating section:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          error: 'Invalid request data', 
          details: error.errors.map(e => ({
            field: e.path.join('.'),
            message: e.message
          }))
        },
        { status: 400 }
      );
    }

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/proposals/[id]/sections/[sectionId]
 * Delete a specific section
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string; sectionId: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user's current tenant
    const currentTenant = session.user.tenants?.[0];
    if (!currentTenant) {
      return NextResponse.json({ error: 'No tenant access' }, { status: 403 });
    }

    // Check permission for proposal updating
    const hasAccess = await hasPermission(
      PermissionResource.PROPOSALS,
      PermissionAction.DELETE,
      currentTenant.id
    );

    if (!hasAccess) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const { id: proposalId, sectionId } = params;

    // Verify section exists and belongs to tenant
    const section = await prisma.proposalSection.findFirst({
      where: {
        id: sectionId,
        proposalId,
        tenantId: currentTenant.id
      },
      include: {
        proposal: true
      }
    });

    if (!section) {
      return NextResponse.json(
        { error: 'Section not found or access denied' },
        { status: 404 }
      );
    }

    // Check if proposal is in a state that allows deletion
    if (section.proposal.generationStatus === 'generating') {
      return NextResponse.json(
        { error: 'Cannot delete section while proposal is being generated' },
        { status: 409 }
      );
    }

    // Delete section
    await prisma.proposalSection.delete({
      where: { id: sectionId }
    });

    return NextResponse.json({
      success: true,
      message: 'Section deleted successfully'
    });

  } catch (error) {
    console.error('Error deleting section:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * @swagger
 * /api/proposals/{id}/sections/{sectionId}:
 *   get:
 *     summary: Get proposal section
 *     description: Retrieve a specific section of a proposal
 *     tags: [Proposals]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Proposal ID
 *       - in: path
 *         name: sectionId
 *         required: true
 *         schema:
 *           type: string
 *         description: Section ID
 *     responses:
 *       200:
 *         description: Section retrieved successfully
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden
 *       404:
 *         description: Section not found
 *       500:
 *         description: Internal server error
 *   put:
 *     summary: Update proposal section
 *     description: Update content, status, or other properties of a proposal section
 *     tags: [Proposals]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Proposal ID
 *       - in: path
 *         name: sectionId
 *         required: true
 *         schema:
 *           type: string
 *         description: Section ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               title:
 *                 type: string
 *                 maxLength: 255
 *               content:
 *                 type: string
 *               status:
 *                 type: string
 *                 enum: [draft, generated, reviewed, approved]
 *               orderIndex:
 *                 type: number
 *                 minimum: 0
 *     responses:
 *       200:
 *         description: Section updated successfully
 *       400:
 *         description: Invalid request data
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden
 *       404:
 *         description: Section not found
 *       409:
 *         description: Conflict - proposal is being generated
 *       500:
 *         description: Internal server error
 *   delete:
 *     summary: Delete proposal section
 *     description: Delete a specific section from a proposal
 *     tags: [Proposals]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Proposal ID
 *       - in: path
 *         name: sectionId
 *         required: true
 *         schema:
 *           type: string
 *         description: Section ID
 *     responses:
 *       200:
 *         description: Section deleted successfully
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden
 *       404:
 *         description: Section not found
 *       409:
 *         description: Conflict - proposal is being generated
 *       500:
 *         description: Internal server error
 */
