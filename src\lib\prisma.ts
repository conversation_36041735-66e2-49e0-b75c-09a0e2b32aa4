import { PrismaClient } from '@prisma/client';

const globalForPrisma = globalThis as unknown as {
  prisma: PrismaClient | undefined;
};

// Enhanced Prisma configuration with optimization
export const prisma = globalForPrisma.prisma ?? new PrismaClient({
  log: process.env.NODE_ENV === 'development' ? ['query', 'info', 'warn', 'error'] : ['error'],
  datasources: {
    db: {
      url: process.env.DATABASE_URL,
    },
  },
  // Error formatting for better debugging
  errorFormat: 'pretty',
  // Enhanced transaction options for better performance
  transactionOptions: {
    maxWait: 5000, // 5 seconds
    timeout: 10000, // 10 seconds
    isolationLevel: 'ReadCommitted',
  },
});

if (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma;

// Enhanced Performance monitoring utilities
export class DatabasePerformanceMonitor {
  private static queryTimes: Map<string, number[]> = new Map();
  private static slowQueryThreshold = parseInt(process.env.SLOW_QUERY_THRESHOLD_MS || '1000');

  static startTimer(queryName: string): () => void {
    const start = Date.now();
    return () => {
      const duration = Date.now() - start;
      const times = this.queryTimes.get(queryName) || [];
      times.push(duration);
      // Keep only last 100 measurements
      if (times.length > 100) times.shift();
      this.queryTimes.set(queryName, times);

      // Track slow queries and alert in production
      if (duration > this.slowQueryThreshold) {
        console.warn(`🐌 Slow query detected: ${queryName} took ${duration}ms`, {
          queryName,
          duration,
          timestamp: new Date().toISOString(),
          threshold: this.slowQueryThreshold
        });
      }
    };
  }

  static getAverageQueryTime(queryName: string): number {
    const times = this.queryTimes.get(queryName) || [];
    return times.length > 0 ? times.reduce((a, b) => a + b, 0) / times.length : 0;
  }

  static getQueryStats(): Record<string, { avg: number; count: number; max: number }> {
    const stats: Record<string, { avg: number; count: number; max: number }> = {};
    for (const [queryName, times] of this.queryTimes.entries()) {
      stats[queryName] = {
        avg: times.reduce((a, b) => a + b, 0) / times.length,
        count: times.length,
        max: Math.max(...times),
      };
    }
    return stats;
  }

  /**
   * Track a database operation with performance monitoring
   */
  static async trackQuery<T>(
    queryName: string,
    operation: () => Promise<T>,
    metadata?: Record<string, any>
  ): Promise<T> {
    const endTimer = this.startTimer(queryName);
    const startTime = Date.now();

    try {
      const result = await operation();
      endTimer();

      const duration = Date.now() - startTime;
      if (process.env.NODE_ENV === 'development') {
        console.log(`📊 Query ${queryName}: ${duration}ms`, metadata);
      }

      return result;
    } catch (error) {
      endTimer();
      console.error(`❌ Query ${queryName} failed:`, error);
      throw error;
    }
  }

  /**
   * Get performance summary
   */
  static getPerformanceSummary(): {
    totalQueries: number;
    slowQueries: number;
    averageResponseTime: number;
    slowQueryRate: number;
  } {
    const allStats = this.getQueryStats();
    if (!allStats || Object.keys(allStats).length === 0) {
      return {
        totalQueries: 0,
        slowQueries: 0,
        averageResponseTime: 0,
        slowQueryRate: 0
      };
    }

    const queryNames = Object.keys(allStats);
    const totalQueries = queryNames.reduce((sum, name) => sum + allStats[name].count, 0);
    const slowQueries = queryNames.reduce((sum, name) =>
      sum + allStats[name].count * (allStats[name].avg > this.slowQueryThreshold ? 1 : 0), 0);
    const totalDuration = queryNames.reduce((sum, name) => sum + (allStats[name].avg * allStats[name].count), 0);

    return {
      totalQueries,
      slowQueries,
      averageResponseTime: Math.round(totalDuration / totalQueries),
      slowQueryRate: Math.round((slowQueries / totalQueries) * 100)
    };
  }
}

// Enhanced tenant-aware database service with performance optimization
export class TenantDatabaseService {
  private currentTenantId: string | null = null;

  async setTenantContext(tenantId: string): Promise<void> {
    const endTimer = DatabasePerformanceMonitor.startTimer('setTenantContext');
    try {
      this.currentTenantId = tenantId;
      // Set PostgreSQL session variable for RLS
      await prisma.$executeRaw`SET app.current_tenant_id = ${tenantId}`;
    } finally {
      endTimer();
    }
  }

  async executeQuery(query: string, params?: any[]): Promise<any> {
    const endTimer = DatabasePerformanceMonitor.startTimer('executeQuery');
    try {
      // All queries automatically filtered by RLS policies
      return await prisma.$queryRawUnsafe(query, ...(params || []));
    } finally {
      endTimer();
    }
  }

  // Optimized tenant data retrieval with caching
  async getTenantWithCache(tenantId: string, includeOptions?: any) {
    const endTimer = DatabasePerformanceMonitor.startTimer('getTenantWithCache');
    try {
      return await prisma.tenant.findUnique({
        where: { id: tenantId },
        ...includeOptions,
      });
    } finally {
      endTimer();
    }
  }

  // Batch operations for better performance
  async batchCreateRecords<T>(model: any, data: T[]): Promise<any> {
    const endTimer = DatabasePerformanceMonitor.startTimer('batchCreateRecords');
    try {
      return await prisma.$transaction(
        data.map(item => model.create({ data: item }))
      );
    } finally {
      endTimer();
    }
  }

  // For super admin queries that need cross-tenant access
  async executeSuperAdminQuery(query: string, params?: any[]): Promise<any> {
    // Temporarily disable RLS for super admin operations
    await prisma.$executeRaw`SET row_security = off`;
    const result = await prisma.$queryRawUnsafe(query, ...(params || []));
    await prisma.$executeRaw`SET row_security = on`;
    return result;
  }

  // Tenant-aware query builder
  async findMany(table: string, where?: any): Promise<any[]> {
    // RLS automatically adds tenant_id filter
    const whereClause = where ? `WHERE ${Object.keys(where).map((key, index) => `${key} = $${index + 1}`).join(' AND ')}` : '';
    const query = `SELECT * FROM ${table} ${whereClause}`;
    const params = where ? Object.values(where) : [];
    
    const result = await this.executeQuery(query, params);
    return result;
  }

  async create(table: string, data: any): Promise<any> {
    // Automatically add tenant_id to all inserts
    const dataWithTenant = {
      ...data,
      tenant_id: this.currentTenantId
    };

    const columns = Object.keys(dataWithTenant);
    const values = Object.values(dataWithTenant);
    const placeholders = values.map((_, index) => `$${index + 1}`);

    const query = `
      INSERT INTO ${table} (${columns.join(', ')})
      VALUES (${placeholders.join(', ')})
      RETURNING *
    `;

    const result = await this.executeQuery(query, values);
    return result[0];
  }

  getCurrentTenantId(): string | null {
    return this.currentTenantId;
  }
}

export const tenantDb = new TenantDatabaseService();
