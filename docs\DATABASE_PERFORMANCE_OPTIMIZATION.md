# Database Performance Optimization Guide

## Overview

This document provides a comprehensive guide to the database performance optimization implemented for the CRM system. The optimization focuses on strategic indexing, query optimization, enhanced monitoring, and scalability improvements.

## Performance Issues Identified

### 1. Missing Critical Indexes
- Dashboard analytics queries performing multiple COUNT operations without optimized indexes
- Date range queries missing indexes on `created_at`, `updated_at` columns
- Status filtering without indexes on status columns across multiple tables
- Search operations missing text search indexes for name, email, description fields
- Foreign key lookups lacking supporting indexes

### 2. N+1 Query Problems
- Lead management loading related data with multiple separate queries
- Opportunity pipeline inefficiently loading stage history and related records
- Dashboard metrics using sequential queries instead of optimized aggregations
- User management performing separate queries for tenant relationships and permissions

### 3. Inefficient Query Patterns
- Dashboard analytics using multiple separate COUNT queries instead of single aggregation
- OFFSET-based pagination for large datasets
- Multiple WHERE conditions without composite indexes
- ORDER BY operations on non-indexed columns

### 4. Transaction and Lock Issues
- Some operations holding locks longer than necessary
- Potential deadlock scenarios in complex multi-table operations
- Basic connection pool configuration without optimization for concurrent users

## Optimization Implementation

### Phase 1: Index Optimization

#### Critical Performance Indexes
```sql
-- Tenant-scoped composite indexes for common query patterns
CREATE INDEX CONCURRENTLY idx_contacts_tenant_status_created ON contacts(tenant_id, status, created_at);
CREATE INDEX CONCURRENTLY idx_leads_tenant_status_created ON leads(tenant_id, status, created_at) WHERE deleted_at IS NULL;
CREATE INDEX CONCURRENTLY idx_opportunities_tenant_stage_created ON opportunities(tenant_id, stage_id, created_at);

-- Dashboard analytics optimization
CREATE INDEX CONCURRENTLY idx_leads_tenant_created_month ON leads(tenant_id, date_trunc('month', created_at)) WHERE deleted_at IS NULL;
CREATE INDEX CONCURRENTLY idx_opportunities_tenant_created_month ON opportunities(tenant_id, date_trunc('month', created_at));

-- Full-text search optimization with GIN indexes
CREATE INDEX CONCURRENTLY idx_contacts_search ON contacts USING GIN(to_tsvector('english', 
  coalesce(first_name, '') || ' ' || coalesce(last_name, '') || ' ' || coalesce(email, '')));
```

#### Partial Indexes for Filtered Queries
```sql
-- Active records only indexes
CREATE INDEX CONCURRENTLY idx_leads_active_tenant_owner ON leads(tenant_id, owner_id) 
WHERE deleted_at IS NULL AND status != 'converted';

-- Recent activity indexes (last 30 days)
CREATE INDEX CONCURRENTLY idx_activities_recent_tenant ON activities(tenant_id, created_at) 
WHERE created_at > NOW() - INTERVAL '30 days';
```

### Phase 2: Query Optimization

#### Optimized Dashboard Metrics
```typescript
// Single aggregation query instead of multiple COUNT queries
const dashboardMetrics = await prisma.$queryRaw`
  SELECT 
    COUNT(DISTINCT c.id) as total_contacts,
    COUNT(DISTINCT l.id) FILTER (WHERE l.deleted_at IS NULL) as total_leads,
    COUNT(DISTINCT o.id) as total_opportunities,
    COALESCE(SUM(o.value), 0) as total_opportunity_value,
    COALESCE(AVG(o.value), 0) as average_deal_size
  FROM tenants t
  LEFT JOIN contacts c ON t.id = c.tenant_id
  LEFT JOIN leads l ON t.id = l.tenant_id
  LEFT JOIN opportunities o ON t.id = o.tenant_id
  WHERE t.id = ${tenantId}
`;
```

#### Cursor-Based Pagination
```typescript
// Optimized pagination for large datasets
async function getPaginatedLeads(tenantId: string, cursor?: string, limit: number = 20) {
  const where = {
    tenantId,
    deletedAt: null,
    ...(cursor && { id: { gt: cursor } })
  };

  const leads = await prisma.lead.findMany({
    where,
    include: { owner: true, company: true, leadSource: true },
    orderBy: { id: 'asc' },
    take: limit + 1
  });

  const hasNextPage = leads.length > limit;
  const items = hasNextPage ? leads.slice(0, -1) : leads;
  const nextCursor = hasNextPage ? items[items.length - 1].id : null;

  return { items, nextCursor, hasNextPage };
}
```

#### Full-Text Search
```typescript
// GIN index-powered full-text search
const contacts = await prisma.$queryRaw`
  SELECT c.*, ts_rank(...) as rank
  FROM contacts c
  WHERE c.tenant_id = ${tenantId}
    AND to_tsvector('english', coalesce(c.first_name, '') || ' ' || coalesce(c.last_name, '')) 
        @@ plainto_tsquery('english', ${searchTerm})
  ORDER BY rank DESC
  LIMIT 20
`;
```

### Phase 3: Enhanced Monitoring

#### Database Performance Monitor
```typescript
// Track query performance with detailed metrics
const result = await EnhancedDatabasePerformanceMonitor.trackQuery(
  'dashboard_metrics',
  () => getDashboardMetrics(tenantId),
  { tenantId, operation: 'dashboard_load' }
);
```

#### Health Monitoring
```typescript
// Comprehensive database health check
const healthStatus = await dbHealthMonitor.getHealthStatus();
// Returns: connection status, active connections, database stats, slow queries, index usage
```

## Performance Improvements Expected

### Query Performance
- **Dashboard Load Time**: 80% reduction (from ~2s to ~400ms)
- **Lead List Pagination**: 90% improvement for large datasets
- **Search Operations**: 95% improvement with full-text search
- **Report Generation**: 70% faster with optimized aggregations

### System Scalability
- **Concurrent Users**: Support 10x more concurrent users
- **Data Volume**: Efficient handling of 100K+ records per tenant
- **Memory Usage**: 50% reduction in memory consumption
- **Connection Pool**: Optimized for high-concurrency scenarios

### Database Health
- **Index Usage**: 100% coverage for critical query patterns
- **Lock Contention**: Minimized through optimized transactions
- **Query Monitoring**: Real-time slow query detection and alerting
- **Resource Utilization**: Optimized CPU and memory usage

## Implementation Steps

### 1. Deploy Index Migration
```bash
# Apply the performance optimization indexes
npx prisma migrate deploy

# Or manually run the migration
psql -d crm_platform -f prisma/migrations/20241221_performance_optimization_indexes/migration.sql
```

### 2. Update Service Layer
```typescript
// Replace existing services with optimized versions
import { OptimizedDatabaseService } from '@/services/optimized-database-service';

const dbService = new OptimizedDatabaseService();
const metrics = await dbService.getDashboardMetrics(tenantId);
```

### 3. Enable Performance Monitoring
```typescript
// Add performance tracking to critical operations
import { EnhancedDatabasePerformanceMonitor } from '@/services/database-performance-monitor';

const result = await EnhancedDatabasePerformanceMonitor.trackQuery(
  'operation_name',
  () => yourDatabaseOperation(),
  { metadata: 'optional' }
);
```

### 4. Monitor and Validate
```bash
# Run performance tests
node scripts/test-database-performance.js

# Check database health via API
curl http://localhost:3000/api/admin/database/health

# Monitor query performance
curl http://localhost:3000/api/admin/database/performance
```

## Testing and Validation

### Performance Test Script
```bash
# Run comprehensive performance tests
node scripts/test-database-performance.js
```

The test script validates:
- Query performance improvements
- Pagination efficiency
- Search operation speed
- Aggregation performance
- Concurrent operation handling

### Expected Test Results
- **Setup**: Create test data for 5 tenants with 1000+ records each
- **Query Performance**: All queries under 100ms
- **Pagination**: Consistent performance across large datasets
- **Search**: Full-text search under 200ms
- **Concurrent Operations**: 10+ simultaneous users without degradation

## Monitoring and Alerting

### Performance Metrics API
```bash
# Get performance summary
GET /api/admin/database/performance

# Get detailed query statistics
GET /api/admin/database/performance?format=detailed

# Get specific query stats
GET /api/admin/database/performance?query=dashboard_metrics
```

### Health Check API
```bash
# Get database health status
GET /api/admin/database/health

# Trigger performance analysis
POST /api/admin/database/health
{
  "action": "analyze_performance"
}
```

### Slow Query Alerts
- Automatic detection of queries > 1000ms
- Console warnings in production
- Integration points for monitoring services (DataDog, New Relic)

## Rollback Procedures

### Emergency Rollback
```bash
# Remove all performance indexes if needed
psql -d crm_platform -f scripts/rollback-performance-indexes.sql
```

### Gradual Rollback
```sql
-- Remove specific problematic indexes
DROP INDEX CONCURRENTLY idx_specific_index_name;
```

## Best Practices

### Query Optimization
1. Always include `tenant_id` in WHERE clauses for multi-tenant queries
2. Use composite indexes for multi-column filtering
3. Prefer cursor-based pagination over OFFSET for large datasets
4. Use partial indexes for frequently filtered subsets
5. Leverage GIN indexes for full-text search

### Index Management
1. Monitor index usage with `pg_stat_user_indexes`
2. Remove unused indexes to save space
3. Use `CONCURRENTLY` option for index creation in production
4. Regular `VACUUM ANALYZE` to maintain index efficiency

### Performance Monitoring
1. Track all critical database operations
2. Set appropriate slow query thresholds
3. Monitor connection pool utilization
4. Regular health checks and performance analysis

## Troubleshooting

### Common Issues

#### Slow Index Creation
```sql
-- Check index creation progress
SELECT 
  pid, 
  now() - pg_stat_activity.query_start AS duration, 
  query 
FROM pg_stat_activity 
WHERE query LIKE '%CREATE INDEX%';
```

#### High Memory Usage
```sql
-- Check memory usage by queries
SELECT 
  query,
  calls,
  total_time,
  mean_time,
  rows
FROM pg_stat_statements 
ORDER BY mean_time DESC 
LIMIT 10;
```

#### Lock Contention
```sql
-- Check for blocking queries
SELECT 
  blocked_locks.pid AS blocked_pid,
  blocked_activity.usename AS blocked_user,
  blocking_locks.pid AS blocking_pid,
  blocking_activity.usename AS blocking_user,
  blocked_activity.query AS blocked_statement,
  blocking_activity.query AS current_statement_in_blocking_process
FROM pg_catalog.pg_locks blocked_locks
JOIN pg_catalog.pg_stat_activity blocked_activity ON blocked_activity.pid = blocked_locks.pid
JOIN pg_catalog.pg_locks blocking_locks ON blocking_locks.locktype = blocked_locks.locktype
JOIN pg_catalog.pg_stat_activity blocking_activity ON blocking_activity.pid = blocking_locks.pid
WHERE NOT blocked_locks.granted;
```

## Maintenance Schedule

### Daily
- Monitor slow query alerts
- Check database health status
- Review performance metrics

### Weekly
- Analyze query performance trends
- Review index usage statistics
- Check for unused indexes

### Monthly
- Run comprehensive performance tests
- Review and optimize slow queries
- Update performance baselines
- Plan capacity scaling if needed

## Support and Resources

### Documentation
- [Prisma Performance Guide](https://www.prisma.io/docs/guides/performance-and-optimization)
- [PostgreSQL Index Documentation](https://www.postgresql.org/docs/current/indexes.html)
- [Database Performance Monitoring](./DATABASE_MONITORING.md)

### Monitoring Tools
- Built-in performance monitoring APIs
- Database health check endpoints
- Query performance tracking
- Slow query alerting system

### Contact
For questions or issues related to database performance optimization, please refer to the development team or create an issue in the project repository.
