import { prisma } from '@/lib/prisma';
import { UnifiedNotificationService } from './unified-notification-service';

export interface QueryMetric {
  duration: number;
  memoryUsed: number;
  success: boolean;
  error?: string;
  timestamp: Date;
  metadata?: Record<string, any>;
}

export interface QueryStats {
  count: number;
  avgDuration: number;
  maxDuration: number;
  minDuration: number;
  errorRate: number;
  recentSlowQueries?: Array<{
    duration: number;
    timestamp: Date;
    metadata?: Record<string, any>;
  }>;
}

export interface DatabaseHealth {
  status: 'healthy' | 'unhealthy' | 'error';
  responseTime: number;
  activeConnections?: number;
  databaseStats?: DatabaseStats;
  slowQueries?: SlowQuery[];
  indexStats?: IndexUsage[];
  error?: string;
  timestamp: Date;
}

export interface DatabaseStats {
  totalSize: string;
  largestTables: Array<{
    name: string;
    rowCount: number;
    size: string;
  }>;
}

export interface SlowQuery {
  query: string;
  calls: number;
  totalTime: number;
  meanTime: number;
}

export interface IndexUsage {
  tableName: string;
  indexName: string;
  scans: number;
  size: string;
}

/**
 * Enhanced Database Performance Monitor
 * Provides comprehensive database performance tracking and health monitoring
 */
export class EnhancedDatabasePerformanceMonitor {
  private static queryMetrics: Map<string, QueryMetric[]> = new Map();
  private static slowQueryThreshold = parseInt(process.env.SLOW_QUERY_THRESHOLD_MS || '1000');
  private static maxMetricsPerQuery = 1000;
  private static notificationService = new UnifiedNotificationService();

  /**
   * Track query performance with detailed metrics
   */
  static async trackQuery<T>(
    queryName: string,
    query: () => Promise<T>,
    metadata?: Record<string, any>
  ): Promise<T> {
    const startTime = Date.now();
    const startMemory = process.memoryUsage().heapUsed;

    try {
      const result = await query();
      const duration = Date.now() - startTime;
      const memoryUsed = process.memoryUsage().heapUsed - startMemory;

      this.recordQueryMetric(queryName, {
        duration,
        memoryUsed,
        success: true,
        timestamp: new Date(),
        metadata
      });

      // Alert on slow queries
      if (duration > this.slowQueryThreshold) {
        await this.alertSlowQuery(queryName, duration, metadata);
      }

      return result;
    } catch (error) {
      const duration = Date.now() - startTime;
      this.recordQueryMetric(queryName, {
        duration,
        memoryUsed: 0,
        success: false,
        error: error instanceof Error ? error.message : String(error),
        timestamp: new Date(),
        metadata
      });
      throw error;
    }
  }

  /**
   * Record query metric
   */
  private static recordQueryMetric(queryName: string, metric: QueryMetric): void {
    const metrics = this.queryMetrics.get(queryName) || [];
    metrics.push(metric);
    
    // Keep only last N metrics per query to prevent memory leaks
    if (metrics.length > this.maxMetricsPerQuery) {
      metrics.shift();
    }
    
    this.queryMetrics.set(queryName, metrics);
  }

  /**
   * Get query statistics
   */
  static getQueryStats(queryName?: string): QueryStats | Record<string, QueryStats> {
    if (queryName) {
      const metrics = this.queryMetrics.get(queryName) || [];
      return this.calculateStats(metrics);
    }

    const allStats: Record<string, QueryStats> = {};
    for (const [name, metrics] of this.queryMetrics.entries()) {
      allStats[name] = this.calculateStats(metrics);
    }
    return allStats;
  }

  /**
   * Calculate statistics from metrics
   */
  private static calculateStats(metrics: QueryMetric[]): QueryStats {
    if (metrics.length === 0) {
      return { count: 0, avgDuration: 0, maxDuration: 0, minDuration: 0, errorRate: 0 };
    }

    const durations = metrics.map(m => m.duration);
    const errors = metrics.filter(m => !m.success).length;

    return {
      count: metrics.length,
      avgDuration: durations.reduce((a, b) => a + b, 0) / durations.length,
      maxDuration: Math.max(...durations),
      minDuration: Math.min(...durations),
      errorRate: (errors / metrics.length) * 100,
      recentSlowQueries: metrics
        .filter(m => m.duration > this.slowQueryThreshold)
        .slice(-10)
        .map(m => ({
          duration: m.duration,
          timestamp: m.timestamp,
          metadata: m.metadata
        }))
    };
  }

  /**
   * Alert on slow queries
   */
  private static async alertSlowQuery(
    queryName: string,
    duration: number,
    metadata?: Record<string, any>
  ): Promise<void> {
    if (process.env.NODE_ENV === 'production') {
      console.warn(`🐌 Slow query detected: ${queryName} took ${duration}ms`, {
        queryName,
        duration,
        metadata,
        timestamp: new Date().toISOString()
      });

      // Send notification for critical slow queries (> 5 seconds)
      if (duration > 5000) {
        await this.sendSlowQueryAlert(queryName, duration, metadata);
      }
    }
  }

  /**
   * Send slow query alert notification
   */
  private static async sendSlowQueryAlert(
    queryName: string,
    duration: number,
    metadata?: Record<string, any>
  ): Promise<void> {
    try {
      // Get all tenants to send alerts (since this is a system-wide issue)
      const tenants = await prisma.tenant.findMany({
        select: { id: true }
      });

      for (const tenant of tenants) {
        // Get administrators for each tenant
        const admins = await prisma.tenantUser.findMany({
          where: {
            tenantId: tenant.id,
            OR: [
              { role: { in: ['Admin', 'System Administrator', 'DBA'] } },
              { isTenantAdmin: true }
            ],
            status: 'active'
          },
          select: { userId: true }
        });

        for (const admin of admins) {
          await this.notificationService.createNotification({
            tenantId: tenant.id,
            userId: admin.userId,
            type: 'database_performance_alert',
            category: 'system',
            title: 'Critical Database Performance Alert',
            message: `Slow query detected: "${queryName}" took ${duration}ms to execute`,
            data: {
              queryName,
              duration,
              threshold: this.slowQueryThreshold,
              severity: duration > 10000 ? 'critical' : 'warning',
              metadata,
              timestamp: new Date().toISOString(),
              recommendations: [
                'Review query execution plan',
                'Check database indexes',
                'Consider query optimization',
                'Monitor database resources'
              ]
            },
            actionUrl: '/admin/monitoring/database',
            actionLabel: 'View Database Monitoring'
          });
        }
      }
    } catch (error) {
      console.error('Failed to send slow query alert:', error);
    }
  }

  /**
   * Clear metrics for a specific query or all queries
   */
  static clearMetrics(queryName?: string): void {
    if (queryName) {
      this.queryMetrics.delete(queryName);
    } else {
      this.queryMetrics.clear();
    }
  }

  /**
   * Get performance summary
   */
  static getPerformanceSummary(): {
    totalQueries: number;
    slowQueries: number;
    averageResponseTime: number;
    errorRate: number;
    topSlowQueries: Array<{ name: string; avgDuration: number; count: number }>;
  } {
    const allStats = this.getQueryStats() as Record<string, QueryStats>;
    const queryNames = Object.keys(allStats);
    
    if (queryNames.length === 0) {
      return {
        totalQueries: 0,
        slowQueries: 0,
        averageResponseTime: 0,
        errorRate: 0,
        topSlowQueries: []
      };
    }

    const totalQueries = queryNames.reduce((sum, name) => sum + allStats[name].count, 0);
    const slowQueries = queryNames.reduce((sum, name) => 
      sum + (allStats[name].recentSlowQueries?.length || 0), 0);
    
    const totalDuration = queryNames.reduce((sum, name) => 
      sum + (allStats[name].avgDuration * allStats[name].count), 0);
    const averageResponseTime = totalDuration / totalQueries;

    const totalErrors = queryNames.reduce((sum, name) => 
      sum + (allStats[name].count * allStats[name].errorRate / 100), 0);
    const errorRate = (totalErrors / totalQueries) * 100;

    const topSlowQueries = queryNames
      .map(name => ({
        name,
        avgDuration: allStats[name].avgDuration,
        count: allStats[name].count
      }))
      .sort((a, b) => b.avgDuration - a.avgDuration)
      .slice(0, 10);

    return {
      totalQueries,
      slowQueries,
      averageResponseTime,
      errorRate,
      topSlowQueries
    };
  }
}

/**
 * Database Health Monitor
 * Provides comprehensive database health monitoring
 */
export class DatabaseHealthMonitor {
  /**
   * Get comprehensive database health status
   */
  async getHealthStatus(): Promise<DatabaseHealth> {
    const startTime = Date.now();

    try {
      // Test basic connectivity
      const connectionTest = await this.testConnection();
      
      // Get active connections
      const activeConnections = await this.getActiveConnections();
      
      // Get database size and growth
      const databaseStats = await this.getDatabaseStats();
      
      // Get slow queries (if pg_stat_statements is available)
      const slowQueries = await this.getSlowQueries();
      
      // Get index usage statistics
      const indexStats = await this.getIndexUsageStats();

      const responseTime = Date.now() - startTime;

      return {
        status: connectionTest ? 'healthy' : 'unhealthy',
        responseTime,
        activeConnections,
        databaseStats,
        slowQueries,
        indexStats,
        timestamp: new Date()
      };
    } catch (error) {
      return {
        status: 'error',
        responseTime: Date.now() - startTime,
        error: error instanceof Error ? error.message : String(error),
        timestamp: new Date()
      };
    }
  }

  /**
   * Test database connection
   */
  private async testConnection(): Promise<boolean> {
    try {
      await prisma.$queryRaw`SELECT 1`;
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Get active database connections
   */
  private async getActiveConnections(): Promise<number> {
    try {
      const result = await prisma.$queryRaw<[{ count: bigint }]>`
        SELECT count(*) as count 
        FROM pg_stat_activity 
        WHERE state = 'active' AND datname = current_database()
      `;
      return Number(result[0].count);
    } catch {
      return 0;
    }
  }

  /**
   * Get database statistics
   */
  private async getDatabaseStats(): Promise<DatabaseStats> {
    try {
      const sizeResult = await prisma.$queryRaw<[{ size: string }]>`
        SELECT pg_size_pretty(pg_database_size(current_database())) as size
      `;

      const tableStats = await prisma.$queryRaw<Array<{
        table_name: string;
        row_count: bigint;
        size: string;
      }>>`
        SELECT 
          schemaname||'.'||tablename as table_name,
          n_tup_ins + n_tup_upd + n_tup_del as row_count,
          pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
        FROM pg_stat_user_tables 
        ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC 
        LIMIT 10
      `;

      return {
        totalSize: sizeResult[0].size,
        largestTables: tableStats.map(t => ({
          name: t.table_name,
          rowCount: Number(t.row_count),
          size: t.size
        }))
      };
    } catch (error) {
      return {
        totalSize: 'Unknown',
        largestTables: []
      };
    }
  }

  /**
   * Get slow queries (requires pg_stat_statements extension)
   */
  private async getSlowQueries(): Promise<SlowQuery[]> {
    try {
      const slowQueries = await prisma.$queryRaw<Array<{
        query: string;
        calls: bigint;
        total_time: number;
        mean_time: number;
      }>>`
        SELECT 
          query,
          calls,
          total_time,
          mean_time
        FROM pg_stat_statements 
        WHERE mean_time > 1000 
        ORDER BY mean_time DESC 
        LIMIT 10
      `;

      return slowQueries.map(q => ({
        query: q.query.substring(0, 200) + (q.query.length > 200 ? '...' : ''),
        calls: Number(q.calls),
        totalTime: q.total_time,
        meanTime: q.mean_time
      }));
    } catch {
      // pg_stat_statements not available
      return [];
    }
  }

  /**
   * Get index usage statistics
   */
  private async getIndexUsageStats(): Promise<IndexUsage[]> {
    try {
      const indexStats = await prisma.$queryRaw<Array<{
        table_name: string;
        index_name: string;
        index_scans: bigint;
        index_size: string;
      }>>`
        SELECT 
          schemaname||'.'||tablename as table_name,
          indexname as index_name,
          idx_scan as index_scans,
          pg_size_pretty(pg_relation_size(schemaname||'.'||indexname)) as index_size
        FROM pg_stat_user_indexes 
        ORDER BY idx_scan DESC 
        LIMIT 20
      `;

      return indexStats.map(i => ({
        tableName: i.table_name,
        indexName: i.index_name,
        scans: Number(i.index_scans),
        size: i.index_size
      }));
    } catch {
      return [];
    }
  }

  /**
   * Get database performance recommendations
   */
  async getPerformanceRecommendations(): Promise<string[]> {
    const recommendations: string[] = [];
    
    try {
      // Check for unused indexes
      const unusedIndexes = await prisma.$queryRaw<Array<{
        index_name: string;
        table_name: string;
        index_size: string;
      }>>`
        SELECT 
          indexname as index_name,
          tablename as table_name,
          pg_size_pretty(pg_relation_size(indexname)) as index_size
        FROM pg_stat_user_indexes 
        WHERE idx_scan = 0 
        AND schemaname = 'public'
        ORDER BY pg_relation_size(indexname) DESC
        LIMIT 5
      `;

      if (unusedIndexes.length > 0) {
        recommendations.push(`Consider removing ${unusedIndexes.length} unused indexes to save space`);
      }

      // Check for tables without primary keys
      const tablesWithoutPK = await prisma.$queryRaw<Array<{
        table_name: string;
      }>>`
        SELECT t.table_name
        FROM information_schema.tables t
        LEFT JOIN information_schema.table_constraints tc 
          ON t.table_name = tc.table_name 
          AND tc.constraint_type = 'PRIMARY KEY'
        WHERE t.table_schema = 'public' 
        AND t.table_type = 'BASE TABLE'
        AND tc.constraint_name IS NULL
      `;

      if (tablesWithoutPK.length > 0) {
        recommendations.push(`${tablesWithoutPK.length} tables are missing primary keys`);
      }

      // Check for large tables that might need partitioning
      const largeTables = await prisma.$queryRaw<Array<{
        table_name: string;
        size_mb: number;
      }>>`
        SELECT 
          tablename as table_name,
          pg_total_relation_size(schemaname||'.'||tablename) / 1024 / 1024 as size_mb
        FROM pg_stat_user_tables 
        WHERE pg_total_relation_size(schemaname||'.'||tablename) > ********** -- 1GB
        ORDER BY size_mb DESC
        LIMIT 5
      `;

      if (largeTables.length > 0) {
        recommendations.push(`${largeTables.length} tables are larger than 1GB and might benefit from partitioning`);
      }

    } catch (error) {
      recommendations.push('Unable to analyze database for recommendations');
    }

    return recommendations;
  }
}

// Export singleton instances
export const enhancedDbMonitor = new EnhancedDatabasePerformanceMonitor();
export const dbHealthMonitor = new DatabaseHealthMonitor();
