import fs from 'fs/promises';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';

const UPLOAD_DIR = process.env.UPLOAD_DIR || './uploads';

export class FileStorageService {
  /**
   * Generate secure file path
   */
  static generateFilePath(originalName: string, tenantId: string): string {
    const ext = path.extname(originalName);
    const filename = `${uuidv4()}${ext}`;
    return path.join(tenantId, 'documents', filename);
  }

  /**
   * Ensure upload directory exists
   */
  static async ensureUploadDir(filePath: string): Promise<void> {
    const dir = path.dirname(path.join(UPLOAD_DIR, filePath));
    await fs.mkdir(dir, { recursive: true });
  }

  /**
   * Save file to disk
   */
  static async saveFile(file: File, filePath: string): Promise<void> {
    const fullPath = path.join(UPLOAD_DIR, filePath);
    await this.ensureUploadDir(filePath);
    
    const buffer = Buffer.from(await file.arrayBuffer());
    await fs.writeFile(fullPath, buffer);
  }

  /**
   * Read file from disk
   */
  static async readFile(filePath: string): Promise<Buffer> {
    const fullPath = path.join(UPLOAD_DIR, filePath);
    return await fs.readFile(fullPath);
  }

  /**
   * Delete file from disk
   */
  static async deleteFile(filePath: string): Promise<void> {
    const fullPath = path.join(UPLOAD_DIR, filePath);
    await fs.unlink(fullPath);
  }

  /**
   * Check if file exists
   */
  static async fileExists(filePath: string): Promise<boolean> {
    try {
      const fullPath = path.join(UPLOAD_DIR, filePath);
      await fs.access(fullPath);
      return true;
    } catch {
      return false;
    }
  }
}
