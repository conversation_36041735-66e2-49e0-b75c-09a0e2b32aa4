import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import path from 'path';
import fs from 'fs/promises';
import { v4 as uuidv4 } from 'uuid';

const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB
const ALLOWED_TYPES = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
const UPLOAD_DIR = process.env.UPLOAD_DIR || './uploads';

// Ensure upload directory exists
async function ensureUploadDir() {
  try {
    await fs.mkdir(UPLOAD_DIR, { recursive: true });
  } catch (error) {
    console.warn('Upload directory creation warning:', error);
  }
}

// Upload avatar
export async function POST(request: Request) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const formData = await request.formData();
    const file = formData.get('avatar') as File;

    if (!file) {
      return NextResponse.json(
        { error: 'No file provided' },
        { status: 400 }
      );
    }

    // Validate file type
    if (!ALLOWED_TYPES.includes(file.type)) {
      return NextResponse.json(
        { error: 'Invalid file type. Only JPEG, PNG, and WebP images are allowed.' },
        { status: 400 }
      );
    }

    // Validate file size
    if (file.size > MAX_FILE_SIZE) {
      return NextResponse.json(
        { error: 'File too large. Maximum size is 5MB.' },
        { status: 400 }
      );
    }

    // Get current tenant for file organization
    const currentTenant = session.user.tenants?.[0];
    if (!currentTenant) {
      return NextResponse.json(
        { error: 'No tenant found' },
        { status: 400 }
      );
    }

    // Generate file path for avatar
    const ext = path.extname(file.name) || '.jpg';
    const filename = `avatar-${session.user.id}-${uuidv4()}${ext}`;
    const filePath = `${currentTenant.id}/avatars/${filename}`;

    try {
      console.log('Saving avatar file:', { filePath, fileSize: file.size, fileType: file.type });

      // Ensure upload directory exists
      await ensureUploadDir();

      // Save file to disk
      const fullPath = path.join(UPLOAD_DIR, filePath);
      const dir = path.dirname(fullPath);
      await fs.mkdir(dir, { recursive: true });

      const buffer = Buffer.from(await file.arrayBuffer());
      await fs.writeFile(fullPath, buffer);

      console.log('File saved successfully to:', fullPath);

      // Get current user to check for existing avatar
      const currentUser = await prisma.user.findUnique({
        where: { id: session.user.id },
        select: { avatarUrl: true }
      });

      // Update user avatar URL
      const avatarUrl = `/api/files/${filePath}`;
      const updatedUser = await prisma.user.update({
        where: { id: session.user.id },
        data: {
          avatarUrl,
          updatedAt: new Date(),
        },
        select: {
          id: true,
          avatarUrl: true,
          firstName: true,
          lastName: true,
        }
      });

      // Delete old avatar file if it exists
      if (currentUser?.avatarUrl && currentUser.avatarUrl.startsWith('/api/files/')) {
        const oldFilePath = currentUser.avatarUrl.replace('/api/files/', '');
        try {
          const oldFullPath = path.join(UPLOAD_DIR, oldFilePath);
          await fs.unlink(oldFullPath);
          console.log('Old avatar deleted:', oldFullPath);
        } catch (error) {
          console.warn('Failed to delete old avatar file:', error);
        }
      }

      return NextResponse.json({
        success: true,
        data: {
          avatarUrl: updatedUser.avatarUrl,
          user: updatedUser,
        },
        message: 'Avatar uploaded successfully'
      });

    } catch (fileError) {
      console.error('File storage error:', fileError);
      return NextResponse.json(
        { error: 'Failed to save avatar file' },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('Avatar upload error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Delete avatar
export async function DELETE() {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get current user avatar
    const currentUser = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: { avatarUrl: true }
    });

    if (!currentUser?.avatarUrl) {
      return NextResponse.json(
        { error: 'No avatar to delete' },
        { status: 400 }
      );
    }

    // Update user to remove avatar
    await prisma.user.update({
      where: { id: session.user.id },
      data: {
        avatarUrl: null,
        updatedAt: new Date(),
      }
    });

    // Delete avatar file if it's stored locally
    if (currentUser.avatarUrl.startsWith('/api/files/')) {
      const filePath = currentUser.avatarUrl.replace('/api/files/', '');
      try {
        const fullPath = path.join(UPLOAD_DIR, filePath);
        await fs.unlink(fullPath);
        console.log('Avatar file deleted:', fullPath);
      } catch (error) {
        console.warn('Failed to delete avatar file:', error);
      }
    }

    return NextResponse.json({
      success: true,
      message: 'Avatar deleted successfully'
    });

  } catch (error) {
    console.error('Avatar delete error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
