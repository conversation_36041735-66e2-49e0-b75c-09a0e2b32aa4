import { NextResponse } from 'next/server';
import { z } from 'zod';
import { withPermission, AuthenticatedRequest } from '@/lib/permission-middleware';
import { scheduledCallService } from '@/services/scheduled-call-service';
import { PermissionAction, PermissionResource } from '@/types';

const completeCallSchema = z.object({
  callNotes: z.string().optional(),
  followUpRequired: z.boolean().default(false),
  nextCallDate: z.string().datetime().optional(),
});

/**
 * POST /api/calls/[callId]/complete
 * Mark a call as completed
 */
export const POST = withPermission({
  resource: PermissionResource.CALLS,
  action: PermissionAction.UPDATE,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest) => {
  try {
    const url = new URL(req.url);
    const pathSegments = url.pathname.split('/');
    const callId = pathSegments[pathSegments.indexOf('calls') + 1];
    
    const body = await req.json();
    const validatedData = completeCallSchema.parse(body);

    const scheduledCall = await scheduledCallService.completeCall(
      callId,
      req.tenantId,
      validatedData.callNotes,
      validatedData.followUpRequired,
      validatedData.nextCallDate
    );

    return NextResponse.json({
      success: true,
      data: { scheduledCall },
      message: 'Call marked as completed successfully'
    });

  } catch (error) {
    console.error('Complete call error:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          error: 'Validation failed',
          details: error.errors.map(e => ({
            field: e.path.join('.'),
            message: e.message
          }))
        },
        { status: 400 }
      );
    }

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
});
