import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { projectCreationService } from '@/services/project-creation';
import { z } from 'zod';

// Request validation schemas
const finalizeHandoverSchema = z.object({
  projectName: z.string().min(1, 'Project name is required').max(255).optional(),
  projectDescription: z.string().optional(),
  startDate: z.string().datetime().optional().transform(val => val ? new Date(val) : undefined),
  endDate: z.string().datetime().optional().transform(val => val ? new Date(val) : undefined),
  budget: z.number().positive().optional(),
  managerId: z.string().optional(),
  clientPortalEnabled: z.boolean().default(true),
  customFields: z.record(z.any()).default({}),
});

interface RouteParams {
  params: {
    id: string;
  };
}

/**
 * POST /api/handovers/[id]/finalize - Complete handover and create project
 */
export async function POST(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get tenantId from user's tenants array (users have one tenant in this system)
    let tenantId: string | null = null;
    if (session.user.tenants && session.user.tenants.length > 0) {
      tenantId = session.user.tenants[0].id;
    }

    if (!tenantId) {
      return NextResponse.json({ error: 'Tenant not found' }, { status: 400 });
    }

    const body = await request.json();
    const validatedData = finalizeHandoverSchema.parse(body);

    const resolvedParams = await params;

    // Check if a project already exists for this opportunity
    const existingProject = await projectCreationService.getProjectByOpportunity(tenantId, resolvedParams.id);

    const project = await projectCreationService.createProjectFromHandover(
      tenantId,
      resolvedParams.id,
      {
        name: validatedData.projectName,
        description: validatedData.projectDescription,
        startDate: validatedData.startDate,
        endDate: validatedData.endDate,
        budget: validatedData.budget,
        managerId: validatedData.managerId,
        clientPortalEnabled: validatedData.clientPortalEnabled,
        customFields: validatedData.customFields,
      },
      session.user.id
    );

    // Determine if this was a new project or existing project
    const isNewProject = !existingProject;
    const message = isNewProject
      ? 'Handover completed and project created successfully'
      : 'Handover completed and linked to existing project';

    return NextResponse.json({
      message,
      project,
      isNewProject
    }, { status: isNewProject ? 201 : 200 });
  } catch (error) {
    console.error('Error finalizing handover:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request data', details: error.errors },
        { status: 400 }
      );
    }

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to finalize handover' },
      { status: 500 }
    );
  }
}
