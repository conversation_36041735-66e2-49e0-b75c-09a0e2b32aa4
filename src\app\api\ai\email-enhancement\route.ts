import { NextResponse } from 'next/server';
import { z } from 'zod';
import { withPermission, AuthenticatedRequest } from '@/lib/permission-middleware';
import { PermissionAction, PermissionResource } from '@/types';
import { aiService } from '@/services/ai-service';
import type { AIRequest } from '@/services/ai-service';

const emailEnhancementSchema = z.object({
  content: z.string().min(1, 'Content is required'),
  subject: z.string().optional(),
  emailType: z.enum([
    'cold_outreach',
    'follow_up', 
    'meeting_request',
    'proposal_follow_up',
    'thank_you',
    'nurturing',
    're_engagement',
    'introduction'
  ]).optional(),
  tone: z.enum(['professional', 'friendly', 'formal', 'casual']).optional(),
  recipientName: z.string().optional(),
  recipientCompany: z.string().optional(),
  isMeetingInvitation: z.boolean().optional()
});

/**
 * POST /api/ai/email-enhancement
 * Enhance existing email content using AI
 */
export const POST = withPermission({
  resource: PermissionResource.CONTACTS,
  action: PermissionAction.CREATE,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest) => {
  try {
    const body = await req.json();
    const validatedData = emailEnhancementSchema.parse(body);

    if (!aiService) {
      return NextResponse.json(
        { error: 'AI service is not configured' },
        { status: 503 }
      );
    }

    const {
      content,
      subject,
      emailType = 'follow_up',
      tone = 'professional',
      recipientName = '',
      recipientCompany = '',
      isMeetingInvitation = false
    } = validatedData;

    // Create enhancement prompt similar to existing email improvement service
    const enhancementPrompt = `Improve this email based on the following goals:
- Make it more professional and engaging
- Improve clarity and readability
- Ensure proper formatting and structure
- Maintain all important details and information
${isMeetingInvitation ? '- Ensure meeting details are clearly presented' : ''}

Email Type: ${emailType.replace('_', ' ')}
Tone: ${tone}
${recipientName ? `Recipient: ${recipientName}${recipientCompany ? ` from ${recipientCompany}` : ''}` : ''}

Original Email:
${content}

${subject ? `Original Subject: ${subject}` : ''}

Provide an improved version with explanation of improvements made.

Response format:
{
  "enhancedContent": "improved email content",
  "enhancedSubject": "${subject ? 'improved subject if provided' : ''}",
  "improvements": ["list of key improvements made"]
}`;

    const aiRequest: AIRequest = {
      prompt: enhancementPrompt,
      systemPrompt: 'You are an expert email copywriter who improves email content to be more professional, engaging, and effective. Always respond with valid JSON containing the enhanced content and improvements made.',
      context: {
        originalContent: content,
        originalSubject: subject,
        emailType,
        tone,
        recipientName,
        recipientCompany,
        isMeetingInvitation
      },
      tenantId: req.tenantId,
      userId: req.userId,
      feature: 'email_enhancement'
    };

    const response = await aiService.generateResponse(aiRequest, {
      temperature: 0.5, // Lower temperature for more consistent JSON output
      maxTokens: 1500
    });

    const responseContent = response.content;
    if (!responseContent) {
      throw new Error('No response from AI service');
    }

    // Debug logging
    console.log('AI Enhancement - Raw response:', responseContent.substring(0, 500) + '...');

    let enhancementResult;
    try {
      // Use the same JSON extraction method as the existing email service
      const jsonMatch = responseContent.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No JSON found in AI response');
      }

      enhancementResult = JSON.parse(jsonMatch[0]);

      // Validate and provide defaults for required fields
      enhancementResult = {
        enhancedContent: enhancementResult.enhancedContent || content,
        enhancedSubject: enhancementResult.enhancedSubject || subject || '',
        improvements: Array.isArray(enhancementResult.improvements) ? enhancementResult.improvements : ['Content enhanced']
      };

    } catch (parseError) {
      console.error('Failed to parse AI response:', responseContent);
      console.error('Parse error:', parseError);

      // Fallback: Try to use the raw response as enhanced content if it looks reasonable
      if (responseContent && responseContent.length > content.length * 0.8) {
        // If the response is substantial, use it as enhanced content
        enhancementResult = {
          enhancedContent: responseContent.trim(),
          enhancedSubject: subject || '',
          improvements: ['Content enhanced (raw AI response)']
        };
      } else {
        // Last resort: return original content
        enhancementResult = {
          enhancedContent: content,
          enhancedSubject: subject || '',
          improvements: ['AI enhancement temporarily unavailable - content returned as-is']
        };
      }
    }

    return NextResponse.json({
      success: true,
      data: {
        enhancedContent: enhancementResult.enhancedContent,
        enhancedSubject: enhancementResult.enhancedSubject,
        improvements: enhancementResult.improvements || [],
        originalContent: content,
        originalSubject: subject
      },
      message: 'Email content enhanced successfully'
    });

  } catch (error) {
    console.error('Email enhancement error:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          error: 'Invalid request data',
          details: error.errors
        },
        { status: 400 }
      );
    }

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
});
