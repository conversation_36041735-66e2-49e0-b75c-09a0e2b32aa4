"use client"

import { usePathname } from 'next/navigation'
import { useUIStore } from '@/stores/ui-store'
import { useEffect } from 'react'

export function useCurrentPage() {
  const pathname = usePathname()
  const { currentPage, setCurrentPage } = useUIStore()

  useEffect(() => {
    setCurrentPage(pathname)
  }, [pathname, setCurrentPage])

  return {
    currentPage: currentPage || pathname,
    pathname
  }
}
