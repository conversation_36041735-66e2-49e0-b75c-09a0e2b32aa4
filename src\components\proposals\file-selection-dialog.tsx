'use client';

import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  FileText, 
  File, 
  CheckCircle, 
  AlertCircle, 
  Upload,
  Search,
  Filter
} from 'lucide-react';
import { Input } from '@/components/ui/input';
import { FileSelectionData } from '@/types/proposal';
import { toast } from 'sonner';

interface FileSelectionDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  proposalId?: string;
  opportunityId?: string;
  onFilesSelected: (fileIds: string[]) => void;
}

export function FileSelectionDialog({
  open,
  onOpenChange,
  proposalId,
  opportunityId,
  onFilesSelected
}: FileSelectionDialogProps) {
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [files, setFiles] = useState<{
    supportedFiles: FileSelectionData[];
    unsupportedFiles: FileSelectionData[];
    selectedCount: number;
    totalFiles: number;
  }>({
    supportedFiles: [],
    unsupportedFiles: [],
    selectedCount: 0,
    totalFiles: 0
  });
  const [selectedFileIds, setSelectedFileIds] = useState<string[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [showUnsupported, setShowUnsupported] = useState(false);

  // Load files when dialog opens
  useEffect(() => {
    if (open && (proposalId || opportunityId)) {
      loadFiles();
    }
  }, [open, proposalId, opportunityId]);

  const loadFiles = async () => {
    setLoading(true);
    try {
      let response;

      if (proposalId) {
        // Load files from existing proposal
        response = await fetch(`/api/proposals/${proposalId}/files`);
      } else if (opportunityId) {
        // Load files directly from opportunity
        response = await fetch(`/api/opportunities/${opportunityId}/documents`);
      } else {
        throw new Error('Either proposalId or opportunityId must be provided');
      }

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to load files');
      }

      if (proposalId) {
        // Handle proposal files response
        setFiles(result.data);

        // Set initially selected files
        const initiallySelected = result.data.supportedFiles
          .filter((file: FileSelectionData) => file.isSelected)
          .map((file: FileSelectionData) => file.documentId);

        setSelectedFileIds(initiallySelected);
      } else {
        // Handle opportunity documents response
        const documents = result.data || result;

        // Filter by supported file types for AI processing
        const supportedMimeTypes = [
          'application/pdf',
          'application/msword',
          'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
          'text/plain',
          'text/markdown',
          'application/vnd.ms-excel',
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        ];

        const supportedFiles = documents
          .filter((doc: any) => supportedMimeTypes.includes(doc.mimeType || ''))
          .map((doc: any) => ({
            documentId: doc.id,
            name: doc.name,
            filePath: doc.filePath,
            mimeType: doc.mimeType || 'application/octet-stream',
            fileSize: doc.fileSize || 0,
            isSelected: false,
            geminiFileUri: undefined
          }));

        const unsupportedFiles = documents
          .filter((doc: any) => !supportedMimeTypes.includes(doc.mimeType || ''))
          .map((doc: any) => ({
            documentId: doc.id,
            name: doc.name,
            filePath: doc.filePath,
            mimeType: doc.mimeType || 'application/octet-stream',
            fileSize: doc.fileSize || 0,
            isSelected: false,
            geminiFileUri: undefined
          }));

        setFiles({
          supportedFiles,
          unsupportedFiles,
          selectedCount: 0,
          totalFiles: documents.length
        });

        setSelectedFileIds([]);
      }

    } catch (error) {
      console.error('Error loading files:', error);
      toast.error('Failed to load files');
    } finally {
      setLoading(false);
    }
  };

  const handleFileToggle = (fileId: string, checked: boolean) => {
    setSelectedFileIds(prev => {
      if (checked) {
        return [...prev, fileId];
      } else {
        return prev.filter(id => id !== fileId);
      }
    });
  };

  const handleSelectAll = () => {
    const allSupportedIds = filteredSupportedFiles.map(file => file.documentId);
    setSelectedFileIds(allSupportedIds);
  };

  const handleDeselectAll = () => {
    setSelectedFileIds([]);
  };

  const handleSave = async () => {
    if (selectedFileIds.length === 0) {
      toast.error('Please select at least one file');
      return;
    }

    setSaving(true);
    try {
      if (proposalId) {
        // Save to existing proposal
        const response = await fetch(`/api/proposals/${proposalId}/select-files`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            selectedFileIds,
            uploadToGemini: true
          }),
        });

        const result = await response.json();

        if (!response.ok) {
          throw new Error(result.error || 'Failed to save file selection');
        }

        toast.success('File selection saved successfully');
      } else {
        // For new proposals, just pass the selected files to the parent
        toast.success('Files selected successfully');
      }

      onFilesSelected(selectedFileIds);
      onOpenChange(false);

    } catch (error) {
      console.error('Error saving file selection:', error);
      toast.error('Failed to save file selection');
    } finally {
      setSaving(false);
    }
  };

  // Filter files based on search query
  const filteredSupportedFiles = files.supportedFiles.filter(file =>
    file.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const filteredUnsupportedFiles = files.unsupportedFiles.filter(file =>
    file.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const getFileIcon = (mimeType: string) => {
    if (mimeType.includes('pdf')) return <FileText className="h-4 w-4 text-red-500" />;
    if (mimeType.includes('word')) return <FileText className="h-4 w-4 text-blue-500" />;
    if (mimeType.includes('text')) return <FileText className="h-4 w-4 text-gray-500" />;
    return <File className="h-4 w-4 text-gray-400" />;
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] flex flex-col">
        <DialogHeader className="flex-shrink-0 pb-6">
          <DialogTitle className="text-xl font-semibold">Select RFP Files</DialogTitle>
          <DialogDescription className="text-sm text-muted-foreground">
            Choose files for AI proposal generation context
          </DialogDescription>
        </DialogHeader>

        <div className="flex-1 overflow-hidden flex flex-col space-y-6">
          {/* Search and Filter Controls */}
          <div className="flex-shrink-0 flex items-center space-x-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search files..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 h-10"
              />
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowUnsupported(!showUnsupported)}
              className="flex-shrink-0"
            >
              <Filter className="h-4 w-4 mr-2" />
              {showUnsupported ? 'Hide' : 'Show'} Unsupported
            </Button>
          </div>

          {/* Selection Controls */}
          <div className="flex-shrink-0 flex items-center justify-between p-4 bg-muted/50 rounded-lg">
            <div className="flex items-center space-x-2">
              <Badge variant="secondary">
                {selectedFileIds.length} of {filteredSupportedFiles.length} selected
              </Badge>
              {files.totalFiles > files.supportedFiles.length && (
                <Badge variant="outline">
                  {files.unsupportedFiles.length} unsupported
                </Badge>
              )}
            </div>
            <div className="flex space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleSelectAll}
                disabled={filteredSupportedFiles.length === 0}
                className="h-8"
              >
                Select All
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handleDeselectAll}
                disabled={selectedFileIds.length === 0}
                className="h-8"
              >
                Clear
              </Button>
            </div>
          </div>

          {loading ? (
            <div className="flex-1 flex items-center justify-center">
              <div className="text-center space-y-3">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
                <p className="text-sm text-gray-500">Loading files...</p>
              </div>
            </div>
          ) : (
            <ScrollArea className="flex-1">
              <div className="space-y-6 pb-4">
                {/* Supported Files */}
                {filteredSupportedFiles.length > 0 && (
                  <div>
                    <h4 className="font-medium text-sm text-foreground mb-4 flex items-center">
                      <CheckCircle className="h-4 w-4 mr-2 text-green-600" />
                      Supported Files ({filteredSupportedFiles.length})
                    </h4>
                    <div className="grid gap-3">
                      {filteredSupportedFiles.map((file) => (
                        <Card
                          key={file.documentId}
                          className={`p-4 transition-all duration-200 hover:shadow-sm cursor-pointer border ${
                            selectedFileIds.includes(file.documentId)
                              ? 'ring-2 ring-primary bg-primary/5 border-primary/20'
                              : 'hover:bg-muted/50'
                          }`}
                          onClick={() => handleFileToggle(file.documentId, !selectedFileIds.includes(file.documentId))}
                        >
                          <div className="flex items-center space-x-3">
                            <Checkbox
                              checked={selectedFileIds.includes(file.documentId)}
                              onCheckedChange={(checked) =>
                                handleFileToggle(file.documentId, checked as boolean)
                              }
                            />
                            {getFileIcon(file.mimeType)}
                            <div className="flex-1 min-w-0">
                              <p className="text-sm font-medium truncate">{file.name}</p>
                              <div className="flex items-center space-x-3 text-xs text-muted-foreground mt-1">
                                <span className="font-medium">{formatFileSize(file.fileSize)}</span>
                                {file.geminiFileUri && (
                                  <Badge variant="outline" className="text-xs">
                                    <Upload className="h-3 w-3 mr-1" />
                                    AI Ready
                                  </Badge>
                                )}
                              </div>
                            </div>
                          </div>
                        </Card>
                      ))}
                    </div>
                  </div>
                )}

                {/* Unsupported Files */}
                {showUnsupported && filteredUnsupportedFiles.length > 0 && (
                  <>
                    <Separator />
                    <div>
                      <h4 className="font-medium text-sm text-orange-700 mb-2 flex items-center">
                        <AlertCircle className="h-4 w-4 mr-2" />
                        Unsupported Files ({filteredUnsupportedFiles.length})
                      </h4>
                      <Alert className="mb-2">
                        <AlertCircle className="h-4 w-4" />
                        <AlertDescription>
                          These files cannot be used for AI proposal generation due to unsupported formats.
                        </AlertDescription>
                      </Alert>
                      <div className="grid gap-2">
                        {filteredUnsupportedFiles.map((file) => (
                          <Card key={file.documentId} className="p-3 opacity-60">
                            <div className="flex items-center space-x-3">
                              <Checkbox disabled />
                              {getFileIcon(file.mimeType)}
                              <div className="flex-1 min-w-0">
                                <p className="text-sm font-medium truncate">{file.name}</p>
                                <div className="flex items-center space-x-4 text-xs text-gray-500">
                                  <span>{formatFileSize(file.fileSize)}</span>
                                  <span>{file.mimeType}</span>
                                </div>
                              </div>
                            </div>
                          </Card>
                        ))}
                      </div>
                    </div>
                  </>
                )}

                {/* No Files Message */}
                {!loading && filteredSupportedFiles.length === 0 && !showUnsupported && (
                  <Card className="p-8 text-center border-dashed border-2 border-gray-200">
                    <File className="h-16 w-16 mx-auto mb-4 text-gray-300" />
                    <h4 className="font-medium text-gray-900 mb-2">No supported files found</h4>
                    <p className="text-sm text-gray-500 mb-4">
                      Upload some documents to the opportunity first, or check if your files are in supported formats.
                    </p>
                    <div className="text-xs text-gray-400">
                      Supported formats: PDF, Word, Excel, Text files
                    </div>
                  </Card>
                )}
              </div>
            </ScrollArea>
          )}
        </div>

        <DialogFooter className="flex-shrink-0 px-6 py-6 border-t bg-muted/30">
          <div className="flex items-center justify-between w-full">
            <div className="text-sm text-muted-foreground">
              {selectedFileIds.length > 0 ? (
                `${selectedFileIds.length} file${selectedFileIds.length !== 1 ? 's' : ''} selected`
              ) : (
                'Select at least one file to continue'
              )}
            </div>
            <div className="flex space-x-3">
              <Button variant="outline" onClick={() => onOpenChange(false)} className="min-w-[80px]">
                Cancel
              </Button>
              <Button
                onClick={handleSave}
                disabled={selectedFileIds.length === 0 || saving}
                className="min-w-[140px]"
                loading={saving}
              >
                {saving ? (
                  'Saving...'
                ) : (
                  `Save Selection (${selectedFileIds.length})`
                )}
              </Button>
            </div>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
