'use client';

import React, { useState, useEffect, use } from 'react';
import { useRouter } from 'next/navigation';
import { PermissionGate } from '@/components/ui/permission-gate';
import { PermissionResource, PermissionAction } from '@/types';
import { ModernCompanyForm } from '@/components/companies/modern-company-form';
import { Card, CardContent } from '@/components/ui/card';
import { EmptyState } from '@/components/ui/empty-state';
import { FormPageSkeleton } from '@/components/ui/skeleton-loaders';
import { ExclamationTriangleIcon, ArrowLeftIcon } from '@heroicons/react/24/outline';
import { CompanyWithRelations } from '@/services/company-management';
import { PageLayout } from '@/components/layout/page-layout';
import { Button } from '@/components/ui/button';

interface EditCompanyPageProps {
  params: Promise<{
    id: string;
  }>;
}

export default function EditCompanyPage({ params }: EditCompanyPageProps) {
  const router = useRouter();
  const resolvedParams = use(params);
  const companyId = resolvedParams.id;

  const [initialLoading, setInitialLoading] = useState(true);
  const [company, setCompany] = useState<CompanyWithRelations | null>(null);
  const [error, setError] = useState<string | null>(null);

  const actions = (
    <Button variant="outline" size="sm" onClick={() => router.push('/companies')}>
      <ArrowLeftIcon className="h-4 w-4 mr-2" />
      Back to Companies
    </Button>
  );

  // Fetch company data
  useEffect(() => {
    const fetchCompany = async () => {
      try {
        const response = await fetch(`/api/companies/${companyId}`);
        const data = await response.json();

        if (data.success) {
          setCompany(data.data.company);
        } else {
          setError('Company not found');
        }
      } catch (err) {
        console.error('Failed to fetch company:', err);
        setError('Failed to load company data');
      } finally {
        setInitialLoading(false);
      }
    };

    if (companyId) {
      fetchCompany();
    }
  }, [companyId]);

  const handleSubmit = async (data: Record<string, unknown>) => {
    const response = await fetch(`/api/companies/${companyId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });

    const result = await response.json();

    if (result.success) {
      router.push('/companies');
    } else {
      throw new Error(result.error || 'Failed to update company');
    }
  };

  if (initialLoading) {
    return (
      <PageLayout
        title="Edit Company"
        description="Update company information"
        actions={actions}
      >
        <FormPageSkeleton />
      </PageLayout>
    );
  }

  if (error || !company) {
    return (
      <PageLayout
        title="Edit Company"
        description="Update company information"
        actions={actions}
      >
        <Card>
          <CardContent className="p-8">
            <EmptyState
              icon={<ExclamationTriangleIcon className="w-12 h-12 text-muted-foreground" />}
              title="Company Not Found"
              description={error || "The company you're looking for doesn't exist or you don't have permission to edit it."}
              action={{
                label: 'Back to Companies',
                onClick: () => router.push('/companies'),
                variant: 'primary'
              }}
            />
          </CardContent>
        </Card>
      </PageLayout>
    );
  }

  return (
    <PermissionGate
      resource={PermissionResource.COMPANIES}
      action={PermissionAction.UPDATE}
      fallback={
        <PageLayout
          title="Edit Company"
          description="Update company information"
          actions={actions}
        >
          <Card>
            <CardContent className="p-8">
              <EmptyState
                icon={<ExclamationTriangleIcon className="w-12 h-12 text-muted-foreground" />}
                title="Access Denied"
                description="You don't have permission to edit companies. Please contact your administrator for access."
                action={{
                  label: 'Back to Companies',
                  onClick: () => router.push('/companies'),
                  variant: 'primary'
                }}
              />
            </CardContent>
          </Card>
        </PageLayout>
      }
    >
      <PageLayout
        title={`Edit ${company?.name || 'Company'}`}
        description="Update company information in your CRM system"
        actions={actions}
      >
        <ModernCompanyForm
          key={company?.id || 'new'}
          company={company}
          onSubmit={handleSubmit}
          onCancel={() => router.push('/companies')}
        />
      </PageLayout>
    </PermissionGate>
  );
}
