# Staging configuration for CRM Platform
# This file contains staging-specific overrides for docker-compose.yml

services:
  # Staging configuration for Next.js app
  app:
    build:
      target: runner
    restart: always
    environment:
      - NODE_ENV=production
    deploy:
      resources:
        limits:
          cpus: '1'
          memory: 1G
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Staging configuration for PostgreSQL
  postgres:
    restart: always
    environment:
      - POSTGRES_PASSWORD=${DB_PASSWORD:-admin}
    volumes:
      - postgres-data:/var/lib/postgresql/data
      # Mount init scripts in staging for easier setup
      - ./scripts/init-db:/docker-entrypoint-initdb.d:ro
    deploy:
      resources:
        limits:
          cpus: '1'
          memory: 1G
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Staging configuration for Redis
  redis:
    restart: always
    command: redis-server /usr/local/etc/redis/redis.conf --appendonly yes
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 512M
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Staging Nginx reverse proxy
  nginx:
    image: nginx:stable-alpine
    container_name: crm-nginx-staging
    restart: always
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./config/nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./config/nginx/conf.d:/etc/nginx/conf.d:ro
      - ./config/nginx/ssl:/etc/nginx/ssl:ro
      - ./public:/var/www/html:ro
    depends_on:
      - app
    networks:
      - crm-network
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 256M
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    security_opt:
      - no-new-privileges:true

networks:
  crm-network:
    driver: bridge

volumes:
  postgres-data:
    driver: local
  redis-data:
    driver: local
