'use client';

import { <PERSON>ada<PERSON> } from 'next';
import HandoverManagement from '@/components/handovers/HandoverManagement';
import { PermissionGate } from '@/components/ui/permission-gate';
import { PermissionResource, PermissionAction } from '@/types';
import { Card, CardContent } from '@/components/ui/card';
import { EmptyState } from '@/components/ui/empty-state';
import { ExclamationTriangleIcon, PlusIcon, ArrowDownTrayIcon, Cog6ToothIcon } from '@heroicons/react/24/outline';
import { PageLayout } from '@/components/layout/page-layout';
import { Button } from '@/components/ui/button';
import { useRouter } from 'next/navigation';

export default function HandoversPage() {
  const router = useRouter();

  const actions = (
    <div className="flex items-center gap-2">
      <Button variant="outline" size="sm" onClick={() => router.push('/handovers/templates')}>
        <Cog6ToothIcon className="h-4 w-4 mr-2" />
        Templates
      </Button>
      <PermissionGate
        resource={PermissionResource.HANDOVERS}
        action={PermissionAction.CREATE}
      >
        <Button size="sm" onClick={() => router.push('/handovers/new')}>
          <PlusIcon className="h-4 w-4 mr-2" />
          Create Handover
        </Button>
      </PermissionGate>
    </div>
  );

  // TODO: Re-enable permission gate after running permission seeding
  // The HANDOVERS permissions need to be seeded in the database first
  // Run: node scripts/seed-permissions.js

  return (
    <PageLayout
      title="Handovers"
      description="Manage sales-to-delivery handover processes"
      actions={actions}
    >
      <HandoverManagement />
    </PageLayout>
  );

  // Uncomment this after seeding handover permissions:
  /*
  return (
    <PermissionGate
      resource={PermissionResource.HANDOVERS}
      action={PermissionAction.READ}
      fallback={
        <PageLayout>
          <Card>
            <CardContent className="p-8">
              <EmptyState
                icon={<ExclamationTriangleIcon className="w-12 h-12 text-muted-foreground" />}
                title="Access Denied"
                description="You don't have permission to view handovers. Please contact your administrator for access."
                action={{
                  label: 'Back to Dashboard',
                  onClick: () => window.location.href = '/dashboard',
                  variant: 'primary'
                }}
              />
            </CardContent>
          </Card>
        </PageLayout>
      }
    >
      <PageLayout
        title="Handovers"
        description="Manage sales-to-delivery handover processes"
        actions={actions}
      >
        <HandoverManagement />
      </PageLayout>
    </PermissionGate>
  );
  */
}
