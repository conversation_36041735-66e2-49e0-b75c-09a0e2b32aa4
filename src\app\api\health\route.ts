import { NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';
import Redis from 'ioredis';

/**
 * Health check endpoint for Docker container health monitoring
 * Tests database and Redis connectivity
 */
export async function GET() {
  const healthStatus = {
    status: 'ok',
    timestamp: new Date().toISOString(),
    services: {
      app: { status: 'ok' },
      database: { status: 'unknown' },
      redis: { status: 'unknown' },
    },
  };

  // Check database connection
  try {
    const prisma = new PrismaClient();
    await prisma.$queryRaw`SELECT 1`;
    await prisma.$disconnect();
    healthStatus.services.database.status = 'ok';
  } catch (error) {
    healthStatus.services.database.status = 'error';
    healthStatus.status = 'error';
  }

  // Check Redis connection
  try {
    const redisUrl = process.env.REDIS_URL || 'redis://localhost:6379';
    const redis = new Redis(redisUrl);
    await redis.ping();
    await redis.quit();
    healthStatus.services.redis.status = 'ok';
  } catch (error) {
    healthStatus.services.redis.status = 'error';
    healthStatus.status = 'error';
  }

  return NextResponse.json(healthStatus);
}
