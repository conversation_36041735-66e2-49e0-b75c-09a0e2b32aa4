import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { withTenantContext } from '@/lib/tenant-context';
import { SubscriptionManagementService } from '@/services/subscription-management';
import { z } from 'zod';

/**
 * @swagger
 * /api/subscriptions:
 *   get:
 *     summary: Get tenant subscription details
 *     description: Retrieve current subscription information including usage metrics
 *     tags:
 *       - Subscriptions
 *     security:
 *       - bearerAuth: []
 *       - tenantHeader: []
 *     responses:
 *       200:
 *         description: Subscription details retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/ApiResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         subscription:
 *                           $ref: '#/components/schemas/Subscription'
 *                         plan:
 *                           $ref: '#/components/schemas/SubscriptionPlan'
 *                         usage:
 *                           type: object
 *                           additionalProperties:
 *                             type: number
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       404:
 *         description: Subscription not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
export const GET = withTenantContext(async (request: NextRequest) => {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const tenantId = (request as any).tenantId;
    const subscriptionService = new SubscriptionManagementService();
    
    const subscription = await subscriptionService.getSubscriptionWithUsage(tenantId);

    if (!subscription) {
      return NextResponse.json(
        { error: 'No subscription found for this tenant' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: {
        subscription: {
          id: subscription.id,
          status: subscription.status,
          currentPeriodStart: subscription.currentPeriodStart,
          currentPeriodEnd: subscription.currentPeriodEnd,
          stripeSubscriptionId: subscription.stripeSubscriptionId,
          createdAt: subscription.createdAt,
          updatedAt: subscription.updatedAt
        },
        plan: (subscription as any).plan ? {
          id: (subscription as any).plan.id,
          name: (subscription as any).plan.name,
          description: (subscription as any).plan.description,
          priceMonthly: (subscription as any).plan.priceMonthly,
          priceYearly: (subscription as any).plan.priceYearly,
          features: (subscription as any).plan.features,
          limits: (subscription as any).plan.limits
        } : null,
        usage: (subscription as any).usage || {}
      }
    });

  } catch (error) {
    console.error('Get subscription error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
});

const createSubscriptionSchema = z.object({
  planId: z.string().min(1, 'Plan ID is required'),
  billingCycle: z.enum(['monthly', 'yearly']),
  paymentMethodId: z.string().optional(),
  trialDays: z.number().min(0).max(30).optional()
});

/**
 * @swagger
 * /api/subscriptions:
 *   post:
 *     summary: Create or update tenant subscription
 *     description: Create a new subscription or update existing subscription for the tenant
 *     tags:
 *       - Subscriptions
 *     security:
 *       - bearerAuth: []
 *       - tenantHeader: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - planId
 *               - billingCycle
 *             properties:
 *               planId:
 *                 type: string
 *                 format: uuid
 *                 description: ID of the subscription plan
 *               billingCycle:
 *                 type: string
 *                 enum: [monthly, yearly]
 *                 description: Billing cycle for the subscription
 *               paymentMethodId:
 *                 type: string
 *                 description: Stripe payment method ID
 *               trialDays:
 *                 type: integer
 *                 minimum: 0
 *                 maximum: 30
 *                 description: Number of trial days (0-30)
 *     responses:
 *       201:
 *         description: Subscription created successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/ApiResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       $ref: '#/components/schemas/Subscription'
 *       400:
 *         description: Validation error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
export const POST = withTenantContext(async (request: NextRequest) => {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const tenantId = (request as any).tenantId;
    const body = await request.json();
    const validatedData = createSubscriptionSchema.parse(body);

    const subscriptionService = new SubscriptionManagementService();
    
    const subscription = await subscriptionService.createSubscription({
      tenantId,
      ...validatedData
    });

    return NextResponse.json({
      success: true,
      data: subscription,
      message: 'Subscription created successfully'
    }, { status: 201 });

  } catch (error) {
    console.error('Create subscription error:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          error: 'Validation failed',
          details: error.errors.map(e => ({
            field: e.path.join('.'),
            message: e.message
          }))
        },
        { status: 400 }
      );
    }

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
});

const updateSubscriptionSchema = z.object({
  planId: z.string().optional(),
  billingCycle: z.enum(['monthly', 'yearly']).optional(),
  cancelAtPeriodEnd: z.boolean().optional()
});

/**
 * @swagger
 * /api/subscriptions:
 *   put:
 *     summary: Update tenant subscription
 *     description: Update existing subscription plan, billing cycle, or cancellation status
 *     tags:
 *       - Subscriptions
 *     security:
 *       - bearerAuth: []
 *       - tenantHeader: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               planId:
 *                 type: string
 *                 format: uuid
 *                 description: New subscription plan ID
 *               billingCycle:
 *                 type: string
 *                 enum: [monthly, yearly]
 *                 description: New billing cycle
 *               cancelAtPeriodEnd:
 *                 type: boolean
 *                 description: Whether to cancel subscription at period end
 *     responses:
 *       200:
 *         description: Subscription updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/ApiResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       $ref: '#/components/schemas/Subscription'
 *       400:
 *         description: Validation error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       404:
 *         description: Subscription not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
export const PUT = withTenantContext(async (request: NextRequest) => {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const tenantId = (request as any).tenantId;
    const body = await request.json();
    const validatedData = updateSubscriptionSchema.parse(body);

    const subscriptionService = new SubscriptionManagementService();
    
    const subscription = await subscriptionService.updateSubscription(tenantId, validatedData);

    return NextResponse.json({
      success: true,
      data: subscription,
      message: 'Subscription updated successfully'
    });

  } catch (error) {
    console.error('Update subscription error:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          error: 'Validation failed',
          details: error.errors.map(e => ({
            field: e.path.join('.'),
            message: e.message
          }))
        },
        { status: 400 }
      );
    }

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
});
