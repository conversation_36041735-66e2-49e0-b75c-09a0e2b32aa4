import { NextResponse } from 'next/server';
import { z } from 'zod';
import { CompanyManagementService } from '@/services/company-management';
import { withPermission, AuthenticatedRequest } from '@/lib/permission-middleware';
import { PermissionResource, PermissionAction } from '@/types';

const companyService = new CompanyManagementService();

// Validation schema for search query
const searchQuerySchema = z.object({
  q: z.string().min(1, 'Search query is required'),
  limit: z.string().transform(val => Math.min(parseInt(val) || 10, 50)).optional(),
});

/**
 * GET /api/companies/search
 * Search companies by name
 */
export const GET = withPermission({
  resource: PermissionResource.COMPANIES,
  action: PermissionAction.READ,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest) => {
  try {
    const { searchParams } = new URL(req.url);
    const queryParams = Object.fromEntries(searchParams.entries());
    
    const validatedQuery = searchQuerySchema.parse(queryParams);
    
    const companies = await companyService.searchCompaniesByName(
      validatedQuery.q,
      req.tenantId,
      validatedQuery.limit
    );

    return NextResponse.json({
      success: true,
      data: { companies },
      message: 'Companies search completed successfully'
    });

  } catch (error) {
    console.error('Search companies error:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          error: 'Invalid search parameters',
          details: error.errors.map(e => ({
            field: e.path.join('.'),
            message: e.message
          }))
        },
        { status: 400 }
      );
    }

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
});
