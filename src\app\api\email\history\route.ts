import { NextResponse } from 'next/server';
import { withPermission } from '@/lib/permission-middleware';
import { PermissionAction, PermissionResource } from '@/types';
import { AuthenticatedRequest } from '@/lib/permission-middleware';
import { prisma } from '@/lib/prisma';

async function getHandler(req: AuthenticatedRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const leadId = searchParams.get('leadId');
    const contactId = searchParams.get('contactId');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const skip = (page - 1) * limit;

    console.log('Email history request:', { leadId, contactId, page, limit });

    // Build where clause
    const where: any = {
      tenantId: req.tenantId
    };

    if (leadId) {
      where.leadId = leadId;
    }

    if (contactId) {
      where.contactId = contactId;
    }

    // Use raw SQL queries until Prisma client is regenerated
    console.log('📧 Fetching email history for:', { tenantId: req.tenantId, leadId, contactId });

    // Build dynamic query based on filters
    let emailLogs: any[] = [];
    let total = 0;

    if (leadId && contactId) {
      // Both lead and contact specified
      const countResult = await prisma.$queryRaw`
        SELECT COUNT(*) as count
        FROM email_logs
        WHERE tenant_id = ${req.tenantId}
        AND (lead_id = ${leadId} OR contact_id = ${contactId})
      ` as any[];
      total = parseInt(countResult[0]?.count || '0');

      emailLogs = await prisma.$queryRaw`
        SELECT
          el.id,
          el.subject,
          el.recipients,
          el.status,
          el.sent_at,
          el.delivered_at,
          el.opened_at,
          el.clicked_at,
          el.bounced_at,
          el.provider,
          el.track_opens,
          el.track_clicks,
          el.tags,
          el.metadata,
          l.id as lead_id,
          l.title as lead_title,
          c.id as contact_id,
          c.first_name as contact_first_name,
          c.last_name as contact_last_name,
          c.email as contact_email,
          u.id as sent_by_id,
          u.first_name as sent_by_first_name,
          u.last_name as sent_by_last_name,
          u.email as sent_by_email
        FROM email_logs el
        LEFT JOIN leads l ON el.lead_id = l.id
        LEFT JOIN contacts c ON el.contact_id = c.id
        LEFT JOIN users u ON el.sent_by = u.id
        WHERE el.tenant_id = ${req.tenantId}
        AND (el.lead_id = ${leadId} OR el.contact_id = ${contactId})
        ORDER BY el.sent_at DESC
        LIMIT ${limit} OFFSET ${skip}
      ` as any[];
    } else if (leadId) {
      // Only lead specified
      const countResult = await prisma.$queryRaw`
        SELECT COUNT(*) as count
        FROM email_logs
        WHERE tenant_id = ${req.tenantId} AND lead_id = ${leadId}
      ` as any[];
      total = parseInt(countResult[0]?.count || '0');

      emailLogs = await prisma.$queryRaw`
        SELECT
          el.id,
          el.subject,
          el.recipients,
          el.status,
          el.sent_at,
          el.delivered_at,
          el.opened_at,
          el.clicked_at,
          el.bounced_at,
          el.provider,
          el.track_opens,
          el.track_clicks,
          el.tags,
          el.metadata,
          l.id as lead_id,
          l.title as lead_title,
          c.id as contact_id,
          c.first_name as contact_first_name,
          c.last_name as contact_last_name,
          c.email as contact_email,
          u.id as sent_by_id,
          u.first_name as sent_by_first_name,
          u.last_name as sent_by_last_name,
          u.email as sent_by_email
        FROM email_logs el
        LEFT JOIN leads l ON el.lead_id = l.id
        LEFT JOIN contacts c ON el.contact_id = c.id
        LEFT JOIN users u ON el.sent_by = u.id
        WHERE el.tenant_id = ${req.tenantId} AND el.lead_id = ${leadId}
        ORDER BY el.sent_at DESC
        LIMIT ${limit} OFFSET ${skip}
      ` as any[];
    } else if (contactId) {
      // Only contact specified
      const countResult = await prisma.$queryRaw`
        SELECT COUNT(*) as count
        FROM email_logs
        WHERE tenant_id = ${req.tenantId} AND contact_id = ${contactId}
      ` as any[];
      total = parseInt(countResult[0]?.count || '0');

      emailLogs = await prisma.$queryRaw`
        SELECT
          el.id,
          el.subject,
          el.recipients,
          el.status,
          el.sent_at,
          el.delivered_at,
          el.opened_at,
          el.clicked_at,
          el.bounced_at,
          el.provider,
          el.track_opens,
          el.track_clicks,
          el.tags,
          el.metadata,
          l.id as lead_id,
          l.title as lead_title,
          c.id as contact_id,
          c.first_name as contact_first_name,
          c.last_name as contact_last_name,
          c.email as contact_email,
          u.id as sent_by_id,
          u.first_name as sent_by_first_name,
          u.last_name as sent_by_last_name,
          u.email as sent_by_email
        FROM email_logs el
        LEFT JOIN leads l ON el.lead_id = l.id
        LEFT JOIN contacts c ON el.contact_id = c.id
        LEFT JOIN users u ON el.sent_by = u.id
        WHERE el.tenant_id = ${req.tenantId} AND el.contact_id = ${contactId}
        ORDER BY el.sent_at DESC
        LIMIT ${limit} OFFSET ${skip}
      ` as any[];
    } else {
      // No specific lead or contact, get all for tenant
      const countResult = await prisma.$queryRaw`
        SELECT COUNT(*) as count
        FROM email_logs
        WHERE tenant_id = ${req.tenantId}
      ` as any[];
      total = parseInt(countResult[0]?.count || '0');

      emailLogs = await prisma.$queryRaw`
        SELECT
          el.id,
          el.subject,
          el.recipients,
          el.status,
          el.sent_at,
          el.delivered_at,
          el.opened_at,
          el.clicked_at,
          el.bounced_at,
          el.provider,
          el.track_opens,
          el.track_clicks,
          el.tags,
          el.metadata,
          l.id as lead_id,
          l.title as lead_title,
          c.id as contact_id,
          c.first_name as contact_first_name,
          c.last_name as contact_last_name,
          c.email as contact_email,
          u.id as sent_by_id,
          u.first_name as sent_by_first_name,
          u.last_name as sent_by_last_name,
          u.email as sent_by_email
        FROM email_logs el
        LEFT JOIN leads l ON el.lead_id = l.id
        LEFT JOIN contacts c ON el.contact_id = c.id
        LEFT JOIN users u ON el.sent_by = u.id
        WHERE el.tenant_id = ${req.tenantId}
        ORDER BY el.sent_at DESC
        LIMIT ${limit} OFFSET ${skip}
      ` as any[];
    }

    console.log('📧 Found email logs:', emailLogs.length, 'total:', total);

    // Transform the raw SQL data for the frontend
    const emails = emailLogs.map(log => ({
      id: log.id,
      subject: log.subject,
      recipients: typeof log.recipients === 'string' ? JSON.parse(log.recipients) : log.recipients,
      status: log.status,
      sentAt: new Date(log.sent_at).toISOString(),
      deliveredAt: log.delivered_at ? new Date(log.delivered_at).toISOString() : undefined,
      openedAt: log.opened_at ? new Date(log.opened_at).toISOString() : undefined,
      clickedAt: log.clicked_at ? new Date(log.clicked_at).toISOString() : undefined,
      bouncedAt: log.bounced_at ? new Date(log.bounced_at).toISOString() : undefined,
      provider: log.provider,
      trackOpens: log.track_opens,
      trackClicks: log.track_clicks,
      tags: typeof log.tags === 'string' ? JSON.parse(log.tags) : (log.tags || []),
      metadata: typeof log.metadata === 'string' ? JSON.parse(log.metadata) : (log.metadata || {}),
      lead: log.lead_id ? {
        id: log.lead_id,
        title: log.lead_title
      } : null,
      contact: log.contact_id ? {
        id: log.contact_id,
        firstName: log.contact_first_name,
        lastName: log.contact_last_name,
        email: log.contact_email
      } : null,
      sentBy: log.sent_by_id ? {
        id: log.sent_by_id,
        firstName: log.sent_by_first_name,
        lastName: log.sent_by_last_name,
        email: log.sent_by_email
      } : null
    }));

    const totalPages = Math.ceil(total / limit);

    return NextResponse.json({
      success: true,
      data: {
        emails,
        pagination: {
          page,
          limit,
          total,
          totalPages,
          hasNext: page < totalPages,
          hasPrev: page > 1
        }
      }
    });

  } catch (error) {
    console.error('Email history API error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch email history' },
      { status: 500 }
    );
  }
}

// Export with permission middleware
export const GET = withPermission({
  resource: PermissionResource.CONTACTS,
  action: PermissionAction.READ
})(getHandler);
