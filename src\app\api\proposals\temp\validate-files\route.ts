import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { hasPermission } from '@/lib/permissions';
import { PermissionResource, PermissionAction } from '@/types';
import { prisma } from '@/lib/prisma';
import { GeminiFileService } from '@/services/gemini-file-service';
import { z } from 'zod';

// Request validation schema
const validateFilesSchema = z.object({
  documentIds: z.array(z.string()).min(1, 'At least one document ID is required')
});

/**
 * POST /api/proposals/temp/validate-files
 * Temporary endpoint to validate files for Gemini upload compatibility
 * Used during proposal creation wizard before proposal exists
 */
export async function POST(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const body = await req.json();
    const validatedData = validateFilesSchema.parse(body);

    // Check permissions
    const currentTenant = session.user.currentTenant;
    if (!currentTenant) {
      return NextResponse.json(
        { error: 'No active tenant found' },
        { status: 400 }
      );
    }

    const hasDocumentPermission = await hasPermission(
      session.user.id,
      currentTenant.id,
      PermissionResource.DOCUMENTS,
      PermissionAction.READ
    );

    if (!hasDocumentPermission) {
      return NextResponse.json(
        { error: 'Insufficient permissions to access documents' },
        { status: 403 }
      );
    }

    // Get documents to validate
    const documents = await prisma.document.findMany({
      where: {
        id: { in: validatedData.documentIds },
        tenantId: currentTenant.id
      }
    });

    if (documents.length !== validatedData.documentIds.length) {
      return NextResponse.json(
        { error: 'Some documents not found or not accessible' },
        { status: 400 }
      );
    }

    // Validate each document for Gemini compatibility
    const validationResults = documents.map(doc => {
      const validation = GeminiFileService.validateFileForGemini(
        doc.mimeType || 'application/octet-stream',
        doc.fileSize || 0
      );

      return {
        documentId: doc.id,
        name: doc.name,
        mimeType: doc.mimeType,
        fileSize: doc.fileSize,
        isValid: validation.valid,
        error: validation.error,
        canUpload: validation.valid
      };
    });

    const validFiles = validationResults.filter(result => result.isValid);
    const invalidFiles = validationResults.filter(result => !result.isValid);

    return NextResponse.json({
      success: true,
      data: {
        totalFiles: documents.length,
        validFiles: validFiles.length,
        invalidFiles: invalidFiles.length,
        validationResults,
        canProceedWithUpload: validFiles.length > 0,
        summary: {
          allValid: invalidFiles.length === 0,
          hasValidFiles: validFiles.length > 0,
          hasInvalidFiles: invalidFiles.length > 0
        }
      },
      message: invalidFiles.length === 0 
        ? 'All files are valid for Gemini upload'
        : validFiles.length === 0
        ? 'No files are valid for Gemini upload'
        : `${validFiles.length} of ${documents.length} files are valid for Gemini upload`
    });

  } catch (error) {
    console.error('File validation error:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          error: 'Invalid request data',
          details: error.errors.map(e => `${e.path.join('.')}: ${e.message}`)
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error during file validation' },
      { status: 500 }
    );
  }
}
