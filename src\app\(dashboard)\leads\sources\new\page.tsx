'use client';

import React from 'react';
import { PageLayout } from '@/components/layout/page-layout';
import { LeadSourceForm } from '@/components/lead-sources/lead-source-form';

import { PermissionResource, PermissionAction } from '@/types';
import { Card, CardContent } from '@/components/ui/card';
import { EmptyState } from '@/components/ui/empty-state';
import { ExclamationTriangleIcon } from '@heroicons/react/24/outline';
import { PermissionGate } from '@/components/ui/permission-gate';
import { useSmoothNavigation } from '@/hooks/use-smooth-navigation';

export default function NewLeadSourcePage() {
  const { navigate } = useSmoothNavigation();

  return (
    <PermissionGate
      resource={PermissionResource.LEADS}
      action={PermissionAction.CREATE}
      fallback={
        <PageLayout>
          <Card>
            <CardContent className="p-8">
              <EmptyState
                icon={<ExclamationTriangleIcon className="w-12 h-12 text-muted-foreground" />}
                title="Access Denied"
                description="You don't have permission to create lead sources. Please contact your administrator for access."
                action={{
                  label: 'Back to Lead Sources',
                  onClick: () => navigate('/leads/sources'),
                  variant: 'primary'
                }}
              />
            </CardContent>
          </Card>
        </PageLayout>
      }
    >
      <PageLayout
        title="Add Lead Source"
        description="Create a new lead source to track where your leads are coming from"
        breadcrumbs={[
          { label: 'Leads', href: '/leads' },
          { label: 'Lead Sources', href: '/leads/sources' },
          { label: 'Add Lead Source' }
        ]}
      >
        <LeadSourceForm />
      </PageLayout>
    </PermissionGate>
  );
}
