import { prisma } from '@/lib/prisma';
import { generateWelcomeEmail, generateUserInviteEmail, WelcomeEmailData, UserInviteEmailData } from '@/lib/email-templates';
import { SMTPService } from '@/services/smtp-service';

export interface UserNotificationData {
  userId: string;
  tenantId: string;
  type: 'welcome' | 'invite' | 'password_reset' | 'account_activated' | 'role_changed';
  title: string;
  message: string;
  data?: Record<string, any>;
}

export interface WelcomeNotificationOptions {
  user: {
    id: string;
    email: string;
    firstName: string;
    lastName: string;
  };
  tenant: {
    id: string;
    name: string;
    slug: string;
  };
  temporaryPassword: string;
  createdBy: string;
}

export interface InviteNotificationOptions {
  user: {
    id: string;
    email: string;
    firstName: string;
    lastName: string;
  };
  tenant: {
    id: string;
    name: string;
    slug: string;
  };
  inviter: {
    id: string;
    firstName: string;
    lastName: string;
  };
  role: string;
  inviteToken: string;
}

export class UserNotificationService {
  private smtpService: SMTPService;

  constructor() {
    this.smtpService = new SMTPService();
  }

  /**
   * Send welcome email and create portal notification for new user
   */
  async sendWelcomeNotification(options: WelcomeNotificationOptions): Promise<void> {
    const { user, tenant, temporaryPassword, createdBy } = options;

    try {
      // Create portal notification
      await this.createPortalNotification({
        userId: user.id,
        tenantId: tenant.id,
        type: 'welcome',
        title: `Welcome to ${tenant.name}!`,
        message: `Your account has been created successfully. Please check your email for login instructions.`,
        data: {
          temporaryPassword: temporaryPassword,
          createdBy: createdBy,
        },
      });

      // Send welcome email
      const loginUrl = `${process.env.NEXTAUTH_URL}/auth/signin`;
      const supportEmail = process.env.SUPPORT_EMAIL;

      const emailData: WelcomeEmailData = {
        firstName: user.firstName,
        lastName: user.lastName,
        email: user.email,
        temporaryPassword: temporaryPassword,
        organizationName: tenant.name,
        loginUrl: loginUrl,
        supportEmail: supportEmail,
      };

      const emailContent = generateWelcomeEmail(emailData);

      await this.smtpService.sendEmail({
        to: user.email,
        subject: emailContent.subject,
        html: emailContent.html,
        text: emailContent.text,
        priority: 'high',
      });

      console.log(`Welcome notification sent to user ${user.id} (${user.email})`);
    } catch (error) {
      console.error('Failed to send welcome notification:', error);
      throw new Error('Failed to send welcome notification');
    }
  }

  /**
   * Send invite email and create portal notification
   */
  async sendInviteNotification(options: InviteNotificationOptions): Promise<void> {
    const { user, tenant, inviter, role, inviteToken } = options;

    try {
      // Create portal notification for inviter
      await this.createPortalNotification({
        userId: inviter.id,
        tenantId: tenant.id,
        type: 'invite',
        title: 'User Invitation Sent',
        message: `Invitation sent to ${user.firstName} ${user.lastName} (${user.email}) for the role of ${role}.`,
        data: {
          invitedUserId: user.id,
          invitedUserEmail: user.email,
          role: role,
        },
      });

      // Send invite email
      const inviteUrl = `${process.env.NEXTAUTH_URL}/auth/accept-invite?token=${inviteToken}`;

      const emailData: UserInviteEmailData = {
        firstName: user.firstName,
        lastName: user.lastName,
        inviterName: `${inviter.firstName} ${inviter.lastName}`,
        organizationName: tenant.name,
        inviteUrl: inviteUrl,
        role: role,
      };

      const emailContent = generateUserInviteEmail(emailData);

      await this.smtpService.sendEmail({
        to: user.email,
        subject: emailContent.subject,
        html: emailContent.html,
        text: emailContent.text,
        priority: 'normal',
      });

      console.log(`Invite notification sent to user ${user.id} (${user.email})`);
    } catch (error) {
      console.error('Failed to send invite notification:', error);
      throw new Error('Failed to send invite notification');
    }
  }

  /**
   * Create a portal notification for the user
   */
  async createPortalNotification(data: UserNotificationData): Promise<void> {
    try {
      await prisma.notification.create({
        data: {
          tenantId: data.tenantId,
          userId: data.userId,
          type: data.type,
          title: data.title,
          message: data.message,
          data: data.data || {},
          isRead: false,
          createdAt: new Date(),
        },
      });

      console.log(`Portal notification created for user ${data.userId}`);
    } catch (error) {
      console.error('Failed to create portal notification:', error);
      // Don't throw here as this is not critical
    }
  }

  /**
   * Send account activation notification
   */
  async sendAccountActivationNotification(userId: string, tenantId: string): Promise<void> {
    try {
      const user = await prisma.user.findUnique({
        where: { id: userId },
        include: {
          tenantUsers: {
            where: { tenantId },
            include: { tenant: true },
          },
        },
      });

      if (!user || !user.tenantUsers[0]) {
        throw new Error('User or tenant not found');
      }

      const tenant = user.tenantUsers[0].tenant;

      await this.createPortalNotification({
        userId: userId,
        tenantId: tenantId,
        type: 'account_activated',
        title: 'Account Activated',
        message: `Your account has been activated. Welcome to ${tenant.name}!`,
        data: {
          activatedAt: new Date().toISOString(),
        },
      });

      console.log(`Account activation notification sent to user ${userId}`);
    } catch (error) {
      console.error('Failed to send account activation notification:', error);
    }
  }

  /**
   * Send role change notification
   */
  async sendRoleChangeNotification(
    userId: string,
    tenantId: string,
    oldRole: string,
    newRole: string,
    changedBy: string
  ): Promise<void> {
    try {
      const user = await prisma.user.findUnique({
        where: { id: userId },
      });

      const changer = await prisma.user.findUnique({
        where: { id: changedBy },
      });

      if (!user || !changer) {
        throw new Error('User not found');
      }

      await this.createPortalNotification({
        userId: userId,
        tenantId: tenantId,
        type: 'role_changed',
        title: 'Role Updated',
        message: `Your role has been changed from ${oldRole} to ${newRole} by ${changer.firstName} ${changer.lastName}.`,
        data: {
          oldRole: oldRole,
          newRole: newRole,
          changedBy: changedBy,
          changedAt: new Date().toISOString(),
        },
      });

      console.log(`Role change notification sent to user ${userId}`);
    } catch (error) {
      console.error('Failed to send role change notification:', error);
    }
  }

  /**
   * Get notifications for a user
   */
  async getUserNotifications(
    userId: string,
    tenantId: string,
    options: {
      unreadOnly?: boolean;
      limit?: number;
      offset?: number;
    } = {}
  ): Promise<any[]> {
    const { unreadOnly = false, limit = 50, offset = 0 } = options;

    try {
      const notifications = await prisma.notification.findMany({
        where: {
          userId: userId,
          tenantId: tenantId,
          ...(unreadOnly && { isRead: false }),
        },
        orderBy: {
          createdAt: 'desc',
        },
        take: limit,
        skip: offset,
      });

      return notifications;
    } catch (error) {
      console.error('Failed to get user notifications:', error);
      return [];
    }
  }

  /**
   * Mark notification as read
   */
  async markNotificationAsRead(notificationId: string, userId: string): Promise<void> {
    try {
      await prisma.notification.updateMany({
        where: {
          id: notificationId,
          userId: userId,
        },
        data: {
          isRead: true,
          readAt: new Date(),
        },
      });

      console.log(`Notification ${notificationId} marked as read for user ${userId}`);
    } catch (error) {
      console.error('Failed to mark notification as read:', error);
    }
  }

  /**
   * Mark all notifications as read for a user
   */
  async markAllNotificationsAsRead(userId: string, tenantId: string): Promise<void> {
    try {
      await prisma.notification.updateMany({
        where: {
          userId: userId,
          tenantId: tenantId,
          isRead: false,
        },
        data: {
          isRead: true,
          readAt: new Date(),
        },
      });

      console.log(`All notifications marked as read for user ${userId}`);
    } catch (error) {
      console.error('Failed to mark all notifications as read:', error);
    }
  }
}
