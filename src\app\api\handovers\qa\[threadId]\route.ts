import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { handoverQAService } from '@/services/handover-qa';
import { z } from 'zod';

// Request validation schemas
const updateQAThreadSchema = z.object({
  title: z.string().min(1, 'Title is required').max(255).optional(),
  category: z.enum(['general', 'technical', 'requirements', 'timeline', 'budget']).optional(),
  priority: z.enum(['low', 'medium', 'high', 'urgent']).optional(),
  status: z.enum(['open', 'answered', 'closed', 'escalated']).optional(),
  assignedToId: z.string().optional().nullable().transform(val => val === '' ? null : val),
});

interface RouteParams {
  params: {
    threadId: string;
  };
}

/**
 * GET /api/handovers/qa/[threadId] - Get a specific Q&A thread
 */
export async function GET(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get tenantId from user's tenants array (users have one tenant in this system)
    let tenantId: string | null = null;
    if (session.user.tenants && session.user.tenants.length > 0) {
      tenantId = session.user.tenants[0].id;
    }

    if (!tenantId) {
      return NextResponse.json({ error: 'Tenant not found' }, { status: 400 });
    }

    const resolvedParams = await params;
    const thread = await handoverQAService.getQAThreadById(
      tenantId,
      resolvedParams.threadId
    );

    if (!thread) {
      return NextResponse.json({ error: 'Q&A thread not found' }, { status: 404 });
    }

    // Mark messages as read for the current user
    await handoverQAService.markMessagesAsRead(
      tenantId,
      resolvedParams.threadId,
      session.user.id
    );

    return NextResponse.json(thread);
  } catch (error) {
    console.error('Error fetching Q&A thread:', error);
    return NextResponse.json(
      { error: 'Failed to fetch Q&A thread' },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/handovers/qa/[threadId] - Update a Q&A thread
 */
export async function PUT(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get tenantId from user's tenants array (users have one tenant in this system)
    let tenantId: string | null = null;
    if (session.user.tenants && session.user.tenants.length > 0) {
      tenantId = session.user.tenants[0].id;
    }

    if (!tenantId) {
      return NextResponse.json({ error: 'Tenant not found' }, { status: 400 });
    }

    const body = await request.json();
    const validatedData = updateQAThreadSchema.parse(body);

    const resolvedParams = await params;
    const thread = await handoverQAService.updateQAThread(
      tenantId,
      resolvedParams.threadId,
      validatedData
    );

    return NextResponse.json(thread);
  } catch (error) {
    console.error('Error updating Q&A thread:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request data', details: error.errors },
        { status: 400 }
      );
    }

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to update Q&A thread' },
      { status: 500 }
    );
  }
}
