import { NextResponse } from 'next/server';
import { z } from 'zod';
import { withPermission, AuthenticatedRequest } from '@/lib/permission-middleware';
import { UserManagementService } from '@/services/user-management';
import { PermissionAction, PermissionResource } from '@/types';

const bulkInviteSchema = z.object({
  users: z.array(z.object({
    email: z.string().email('Invalid email address'),
    firstName: z.string().min(1, 'First name is required'),
    lastName: z.string().min(1, 'Last name is required'),
    role: z.string().min(1, 'Role is required'),
    teamId: z.string().optional(),
    department: z.string().optional(),
    jobTitle: z.string().optional()
  })).min(1, 'At least one user is required').max(50, 'Maximum 50 users per batch')
});

const bulkUpdateSchema = z.object({
  userIds: z.array(z.string()).min(1, 'At least one user ID is required'),
  updates: z.object({
    status: z.string().optional(),
    teamId: z.string().optional(),
    department: z.string().optional(),
    jobTitle: z.string().optional(),
    role: z.string().optional()
  })
});

const bulkRemoveSchema = z.object({
  userIds: z.array(z.string()).min(1, 'At least one user ID is required'),
  reason: z.string().optional()
});

/**
 * POST /api/users/bulk
 * Bulk operations for users
 */
export const POST = withPermission({
  resource: PermissionResource.USERS,
  action: PermissionAction.MANAGE,
  allowSuperAdmin: true
})(async (req) => {
  try {
    const body = await req.json();
    const { operation } = body;

    const userService = new UserManagementService();

    switch (operation) {
      case 'invite':
        const inviteData = bulkInviteSchema.parse(body);
        const inviteResults = await userService.bulkInviteUsers(
          req.tenantId,
          inviteData.users,
          req.userId
        );

        return NextResponse.json({
          success: true,
          data: { results: inviteResults },
          message: `${inviteResults.successful.length} users invited successfully`
        });

      case 'update':
        const updateData = bulkUpdateSchema.parse(body);
        const updateResults = await userService.bulkUpdateUsers(
          req.tenantId,
          updateData.userIds,
          updateData.updates,
          req.userId
        );

        return NextResponse.json({
          success: true,
          data: { results: updateResults },
          message: `${updateResults.successful.length} users updated successfully`
        });

      case 'remove':
        const removeData = bulkRemoveSchema.parse(body);
        const removeResults = await userService.bulkRemoveUsers(
          req.tenantId,
          removeData.userIds,
          req.userId,
          removeData.reason
        );

        return NextResponse.json({
          success: true,
          data: { results: removeResults },
          message: `${removeResults.successful.length} users removed successfully`
        });

      default:
        return NextResponse.json(
          { error: 'Invalid operation. Supported operations: invite, update, remove' },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('Bulk user operation error:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          error: 'Validation failed',
          details: error.errors.map(e => ({
            field: e.path.join('.'),
            message: e.message
          }))
        },
        { status: 400 }
      );
    }

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Bulk operation failed' },
      { status: 500 }
    );
  }
});

/**
 * GET /api/users/bulk/export
 * Export users data
 */
export const GET = withPermission({
  resource: PermissionResource.USERS,
  action: PermissionAction.EXPORT,
  allowSuperAdmin: true
})(async (req) => {
  try {
    const { searchParams } = new URL(req.url);
    const format = searchParams.get('format') || 'csv';
    const includeRoles = searchParams.get('includeRoles') === 'true';
    const includeTeams = searchParams.get('includeTeams') === 'true';

    const userService = new UserManagementService();
    const exportData = await userService.exportUsers(req.tenantId, {
      format: format as 'csv' | 'xlsx',
      includeRoles,
      includeTeams
    });

    const headers = new Headers();
    headers.set('Content-Type', format === 'xlsx'
      ? 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      : 'text/csv; charset=utf-8'
    );
    headers.set('Content-Disposition', `attachment; filename="users.${format}"`);

    return new NextResponse(exportData, { headers });

  } catch (error) {
    console.error('Export users error:', error);
    return NextResponse.json(
      { error: 'Failed to export users' },
      { status: 500 }
    );
  }
});
