'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import {
  Search,
  Plus,
  Filter,
  Calendar,
  Users,
  FileText,
  MessageSquare,
  Clock,
  CheckCircle,
  AlertCircle,
  XCircle,
  Edit,
  Trash2,
  MoreHorizontal
} from 'lucide-react';
import { format } from 'date-fns';
import { useEnhancedDeleteDialog } from '@/components/ui/enhanced-delete-dialog';
import { toastFunctions as toast } from '@/hooks/use-toast';

// Types
interface HandoverSummary {
  id: string;
  title: string;
  status: string;
  priority: string;
  checklistProgress: number;
  documentsCount: number;
  qaThreadsCount: number;
  scheduledDate?: string;
  createdAt: string;
  opportunity: {
    id: string;
    title: string;
    value?: number;
    contact?: {
      firstName: string;
      lastName: string;
    };
    company?: {
      name: string;
    };
  };
  salesRep?: {
    firstName?: string;
    lastName?: string;
  };
  deliveryManager?: {
    firstName?: string;
    lastName?: string;
  };
}

interface HandoversResponse {
  handovers: HandoverSummary[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// Status and priority configurations
const statusConfig = {
  pending: { label: 'Pending', color: 'bg-yellow-100 text-yellow-800', icon: Clock },
  in_progress: { label: 'In Progress', color: 'bg-blue-100 text-blue-800', icon: AlertCircle },
  completed: { label: 'Completed', color: 'bg-green-100 text-green-800', icon: CheckCircle },
  cancelled: { label: 'Cancelled', color: 'bg-red-100 text-red-800', icon: XCircle },
};

const priorityConfig = {
  low: { label: 'Low', color: 'bg-gray-100 text-gray-800' },
  medium: { label: 'Medium', color: 'bg-blue-100 text-blue-800' },
  high: { label: 'High', color: 'bg-orange-100 text-orange-800' },
  urgent: { label: 'Urgent', color: 'bg-red-100 text-red-800' },
};

export default function HandoverManagement() {
  const router = useRouter();
  const { showDeleteDialog } = useEnhancedDeleteDialog();
  const [handovers, setHandovers] = useState<HandoverSummary[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [priorityFilter, setPriorityFilter] = useState<string>('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [total, setTotal] = useState(0);

  // Fetch handovers
  const fetchHandovers = async () => {
    try {
      setLoading(true);
      setError(null);

      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: '20',
        ...(searchTerm && { search: searchTerm }),
        ...(statusFilter && statusFilter !== 'all' && { status: statusFilter }),
        ...(priorityFilter && priorityFilter !== 'all' && { priority: priorityFilter }),
      });

      const response = await fetch(`/api/handovers?${params}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch handovers');
      }

      const data: HandoversResponse = await response.json();
      setHandovers(data.handovers);
      setTotalPages(data.totalPages);
      setTotal(data.total);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchHandovers();
  }, [currentPage, searchTerm, statusFilter, priorityFilter]);

  // Handle search with debouncing
  useEffect(() => {
    const timer = setTimeout(() => {
      setCurrentPage(1); // Reset to first page when searching
    }, 300);

    return () => clearTimeout(timer);
  }, [searchTerm]);

  // Reset page when filters change
  useEffect(() => {
    setCurrentPage(1);
  }, [statusFilter, priorityFilter]);

  const handleCreateHandover = () => {
    router.push('/handovers/new');
  };

  const handleViewHandover = (handoverId: string) => {
    console.log('Navigating to view handover:', handoverId);
    router.push(`/handovers/${handoverId}`);
  };

  const handleEditHandover = (handoverId: string) => {
    console.log('Navigating to edit handover:', handoverId);
    router.push(`/handovers/${handoverId}/edit`);
  };

  const handleDeleteHandover = async (handoverId: string) => {
    const handover = handovers.find(h => h.id === handoverId);
    if (!handover) return;

    // Prepare related data for the delete dialog
    const relatedData = [
      `Opportunity: ${handover.opportunity.title}`,
      `Status: ${handover.status}`,
      `Priority: ${handover.priority}`,
      `Progress: ${handover.checklistProgress}%`,
    ];

    // Add warnings based on handover status and progress
    const customWarnings = [];
    if (handover.status === 'in_progress') {
      customWarnings.push('This handover is currently in progress');
    }
    if (handover.checklistProgress > 0) {
      customWarnings.push(`${handover.checklistProgress}% of checklist items have been completed`);
    }
    if (handover.documentsCount > 0) {
      customWarnings.push(`${handover.documentsCount} documents are attached to this handover`);
    }
    if (handover.qaThreadsCount > 0) {
      customWarnings.push(`${handover.qaThreadsCount} Q&A threads will be lost`);
    }

    showDeleteDialog({
      title: 'Delete Handover',
      description: `Are you sure you want to delete the handover "${handover.title}"?`,
      itemName: handover.title,
      itemType: 'handover',
      relatedData,
      customWarnings,
      onConfirm: async () => {
        try {
          const response = await fetch(`/api/handovers/${handoverId}`, {
            method: 'DELETE',
          });

          if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || 'Failed to delete handover');
          }

          // Show success message
          toast({
            title: 'Handover Deleted',
            description: `"${handover.title}" has been successfully deleted.`,
          });

          // Refresh the handovers list
          await fetchHandovers();
        } catch (err) {
          console.error('Error deleting handover:', err);
          toast({
            title: 'Delete Failed',
            description: err instanceof Error ? err.message : 'Failed to delete handover',
            variant: 'destructive',
          });
        }
      },
    });
  };

  const getStatusBadge = (status: string) => {
    const config = statusConfig[status as keyof typeof statusConfig];
    if (!config) return null;

    const Icon = config.icon;
    return (
      <Badge className={config.color}>
        <Icon className="w-3 h-3 mr-1" />
        {config.label}
      </Badge>
    );
  };

  const getPriorityBadge = (priority: string) => {
    const config = priorityConfig[priority as keyof typeof priorityConfig];
    if (!config) return null;

    return (
      <Badge variant="outline" className={config.color}>
        {config.label}
      </Badge>
    );
  };

  const formatCurrency = (value?: number) => {
    if (!value) return 'N/A';
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
    }).format(value);
  };

  if (loading && handovers.length === 0) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-sm text-gray-600">Loading handovers...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">

      {/* Filters */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="Search handovers..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-full sm:w-48">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Statuses</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
                <SelectItem value="in_progress">In Progress</SelectItem>
                <SelectItem value="completed">Completed</SelectItem>
                <SelectItem value="cancelled">Cancelled</SelectItem>
              </SelectContent>
            </Select>
            <Select value={priorityFilter} onValueChange={setPriorityFilter}>
              <SelectTrigger className="w-full sm:w-48">
                <SelectValue placeholder="Filter by priority" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Priorities</SelectItem>
                <SelectItem value="low">Low</SelectItem>
                <SelectItem value="medium">Medium</SelectItem>
                <SelectItem value="high">High</SelectItem>
                <SelectItem value="urgent">Urgent</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Results Summary */}
      {!loading && (
        <div className="text-sm text-gray-600">
          Showing {handovers.length} of {total} handovers
        </div>
      )}

      {/* Error State */}
      {error && (
        <Card>
          <CardContent className="pt-6">
            <div className="text-center text-red-600">
              <AlertCircle className="w-8 h-8 mx-auto mb-2" />
              <p>{error}</p>
              <Button 
                variant="outline" 
                onClick={fetchHandovers}
                className="mt-2"
              >
                Try Again
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Handovers Table */}
      {!error && (
        <Card>
          <CardContent className="p-6">
            <div className="overflow-x-auto">
              <table className="w-full border-collapse">
                <thead>
                  <tr className="border-b">
                    <th className="text-left p-3 font-medium">Handover</th>
                    <th className="text-left p-3 font-medium">Opportunity</th>
                    <th className="text-left p-3 font-medium">Status</th>
                    <th className="text-left p-3 font-medium">Priority</th>
                    <th className="text-left p-3 font-medium">Progress</th>
                    <th className="text-left p-3 font-medium">Team</th>
                    <th className="text-left p-3 font-medium">Scheduled</th>
                    <th className="text-left p-3 font-medium w-32">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {handovers.map((handover) => (
                    <tr key={handover.id} className="border-b hover:bg-gray-50">
                      <td className="p-3">
                        <div>
                          <div className="font-medium">{handover.title}</div>
                          <div className="text-sm text-gray-500">
                            Created {format(new Date(handover.createdAt), 'MMM d, yyyy')}
                          </div>
                        </div>
                      </td>
                      <td className="p-3">
                        <div>
                          <div className="font-medium">{handover.opportunity.title}</div>
                          <div className="text-sm text-gray-500">
                            {handover.opportunity.company?.name ||
                             `${handover.opportunity.contact?.firstName} ${handover.opportunity.contact?.lastName}`}
                          </div>
                          {handover.opportunity.value && (
                            <div className="text-sm font-medium text-green-600">
                              {formatCurrency(handover.opportunity.value)}
                            </div>
                          )}
                        </div>
                      </td>
                      <td className="p-3">
                        {getStatusBadge(handover.status)}
                      </td>
                      <td className="p-3">
                        {getPriorityBadge(handover.priority)}
                      </td>
                      <td className="p-3">
                        <div className="space-y-1">
                          <Progress value={handover.checklistProgress} className="w-16" />
                          <div className="text-xs text-gray-500">
                            {handover.checklistProgress}%
                          </div>
                        </div>
                      </td>
                      <td className="p-3">
                        <div className="text-sm">
                          {handover.salesRep && (
                            <div>Sales: {handover.salesRep.firstName} {handover.salesRep.lastName}</div>
                          )}
                          {handover.deliveryManager && (
                            <div>Delivery: {handover.deliveryManager.firstName} {handover.deliveryManager.lastName}</div>
                          )}
                        </div>
                      </td>
                      <td className="p-3">
                        {handover.scheduledDate ? (
                          <div className="text-sm">
                            {format(new Date(handover.scheduledDate), 'MMM d, yyyy')}
                          </div>
                        ) : (
                          <span className="text-gray-400">Not scheduled</span>
                        )}
                      </td>
                      <td className="p-3">
                        <div className="flex items-center gap-1">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={(e) => {
                              e.preventDefault();
                              e.stopPropagation();
                              console.log('View handover clicked:', handover.id);
                              handleViewHandover(handover.id);
                            }}
                          >
                            View
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={(e) => {
                              e.preventDefault();
                              e.stopPropagation();
                              console.log('Edit handover clicked:', handover.id);
                              handleEditHandover(handover.id);
                            }}
                            title="Edit handover"
                          >
                            <Edit className="w-4 h-4" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={(e) => {
                              e.preventDefault();
                              e.stopPropagation();
                              console.log('Delete handover clicked:', handover.id);
                              handleDeleteHandover(handover.id);
                            }}
                            className="text-red-600 hover:text-red-700 hover:bg-red-50"
                            title="Delete handover"
                          >
                            <Trash2 className="w-4 h-4" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Empty State */}
      {!loading && !error && handovers.length === 0 && (
        <Card>
          <CardContent className="pt-6">
            <div className="text-center py-8">
              <FileText className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No handovers found</h3>
              <p className="text-gray-600 mb-4">
                {searchTerm || (statusFilter !== 'all') || (priorityFilter !== 'all')
                  ? 'Try adjusting your search or filters'
                  : 'Create your first handover to get started'}
              </p>
              {!searchTerm && statusFilter === 'all' && priorityFilter === 'all' && (
                <Button onClick={handleCreateHandover}>
                  <Plus className="w-4 h-4 mr-2" />
                  Create Handover
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex justify-center gap-2">
          <Button
            variant="outline"
            onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
            disabled={currentPage === 1}
          >
            Previous
          </Button>
          <span className="flex items-center px-4 text-sm text-gray-600">
            Page {currentPage} of {totalPages}
          </span>
          <Button
            variant="outline"
            onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
            disabled={currentPage === totalPages}
          >
            Next
          </Button>
        </div>
      )}
    </div>
  );
}
