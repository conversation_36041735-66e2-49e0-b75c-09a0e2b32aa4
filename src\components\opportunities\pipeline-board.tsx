'use client';

import React, { useState, useEffect } from 'react';
import { DragDropContext, Droppable, Draggable, DropResult } from '@hello-pangea/dnd';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { 
  MoreHorizontal, 
  DollarSign, 
  Calendar, 
  User, 
  Building, 
  Eye,
  Edit,
  Trash2,
  Lightbulb
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { OpportunityWithRelations } from '@/services/opportunity-management';
import { PipelineData } from '@/services/opportunity-management';
import { usePermissions } from '@/components/providers/permission-provider';
import { PermissionResource, PermissionAction } from '@/types';
import { toastFunctions as toast } from '@/hooks/use-toast';
import { useRouter } from 'next/navigation';
import { format } from 'date-fns';

interface PipelineBoardProps {
  className?: string;
  onOpportunityClick?: (opportunity: OpportunityWithRelations) => void;
}

export function PipelineBoard({ className, onOpportunityClick }: PipelineBoardProps) {
  const router = useRouter();
  const { hasPermission } = usePermissions();
  const [pipelineData, setPipelineData] = useState<PipelineData | null>(null);
  const [loading, setLoading] = useState(true);
  const [dragDisabled, setDragDisabled] = useState(false);

  // Load pipeline data
  const loadPipelineData = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/opportunities/pipeline');
      const data = await response.json();
      
      if (response.ok) {
        setPipelineData(data);
      } else {
        toast.error('Failed to load pipeline data');
      }
    } catch (error) {
      console.error('Error loading pipeline data:', error);
      toast.error('Failed to load pipeline data');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadPipelineData();
  }, []);

  // Handle drag end
  const handleDragEnd = async (result: DropResult) => {
    if (!result.destination || !pipelineData) return;

    const { source, destination, draggableId } = result;
    
    // If dropped in the same position, do nothing
    if (source.droppableId === destination.droppableId && source.index === destination.index) {
      return;
    }

    // Check permission to update opportunities
    if (!hasPermission(PermissionResource.OPPORTUNITIES, PermissionAction.UPDATE)) {
      toast.error('You do not have permission to move opportunities');
      return;
    }

    const sourceStageIndex = pipelineData.stages.findIndex(stage => stage.id === source.droppableId);
    const destStageIndex = pipelineData.stages.findIndex(stage => stage.id === destination.droppableId);
    
    if (sourceStageIndex === -1 || destStageIndex === -1) return;

    const sourceStage = pipelineData.stages[sourceStageIndex];
    const destStage = pipelineData.stages[destStageIndex];
    
    // Optimistically update the UI
    const newPipelineData = { ...pipelineData };
    const [movedOpportunity] = sourceStage.opportunities.splice(source.index, 1);
    destStage.opportunities.splice(destination.index, 0, movedOpportunity);
    
    // Update stage counts and values
    sourceStage.count = sourceStage.opportunities.length;
    sourceStage.totalValue = sourceStage.opportunities.reduce((sum, opp) => 
      sum + (opp.value ? Number(opp.value) : 0), 0
    );
    
    destStage.count = destStage.opportunities.length;
    destStage.totalValue = destStage.opportunities.reduce((sum, opp) => 
      sum + (opp.value ? Number(opp.value) : 0), 0
    );

    setPipelineData(newPipelineData);

    // Update the opportunity stage on the server
    try {
      const response = await fetch(`/api/opportunities/${draggableId}/stage`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          stage: destStage.name,
          notes: `Moved from ${sourceStage.name} to ${destStage.name} via pipeline`
        })
      });

      if (!response.ok) {
        // Revert the optimistic update
        loadPipelineData();
        toast.error('Failed to update opportunity stage');
      } else {
        toast.success(`Opportunity moved to ${destStage.name}`);
      }
    } catch (error) {
      console.error('Error updating opportunity stage:', error);
      loadPipelineData();
      toast.error('Failed to update opportunity stage');
    }
  };

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  // Get initials for avatar
  const getInitials = (firstName?: string | null, lastName?: string | null) => {
    if (!firstName && !lastName) return 'U';
    return `${firstName?.[0] || ''}${lastName?.[0] || ''}`.toUpperCase();
  };

  // Opportunity card component
  const OpportunityCard = ({ opportunity, index }: { opportunity: OpportunityWithRelations; index: number }) => (
    <Draggable 
      draggableId={opportunity.id} 
      index={index}
      isDragDisabled={dragDisabled || !hasPermission(PermissionResource.OPPORTUNITIES, PermissionAction.UPDATE)}
    >
      {(provided) => (
        <div
          ref={provided.innerRef}
          {...provided.draggableProps}
          {...provided.dragHandleProps}
          className="mb-3"
        >
          <Card className="cursor-pointer">
            <CardContent className="p-4">
              <div className="flex items-start justify-between mb-2">
                <h4 className="font-medium text-sm line-clamp-2 flex-1 mr-2">
                  {opportunity.title}
                </h4>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem onClick={() => router.push(`/opportunities/${opportunity.id}`)}>
                      <Eye className="mr-2 h-4 w-4" />
                      View Details
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => router.push(`/opportunities/${opportunity.id}/insights`)}>
                      <Lightbulb className="mr-2 h-4 w-4" />
                      AI Insights
                    </DropdownMenuItem>
                    {hasPermission(PermissionResource.OPPORTUNITIES, PermissionAction.UPDATE) && (
                      <DropdownMenuItem onClick={() => router.push(`/opportunities/${opportunity.id}/edit`)}>
                        <Edit className="mr-2 h-4 w-4" />
                        Edit
                      </DropdownMenuItem>
                    )}
                    {hasPermission(PermissionResource.OPPORTUNITIES, PermissionAction.DELETE) && (
                      <DropdownMenuItem className="text-red-600">
                        <Trash2 className="mr-2 h-4 w-4" />
                        Delete
                      </DropdownMenuItem>
                    )}
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>

              {/* Value and Probability */}
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center text-green-600">
                  <DollarSign className="h-4 w-4 mr-1" />
                  <span className="font-semibold text-sm">
                    {opportunity.value ? formatCurrency(Number(opportunity.value)) : 'No value'}
                  </span>
                </div>
                <Badge variant="outline" className="text-xs">
                  {opportunity.probability}%
                </Badge>
              </div>

              {/* Contact and Company */}
              <div className="space-y-1 mb-3">
                {opportunity.contact && (
                  <div className="flex items-center text-xs text-gray-600">
                    <User className="h-3 w-3 mr-1" />
                    <span className="truncate">
                      {opportunity.contact.firstName} {opportunity.contact.lastName}
                    </span>
                  </div>
                )}
                {opportunity.company && (
                  <div className="flex items-center text-xs text-gray-600">
                    <Building className="h-3 w-3 mr-1" />
                    <span className="truncate">{opportunity.company.name}</span>
                  </div>
                )}
              </div>

              {/* Expected Close Date */}
              {opportunity.expectedCloseDate && (
                <div className="flex items-center text-xs text-gray-500 mb-3">
                  <Calendar className="h-3 w-3 mr-1" />
                  <span>{format(new Date(opportunity.expectedCloseDate), 'MMM dd, yyyy')}</span>
                </div>
              )}

              {/* Owner */}
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Avatar className="h-6 w-6">
                    <AvatarImage src={opportunity.owner?.avatar} />
                    <AvatarFallback className="text-xs">
                      {getInitials(opportunity.owner?.firstName, opportunity.owner?.lastName)}
                    </AvatarFallback>
                  </Avatar>
                  <span className="text-xs text-gray-600 truncate">
                    {opportunity.owner ? 
                      `${opportunity.owner.firstName} ${opportunity.owner.lastName}` : 
                      'Unassigned'
                    }
                  </span>
                </div>
                
                {/* Priority indicator */}
                <div className={`w-2 h-2 rounded-full ${
                  opportunity.priority === 'urgent' ? 'bg-red-500' :
                  opportunity.priority === 'high' ? 'bg-orange-500' :
                  opportunity.priority === 'medium' ? 'bg-blue-500' :
                  'bg-gray-400'
                }`} />
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </Draggable>
  );

  // Stage column component
  const StageColumn = ({ stage }: { stage: any }) => (
    <div className="flex-1 min-w-80">
      <Card className="h-full">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <div 
                className="w-3 h-3 rounded-full" 
                style={{ backgroundColor: stage.color }}
              />
              <CardTitle className="text-sm font-medium">{stage.name}</CardTitle>
            </div>
            <Badge variant="secondary" className="text-xs">
              {stage.count}
            </Badge>
          </div>
          <div className="text-xs text-gray-500">
            {formatCurrency(stage.totalValue)}
          </div>
        </CardHeader>
        <CardContent className="pt-0">
          <Droppable droppableId={stage.id}>
            {(provided) => (
              <div
                ref={provided.innerRef}
                {...provided.droppableProps}
                className="min-h-96"
              >
                {stage.opportunities.map((opportunity: OpportunityWithRelations, index: number) => (
                  <OpportunityCard
                    key={opportunity.id}
                    opportunity={opportunity}
                    index={index}
                  />
                ))}
                {provided.placeholder}
              </div>
            )}
          </Droppable>
        </CardContent>
      </Card>
    </div>
  );

  if (loading) {
    return (
      <div className="flex justify-center items-center h-96">
        <div className="rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!pipelineData) {
    return (
      <Card>
        <CardContent className="p-8 text-center">
          <p className="text-gray-500">Failed to load pipeline data</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Pipeline Summary */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <p className="text-2xl font-bold text-gray-900">{pipelineData.totalCount}</p>
              <p className="text-sm text-gray-600">Total Opportunities</p>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <p className="text-2xl font-bold text-gray-900">{formatCurrency(pipelineData.totalValue)}</p>
              <p className="text-sm text-gray-600">Total Pipeline Value</p>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <p className="text-2xl font-bold text-gray-900">{formatCurrency(pipelineData.weightedValue)}</p>
              <p className="text-sm text-gray-600">Weighted Value</p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Pipeline Board */}
      <DragDropContext onDragEnd={handleDragEnd}>
        <div className="flex space-x-4 overflow-x-auto pb-4">
          {pipelineData.stages.map((stage) => (
            <StageColumn key={stage.id} stage={stage} />
          ))}
        </div>
      </DragDropContext>
    </div>
  );
}
