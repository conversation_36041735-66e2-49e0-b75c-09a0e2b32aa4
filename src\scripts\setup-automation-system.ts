/**
 * Setup script for Process Automation System
 * This script initializes the automation system for existing tenants
 */

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function setupAutomationSystem() {
  console.log('🚀 Setting up Process Automation System...\n');

  try {
    // Check if automation tables exist by trying to query them
    console.log('1. Checking database schema...');
    
    try {
      await prisma.$queryRaw`SELECT 1 FROM automation_triggers LIMIT 1`;
      console.log('✅ Automation tables already exist');
    } catch (error) {
      console.log('⚠️  Automation tables do not exist yet');
      console.log('   Please run: npm run db:push to create the tables');
      console.log('   Or run: npm run db:migrate to create a migration');
      return false;
    }

    // Get all active tenants
    console.log('\n2. Finding active tenants...');
    const tenants = await prisma.tenant.findMany({
      where: { status: 'active' },
      select: { id: true, name: true }
    });

    console.log(`   Found ${tenants.length} active tenants`);

    // Setup default triggers for each tenant
    console.log('\n3. Setting up default automation triggers...');
    
    for (const tenant of tenants) {
      console.log(`\n   🏢 Setting up automation for: ${tenant.name}`);
      
      // Check if triggers already exist
      const existingTriggers = await prisma.automationTrigger.count({
        where: { tenantId: tenant.id }
      });

      if (existingTriggers > 0) {
        console.log(`      ⏭️  Skipping - ${existingTriggers} triggers already exist`);
        continue;
      }

      // Create default triggers
      const defaultTriggers = [
        {
          name: 'Opportunity Closed Won - Create Handover',
          description: 'Automatically create a handover when an opportunity is marked as "Closed Won"',
          triggerType: 'opportunity_status_change',
          triggerConditions: { toStatus: 'Closed Won' },
          actions: [
            {
              type: 'create_handover',
              config: {
                priority: 'high',
                scheduledDelayHours: 24
              }
            },
            {
              type: 'send_notification',
              config: {
                recipients: ['sales_rep', 'delivery_manager'],
                template: 'handover_created'
              }
            }
          ],
          isActive: true,
          priority: 9
        },
        {
          name: 'Opportunity Moved to Proposal - Notify Team',
          description: 'Send notifications when an opportunity moves to proposal stage',
          triggerType: 'opportunity_status_change',
          triggerConditions: { toStatus: 'Proposal' },
          actions: [
            {
              type: 'send_notification',
              config: {
                recipients: ['sales_rep', 'sales_manager'],
                template: 'proposal_stage_reached'
              }
            }
          ],
          isActive: true,
          priority: 5
        },
        {
          name: 'Opportunity Moved to Negotiation - High Priority',
          description: 'Escalate notifications when opportunity reaches negotiation stage',
          triggerType: 'opportunity_status_change',
          triggerConditions: { toStatus: 'Negotiation' },
          actions: [
            {
              type: 'send_notification',
              config: {
                recipients: ['sales_rep', 'sales_manager', 'delivery_manager'],
                template: 'negotiation_stage_reached',
                priority: 'high'
              }
            }
          ],
          isActive: true,
          priority: 8
        }
      ];

      // Create triggers
      for (const triggerData of defaultTriggers) {
        try {
          await prisma.automationTrigger.create({
            data: {
              tenantId: tenant.id,
              ...triggerData,
              createdById: 'system'
            }
          });
          console.log(`      ✅ Created: ${triggerData.name}`);
        } catch (error) {
          console.log(`      ❌ Failed to create: ${triggerData.name}`);
          console.error(`         Error: ${error}`);
        }
      }
    }

    console.log('\n4. Automation system setup completed! 🎉');
    console.log('\n📋 Next steps:');
    console.log('   1. Access the automation dashboard at: /admin/automation');
    console.log('   2. Test opportunity stage changes to see automation in action');
    console.log('   3. Monitor automation executions in the dashboard');
    
    return true;

  } catch (error) {
    console.error('❌ Setup failed:', error);
    return false;
  } finally {
    await prisma.$disconnect();
  }
}

async function testAutomationTrigger() {
  console.log('\n🧪 Testing automation trigger...');
  
  try {
    // Find a test opportunity
    const opportunity = await prisma.opportunity.findFirst({
      where: { stage: { not: 'Closed Won' } },
      include: { contact: true, company: true }
    });

    if (!opportunity) {
      console.log('   ⚠️  No test opportunity found');
      return;
    }

    console.log(`   Found test opportunity: ${opportunity.title}`);
    console.log(`   Current stage: ${opportunity.stage}`);
    console.log('   💡 To test automation:');
    console.log(`      1. Go to /opportunities/${opportunity.id}`);
    console.log('      2. Change the stage to "Closed Won"');
    console.log('      3. Check the automation dashboard for execution logs');

  } catch (error) {
    console.error('   ❌ Test setup failed:', error);
  }
}

// Main execution
async function main() {
  const success = await setupAutomationSystem();
  
  if (success) {
    await testAutomationTrigger();
  }
}

// Run if executed directly
if (require.main === module) {
  main().catch(console.error);
}

export { setupAutomationSystem, testAutomationTrigger };
