import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { emailLeadCaptureService, emailLeadCaptureSchema } from '@/services/email-lead-capture';
import { hasPermission } from '@/lib/permissions';
import { PermissionResource, PermissionAction } from '@/types';

export async function POST(request: NextRequest) {
  const tenantId = request.headers.get('x-tenant-id');

  try {
    // Email capture API is open for external integrations (webhooks, email forwarding, etc.)
    if (!tenantId) {
      return NextResponse.json({ error: 'Tenant ID required' }, { status: 400 });
    }

    const body = await request.json();
    console.log('📧 Email capture request body:', body);
    console.log('📧 Tenant ID:', tenantId);

    // Validate request body
    const validationResult = emailLeadCaptureSchema.safeParse(body);
    if (!validationResult.success) {
      console.error('❌ Email validation failed:', validationResult.error.errors);
      return NextResponse.json(
        {
          error: 'Validation failed',
          details: validationResult.error.errors
        },
        { status: 400 }
      );
    }

    console.log('✅ Email validation passed, processing...');
    const result = await emailLeadCaptureService.processIncomingEmail(
      tenantId,
      validationResult.data
    );

    console.log('✅ Email processed successfully:', {
      emailCaptureId: result.emailCapture.id,
      leadCreated: !!result.lead,
      contactCreated: !!result.contact
    });

    return NextResponse.json({
      success: true,
      data: {
        emailCapture: result.emailCapture,
        lead: result.lead,
        contact: result.contact,
        sentimentAnalysis: result.sentimentAnalysis
      }
    }, { status: 201 });
  } catch (error) {
    console.error('❌ Error processing email lead capture:', error);

    // Return more detailed error information
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    const errorStack = error instanceof Error ? error.stack : undefined;

    console.error('Error details:', {
      message: errorMessage,
      stack: errorStack,
      tenantId,
      timestamp: new Date().toISOString()
    });

    return NextResponse.json(
      {
        error: 'Failed to process email',
        details: errorMessage,
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const tenantId = request.headers.get('x-tenant-id');
    if (!tenantId) {
      return NextResponse.json({ error: 'Tenant ID required' }, { status: 400 });
    }

    // Check permissions
    const canView = await hasPermission(
      PermissionResource.LEADS,
      PermissionAction.READ,
      tenantId
    );

    if (!canView) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '50');
    const offset = parseInt(searchParams.get('offset') || '0');
    const processed = searchParams.get('processed') === 'true' ? true : 
                     searchParams.get('processed') === 'false' ? false : undefined;

    const result = await emailLeadCaptureService.getEmailCaptures(
      tenantId,
      { limit, offset, processed }
    );

    return NextResponse.json({
      success: true,
      data: result.captures,
      total: result.total,
      hasMore: result.total > offset + limit
    });
  } catch (error) {
    console.error('Error fetching email captures:', error);
    return NextResponse.json(
      { error: 'Failed to fetch email captures' },
      { status: 500 }
    );
  }
}
