# Development configuration for CRM Platform with Mermaid PNG support
# This file contains development-specific overrides for docker-compose.yml

services:
  # Development configuration for Next.js app
  app:
    build:
      target: dev
    restart: unless-stopped
    environment:
      - NODE_ENV=development
      # Mermaid PNG generation environment variables for development
      - PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true
      - PUPPETEER_EXECUTABLE_PATH=/usr/bin/chromium-browser
      - PUPPETEER_ARGS=--no-sandbox --disable-setuid-sandbox --disable-dev-shm-usage --disable-accelerated-2d-canvas --no-first-run --no-zygote --disable-gpu
      - MERMAID_TEMP_DIR=/tmp/mermaid
      - CHROME_BIN=/usr/bin/chromium-browser
      # Development-specific Mermaid settings
      - MERMAID_PNG_WIDTH=1200
      - MERMAID_PNG_HEIGHT=800
      - MERMAID_PNG_THEME=default
      - MERMAID_PNG_BACKGROUND=white
      - MERMAID_PNG_SCALE=1.5
      - MER<PERSON><PERSON>_CLEANUP_INTERVAL_HOURS=1
    volumes:
      - .:/app
      - /app/node_modules
      - /app/.next
      - ./uploads:/app/uploads
      - mermaid-temp:/tmp/mermaid
    # Increased shared memory for Chrome in development
    shm_size: 2gb
    # Security options for Chrome in container
    security_opt:
      - seccomp:unconfined
    ports:
      - "3010:3000"
    command: ["npm", "run", "dev"]
    deploy:
      resources:
        limits:
          cpus: '2'
          memory: 2G

  # Development configuration for PostgreSQL
  postgres:
    ports:
      - "5434:5432"
    environment:
      - POSTGRES_USER=admin
      - POSTGRES_PASSWORD=admin
      - POSTGRES_DB=crm_platform
    volumes:
      - postgres-dev-data:/var/lib/postgresql/data
      - ./scripts/init-db:/docker-entrypoint-initdb.d:ro

  # Development configuration for Redis
  redis:
    ports:
      - "6380:6379"
    volumes:
      - redis-dev-data:/data
      - ./config/redis/redis.conf:/usr/local/etc/redis/redis.conf:ro

  # Development tools
  pgadmin:
    ports:
      - "8089:80"
    volumes:
      - pgadmin-dev-data:/var/lib/pgadmin

  # Mermaid PNG test service (development only)
  mermaid-test:
    build:
      context: .
      dockerfile: Dockerfile
      target: base
    container_name: crm-mermaid-test
    working_dir: /app
    environment:
      - PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true
      - PUPPETEER_EXECUTABLE_PATH=/usr/bin/chromium-browser
      - PUPPETEER_ARGS=--no-sandbox --disable-setuid-sandbox --disable-dev-shm-usage
    volumes:
      - .:/app
      - mermaid-temp:/tmp/mermaid
    shm_size: 1gb
    security_opt:
      - seccomp:unconfined
    networks:
      - crm-network
    command: ["tail", "-f", "/dev/null"]  # Keep container running for testing
    profiles:
      - testing

networks:
  crm-network:
    driver: bridge

volumes:
  postgres-dev-data:
  redis-dev-data:
  mermaid-temp:
  pgadmin-dev-data:
