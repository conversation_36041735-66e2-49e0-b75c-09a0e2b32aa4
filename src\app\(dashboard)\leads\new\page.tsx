'use client';

import { PermissionGate } from '@/components/ui/permission-gate';
import { PermissionResource, PermissionAction } from '@/types';
import { LeadForm } from '@/components/leads/lead-form';
import { Card, CardContent } from '@/components/ui/card';
import { EmptyState } from '@/components/ui/empty-state';
import { ExclamationTriangleIcon, ArrowLeftIcon } from '@heroicons/react/24/outline';
import { PageLayout } from '@/components/layout/page-layout';
import { Button } from '@/components/ui/button';
import { useRouter, useSearchParams } from 'next/navigation';
import { useMemo } from 'react';

export default function NewLeadPage() {
  const router = useRouter();
  const searchParams = useSearchParams();

  // Extract URL parameters for pre-populating the form
  const initialData = useMemo(() => {
    const contactId = searchParams.get('contactId');
    const companyId = searchParams.get('companyId');
    const contactName = searchParams.get('contactName');
    const email = searchParams.get('email');
    const phone = searchParams.get('phone');

    // Generate a default title if we have contact information
    let defaultTitle = '';
    if (contactName) {
      defaultTitle = `Lead for ${contactName}`;
    }

    return {
      contactId: contactId || undefined,
      companyId: companyId || undefined,
      title: defaultTitle,
      // Store additional contact info for reference
      _contactInfo: {
        name: contactName,
        email,
        phone
      }
    };
  }, [searchParams]);

  const actions = (
    <Button variant="outline" size="sm" onClick={() => router.push('/leads')}>
      <ArrowLeftIcon className="h-4 w-4 mr-2" />
      Back to Leads
    </Button>
  );

  return (
    <PermissionGate
      resource={PermissionResource.LEADS}
      action={PermissionAction.CREATE}
      fallback={
        <PageLayout>
          <Card>
            <CardContent className="p-8">
              <EmptyState
                icon={<ExclamationTriangleIcon className="w-12 h-12 text-muted-foreground" />}
                title="Access Denied"
                description="You don't have permission to create leads. Please contact your administrator for access."
                action={{
                  label: 'Back to Leads',
                  onClick: () => router.push('/leads'),
                  variant: 'primary'
                }}
              />
            </CardContent>
          </Card>
        </PageLayout>
      }
    >
      <PageLayout
        title="Add New Lead"
        description="Create a new lead in your CRM system"
        actions={actions}
      >
        <LeadForm initialData={initialData} />
      </PageLayout>
    </PermissionGate>
  );
}
