import { prisma } from '@/lib/prisma';

export interface FeatureFlag {
  id: string;
  name: string;
  description: string | null;
  isEnabled: boolean;
  requiredPlanLevel: number;
  createdAt: Date;
}

export interface CreateFeatureFlagData {
  name: string;
  description?: string;
  isEnabled?: boolean;
  requiredPlanLevel?: number;
}

export interface UpdateFeatureFlagData {
  name?: string;
  description?: string;
  isEnabled?: boolean;
  requiredPlanLevel?: number;
}

export class FeatureFlagService {
  // Get all feature flags
  async getAllFeatureFlags(): Promise<FeatureFlag[]> {
    return await prisma.featureFlag.findMany({
      orderBy: { name: 'asc' }
    });
  }

  // Get feature flag by ID
  async getFeatureFlagById(id: string): Promise<FeatureFlag | null> {
    return await prisma.featureFlag.findUnique({
      where: { id }
    });
  }

  // Get feature flag by name
  async getFeatureFlagByName(name: string): Promise<FeatureFlag | null> {
    return await prisma.featureFlag.findUnique({
      where: { name }
    });
  }

  // Create new feature flag
  async createFeatureFlag(data: CreateFeatureFlagData): Promise<FeatureFlag> {
    // Check if feature flag with same name already exists
    const existing = await this.getFeatureFlagByName(data.name);
    if (existing) {
      throw new Error('Feature flag with this name already exists');
    }

    return await prisma.featureFlag.create({
      data: {
        name: data.name,
        description: data.description || null,
        isEnabled: data.isEnabled ?? true,
        requiredPlanLevel: data.requiredPlanLevel ?? 1
      }
    });
  }

  // Update feature flag
  async updateFeatureFlag(id: string, data: UpdateFeatureFlagData): Promise<FeatureFlag> {
    // Check if feature flag exists
    const existing = await this.getFeatureFlagById(id);
    if (!existing) {
      throw new Error('Feature flag not found');
    }

    // If name is being updated, check for conflicts
    if (data.name && data.name !== existing.name) {
      const nameConflict = await this.getFeatureFlagByName(data.name);
      if (nameConflict) {
        throw new Error('Feature flag with this name already exists');
      }
    }

    return await prisma.featureFlag.update({
      where: { id },
      data: {
        ...(data.name && { name: data.name }),
        ...(data.description !== undefined && { description: data.description }),
        ...(data.isEnabled !== undefined && { isEnabled: data.isEnabled }),
        ...(data.requiredPlanLevel !== undefined && { requiredPlanLevel: data.requiredPlanLevel })
      }
    });
  }

  // Delete feature flag
  async deleteFeatureFlag(id: string): Promise<void> {
    const existing = await this.getFeatureFlagById(id);
    if (!existing) {
      throw new Error('Feature flag not found');
    }

    await prisma.featureFlag.delete({
      where: { id }
    });
  }

  // Toggle feature flag status
  async toggleFeatureFlag(id: string): Promise<FeatureFlag> {
    const existing = await this.getFeatureFlagById(id);
    if (!existing) {
      throw new Error('Feature flag not found');
    }

    return await prisma.featureFlag.update({
      where: { id },
      data: { isEnabled: !existing.isEnabled }
    });
  }

  // Check if feature is enabled for a specific plan level
  async isFeatureEnabledForPlan(featureName: string, planLevel: number): Promise<boolean> {
    const featureFlag = await this.getFeatureFlagByName(featureName);
    
    if (!featureFlag) {
      return false; // Feature doesn't exist, so it's disabled
    }

    return featureFlag.isEnabled && planLevel >= featureFlag.requiredPlanLevel;
  }

  // Get enabled features for a plan level
  async getEnabledFeaturesForPlan(planLevel: number): Promise<string[]> {
    const features = await prisma.featureFlag.findMany({
      where: {
        isEnabled: true,
        requiredPlanLevel: { lte: planLevel }
      },
      select: { name: true }
    });

    return features.map(f => f.name);
  }

  // Bulk update feature flags
  async bulkUpdateFeatureFlags(updates: Array<{ id: string; isEnabled: boolean }>): Promise<void> {
    await prisma.$transaction(
      updates.map(update => 
        prisma.featureFlag.update({
          where: { id: update.id },
          data: { isEnabled: update.isEnabled }
        })
      )
    );
  }

  // Get feature flag usage statistics
  async getFeatureFlagStats(): Promise<{
    total: number;
    enabled: number;
    disabled: number;
    byPlanLevel: Record<number, number>;
  }> {
    const [total, enabled, allFlags] = await Promise.all([
      prisma.featureFlag.count(),
      prisma.featureFlag.count({ where: { isEnabled: true } }),
      prisma.featureFlag.findMany({ select: { requiredPlanLevel: true } })
    ]);

    const byPlanLevel = allFlags.reduce((acc, flag) => {
      acc[flag.requiredPlanLevel] = (acc[flag.requiredPlanLevel] || 0) + 1;
      return acc;
    }, {} as Record<number, number>);

    return {
      total,
      enabled,
      disabled: total - enabled,
      byPlanLevel
    };
  }
}
