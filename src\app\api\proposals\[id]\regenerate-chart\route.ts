import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { hasPermission } from '@/lib/permissions';
import { PermissionResource, PermissionAction } from '@/types';
import { aiProposalService } from '@/services/ai-proposal-generation';
import { aiService } from '@/services/ai-service';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';
import { ProposalChartType } from '@/types/proposal';

// Request validation schema
const regenerateChartSchema = z.object({
  chartId: z.string().min(1, 'Chart ID is required'),
  errorContext: z.object({
    error: z.string(),
    errorType: z.enum(['syntax', 'parsing', 'rendering', 'timeout', 'unknown']).optional(),
    suggestions: z.array(z.string()).optional(),
    line: z.number().optional(),
    column: z.number().optional()
  }).optional(),
  customPrompt: z.string().optional(),
  preserveChartType: z.boolean().default(true)
});

/**
 * POST /api/proposals/[id]/regenerate-chart
 * Regenerate a specific chart with error context for better AI generation
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user's current tenant
    const currentTenant = session.user.tenants?.[0];
    if (!currentTenant) {
      return NextResponse.json({ error: 'No tenant access' }, { status: 403 });
    }

    // Check permission for proposal management
    const hasAccess = await hasPermission(
      PermissionResource.PROPOSALS,
      PermissionAction.UPDATE,
      currentTenant.id
    );

    if (!hasAccess) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const proposalId = params.id;
    const body = await request.json();
    
    // Validate request data
    const validatedData = regenerateChartSchema.parse(body);

    // Verify proposal exists and belongs to tenant
    const proposal = await prisma.proposal.findFirst({
      where: {
        id: proposalId,
        tenantId: currentTenant.id
      },
      include: {
        opportunity: {
          include: {
            company: true,
            contact: true
          }
        }
      }
    });

    if (!proposal) {
      return NextResponse.json(
        { error: 'Proposal not found or access denied' },
        { status: 404 }
      );
    }

    // Verify chart exists and belongs to the proposal
    const chart = await prisma.proposalChart.findFirst({
      where: {
        id: validatedData.chartId,
        proposalId,
        tenantId: currentTenant.id
      }
    });

    if (!chart) {
      return NextResponse.json(
        { error: 'Chart not found or access denied' },
        { status: 404 }
      );
    }

    // Check if proposal is in a state that allows regeneration
    if (proposal.generationStatus === 'generating') {
      return NextResponse.json(
        { error: 'Cannot regenerate chart while proposal is being generated' },
        { status: 409 }
      );
    }

    // Get file contexts for the proposal
    const fileContexts = await getProposalFileContexts(proposalId, currentTenant.id);

    // Mark chart as being regenerated
    await prisma.proposalChart.update({
      where: { id: validatedData.chartId },
      data: {
        status: 'generating'
      }
    });

    // Build enhanced prompt with error context
    const enhancedPrompt = buildErrorAwarePrompt(
      chart,
      validatedData.errorContext,
      validatedData.customPrompt
    );

    // Generate new mermaid code directly without creating a new chart record
    const mermaidCode = await generateMermaidCodeOnly(
      {
        proposalId,
        chartType: chart.chartType as any,
        title: chart.title,
        description: chart.description,
        customPrompt: enhancedPrompt
      },
      proposal,
      fileContexts
    );

    // Update the existing chart with new content
    const updatedChart = await prisma.proposalChart.update({
      where: { id: validatedData.chartId },
      data: {
        mermaidCode,
        status: 'generated',
        generatedAt: new Date(),
        updatedAt: new Date(),
        // Store error context for future reference
        errorContext: validatedData.errorContext ? JSON.stringify(validatedData.errorContext) : null
      },
      include: {
        createdBy: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true
          }
        }
      }
    });

    return NextResponse.json({
      success: true,
      data: {
        chart: updatedChart,
        regenerationStats: {
          originalError: validatedData.errorContext?.error,
          errorType: validatedData.errorContext?.errorType,
          enhancedPromptUsed: !!validatedData.errorContext,
          mermaidCodeLength: mermaidCode.length
        }
      },
      message: 'Chart regenerated successfully with error context'
    });

  } catch (error) {
    console.error('Error regenerating chart:', error);
    
    // Reset chart status on error
    if (error instanceof Error && error.message.includes('chartId')) {
      try {
        const body = await request.json();
        const { chartId } = body;
        await prisma.proposalChart.update({
          where: { id: chartId },
          data: { status: 'failed' }
        });
      } catch (resetError) {
        console.error('Error resetting chart status:', resetError);
      }
    }
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          error: 'Invalid request data', 
          details: error.errors.map(e => ({
            field: e.path.join('.'),
            message: e.message
          }))
        },
        { status: 400 }
      );
    }

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * Build an enhanced prompt that includes error context and Word document optimization
 */
function buildErrorAwarePrompt(
  chart: any,
  errorContext?: any,
  customPrompt?: string
): string {
  const chartTypeFormatted = chart.chartType.replace(/_/g, ' ');
  let prompt = customPrompt || `Generate a professional ${chartTypeFormatted} Mermaid diagram for: ${chart.title}`;

  // Add Word document optimization requirements
  prompt += '\n\nWORD DOCUMENT OPTIMIZATION:';
  prompt += '\n- Design for standard Word document pages (8.5" x 11")';
  prompt += '\n- Use horizontal layouts (LR) when possible for better width utilization';
  prompt += '\n- Limit to 6-12 nodes maximum to prevent overcrowding';
  prompt += '\n- Ensure balanced, well-aligned layout with proper spacing';
  prompt += '\n- Create professional presentation suitable for business documents';

  // Add layout-specific guidance based on chart type
  const layoutGuidance = {
    architecture_high_level: 'Use "graph LR" layout with 5-8 main components arranged horizontally',
    architecture_low_level: 'Use "flowchart TD" layout with 8-12 components in logical layers',
    project_timeline: 'Use "gantt" format with 4-6 phases and clear milestone markers',
    system_flow: 'Use "flowchart TD" layout with 6-10 process steps and decision points'
  };

  if (layoutGuidance[chart.chartType as keyof typeof layoutGuidance]) {
    prompt += `\n\nLAYOUT GUIDANCE: ${layoutGuidance[chart.chartType as keyof typeof layoutGuidance]}`;
  }

  if (errorContext) {
    prompt += '\n\nIMPORTANT ERROR CONTEXT:';
    prompt += `\nThe previous chart generation failed with: ${errorContext.error}`;

    if (errorContext.errorType) {
      prompt += `\nError type: ${errorContext.errorType}`;
    }

    if (errorContext.suggestions && errorContext.suggestions.length > 0) {
      prompt += '\nTo avoid this error, please:';
      errorContext.suggestions.forEach((suggestion: string, index: number) => {
        prompt += `\n${index + 1}. ${suggestion}`;
      });
    }

    // Add specific instructions based on error type
    switch (errorContext.errorType) {
      case 'syntax':
      case 'parsing':
        prompt += '\n\nCRITICAL SYNTAX REQUIREMENTS:';
        prompt += '\n- Use only simple text in node labels (no quotes, parentheses, or special characters)';
        prompt += '\n- Ensure all brackets are properly matched';
        prompt += '\n- Use hyphens or spaces instead of special characters';
        prompt += '\n- Keep node labels concise (2-3 words maximum)';
        prompt += '\n- Avoid any problematic characters that caused the previous error';
        break;
      case 'timeout':
        prompt += '\n\nSIMPLIFICATION REQUIREMENTS:';
        prompt += '\n- Create a simpler diagram with maximum 8 nodes';
        prompt += '\n- Reduce the number of connections between nodes';
        prompt += '\n- Focus only on the most essential components';
        prompt += '\n- Use straightforward, linear flow patterns';
        break;
      case 'rendering':
        prompt += '\n\nRENDERING REQUIREMENTS:';
        prompt += '\n- Use only standard Mermaid syntax';
        prompt += '\n- Avoid complex nested structures';
        prompt += '\n- Ensure all node references are properly defined';
        prompt += '\n- Use consistent node naming throughout';
        break;
    }
  }

  // Add final requirements
  prompt += '\n\nFINAL REQUIREMENTS:';
  prompt += '\n- Generate only valid Mermaid syntax without explanations or markdown formatting';
  prompt += '\n- Use professional, concise labels suitable for business documents';
  prompt += '\n- Ensure the diagram is clean, well-organized, and document-ready';
  prompt += '\n- Test that all syntax is valid and will render without errors';

  return prompt;
}

/**
 * Generate mermaid code only without creating a new chart record
 */
async function generateMermaidCodeOnly(
  request: {
    proposalId: string;
    chartType: ProposalChartType;
    title: string;
    description: string;
    customPrompt?: string;
  },
  proposal: any,
  fileContexts?: any[]
): Promise<string> {
  // Build chart-specific prompt (similar to aiProposalService.buildChartPrompt)
  const baseContext = `
Project: ${proposal.title}
Company: ${proposal.opportunity?.company?.name || 'Unknown'}
Industry: ${proposal.opportunity?.company?.industry || 'Unknown'}
`;

  // Add file context if available
  const fileContext = fileContexts?.length
    ? `\n\nReference Documents:\n${fileContexts.map(ctx => `- ${ctx.name}${ctx.fileUri ? ` (File URI: ${ctx.fileUri})` : ''}`).join('\n')}`
    : '';

  const chartPrompts = {
    architecture_high_level: 'Generate a high-level system architecture Mermaid diagram showing major components and their relationships. Focus on 5-8 main components with clear connections. Use horizontal layout (LR) for better Word document fit.',
    architecture_low_level: 'Generate a detailed technical architecture Mermaid diagram with specific technologies and data flow. Include 8-12 components with clear data flow paths. Use top-down layout (TD) with proper alignment.',
    project_timeline: 'Generate a project timeline Mermaid Gantt chart showing phases, milestones, and dependencies. Include 4-6 major phases with realistic timelines. Ensure proper date formatting and clear milestone markers.',
    system_flow: 'Generate a system flow Mermaid diagram showing user interactions and data processing flow. Include 6-10 process steps with decision points. Use flowchart format with clear directional flow.'
  };

  const prompt = `${baseContext}${fileContext}

Task: ${request.customPrompt || chartPrompts[request.chartType]}

WORD DOCUMENT OPTIMIZATION REQUIREMENTS:
- Create diagrams optimized for standard Word document pages (8.5" x 11")
- Use appropriate layout direction for the chart type:
  * Architecture diagrams: Use "graph LR" (left-to-right) for better width utilization
  * Process flows: Use "flowchart TD" (top-down) for logical flow
  * Timelines: Use "gantt" format with proper date ranges
- Limit to 6-12 nodes maximum to prevent overcrowding
- Ensure balanced, symmetrical layout with proper spacing

CONTENT AND STYLING REQUIREMENTS:
- Use concise, professional labels (2-4 words maximum per node)
- Avoid special characters, quotes, parentheses, or symbols in labels
- Use consistent node shapes throughout the diagram
- Ensure all text is readable and professional
- Create logical, easy-to-follow connections

TECHNICAL REQUIREMENTS:
- Generate only valid Mermaid syntax without explanations
- Do not include markdown formatting or conversational text
- Start with appropriate diagram declaration (graph LR, flowchart TD, gantt, etc.)
- Use consistent styling and formatting throughout
- Ensure the diagram renders properly in document formats`;

  // Get system prompt for chart generation
  const chartTypeFormatted = request.chartType.replace(/_/g, ' ');
  const systemPrompt = `You are an expert system architect specializing in creating professional diagrams for business documents.

Generate valid Mermaid diagram syntax for ${chartTypeFormatted} diagrams that are optimized for Word documents with the following requirements:

DOCUMENT OPTIMIZATION:
- Create diagrams that fit well within standard Word document pages (8.5" x 11")
- Use horizontal layouts (LR) when possible to maximize width utilization
- Limit diagram width to prevent horizontal scrolling in documents
- Ensure proper spacing and alignment for professional presentation

LAYOUT REQUIREMENTS:
- Use clear, well-aligned node positioning
- Maintain consistent spacing between elements
- Create balanced, symmetrical layouts when possible
- Avoid overcrowded or cluttered arrangements

CONTENT STANDARDS:
- Use concise, professional node labels (max 3-4 words per label)
- Avoid special characters, quotes, or parentheses in labels
- Use simple, clean text that renders well in documents
- Ensure all connections are logical and easy to follow

OUTPUT FORMAT:
- Provide only the Mermaid code without explanations or markdown formatting
- Start with appropriate diagram type and direction (e.g., "graph LR" or "flowchart TD")
- Use consistent node styling throughout the diagram`;

  // Generate AI response
  const response = await aiService.generateResponse({
    systemPrompt,
    userPrompt: prompt,
    context: 'chart_generation'
  }, {
    temperature: 0.6,
    maxTokens: 1500
  });

  // Extract Mermaid code from response
  const mermaidMatch = response.content.match(/```mermaid\n([\s\S]*?)\n```/);
  if (mermaidMatch) {
    return mermaidMatch[1].trim();
  }

  // If no code block, assume the entire content is Mermaid code
  return response.content.trim();
}

/**
 * Get file contexts for proposal (helper function)
 */
async function getProposalFileContexts(proposalId: string, tenantId: string) {
  // This would typically fetch file contexts from the database
  // For now, return empty array as a placeholder
  return [];
}
