import { NextResponse } from 'next/server';
import { withPermission } from '@/lib/permission-middleware';
import { leadNurturingService, createNurturingCampaignSchema } from '@/services/lead-nurturing';
import { PermissionAction, PermissionResource } from '@/types';
import { AuthenticatedRequest } from '@/lib/permission-middleware';

/**
 * GET /api/leads/nurturing/campaigns/[campaignId]
 * Get a specific nurturing campaign by ID
 */
export const GET = withPermission({
  resource: PermissionResource.LEADS,
  action: PermissionAction.READ,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest) => {
  try {
    // Extract campaignId from URL
    const url = new URL(req.url);
    const pathSegments = url.pathname.split('/');
    const campaignId = pathSegments[pathSegments.length - 1];

    if (!campaignId) {
      return NextResponse.json({ error: 'Campaign ID required' }, { status: 400 });
    }

    const campaign = await leadNurturingService.getCampaign(campaignId, req.tenantId);

    if (!campaign) {
      return NextResponse.json({ error: 'Campaign not found' }, { status: 404 });
    }

    return NextResponse.json({
      success: true,
      data: campaign
    });
  } catch (error) {
    return NextResponse.json(
      { error: 'Failed to fetch campaign' },
      { status: 500 }
    );
  }
});

/**
 * PUT /api/leads/nurturing/campaigns/[campaignId]
 * Update a specific nurturing campaign
 */
export const PUT = withPermission({
  resource: PermissionResource.LEADS,
  action: PermissionAction.UPDATE,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest) => {
  try {
    // Extract campaignId from URL
    const url = new URL(req.url);
    const pathSegments = url.pathname.split('/');
    const campaignId = pathSegments[pathSegments.length - 1];

    if (!campaignId) {
      return NextResponse.json({ error: 'Campaign ID required' }, { status: 400 });
    }

    const body = await req.json();

    // Validate request body
    const validationResult = createNurturingCampaignSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        {
          error: 'Validation failed',
          details: validationResult.error.errors
        },
        { status: 400 }
      );
    }

    const campaign = await leadNurturingService.updateCampaign(
      campaignId,
      req.tenantId,
      validationResult.data,
      req.userId
    );

    return NextResponse.json({
      success: true,
      data: campaign
    });
  } catch (error) {
    return NextResponse.json(
      { error: 'Failed to update campaign' },
      { status: 500 }
    );
  }
});

/**
 * DELETE /api/leads/nurturing/campaigns/[campaignId]
 * Delete a specific nurturing campaign
 */
export const DELETE = withPermission({
  resource: PermissionResource.LEADS,
  action: PermissionAction.DELETE,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest) => {
  try {
    // Extract campaignId from URL
    const url = new URL(req.url);
    const pathSegments = url.pathname.split('/');
    const campaignId = pathSegments[pathSegments.length - 1];

    if (!campaignId) {
      return NextResponse.json({ error: 'Campaign ID required' }, { status: 400 });
    }

    await leadNurturingService.deleteCampaign(campaignId, req.tenantId);

    return NextResponse.json({
      success: true,
      message: 'Campaign deleted successfully'
    });
  } catch (error) {
    return NextResponse.json(
      { error: 'Failed to delete campaign' },
      { status: 500 }
    );
  }
});
