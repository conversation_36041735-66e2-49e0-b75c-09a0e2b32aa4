'use client';

import { useEffect, useState } from 'react';
import {
  <PERSON><PERSON>hart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  AreaChart,
  Area
} from 'recharts';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { TenantDashboardMetrics } from '@/services/tenant-dashboard-analytics';

interface ChartData {
  date: string;
  leads: number;
  opportunities: number;
  contacts: number;
  revenue: number;
}

interface TenantAnalyticsChartProps {
  className?: string;
}

export function TenantAnalyticsChart({ className }: TenantAnalyticsChartProps) {
  const [chartData, setChartData] = useState<ChartData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [timeRange, setTimeRange] = useState("30");
  const [chartType, setChartType] = useState<'line' | 'area'>('area');
  const [activeMetrics, setActiveMetrics] = useState({
    leads: true,
    opportunities: true,
    contacts: true,
    revenue: false // Revenue on different scale, toggle separately
  });

  // Define vibrant colors directly
  const chartColors = {
    contacts: '#22c55e',    // Vibrant Green
    leads: '#3b82f6',       // Vibrant Blue
    opportunities: '#f97316', // Vibrant Orange
    revenue: '#a855f7'      // Vibrant Purple
  };

  useEffect(() => {
    fetchChartData();
  }, [timeRange]);

  const fetchChartData = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await fetch(`/api/dashboard/analytics?type=full&days=${timeRange}`);
      
      if (!response.ok) {
        throw new Error(`Failed to fetch chart data: ${response.statusText}`);
      }
      
      const data = await response.json();
      
      if (!data.success) {
        throw new Error(data.error || 'Failed to fetch chart data');
      }
      
      const metrics: TenantDashboardMetrics = data.data;
      
      // Transform the trend data into chart format
      const transformedData = transformTrendData(metrics.trends);
      setChartData(transformedData);
      
    } catch (err) {
      console.error('Error fetching chart data:', err);
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  const transformTrendData = (trends: TenantDashboardMetrics['trends']): ChartData[] => {
    const { leads, opportunities, contacts } = trends;
    
    // Create a map to combine all data by date
    const dataMap = new Map<string, ChartData>();
    
    // Initialize with leads data
    leads.forEach(item => {
      dataMap.set(item.date, {
        date: formatDate(item.date),
        leads: item.count,
        opportunities: 0,
        contacts: 0,
        revenue: 0
      });
    });
    
    // Add opportunities data
    opportunities.forEach(item => {
      const existing = dataMap.get(item.date);
      if (existing) {
        existing.opportunities = item.count;
        existing.revenue = item.value;
      } else {
        dataMap.set(item.date, {
          date: formatDate(item.date),
          leads: 0,
          opportunities: item.count,
          contacts: 0,
          revenue: item.value
        });
      }
    });
    
    // Add contacts data
    contacts.forEach(item => {
      const existing = dataMap.get(item.date);
      if (existing) {
        existing.contacts = item.count;
      } else {
        dataMap.set(item.date, {
          date: formatDate(item.date),
          leads: 0,
          opportunities: 0,
          contacts: item.count,
          revenue: 0
        });
      }
    });
    
    // Convert to array and sort by date
    return Array.from(dataMap.values()).sort((a, b) => 
      new Date(a.date).getTime() - new Date(b.date).getTime()
    );
  };

  const formatDate = (dateStr: string) => {
    const date = new Date(dateStr);
    return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
  };

  const formatTooltipValue = (value: number, name: string) => {
    if (name === 'revenue') {
      return [`$${value.toLocaleString()}`, 'Revenue'];
    }
    return [value.toLocaleString(), name.charAt(0).toUpperCase() + name.slice(1)];
  };

  const toggleMetric = (metric: keyof typeof activeMetrics) => {
    setActiveMetrics(prev => ({
      ...prev,
      [metric]: !prev[metric]
    }));
  };

  const handleRefresh = () => {
    fetchChartData();
  };

  if (loading) {
    return (
      <Card className={className}>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Analytics Overview</CardTitle>
              <CardDescription>Loading chart data...</CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="h-[300px] flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle>Analytics Overview</CardTitle>
          <CardDescription className="text-red-500">Error loading chart data</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="h-[300px] flex items-center justify-center">
            <div className="text-center">
              <p className="text-sm text-red-500 mb-4">{error}</p>
              <Button onClick={handleRefresh} variant="outline" size="sm">
                Retry
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  const ChartComponent = chartType === 'area' ? AreaChart : LineChart;

  return (
    <Card className={className}>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <div className="space-y-1">
          <CardTitle>Analytics Overview</CardTitle>
          <CardDescription>
            Performance metrics over the last {timeRange} days
          </CardDescription>
        </div>
        <div className="flex items-center space-x-2">
          <Select value={chartType} onValueChange={(value: 'line' | 'area') => setChartType(value)}>
            <SelectTrigger className="w-20">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="area">Area</SelectItem>
              <SelectItem value="line">Line</SelectItem>
            </SelectContent>
          </Select>
          
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7">Last 7 days</SelectItem>
              <SelectItem value="30">Last 30 days</SelectItem>
              <SelectItem value="90">Last 90 days</SelectItem>
            </SelectContent>
          </Select>
          
          <Button onClick={handleRefresh} variant="outline" size="sm">
            Refresh
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <div className="h-[300px] w-full">
          <ResponsiveContainer width="100%" height="100%">
            <ChartComponent data={chartData} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
              <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
              <XAxis 
                dataKey="date" 
                className="text-xs"
                interval="preserveStartEnd"
              />
              <YAxis className="text-xs" />
              <Tooltip
                formatter={formatTooltipValue}
                contentStyle={{
                  backgroundColor: 'hsl(var(--card))',
                  border: '1px solid hsl(var(--border))',
                  borderRadius: '6px',
                  fontSize: '12px'
                }}
              />
              <Legend />

              {activeMetrics.leads && (
                chartType === 'area' ? (
                  <Area
                    type="monotone"
                    dataKey="leads"
                    stackId="1"
                    stroke={chartColors.leads}
                    fill={chartColors.leads}
                    fillOpacity={0.7}
                    name="Leads"
                  />
                ) : (
                  <Line
                    type="monotone"
                    dataKey="leads"
                    stroke={chartColors.leads}
                    strokeWidth={3}
                    dot={false}
                    activeDot={{ r: 5, fill: chartColors.leads }}
                    name="Leads"
                  />
                )
              )}

              {activeMetrics.opportunities && (
                chartType === 'area' ? (
                  <Area
                    type="monotone"
                    dataKey="opportunities"
                    stackId="1"
                    stroke="hsl(var(--chart-3))"
                    fill="hsl(var(--chart-3))"
                    fillOpacity={0.7}
                    name="Opportunities"
                  />
                ) : (
                  <Line
                    type="monotone"
                    dataKey="opportunities"
                    stroke="hsl(var(--chart-3))"
                    strokeWidth={3}
                    dot={false}
                    activeDot={{ r: 5, fill: "hsl(var(--chart-3))" }}
                    name="Opportunities"
                  />
                )
              )}

              {activeMetrics.contacts && (
                chartType === 'area' ? (
                  <Area
                    type="monotone"
                    dataKey="contacts"
                    stackId="1"
                    stroke="hsl(var(--chart-1))"
                    fill="hsl(var(--chart-1))"
                    fillOpacity={0.7}
                    name="Contacts"
                  />
                ) : (
                  <Line
                    type="monotone"
                    dataKey="contacts"
                    stroke="hsl(var(--chart-1))"
                    strokeWidth={3}
                    dot={false}
                    activeDot={{ r: 5, fill: "hsl(var(--chart-1))" }}
                    name="Contacts"
                  />
                )
              )}
            </ChartComponent>
          </ResponsiveContainer>
        </div>
        
        {/* Metric Toggle Buttons */}
        <div className="flex items-center justify-center gap-4 pt-4">
          <Button
            variant={activeMetrics.leads ? "default" : "outline"}
            size="sm"
            onClick={() => toggleMetric('leads')}
            className="flex items-center gap-2"
          >
            <div
              className="w-3 h-3 rounded-full"
              style={{ backgroundColor: 'hsl(var(--chart-2))' }}
            />
            Leads
          </Button>
          <Button
            variant={activeMetrics.opportunities ? "default" : "outline"}
            size="sm"
            onClick={() => toggleMetric('opportunities')}
            className="flex items-center gap-2"
          >
            <div
              className="w-3 h-3 rounded-full"
              style={{ backgroundColor: 'hsl(var(--chart-3))' }}
            />
            Opportunities
          </Button>
          <Button
            variant={activeMetrics.contacts ? "default" : "outline"}
            size="sm"
            onClick={() => toggleMetric('contacts')}
            className="flex items-center gap-2"
          >
            <div
              className="w-3 h-3 rounded-full"
              style={{ backgroundColor: 'hsl(var(--chart-1))' }}
            />
            Contacts
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
