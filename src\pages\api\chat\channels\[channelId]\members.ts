import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../../../../../lib/auth';
import { ChatChannelService } from '../../../../../services/communication';
import { withTenant } from '../../../../../lib/middleware/withTenant';
import { withRateLimit } from '../../../../../lib/middleware/withRateLimit';

async function handler(req: NextApiRequest, res: NextApiResponse) {
  const session = await getServerSession(req, res, authOptions);
  
  if (!session?.user?.id) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  const tenantId = req.headers['x-tenant-id'] as string;
  const userId = session.user.id;
  const channelId = req.query.channelId as string;

  if (!channelId) {
    return res.status(400).json({ error: 'Channel ID is required' });
  }

  try {
    switch (req.method) {
      case 'POST':
        const { targetUserId, role = 'member' } = req.body;
        
        if (!targetUserId) {
          return res.status(400).json({ error: 'Target user ID is required' });
        }

        const newMember = await ChatChannelService.addMember(
          tenantId,
          channelId,
          userId,
          targetUserId,
          role
        );
        return res.status(201).json(newMember);

      case 'DELETE':
        const { targetUserId: removeUserId } = req.body;
        
        if (!removeUserId) {
          return res.status(400).json({ error: 'Target user ID is required' });
        }

        await ChatChannelService.removeMember(
          tenantId,
          channelId,
          userId,
          removeUserId
        );
        return res.status(200).json({ success: true });

      default:
        res.setHeader('Allow', ['POST', 'DELETE']);
        return res.status(405).json({ error: `Method ${req.method} not allowed` });
    }
  } catch (error) {
    console.error('Channel members API error:', error);
    return res.status(500).json({ 
      error: error instanceof Error ? error.message : 'Internal server error' 
    });
  }
}

export default withRateLimit(withTenant(handler));
