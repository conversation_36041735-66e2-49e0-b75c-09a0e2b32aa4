import { prisma } from '@/lib/prisma';
import { enhancedMermaidRenderer } from '@/services/enhanced-mermaid-renderer';
import { MarkdownConverter, MarkdownDocumentContent } from '@/services/markdown-converter';
import {
  DocumentGenerationRequest,
  DocumentGenerationResponse,
  DocumentStyling
} from '@/types/proposal';
import fs from 'fs/promises';
import path from 'path';

export class DocumentGenerationService {
  private uploadsDir: string;

  constructor() {
    this.uploadsDir = path.join(process.cwd(), 'uploads');
  }

  /**
   * Generate Word document from proposal
   */
  async generateDocument(
    proposalId: string,
    tenantId: string,
    request: DocumentGenerationRequest
  ): Promise<DocumentGenerationResponse> {
    try {
      // Get proposal with all sections and charts
      const proposal = await this.getProposalWithContent(proposalId, tenantId);

      if (!proposal) {
        throw new Error('Proposal not found');
      }

      // Render charts if requested and not already rendered
      if (request.includeCharts) {
        await this.ensureChartsRendered(proposal.charts, tenantId);
      }

      // Get tenant branding if requested
      let branding = null;
      if (request.includeBranding) {
        branding = await this.getTenantBranding(tenantId);
      }

      // Generate document content
      const documentContent = await this.buildDocumentContent(
        proposal,
        request,
        branding
      );

      // Generate file path
      const fileName = `proposal_${proposalId}_${Date.now()}.${request.format}`;
      const documentDir = path.join(this.uploadsDir, tenantId, 'documents');
      const documentPath = path.join(documentDir, fileName);
      const relativeDocumentPath = path.join(tenantId, 'documents', fileName);

      // Ensure directory exists
      await fs.mkdir(documentDir, { recursive: true });

      // Generate document based on format
      if (request.format === 'docx') {
        await this.generateWordDocumentWithMarkdown(documentContent, documentPath, request.customStyling);
      } else if (request.format === 'pdf') {
        await this.generatePdfDocument(documentContent, documentPath, request.customStyling);
      }

      // Get file size
      const stats = await fs.stat(documentPath);
      const fileSize = stats.size;

      // Determine MIME type based on format
      const mimeType = request.format === 'docx'
        ? 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        : request.format === 'pdf'
        ? 'application/pdf'
        : 'application/octet-stream';

      // Update proposal and create document record in a transaction
      await prisma.$transaction(async (tx) => {
        // Update proposal with document path
        await tx.proposal.update({
          where: { id: proposalId },
          data: {
            documentPath: relativeDocumentPath,
            documentGenerated: true,
            updatedAt: new Date()
          }
        });

        // Check if a document record already exists for this proposal
        const existingDocument = await tx.document.findFirst({
          where: {
            relatedToType: 'proposal',
            relatedToId: proposalId,
            tenantId
          }
        });

        if (existingDocument) {
          // Update existing document record
          await tx.document.update({
            where: { id: existingDocument.id },
            data: {
              name: fileName,
              filePath: relativeDocumentPath,
              fileSize,
              mimeType,
              description: `Generated ${request.format.toUpperCase()} document for proposal: ${proposal.title}`,
              tags: ['proposal', 'generated', request.format],
              updatedAt: new Date()
            }
          });
        } else {
          // Create new document record
          await tx.document.create({
            data: {
              name: fileName,
              filePath: relativeDocumentPath,
              fileSize,
              mimeType,
              relatedToType: 'proposal',
              relatedToId: proposalId,
              tenantId,
              uploadedById: proposal.createdById,
              description: `Generated ${request.format.toUpperCase()} document for proposal: ${proposal.title}`,
              tags: ['proposal', 'generated', request.format],
              isPublic: false
            }
          });
        }
      });

      const response: DocumentGenerationResponse = {
        documentPath: relativeDocumentPath,
        fileName,
        fileSize,
        downloadUrl: `/api/proposals/${proposalId}/download`
      };

      return response;

    } catch (error) {
      console.error('Error generating document:', error);
      throw new Error(`Failed to generate document: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get proposal with all content
   */
  private async getProposalWithContent(proposalId: string, tenantId: string) {
    return await prisma.proposal.findFirst({
      where: {
        id: proposalId,
        tenantId
      },
      include: {
        opportunity: {
          include: {
            company: true,
            contact: true
          }
        },
        sections: {
          orderBy: {
            orderIndex: 'asc'
          }
        },
        charts: {
          orderBy: {
            orderIndex: 'asc'
          }
        },
        createdBy: {
          select: {
            firstName: true,
            lastName: true,
            email: true
          }
        }
      }
    });
  }

  /**
   * Ensure all charts are rendered as PNG for document embedding
   */
  private async ensureChartsRendered(charts: any[], tenantId: string) {
    const unrenderedCharts = charts.filter(chart =>
      chart.status === 'generated' && !chart.renderedImagePath
    );

    if (unrenderedCharts.length > 0) {
      const renderPromises = unrenderedCharts.map(chart =>
        enhancedMermaidRenderer.renderChartAsPng(chart.id, tenantId, {
          theme: 'default',
          width: 800,
          height: 600,
          backgroundColor: 'white'
        })
      );

      await Promise.allSettled(renderPromises);
    }
  }

  /**
   * Get tenant branding information
   */
  private async getTenantBranding(tenantId: string) {
    const tenant = await prisma.tenant.findUnique({
      where: { id: tenantId }
    });

    return tenant?.branding as any || {};
  }

  /**
   * Build document content structure
   */
  private async buildDocumentContent(
    proposal: any,
    request: DocumentGenerationRequest,
    branding: any
  ): Promise<MarkdownDocumentContent> {
    const content: MarkdownDocumentContent = {
      title: proposal.title,
      metadata: {
        proposalId: proposal.id,
        generatedAt: new Date().toISOString(),
        generatedBy: proposal.createdBy ?
          `${proposal.createdBy.firstName} ${proposal.createdBy.lastName}` :
          'System',
        opportunity: proposal.opportunity ? {
          title: proposal.opportunity.title,
          company: proposal.opportunity.company?.name,
          contact: proposal.opportunity.contact ?
            `${proposal.opportunity.contact.firstName} ${proposal.opportunity.contact.lastName}` :
            undefined
          // Note: value/price is intentionally excluded from document generation
        } : null
      },
      branding: request.includeBranding ? branding : null,
      sections: proposal.sections.map((section: any) => ({
        id: section.id,
        type: section.sectionType,
        title: section.title,
        content: section.content,
        wordCount: section.wordCount,
        isAiGenerated: section.isAiGenerated,
        generatedAt: section.generatedAt
      })),
      charts: request.includeCharts ? proposal.charts.map((chart: any) => ({
        id: chart.id,
        type: chart.chartType,
        title: chart.title,
        description: chart.description,
        // Note: mermaidCode is intentionally excluded from document generation
        // Only the rendered image is included
        imagePath: chart.renderedImagePath ?
          path.join(this.uploadsDir, chart.renderedImagePath) : undefined,
        isAiGenerated: chart.isAiGenerated
      })) : []
    };

    return content;
  }

  /**
   * Generate Word document using markdown converter
   */
  private async generateWordDocumentWithMarkdown(
    content: MarkdownDocumentContent,
    outputPath: string,
    styling?: DocumentStyling
  ) {
    try {
      // Convert content to markdown
      const markdown = MarkdownConverter.convertToMarkdown(content);

      // Validate markdown
      const validation = MarkdownConverter.validateMarkdown(markdown);
      if (!validation.isValid) {
        throw new Error(`Invalid markdown content: ${validation.errors.join(', ')}`);
      }

      // Generate DOCX using the markdown converter
      await MarkdownConverter.generateDocx(markdown, outputPath, styling);

      // Also save markdown for debugging (optional)
      if (process.env.NODE_ENV === 'development') {
        const markdownPath = outputPath.replace('.docx', '.md');
        await fs.writeFile(markdownPath, markdown, 'utf-8');
      }

    } catch (error) {
      console.error('Error generating Word document with markdown:', error);

      // Fallback to old HTML method if markdown conversion fails
      console.log('Falling back to HTML generation method...');
      await this.generateWordDocument(content, outputPath, styling);
    }
  }

  /**
   * Generate Word document (legacy HTML method - kept as fallback)
   */
  private async generateWordDocument(
    content: any,
    outputPath: string,
    styling?: DocumentStyling
  ) {
    // This is a simplified implementation
    // In a real implementation, you would use a library like docx or officegen

    const htmlContent = await this.generateHtmlContent(content, styling);

    // For now, we'll create an HTML file that can be opened in Word
    // In production, you'd use proper Word document generation
    await fs.writeFile(outputPath.replace('.docx', '.html'), htmlContent, 'utf-8');

    // Simulate Word document creation
    const wordContent = this.convertHtmlToWordFormat(htmlContent);
    await fs.writeFile(outputPath, wordContent, 'utf-8');
  }

  /**
   * Generate PDF document
   */
  private async generatePdfDocument(
    content: any,
    outputPath: string,
    styling?: DocumentStyling
  ) {
    // This is a simplified implementation
    // In a real implementation, you would use a library like puppeteer or jsPDF

    const htmlContent = await this.generateHtmlContent(content, styling);

    // For now, we'll create an HTML file
    // In production, you'd convert HTML to PDF using puppeteer or similar
    await fs.writeFile(outputPath.replace('.pdf', '.html'), htmlContent, 'utf-8');

    // Simulate PDF creation
    await fs.writeFile(outputPath, htmlContent, 'utf-8');
  }

  /**
   * Generate HTML content for the document
   */
  private async generateHtmlContent(content: any, styling?: DocumentStyling): Promise<string> {
    const defaultStyling = {
      fontFamily: 'Arial, sans-serif',
      fontSize: 16,
      headerColor: '#2563eb',
      accentColor: '#3b82f6',
      ...styling
    };

    let html = `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>${content.title}</title>
    <style>
        body {
            font-family: ${defaultStyling.fontFamily};
            font-size: ${defaultStyling.fontSize}pt;
            line-height: 1.6;
            margin: 40px;
            color: #333;
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
            border-bottom: 2px solid ${defaultStyling.headerColor};
            padding-bottom: 20px;
        }
        .title {
            font-size: 24pt;
            font-weight: bold;
            color: ${defaultStyling.headerColor};
            margin-bottom: 10px;
        }
        .metadata {
            font-size: 10pt;
            color: #666;
            margin-bottom: 20px;
        }
        .section {
            margin-bottom: 30px;
            page-break-inside: avoid;
        }
        .section-title {
            font-size: 16pt;
            font-weight: bold;
            color: ${defaultStyling.headerColor};
            margin-bottom: 15px;
            border-left: 4px solid ${defaultStyling.accentColor};
            padding-left: 15px;
        }
        .section-content {
            margin-left: 20px;
            text-align: justify;
        }
        .chart {
            text-align: center;
            margin: 20px 0;
            page-break-inside: avoid;
        }
        .chart img {
            max-width: 100%;
            height: auto;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
        }
        .chart-title {
            font-weight: bold;
            margin-bottom: 10px;
            color: ${defaultStyling.headerColor};
        }
        .chart-description {
            font-size: 10pt;
            color: #666;
            margin-top: 5px;
        }
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            font-size: 10pt;
            color: #666;
            text-align: center;
        }
        @media print {
            body { margin: 20px; }
            .section { page-break-inside: avoid; }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="title">${content.title}</div>
        <div class="metadata">
            Generated on ${new Date(content.metadata.generatedAt).toLocaleDateString()}<br>
            ${content.metadata.opportunity ? `For: ${content.metadata.opportunity.company}` : ''}
            ${content.metadata.opportunity?.contact ? ` - ${content.metadata.opportunity.contact}` : ''}
        </div>
    </div>
`;

    // Add sections
    content.sections.forEach((section: any) => {
      html += `
    <div class="section">
        <div class="section-title">${section.title}</div>
        <div class="section-content">
            ${this.formatSectionContent(section.content)}
        </div>
    </div>
`;
    });

    // Add charts
    if (content.charts.length > 0) {
      html += `
    <div class="section">
        <div class="section-title">Architecture Diagrams</div>
`;
      for (const chart of content.charts) {
        const imageBase64 = chart.imagePath ? await this.getImageBase64(chart.imagePath) : '';
        html += `
        <div class="chart">
            <div class="chart-title">${chart.title}</div>
            ${imageBase64 ? `<img src="data:image/png;base64,${imageBase64}" alt="${chart.title}">` : ''}
            ${chart.description ? `<div class="chart-description">${chart.description}</div>` : ''}
        </div>
`;
      }
      html += `    </div>`;
    }

    html += `
    <div class="footer">
        This proposal was generated using AI technology.<br>
        Proposal ID: ${content.metadata.proposalId}
    </div>
</body>
</html>`;

    return html;
  }

  /**
   * Format section content (convert markdown-like formatting to HTML)
   */
  private formatSectionContent(content: string): string {
    if (!content) return '';

    return content
      .replace(/\n\n/g, '</p><p>')
      .replace(/\n/g, '<br>')
      .replace(/^\s*/, '<p>')
      .replace(/\s*$/, '</p>')
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      .replace(/\*(.*?)\*/g, '<em>$1</em>')
      .replace(/`(.*?)`/g, '<code>$1</code>');
  }

  /**
   * Get image as base64
   */
  private async getImageBase64(imagePath: string): Promise<string> {
    try {
      const imageBuffer = await fs.readFile(imagePath);
      return imageBuffer.toString('base64');
    } catch (error) {
      console.error('Error reading image file:', error);
      return '';
    }
  }

  /**
   * Convert HTML to Word format (placeholder implementation)
   */
  private convertHtmlToWordFormat(html: string): string {
    // In a real implementation, you would use a proper HTML to Word converter
    // For now, return the HTML content
    return html;
  }

  /**
   * Get generated document
   */
  async getDocument(proposalId: string, tenantId: string): Promise<Buffer | null> {
    try {
      const proposal = await prisma.proposal.findFirst({
        where: {
          id: proposalId,
          tenantId
        }
      });

      if (!proposal || !proposal.documentPath) {
        return null;
      }

      const documentPath = path.join(this.uploadsDir, proposal.documentPath);
      const documentData = await fs.readFile(documentPath);
      
      return documentData;

    } catch (error) {
      console.error('Error getting document:', error);
      return null;
    }
  }

  /**
   * Clean up old documents
   */
  async cleanupOldDocuments(tenantId: string, olderThanDays: number = 30): Promise<void> {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);

      // Get proposals with old documents
      const oldProposals = await prisma.proposal.findMany({
        where: {
          tenantId,
          documentPath: { not: null },
          updatedAt: { lt: cutoffDate }
        }
      });

      // Delete old document files and records
      for (const proposal of oldProposals) {
        if (proposal.documentPath) {
          const documentPath = path.join(this.uploadsDir, proposal.documentPath);
          try {
            await fs.unlink(documentPath);

            // Clear the document path from database and remove document record
            await prisma.$transaction(async (tx) => {
              await tx.proposal.update({
                where: { id: proposal.id },
                data: {
                  documentPath: null,
                  documentGenerated: false
                }
              });

              // Remove the document record if it exists
              await tx.document.deleteMany({
                where: {
                  relatedToType: 'proposal',
                  relatedToId: proposal.id,
                  tenantId: proposal.tenantId
                }
              });
            });
          } catch (error) {
            // File might already be deleted, continue
            console.warn(`Could not delete document file ${documentPath}:`, error);
          }
        }
      }

    } catch (error) {
      console.error('Error cleaning up old documents:', error);
    }
  }
}

export const documentGenerator = new DocumentGenerationService();
