import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { bulkConvertLeadsSchema } from '@/services/lead-management';
import { opportunityManagementService } from '@/services/opportunity-management';
import { hasPermission } from '@/lib/permissions';
import { PermissionResource, PermissionAction } from '@/types';

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get tenantId from user's tenants array (users have one tenant in this system)
    let tenantId: string | null = null;
    if (session.user.tenants && session.user.tenants.length > 0) {
      tenantId = session.user.tenants[0].id;
    }

    if (!tenantId) {
      return NextResponse.json({ error: 'Tenant not found' }, { status: 400 });
    }

    // Check permissions for both leads and opportunities
    const [hasLeadPermission, hasOpportunityPermission] = await Promise.all([
      hasPermission(
        PermissionResource.LEADS,
        PermissionAction.UPDATE,
        tenantId
      ),
      hasPermission(
        PermissionResource.OPPORTUNITIES,
        PermissionAction.CREATE,
        tenantId
      )
    ]);

    if (!hasLeadPermission || !hasOpportunityPermission) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    const body = await request.json();
    const validatedData = bulkConvertLeadsSchema.parse(body);

    const conversionResults = await opportunityManagementService.bulkConvertLeadsToOpportunities(
      validatedData.leadIds,
      tenantId,
      session.user.id,
      validatedData.conversionData
    );

    return NextResponse.json({
      success: true,
      data: {
        successful: conversionResults.successful,
        failed: conversionResults.failed,
        summary: {
          total: validatedData.leadIds.length,
          successful: conversionResults.successful.length,
          failed: conversionResults.failed.length
        }
      }
    });

  } catch (error) {
    console.error('Error bulk converting leads:', error);
    
    if (error instanceof Error && error.message.includes('validation')) {
      return NextResponse.json(
        { error: 'Invalid request data', details: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
