#!/bin/bash
# Docker volume management script for CRM Platform

set -e

# Configuration
ENVIRONMENT=${2:-dev}  # Default to dev if not specified
COMPOSE_FILE="docker-compose.yml"
COMPOSE_OVERRIDE=""
ACTION=$1
BACKUP_DIR="./backups/volumes"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")

# Colors for terminal output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

# Function to display usage information
usage() {
  echo "Usage: $0 [action] [environment] [options]"
  echo ""
  echo "Actions:"
  echo "  list        List all Docker volumes"
  echo "  backup      Backup Docker volumes"
  echo "  restore     Restore Docker volumes from backup"
  echo "  inspect     Inspect a specific volume"
  echo "  clean       Clean unused volumes"
  echo ""
  echo "Environments:"
  echo "  dev         Development environment (default)"
  echo "  staging     Staging environment"
  echo "  prod        Production environment"
  echo ""
  echo "Options:"
  echo "  --volume VOLUME_NAME   Specify volume name for backup/restore/inspect"
  echo "  --file FILENAME        Specify backup filename for restore"
  echo ""
  echo "Examples:"
  echo "  $0 list                # List all volumes in dev environment"
  echo "  $0 backup prod         # Backup all volumes in production environment"
  echo "  $0 backup --volume postgres-data  # Backup specific volume"
  echo "  $0 restore --file backup_20230101_120000.tar.gz  # Restore from backup"
  exit 1
}

# Function to set up environment-specific configuration
setup_environment() {
  case "$ENVIRONMENT" in
    dev)
      echo -e "${GREEN}Using development environment...${NC}"
      COMPOSE_FILE="docker-compose.yml"
      COMPOSE_OVERRIDE="-f docker-compose.override.yml"
      ;;
    staging)
      echo -e "${GREEN}Using staging environment...${NC}"
      COMPOSE_FILE="docker-compose.yml"
      COMPOSE_OVERRIDE="-f docker-compose.staging.yml"
      ;;
    prod)
      echo -e "${GREEN}Using production environment...${NC}"
      COMPOSE_FILE="docker-compose.yml"
      COMPOSE_OVERRIDE="-f docker-compose.prod.yml"
      ;;
    *)
      echo -e "${RED}Error: Unknown environment '$ENVIRONMENT'${NC}"
      usage
      ;;
  esac
}

# Function to create backup directory if it doesn't exist
ensure_backup_dir() {
  if [ ! -d "$BACKUP_DIR" ]; then
    echo -e "${YELLOW}Creating backup directory: $BACKUP_DIR${NC}"
    mkdir -p "$BACKUP_DIR"
  fi
}

# Function to list all Docker volumes
list_volumes() {
  echo -e "${YELLOW}Docker volumes:${NC}"
  docker volume ls
}

# Function to backup a specific volume
backup_volume() {
  local volume_name=$1
  
  echo -e "${YELLOW}Backing up volume: $volume_name${NC}"
  
  # Create a temporary container to access the volume
  container_id=$(docker run -d -v $volume_name:/data alpine:latest tail -f /dev/null)
  
  # Create backup directory
  ensure_backup_dir
  
  # Create tar archive of the volume
  docker exec $container_id tar -czf /tmp/volume_backup.tar.gz -C /data .
  
  # Copy the backup from the container to the host
  docker cp $container_id:/tmp/volume_backup.tar.gz "$BACKUP_DIR/${volume_name}_${TIMESTAMP}.tar.gz"
  
  # Clean up the temporary container
  docker rm -f $container_id
  
  echo -e "${GREEN}Volume backup created: $BACKUP_DIR/${volume_name}_${TIMESTAMP}.tar.gz${NC}"
}

# Function to backup all volumes
backup_all_volumes() {
  echo -e "${YELLOW}Backing up all Docker volumes...${NC}"
  
  # Get all volume names
  volumes=$(docker volume ls --format "{{.Name}}")
  
  # Create backup directory
  ensure_backup_dir
  
  # Backup each volume
  for volume in $volumes; do
    backup_volume $volume
  done
  
  echo -e "${GREEN}All volumes backed up successfully!${NC}"
}

# Function to restore a volume from backup
restore_volume() {
  local volume_name=$1
  local backup_file=$2
  
  if [ ! -f "$backup_file" ]; then
    echo -e "${RED}Error: Backup file not found: $backup_file${NC}"
    exit 1
  fi
  
  echo -e "${YELLOW}Restoring volume: $volume_name from $backup_file${NC}"
  
  # Check if volume exists
  if ! docker volume inspect $volume_name &> /dev/null; then
    echo -e "${YELLOW}Volume $volume_name does not exist, creating...${NC}"
    docker volume create $volume_name
  fi
  
  # Create a temporary container to access the volume
  container_id=$(docker run -d -v $volume_name:/data alpine:latest tail -f /dev/null)
  
  # Copy the backup file to the container
  docker cp $backup_file $container_id:/tmp/volume_backup.tar.gz
  
  # Extract the backup to the volume
  docker exec $container_id sh -c "rm -rf /data/* && tar -xzf /tmp/volume_backup.tar.gz -C /data"
  
  # Clean up the temporary container
  docker rm -f $container_id
  
  echo -e "${GREEN}Volume $volume_name restored successfully from $backup_file${NC}"
}

# Function to inspect a volume
inspect_volume() {
  local volume_name=$1
  
  echo -e "${YELLOW}Inspecting volume: $volume_name${NC}"
  
  # Check if volume exists
  if ! docker volume inspect $volume_name &> /dev/null; then
    echo -e "${RED}Error: Volume $volume_name does not exist${NC}"
    exit 1
  fi
  
  # Display volume details
  docker volume inspect $volume_name
  
  # Create a temporary container to access the volume
  container_id=$(docker run -d -v $volume_name:/data alpine:latest tail -f /dev/null)
  
  # List files in the volume
  echo -e "${YELLOW}Files in volume:${NC}"
  docker exec $container_id find /data -type f | sort
  
  # Display volume size
  echo -e "${YELLOW}Volume size:${NC}"
  docker exec $container_id du -sh /data
  
  # Clean up the temporary container
  docker rm -f $container_id
}

# Function to clean unused volumes
clean_volumes() {
  echo -e "${YELLOW}Cleaning unused Docker volumes...${NC}"
  
  # Prompt for confirmation
  echo -e "${RED}WARNING: This will remove all unused volumes and their data!${NC}"
  read -p "Are you sure you want to continue? (y/n): " confirm
  
  if [ "$confirm" = "y" ]; then
    docker volume prune -f
    echo -e "${GREEN}Unused volumes cleaned successfully!${NC}"
  else
    echo -e "${YELLOW}Volume cleaning cancelled.${NC}"
  fi
}

# Function to parse command line arguments
parse_arguments() {
  local volume_name=""
  local backup_file=""
  
  while [[ $# -gt 0 ]]; do
    case "$1" in
      list|backup|restore|inspect|clean)
        ACTION="$1"
        shift
        ;;
      dev|staging|prod)
        ENVIRONMENT="$1"
        shift
        ;;
      --volume)
        volume_name="$2"
        shift 2
        ;;
      --file)
        backup_file="$2"
        shift 2
        ;;
      -h|--help)
        usage
        ;;
      *)
        echo -e "${RED}Unknown option: $1${NC}"
        usage
        ;;
    esac
  done
  
  # Validate arguments
  if [ "$ACTION" = "restore" ] && [ -z "$backup_file" ]; then
    echo -e "${RED}Error: --file option is required for restore action.${NC}"
    usage
  fi
  
  if [ "$ACTION" = "inspect" ] && [ -z "$volume_name" ]; then
    echo -e "${RED}Error: --volume option is required for inspect action.${NC}"
    usage
  fi
  
  return_values=($volume_name $backup_file)
  echo "${return_values[@]}"
}

# Main script execution
echo "Docker Volume Management"
echo "======================="

# Check if action is provided
if [ -z "$ACTION" ]; then
  echo -e "${RED}Error: No action specified.${NC}"
  usage
fi

# Setup environment
setup_environment

# Parse arguments
args=($(parse_arguments "$@"))
volume_name=${args[0]}
backup_file=${args[1]}

# Execute the requested action
case "$ACTION" in
  list)
    list_volumes
    ;;
  backup)
    if [ -n "$volume_name" ]; then
      backup_volume "$volume_name"
    else
      backup_all_volumes
    fi
    ;;
  restore)
    if [ -z "$volume_name" ]; then
      echo -e "${RED}Error: --volume option is required for restore action.${NC}"
      usage
    fi
    restore_volume "$volume_name" "$backup_file"
    ;;
  inspect)
    inspect_volume "$volume_name"
    ;;
  clean)
    clean_volumes
    ;;
  *)
    echo -e "${RED}Unknown action: $ACTION${NC}"
    usage
    ;;
esac
