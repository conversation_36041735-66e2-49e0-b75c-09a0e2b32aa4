#!/bin/bash
# =============================================================================
# CRM Platform SSL Certificate Generation Script
# Generates self-signed SSL certificates for development and staging
# =============================================================================

set -euo pipefail

# -----------------------------------------------------------------------------
# Configuration
# -----------------------------------------------------------------------------
readonly SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
readonly PROJECT_ROOT="$(cd "${SCRIPT_DIR}/../../.." && pwd)"
readonly DEPLOYMENT_DIR="$(cd "${SCRIPT_DIR}/../.." && pwd)"

SSL_DIR="${DEPLOYMENT_DIR}/config/nginx/ssl"
CERT_DAYS="${CERT_DAYS:-365}"
COUNTRY="${SSL_COUNTRY:-US}"
STATE="${SSL_STATE:-State}"
CITY="${SSL_CITY:-City}"
ORGANIZATION="${SSL_ORG:-CRM Platform}"
COMMON_NAME="${SSL_CN:-localhost}"

# Colors for output
readonly RED='\033[0;31m'
readonly GREEN='\033[0;32m'
readonly YELLOW='\033[1;33m'
readonly BLUE='\033[0;34m'
readonly NC='\033[0m'

# -----------------------------------------------------------------------------
# Utility Functions
# -----------------------------------------------------------------------------
print_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
print_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
print_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
print_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# -----------------------------------------------------------------------------
# SSL Certificate Functions
# -----------------------------------------------------------------------------

# Check if OpenSSL is available
check_openssl() {
  if ! command -v openssl &> /dev/null; then
    print_error "OpenSSL is not installed or not in PATH"
    print_info "Please install OpenSSL to generate SSL certificates"
    exit 1
  fi
  
  print_success "OpenSSL is available"
}

# Create SSL directory
setup_ssl_directory() {
  print_info "Setting up SSL directory..."
  
  mkdir -p "$SSL_DIR"
  chmod 755 "$SSL_DIR"
  
  print_success "SSL directory created: $SSL_DIR"
}

# Generate SSL configuration file
create_ssl_config() {
  local config_file="${SSL_DIR}/openssl.cnf"
  
  print_info "Creating SSL configuration..."
  
  cat > "$config_file" << EOF
[req]
default_bits = 2048
prompt = no
default_md = sha256
distinguished_name = dn
req_extensions = v3_req

[dn]
C=${COUNTRY}
ST=${STATE}
L=${CITY}
O=${ORGANIZATION}
CN=${COMMON_NAME}

[v3_req]
basicConstraints = CA:FALSE
keyUsage = nonRepudiation, digitalSignature, keyEncipherment
subjectAltName = @alt_names

[alt_names]
DNS.1 = localhost
DNS.2 = *.localhost
DNS.3 = 127.0.0.1
DNS.4 = crm-app
DNS.5 = app
IP.1 = 127.0.0.1
IP.2 = ::1
EOF

  print_success "SSL configuration created"
  echo "$config_file"
}

# Generate private key
generate_private_key() {
  local key_file="${SSL_DIR}/server.key"
  
  print_info "Generating private key..."
  
  openssl genrsa -out "$key_file" 2048
  chmod 600 "$key_file"
  
  print_success "Private key generated: $key_file"
}

# Generate certificate signing request
generate_csr() {
  local config_file="$1"
  local key_file="${SSL_DIR}/server.key"
  local csr_file="${SSL_DIR}/server.csr"
  
  print_info "Generating certificate signing request..."
  
  openssl req -new -key "$key_file" -out "$csr_file" -config "$config_file"
  
  print_success "CSR generated: $csr_file"
}

# Generate self-signed certificate
generate_certificate() {
  local config_file="$1"
  local key_file="${SSL_DIR}/server.key"
  local csr_file="${SSL_DIR}/server.csr"
  local cert_file="${SSL_DIR}/server.crt"
  
  print_info "Generating self-signed certificate..."
  
  openssl x509 -req -days "$CERT_DAYS" \
    -in "$csr_file" \
    -signkey "$key_file" \
    -out "$cert_file" \
    -extensions v3_req \
    -extfile "$config_file"
  
  chmod 644 "$cert_file"
  
  print_success "Certificate generated: $cert_file"
}

# Cleanup temporary files
cleanup_temp_files() {
  print_info "Cleaning up temporary files..."
  
  rm -f "${SSL_DIR}/server.csr"
  rm -f "${SSL_DIR}/openssl.cnf"
  
  print_success "Temporary files cleaned up"
}

# Verify generated certificates
verify_certificates() {
  local cert_file="${SSL_DIR}/server.crt"
  local key_file="${SSL_DIR}/server.key"
  
  print_info "Verifying generated certificates..."
  
  # Check if files exist
  if [[ ! -f "$cert_file" ]] || [[ ! -f "$key_file" ]]; then
    print_error "Certificate files not found"
    return 1
  fi
  
  # Verify certificate
  if openssl x509 -in "$cert_file" -text -noout > /dev/null 2>&1; then
    print_success "Certificate is valid"
  else
    print_error "Certificate is invalid"
    return 1
  fi
  
  # Verify private key
  if openssl rsa -in "$key_file" -check -noout > /dev/null 2>&1; then
    print_success "Private key is valid"
  else
    print_error "Private key is invalid"
    return 1
  fi
  
  # Check if certificate and key match
  local cert_hash=$(openssl x509 -noout -modulus -in "$cert_file" | openssl md5)
  local key_hash=$(openssl rsa -noout -modulus -in "$key_file" | openssl md5)
  
  if [[ "$cert_hash" == "$key_hash" ]]; then
    print_success "Certificate and private key match"
  else
    print_error "Certificate and private key do not match"
    return 1
  fi
}

# Display certificate information
show_certificate_info() {
  local cert_file="${SSL_DIR}/server.crt"
  
  print_info "Certificate Information:"
  echo ""
  
  # Extract certificate details
  local subject=$(openssl x509 -in "$cert_file" -noout -subject | sed 's/subject=//')
  local issuer=$(openssl x509 -in "$cert_file" -noout -issuer | sed 's/issuer=//')
  local valid_from=$(openssl x509 -in "$cert_file" -noout -startdate | sed 's/notBefore=//')
  local valid_to=$(openssl x509 -in "$cert_file" -noout -enddate | sed 's/notAfter=//')
  
  echo "  Subject: $subject"
  echo "  Issuer: $issuer"
  echo "  Valid From: $valid_from"
  echo "  Valid To: $valid_to"
  echo ""
  
  # Show Subject Alternative Names
  echo "  Subject Alternative Names:"
  openssl x509 -in "$cert_file" -text -noout | grep -A 1 "Subject Alternative Name" | tail -n 1 | sed 's/^[[:space:]]*/    /' || echo "    None"
  echo ""
}

# -----------------------------------------------------------------------------
# Main Function
# -----------------------------------------------------------------------------

main() {
  print_info "🔐 CRM Platform SSL Certificate Generation"
  print_info "=========================================="
  
  # Check prerequisites
  check_openssl
  
  # Setup
  setup_ssl_directory
  
  # Generate certificates
  local config_file
  config_file=$(create_ssl_config)
  
  generate_private_key
  generate_csr "$config_file"
  generate_certificate "$config_file"
  
  # Cleanup and verify
  cleanup_temp_files
  verify_certificates
  
  # Show results
  show_certificate_info
  
  print_success "🎉 SSL certificates generated successfully!"
  print_info "Certificate: ${SSL_DIR}/server.crt"
  print_info "Private Key: ${SSL_DIR}/server.key"
  print_warning "Note: These are self-signed certificates for development/staging only"
  print_warning "Use proper CA-signed certificates for production"
}

# -----------------------------------------------------------------------------
# Usage
# -----------------------------------------------------------------------------

usage() {
  cat << EOF
Usage: $0 [OPTIONS]

OPTIONS:
  --days DAYS        Certificate validity period in days (default: 365)
  --country CODE     Country code (default: US)
  --state STATE      State or province (default: State)
  --city CITY        City or locality (default: City)
  --org ORG          Organization name (default: CRM Platform)
  --cn COMMON_NAME   Common name (default: localhost)
  --help             Show this help message

EXAMPLES:
  $0                                    # Generate with defaults
  $0 --days 730                        # 2-year certificate
  $0 --cn myapp.local                   # Custom common name
  $0 --org "My Company" --city "New York"  # Custom organization

ENVIRONMENT VARIABLES:
  CERT_DAYS=365                        # Certificate validity period
  SSL_COUNTRY=US                       # Country code
  SSL_STATE=State                      # State or province
  SSL_CITY=City                        # City or locality
  SSL_ORG="CRM Platform"               # Organization name
  SSL_CN=localhost                     # Common name

EOF
  exit 1
}

# Parse arguments
while [[ $# -gt 0 ]]; do
  case $1 in
    --days)
      CERT_DAYS="$2"
      shift 2
      ;;
    --country)
      COUNTRY="$2"
      shift 2
      ;;
    --state)
      STATE="$2"
      shift 2
      ;;
    --city)
      CITY="$2"
      shift 2
      ;;
    --org)
      ORGANIZATION="$2"
      shift 2
      ;;
    --cn)
      COMMON_NAME="$2"
      shift 2
      ;;
    --help|-h)
      usage
      ;;
    *)
      print_error "Unknown option: $1"
      usage
      ;;
  esac
done

# Run main function
main
