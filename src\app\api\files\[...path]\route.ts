import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import path from 'path';
import fs from 'fs/promises';

const UPLOAD_DIR = process.env.UPLOAD_DIR || './uploads';

interface RouteParams {
  params: Promise<{
    path: string[];
  }>;
}

// Serve files (avatars, documents, etc.)
export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { path: filePath } = await params;
    const fullPath = filePath.join('/');
    const fullFilePath = path.join(UPLOAD_DIR, fullPath);

    console.log('File serving request:', { fullPath, fullFilePath });

    // Basic security check - ensure the path doesn't contain directory traversal
    if (fullPath.includes('..') || fullPath.includes('\\..')) {
      return NextResponse.json(
        { error: 'Invalid file path' },
        { status: 400 }
      );
    }

    // Check if file exists
    try {
      await fs.access(fullFilePath);
    } catch {
      return NextResponse.json(
        { error: 'File not found' },
        { status: 404 }
      );
    }

    // Additional security: Check if user has access to this file
    // For avatars, users can access their own tenant's files
    const currentTenant = session.user.tenants?.[0];
    if (!currentTenant) {
      return NextResponse.json(
        { error: 'No tenant access' },
        { status: 403 }
      );
    }

    // Check if the file path starts with the user's tenant ID
    if (!fullPath.startsWith(currentTenant.id)) {
      return NextResponse.json(
        { error: 'Access denied' },
        { status: 403 }
      );
    }

    try {
      // Read file from storage
      const fileBuffer = await fs.readFile(fullFilePath);
      
      // Determine content type based on file extension
      const ext = path.extname(fullPath).toLowerCase();
      let contentType = 'application/octet-stream';
      
      switch (ext) {
        case '.jpg':
        case '.jpeg':
          contentType = 'image/jpeg';
          break;
        case '.png':
          contentType = 'image/png';
          break;
        case '.webp':
          contentType = 'image/webp';
          break;
        case '.gif':
          contentType = 'image/gif';
          break;
        case '.svg':
          contentType = 'image/svg+xml';
          break;
        case '.pdf':
          contentType = 'application/pdf';
          break;
        case '.doc':
          contentType = 'application/msword';
          break;
        case '.docx':
          contentType = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
          break;
        default:
          contentType = 'application/octet-stream';
      }

      // Set cache headers for images
      const headers = new Headers();
      headers.set('Content-Type', contentType);
      
      if (contentType.startsWith('image/')) {
        headers.set('Cache-Control', 'public, max-age=31536000, immutable');
      } else {
        headers.set('Cache-Control', 'private, max-age=3600');
      }

      return new NextResponse(fileBuffer, {
        status: 200,
        headers,
      });

    } catch (fileError) {
      console.error('File read error:', fileError);
      return NextResponse.json(
        { error: 'Failed to read file' },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('File serve error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
