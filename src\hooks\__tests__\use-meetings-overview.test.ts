import { renderHook, act, waitFor } from '@testing-library/react';
import { useSession } from 'next-auth/react';
import { useMeetingsOverview } from '../use-meetings-overview';

// Mock the dependencies
jest.mock('next-auth/react');
jest.mock('@/hooks/use-toast', () => ({
  useToast: () => ({
    toast: jest.fn(),
  }),
}));

// Mock fetch
global.fetch = jest.fn();

const mockSession = {
  user: {
    id: 'user-1',
    email: '<EMAIL>',
    name: 'Test User',
  },
};

const mockMeetingsResponse = {
  success: true,
  data: {
    meetings: [
      {
        id: 'meeting-1',
        title: 'Test Meeting',
        scheduledAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // Tomorrow
        status: 'scheduled',
        meetingType: 'video',
      },
      {
        id: 'meeting-2',
        title: 'Past Meeting',
        scheduledAt: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(), // Yesterday
        status: 'completed',
        meetingType: 'phone',
      },
    ],
    total: 2,
    page: 1,
    limit: 50,
    totalPages: 1,
  },
};

describe('useMeetingsOverview', () => {
  beforeEach(() => {
    (useSession as jest.Mock).mockReturnValue({
      data: mockSession,
      status: 'authenticated',
    });

    (global.fetch as jest.Mock).mockResolvedValue({
      ok: true,
      json: async () => mockMeetingsResponse,
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('initializes with default values', () => {
    const { result } = renderHook(() => useMeetingsOverview());

    expect(result.current.meetings).toEqual([]);
    expect(result.current.upcomingMeetings).toEqual([]);
    expect(result.current.pastMeetings).toEqual([]);
    expect(result.current.loading).toBe(true);
    expect(result.current.error).toBe(null);
    expect(result.current.timePeriod).toBe('week');
  });

  it('fetches meetings on mount', async () => {
    const { result } = renderHook(() => useMeetingsOverview());

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    expect(global.fetch).toHaveBeenCalledWith(
      expect.stringContaining('/api/meetings?')
    );
    expect(result.current.meetings).toHaveLength(2);
  });

  it('separates upcoming and past meetings correctly', async () => {
    const { result } = renderHook(() => useMeetingsOverview());

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    expect(result.current.upcomingMeetings).toHaveLength(1);
    expect(result.current.pastMeetings).toHaveLength(1);
    expect(result.current.upcomingMeetings[0].title).toBe('Test Meeting');
    expect(result.current.pastMeetings[0].title).toBe('Past Meeting');
  });

  it('changes time period and refetches data', async () => {
    const { result } = renderHook(() => useMeetingsOverview());

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    act(() => {
      result.current.setTimePeriod('day');
    });

    expect(result.current.timePeriod).toBe('day');

    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalledTimes(2); // Initial + time period change
    });
  });

  it('handles fetch errors gracefully', async () => {
    (global.fetch as jest.Mock).mockRejectedValue(new Error('Network error'));

    const { result } = renderHook(() => useMeetingsOverview());

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    expect(result.current.error).toBe('Network error');
    expect(result.current.meetings).toEqual([]);
  });

  it('handles API error responses', async () => {
    (global.fetch as jest.Mock).mockResolvedValue({
      ok: false,
      json: async () => ({
        success: false,
        message: 'Unauthorized',
      }),
    });

    const { result } = renderHook(() => useMeetingsOverview());

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    expect(result.current.error).toBe('Failed to fetch meetings');
  });

  it('refetches data when refetch is called', async () => {
    const { result } = renderHook(() => useMeetingsOverview());

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    act(() => {
      result.current.refetch();
    });

    expect(result.current.loading).toBe(true);

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    expect(global.fetch).toHaveBeenCalledTimes(2); // Initial + refetch
  });

  it('does not fetch when user is not authenticated', () => {
    (useSession as jest.Mock).mockReturnValue({
      data: null,
      status: 'unauthenticated',
    });

    renderHook(() => useMeetingsOverview());

    expect(global.fetch).not.toHaveBeenCalled();
  });

  it('includes correct query parameters in fetch request', async () => {
    const { result } = renderHook(() => useMeetingsOverview());

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    const fetchCall = (global.fetch as jest.Mock).mock.calls[0][0];
    const url = new URL(fetchCall, 'http://localhost');
    
    expect(url.searchParams.get('sortBy')).toBe('scheduledAt');
    expect(url.searchParams.get('sortOrder')).toBe('asc');
    expect(url.searchParams.get('limit')).toBe('50');
    expect(url.searchParams.has('dateFrom')).toBe(true);
    expect(url.searchParams.has('dateTo')).toBe(true);
  });
});
