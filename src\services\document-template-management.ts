import { prisma } from '@/lib/prisma';
import { z } from 'zod';

// Validation schemas
export const createDocumentTemplateSchema = z.object({
  name: z.string().min(1, 'Template name is required').max(255),
  description: z.string().optional(),
  category: z.enum(['proposal', 'contract', 'report']),
  templateType: z.string().min(1, 'Template type is required'),
  isDefault: z.boolean().default(false),
  isActive: z.boolean().default(true),
  styling: z.any().optional(),
  branding: z.any().optional(),
  variables: z.array(z.string()).default([]),
  filePath: z.string().optional(),
  htmlTemplate: z.string().optional(),
});

export const updateDocumentTemplateSchema = createDocumentTemplateSchema.partial();

export type CreateDocumentTemplateData = z.infer<typeof createDocumentTemplateSchema>;
export type UpdateDocumentTemplateData = z.infer<typeof updateDocumentTemplateSchema>;

export interface DocumentTemplateWithRelations {
  id: string;
  name: string;
  description?: string;
  category: string;
  templateType: string;
  filePath?: string;
  htmlTemplate?: string;
  isDefault: boolean;
  isActive: boolean;
  styling: any;
  branding: any;
  variables: string[];
  usageCount: number;
  lastUsedAt?: Date;
  createdAt: Date;
  updatedAt: Date;
  tenantId: string;
  createdBy?: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
  };
}

export interface DocumentTemplateFilters {
  category?: string;
  templateType?: string;
  isActive?: boolean;
  includeInactive?: boolean;
  search?: string;
  page?: number;
  limit?: number;
  sortBy?: 'name' | 'createdAt' | 'updatedAt' | 'usageCount';
  sortOrder?: 'asc' | 'desc';
}

export class DocumentTemplateManagementService {
  private readonly includeOptions = {
    createdBy: {
      select: {
        id: true,
        firstName: true,
        lastName: true,
        email: true,
      },
    },
  };

  /**
   * Get document templates with filtering and pagination
   */
  async getTemplates(
    tenantId: string,
    filters: DocumentTemplateFilters = {}
  ): Promise<{
    templates: DocumentTemplateWithRelations[];
    totalCount: number;
    totalPages: number;
    currentPage: number;
  }> {
    const {
      category,
      templateType,
      isActive,
      includeInactive = false,
      search,
      page = 1,
      limit = 20,
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = filters;

    const skip = (page - 1) * limit;

    // Build where clause
    const where: any = {
      tenantId,
      ...(category && { category }),
      ...(templateType && { templateType }),
      ...(isActive !== undefined && { isActive }),
      ...(!includeInactive && { isActive: true }),
    };

    // Add search filter
    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } },
        { templateType: { contains: search, mode: 'insensitive' } },
      ];
    }

    // Get total count
    const totalCount = await prisma.documentTemplate.count({ where });

    // Get templates
    const templates = await prisma.documentTemplate.findMany({
      where,
      include: this.includeOptions,
      orderBy: { [sortBy]: sortOrder },
      skip,
      take: limit,
    });

    const totalPages = Math.ceil(totalCount / limit);

    return {
      templates: templates as DocumentTemplateWithRelations[],
      totalCount,
      totalPages,
      currentPage: page,
    };
  }

  /**
   * Get a single template by ID
   */
  async getTemplate(templateId: string, tenantId: string): Promise<DocumentTemplateWithRelations | null> {
    const template = await prisma.documentTemplate.findFirst({
      where: {
        id: templateId,
        tenantId,
      },
      include: this.includeOptions,
    });

    return template as DocumentTemplateWithRelations | null;
  }

  /**
   * Create a new document template
   */
  async createTemplate(
    tenantId: string,
    templateData: CreateDocumentTemplateData,
    createdBy: string
  ): Promise<DocumentTemplateWithRelations> {
    const validatedData = createDocumentTemplateSchema.parse(templateData);

    return await prisma.$transaction(async (tx) => {
      // If this is set as default, unset other defaults in the same category
      if (validatedData.isDefault) {
        await tx.documentTemplate.updateMany({
          where: {
            tenantId,
            category: validatedData.category,
            isDefault: true,
          },
          data: {
            isDefault: false,
          },
        });
      }

      const template = await tx.documentTemplate.create({
        data: {
          tenantId,
          name: validatedData.name,
          description: validatedData.description,
          category: validatedData.category,
          templateType: validatedData.templateType,
          isDefault: validatedData.isDefault,
          isActive: validatedData.isActive,
          styling: validatedData.styling || {},
          branding: validatedData.branding || {},
          variables: validatedData.variables || [],
          filePath: validatedData.filePath,
          htmlTemplate: validatedData.htmlTemplate,
          usageCount: 0,
          createdById: createdBy,
        },
        include: this.includeOptions,
      });

      return template as DocumentTemplateWithRelations;
    });
  }

  /**
   * Update a document template
   */
  async updateTemplate(
    templateId: string,
    tenantId: string,
    updateData: UpdateDocumentTemplateData,
    updatedBy: string
  ): Promise<DocumentTemplateWithRelations> {
    const validatedData = updateDocumentTemplateSchema.parse(updateData);

    return await prisma.$transaction(async (tx) => {
      // Verify template exists and belongs to tenant
      const existingTemplate = await tx.documentTemplate.findFirst({
        where: {
          id: templateId,
          tenantId,
        },
      });

      if (!existingTemplate) {
        throw new Error('Template not found or access denied');
      }

      // If setting as default, unset other defaults in the same category
      if (validatedData.isDefault) {
        await tx.documentTemplate.updateMany({
          where: {
            tenantId,
            category: existingTemplate.category,
            isDefault: true,
            id: { not: templateId },
          },
          data: {
            isDefault: false,
          },
        });
      }

      const template = await tx.documentTemplate.update({
        where: { id: templateId },
        data: {
          ...validatedData,
          updatedAt: new Date(),
        },
        include: this.includeOptions,
      });

      return template as DocumentTemplateWithRelations;
    });
  }

  /**
   * Delete a document template
   */
  async deleteTemplate(templateId: string, tenantId: string): Promise<void> {
    const template = await prisma.documentTemplate.findFirst({
      where: {
        id: templateId,
        tenantId,
      },
    });

    if (!template) {
      throw new Error('Template not found or access denied');
    }

    await prisma.documentTemplate.delete({
      where: { id: templateId },
    });
  }

  /**
   * Set template as default
   */
  async setDefaultTemplate(templateId: string, tenantId: string): Promise<void> {
    return await prisma.$transaction(async (tx) => {
      // Get the template to set as default
      const template = await tx.documentTemplate.findFirst({
        where: {
          id: templateId,
          tenantId,
        },
      });

      if (!template) {
        throw new Error('Template not found');
      }

      // Unset other defaults in the same category
      await tx.documentTemplate.updateMany({
        where: {
          tenantId,
          category: template.category,
          isDefault: true,
        },
        data: {
          isDefault: false,
        },
      });

      // Set this template as default
      await tx.documentTemplate.update({
        where: { id: templateId },
        data: {
          isDefault: true,
          isActive: true, // Ensure it's active when set as default
        },
      });
    });
  }

  /**
   * Get default template for a category
   */
  async getDefaultTemplate(tenantId: string, category: string): Promise<DocumentTemplateWithRelations | null> {
    const template = await prisma.documentTemplate.findFirst({
      where: {
        tenantId,
        category,
        isDefault: true,
        isActive: true,
      },
      include: this.includeOptions,
    });

    return template as DocumentTemplateWithRelations | null;
  }

  /**
   * Increment usage count
   */
  async incrementUsageCount(templateId: string, tenantId: string): Promise<void> {
    await prisma.documentTemplate.updateMany({
      where: {
        id: templateId,
        tenantId,
      },
      data: {
        usageCount: {
          increment: 1,
        },
        lastUsedAt: new Date(),
      },
    });
  }
}
