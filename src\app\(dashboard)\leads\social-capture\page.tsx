'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import {
  ChatBubbleLeftRightIcon,
  CheckCircleIcon,
  XCircleIcon,
  ClockIcon,
  HeartIcon,
  ArrowPathIcon,
  EyeIcon
} from '@heroicons/react/24/outline';
import { formatDistanceToNow } from 'date-fns';
import { useToast } from '@/hooks/use-toast';
import { SocialMediaCapture } from '@/services/social-media-integration';
import { PageLayout } from '@/components/layout/page-layout';
import { useRouter } from 'next/navigation';
import { PlusIcon } from '@heroicons/react/24/outline';

const PLATFORM_COLORS = {
  twitter: 'bg-blue-100 text-blue-800',
  linkedin: 'bg-blue-100 text-blue-800',
  facebook: 'bg-blue-100 text-blue-800',
  instagram: 'bg-pink-100 text-pink-800',
  tiktok: 'bg-black text-white'
};

const PLATFORM_ICONS = {
  twitter: '𝕏',
  linkedin: 'in',
  facebook: 'f',
  instagram: '📷',
  tiktok: '🎵'
};

export default function SocialCapturePage() {
  const { data: session } = useSession();
  const router = useRouter();
  const [captures, setCaptures] = useState<SocialMediaCapture[]>([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState<'all' | 'processed' | 'unprocessed'>('all');
  const [platformFilter, setPlatformFilter] = useState<string>('all');
  const { toast } = useToast();

  const tenantId = session?.user?.tenants?.[0]?.id;

  useEffect(() => {
    if (tenantId) {
      fetchCaptures();
    }
  }, [tenantId, filter, platformFilter]);

  const fetchCaptures = async () => {
    try {
      const params = new URLSearchParams();
      if (filter === 'processed') params.append('processed', 'true');
      if (filter === 'unprocessed') params.append('processed', 'false');
      if (platformFilter !== 'all') params.append('platform', platformFilter);

      const url = `/api/leads/social-capture${params.toString() ? `?${params.toString()}` : ''}`;

      const response = await fetch(url, {
        headers: {
          'x-tenant-id': tenantId!
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch social captures');
      }

      const data = await response.json();
      setCaptures(data.data || []);
    } catch (error) {
      console.error('Error fetching social captures:', error);
      toast({
        title: 'Error',
        description: 'Failed to fetch social captures',
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  };

  const getSentimentColor = (sentiment: string) => {
    switch (sentiment) {
      case 'positive': return 'bg-green-100 text-green-800';
      case 'negative': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getLeadPotentialColor = (potential: string) => {
    switch (potential) {
      case 'high': return 'bg-green-100 text-green-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  if (!tenantId) {
    return (
      <PageLayout>
        <div className="flex items-center justify-center h-64">
          <p className="text-muted-foreground">Loading...</p>
        </div>
      </PageLayout>
    );
  }

  if (loading) {
    return (
      <PageLayout
        title="Social Media Capture"
        description="Monitor and manage leads captured from social media platforms"
      >
        <div className="grid gap-4">
          {[1, 2, 3].map((i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader>
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
              </CardHeader>
              <CardContent>
                <div className="h-20 bg-gray-200 rounded"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </PageLayout>
    );
  }

  const platforms = ['all', ...Array.from(new Set(captures.map(c => c.platform)))];

  const actions = (
    <div className="flex space-x-2">
      <Button
        onClick={() => router.push('/leads/social-capture/add')}
        className="flex items-center space-x-2"
      >
        <PlusIcon className="h-4 w-4" />
        <span>Add Social Post</span>
      </Button>
      <select
        value={platformFilter}
        onChange={(e) => setPlatformFilter(e.target.value)}
        className="px-3 py-2 border rounded-md text-sm"
      >
        {platforms.map(platform => (
          <option key={platform} value={platform}>
            {platform === 'all' ? 'All Platforms' : platform.charAt(0).toUpperCase() + platform.slice(1)}
          </option>
        ))}
      </select>

      <Button
        variant={filter === 'all' ? 'default' : 'outline'}
        onClick={() => setFilter('all')}
      >
        All
      </Button>
      <Button
        variant={filter === 'processed' ? 'default' : 'outline'}
        onClick={() => setFilter('processed')}
      >
        Processed
      </Button>
      <Button
        variant={filter === 'unprocessed' ? 'default' : 'outline'}
        onClick={() => setFilter('unprocessed')}
      >
        Unprocessed
      </Button>
    </div>
  );

  return (
    <PageLayout
      title="Social Media Capture"
      description="Monitor and manage leads captured from social media platforms with AI-powered content analysis"
      actions={actions}
    >

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Captures</CardTitle>
            <ChatBubbleLeftRightIcon className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{captures.length}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Processed</CardTitle>
            <CheckCircleIcon className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {captures.filter(c => c.processed).length}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Leads Created</CardTitle>
            <CheckCircleIcon className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {captures.filter(c => c.leadId).length}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">High Potential</CardTitle>
            <CheckCircleIcon className="h-4 w-4 text-orange-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {captures.filter(c => c.analysis?.leadPotential === 'high').length}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Social Captures List */}
      <div className="space-y-4">
        {captures.length === 0 ? (
          <Card>
            <CardContent className="flex flex-col items-center justify-center py-12">
              <ChatBubbleLeftRightIcon className="h-12 w-12 text-muted-foreground mb-4" />
              <h3 className="text-lg font-semibold mb-2">No Social Captures</h3>
              <p className="text-muted-foreground text-center">
                No social media posts have been captured yet. Set up social media monitoring to start capturing leads.
              </p>
            </CardContent>
          </Card>
        ) : (
          captures.map((capture) => (
            <Card key={capture.id}>
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="flex items-start space-x-3">
                    <Avatar className="h-10 w-10">
                      <AvatarFallback>
                        {PLATFORM_ICONS[capture.platform as keyof typeof PLATFORM_ICONS] || capture.authorName[0]}
                      </AvatarFallback>
                    </Avatar>
                    <div className="space-y-1">
                      <div className="flex items-center space-x-2">
                        <CardTitle className="text-lg">{capture.authorName}</CardTitle>
                        {capture.authorHandle && (
                          <span className="text-sm text-muted-foreground">@{capture.authorHandle}</span>
                        )}
                        <Badge className={PLATFORM_COLORS[capture.platform as keyof typeof PLATFORM_COLORS] || 'bg-gray-100 text-gray-800'}>
                          {capture.platform}
                        </Badge>
                      </div>
                      <CardDescription>
                        {formatDistanceToNow(new Date(capture.createdAt), { addSuffix: true })}
                      </CardDescription>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    {capture.processed ? (
                      <Badge variant="default" className="bg-green-100 text-green-800">
                        <CheckCircleIcon className="h-3 w-3 mr-1" />
                        Processed
                      </Badge>
                    ) : capture.processingError ? (
                      <Badge variant="destructive">
                        <XCircleIcon className="h-3 w-3 mr-1" />
                        Error
                      </Badge>
                    ) : (
                      <Badge variant="secondary">
                        <ClockIcon className="h-3 w-3 mr-1" />
                        Pending
                      </Badge>
                    )}
                  </div>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="text-sm">
                  <p>{capture.content}</p>
                </div>

                {capture.engagement && (
                  <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                    {capture.engagement.likes > 0 && (
                      <div className="flex items-center space-x-1">
                        <HeartIcon className="h-4 w-4" />
                        <span>{capture.engagement.likes}</span>
                      </div>
                    )}
                    {capture.engagement.shares > 0 && (
                      <div className="flex items-center space-x-1">
                        <ArrowPathIcon className="h-4 w-4" />
                        <span>{capture.engagement.shares}</span>
                      </div>
                    )}
                    {capture.engagement.views > 0 && (
                      <div className="flex items-center space-x-1">
                        <EyeIcon className="h-4 w-4" />
                        <span>{capture.engagement.views}</span>
                      </div>
                    )}
                  </div>
                )}

                {capture.analysis && (
                  <div className="flex flex-wrap gap-2">
                    <Badge className={getSentimentColor(capture.analysis.sentiment)}>
                      {capture.analysis.sentiment}
                    </Badge>
                    <Badge className={getLeadPotentialColor(capture.analysis.leadPotential)}>
                      {capture.analysis.leadPotential} potential
                    </Badge>
                    <Badge variant="outline">
                      {capture.analysis.intent}
                    </Badge>
                    <Badge variant="outline">
                      {Math.round(capture.analysis.businessRelevance * 100)}% relevant
                    </Badge>
                  </div>
                )}

                <div className="flex items-center justify-between text-sm text-muted-foreground">
                  {capture.url && (
                    <a
                      href={capture.url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-blue-600 hover:underline"
                    >
                      View original post
                    </a>
                  )}
                  {capture.leadId && (
                    <span className="text-green-600">Lead created</span>
                  )}
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>
    </PageLayout>
  );
}
