'use client';

import { useState, useEffect } from 'react';
import {
  UserIcon,
  PlayIcon,
  StopIcon,
  ClockIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  XCircleIcon
} from '@heroicons/react/24/outline';
import { ImpersonationSessionWithDetails } from '@/services/tenant-impersonation';

interface TenantUser {
  id: string;
  email: string;
  firstName: string | null;
  lastName: string | null;
  role: string;
  isBeingImpersonated: boolean;
}

interface TenantImpersonationProps {
  tenantId: string;
  tenantName: string;
  onImpersonationStart?: (session: any) => void;
  onImpersonationEnd?: () => void;
}

export default function TenantImpersonation({
  tenantId,
  tenantName,
  onImpersonationStart,
  onImpersonationEnd
}: TenantImpersonationProps) {
  const [users, setUsers] = useState<TenantUser[]>([]);
  const [activeSession, setActiveSession] = useState<ImpersonationSessionWithDetails | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [impersonating, setImpersonating] = useState<string | null>(null);
  const [showReasonModal, setShowReasonModal] = useState(false);
  const [selectedUserId, setSelectedUserId] = useState<string | null>(null);
  const [reason, setReason] = useState('');

  useEffect(() => {
    fetchUsers();
    checkActiveSession();
  }, [tenantId]);

  const fetchUsers = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/admin/impersonation/tenants/${tenantId}/users`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch users');
      }

      const data = await response.json();
      setUsers(data.data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  const checkActiveSession = async () => {
    try {
      const response = await fetch('/api/admin/impersonation/active');
      
      if (response.ok) {
        const data = await response.json();
        setActiveSession(data.data);
      }
    } catch (err) {
      console.error('Error checking active session:', err);
    }
  };

  const handleStartImpersonation = (userId: string) => {
    setSelectedUserId(userId);
    setShowReasonModal(true);
  };

  const confirmStartImpersonation = async () => {
    if (!selectedUserId) return;

    try {
      setImpersonating(selectedUserId);
      
      const response = await fetch('/api/admin/impersonation', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          tenantId,
          userId: selectedUserId,
          reason: reason.trim() || undefined
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to start impersonation');
      }

      const data = await response.json();
      setActiveSession(data.data);
      setShowReasonModal(false);
      setReason('');
      setSelectedUserId(null);
      
      // Refresh users to update impersonation status
      await fetchUsers();
      
      if (onImpersonationStart) {
        onImpersonationStart(data.data);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setImpersonating(null);
    }
  };

  const handleEndImpersonation = async () => {
    if (!activeSession) return;

    try {
      const response = await fetch('/api/admin/impersonation/active', {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Failed to end impersonation');
      }

      setActiveSession(null);
      
      // Refresh users to update impersonation status
      await fetchUsers();
      
      if (onImpersonationEnd) {
        onImpersonationEnd();
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    }
  };

  const getUserDisplayName = (user: TenantUser) => {
    if (user.firstName && user.lastName) {
      return `${user.firstName} ${user.lastName}`;
    }
    return user.email;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            Impersonate Users in {tenantName}
          </h3>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            Temporarily access the platform as another user for support purposes
          </p>
        </div>
      </div>

      {/* Active Session Alert */}
      {activeSession && (
        <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-md p-4">
          <div className="flex">
            <ExclamationTriangleIcon className="h-5 w-5 text-yellow-400" />
            <div className="ml-3 flex-1">
              <h3 className="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                Active Impersonation Session
              </h3>
              <div className="mt-2 text-sm text-yellow-700 dark:text-yellow-300">
                <p>
                  Currently impersonating: <strong>{activeSession.user.email}</strong>
                </p>
                <p>
                  Tenant: <strong>{activeSession.tenant.name}</strong>
                </p>
                <p>
                  Started: {new Date(activeSession.startedAt).toLocaleString()}
                </p>
                {activeSession.reason && (
                  <p>Reason: {activeSession.reason}</p>
                )}
              </div>
              <div className="mt-4">
                <button
                  onClick={handleEndImpersonation}
                  className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-yellow-800 dark:text-yellow-200 bg-yellow-100 dark:bg-yellow-900 hover:bg-yellow-200 dark:hover:bg-yellow-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500"
                >
                  <StopIcon className="w-4 h-4 mr-2" />
                  End Impersonation
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Error Display */}
      {error && (
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-4">
          <div className="flex">
            <XCircleIcon className="h-5 w-5 text-red-400" />
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800 dark:text-red-200">Error</h3>
              <div className="mt-2 text-sm text-red-700 dark:text-red-300">{error}</div>
              <button
                onClick={() => setError(null)}
                className="mt-2 text-sm bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 px-3 py-1 rounded-md hover:bg-red-200 dark:hover:bg-red-800"
              >
                Dismiss
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Users List */}
      <div className="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-md">
        <ul className="divide-y divide-gray-200 dark:divide-gray-700">
          {users.map((user) => (
            <li key={user.id} className="px-6 py-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className="flex-shrink-0">
                    <UserIcon className="h-8 w-8 text-gray-400" />
                  </div>
                  <div>
                    <h4 className="text-sm font-medium text-gray-900 dark:text-white">
                      {getUserDisplayName(user)}
                    </h4>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      {user.email}
                    </p>
                    <div className="flex items-center space-x-2 mt-1">
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                        {user.role}
                      </span>
                      {user.isBeingImpersonated && (
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
                          <ClockIcon className="w-3 h-3 mr-1" />
                          Being Impersonated
                        </span>
                      )}
                    </div>
                  </div>
                </div>
                
                <div>
                  {user.isBeingImpersonated ? (
                    <span className="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm font-medium text-gray-500 dark:text-gray-400 bg-gray-50 dark:bg-gray-700">
                      <ClockIcon className="w-4 h-4 mr-2" />
                      In Use
                    </span>
                  ) : (
                    <button
                      onClick={() => handleStartImpersonation(user.id)}
                      disabled={!!activeSession || impersonating === user.id}
                      className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {impersonating === user.id ? (
                        <>
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                          Starting...
                        </>
                      ) : (
                        <>
                          <PlayIcon className="w-4 h-4 mr-2" />
                          Impersonate
                        </>
                      )}
                    </button>
                  )}
                </div>
              </div>
            </li>
          ))}
        </ul>
        
        {users.length === 0 && (
          <div className="text-center py-12">
            <UserIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">No users found</h3>
            <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
              No active users found in this tenant.
            </p>
          </div>
        )}
      </div>

      {/* Reason Modal */}
      {showReasonModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white dark:bg-gray-800">
            <div className="mt-3">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                Impersonation Reason
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                Please provide a reason for impersonating this user (optional but recommended for audit purposes).
              </p>
              <textarea
                value={reason}
                onChange={(e) => setReason(e.target.value)}
                rows={3}
                className="w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
                placeholder="e.g., Customer support request #12345"
              />
              <div className="flex justify-end space-x-3 mt-4">
                <button
                  onClick={() => {
                    setShowReasonModal(false);
                    setReason('');
                    setSelectedUserId(null);
                  }}
                  className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600"
                >
                  Cancel
                </button>
                <button
                  onClick={confirmStartImpersonation}
                  className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                >
                  Start Impersonation
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
