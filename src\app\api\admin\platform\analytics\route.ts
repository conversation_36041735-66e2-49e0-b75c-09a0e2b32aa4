import { NextRequest, NextResponse } from 'next/server';
import { withSuperAdminAuth } from '@/lib/tenant-context';
import { SuperAdminService } from '@/services/super-admin';

/**
 * @swagger
 * /api/admin/platform/analytics:
 *   get:
 *     summary: Get platform-wide analytics (Super Admin only)
 *     description: Retrieve comprehensive analytics and metrics for the entire platform
 *     tags:
 *       - Super Admin - Analytics
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Platform analytics retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     totalTenants:
 *                       type: number
 *                     activeTenants:
 *                       type: number
 *                     totalUsers:
 *                       type: number
 *                     activeUsers:
 *                       type: number
 *                     totalRevenue:
 *                       type: number
 *                     monthlyGrowth:
 *                       type: number
 *                     topTenantsByUsers:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: string
 *                           name:
 *                             type: string
 *                           userCount:
 *                             type: number
 *                           status:
 *                             type: string
 *                     recentActivity:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           type:
 *                             type: string
 *                           description:
 *                             type: string
 *                           timestamp:
 *                             type: string
 *                             format: date-time
 *                           tenantId:
 *                             type: string
 *                           userId:
 *                             type: string
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Super Admin access required
 */
export const GET = withSuperAdminAuth(async (request: NextRequest) => {
  try {
    const superAdminService = new SuperAdminService();
    const analytics = await superAdminService.getPlatformAnalytics();

    return NextResponse.json({
      success: true,
      data: analytics
    });

  } catch (error) {
    console.error('Get platform analytics error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
});
