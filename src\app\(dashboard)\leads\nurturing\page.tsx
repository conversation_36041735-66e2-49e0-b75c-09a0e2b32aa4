'use client';

import { useSession } from 'next-auth/react';
import { NurturingCampaigns } from '@/components/leads/nurturing-campaigns';
import { PageLayout } from '@/components/layout/page-layout';
import { PermissionGate } from '@/components/ui/permission-gate';
import { PermissionResource, PermissionAction } from '@/types';
import { Card, CardContent } from '@/components/ui/card';
import { EmptyState } from '@/components/ui/empty-state';
import { ExclamationTriangleIcon, PlusIcon } from '@heroicons/react/24/outline';
import { Button } from '@/components/ui/button';
import { useRouter } from 'next/navigation';
import { useSmoothNavigation } from '@/hooks/use-smooth-navigation';

export default function NurturingCampaignsPage() {
  const { data: session } = useSession();
  const router = useRouter();
  const { navigate } = useSmoothNavigation();
  const tenantId = session?.user?.tenants?.[0]?.id;

  const actions = (
    <div className="flex items-center gap-3">
      <Button onClick={() => router.push('/leads/nurturing/new')}>
        <PlusIcon className="w-4 h-4 mr-2" />
        Create Campaign
      </Button>
    </div>
  );

  if (!tenantId) {
    return (
      <PageLayout>
        <div className="flex items-center justify-center h-64">
          <p className="text-muted-foreground">Loading...</p>
        </div>
      </PageLayout>
    );
  }

  return (
    <PermissionGate
      resource={PermissionResource.LEADS}
      action={PermissionAction.READ}
      fallback={
        <PageLayout>
          <Card>
            <CardContent className="p-8">
              <EmptyState
                icon={<ExclamationTriangleIcon className="w-12 h-12 text-muted-foreground" />}
                title="Access Denied"
                description="You don't have permission to view lead nurturing campaigns. Please contact your administrator for access."
                action={{
                  label: 'Back to Dashboard',
                  onClick: () => navigate('/dashboard'),
                  variant: 'primary'
                }}
              />
            </CardContent>
          </Card>
        </PageLayout>
      }
    >
      <PageLayout
        title="Lead Nurturing Campaigns"
        description="Create and manage automated email sequences to nurture your leads with AI-generated content"
        actions={actions}
      >
        <NurturingCampaigns tenantId={tenantId} />
      </PageLayout>
    </PermissionGate>
  );
}
