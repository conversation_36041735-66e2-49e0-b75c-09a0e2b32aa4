#!/usr/bin/env node

/**
 * Minimal Database Seeding Script
 * This script creates only the essential data needed for the application to function
 * Used as a fallback when the full master-seed.js fails
 */

const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

// Initialize Prisma client
let prisma;

try {
  prisma = new PrismaClient({
    log: ['error'],
    errorFormat: 'minimal'
  });
  console.log('✅ Prisma client initialized successfully');
} catch (error) {
  console.error('❌ Failed to initialize Prisma client:', error.message);
  process.exit(1);
}

/**
 * Create minimal essential data
 */
async function minimalSeed() {
  console.log('🌱 Starting Minimal Database Seeding...\n');

  try {
    // Test database connection
    await prisma.$connect();
    console.log('✅ Database connection established');

    // Step 1: Create a basic subscription plan (if table exists)
    let plan = null;
    try {
      plan = await prisma.subscriptionPlan.upsert({
        where: { id: 'plan_basic' },
        update: {},
        create: {
          id: 'plan_basic',
          name: 'Basic',
          description: 'Basic CRM functionality',
          priceMonthly: 0.00,
          priceYearly: 0.00,
          features: {
            contacts: 1000,
            leads: 500,
            users: 5,
            basic_features: true
          },
          limits: {
            contacts: 1000,
            leads: 500,
            users: 5
          },
          isActive: true
        }
      });
      console.log('✅ Created basic subscription plan');
    } catch (error) {
      console.log('⚠️  Subscription plans table not available, skipping...');
    }

    // Step 2: Create default tenant
    let tenant = null;
    try {
      tenant = await prisma.tenant.upsert({
        where: { slug: 'default' },
        update: {},
        create: {
          id: 'tenant_default',
          name: 'Default Organization',
          slug: 'default',
          status: 'active',
          settings: {
            locale: 'en',
            timezone: 'UTC',
            currency: 'USD'
          },
          branding: {
            company_name: 'Default Organization'
          }
        }
      });
      console.log('✅ Created default tenant');
    } catch (error) {
      console.log('❌ Failed to create tenant:', error.message);
      throw error;
    }

    // Step 3: Create admin user
    let user = null;
    try {
      const passwordHash = await bcrypt.hash('Admin123!', 12);
      
      user = await prisma.user.upsert({
        where: { email: '<EMAIL>' },
        update: {},
        create: {
          id: 'user_admin_' + Date.now(),
          email: '<EMAIL>',
          passwordHash: passwordHash,
          firstName: 'Admin',
          lastName: 'User',
          locale: 'en',
          timezone: 'UTC',
          emailVerified: true,
          isPlatformAdmin: true,
          status: 'active'
        }
      });
      console.log('✅ Created admin user');
    } catch (error) {
      console.log('❌ Failed to create user:', error.message);
      throw error;
    }

    // Step 4: Link user to tenant
    try {
      await prisma.tenantUser.upsert({
        where: {
          tenantId_userId: {
            tenantId: tenant.id,
            userId: user.id
          }
        },
        update: {},
        create: {
          id: 'tenant_user_admin_' + Date.now(),
          tenantId: tenant.id,
          userId: user.id,
          isTenantAdmin: true,
          role: 'admin',
          status: 'active'
        }
      });
      console.log('✅ Linked user to tenant');
    } catch (error) {
      console.log('❌ Failed to link user to tenant:', error.message);
      throw error;
    }

    // Step 5: Create subscription (if plan exists)
    if (plan && tenant) {
      try {
        await prisma.subscription.upsert({
          where: {
            tenantId_planId: {
              tenantId: tenant.id,
              planId: plan.id
            }
          },
          update: {},
          create: {
            tenantId: tenant.id,
            planId: plan.id,
            status: 'active',
            currentPeriodStart: new Date(),
            currentPeriodEnd: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1 year
            stripeSubscriptionId: null,
            stripeCustomerId: null
          }
        });
        console.log('✅ Created subscription');
      } catch (error) {
        console.log('⚠️  Subscription table not available, skipping...');
      }
    }

    // Step 6: Create basic lead source (if table exists)
    try {
      const existingLeadSource = await prisma.leadSource.findFirst({
        where: {
          tenantId: tenant.id,
          name: 'Website'
        }
      });

      if (!existingLeadSource) {
        await prisma.leadSource.create({
          data: {
            tenantId: tenant.id,
            name: 'Website',
            description: 'Leads from website contact forms',
            isActive: true,
            color: '#4f46e5'
          }
        });
        console.log('✅ Created basic lead source');
      } else {
        console.log('✅ Basic lead source already exists');
      }
    } catch (error) {
      console.log('⚠️  Lead sources table not available, skipping...');
    }

    // Step 7: Create basic sales stage (if table exists)
    try {
      const existingSalesStage = await prisma.salesStage.findFirst({
        where: {
          tenantId: tenant.id,
          name: 'New'
        }
      });

      if (!existingSalesStage) {
        await prisma.salesStage.create({
          data: {
            tenantId: tenant.id,
            name: 'New',
            description: 'New opportunities',
            orderIndex: 1,
            probabilityMin: 10,
            probabilityMax: 20,
            color: '#10b981'
          }
        });
        console.log('✅ Created basic sales stage');
      } else {
        console.log('✅ Basic sales stage already exists');
      }
    } catch (error) {
      console.log('⚠️  Sales stages table not available, skipping...');
    }

    console.log('\n🎉 Minimal database seeding completed successfully!');
    console.log('\n🔑 Default login credentials:');
    console.log('   Email: <EMAIL>');
    console.log('   Password: Admin123!');

    return {
      tenant,
      user,
      plan
    };

  } catch (error) {
    console.error('\n❌ Minimal seeding failed:', error);
    console.error('Stack trace:', error.stack);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the minimal seeding if this script is called directly
if (require.main === module) {
  minimalSeed().catch((error) => {
    console.error('Failed to seed database:', error);
    process.exit(1);
  });
}

module.exports = { minimalSeed };
