@echo off
REM Windows batch version of volume-manager.sh for CRM Platform

setlocal enabledelayedexpansion

REM Configuration
set "ACTION=%~1"
set "ENVIRONMENT=%~2"
if "%ENVIRONMENT%"=="" set "ENVIRONMENT=dev"
set "VOLUME_NAME=%~3"
set "BACKUP_DIR=backups\volumes"
set "TIMESTAMP=%DATE:~10,4%%DATE:~4,2%%DATE:~7,2%_%TIME:~0,2%%TIME:~3,2%%TIME:~6,2%"
set "TIMESTAMP=%TIMESTAMP: =0%"

REM Colors for terminal output
set "RED=[91m"
set "GREEN=[92m"
set "YELLOW=[93m"
set "BLUE=[94m"
set "NC=[0m"

REM Function to display usage information
:usage
if "%~1"=="show" (
    echo Usage: %0 [action] [environment] [volume_name]
    echo.
    echo Actions:
    echo   list        List all Docker volumes
    echo   backup      Backup a Docker volume
    echo   restore     Restore a Docker volume from backup
    echo   inspect     Inspect a Docker volume
    echo   clean       Remove a Docker volume
    echo.
    echo Environments:
    echo   dev         Development environment ^(default^)
    echo   staging     Staging environment
    echo   prod        Production environment
    echo.
    echo Examples:
    echo   %0 list dev                  # List all volumes in dev environment
    echo   %0 backup dev crm_postgres   # Backup postgres volume in dev environment
    echo   %0 restore prod crm_uploads  # Restore uploads volume in prod environment
    echo   %0 inspect staging crm_redis # Inspect redis volume in staging environment
    echo   %0 clean dev crm_temp        # Remove temp volume in dev environment
    exit /b 1
)

REM Function to set up environment-specific configuration
:setup_environment
if "%ENVIRONMENT%"=="dev" (
    echo %GREEN%Using development environment...%NC%
    set "COMPOSE_FILE=docker-compose.yml"
    set "COMPOSE_OVERRIDE=-f docker-compose.override.yml"
    set "VOLUME_PREFIX=crm"
) else if "%ENVIRONMENT%"=="staging" (
    echo %GREEN%Using staging environment...%NC%
    set "COMPOSE_FILE=docker-compose.yml"
    set "COMPOSE_OVERRIDE=-f docker-compose.staging.yml"
    set "VOLUME_PREFIX=crm_staging"
) else if "%ENVIRONMENT%"=="prod" (
    echo %GREEN%Using production environment...%NC%
    set "COMPOSE_FILE=docker-compose.yml"
    set "COMPOSE_OVERRIDE=-f docker-compose.prod.yml"
    set "VOLUME_PREFIX=crm_prod"
) else (
    echo %RED%Error: Unknown environment '%ENVIRONMENT%'%NC%
    goto :usage show
)

REM Check if Docker is installed
:check_docker
docker --version >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo %RED%Error: Docker is not installed or not in PATH%NC%
    exit /b 1
)

REM Create backup directory if it doesn't exist
if not exist "%BACKUP_DIR%" (
    echo %YELLOW%Creating backup directory: %BACKUP_DIR%%NC%
    mkdir "%BACKUP_DIR%"
)

REM Function to list volumes
:list_volumes
echo %YELLOW%Listing Docker volumes for %ENVIRONMENT% environment...%NC%

REM Get all volumes
for /f "tokens=*" %%v in ('docker volume ls --format "{{.Name}}"') do (
    REM Filter volumes by environment prefix
    echo %%v | findstr /i /c:"%VOLUME_PREFIX%" >nul
    if !ERRORLEVEL! equ 0 (
        REM Get volume details
        for /f "tokens=*" %%d in ('docker volume inspect --format "{{.Name}} ({{.Driver}}) - {{.Mountpoint}}" %%v') do (
            echo %%d
        )
    )
)

exit /b 0

REM Function to backup a volume
:backup_volume
if "%VOLUME_NAME%"=="" (
    echo %RED%Error: No volume name specified.%NC%
    goto :usage show
)

echo %YELLOW%Backing up volume %VOLUME_NAME%...%NC%

REM Check if volume exists
docker volume inspect %VOLUME_NAME% >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo %RED%Error: Volume %VOLUME_NAME% not found.%NC%
    exit /b 1
)

REM Create a temporary container to access the volume
echo %YELLOW%Creating temporary container to access volume...%NC%
docker run --rm -v %VOLUME_NAME%:/source -v "%CD%\%BACKUP_DIR%:/backup" --name temp_backup alpine sh -c "cd /source && tar -czf /backup/%VOLUME_NAME%_%TIMESTAMP%.tar.gz ."

if %ERRORLEVEL% equ 0 (
    echo %GREEN%Volume %VOLUME_NAME% backed up successfully to %BACKUP_DIR%\%VOLUME_NAME%_%TIMESTAMP%.tar.gz%NC%
) else (
    echo %RED%Failed to backup volume %VOLUME_NAME%.%NC%
    exit /b 1
)

exit /b 0

REM Function to restore a volume
:restore_volume
if "%VOLUME_NAME%"=="" (
    echo %RED%Error: No volume name specified.%NC%
    goto :usage show
)

REM List available backups for the volume
echo %YELLOW%Available backups for %VOLUME_NAME%:%NC%
set "backup_count=0"
set "backup_list="

for /f "tokens=*" %%f in ('dir /b "%BACKUP_DIR%\%VOLUME_NAME%_*.tar.gz" 2^>nul') do (
    set /a backup_count+=1
    echo !backup_count!: %%f
    set "backup_list=!backup_list!%%f "
)

if %backup_count% equ 0 (
    echo %RED%No backups found for volume %VOLUME_NAME%.%NC%
    exit /b 1
)

REM Prompt user to select a backup
set /p backup_num="Enter the number of the backup to restore: "

REM Validate input
if %backup_num% leq 0 (
    echo %RED%Invalid selection.%NC%
    exit /b 1
)

if %backup_num% gtr %backup_count% (
    echo %RED%Invalid selection.%NC%
    exit /b 1
)

REM Get the selected backup file
set "counter=0"
for %%a in (%backup_list%) do (
    set /a counter+=1
    if !counter! equ %backup_num% (
        set "selected_backup=%%a"
    )
)

REM Confirm restore
echo %RED%WARNING: This will overwrite the current contents of volume %VOLUME_NAME%!%NC%
set /p confirm="Are you sure you want to continue? (y/n): "
if /i not "%confirm%"=="y" (
    echo %YELLOW%Restore cancelled.%NC%
    exit /b 0
)

REM Check if volume exists
docker volume inspect %VOLUME_NAME% >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo %YELLOW%Volume %VOLUME_NAME% does not exist. Creating...%NC%
    docker volume create %VOLUME_NAME%
)

REM Create a temporary container to restore the volume
echo %YELLOW%Restoring volume %VOLUME_NAME% from %selected_backup%...%NC%
docker run --rm -v %VOLUME_NAME%:/target -v "%CD%\%BACKUP_DIR%:/backup" --name temp_restore alpine sh -c "rm -rf /target/* && tar -xzf /backup/%selected_backup% -C /target"

if %ERRORLEVEL% equ 0 (
    echo %GREEN%Volume %VOLUME_NAME% restored successfully from %selected_backup%.%NC%
) else (
    echo %RED%Failed to restore volume %VOLUME_NAME%.%NC%
    exit /b 1
)

exit /b 0

REM Function to inspect a volume
:inspect_volume
if "%VOLUME_NAME%"=="" (
    echo %RED%Error: No volume name specified.%NC%
    goto :usage show
)

echo %YELLOW%Inspecting volume %VOLUME_NAME%...%NC%

REM Check if volume exists
docker volume inspect %VOLUME_NAME% >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo %RED%Error: Volume %VOLUME_NAME% not found.%NC%
    exit /b 1
)

REM Display volume details
docker volume inspect %VOLUME_NAME%

REM List files in the volume
echo %YELLOW%Files in volume %VOLUME_NAME%:%NC%
docker run --rm -v %VOLUME_NAME%:/inspect --name temp_inspect alpine sh -c "ls -la /inspect"

REM Display volume size
echo %YELLOW%Volume size:%NC%
docker run --rm -v %VOLUME_NAME%:/inspect --name temp_inspect alpine sh -c "du -sh /inspect"

exit /b 0

REM Function to clean a volume
:clean_volume
if "%VOLUME_NAME%"=="" (
    echo %RED%Error: No volume name specified.%NC%
    goto :usage show
)

echo %RED%WARNING: This will permanently delete volume %VOLUME_NAME% and all its data!%NC%
set /p confirm="Are you sure you want to continue? (y/n): "
if /i not "%confirm%"=="y" (
    echo %YELLOW%Clean operation cancelled.%NC%
    exit /b 0
)

REM Check if volume exists
docker volume inspect %VOLUME_NAME% >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo %RED%Error: Volume %VOLUME_NAME% not found.%NC%
    exit /b 1
)

REM Check if volume is in use
for /f "tokens=*" %%c in ('docker ps -q -a --filter "volume=%VOLUME_NAME%"') do (
    echo %RED%Error: Volume %VOLUME_NAME% is in use by container %%c.%NC%
    echo %YELLOW%Stop and remove the container first.%NC%
    exit /b 1
)

REM Remove the volume
docker volume rm %VOLUME_NAME%

if %ERRORLEVEL% equ 0 (
    echo %GREEN%Volume %VOLUME_NAME% removed successfully.%NC%
) else (
    echo %RED%Failed to remove volume %VOLUME_NAME%.%NC%
    exit /b 1
)

exit /b 0

REM Main script execution
if "%ACTION%"=="" (
    echo %RED%Error: No action specified.%NC%
    goto :usage show
)

REM Check Docker installation
call :check_docker

REM Setup environment
call :setup_environment

REM Execute the requested action
if "%ACTION%"=="list" (
    call :list_volumes
) else if "%ACTION%"=="backup" (
    call :backup_volume
) else if "%ACTION%"=="restore" (
    call :restore_volume
) else if "%ACTION%"=="inspect" (
    call :inspect_volume
) else if "%ACTION%"=="clean" (
    call :clean_volume
) else (
    echo %RED%Unknown action: %ACTION%%NC%
    goto :usage show
)

endlocal
