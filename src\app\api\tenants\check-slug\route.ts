import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { TenantProvisioningService } from '@/services/tenant-provisioning';

const checkSlugSchema = z.object({
  slug: z.string()
    .min(3, 'Slug must be at least 3 characters')
    .max(50, 'Slug must be less than 50 characters')
    .regex(/^[a-z0-9-]+$/, 'Slug can only contain lowercase letters, numbers, and hyphens'),
});

/**
 * @swagger
 * /api/tenants/check-slug:
 *   post:
 *     summary: Check if tenant slug is available
 *     description: Validates if a tenant slug is available for registration
 *     tags:
 *       - Tenants
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - slug
 *             properties:
 *               slug:
 *                 type: string
 *                 description: The slug to check
 *                 example: "my-company"
 *     responses:
 *       200:
 *         description: Slug availability status
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 available:
 *                   type: boolean
 *                   description: Whether the slug is available
 *                 slug:
 *                   type: string
 *                   description: The checked slug
 *       400:
 *         description: Validation error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                 details:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       field:
 *                         type: string
 *                       message:
 *                         type: string
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const validatedData = checkSlugSchema.parse(body);

    const provisioningService = new TenantProvisioningService();
    const isAvailable = await provisioningService.isSlugAvailable(validatedData.slug);

    return NextResponse.json({
      available: isAvailable,
      slug: validatedData.slug
    });

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          error: 'Validation failed',
          details: error.errors.map(e => ({
            field: e.path.join('.'),
            message: e.message
          }))
        },
        { status: 400 }
      );
    }

    console.error('Check slug error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * GET endpoint for checking slug availability via query parameter
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const slug = searchParams.get('slug');

    if (!slug) {
      return NextResponse.json(
        { error: 'Slug parameter is required' },
        { status: 400 }
      );
    }

    // Validate slug format
    const slugRegex = /^[a-z0-9-]+$/;
    if (!slugRegex.test(slug) || slug.length < 3 || slug.length > 50) {
      return NextResponse.json(
        {
          available: false,
          error: 'Invalid slug format. Use lowercase letters, numbers, and hyphens only (3-50 characters)'
        },
        { status: 400 }
      );
    }

    const provisioningService = new TenantProvisioningService();
    const isAvailable = await provisioningService.isSlugAvailable(slug);

    return NextResponse.json({
      available: isAvailable,
      slug
    });

  } catch (error) {
    console.error('Check slug error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
