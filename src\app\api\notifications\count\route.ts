import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { unifiedNotificationService } from '@/services/unified-notification-service';

/**
 * GET /api/notifications/count - Get unread notification count for current user
 */
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get tenantId from user's tenants array
    let tenantId: string | null = null;
    if (session.user.tenants && session.user.tenants.length > 0) {
      tenantId = session.user.tenants[0].id;
    }

    if (!tenantId) {
      return NextResponse.json({ error: 'Tenant not found' }, { status: 400 });
    }

    // Get query parameters for filtering
    const url = new URL(request.url);
    const category = url.searchParams.get('category') || undefined;
    const type = url.searchParams.get('type') || undefined;

    const unreadCount = await unifiedNotificationService.getUnreadNotificationCount(
      session.user.id,
      tenantId,
      { category, type }
    );

    return NextResponse.json({
      success: true,
      data: {
        unreadCount,
        category,
        type
      }
    });
  } catch (error) {
    console.error('Error fetching notification count:', error);
    return NextResponse.json(
      { error: 'Failed to fetch notification count' },
      { status: 500 }
    );
  }
}
