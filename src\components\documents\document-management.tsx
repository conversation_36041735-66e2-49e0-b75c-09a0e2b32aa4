'use client';

import React, { useState, useEffect } from 'react';
import { DocumentWithRelations } from '@/services/document-management';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Upload,
  Search,
  Filter,
  Grid,
  List,
  Download,
  FileText,
  BarChart3,
} from 'lucide-react';
import { DocumentList } from './document-list';
import { DocumentUploadDialog } from './document-upload-dialog';
import { DocumentDetailsDialog } from './document-details-dialog';
import { usePermissions } from '@/components/providers/permission-provider';
import { PermissionResource, PermissionAction } from '@/types';
import { PermissionGate } from '@/components/ui/permission-gate';
import { useEnhancedDeleteDialog, deleteConfigurations } from '@/components/ui/enhanced-delete-dialog';
import { useToast } from '@/hooks/use-toast';
import { cn } from '@/lib/utils';
import { downloadFile } from '@/lib/download-utils';

interface DocumentManagementProps {
  relatedToType: string;
  relatedToId: string;
  title?: string;
  className?: string;
  showStats?: boolean;
  allowUpload?: boolean;
  opportunityTitle?: string;
  companyName?: string;
}

export function DocumentManagement({
  relatedToType,
  relatedToId,
  title = 'Documents',
  className,
  showStats = true,
  allowUpload = true,
  opportunityTitle,
  companyName,
}: DocumentManagementProps) {
  const { hasPermission } = usePermissions();
  const { toast } = useToast();
  const [documents, setDocuments] = useState<DocumentWithRelations[]>([]);
  const [loading, setLoading] = useState(true);
  const [uploadDialogOpen, setUploadDialogOpen] = useState(false);
  const [detailsDialogOpen, setDetailsDialogOpen] = useState(false);
  const [selectedDocument, setSelectedDocument] = useState<DocumentWithRelations | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [mimeTypeFilter, setMimeTypeFilter] = useState<string>('all');
  const [viewMode, setViewMode] = useState<'list' | 'grid'>('list');
  const [stats, setStats] = useState<any>(null);
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [aiAvailable, setAiAvailable] = useState(false);

  const { showDeleteDialog, DialogComponent } = useEnhancedDeleteDialog();

  // Load documents
  const loadDocuments = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        relatedToType,
        relatedToId,
        page: page.toString(),
        limit: '20',
        ...(searchTerm && { search: searchTerm }),
        ...(mimeTypeFilter && mimeTypeFilter !== 'all' && { mimeType: mimeTypeFilter }),
      });

      const response = await fetch(`/api/documents?${params}`);
      const data = await response.json();

      if (response.ok) {
        setDocuments(data.data.documents);
        setTotalPages(data.data.pagination.pages);
      } else {
        toast({ description: 'Failed to load documents', variant: 'destructive' });
      }
    } catch (error) {
      console.error('Error loading documents:', error);
      toast({ description: 'Failed to load documents', variant: 'destructive' });
    } finally {
      setLoading(false);
    }
  };

  // Load stats
  const loadStats = async () => {
    if (!showStats) return;

    try {
      const response = await fetch('/api/documents/stats');
      const data = await response.json();

      if (response.ok) {
        setStats(data.data.stats);
      }
    } catch (error) {
      console.error('Error loading document stats:', error);
    }
  };

  // Check AI availability
  const checkAiAvailability = async () => {
    try {
      const response = await fetch('/api/documents/analyze');
      const data = await response.json();
      setAiAvailable(data.success && data.data.available);
    } catch (error) {
      console.error('Failed to check AI availability:', error);
      setAiAvailable(false);
    }
  };

  useEffect(() => {
    loadDocuments();
    loadStats();
    checkAiAvailability();
  }, [relatedToType, relatedToId, page, searchTerm, mimeTypeFilter]);

  const handleUploadComplete = (uploadedDocuments: any[]) => {
    toast({
      description: `${uploadedDocuments.length} document(s) uploaded successfully`,
      variant: 'success'
    });
    loadDocuments();
    loadStats();
    setUploadDialogOpen(false);
  };

  const handleUploadError = (error: string) => {
    toast({ description: error, variant: 'destructive' });
  };

  const handleView = (document: DocumentWithRelations) => {
    setSelectedDocument(document);
    setDetailsDialogOpen(true);
  };

  const handleOpenFile = (document: DocumentWithRelations) => {
    // Open document in new tab
    window.open(`/api/documents/${document.id}/download`, '_blank');
  };

  const handleDownload = async (document: DocumentWithRelations) => {
    try {
      await downloadFile(`/api/documents/${document.id}/download`, document.name);
      toast({ description: 'Document downloaded successfully', variant: 'success' });
    } catch (error) {
      console.error('Error downloading document:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to download document';
      toast({ description: errorMessage, variant: 'destructive' });
    }
  };

  const handleEdit = (document: DocumentWithRelations) => {
    // TODO: Implement edit functionality
    toast({ description: 'Edit functionality coming soon', variant: 'default' });
  };

  const handleDelete = async (document: DocumentWithRelations) => {
    console.log('Delete button clicked for document:', document.name);

    try {
      const confirmed = await showDeleteDialog({
        ...deleteConfigurations.document(document.name),
        onConfirm: async () => {
          console.log('Delete confirmed, making API call...');
          const response = await fetch(`/api/documents/${document.id}`, {
            method: 'DELETE',
          });

          if (!response.ok) {
            const data = await response.json();
            throw new Error(data.error || 'Failed to delete document');
          }
          console.log('Document deleted successfully from API');
        },
      });

      console.log('Dialog result:', confirmed);

      if (confirmed) {
        toast({ description: 'Document deleted successfully', variant: 'success' });
        loadDocuments();
        loadStats();
      }
    } catch (error) {
      console.error('Error in handleDelete:', error);
      toast({ description: 'Failed to delete document', variant: 'destructive' });
    }
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className={cn('space-y-6', className)}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">{title}</h2>
          <p className="text-sm text-gray-500">
            Manage documents for this {relatedToType}
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <PermissionGate
            resource={PermissionResource.DOCUMENTS}
            action={PermissionAction.CREATE}
          >
            {allowUpload && (
              <Button onClick={() => setUploadDialogOpen(true)}>
                <Upload className="w-4 h-4 mr-2" />
                Upload Documents
              </Button>
            )}
          </PermissionGate>
        </div>
      </div>

      {/* Stats */}
      {showStats && stats && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <FileText className="w-5 h-5 text-blue-500" />
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Documents</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.totalDocuments}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <BarChart3 className="w-5 h-5 text-green-500" />
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Size</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {formatFileSize(stats.totalSize)}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <Upload className="w-5 h-5 text-purple-500" />
                <div>
                  <p className="text-sm font-medium text-gray-600">Recent Uploads</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.recentUploads}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <FileText className="w-5 h-5 text-orange-500" />
                <div>
                  <p className="text-sm font-medium text-gray-600">File Types</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {Object.keys(stats.byType).length}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Filters and Controls */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col sm:flex-row gap-4 items-center justify-between">
            <div className="flex flex-1 items-center space-x-2">
              <div className="relative flex-1 max-w-sm">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="Search documents..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              <Select value={mimeTypeFilter} onValueChange={setMimeTypeFilter}>
                <SelectTrigger className="w-48">
                  <SelectValue placeholder="Filter by type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  <SelectItem value="application/pdf">PDF</SelectItem>
                  <SelectItem value="image/">Images</SelectItem>
                  <SelectItem value="application/msword">Word Documents</SelectItem>
                  <SelectItem value="application/vnd.ms-excel">Excel Files</SelectItem>
                  <SelectItem value="application/zip">Archives</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant={viewMode === 'list' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('list')}
              >
                <List className="w-4 h-4" />
              </Button>
              <Button
                variant={viewMode === 'grid' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('grid')}
              >
                <Grid className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Document List */}
      <DocumentList
        documents={documents}
        onView={handleView}
        onEdit={handleEdit}
        onDelete={handleDelete}
        onDownload={handleDownload}
        loading={loading}
        viewMode={viewMode}
      />

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex items-center justify-center space-x-2">
          <Button
            variant="outline"
            onClick={() => setPage(p => Math.max(1, p - 1))}
            disabled={page === 1}
          >
            Previous
          </Button>
          <span className="text-sm text-gray-600">
            Page {page} of {totalPages}
          </span>
          <Button
            variant="outline"
            onClick={() => setPage(p => Math.min(totalPages, p + 1))}
            disabled={page === totalPages}
          >
            Next
          </Button>
        </div>
      )}

      {/* Upload Dialog */}
      <DocumentUploadDialog
        open={uploadDialogOpen}
        onOpenChange={setUploadDialogOpen}
        relatedToType={relatedToType}
        relatedToId={relatedToId}
        onUploadComplete={handleUploadComplete}
        onUploadError={handleUploadError}
        opportunityTitle={opportunityTitle}
        companyName={companyName}
      />

      {/* Document Details Dialog */}
      <DocumentDetailsDialog
        open={detailsDialogOpen}
        onOpenChange={setDetailsDialogOpen}
        document={selectedDocument}
        onDownload={handleDownload}
        onView={handleOpenFile}
        aiAvailable={aiAvailable}
      />

      {/* Delete Dialog */}
      <DialogComponent />
    </div>
  );
}
