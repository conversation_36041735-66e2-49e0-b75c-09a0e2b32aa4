import { NextRequest, NextResponse } from 'next/server';
import { withSuperAdminAuth } from '@/lib/tenant-context';
import { SuperAdminService } from '@/services/super-admin';

/**
 * @swagger
 * /api/admin/platform/analytics/system-health:
 *   get:
 *     summary: Get system health metrics (Super Admin only)
 *     description: Retrieve real-time system performance and health metrics
 *     tags:
 *       - Super Admin - Analytics
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: System health metrics retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     uptime:
 *                       type: number
 *                       description: System uptime percentage
 *                     responseTime:
 *                       type: number
 *                       description: Average response time in milliseconds
 *                     errorRate:
 *                       type: number
 *                       description: Error rate percentage
 *                     databaseConnections:
 *                       type: number
 *                       description: Number of active database connections
 *                     memoryUsage:
 *                       type: number
 *                       description: Memory usage percentage
 *                     cpuUsage:
 *                       type: number
 *                       description: CPU usage percentage
 *                     activeConnections:
 *                       type: number
 *                       description: Number of active user connections
 *                     queueSize:
 *                       type: number
 *                       description: Background job queue size
 *                     lastUpdated:
 *                       type: string
 *                       format: date-time
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Super Admin access required
 */
export const GET = withSuperAdminAuth(async (request: NextRequest) => {
  try {
    const { searchParams } = new URL(request.url);
    const includeHistory = searchParams.get('includeHistory') === 'true';
    const timeRange = searchParams.get('timeRange') || '1h';

    const superAdminService = new SuperAdminService();
    const systemHealth = await superAdminService.getSystemHealthMetrics();

    // Get additional health metrics for dashboard
    const healthData: any = {
      current: systemHealth,
      timestamp: new Date().toISOString(),
    };

    // Add historical data if requested
    if (includeHistory) {
      healthData.history = await superAdminService.getSystemHealthHistory(timeRange);
    }

    // Add service status checks
    healthData.services = await superAdminService.getServiceHealthStatus();

    return NextResponse.json({
      success: true,
      data: healthData
    });

  } catch (error) {
    console.error('Get system health error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
});
