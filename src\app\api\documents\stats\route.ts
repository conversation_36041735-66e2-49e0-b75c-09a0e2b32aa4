import { NextResponse } from 'next/server';
import { DocumentManagementService } from '@/services/document-management';
import { withPermission, AuthenticatedRequest } from '@/lib/permission-middleware';
import { PermissionResource, PermissionAction } from '@/types';

const documentService = new DocumentManagementService();

/**
 * GET /api/documents/stats
 * Get document statistics for the tenant
 */
export const GET = withPermission({
  resource: PermissionResource.DOCUMENTS,
  action: PermissionAction.READ,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest) => {
  try {
    const stats = await documentService.getDocumentStats(req.tenantId);

    return NextResponse.json({
      success: true,
      data: { stats },
      message: 'Document statistics retrieved successfully'
    });

  } catch (error) {
    console.error('Get document stats error:', error);

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
});
