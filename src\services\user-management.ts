import { prisma } from '@/lib/prisma';
import bcrypt from 'bcryptjs';
import { UserWithTenants } from '@/types';
import { UnifiedNotificationService } from './unified-notification-service';

export interface CreateUserData {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  phone?: string;
  locale?: string;
  timezone?: string;
}

export interface InviteUserData {
  email: string;
  firstName: string;
  lastName: string;
  role: string;
  tenantId: string;
  invitedBy: string;
}

export interface UpdateUserData {
  firstName?: string;
  lastName?: string;
  phone?: string;
  locale?: string;
  timezone?: string;
  avatarUrl?: string;
  status?: string;
}

export interface CreateUserWithTeamData extends CreateUserData {
  teamId?: string;
  role?: string;
  department?: string;
  jobTitle?: string;
  sendWelcomeEmail?: boolean;
}

export interface UserListOptions {
  page?: number;
  limit?: number;
  search?: string;
  role?: string;
  status?: string;
  teamId?: string;
}

export interface UserListResult {
  users: UserWithTenantInfo[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export interface User {
  id: string;
  email: string;
  firstName: string | null;
  lastName: string | null;
  avatarUrl: string | null;
  status: string;
  lastLogin: Date | null;
}

export interface UserWithTenantInfo {
  id: string;
  email: string;
  firstName: string | null;
  lastName: string | null;
  avatarUrl: string | null;
  status: string;
  lastLogin: Date | null;
  tenantUser: {
    id: string;
    role: string;
    status: string;
    joinedAt: Date;
    teamId: string | null;
    department: string | null;
    jobTitle: string | null;
    team?: {
      id: string;
      name: string;
      color: string | null;
    } | null;
  };
}

export class UserManagementService {
  private notificationService = new UnifiedNotificationService();

  // Create a new user
  async createUser(userData: CreateUserData): Promise<UserWithTenants> {
    const hashedPassword = await bcrypt.hash(userData.password, 12);

    const user = await prisma.user.create({
      data: {
        email: userData.email,
        passwordHash: hashedPassword,
        firstName: userData.firstName,
        lastName: userData.lastName,
        phone: userData.phone,
        locale: userData.locale || 'en',
        timezone: userData.timezone || 'UTC',
        status: 'active'
      },
      include: {
        tenantUsers: {
          include: {
            tenant: true
          }
        }
      }
    });

    return {
      ...user,
      tenants: user.tenantUsers.map(tu => ({
        id: tu.tenant.id,
        name: tu.tenant.name,
        slug: tu.tenant.slug,
        role: tu.role,
        isTenantAdmin: tu.isTenantAdmin
      }))
    };
  }

  // Invite user to tenant
  async inviteUserToTenant(inviteData: InviteUserData): Promise<void> {
    await prisma.$transaction(async (tx) => {
      // Check if user already exists
      let user = await tx.user.findUnique({
        where: { email: inviteData.email }
      });

      if (!user) {
        // Create user without password (they'll set it on first login)
        user = await tx.user.create({
          data: {
            email: inviteData.email,
            firstName: inviteData.firstName,
            lastName: inviteData.lastName,
            emailVerified: false,
            status: 'pending'
          }
        });
      }

      // Check if user is already in this tenant
      const existingTenantUser = await tx.tenantUser.findUnique({
        where: {
          tenantId_userId: {
            tenantId: inviteData.tenantId,
            userId: user.id
          }
        }
      });

      if (existingTenantUser) {
        throw new Error('User is already a member of this tenant');
      }

      // Add user to tenant
      await tx.tenantUser.create({
        data: {
          tenantId: inviteData.tenantId,
          userId: user.id,
          role: inviteData.role,
          status: 'active'
        }
      });

      // Assign role if it exists
      const role = await tx.role.findFirst({
        where: {
          tenantId: inviteData.tenantId,
          name: inviteData.role
        }
      });

      if (role) {
        await tx.userRole.create({
          data: {
            tenantId: inviteData.tenantId,
            userId: user.id,
            roleId: role.id,
            assignedBy: inviteData.invitedBy
          }
        });
      }

      // Log the invitation
      await tx.auditLog.create({
        data: {
          tenantId: inviteData.tenantId,
          userId: inviteData.invitedBy,
          action: 'USER_INVITED',
          resourceType: 'user',
          resourceId: user.id,
          newValues: {
            email: inviteData.email,
            role: inviteData.role
          }
        }
      });
    });

    // TODO: Send invitation email
    await this.sendInvitationEmail(inviteData.email, inviteData.tenantId);
  }

  // Create user and add to tenant with team assignment
  async createUserWithTeam(userData: CreateUserWithTeamData, tenantId: string, createdBy: string): Promise<UserWithTenants> {
    return await prisma.$transaction(async (tx) => {
      // Create the user
      const hashedPassword = await bcrypt.hash(userData.password, 12);

      const user = await tx.user.create({
        data: {
          email: userData.email,
          passwordHash: hashedPassword,
          firstName: userData.firstName,
          lastName: userData.lastName,
          locale: userData.locale || 'en',
          timezone: userData.timezone || 'UTC',
          status: 'active'
        }
      });

      // Get default role if none provided
      let roleToAssign = userData.role;
      if (!roleToAssign) {
        // Find a default role (prefer 'User', 'Sales Rep', or first available role)
        const defaultRole = await tx.role.findFirst({
          where: {
            tenantId,
            name: {
              notIn: ['Super Admin', 'super_admin', 'Platform Admin']
            }
          },
          orderBy: [
            { name: 'asc' }
          ]
        });
        roleToAssign = defaultRole?.name || 'User';
      }

      // Add user to tenant
      await tx.tenantUser.create({
        data: {
          tenantId,
          userId: user.id,
          role: roleToAssign,
          status: 'active',
          teamId: userData.teamId,
          department: userData.department,
          jobTitle: userData.jobTitle,
          invitedBy: createdBy,
          invitedAt: new Date()
        }
      });

      // Log user creation
      await tx.auditLog.create({
        data: {
          tenantId,
          userId: createdBy,
          action: 'USER_CREATED',
          resourceType: 'user',
          resourceId: user.id,
          newValues: {
            email: userData.email,
            role: userData.role,
            teamId: userData.teamId
          }
        }
      });

      // Return user with tenant info
      const userWithTenants = await tx.user.findUnique({
        where: { id: user.id },
        include: {
          tenantUsers: {
            include: {
              tenant: true
            }
          }
        }
      });

      const result = {
        ...userWithTenants!,
        tenants: userWithTenants!.tenantUsers.map(tu => ({
          id: tu.tenant.id,
          name: tu.tenant.name,
          slug: tu.tenant.slug,
          role: tu.role,
          isTenantAdmin: tu.isTenantAdmin
        }))
      };

      // Send welcome notification if requested
      if (userData.sendWelcomeEmail) {
        try {
          const { UserNotificationService } = await import('./user-notification-service');
          const notificationService = new UserNotificationService();

          const tenant = userWithTenants!.tenantUsers[0].tenant;

          await notificationService.sendWelcomeNotification({
            user: {
              id: user.id,
              email: user.email,
              firstName: user.firstName || '',
              lastName: user.lastName || ''
            },
            tenant: {
              id: tenant.id,
              name: tenant.name,
              slug: tenant.slug
            },
            temporaryPassword: userData.password,
            createdBy: createdBy
          });
        } catch (error) {
          console.error('Failed to send welcome notification:', error);
          // Don't fail the user creation if notification fails
        }
      }

      // Trigger user creation notification
      await this.triggerUserCreationNotification(result, tenantId, createdBy);

      return result;
    });
  }

  // Get single user with tenant details
  async getUserById(userId: string, tenantId: string): Promise<UserWithTenantInfo | null> {
    const tenantUser = await prisma.tenantUser.findUnique({
      where: {
        tenantId_userId: {
          tenantId,
          userId
        }
      },
      include: {
        user: {
          select: {
            id: true,
            email: true,
            firstName: true,
            lastName: true,
            avatarUrl: true,
            status: true,
            lastLogin: true
          }
        },
        team: {
          select: {
            id: true,
            name: true,
            color: true
          }
        }
      }
    });

    if (!tenantUser) return null;

    return {
      id: tenantUser.user.id,
      email: tenantUser.user.email,
      firstName: tenantUser.user.firstName,
      lastName: tenantUser.user.lastName,
      avatarUrl: tenantUser.user.avatarUrl,
      status: tenantUser.user.status,
      lastLogin: tenantUser.user.lastLogin,
      tenantUser: {
        id: tenantUser.id,
        role: tenantUser.role,
        status: tenantUser.status,
        joinedAt: tenantUser.joinedAt,
        teamId: tenantUser.teamId,
        department: tenantUser.department,
        jobTitle: tenantUser.jobTitle,
        team: tenantUser.team
      }
    };
  }

  // Update user tenant information
  async updateUserTenantInfo(
    userId: string,
    tenantId: string,
    updates: {
      role?: string;
      teamId?: string;
      department?: string;
      jobTitle?: string;
      status?: string;
    },
    updatedBy: string
  ): Promise<void> {
    // If role is being updated, use the proper updateUserRole method
    if (updates.role) {
      // Update role properly (both tenantUser.role and userRole table)
      await this.updateUserRole(tenantId, userId, updates.role, updatedBy);

      // Remove role from updates since it's already handled
      const { role, ...otherUpdates } = updates;

      // Update other fields if any remain
      if (Object.keys(otherUpdates).length > 0) {
        await prisma.$transaction(async (tx) => {
          await tx.tenantUser.update({
            where: {
              tenantId_userId: {
                tenantId,
                userId
              }
            },
            data: otherUpdates
          });

          // Log the update
          await tx.auditLog.create({
            data: {
              tenantId,
              userId: updatedBy,
              action: 'USER_TENANT_INFO_UPDATED',
              resourceType: 'user',
              resourceId: userId,
              newValues: otherUpdates
            }
          });
        });
      }
    } else {
      // No role update, just update other fields
      await prisma.$transaction(async (tx) => {
        await tx.tenantUser.update({
          where: {
            tenantId_userId: {
              tenantId,
              userId
            }
          },
          data: updates
        });

        // Log the update
        await tx.auditLog.create({
          data: {
            tenantId,
            userId: updatedBy,
            action: 'USER_TENANT_INFO_UPDATED',
            resourceType: 'user',
            resourceId: userId,
            newValues: updates
          }
        });
      });
    }
  }

  // Update user profile
  async updateUser(userId: string, updateData: UpdateUserData): Promise<UserWithTenants> {
    const user = await prisma.user.update({
      where: { id: userId },
      data: updateData,
      include: {
        tenantUsers: {
          include: {
            tenant: true
          }
        }
      }
    });

    return {
      ...user,
      tenants: user.tenantUsers.map(tu => ({
        id: tu.tenant.id,
        name: tu.tenant.name,
        slug: tu.tenant.slug,
        role: tu.role,
        isTenantAdmin: tu.isTenantAdmin
      }))
    };
  }

  // Get user with tenant information
  async getUserWithTenants(userId: string): Promise<UserWithTenants | null> {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: {
        tenantUsers: {
          where: { status: 'active' },
          include: {
            tenant: {
              include: {
                subscriptions: {
                  where: { status: 'active' },
                  include: { plan: true },
                  take: 1
                }
              }
            }
          }
        }
      }
    });

    if (!user) return null;

    return {
      ...user,
      tenants: user.tenantUsers.map(tu => ({
        id: tu.tenant.id,
        name: tu.tenant.name,
        slug: tu.tenant.slug,
        role: tu.role,
        isTenantAdmin: tu.isTenantAdmin
      }))
    };
  }

  // Search for users not in tenant (for adding existing users)
  async searchUsersNotInTenant(tenantId: string, query: string): Promise<User[]> {
    if (query.length < 3) {
      throw new Error('Search query must be at least 3 characters long');
    }

    const users = await prisma.user.findMany({
      where: {
        AND: [
          {
            OR: [
              { email: { contains: query, mode: 'insensitive' } },
              { firstName: { contains: query, mode: 'insensitive' } },
              { lastName: { contains: query, mode: 'insensitive' } }
            ]
          },
          {
            // Exclude users who are already in this tenant
            tenantUsers: {
              none: {
                tenantId: tenantId
              }
            }
          },
          {
            // Exclude platform admins from search results
            isPlatformAdmin: false
          },
          {
            // Only include active users
            status: 'active'
          }
        ]
      },
      select: {
        id: true,
        email: true,
        firstName: true,
        lastName: true,
        avatarUrl: true,
        status: true,
        lastLogin: true
      },
      take: 20, // Limit results to prevent overwhelming the UI
      orderBy: [
        { firstName: 'asc' },
        { lastName: 'asc' },
        { email: 'asc' }
      ]
    });

    return users;
  }

  // Get users in a tenant with enhanced filtering
  async getTenantUsers(tenantId: string, options: UserListOptions = {}): Promise<UserListResult> {
    const {
      page = 1,
      limit = 20,
      search,
      role,
      status,
      teamId
    } = options;

    const skip = (page - 1) * limit;

    // Build where clause
    const where: any = { tenantId };

    if (search) {
      where.user = {
        AND: [
          {
            OR: [
              { email: { contains: search, mode: 'insensitive' } },
              { firstName: { contains: search, mode: 'insensitive' } },
              { lastName: { contains: search, mode: 'insensitive' } }
            ]
          },
          {
            // Exclude platform admins from tenant user lists
            isPlatformAdmin: false
          }
        ]
      };
    } else {
      // Even without search, exclude platform admins
      where.user = {
        isPlatformAdmin: false
      };
    }

    if (role) {
      where.role = role;
    }

    if (status) {
      where.status = status;
    }

    if (teamId) {
      where.teamId = teamId;
    }

    const [users, total] = await Promise.all([
      prisma.tenantUser.findMany({
        where,
        include: {
          user: {
            select: {
              id: true,
              email: true,
              firstName: true,
              lastName: true,
              avatarUrl: true,
              status: true,
              lastLogin: true
            }
          },
          team: {
            select: {
              id: true,
              name: true,
              color: true
            }
          }
        },
        skip,
        take: limit,
        orderBy: { joinedAt: 'desc' }
      }),
      prisma.tenantUser.count({ where })
    ]);

    return {
      users: users.map(tu => ({
        id: tu.user.id,
        email: tu.user.email,
        firstName: tu.user.firstName,
        lastName: tu.user.lastName,
        avatarUrl: tu.user.avatarUrl,
        status: tu.user.status,
        lastLogin: tu.user.lastLogin,
        tenantUser: {
          id: tu.id,
          role: tu.role,
          status: tu.status,
          joinedAt: tu.joinedAt,
          teamId: tu.teamId,
          department: tu.department,
          jobTitle: tu.jobTitle,
          team: tu.team
        }
      })),
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    };
  }

  // Remove user from tenant
  async removeUserFromTenant(tenantId: string, userId: string, removedBy: string): Promise<void> {
    await prisma.$transaction(async (tx) => {
      // Remove tenant-user relationship
      await tx.tenantUser.delete({
        where: {
          tenantId_userId: {
            tenantId,
            userId
          }
        }
      });

      // Remove user roles in this tenant
      await tx.userRole.deleteMany({
        where: {
          tenantId,
          userId
        }
      });

      // Log the removal
      await tx.auditLog.create({
        data: {
          tenantId,
          userId: removedBy,
          action: 'USER_REMOVED',
          resourceType: 'user',
          resourceId: userId
        }
      });
    });

    // Invalidate user permission cache after removal
    const { permissionService } = await import('./permission-service');
    await permissionService.invalidateUserPermissions(userId, tenantId);
  }

  // Update user role in tenant
  async updateUserRole(
    tenantId: string,
    userId: string,
    newRole: string,
    updatedBy: string
  ): Promise<void> {
    // Get current role for notification
    const currentTenantUser = await prisma.tenantUser.findUnique({
      where: {
        tenantId_userId: {
          tenantId,
          userId
        }
      },
      select: { role: true }
    });

    const oldRole = currentTenantUser?.role || 'Unknown';

    await prisma.$transaction(async (tx) => {
      // Update tenant user role
      await tx.tenantUser.update({
        where: {
          tenantId_userId: {
            tenantId,
            userId
          }
        },
        data: { role: newRole }
      });

      // Remove existing role assignments
      await tx.userRole.deleteMany({
        where: {
          tenantId,
          userId
        }
      });

      // Assign new role
      const role = await tx.role.findFirst({
        where: {
          tenantId,
          name: newRole
        }
      });

      if (role) {
        await tx.userRole.create({
          data: {
            tenantId,
            userId,
            roleId: role.id,
            assignedBy: updatedBy
          }
        });
      }

      // Log the role change
      await tx.auditLog.create({
        data: {
          tenantId,
          userId: updatedBy,
          action: 'USER_ROLE_UPDATED',
          resourceType: 'user',
          resourceId: userId,
          newValues: { role: newRole }
        }
      });
    });

    // CRITICAL: Invalidate user permission cache after role change
    const { permissionService } = await import('./permission-service');
    await permissionService.invalidateUserPermissions(userId, tenantId);

    // Trigger role change notification
    await this.triggerRoleChangeNotification(tenantId, userId, oldRole, newRole, updatedBy);
  }

  // Change user password
  async changePassword(userId: string, currentPassword: string, newPassword: string): Promise<void> {
    const user = await prisma.user.findUnique({
      where: { id: userId }
    });

    if (!user || !user.passwordHash) {
      throw new Error('User not found');
    }

    const isCurrentPasswordValid = await bcrypt.compare(currentPassword, user.passwordHash);
    if (!isCurrentPasswordValid) {
      throw new Error('Current password is incorrect');
    }

    const hashedNewPassword = await bcrypt.hash(newPassword, 12);

    await prisma.user.update({
      where: { id: userId },
      data: { passwordHash: hashedNewPassword }
    });
  }

  /**
   * Bulk invite users to tenant
   */
  async bulkInviteUsers(
    tenantId: string,
    users: Array<{
      email: string;
      firstName: string;
      lastName: string;
      role: string;
      teamId?: string;
      department?: string;
      jobTitle?: string;
    }>,
    invitedBy: string
  ): Promise<{ successful: string[]; failed: Array<{ email: string; error: string }> }> {
    const results = {
      successful: [] as string[],
      failed: [] as Array<{ email: string; error: string }>
    };

    for (const userData of users) {
      try {
        await this.inviteUserToTenant({
          email: userData.email,
          firstName: userData.firstName,
          lastName: userData.lastName,
          role: userData.role,
          tenantId,
          invitedBy
        });

        // If team is specified, add user to team after invitation
        if (userData.teamId) {
          // This would need to be implemented after the user accepts the invitation
          // For now, we'll store it in the tenant user record
        }

        results.successful.push(userData.email);
      } catch (error) {
        results.failed.push({
          email: userData.email,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }

    return results;
  }

  /**
   * Bulk update users
   */
  async bulkUpdateUsers(
    tenantId: string,
    userIds: string[],
    updates: {
      status?: string;
      teamId?: string;
      department?: string;
      jobTitle?: string;
      role?: string;
    },
    updatedBy: string
  ): Promise<{ successful: string[]; failed: Array<{ id: string; error: string }> }> {
    const results = {
      successful: [] as string[],
      failed: [] as Array<{ id: string; error: string }>
    };

    for (const userId of userIds) {
      try {
        // If role is being updated, use the proper updateUserRole method
        if (updates.role) {
          // Update role properly (both tenantUser.role and userRole table)
          await this.updateUserRole(tenantId, userId, updates.role, updatedBy);

          // Remove role from updates since it's already handled
          const { role, ...otherUpdates } = updates;

          // Update other fields if any remain
          if (Object.keys(otherUpdates).length > 0) {
            await prisma.$transaction(async (tx) => {
              await tx.tenantUser.update({
                where: {
                  tenantId_userId: {
                    tenantId,
                    userId
                  }
                },
                data: otherUpdates
              });

              // Log the update
              await tx.auditLog.create({
                data: {
                  tenantId,
                  userId: updatedBy,
                  action: 'USER_BULK_UPDATED',
                  resourceType: 'user',
                  resourceId: userId,
                  newValues: otherUpdates
                }
              });
            });
          }
        } else {
          // No role update, just update other fields
          await prisma.$transaction(async (tx) => {
            await tx.tenantUser.update({
              where: {
                tenantId_userId: {
                  tenantId,
                  userId
                }
              },
              data: updates
            });

            // Log the update
            await tx.auditLog.create({
              data: {
                tenantId,
                userId: updatedBy,
                action: 'USER_BULK_UPDATED',
                resourceType: 'user',
                resourceId: userId,
                newValues: updates
              }
            });
          });
        }

        results.successful.push(userId);
      } catch (error) {
        results.failed.push({
          id: userId,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }

    return results;
  }

  /**
   * Bulk remove users from tenant
   */
  async bulkRemoveUsers(
    tenantId: string,
    userIds: string[],
    removedBy: string,
    reason?: string
  ): Promise<{ successful: string[]; failed: Array<{ id: string; error: string }> }> {
    const results = {
      successful: [] as string[],
      failed: [] as Array<{ id: string; error: string }>
    };

    for (const userId of userIds) {
      try {
        // Don't allow removing self
        if (userId === removedBy) {
          results.failed.push({
            id: userId,
            error: 'Cannot remove yourself'
          });
          continue;
        }

        await this.removeUserFromTenant(tenantId, userId, removedBy);
        results.successful.push(userId);
      } catch (error) {
        results.failed.push({
          id: userId,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }

    return results;
  }

  /**
   * Export users data
   */
  async exportUsers(
    tenantId: string,
    options: {
      format: 'csv' | 'xlsx';
      includeRoles?: boolean;
      includeTeams?: boolean;
    }
  ): Promise<Buffer> {
    const users = await prisma.tenantUser.findMany({
      where: {
        tenantId,
        user: {
          // Exclude platform admins from export
          isPlatformAdmin: false
        }
      },
      include: {
        user: true,
        team: options.includeTeams,
        userRoles: options.includeRoles ? {
          include: { role: true }
        } : false
      }
    });

    // For now, return CSV format as a simple implementation
    // In a real implementation, you'd use libraries like csv-writer or xlsx
    const csvData = users.map(tu => ({
      email: tu.user.email,
      firstName: tu.user.firstName,
      lastName: tu.user.lastName,
      role: tu.role,
      status: tu.status,
      joinedAt: tu.joinedAt.toISOString(),
      team: options.includeTeams && tu.team ? tu.team.name : '',
      roles: options.includeRoles && tu.userRoles ?
        tu.userRoles.map(ur => ur.role.name).join(';') : ''
    }));

    const csvHeader = 'Email,First Name,Last Name,Role,Status,Joined At' +
      (options.includeTeams ? ',Team' : '') +
      (options.includeRoles ? ',Roles' : '') + '\n';

    const csvContent = csvData.map(row =>
      `${row.email},${row.firstName},${row.lastName},${row.role},${row.status},${row.joinedAt}` +
      (options.includeTeams ? `,${row.team}` : '') +
      (options.includeRoles ? `,${row.roles}` : '')
    ).join('\n');

    // Add UTF-8 BOM for proper Arabic character support
    const BOM = '\uFEFF';
    const csvWithBOM = BOM + csvHeader + csvContent;

    return Buffer.from(csvWithBOM, 'utf-8');
  }

  // Send invitation email (placeholder)
  private async sendInvitationEmail(email: string, tenantId: string): Promise<void> {
    // TODO: Implement email sending logic
    console.log(`Sending invitation email to ${email} for tenant ${tenantId}`);
  }

  /**
   * Trigger user creation notification
   */
  private async triggerUserCreationNotification(
    user: UserWithTenants,
    tenantId: string,
    createdBy: string
  ): Promise<void> {
    try {
      const targetUsers = [];

      // Notify administrators and managers
      const admins = await prisma.tenantUser.findMany({
        where: {
          tenantId,
          OR: [
            { role: { in: ['Admin', 'Manager', 'Team Lead'] } },
            { isTenantAdmin: true }
          ],
          status: 'active'
        },
        select: { userId: true }
      });

      targetUsers.push(...admins.map(admin => admin.userId));

      // Notify the creator if different from user
      if (createdBy !== user.id) {
        targetUsers.push(createdBy);
      }

      // Get tenant information
      const tenant = user.tenants[0];
      const tenantUser = user.tenantUsers.find(tu => tu.tenantId === tenantId);

      for (const userId of [...new Set(targetUsers)]) {
        await this.notificationService.createNotification({
          tenantId,
          userId,
          type: 'user_created',
          category: 'system',
          title: 'New User Created',
          message: `New user ${user.firstName} ${user.lastName} (${user.email}) has been added to the organization`,
          data: {
            newUserId: user.id,
            newUserEmail: user.email,
            newUserName: `${user.firstName} ${user.lastName}`,
            newUserRole: tenantUser?.role,
            newUserTeamId: tenantUser?.teamId,
            newUserDepartment: tenantUser?.department,
            newUserJobTitle: tenantUser?.jobTitle,
            createdBy,
            tenantName: tenant?.name
          },
          actionUrl: `/admin/users/${user.id}`,
          actionLabel: 'View User'
        });
      }

      // Notify the new user
      await this.notificationService.createNotification({
        tenantId,
        userId: user.id,
        type: 'user_welcome',
        category: 'system',
        title: `Welcome to ${tenant?.name}!`,
        message: `Your account has been created successfully. Welcome to the team!`,
        data: {
          tenantName: tenant?.name,
          userRole: tenantUser?.role,
          userTeamId: tenantUser?.teamId,
          createdBy
        },
        actionUrl: '/dashboard',
        actionLabel: 'Go to Dashboard'
      });

    } catch (error) {
      console.error('Failed to send user creation notification:', error);
      // Don't throw error to avoid breaking the main operation
    }
  }

  /**
   * Trigger role change notification
   */
  async triggerRoleChangeNotification(
    tenantId: string,
    userId: string,
    oldRole: string,
    newRole: string,
    changedBy: string
  ): Promise<void> {
    try {
      // Get user information
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: {
          id: true,
          firstName: true,
          lastName: true,
          email: true
        }
      });

      if (!user) return;

      const targetUsers = [];

      // Notify the user whose role changed
      targetUsers.push(userId);

      // Notify administrators and managers
      const admins = await prisma.tenantUser.findMany({
        where: {
          tenantId,
          OR: [
            { role: { in: ['Admin', 'Manager', 'Team Lead'] } },
            { isTenantAdmin: true }
          ],
          status: 'active',
          userId: { not: userId } // Don't duplicate notification for the user
        },
        select: { userId: true }
      });

      targetUsers.push(...admins.map(admin => admin.userId));

      // Notify the person who made the change if different
      if (changedBy !== userId && !targetUsers.includes(changedBy)) {
        targetUsers.push(changedBy);
      }

      for (const targetUserId of [...new Set(targetUsers)]) {
        const isTargetUser = targetUserId === userId;

        await this.notificationService.createNotification({
          tenantId,
          userId: targetUserId,
          type: 'role_changed',
          category: 'system',
          title: isTargetUser ? 'Your Role Has Been Updated' : 'User Role Updated',
          message: isTargetUser
            ? `Your role has been changed from ${oldRole} to ${newRole}`
            : `${user.firstName} ${user.lastName}'s role has been changed from ${oldRole} to ${newRole}`,
          data: {
            affectedUserId: userId,
            affectedUserName: `${user.firstName} ${user.lastName}`,
            affectedUserEmail: user.email,
            oldRole,
            newRole,
            changedBy,
            changedAt: new Date().toISOString()
          },
          actionUrl: isTargetUser ? '/profile' : `/admin/users/${userId}`,
          actionLabel: isTargetUser ? 'View Profile' : 'View User'
        });
      }

    } catch (error) {
      console.error('Failed to send role change notification:', error);
      // Don't throw error to avoid breaking the main operation
    }
  }
}
