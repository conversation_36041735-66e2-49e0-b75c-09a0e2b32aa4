import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';

// Request validation schemas
const getAvailableDocumentsQuerySchema = z.object({
  search: z.string().optional(),
  mimeType: z.string().optional(),
  limit: z.coerce.number().int().min(1).max(100).default(50),
});

interface RouteParams {
  params: Promise<{
    id: string;
  }>;
}

/**
 * GET /api/handovers/[id]/available-documents
 * Get all documents available for linking to a handover (from opportunity and proposals)
 */
export async function GET(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get tenantId from user's tenants array (users have one tenant in this system)
    let tenantId: string | null = null;
    if (session.user.tenants && session.user.tenants.length > 0) {
      tenantId = session.user.tenants[0].id;
    }

    if (!tenantId) {
      return NextResponse.json({ error: 'Tenant not found' }, { status: 400 });
    }

    // Parse and validate query parameters
    const url = new URL(request.url);
    const queryParams = Object.fromEntries(url.searchParams.entries());
    const validatedQuery = getAvailableDocumentsQuerySchema.parse(queryParams);

    const resolvedParams = await params;
    const handoverId = resolvedParams.id;

    // Get handover details to find the associated opportunity
    const handover = await prisma.projectHandover.findFirst({
      where: {
        id: handoverId,
        tenantId
      },
      include: {
        opportunity: {
          include: {
            company: {
              select: {
                id: true,
                name: true
              }
            }
          }
        }
      }
    });

    if (!handover) {
      return NextResponse.json(
        { error: 'Handover not found or access denied' },
        { status: 404 }
      );
    }

    const opportunityId = handover.opportunityId;

    // Get all documents already linked to this handover
    const linkedDocuments = await prisma.handoverDocument.findMany({
      where: {
        handoverId,
        tenantId
      },
      select: {
        documentId: true
      }
    });

    const linkedDocumentIds = linkedDocuments.map(hd => hd.documentId);

    // Build where clause for document search
    const documentWhere: any = {
      tenantId,
      id: {
        notIn: linkedDocumentIds // Exclude already linked documents
      },
      OR: [
        // Documents directly related to the opportunity
        {
          relatedToType: 'opportunity',
          relatedToId: opportunityId
        }
      ]
    };

    // Get proposals for this opportunity and include their documents
    const proposals = await prisma.proposal.findMany({
      where: {
        opportunityId,
        tenantId
      },
      select: {
        id: true,
        title: true
      }
    });

    // Add proposal documents to the search
    if (proposals.length > 0) {
      const proposalIds = proposals.map(p => p.id);
      documentWhere.OR.push({
        relatedToType: 'proposal',
        relatedToId: {
          in: proposalIds
        }
      });
    }

    // Add search filter if provided
    if (validatedQuery.search) {
      documentWhere.AND = [
        {
          OR: [
            { name: { contains: validatedQuery.search, mode: 'insensitive' } },
            { description: { contains: validatedQuery.search, mode: 'insensitive' } }
          ]
        }
      ];
    }

    // Add mime type filter if provided
    if (validatedQuery.mimeType && validatedQuery.mimeType !== 'all') {
      if (validatedQuery.mimeType.endsWith('/')) {
        // Handle partial mime types like "image/"
        documentWhere.mimeType = {
          startsWith: validatedQuery.mimeType
        };
      } else {
        documentWhere.mimeType = validatedQuery.mimeType;
      }
    }

    // Get available documents
    const documents = await prisma.document.findMany({
      where: documentWhere,
      include: {
        uploadedBy: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true
          }
        }
      },
      orderBy: [
        { createdAt: 'desc' }
      ],
      take: validatedQuery.limit
    });

    // Categorize documents by source
    const categorizedDocuments = {
      opportunity: documents.filter(doc => doc.relatedToType === 'opportunity'),
      proposals: documents.filter(doc => doc.relatedToType === 'proposal')
    };

    // Get proposal titles and IDs for proposal documents
    const proposalDocuments = categorizedDocuments.proposals.map(doc => {
      const proposal = proposals.find(p => p.id === doc.relatedToId);
      return {
        ...doc,
        proposalTitle: proposal?.title || 'Unknown Proposal',
        proposalId: proposal?.id || doc.relatedToId
      };
    });

    const response = {
      handover: {
        id: handover.id,
        title: handover.title,
        opportunityId: handover.opportunityId,
        opportunityTitle: handover.opportunity.title,
        companyName: handover.opportunity.company?.name
      },
      documents: {
        opportunity: categorizedDocuments.opportunity,
        proposals: proposalDocuments,
        total: documents.length
      },
      metadata: {
        totalAvailable: documents.length,
        alreadyLinked: linkedDocumentIds.length,
        proposalCount: proposals.length,
        searchTerm: validatedQuery.search,
        mimeTypeFilter: validatedQuery.mimeType
      }
    };

    return NextResponse.json(response);

  } catch (error) {
    console.error('Error fetching available documents for handover:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid query parameters', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to fetch available documents' },
      { status: 500 }
    );
  }
}
