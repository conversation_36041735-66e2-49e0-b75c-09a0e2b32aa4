# Process Automation System Guide

## Overview

The Process Automation System provides comprehensive workflow automation for the CRM, focusing on opportunity status triggers and automated handover creation. This system monitors opportunity status changes and automatically executes configured actions such as creating handovers, sending notifications, and creating project structures.

## Key Features

### 🔄 Opportunity Status Triggers
- **Automatic Monitoring**: Continuously monitors opportunity status changes
- **Configurable Conditions**: Set specific trigger conditions (from/to status combinations)
- **Priority-Based Execution**: Execute triggers based on priority levels (1-10)
- **Error Handling**: Comprehensive error handling and logging

### 📋 Automated Handover Creation
- **Closed Won Triggers**: Automatically create handovers when opportunities close as won
- **Template Integration**: Use default or custom handover templates
- **Team Assignment**: Automatically assign delivery managers and team members
- **Scheduling**: Schedule handovers with configurable delays

### 🔔 Notification Workflows
- **Multi-Channel Notifications**: Email, in-app, and system notifications
- **Role-Based Recipients**: Notify sales reps, delivery managers, and team members
- **Customizable Templates**: Use predefined or custom notification templates
- **Escalation Support**: Automatic escalation for overdue items

### 📊 Quality Assurance & Metrics
- **Execution Tracking**: Monitor all automation executions with success/failure rates
- **Performance Metrics**: Track handover creation times and process bottlenecks
- **Analytics Dashboard**: Real-time metrics and performance monitoring
- **Audit Logging**: Comprehensive audit trail for all automation activities

## Architecture

### Core Services

#### ProcessAutomationService
- **Purpose**: Main automation engine
- **Responsibilities**:
  - Create and manage automation triggers
  - Execute trigger actions
  - Collect and provide metrics
  - Handle error logging

#### OpportunityTriggersService
- **Purpose**: Specialized opportunity monitoring
- **Responsibilities**:
  - Monitor opportunity status changes
  - Handle specific status transitions
  - Create handovers for closed won opportunities
  - Send notifications and updates

### Database Schema

#### AutomationTrigger
```sql
- id: string (Primary Key)
- tenantId: string (Foreign Key)
- name: string
- description: string (optional)
- triggerType: enum (opportunity_status_change, handover_completion, etc.)
- triggerConditions: JSON (conditions that must be met)
- actions: JSON (array of actions to execute)
- isActive: boolean
- priority: integer (1-10)
- createdById: string (Foreign Key)
- createdAt: datetime
- updatedAt: datetime
```

#### AutomationExecution
```sql
- id: string (Primary Key)
- tenantId: string (Foreign Key)
- triggerId: string (Foreign Key)
- entityType: enum (opportunity, handover, project, etc.)
- entityId: string
- triggerData: JSON (data that triggered the automation)
- executedActions: JSON (array of executed actions with results)
- status: enum (pending, completed, failed)
- error: string (optional)
- startedAt: datetime
- completedAt: datetime (optional)
```

## API Endpoints

### Trigger Management
- `GET /api/automation/triggers` - List all triggers with filtering
- `POST /api/automation/triggers` - Create new trigger
- `GET /api/automation/triggers/[id]` - Get specific trigger
- `PUT /api/automation/triggers/[id]` - Update trigger
- `DELETE /api/automation/triggers/[id]` - Delete/deactivate trigger

### Testing & Monitoring
- `POST /api/automation/triggers/[id]/test` - Test trigger execution
- `GET /api/automation/triggers/metrics` - Get automation metrics
- `GET /api/automation/triggers/[id]/executions` - Get execution history

## Configuration

### Default Triggers

The system comes with pre-configured triggers for common workflows:

#### 1. Opportunity Closed Won → Create Handover
```json
{
  "name": "Opportunity Closed Won - Create Handover",
  "triggerType": "opportunity_status_change",
  "triggerConditions": { "toStatus": "Closed Won" },
  "actions": [
    {
      "type": "create_handover",
      "config": {
        "priority": "high",
        "scheduledDelayHours": 24
      }
    },
    {
      "type": "send_notification",
      "config": {
        "recipients": ["sales_rep", "delivery_manager"],
        "template": "handover_created"
      }
    }
  ],
  "priority": 9
}
```

#### 2. Proposal Stage → Notify Team
```json
{
  "name": "Opportunity Moved to Proposal - Notify Team",
  "triggerType": "opportunity_status_change",
  "triggerConditions": { "toStatus": "Proposal" },
  "actions": [
    {
      "type": "send_notification",
      "config": {
        "recipients": ["sales_rep", "sales_manager"],
        "template": "proposal_stage_reached"
      }
    }
  ],
  "priority": 5
}
```

### Custom Trigger Configuration

Create custom triggers through the API or admin interface:

```typescript
const triggerData = {
  name: "Custom Trigger Name",
  description: "Description of what this trigger does",
  triggerType: "opportunity_status_change",
  triggerConditions: {
    fromStatus: "Negotiation",  // Optional: specific from status
    toStatus: "Closed Won",     // Required: target status
    minValue: 10000            // Optional: minimum opportunity value
  },
  actions: [
    {
      type: "create_handover",
      config: {
        priority: "urgent",
        templateId: "custom-template-id",
        assignToTeam: ["team-id-1", "team-id-2"]
      }
    }
  ],
  isActive: true,
  priority: 8
};
```

## Usage Examples

### Setting Up Automation for New Tenant

```typescript
import { setupDefaultAutomationTriggers } from '@/scripts/setup-default-automation';

// Setup default triggers for a new tenant
await setupDefaultAutomationTriggers(tenantId, userId);
```

### Manual Trigger Execution

```typescript
import { opportunityTriggersService } from '@/services/opportunity-triggers';

// Manually trigger automation for opportunity status change
await opportunityTriggersService.handleOpportunityStatusChange({
  opportunityId: 'opp-123',
  tenantId: 'tenant-456',
  oldStatus: 'Negotiation',
  newStatus: 'Closed Won',
  changedBy: 'user-789',
  changedAt: new Date()
});
```

### Getting Automation Metrics

```typescript
import { processAutomationService } from '@/services/process-automation';

// Get metrics for the last 30 days
const metrics = await processAutomationService.getProcessMetrics(
  tenantId,
  {
    from: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
    to: new Date()
  }
);

console.log(`Total executions: ${metrics.totalExecutions}`);
console.log(`Success rate: ${(metrics.successfulExecutions / metrics.totalExecutions * 100).toFixed(1)}%`);
```

## Monitoring & Troubleshooting

### Execution Monitoring
- All automation executions are logged in the `AutomationExecution` table
- Failed executions include error messages for debugging
- Execution history is available through the API and admin interface

### Common Issues

#### 1. Trigger Not Executing
- Check if trigger is active (`isActive: true`)
- Verify trigger conditions match the actual status change
- Check tenant isolation (trigger belongs to correct tenant)

#### 2. Handover Creation Failing
- Ensure default handover template exists for tenant
- Verify delivery manager and team members exist
- Check opportunity data completeness

#### 3. Notification Failures
- Verify notification service configuration
- Check recipient user permissions and status
- Ensure notification templates exist

### Debugging

Enable detailed logging by setting environment variables:
```bash
DEBUG_AUTOMATION=true
LOG_LEVEL=debug
```

## Performance Considerations

### Optimization Tips
1. **Trigger Priority**: Use appropriate priority levels to control execution order
2. **Batch Processing**: Group similar actions to reduce database calls
3. **Async Execution**: All triggers execute asynchronously to avoid blocking
4. **Error Isolation**: Failed actions don't prevent other actions from executing

### Monitoring Metrics
- Track execution times and success rates
- Monitor database performance for automation queries
- Set up alerts for high failure rates or long execution times

## Security & Permissions

### Access Control
- Automation management requires `SETTINGS` permissions
- Trigger creation/modification requires `CREATE`/`UPDATE` permissions
- Execution logs are tenant-isolated

### Data Privacy
- All automation data is tenant-scoped
- Sensitive data in trigger conditions is encrypted
- Audit logs maintain data privacy compliance

## Future Enhancements

### Planned Features
- **Time-Based Triggers**: Schedule actions based on time intervals
- **Complex Conditions**: Support for AND/OR logic in trigger conditions
- **Webhook Integration**: External system integration via webhooks
- **Machine Learning**: Predictive automation based on historical data
- **Visual Workflow Builder**: Drag-and-drop trigger configuration interface

### Integration Roadmap
- **Project Management**: Automatic project creation and milestone setup
- **Calendar Integration**: Schedule meetings and follow-ups
- **External CRM**: Sync with external CRM systems
- **Reporting**: Advanced analytics and custom reports
