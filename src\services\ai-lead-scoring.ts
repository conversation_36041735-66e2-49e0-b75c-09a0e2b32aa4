import { aiService, AIRequest } from './ai-service';
import { prisma } from '@/lib/prisma';
import { UnifiedNotificationService } from './unified-notification-service';

// Database lead structure (what we receive from the API)
export interface DatabaseLeadData {
  id: string;
  tenantId: string;
  title: string;
  description?: string;
  status: string;
  source?: string;
  score: number;
  value?: string | number;
  expectedCloseDate?: string | Date;
  priority: string;
  tags: any[];
  customFields: Record<string, any>;
  ownerId?: string;
  createdById: string;
  contact?: {
    id: string;
    firstName: string;
    lastName: string;
    email?: string;
    phone?: string;
    companyId?: string;
  };
  company?: {
    id: string;
    name: string;
    website?: string;
    industry?: string;
    size?: string;
  };
  owner?: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
  };
  createdBy?: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
  };
  // Enhanced data for comprehensive analysis
  activities?: {
    id: string;
    type: string;
    subject: string | null;
    description: string | null;
    scheduledAt: Date | null;
    completedAt: Date | null;
    createdAt: Date;
    owner?: {
      id: string;
      firstName: string | null;
      lastName: string | null;
      email: string;
    } | null;
  }[];
  scheduledCalls?: {
    id: string;
    title: string;
    description: string | null;
    callType: string;
    scheduledAt: Date;
    duration: number;
    status: string;
    completedAt: Date | null;
    callNotes: string | null;
    followUpRequired: boolean;
    nextCallDate: Date | null;
    assignedToUser: {
      id: string;
      firstName: string | null;
      lastName: string | null;
      email: string;
    };
  }[];
  emailLogs?: {
    id: string;
    subject: string;
    status: string;
    sentAt: Date;
    deliveredAt: Date | null;
    openedAt: Date | null;
    clickedAt: Date | null;
    sentByUser: {
      id: string;
      firstName: string | null;
      lastName: string | null;
      email: string;
    };
  }[];
}

// Processed lead data for AI analysis
export interface LeadData {
  id: string;
  tenantId: string;
  title: string;
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  company?: string;
  jobTitle?: string;
  industry?: string;
  source: string;
  notes?: string;
  interactions: LeadInteraction[];
  customFields?: Record<string, any>;
  value?: number;
  status: string;
  priority: string;
  expectedCloseDate?: Date;
}

export interface LeadInteraction {
  type: 'email' | 'call' | 'meeting' | 'form_submission' | 'website_visit';
  content: string;
  timestamp: Date;
  sentiment?: 'positive' | 'neutral' | 'negative';
  engagement_level?: number; // 1-10
}

export interface LeadScore {
  overall: number; // 0-100
  demographic: number; // 0-100
  behavioral: number; // 0-100
  engagement: number; // 0-100
  intent: number; // 0-100
  fit: number; // 0-100
  reasoning: string;
  recommendations: string[];
  nextActions: string[];
  confidence: number; // 0-1
}

export interface LeadQualification {
  budget: 'qualified' | 'unqualified' | 'unknown';
  authority: 'qualified' | 'unqualified' | 'unknown';
  need: 'qualified' | 'unqualified' | 'unknown';
  timeline: 'qualified' | 'unqualified' | 'unknown';
  overall: 'qualified' | 'unqualified' | 'unknown';
  reasoning: string;
  questions: string[];
}

export class AILeadScoringService {
  private notificationService = new UnifiedNotificationService();

  private readonly LEAD_SCORING_PROMPT = `
You are an expert sales lead scoring AI. Analyze the provided lead data and interactions to generate a comprehensive lead score.

Consider these factors:
1. DEMOGRAPHIC SCORING (0-100):
   - Company size and industry relevance
   - Job title and decision-making authority
   - Geographic location and market fit

2. BEHAVIORAL SCORING (0-100):
   - Website engagement patterns
   - Content consumption behavior
   - Response patterns to outreach

3. ENGAGEMENT SCORING (0-100):
   - Frequency and quality of interactions
   - Response time and enthusiasm
   - Meeting attendance and participation

4. INTENT SCORING (0-100):
   - Explicit buying signals
   - Research behavior and questions asked
   - Timeline indicators

5. FIT SCORING (0-100):
   - Product-market fit indicators
   - Budget alignment signals
   - Technical requirements match

Provide scores for each category and an overall score (weighted average).
Include reasoning for the scores and specific recommendations for next actions.
`;

  private readonly LEAD_QUALIFICATION_PROMPT = `
You are an expert sales qualification AI. Analyze the provided lead data using the BANT (Budget, Authority, Need, Timeline) framework.

Evaluate each BANT criterion:
1. BUDGET: Does the lead have budget allocated or budget authority?
2. AUTHORITY: Is the lead a decision-maker or influencer in the buying process?
3. NEED: Does the lead have a clear business need for our solution?
4. TIMELINE: Is there a defined timeline for making a purchase decision?

For each criterion, classify as:
- "qualified": Strong evidence of qualification
- "unqualified": Clear evidence of disqualification
- "unknown": Insufficient information to determine

Provide an overall qualification status and specific questions to ask to gather missing information.
`;

  // Fallback scoring when AI is not available
  private getFallbackScore(leadData: LeadData, includeQualification?: boolean): { score: LeadScore; qualification?: LeadQualification } {
    // Simple rule-based scoring as fallback
    let score = 50; // Base score

    // Score based on available data
    if (leadData.email) score += 10;
    if (leadData.phone) score += 5;
    if (leadData.company) score += 15;
    if (leadData.value && leadData.value > 0) score += 20;

    // Score based on status
    const statusScores: Record<string, number> = {
      'new': 0,
      'contacted': 10,
      'qualified': 20,
      'proposal': 30,
      'negotiation': 40
    };
    score += statusScores[leadData.status] || 0;

    // Score based on priority
    const priorityScores: Record<string, number> = {
      'low': 0,
      'medium': 5,
      'high': 10,
      'urgent': 15
    };
    score += priorityScores[leadData.priority] || 0;

    score = Math.min(100, Math.max(0, score));

    const leadScore: LeadScore = {
      overall: score,
      demographic: score * 0.8,
      behavioral: score * 0.6,
      engagement: score * 0.7,
      intent: score * 0.9,
      fit: score * 0.8,
      reasoning: 'Fallback scoring used - AI service unavailable',
      recommendations: ['Configure AI service for better scoring', 'Review lead data manually'],
      nextActions: ['Contact lead for more information', 'Qualify lead manually'],
      confidence: 0.3
    };

    let qualification: LeadQualification | undefined;
    if (includeQualification) {
      qualification = {
        budget: 'unknown',
        authority: 'unknown',
        need: 'unknown',
        timeline: 'unknown',
        overall: 'unknown',
        reasoning: 'AI qualification unavailable - manual qualification required',
        questions: [
          'What is your budget for this project?',
          'Who makes the final decision?',
          'What is your timeline for implementation?',
          'What specific needs are you looking to address?'
        ]
      };
    }

    return { score: leadScore, qualification };
  }

  // Transform database lead data to AI-compatible format
  private transformLeadData(dbLead: DatabaseLeadData): LeadData {
    return {
      id: dbLead.id,
      tenantId: dbLead.tenantId,
      title: dbLead.title,
      firstName: dbLead.contact?.firstName || 'Unknown',
      lastName: dbLead.contact?.lastName || 'Contact',
      email: dbLead.contact?.email || '',
      phone: dbLead.contact?.phone,
      company: dbLead.company?.name,
      jobTitle: '', // Not available in current schema
      industry: dbLead.company?.industry,
      source: dbLead.source || 'unknown',
      notes: dbLead.description,
      interactions: [], // Will be populated from activities if available
      customFields: dbLead.customFields || {},
      value: typeof dbLead.value === 'string' ? parseFloat(dbLead.value) : dbLead.value,
      status: dbLead.status,
      priority: dbLead.priority,
      expectedCloseDate: dbLead.expectedCloseDate ? new Date(dbLead.expectedCloseDate) : undefined
    };
  }

  async scoreLeadWithAI(
    leadData: DatabaseLeadData | LeadData,
    userId: string,
    options?: {
      includeQualification?: boolean;
      customPrompt?: string;
    }
  ): Promise<{ score: LeadScore; qualification?: LeadQualification }> {
    try {
      // Transform database lead data if needed
      const processedLeadData = 'contact' in leadData ? this.transformLeadData(leadData as DatabaseLeadData) : leadData as LeadData;

      // Check if AI service is available
      if (!aiService.getAvailableProviders().length) {
        console.warn('No AI providers available, using fallback scoring');
        return this.getFallbackScore(processedLeadData, options?.includeQualification);
      }

      // Prepare lead data for AI analysis
      const leadContext = this.prepareLeadContext(processedLeadData);
      
      // Generate lead score
      const scoreRequest: AIRequest = {
        prompt: this.buildScoringPrompt(processedLeadData, leadContext),
        systemPrompt: options?.customPrompt || this.LEAD_SCORING_PROMPT,
        context: { leadData: leadContext },
        tenantId: processedLeadData.tenantId,
        userId,
        feature: 'lead_scoring'
      };

      const scoreResponse = await aiService.generateResponse(scoreRequest, {
        temperature: 0.3, // Lower temperature for more consistent scoring
        maxTokens: 1500
      });

      const leadScore = this.parseLeadScore(scoreResponse.content);

      let qualification: LeadQualification | undefined;

      // Generate qualification if requested
      if (options?.includeQualification) {
        const qualificationRequest: AIRequest = {
          prompt: this.buildQualificationPrompt(processedLeadData, leadContext),
          systemPrompt: this.LEAD_QUALIFICATION_PROMPT,
          context: { leadData: leadContext },
          tenantId: processedLeadData.tenantId,
          userId,
          feature: 'lead_qualification'
        };

        const qualificationResponse = await aiService.generateResponse(qualificationRequest, {
          temperature: 0.2,
          maxTokens: 1000
        });

        qualification = this.parseLeadQualification(qualificationResponse.content);
      }

      // Store the AI insights
      await this.storeAIInsights(processedLeadData.id, processedLeadData.tenantId, {
        type: 'lead_scoring',
        score: leadScore,
        qualification,
        aiResponse: scoreResponse,
        timestamp: new Date()
      });

      // Trigger AI-powered notifications based on score
      await this.triggerAILeadScoringNotifications(
        processedLeadData,
        leadScore,
        qualification,
        userId
      );

      return { score: leadScore, qualification };

    } catch (error) {
      console.error('AI lead scoring failed:', error);

      // Provide more specific error information
      if (error instanceof Error) {
        if (error.message.includes('not initialized')) {
          throw new Error('AI service not properly configured. Please check API keys.');
        }
        if (error.message.includes('rate limit')) {
          throw new Error('AI service rate limit exceeded. Please try again later.');
        }
        if (error.message.includes('quota')) {
          throw new Error('AI service quota exceeded. Please check your billing.');
        }
        throw new Error(`AI service error: ${error.message}`);
      }

      throw new Error('Failed to generate AI lead score');
    }
  }

  private prepareLeadContext(leadData: LeadData | DatabaseLeadData): Record<string, any> {
    // Handle both LeadData and DatabaseLeadData formats
    const isDatabase = 'contact' in leadData;

    const basicInfo = isDatabase ? {
      name: (leadData as DatabaseLeadData).contact ? `${(leadData as DatabaseLeadData).contact!.firstName} ${(leadData as DatabaseLeadData).contact!.lastName}` : 'Unknown',
      email: (leadData as DatabaseLeadData).contact?.email || 'Unknown',
      phone: (leadData as DatabaseLeadData).contact?.phone || 'Unknown',
      company: (leadData as DatabaseLeadData).company?.name || 'Unknown',
      job_title: 'Unknown', // Not available in database format
      industry: (leadData as DatabaseLeadData).company?.industry || 'Unknown',
      source: (leadData as DatabaseLeadData).source || 'Unknown',
      title: (leadData as DatabaseLeadData).title,
      status: (leadData as DatabaseLeadData).status,
      priority: (leadData as DatabaseLeadData).priority,
      value: (leadData as DatabaseLeadData).value,
      expectedCloseDate: (leadData as DatabaseLeadData).expectedCloseDate
    } : {
      name: `${(leadData as LeadData).firstName} ${(leadData as LeadData).lastName}`,
      email: (leadData as LeadData).email,
      phone: (leadData as LeadData).phone,
      company: (leadData as LeadData).company,
      job_title: (leadData as LeadData).jobTitle,
      industry: (leadData as LeadData).industry,
      source: (leadData as LeadData).source,
      title: (leadData as LeadData).title || 'Unknown',
      status: (leadData as LeadData).status,
      priority: (leadData as LeadData).priority,
      value: (leadData as LeadData).value,
      expectedCloseDate: (leadData as LeadData).expectedCloseDate
    };

    // Process interactions (for LeadData format)
    const interactions = isDatabase ? [] : (leadData as LeadData).interactions.map(interaction => ({
      type: interaction.type,
      content: interaction.content.substring(0, 500),
      timestamp: interaction.timestamp.toISOString(),
      sentiment: interaction.sentiment,
      engagement_level: interaction.engagement_level
    }));

    // Process activities (for DatabaseLeadData format)
    const activities = isDatabase && (leadData as DatabaseLeadData).activities ? (leadData as DatabaseLeadData).activities!.map(activity => ({
      id: activity.id,
      type: activity.type,
      subject: activity.subject,
      description: activity.description?.substring(0, 500),
      scheduledAt: activity.scheduledAt?.toISOString(),
      completedAt: activity.completedAt?.toISOString(),
      createdAt: activity.createdAt.toISOString(),
      owner: activity.owner ? `${activity.owner.firstName} ${activity.owner.lastName}` : 'Unknown'
    })) : [];

    // Process scheduled calls
    const scheduledCalls = isDatabase && (leadData as DatabaseLeadData).scheduledCalls ? (leadData as DatabaseLeadData).scheduledCalls!.map(call => ({
      id: call.id,
      title: call.title,
      description: call.description?.substring(0, 300),
      callType: call.callType,
      scheduledAt: call.scheduledAt.toISOString(),
      duration: call.duration,
      status: call.status,
      completedAt: call.completedAt?.toISOString(),
      callNotes: call.callNotes?.substring(0, 500),
      followUpRequired: call.followUpRequired,
      nextCallDate: call.nextCallDate?.toISOString(),
      assignedTo: `${call.assignedToUser.firstName} ${call.assignedToUser.lastName}`
    })) : [];

    // Process email logs
    const emailLogs = isDatabase && (leadData as DatabaseLeadData).emailLogs ? (leadData as DatabaseLeadData).emailLogs!.map(email => ({
      id: email.id,
      subject: email.subject,
      status: email.status,
      sentAt: email.sentAt.toISOString(),
      deliveredAt: email.deliveredAt?.toISOString(),
      openedAt: email.openedAt?.toISOString(),
      clickedAt: email.clickedAt?.toISOString(),
      sentBy: `${email.sentByUser.firstName} ${email.sentByUser.lastName}`
    })) : [];

    return {
      basic_info: basicInfo,
      interactions,
      activities,
      scheduled_calls: scheduledCalls,
      email_logs: emailLogs,
      interaction_summary: {
        total_interactions: interactions.length,
        recent_interactions: interactions
          .filter(i => new Date(i.timestamp) > new Date(Date.now() - 30 * 24 * 60 * 60 * 1000))
          .length,
        avg_engagement: interactions.reduce((sum, i) => sum + (i.engagement_level || 0), 0) / interactions.length || 0,
        sentiment_distribution: this.calculateSentimentDistribution(interactions as any)
      },
      activity_summary: {
        total_activities: activities.length,
        completed_activities: activities.filter(a => a.completedAt).length,
        recent_activities: activities
          .filter(a => new Date(a.createdAt) > new Date(Date.now() - 30 * 24 * 60 * 60 * 1000))
          .length
      },
      call_summary: {
        total_calls: scheduledCalls.length,
        completed_calls: scheduledCalls.filter(c => c.status === 'completed').length,
        upcoming_calls: scheduledCalls.filter(c => c.status === 'scheduled' && new Date(c.scheduledAt) > new Date()).length,
        follow_up_required: scheduledCalls.filter(c => c.followUpRequired).length
      },
      email_summary: {
        total_emails: emailLogs.length,
        delivered_emails: emailLogs.filter(e => e.deliveredAt).length,
        opened_emails: emailLogs.filter(e => e.openedAt).length,
        clicked_emails: emailLogs.filter(e => e.clickedAt).length,
        engagement_rate: emailLogs.length > 0 ? (emailLogs.filter(e => e.openedAt).length / emailLogs.length) * 100 : 0
      },
      custom_fields: isDatabase ? (leadData as DatabaseLeadData).customFields : (leadData as LeadData).customFields || {},
      notes: isDatabase ? (leadData as DatabaseLeadData).description?.substring(0, 1000) : (leadData as LeadData).notes?.substring(0, 1000)
    };
  }

  private buildScoringPrompt(_leadData: LeadData | DatabaseLeadData, context: Record<string, any>): string {
    return `
Analyze this lead and provide a comprehensive scoring based on all available data:

LEAD INFORMATION:
${JSON.stringify(context.basic_info, null, 2)}

INTERACTION HISTORY:
Total Interactions: ${context.interaction_summary.total_interactions}
Recent Interactions (30 days): ${context.interaction_summary.recent_interactions}
Average Engagement Level: ${context.interaction_summary.avg_engagement.toFixed(1)}/10
Sentiment Distribution: ${JSON.stringify(context.interaction_summary.sentiment_distribution)}

ACTIVITY SUMMARY:
Total Activities: ${context.activity_summary.total_activities}
Completed Activities: ${context.activity_summary.completed_activities}
Recent Activities (30 days): ${context.activity_summary.recent_activities}

CALL SUMMARY:
Total Calls: ${context.call_summary.total_calls}
Completed Calls: ${context.call_summary.completed_calls}
Upcoming Calls: ${context.call_summary.upcoming_calls}
Follow-up Required: ${context.call_summary.follow_up_required}

EMAIL SUMMARY:
Total Emails: ${context.email_summary.total_emails}
Delivered Emails: ${context.email_summary.delivered_emails}
Opened Emails: ${context.email_summary.opened_emails}
Clicked Emails: ${context.email_summary.clicked_emails}
Engagement Rate: ${context.email_summary.engagement_rate.toFixed(1)}%

RECENT INTERACTIONS:
${context.interactions.slice(-5).map((i: any) => `
- ${i.type.toUpperCase()} (${i.timestamp}): ${i.content}
  Sentiment: ${i.sentiment || 'unknown'}, Engagement: ${i.engagement_level || 'unknown'}/10
`).join('')}

RECENT ACTIVITIES:
${context.activities.slice(-5).map((a: any) => `
- ${a.type.toUpperCase()} (${a.createdAt}): ${a.subject || 'No subject'}
  Description: ${a.description || 'No description'}
  Status: ${a.completedAt ? 'Completed' : 'Pending'}
  Owner: ${a.owner}
`).join('')}

RECENT CALLS:
${context.scheduled_calls.slice(-3).map((c: any) => `
- ${c.title} (${c.scheduledAt})
  Type: ${c.callType}, Duration: ${c.duration} minutes
  Status: ${c.status}
  ${c.callNotes ? `Notes: ${c.callNotes}` : ''}
  Follow-up Required: ${c.followUpRequired ? 'Yes' : 'No'}
`).join('')}

RECENT EMAILS:
${context.email_logs.slice(-3).map((e: any) => `
- ${e.subject} (${e.sentAt})
  Status: ${e.status}
  Delivered: ${e.deliveredAt ? 'Yes' : 'No'}
  Opened: ${e.openedAt ? 'Yes' : 'No'}
  Clicked: ${e.clickedAt ? 'Yes' : 'No'}
  Sent by: ${e.sentBy}
`).join('')}

ADDITIONAL NOTES:
${context.notes || 'No additional notes'}

Please provide a detailed analysis considering all the interaction history, activities, calls, and email engagement.
Focus on:
1. Lead engagement patterns and responsiveness
2. Communication preferences and timing
3. Interest level based on email opens/clicks and call participation
4. Follow-up requirements and next best actions
5. Overall lead quality and conversion potential

Return your response in this JSON format:
{
  "overall": number,
  "demographic": number,
  "behavioral": number,
  "engagement": number,
  "intent": number,
  "fit": number,
  "reasoning": "detailed explanation considering all data points",
  "recommendations": ["recommendation1", "recommendation2"],
  "nextActions": ["action1", "action2"],
  "confidence": number
}
`;
  }

  private buildQualificationPrompt(_leadData: LeadData | DatabaseLeadData, context: Record<string, any>): string {
    return `
Analyze this lead using the BANT qualification framework based on comprehensive data:

LEAD INFORMATION:
${JSON.stringify(context.basic_info, null, 2)}

INTERACTION SUMMARY:
${JSON.stringify(context.interaction_summary, null, 2)}

ACTIVITY SUMMARY:
${JSON.stringify(context.activity_summary, null, 2)}

CALL SUMMARY:
${JSON.stringify(context.call_summary, null, 2)}

EMAIL SUMMARY:
${JSON.stringify(context.email_summary, null, 2)}

RECENT INTERACTIONS:
${context.interactions.slice(-3).map((i: any) => `
- ${i.type}: ${i.content}
`).join('')}

RECENT CALL NOTES:
${context.scheduled_calls.filter((c: any) => c.callNotes).slice(-2).map((c: any) => `
- ${c.title}: ${c.callNotes}
`).join('')}

ENGAGEMENT INDICATORS:
- Email engagement rate: ${context.email_summary.engagement_rate.toFixed(1)}%
- Call completion rate: ${context.call_summary.total_calls > 0 ? ((context.call_summary.completed_calls / context.call_summary.total_calls) * 100).toFixed(1) : 0}%
- Activity completion rate: ${context.activity_summary.total_activities > 0 ? ((context.activity_summary.completed_activities / context.activity_summary.total_activities) * 100).toFixed(1) : 0}%

Evaluate BANT criteria considering all available data and return in this JSON format:
{
  "budget": "qualified|unqualified|unknown",
  "authority": "qualified|unqualified|unknown",
  "need": "qualified|unqualified|unknown",
  "timeline": "qualified|unqualified|unknown",
  "overall": "qualified|unqualified|unknown",
  "reasoning": "detailed explanation based on all interaction data",
  "questions": ["question1", "question2"]
}
`;
  }

  private parseLeadScore(aiResponse: string): LeadScore {
    try {
      // Extract JSON from AI response
      const jsonMatch = aiResponse.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No JSON found in AI response');
      }

      const parsed = JSON.parse(jsonMatch[0]);
      
      return {
        overall: Math.max(0, Math.min(100, parsed.overall || 0)),
        demographic: Math.max(0, Math.min(100, parsed.demographic || 0)),
        behavioral: Math.max(0, Math.min(100, parsed.behavioral || 0)),
        engagement: Math.max(0, Math.min(100, parsed.engagement || 0)),
        intent: Math.max(0, Math.min(100, parsed.intent || 0)),
        fit: Math.max(0, Math.min(100, parsed.fit || 0)),
        reasoning: parsed.reasoning || 'No reasoning provided',
        recommendations: Array.isArray(parsed.recommendations) ? parsed.recommendations : [],
        nextActions: Array.isArray(parsed.nextActions) ? parsed.nextActions : [],
        confidence: Math.max(0, Math.min(1, parsed.confidence || 0.5))
      };
    } catch (error) {
      console.error('Failed to parse lead score:', error);
      // Return default score if parsing fails
      return {
        overall: 50,
        demographic: 50,
        behavioral: 50,
        engagement: 50,
        intent: 50,
        fit: 50,
        reasoning: 'Failed to analyze lead data',
        recommendations: ['Review lead data manually'],
        nextActions: ['Contact lead for more information'],
        confidence: 0.1
      };
    }
  }

  private parseLeadQualification(aiResponse: string): LeadQualification {
    try {
      const jsonMatch = aiResponse.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No JSON found in AI response');
      }

      const parsed = JSON.parse(jsonMatch[0]);
      
      return {
        budget: parsed.budget || 'unknown',
        authority: parsed.authority || 'unknown',
        need: parsed.need || 'unknown',
        timeline: parsed.timeline || 'unknown',
        overall: parsed.overall || 'unknown',
        reasoning: parsed.reasoning || 'No reasoning provided',
        questions: Array.isArray(parsed.questions) ? parsed.questions : []
      };
    } catch (error) {
      console.error('Failed to parse lead qualification:', error);
      return {
        budget: 'unknown',
        authority: 'unknown',
        need: 'unknown',
        timeline: 'unknown',
        overall: 'unknown',
        reasoning: 'Failed to analyze qualification data',
        questions: ['What is your budget for this project?', 'Who makes the final decision?']
      };
    }
  }

  private calculateSentimentDistribution(interactions: LeadInteraction[]): Record<string, number> {
    const distribution = { positive: 0, neutral: 0, negative: 0, unknown: 0 };
    
    interactions.forEach(interaction => {
      const sentiment = interaction.sentiment || 'unknown';
      distribution[sentiment]++;
    });

    return distribution;
  }

  private async storeAIInsights(
    leadId: string,
    tenantId: string,
    insights: Record<string, any>
  ): Promise<void> {
    try {
      await prisma.aiInsight.create({
        data: {
          tenantId: tenantId,
          relatedToType: 'lead',
          relatedToId: leadId,
          insightType: insights.type,
          data: insights as any,
          confidenceScore: insights.score?.confidence || 0.5,
          createdAt: new Date()
        }
      });
    } catch (error) {
      console.error('Failed to store AI insights:', error);
    }
  }

  async batchScoreLeads(
    leads: DatabaseLeadData[] | LeadData[],
    userId: string,
    _tenantId: string
  ): Promise<Map<string, LeadScore>> {
    const results = new Map<string, LeadScore>();
    
    // Process leads in batches to avoid rate limiting
    const batchSize = 5;
    for (let i = 0; i < leads.length; i += batchSize) {
      const batch = leads.slice(i, i + batchSize);
      
      const batchPromises = batch.map(async (lead) => {
        try {
          const { score } = await this.scoreLeadWithAI(lead, userId);
          results.set(lead.id, score);
        } catch (error) {
          console.error(`Failed to score lead ${lead.id}:`, error);
        }
      });

      await Promise.all(batchPromises);
      
      // Add delay between batches to respect rate limits
      if (i + batchSize < leads.length) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }

    return results;
  }

  /**
   * Trigger AI-powered lead scoring notifications
   */
  private async triggerAILeadScoringNotifications(
    leadData: LeadData | DatabaseLeadData,
    score: LeadScore,
    qualification?: LeadQualification,
    scoredBy?: string
  ): Promise<void> {
    try {
      const isDatabase = 'contact' in leadData;
      const tenantId = leadData.tenantId;
      const leadId = leadData.id;
      const leadTitle = isDatabase ? (leadData as DatabaseLeadData).title : (leadData as LeadData).title;

      // Get lead owner and relevant users
      const ownerId = isDatabase ? (leadData as DatabaseLeadData).ownerId : undefined;
      const targetUsers = [];

      // Always notify the lead owner if assigned
      if (ownerId) {
        targetUsers.push(ownerId);
      }

      // Determine notification triggers based on score thresholds
      const notifications = [];

      // High-value lead detection (score >= 80)
      if (score.overall >= 80) {
        notifications.push({
          type: 'high_value_lead_detected',
          title: 'High-Value Lead Detected by AI',
          message: `AI analysis identified "${leadTitle}" as a high-value lead with ${score.overall}% score`,
          priority: 'high',
          targetSalesTeam: true
        });
      }

      // Hot lead detection (score >= 90)
      if (score.overall >= 90) {
        notifications.push({
          type: 'hot_lead_detected',
          title: 'Hot Lead Alert - Immediate Action Required',
          message: `AI detected an extremely hot lead "${leadTitle}" with ${score.overall}% score. Immediate follow-up recommended.`,
          priority: 'urgent',
          targetSalesTeam: true,
          targetManagers: true
        });
      }

      // Qualified lead detection
      if (qualification?.overall === 'qualified') {
        notifications.push({
          type: 'lead_qualified_by_ai',
          title: 'Lead Qualified by AI Analysis',
          message: `AI analysis has qualified "${leadTitle}" based on BANT criteria`,
          priority: 'high',
          targetSalesTeam: true
        });
      }

      // Low engagement warning (score < 30)
      if (score.overall < 30 && score.engagement < 20) {
        notifications.push({
          type: 'low_engagement_warning',
          title: 'Low Engagement Lead Detected',
          message: `AI analysis shows low engagement for "${leadTitle}". Consider re-engagement strategies.`,
          priority: 'medium',
          targetOwnerOnly: true
        });
      }

      // High intent but low fit (intent > 70, fit < 40)
      if (score.intent > 70 && score.fit < 40) {
        notifications.push({
          type: 'intent_fit_mismatch',
          title: 'High Intent, Low Fit Lead',
          message: `"${leadTitle}" shows high buying intent but low product fit. Review qualification criteria.`,
          priority: 'medium',
          targetOwnerOnly: true
        });
      }

      // Process each notification
      for (const notification of notifications) {
        await this.sendAILeadScoringNotification(
          tenantId,
          leadId,
          leadTitle,
          notification,
          score,
          qualification,
          ownerId,
          scoredBy
        );
      }

    } catch (error) {
      console.error('Failed to send AI lead scoring notifications:', error);
      // Don't throw error to avoid breaking the main operation
    }
  }

  /**
   * Send AI lead scoring notification
   */
  private async sendAILeadScoringNotification(
    tenantId: string,
    leadId: string,
    leadTitle: string,
    notification: any,
    score: LeadScore,
    qualification?: LeadQualification,
    ownerId?: string,
    scoredBy?: string
  ): Promise<void> {
    try {
      const targetUsers = [];

      // Determine target users based on notification settings
      if (notification.targetOwnerOnly && ownerId) {
        targetUsers.push(ownerId);
      } else {
        // Add lead owner
        if (ownerId) {
          targetUsers.push(ownerId);
        }

        // Add sales team if required
        if (notification.targetSalesTeam) {
          const salesTeam = await prisma.tenantUser.findMany({
            where: {
              tenantId,
              OR: [
                { role: { in: ['Sales Rep', 'Sales Manager', 'Account Manager'] } },
                { isTenantAdmin: true }
              ],
              status: 'active'
            },
            select: { userId: true }
          });
          targetUsers.push(...salesTeam.map(member => member.userId));
        }

        // Add managers if required
        if (notification.targetManagers) {
          const managers = await prisma.tenantUser.findMany({
            where: {
              tenantId,
              OR: [
                { role: { in: ['Sales Manager', 'Manager', 'Director', 'VP Sales'] } },
                { isTenantAdmin: true }
              ],
              status: 'active'
            },
            select: { userId: true }
          });
          targetUsers.push(...managers.map(manager => manager.userId));
        }
      }

      // Remove duplicates and exclude the person who triggered the scoring
      const uniqueTargetUsers = [...new Set(targetUsers)].filter(userId => userId !== scoredBy);

      for (const userId of uniqueTargetUsers) {
        await this.notificationService.createNotification({
          tenantId,
          userId,
          type: notification.type,
          category: 'lead',
          title: notification.title,
          message: notification.message,
          data: {
            leadId,
            leadTitle,
            aiScore: {
              overall: score.overall,
              demographic: score.demographic,
              behavioral: score.behavioral,
              engagement: score.engagement,
              intent: score.intent,
              fit: score.fit,
              confidence: score.confidence
            },
            aiRecommendations: score.recommendations,
            aiNextActions: score.nextActions,
            aiReasoning: score.reasoning,
            qualification: qualification ? {
              overall: qualification.overall,
              budget: qualification.budget,
              authority: qualification.authority,
              need: qualification.need,
              timeline: qualification.timeline,
              reasoning: qualification.reasoning
            } : undefined,
            scoredBy,
            scoredAt: new Date().toISOString()
          },
          actionUrl: `/leads/${leadId}`,
          actionLabel: 'View Lead'
        });
      }

    } catch (error) {
      console.error('Failed to send AI lead scoring notification:', error);
    }
  }
}

export const aiLeadScoringService = new AILeadScoringService();
