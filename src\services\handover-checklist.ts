import { prisma } from '@/lib/prisma';
import { z } from 'zod';
import { UnifiedNotificationService } from './unified-notification-service';

// Validation schemas
export const createChecklistTemplateSchema = z.object({
  name: z.string().min(1, 'Name is required').max(255),
  description: z.string().optional(),
  category: z.enum(['general', 'technical', 'legal', 'financial']).default('general'),
  items: z.array(z.object({
    title: z.string().min(1, 'Title is required'),
    description: z.string().optional(),
    category: z.enum(['general', 'technical', 'legal', 'financial', 'client']).default('general'),
    orderIndex: z.number().int().min(0),
    isRequired: z.boolean().default(true),
    dependsOnIds: z.array(z.string()).default([]),
  })).default([]),
  estimatedDuration: z.number().int().positive().optional(),
  isDefault: z.boolean().default(false),
});

export const updateChecklistItemSchema = z.object({
  title: z.string().min(1, 'Title is required').max(255).optional(),
  description: z.string().optional(),
  category: z.enum(['general', 'technical', 'legal', 'financial', 'client']).optional(),
  orderIndex: z.number().int().min(0).optional(),
  isRequired: z.boolean().optional(),
  isCompleted: z.boolean().optional(),
  notes: z.string().optional(),
  dependsOnIds: z.array(z.string()).optional(),
});

// Types
export type CreateChecklistTemplateData = z.infer<typeof createChecklistTemplateSchema>;
export type UpdateChecklistItemData = z.infer<typeof updateChecklistItemSchema>;

export interface ChecklistItemWithDetails {
  id: string;
  tenantId: string;
  handoverId: string;
  title: string;
  description?: string;
  category: string;
  orderIndex: number;
  isRequired: boolean;
  isCompleted: boolean;
  completedAt?: Date;
  completedById?: string;
  notes?: string;
  dependsOnIds: string[];
  createdAt: Date;
  updatedAt: Date;
  completedBy?: {
    id: string;
    firstName?: string;
    lastName?: string;
    email: string;
  };
}

export interface ChecklistTemplateWithItems {
  id: string;
  tenantId: string;
  name: string;
  description?: string;
  category: string;
  isDefault: boolean;
  items: any[];
  estimatedDuration?: number;
  createdById?: string;
  createdAt: Date;
  updatedAt: Date;
  createdBy?: {
    id: string;
    firstName?: string;
    lastName?: string;
    email: string;
  };
}

export class HandoverChecklistService {
  private notificationService = new UnifiedNotificationService();

  /**
   * Create a new checklist template
   */
  async createTemplate(
    tenantId: string,
    templateData: CreateChecklistTemplateData,
    createdBy: string
  ): Promise<ChecklistTemplateWithItems> {
    const validatedData = createChecklistTemplateSchema.parse(templateData);

    return await prisma.$transaction(async (tx) => {
      // If this is set as default, unset other defaults
      if (validatedData.isDefault) {
        await tx.handoverChecklistTemplate.updateMany({
          where: {
            tenantId,
            isDefault: true
          },
          data: {
            isDefault: false
          }
        });
      }

      const template = await tx.handoverChecklistTemplate.create({
        data: {
          tenantId,
          name: validatedData.name,
          description: validatedData.description,
          category: validatedData.category,
          items: validatedData.items,
          estimatedDuration: validatedData.estimatedDuration,
          isDefault: validatedData.isDefault,
          createdById: createdBy,
        },
        include: {
          createdBy: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            }
          }
        }
      });

      return template as ChecklistTemplateWithItems;
    });
  }

  /**
   * Get checklist templates for a tenant
   */
  async getTemplates(
    tenantId: string,
    options: {
      category?: string;
      search?: string;
      page?: number;
      limit?: number;
    } = {}
  ): Promise<{
    templates: ChecklistTemplateWithItems[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  }> {
    const {
      category,
      search,
      page = 1,
      limit = 20
    } = options;

    const where: any = {
      tenantId,
      ...(category && { category }),
      ...(search && {
        OR: [
          { name: { contains: search, mode: 'insensitive' } },
          { description: { contains: search, mode: 'insensitive' } },
        ]
      })
    };

    const [templates, total] = await Promise.all([
      prisma.handoverChecklistTemplate.findMany({
        where,
        include: {
          createdBy: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            }
          }
        },
        orderBy: [
          { isDefault: 'desc' },
          { createdAt: 'desc' }
        ],
        skip: (page - 1) * limit,
        take: limit,
      }),
      prisma.handoverChecklistTemplate.count({ where })
    ]);

    return {
      templates: templates as ChecklistTemplateWithItems[],
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit)
    };
  }

  /**
   * Get a specific template by ID
   */
  async getTemplateById(
    tenantId: string,
    templateId: string
  ): Promise<ChecklistTemplateWithItems | null> {
    const template = await prisma.handoverChecklistTemplate.findUnique({
      where: {
        id: templateId,
        tenantId,
      },
      include: {
        createdBy: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          }
        }
      }
    });

    return template as ChecklistTemplateWithItems | null;
  }

  /**
   * Get default template for a tenant
   */
  async getDefaultTemplate(tenantId: string): Promise<ChecklistTemplateWithItems | null> {
    const template = await prisma.handoverChecklistTemplate.findFirst({
      where: {
        tenantId,
        isDefault: true
      },
      include: {
        createdBy: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          }
        }
      }
    });

    return template as ChecklistTemplateWithItems | null;
  }

  /**
   * Update a template
   */
  async updateTemplate(
    tenantId: string,
    templateId: string,
    updateData: Partial<CreateChecklistTemplateData>,
    updatedBy: string
  ): Promise<ChecklistTemplateWithItems> {
    return await prisma.$transaction(async (tx) => {
      // Verify template exists and belongs to tenant
      const existingTemplate = await tx.handoverChecklistTemplate.findUnique({
        where: {
          id: templateId,
          tenantId,
        }
      });

      if (!existingTemplate) {
        throw new Error('Template not found or access denied');
      }

      // If setting as default, unset other defaults
      if (updateData.isDefault) {
        await tx.handoverChecklistTemplate.updateMany({
          where: {
            tenantId,
            isDefault: true,
            id: { not: templateId }
          },
          data: {
            isDefault: false
          }
        });
      }

      const template = await tx.handoverChecklistTemplate.update({
        where: {
          id: templateId,
          tenantId,
        },
        data: {
          ...updateData,
          updatedAt: new Date(),
        },
        include: {
          createdBy: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            }
          }
        }
      });

      return template as ChecklistTemplateWithItems;
    });
  }

  /**
   * Delete a template
   */
  async deleteTemplate(
    tenantId: string,
    templateId: string
  ): Promise<void> {
    return await prisma.$transaction(async (tx) => {
      // Verify template exists and belongs to tenant
      const existingTemplate = await tx.handoverChecklistTemplate.findUnique({
        where: {
          id: templateId,
          tenantId,
        }
      });

      if (!existingTemplate) {
        throw new Error('Template not found or access denied');
      }

      // Check if template is being used by any handovers
      const handoversUsingTemplate = await tx.projectHandover.count({
        where: {
          tenantId,
          templateId,
        }
      });

      if (handoversUsingTemplate > 0) {
        throw new Error('Cannot delete template that is being used by existing handovers');
      }

      await tx.handoverChecklistTemplate.delete({
        where: {
          id: templateId,
          tenantId,
        }
      });
    });
  }

  /**
   * Create checklist items from template for a handover
   */
  async createChecklistFromTemplate(
    tenantId: string,
    handoverId: string,
    templateId?: string
  ): Promise<ChecklistItemWithDetails[]> {
    return await prisma.$transaction(async (tx) => {
      // Verify handover exists and belongs to tenant
      const handover = await tx.projectHandover.findUnique({
        where: {
          id: handoverId,
          tenantId
        }
      });

      if (!handover) {
        throw new Error('Handover not found or access denied');
      }

      // Get template (use provided templateId or default)
      let template: any;
      if (templateId) {
        template = await tx.handoverChecklistTemplate.findUnique({
          where: {
            id: templateId,
            tenantId
          }
        });
      } else {
        template = await tx.handoverChecklistTemplate.findFirst({
          where: {
            tenantId,
            isDefault: true
          }
        });
      }

      if (!template || !template.items || !Array.isArray(template.items)) {
        throw new Error('Template not found or has no items');
      }

      // Create checklist items from template
      const checklistItems = await Promise.all(
        template.items.map(async (item: any, index: number) => {
          return await tx.handoverChecklistItem.create({
            data: {
              tenantId,
              handoverId,
              title: item.title,
              description: item.description,
              category: item.category || 'general',
              orderIndex: item.orderIndex ?? index,
              isRequired: item.isRequired ?? true,
              dependsOnIds: item.dependsOnIds || [],
            },
            include: {
              completedBy: {
                select: {
                  id: true,
                  firstName: true,
                  lastName: true,
                  email: true,
                }
              }
            }
          });
        })
      );

      return checklistItems as ChecklistItemWithDetails[];
    });
  }

  /**
   * Get checklist items for a handover
   */
  async getChecklistItems(
    tenantId: string,
    handoverId: string
  ): Promise<ChecklistItemWithDetails[]> {
    const items = await prisma.handoverChecklistItem.findMany({
      where: {
        tenantId,
        handoverId
      },
      include: {
        completedBy: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          }
        }
      },
      orderBy: {
        orderIndex: 'asc'
      }
    });

    return items as ChecklistItemWithDetails[];
  }

  /**
   * Update a checklist item
   */
  async updateChecklistItem(
    tenantId: string,
    itemId: string,
    updateData: UpdateChecklistItemData,
    updatedBy?: string
  ): Promise<ChecklistItemWithDetails> {
    const validatedData = updateChecklistItemSchema.parse(updateData);

    return await prisma.$transaction(async (tx) => {
      // Verify item exists and belongs to tenant
      const existingItem = await tx.handoverChecklistItem.findUnique({
        where: {
          id: itemId,
          tenantId
        }
      });

      if (!existingItem) {
        throw new Error('Checklist item not found or access denied');
      }

      // Handle completion status change
      const updateFields: any = { ...validatedData };
      
      if (validatedData.isCompleted === true && !existingItem.isCompleted) {
        updateFields.completedAt = new Date();
        updateFields.completedById = updatedBy;
      } else if (validatedData.isCompleted === false && existingItem.isCompleted) {
        updateFields.completedAt = null;
        updateFields.completedById = null;
      }

      const item = await tx.handoverChecklistItem.update({
        where: {
          id: itemId,
          tenantId
        },
        data: updateFields,
        include: {
          completedBy: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            }
          }
        }
      });

      // Update handover progress
      await this.updateHandoverProgress(tx, tenantId, existingItem.handoverId);

      // Trigger checklist item completion notification if item was just completed
      if (validatedData.isCompleted && !existingItem.isCompleted) {
        await this.triggerChecklistItemCompletionNotification(
          item as ChecklistItemWithDetails,
          tenantId
        );
      }

      return item as ChecklistItemWithDetails;
    });
  }

  /**
   * Calculate and update handover progress based on checklist completion
   */
  private async updateHandoverProgress(
    tx: any,
    tenantId: string,
    handoverId: string
  ): Promise<void> {
    const items = await tx.handoverChecklistItem.findMany({
      where: {
        tenantId,
        handoverId
      }
    });

    if (items.length === 0) {
      return;
    }

    const completedItems = items.filter((item: any) => item.isCompleted);
    const progress = Math.round((completedItems.length / items.length) * 100);

    await tx.projectHandover.update({
      where: {
        id: handoverId,
        tenantId
      },
      data: {
        checklistProgress: progress
      }
    });
  }

  /**
   * Trigger checklist item completion notification
   */
  private async triggerChecklistItemCompletionNotification(
    item: ChecklistItemWithDetails,
    tenantId: string
  ): Promise<void> {
    try {
      // Get handover details to notify relevant users
      const handover = await prisma.projectHandover.findUnique({
        where: {
          id: item.handoverId,
          tenantId
        },
        include: {
          opportunity: {
            select: {
              title: true
            }
          },
          salesRep: {
            select: {
              id: true,
              firstName: true,
              lastName: true
            }
          },
          deliveryManager: {
            select: {
              id: true,
              firstName: true,
              lastName: true
            }
          }
        }
      });

      if (!handover) return;

      const targetUsers = [
        handover.salesRepId,
        handover.deliveryManagerId,
        ...(handover.assignedTeamIds as string[] || [])
      ].filter(Boolean);

      const completedByName = item.completedBy ?
        `${item.completedBy.firstName} ${item.completedBy.lastName}` : 'Unknown';

      for (const userId of Array.from(new Set(targetUsers))) {
        await this.notificationService.createNotification({
          tenantId,
          userId,
          type: 'checklist_item_completed',
          category: 'handover',
          title: 'Checklist Item Completed',
          message: `"${item.title}" has been completed by ${completedByName}`,
          data: {
            handoverId: handover.id,
            handoverTitle: handover.title,
            checklistItemId: item.id,
            checklistItemTitle: item.title,
            checklistItemCategory: item.category,
            completedBy: completedByName,
            completedAt: item.completedAt?.toISOString(),
            handoverProgress: handover.checklistProgress,
            opportunityTitle: handover.opportunity.title
          },
          actionUrl: `/handovers/${handover.id}`,
          actionLabel: 'View Handover'
        });
      }
    } catch (error) {
      console.error('Failed to send checklist item completion notification:', error);
      // Don't throw error to avoid breaking the main operation
    }
  }
}

// Export singleton instance
export const handoverChecklistService = new HandoverChecklistService();
