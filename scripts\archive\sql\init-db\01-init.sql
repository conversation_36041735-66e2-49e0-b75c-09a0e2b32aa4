-- Create extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- Set timezone
SET timezone = 'UTC';

-- Create initial database schema
-- The actual schema will be managed by Prisma migrations
-- This script is just for initial setup and permissions

-- Ensure the database exists
-- Note: This is redundant as the database is created by Docker Compose
-- but included for completeness
CREATE DATABASE IF NOT EXISTS crm_platform;

-- Grant privileges
GRANT ALL PRIVILEGES ON DATABASE crm_platform TO admin;

-- Connection parameters
\c crm_platform

-- Create schema if it doesn't exist
CREATE SCHEMA IF NOT EXISTS public;

-- Set search path
SET search_path TO public;

-- Enable Row Level Security
ALTER DATABASE crm_platform SET row_security = on;

-- <PERSON>reate functions for tenant isolation
CREATE OR REPLACE FUNCTION current_tenant_id() RETURNS TEXT AS $$
BEGIN
    RETURN current_setting('app.current_tenant_id', true);
EXCEPTION
    WHEN OTHERS THEN
        RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- <PERSON>reate function to check if user is platform admin
CREATE OR REPLACE FUNCTION is_platform_admin() RETURNS BOOLEAN AS $$
BEGIN
    RETURN current_setting('app.is_platform_admin', true)::BOOLEAN;
EXCEPTION
    WHEN OTHERS THEN
        RETURN FALSE;
END;
$$ LANGUAGE plpgsql;
