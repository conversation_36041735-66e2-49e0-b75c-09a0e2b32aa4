'use client';

import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { SearchableSelect } from '@/components/ui/searchable-select';
import { X, Loader2 } from 'lucide-react';
import { FileUploadDropzone, FileUploadItem } from '@/components/documents/file-upload-dropzone';
import { createDocumentSchema } from '@/services/document-management';
import { v4 as uuidv4 } from 'uuid';
import { useToast } from '@/hooks/use-toast';

const uploadFormSchema = createDocumentSchema.extend({
  files: z.array(z.any()).min(1, 'At least one file is required'),
  // Handover-specific fields
  category: z.enum(['requirements', 'contracts', 'technical', 'legal', 'financial']).default('requirements'),
  isRequired: z.boolean().default(false),
  isClientVisible: z.boolean().default(false),
  accessLevel: z.enum(['internal', 'delivery_team', 'client']).default('internal'),
  notes: z.string().optional(),
});

type UploadFormData = z.infer<typeof uploadFormSchema>;

interface HandoverDocumentUploadDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  handoverId: string;
  onUploadComplete?: (documents: any[]) => void;
  onUploadError?: (error: string) => void;
}

export function HandoverDocumentUploadDialog({
  open,
  onOpenChange,
  handoverId,
  onUploadComplete,
  onUploadError,
}: HandoverDocumentUploadDialogProps) {
  const { toast } = useToast();
  const [uploadItems, setUploadItems] = useState<FileUploadItem[]>([]);
  const [uploading, setUploading] = useState(false);
  const [tags, setTags] = useState<string[]>([]);
  const [tagInput, setTagInput] = useState('');

  const form = useForm<UploadFormData>({
    resolver: zodResolver(uploadFormSchema),
    defaultValues: {
      name: '',
      relatedToType: 'handover' as any,
      relatedToId: handoverId,
      description: '',
      tags: [],
      isPublic: false,
      files: [],
      category: 'requirements',
      isRequired: false,
      isClientVisible: false,
      accessLevel: 'internal',
      notes: '',
    },
  });

  const handleFilesAdded = (files: File[]) => {
    const newItems: FileUploadItem[] = files.map(file => ({
      file,
      id: uuidv4(),
      progress: 0,
      status: 'pending',
    }));

    setUploadItems(prev => [...prev, ...newItems]);
    form.setValue('files', [...uploadItems.map(item => item.file), ...files]);

    // Auto-set document name if only one file
    if (files.length === 1 && !form.getValues('name')) {
      form.setValue('name', files[0].name.replace(/\.[^/.]+$/, ''));
    }
  };

  const handleFileRemove = (itemId: string) => {
    setUploadItems(prev => prev.filter(item => item.id !== itemId));
    const remainingFiles = uploadItems.filter(item => item.id !== itemId).map(item => item.file);
    form.setValue('files', remainingFiles);
  };

  const addTag = () => {
    if (tagInput.trim() && !tags.includes(tagInput.trim())) {
      const newTags = [...tags, tagInput.trim()];
      setTags(newTags);
      form.setValue('tags', newTags);
      setTagInput('');
    }
  };

  const removeTag = (tagToRemove: string) => {
    const newTags = tags.filter(tag => tag !== tagToRemove);
    setTags(newTags);
    form.setValue('tags', newTags);
  };

  const onSubmit = async (data: UploadFormData) => {
    try {
      setUploading(true);
      const uploadedDocuments = [];

      // Upload each file
      for (const [index, file] of data.files.entries()) {
        const formData = new FormData();
        formData.append('file', file);
        formData.append('name', data.name || file.name);
        formData.append('relatedToType', data.relatedToType);
        formData.append('relatedToId', data.relatedToId);
        formData.append('description', data.description || '');
        formData.append('tags', JSON.stringify(data.tags));
        formData.append('isPublic', data.isPublic.toString());

        // Update progress
        setUploadItems(prev => prev.map(item => 
          item.file === file ? { ...item, status: 'uploading', progress: 0 } : item
        ));

        const response = await fetch('/api/documents', {
          method: 'POST',
          body: formData,
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to upload document');
        }

        const result = await response.json();
        const document = result.data.document;

        // Update progress to complete
        setUploadItems(prev => prev.map(item => 
          item.file === file ? { ...item, status: 'complete', progress: 100 } : item
        ));

        // Now link the document to the handover
        const linkData = {
          documentId: document.id,
          category: data.category,
          isRequired: data.isRequired,
          isClientVisible: data.isClientVisible,
          accessLevel: data.accessLevel,
          notes: data.notes,
        };

        const linkResponse = await fetch(`/api/handovers/${handoverId}/documents`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(linkData),
        });

        if (!linkResponse.ok) {
          const errorData = await linkResponse.json();
          throw new Error(errorData.error || 'Failed to link document to handover');
        }

        const linkedDocument = await linkResponse.json();
        uploadedDocuments.push(linkedDocument);
      }

      toast({
        description: `Successfully uploaded and linked ${uploadedDocuments.length} document(s)`,
        variant: 'default',
      });

      onUploadComplete?.(uploadedDocuments);
      onOpenChange(false);
      
      // Reset form
      form.reset();
      setUploadItems([]);
      setTags([]);

    } catch (error) {
      console.error('Error uploading documents:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to upload documents';
      
      // Update failed items
      setUploadItems(prev => prev.map(item => 
        item.status === 'uploading' ? { ...item, status: 'error', progress: 0 } : item
      ));

      toast({ description: errorMessage, variant: 'destructive' });
      onUploadError?.(errorMessage);
    } finally {
      setUploading(false);
    }
  };

  // Category options
  const getCategoryOptions = () => [
    { value: 'requirements', label: 'Requirements' },
    { value: 'contracts', label: 'Contracts' },
    { value: 'technical', label: 'Technical' },
    { value: 'legal', label: 'Legal' },
    { value: 'financial', label: 'Financial' },
  ];

  // Access level options
  const getAccessLevelOptions = () => [
    { value: 'internal', label: 'Internal Only' },
    { value: 'delivery_team', label: 'Delivery Team' },
    { value: 'client', label: 'Client Access' },
  ];

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Upload New Documents</DialogTitle>
          <DialogDescription>
            Upload new documents and automatically link them to this handover project.
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* File Upload */}
            <div className="space-y-4">
              <FileUploadDropzone
                onFilesAdded={handleFilesAdded}
                onFileRemove={handleFileRemove}
                uploadItems={uploadItems}
                disabled={uploading}
              />
            </div>

            {/* Document Details */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Document Name</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter document name..." {...field} />
                    </FormControl>
                    <FormDescription>
                      Leave empty to use filename
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="category"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Category</FormLabel>
                    <FormControl>
                      <SearchableSelect
                        options={getCategoryOptions()}
                        value={field.value}
                        onValueChange={field.onChange}
                        placeholder="Select category"
                        searchPlaceholder="Search categories..."
                        emptyMessage="No categories found"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Describe the document content..."
                      className="resize-none"
                      rows={3}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Handover-specific settings */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="accessLevel"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Access Level</FormLabel>
                    <FormControl>
                      <SearchableSelect
                        options={getAccessLevelOptions()}
                        value={field.value}
                        onValueChange={field.onChange}
                        placeholder="Select access level"
                        searchPlaceholder="Search access levels..."
                        emptyMessage="No access levels found"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="space-y-3">
                <FormField
                  control={form.control}
                  name="isRequired"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center space-x-3 space-y-0">
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                      <FormLabel className="text-sm font-normal">
                        Required document
                      </FormLabel>
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="isClientVisible"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center space-x-3 space-y-0">
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                      <FormLabel className="text-sm font-normal">
                        Visible to client
                      </FormLabel>
                    </FormItem>
                  )}
                />
              </div>
            </div>

            <FormField
              control={form.control}
              name="notes"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Notes (Optional)</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Add any notes about this document..."
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Tags */}
            <div className="space-y-3">
              <FormLabel>Tags</FormLabel>
              <div className="flex gap-2">
                <Input
                  placeholder="Add tag..."
                  value={tagInput}
                  onChange={(e) => setTagInput(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addTag())}
                />
                <Button type="button" variant="outline" onClick={addTag}>
                  Add
                </Button>
              </div>
              {tags.length > 0 && (
                <div className="flex flex-wrap gap-2">
                  {tags.map((tag) => (
                    <Badge key={tag} variant="secondary" className="flex items-center gap-1">
                      {tag}
                      <X
                        className="w-3 h-3 cursor-pointer"
                        onClick={() => removeTag(tag)}
                      />
                    </Badge>
                  ))}
                </div>
              )}
            </div>

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
                disabled={uploading}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={uploading || uploadItems.length === 0}>
                {uploading && <Loader2 className="w-4 h-4 mr-2 animate-spin" />}
                Upload & Link Documents
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
