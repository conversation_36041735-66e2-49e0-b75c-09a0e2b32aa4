-- Rollback Script for Performance Optimization Indexes
-- This script removes all indexes created by the performance optimization migration
-- Use this script if you need to rollback the performance optimization changes

-- CREATE INDEX statements are replaced with DROP INDEX statements for rollback

-- ============================================================================
-- REMOVE TENANT-SCOPED COMPOSITE INDEXES
-- ============================================================================

-- Contacts indexes
DROP INDEX IF EXISTS idx_contacts_tenant_status_created;
DROP INDEX IF EXISTS idx_contacts_tenant_owner_status;
DROP INDEX IF EXISTS idx_contacts_tenant_company;

-- Leads indexes
DROP INDEX IF EXISTS idx_leads_tenant_status_created;
DROP INDEX IF EXISTS idx_leads_tenant_owner_status;
DROP INDEX IF EXISTS idx_leads_tenant_source;
DROP INDEX IF EXISTS idx_leads_tenant_priority;

-- Opportunities indexes
DROP INDEX IF EXISTS idx_opportunities_tenant_stage_created;
DROP INDEX IF EXISTS idx_opportunities_tenant_owner_stage;
DROP INDEX IF EXISTS idx_opportunities_tenant_value;
DROP INDEX IF EXISTS idx_opportunities_tenant_close_date;

-- Companies indexes
DROP INDEX IF EXISTS idx_companies_tenant_industry;
DROP INDEX IF EXISTS idx_companies_tenant_size;
DROP INDEX IF EXISTS idx_companies_tenant_owner;

-- ============================================================================
-- REMOVE DASHBOARD ANALYTICS OPTIMIZATION INDEXES
-- ============================================================================

-- Monthly aggregation indexes
DROP INDEX IF EXISTS idx_leads_tenant_created_month;
DROP INDEX IF EXISTS idx_opportunities_tenant_created_month;
DROP INDEX IF EXISTS idx_contacts_tenant_created_month;
DROP INDEX IF EXISTS idx_companies_tenant_created_month;

-- Weekly aggregation indexes
DROP INDEX IF EXISTS idx_leads_tenant_created_week;
DROP INDEX IF EXISTS idx_opportunities_tenant_created_week;

-- Conversion tracking indexes
DROP INDEX IF EXISTS idx_leads_tenant_converted;
DROP INDEX IF EXISTS idx_lead_conversion_history_tenant_date;

-- ============================================================================
-- REMOVE FULL-TEXT SEARCH INDEXES
-- ============================================================================

-- Search optimization indexes (GIN indexes)
DROP INDEX IF EXISTS idx_contacts_search;
DROP INDEX IF EXISTS idx_companies_search;
DROP INDEX IF EXISTS idx_leads_search;
DROP INDEX IF EXISTS idx_opportunities_search;

-- ============================================================================
-- REMOVE FOREIGN KEY PERFORMANCE INDEXES
-- ============================================================================

-- Activities indexes
DROP INDEX IF EXISTS idx_activities_tenant_related;
DROP INDEX IF EXISTS idx_activities_tenant_user_date;
DROP INDEX IF EXISTS idx_activities_tenant_type_date;

-- Documents indexes
DROP INDEX IF EXISTS idx_documents_tenant_related;
DROP INDEX IF EXISTS idx_documents_tenant_type;
DROP INDEX IF EXISTS idx_documents_tenant_public;

-- Audit logs indexes
DROP INDEX IF EXISTS idx_audit_logs_tenant_created;
DROP INDEX IF EXISTS idx_audit_logs_tenant_user_action;
DROP INDEX IF EXISTS idx_audit_logs_tenant_resource;

-- ============================================================================
-- REMOVE USER AND PERMISSION OPTIMIZATION INDEXES
-- ============================================================================

-- User roles indexes
DROP INDEX IF EXISTS idx_user_roles_tenant_user;
DROP INDEX IF EXISTS idx_user_roles_tenant_role;

-- Role permissions indexes
DROP INDEX IF EXISTS idx_role_permissions_tenant_role;
DROP INDEX IF EXISTS idx_role_permissions_tenant_permission;

-- Tenant users indexes
DROP INDEX IF EXISTS idx_tenant_users_user_status;
DROP INDEX IF EXISTS idx_tenant_users_tenant_role;

-- ============================================================================
-- REMOVE PARTIAL INDEXES FOR FILTERED QUERIES
-- ============================================================================

-- Active records indexes
DROP INDEX IF EXISTS idx_leads_active_tenant_owner;
DROP INDEX IF EXISTS idx_opportunities_active_tenant_stage;
DROP INDEX IF EXISTS idx_subscriptions_active_tenant;

-- Recent activity indexes
DROP INDEX IF EXISTS idx_activities_recent_tenant;

-- Unread notifications
DROP INDEX IF EXISTS idx_notifications_unread_tenant;

-- High-value opportunities
DROP INDEX IF EXISTS idx_opportunities_high_value_tenant;

-- ============================================================================
-- REMOVE SPECIALIZED REPORTING INDEXES
-- ============================================================================

-- Lead source effectiveness
DROP INDEX IF EXISTS idx_leads_source_conversion;

-- Opportunity stage progression
DROP INDEX IF EXISTS idx_opportunity_stage_history_progression;

-- Sales performance tracking
DROP INDEX IF EXISTS idx_opportunities_owner_closed;

-- Usage metrics optimization
DROP INDEX IF EXISTS idx_usage_metrics_tenant_period;

-- AI insights optimization
DROP INDEX IF EXISTS idx_ai_insights_tenant_type_date;

-- ============================================================================
-- REMOVE MAINTENANCE AND MONITORING INDEXES
-- ============================================================================

-- Email logs optimization
DROP INDEX IF EXISTS idx_email_logs_tenant_status_date;

-- Scheduled meetings optimization
DROP INDEX IF EXISTS idx_scheduled_meetings_tenant_date;
DROP INDEX IF EXISTS idx_scheduled_meetings_tenant_assigned;

-- Billing events optimization
DROP INDEX IF EXISTS idx_billing_events_tenant_type_date;

-- Team management optimization
DROP INDEX IF EXISTS idx_team_members_tenant_team;
DROP INDEX IF EXISTS idx_team_members_tenant_user;

-- ============================================================================
-- VERIFICATION QUERIES
-- ============================================================================

-- Check remaining custom indexes (should only show system indexes after rollback)
SELECT 
    schemaname,
    tablename,
    indexname,
    indexdef
FROM pg_indexes 
WHERE schemaname = 'public' 
    AND indexname LIKE 'idx_%'
ORDER BY tablename, indexname;

-- Check database size after index removal
SELECT pg_size_pretty(pg_database_size(current_database())) as database_size;

-- Check table sizes after index removal
SELECT 
    schemaname||'.'||tablename as table_name,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as total_size,
    pg_size_pretty(pg_relation_size(schemaname||'.'||tablename)) as table_size,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename) - pg_relation_size(schemaname||'.'||tablename)) as index_size
FROM pg_stat_user_tables 
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC 
LIMIT 10;

-- ============================================================================
-- ROLLBACK COMPLETION MESSAGE
-- ============================================================================

DO $$
BEGIN
    RAISE NOTICE '✅ Performance optimization indexes rollback completed successfully!';
    RAISE NOTICE '📊 Database size and performance may be affected.';
    RAISE NOTICE '⚠️  Consider running VACUUM ANALYZE to reclaim space and update statistics.';
    RAISE NOTICE '🔄 You may want to restart the application to clear any cached query plans.';
END $$;
