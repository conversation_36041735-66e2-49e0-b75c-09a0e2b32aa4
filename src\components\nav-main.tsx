"use client"

import { ChevronRight } from "lucide-react"
import { SmoothLink } from "@/components/ui/smooth-link"
import { useRTL } from "@/hooks/use-rtl"
import { memo, useState } from "react"

import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible"
import {
  SidebarGroup,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
} from "@/components/ui/sidebar"

export const NavMain = memo(function NavMain({
  items,
}: {
  items: {
    title: string
    url: string
    icon?: any
    isActive?: boolean
    items?: {
      title: string
      url: string
    }[]
  }[]
}) {
  const { isRTL } = useRTL()
  const [openItems, setOpenItems] = useState<Record<string, boolean>>(() => {
    // Initialize with active items open
    const initial: Record<string, boolean> = {}
    items.forEach(item => {
      if (item.isActive && item.items && item.items.length > 0) {
        initial[item.title] = true
      }
    })
    return initial
  })

  const toggleItem = (itemTitle: string) => {
    setOpenItems(prev => ({
      ...prev,
      [itemTitle]: !prev[itemTitle]
    }))
  }

  return (
    <SidebarGroup>
      <SidebarGroupLabel>CRM Platform</SidebarGroupLabel>
      <SidebarMenu>
        {items.map((item) => (
          <Collapsible
            key={item.title}
            open={openItems[item.title] || false}
            onOpenChange={(open) => setOpenItems(prev => ({ ...prev, [item.title]: open }))}
            className="group/collapsible"
          >
            <SidebarMenuItem>
              {/* Main navigation item - different behavior for parent vs leaf items */}
              {item.items && item.items.length > 0 ? (
                // Parent item - only toggle, don't navigate
                <SidebarMenuButton
                  tooltip={item.title}
                  className="w-full cursor-pointer"
                  onClick={() => toggleItem(item.title)}
                >
                  {item.icon && <item.icon className="w-4 h-4" />}
                  <span className="flex-1">{item.title}</span>
                  <ChevronRight className={`w-4 h-4 transition-transform duration-200 ${openItems[item.title] ? 'rotate-90' : ''} ${isRTL ? 'rotate-180' : ''}`} />
                </SidebarMenuButton>
              ) : (
                // Leaf item - navigate normally
                <SidebarMenuButton asChild tooltip={item.title}>
                  <SmoothLink href={item.url} className="flex items-center w-full">
                    {item.icon && <item.icon className="w-4 h-4" />}
                    <span className="flex-1">{item.title}</span>
                  </SmoothLink>
                </SidebarMenuButton>
              )}
              {item.items && item.items.length > 0 && (
                <CollapsibleContent>
                  <SidebarMenuSub>
                    {item.items.map((subItem) => (
                      <SidebarMenuSubItem key={subItem.title}>
                        <SidebarMenuSubButton asChild>
                          <SmoothLink href={subItem.url}>
                            <span>{subItem.title}</span>
                          </SmoothLink>
                        </SidebarMenuSubButton>
                      </SidebarMenuSubItem>
                    ))}
                  </SidebarMenuSub>
                </CollapsibleContent>
              )}
            </SidebarMenuItem>
          </Collapsible>
        ))}
      </SidebarMenu>
    </SidebarGroup>
  )
})
