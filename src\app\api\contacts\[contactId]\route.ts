import { NextResponse } from 'next/server';
import { ContactManagementService } from '@/services/contact-management';
import { withPermission, AuthenticatedRequest } from '@/lib/permission-middleware';
import { PermissionResource, PermissionAction } from '@/types';

const contactService = new ContactManagementService();

/**
 * GET /api/contacts/[contactId]
 * Get a specific contact by ID
 */
export const GET = withPermission({
  resource: PermissionResource.CONTACTS,
  action: PermissionAction.READ,
  requireOwnership: true,
  getResourceOwnerId: async (req) => {
    const url = new URL(req.url);
    const pathSegments = url.pathname.split('/');
    return pathSegments[pathSegments.indexOf('contacts') + 1];
  },
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest) => {
  try {
    const url = new URL(req.url);
    const pathSegments = url.pathname.split('/');
    const contactId = pathSegments[pathSegments.indexOf('contacts') + 1];

    const contact = await contactService.getContact(contactId, req.tenantId);

    if (!contact) {
      return NextResponse.json(
        { error: 'Contact not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: { contact },
      message: 'Contact retrieved successfully'
    });

  } catch (error) {
    console.error('Get contact error:', error);

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
});

/**
 * PUT /api/contacts/[contactId]
 * Update a specific contact
 */
export const PUT = withPermission({
  resource: PermissionResource.CONTACTS,
  action: PermissionAction.UPDATE,
  requireOwnership: true,
  getResourceOwnerId: async (req) => {
    const url = new URL(req.url);
    const pathSegments = url.pathname.split('/');
    return pathSegments[pathSegments.indexOf('contacts') + 1];
  },
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest) => {
  try {
    const url = new URL(req.url);
    const pathSegments = url.pathname.split('/');
    const contactId = pathSegments[pathSegments.indexOf('contacts') + 1];
    const body = await req.json();

    const contact = await contactService.updateContact(
      contactId,
      req.tenantId,
      body,
      req.userId
    );

    return NextResponse.json({
      success: true,
      data: { contact },
      message: 'Contact updated successfully'
    });

  } catch (error) {
    console.error('Update contact error:', error);

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
});

/**
 * DELETE /api/contacts/[contactId]
 * Delete a specific contact
 */
export const DELETE = withPermission({
  resource: PermissionResource.CONTACTS,
  action: PermissionAction.DELETE,
  requireOwnership: true,
  getResourceOwnerId: async (req) => {
    const url = new URL(req.url);
    const pathSegments = url.pathname.split('/');
    return pathSegments[pathSegments.indexOf('contacts') + 1];
  },
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest) => {
  try {
    const url = new URL(req.url);
    const pathSegments = url.pathname.split('/');
    const contactId = pathSegments[pathSegments.indexOf('contacts') + 1];

    await contactService.deleteContact(contactId, req.tenantId);

    return NextResponse.json({
      success: true,
      message: 'Contact deleted successfully'
    });

  } catch (error) {
    console.error('Delete contact error:', error);

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
});
