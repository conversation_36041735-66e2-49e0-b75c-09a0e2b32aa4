/**
 * Seed Default Sales Stages for All Tenants
 * 
 * This script creates default sales stages for all existing tenants
 * and can be run safely multiple times (idempotent).
 */

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

// Default sales stages configuration
const DEFAULT_SALES_STAGES = [
  {
    name: 'Prospecting',
    description: 'Initial contact and research phase',
    orderIndex: 1,
    color: '#6B7280',
    probabilityMin: 0,
    probabilityMax: 20,
    isClosed: false,
    isWon: false
  },
  {
    name: 'Qualification',
    description: 'Qualifying the opportunity and understanding needs',
    orderIndex: 2,
    color: '#3B82F6',
    probabilityMin: 20,
    probabilityMax: 40,
    isClosed: false,
    isWon: false
  },
  {
    name: 'Proposal',
    description: 'Preparing and presenting proposal',
    orderIndex: 3,
    color: '#F59E0B',
    probabilityMin: 40,
    probabilityMax: 70,
    isClosed: false,
    isWon: false
  },
  {
    name: 'Negotiation',
    description: 'Negotiating terms and finalizing details',
    orderIndex: 4,
    color: '#EF4444',
    probabilityMin: 70,
    probabilityMax: 90,
    isClosed: false,
    isWon: false
  },
  {
    name: 'Closed Won',
    description: 'Successfully closed deal',
    orderIndex: 5,
    color: '#10B981',
    probabilityMin: 100,
    probabilityMax: 100,
    isClosed: true,
    isWon: true
  },
  {
    name: 'Closed Lost',
    description: 'Deal was lost or cancelled',
    orderIndex: 6,
    color: '#6B7280',
    probabilityMin: 0,
    probabilityMax: 0,
    isClosed: true,
    isWon: false
  }
];

async function seedSalesStages() {
  console.log('🚀 Starting sales stages seeding...');

  try {
    // Get all tenants
    const tenants = await prisma.tenant.findMany({
      select: {
        id: true,
        name: true,
        slug: true
      }
    });

    console.log(`📊 Found ${tenants.length} tenants`);

    let totalStagesCreated = 0;
    let tenantsUpdated = 0;

    for (const tenant of tenants) {
      console.log(`\n🏢 Processing tenant: ${tenant.name} (${tenant.slug})`);

      // Check if tenant already has sales stages
      const existingStages = await prisma.salesStage.findMany({
        where: { tenantId: tenant.id }
      });

      if (existingStages.length > 0) {
        console.log(`   ✅ Tenant already has ${existingStages.length} sales stages, skipping...`);
        continue;
      }

      // Create default sales stages for this tenant
      const stagesToCreate = DEFAULT_SALES_STAGES.map(stage => ({
        ...stage,
        tenantId: tenant.id
      }));

      try {
        const createdStages = await prisma.salesStage.createMany({
          data: stagesToCreate,
          skipDuplicates: true
        });

        console.log(`   ✅ Created ${createdStages.count} sales stages`);
        totalStagesCreated += createdStages.count;
        tenantsUpdated++;

      } catch (error) {
        console.error(`   ❌ Error creating stages for tenant ${tenant.name}:`, error.message);
      }
    }

    console.log('\n📈 Sales stages seeding completed!');
    console.log(`   • Tenants updated: ${tenantsUpdated}`);
    console.log(`   • Total stages created: ${totalStagesCreated}`);

    // Update existing opportunities to use default stage if needed
    console.log('\n🔄 Updating existing opportunities...');
    
    const opportunitiesUpdated = await prisma.opportunity.updateMany({
      where: {
        stage: {
          notIn: DEFAULT_SALES_STAGES.map(s => s.name.toLowerCase())
        }
      },
      data: {
        stage: 'qualification' // Default stage
      }
    });

    console.log(`   ✅ Updated ${opportunitiesUpdated.count} opportunities to use default stage`);

  } catch (error) {
    console.error('❌ Error during sales stages seeding:', error);
    throw error;
  }
}

async function main() {
  try {
    await seedSalesStages();
  } catch (error) {
    console.error('Fatal error:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the script
if (require.main === module) {
  main();
}

module.exports = { seedSalesStages, DEFAULT_SALES_STAGES };
