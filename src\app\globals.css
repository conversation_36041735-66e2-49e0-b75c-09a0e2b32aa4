@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@100..900&display=swap');
@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

:root {
  /* Modern monochromatic color scheme */
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --destructive-foreground: hsl(210 40% 98%);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --radius: 0.625rem;

  /* Chart colors - vibrant and distinct */
  --chart-1: 142 71% 73%;     /* Vibrant Green for Contacts */
  --chart-2: 217 91% 60%;     /* Vibrant Blue for Leads */
  --chart-3: 25 95% 53%;      /* Vibrant Orange for Opportunities */
  --chart-4: 271 81% 56%;     /* Vibrant Purple */
  --chart-5: 0 84% 60%;       /* Vibrant Red */

  /* Subtle accent colors */
  --success: hsl(142.1 76.2% 36.3%);
  --warning: hsl(47.9 95.8% 53.1%);
  --info: hsl(199.4 89.1% 48.4%);
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --destructive-foreground: hsl(210 40% 98%);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);

  /* Chart colors - dark mode vibrant */
  --chart-1: 142 69% 76%;     /* Bright Green for Contacts */
  --chart-2: 217 91% 65%;     /* Bright Blue for Leads */
  --chart-3: 25 95% 58%;      /* Bright Orange for Opportunities */
  --chart-4: 271 81% 61%;     /* Bright Purple */
  --chart-5: 0 84% 65%;       /* Bright Red */
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --font-sans: 'Inter', system-ui, sans-serif;
  --font-arabic: 'Noto Sans Arabic', system-ui, sans-serif;
  --radius: var(--radius);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

/* Enhanced smooth transitions */
* {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 200ms;
}

/* Custom scrollbars */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: hsl(var(--muted));
}

::-webkit-scrollbar-thumb {
  background: hsl(var(--muted-foreground) / 0.3);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--muted-foreground) / 0.5);
}

/* Focus styles */
.focus-ring {
  @apply focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 focus:ring-offset-background;
}

/* Glass morphism utility */
.glass {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.dark .glass {
  background: rgba(0, 0, 0, 0.4);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Animation utilities - removed page transition animations */

body {
  font-family: 'Inter', system-ui, sans-serif;
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Enhanced Arabic font support */
.font-arabic {
  font-family: 'Noto Sans Arabic', system-ui, sans-serif;
  font-feature-settings: 'kern' 1, 'liga' 1;
}

/* Enhanced RTL support */
[dir="rtl"] {
  text-align: right;
}

[dir="rtl"] .rtl\:text-left {
  text-align: left;
}

[dir="rtl"] .rtl\:text-right {
  text-align: right;
}

[dir="rtl"] .rtl\:space-x-reverse > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 1;
}

[dir="rtl"] .rtl\:flex-row-reverse {
  flex-direction: row-reverse;
}

/* Enhanced RTL support for layout positioning */
[dir="rtl"] .lg\:pr-16 {
  padding-right: 4rem;
}

[dir="rtl"] .lg\:pr-64 {
  padding-right: 16rem;
}

[dir="rtl"] .lg\:right-0 {
  right: 0;
}

[dir="rtl"] .lg\:left-0 {
  left: auto;
}

/* RTL gap support */
[dir="rtl"] .gap-x-reverse {
  flex-direction: row-reverse;
}

[dir="rtl"] .space-x-reverse > :not([hidden]) ~ :not([hidden]) {
  margin-right: var(--tw-space-x-reverse, 0);
  margin-left: calc(var(--tw-space-x, 0) * calc(1 - var(--tw-space-x-reverse, 0)));
}

/* Sidebar RTL positioning */
[dir="rtl"] .group\/sidebar-wrapper {
  direction: rtl;
}

[dir="rtl"] .group\/sidebar-wrapper [data-side="left"] {
  right: 0;
  left: auto;
}

[dir="rtl"] .group\/sidebar-wrapper [data-side="right"] {
  left: 0;
  right: auto;
}

/* RTL sidebar content alignment */
[dir="rtl"] .sidebar-content {
  text-align: right;
}

[dir="rtl"] .sidebar-content .flex {
  flex-direction: row-reverse;
}

/* RTL dropdown positioning */
[dir="rtl"] .dropdown-content {
  right: auto;
  left: 0;
}

/* RTL form elements */
[dir="rtl"] input,
[dir="rtl"] textarea,
[dir="rtl"] select {
  text-align: right;
}

[dir="rtl"] .form-label {
  text-align: right;
}

/* RTL navigation */
[dir="rtl"] .nav-item {
  text-align: right;
}

[dir="rtl"] .nav-item .icon {
  margin-left: 0.5rem;
  margin-right: 0;
}

/* RTL breadcrumb */
[dir="rtl"] .breadcrumb {
  flex-direction: row-reverse;
}

[dir="rtl"] .breadcrumb-separator {
  transform: scaleX(-1);
}

/* RTL table alignment */
[dir="rtl"] table {
  direction: rtl;
}

[dir="rtl"] th,
[dir="rtl"] td {
  text-align: right;
}

/* RTL button groups */
[dir="rtl"] .btn-group {
  flex-direction: row-reverse;
}

/* RTL modal positioning */
[dir="rtl"] .modal-content {
  text-align: right;
}

/* RTL tooltip positioning */
[dir="rtl"] .tooltip {
  direction: rtl;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: rgb(156 163 175 / 0.5);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgb(156 163 175 / 0.7);
}

.dark ::-webkit-scrollbar-thumb {
  background: rgb(75 85 99 / 0.5);
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: rgb(75 85 99 / 0.7);
}

/* Enhanced animations - removed page transition animations */

/* Focus styles */
.focus-ring {
  outline: none;
  box-shadow: 0 0 0 2px var(--primary-500), 0 0 0 4px rgba(59, 130, 246, 0.1);
}

.dark .focus-ring {
  box-shadow: 0 0 0 2px var(--primary-500), 0 0 0 4px rgba(59, 130, 246, 0.2);
}

/* Glass morphism effect */
.glass {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.dark .glass {
  background: rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Improved Radix UI Select positioning and animations */
[data-radix-select-content] {
  z-index: 50;
  position: relative;
}

[data-radix-popper-content-wrapper] {
  z-index: 50;
}

/* Ensure select content appears correctly positioned */
[data-radix-select-content][data-side="bottom"] {
  transform-origin: top;
}

[data-radix-select-content][data-side="top"] {
  transform-origin: bottom;
}

[data-radix-select-content][data-side="left"] {
  transform-origin: right;
}

[data-radix-select-content][data-side="right"] {
  transform-origin: left;
}

/* Smooth animations for select content */
[data-radix-select-content][data-state="open"] {
  animation: selectContentShow 0.15s ease-out;
}

[data-radix-select-content][data-state="closed"] {
  animation: selectContentHide 0.1s ease-in;
}

@keyframes selectContentShow {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(-2px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

@keyframes selectContentHide {
  from {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
  to {
    opacity: 0;
    transform: scale(0.95) translateY(-2px);
  }
}

/* Ensure select trigger has proper styling */
[data-radix-select-trigger] {
  transition: color 0.2s, background-color 0.2s, border-color 0.2s, box-shadow 0.2s !important;
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Lead Pipeline specific styles */
.lead-pipeline-scroll {
  scrollbar-width: thin;
  scrollbar-color: rgb(203 213 225) rgb(241 245 249);
}

.lead-pipeline-scroll::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.lead-pipeline-scroll::-webkit-scrollbar-track {
  background: rgb(241 245 249);
  border-radius: 4px;
}

.lead-pipeline-scroll::-webkit-scrollbar-thumb {
  background: rgb(203 213 225);
  border-radius: 4px;
  border: 1px solid rgb(241 245 249);
}

.lead-pipeline-scroll::-webkit-scrollbar-thumb:hover {
  background: rgb(148 163 184);
}

.dark .lead-pipeline-scroll {
  scrollbar-color: rgb(75 85 99) rgb(31 41 55);
}

.dark .lead-pipeline-scroll::-webkit-scrollbar-track {
  background: rgb(31 41 55);
}

.dark .lead-pipeline-scroll::-webkit-scrollbar-thumb {
  background: rgb(75 85 99);
  border: 1px solid rgb(31 41 55);
}

.dark .lead-pipeline-scroll::-webkit-scrollbar-thumb:hover {
  background: rgb(107 114 128);
}

/* Scroll shadows for better UX */
.scroll-shadow-left {
  box-shadow: inset 10px 0 10px -10px rgba(0, 0, 0, 0.1);
}

.scroll-shadow-right {
  box-shadow: inset -10px 0 10px -10px rgba(0, 0, 0, 0.1);
}

/* Drag and Drop styles for proposal sections */
.proposal-section-dragging {
  transform: rotate(2deg);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  z-index: 1000;
}

.proposal-section-drag-handle {
  opacity: 0.6;
  transition: opacity 0.2s ease;
}

.proposal-section-drag-handle:hover {
  opacity: 1;
  background-color: rgba(0, 0, 0, 0.05);
}

.dark .proposal-section-drag-handle:hover {
  background-color: rgba(255, 255, 255, 0.05);
}

/* Smooth transitions for reordering */
.proposal-section-transition {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.dark .scroll-shadow-left {
  box-shadow: inset 10px 0 10px -10px rgba(255, 255, 255, 0.1);
}

.dark .scroll-shadow-right {
  box-shadow: inset -10px 0 10px -10px rgba(255, 255, 255, 0.1);
}

/* Lead pipeline card enhancements - animations removed */
.lead-card {
  /* No animations */
}

/* Pipeline column scroll indicators */
.pipeline-column {
  position: relative;
}

.pipeline-column::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 20px;
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0.8), transparent);
  pointer-events: none;
  z-index: 1;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.pipeline-column::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 20px;
  background: linear-gradient(to top, rgba(255, 255, 255, 0.8), transparent);
  pointer-events: none;
  z-index: 1;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.pipeline-column.scrollable::before,
.pipeline-column.scrollable::after {
  opacity: 1;
}

.dark .pipeline-column::before {
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0.8), transparent);
}

.dark .pipeline-column::after {
  background: linear-gradient(to top, rgba(0, 0, 0, 0.8), transparent);
}

/* Calendar popover specific styles */
[data-radix-popover-content]:has(.rdp) {
  z-index: 1000 !important;
  position: fixed !important;
}

/* Ensure calendar popovers don't overlap with form elements */
.rdp {
  background: white;
  border: 1px solid hsl(var(--border));
  border-radius: 8px;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.dark .rdp {
  background: hsl(var(--popover));
  border-color: hsl(var(--border));
}

/* Fix calendar positioning issues */
[data-radix-popover-content][data-side="bottom"] {
  margin-top: 4px;
}

[data-radix-popover-content][data-side="top"] {
  margin-bottom: 4px;
}

/* Ensure calendar doesn't get cut off */
[data-radix-popover-content]:has(.rdp) {
  max-height: none !important;
  overflow: visible !important;
}
