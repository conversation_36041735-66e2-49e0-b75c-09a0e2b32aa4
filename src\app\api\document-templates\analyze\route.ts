import { NextRequest, NextResponse } from 'next/server';
import { withPermission, AuthenticatedRequest } from '@/lib/permission-middleware';
import { aiTemplateAnalysisService } from '@/services/ai-template-analysis';
import { PermissionResource, PermissionAction } from '@/types';
import { z } from 'zod';

// Request validation schema
const analyzeRequestSchema = z.object({
  file: z.instanceof(File).refine(
    (file) => file.name.toLowerCase().endsWith('.docx'),
    'Only DOCX files are supported'
  ).refine(
    (file) => file.size <= 10 * 1024 * 1024, // 10MB limit
    'File size must be less than 10MB'
  ),
});

/**
 * POST /api/document-templates/analyze
 * Analyze a DOCX template file using AI to extract styling, branding, and variables
 */
export const POST = withPermission({
  resource: PermissionResource.PROPOSALS,
  action: PermissionAction.CREATE,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest) => {
  try {
    // Parse form data
    const formData = await req.formData();
    const file = formData.get('file') as File;

    if (!file) {
      return NextResponse.json(
        { error: 'No file provided' },
        { status: 400 }
      );
    }

    // Validate file
    try {
      analyzeRequestSchema.parse({ file });
    } catch (error) {
      if (error instanceof z.ZodError) {
        return NextResponse.json(
          { error: error.errors[0].message },
          { status: 400 }
        );
      }
      throw error;
    }

    // Check if AI service is available
    if (!process.env.GOOGLE_AI_API_KEY) {
      return NextResponse.json(
        { error: 'AI analysis service is not configured' },
        { status: 503 }
      );
    }

    // Perform AI template analysis
    const analysis = await aiTemplateAnalysisService.analyzeTemplate({
      file,
      tenantId: req.tenantId,
      userId: req.userId
    });

    return NextResponse.json({
      success: true,
      data: {
        styling: analysis.styling,
        branding: analysis.branding,
        variables: analysis.variables,
        htmlTemplate: analysis.htmlTemplate,
        extractedStructure: analysis.extractedStructure,
        placeholderMappings: analysis.placeholderMappings,
        templateMetadata: analysis.templateMetadata,
        confidence: analysis.confidence,
        processingTime: analysis.processingTime
      },
      message: 'Template analyzed successfully'
    });

  } catch (error) {
    console.error('Template analysis error:', error);
    
    if (error instanceof Error) {
      // Handle specific error types
      if (error.message.includes('API key')) {
        return NextResponse.json(
          { error: 'AI service configuration error' },
          { status: 503 }
        );
      }
      
      if (error.message.includes('file processing failed')) {
        return NextResponse.json(
          { error: 'Failed to process the template file. Please ensure it is a valid DOCX file.' },
          { status: 400 }
        );
      }
      
      if (error.message.includes('Only DOCX files')) {
        return NextResponse.json(
          { error: 'Only DOCX files are supported for template analysis' },
          { status: 400 }
        );
      }

      return NextResponse.json(
        { error: error.message },
        { status: 500 }
      );
    }

    return NextResponse.json(
      { error: 'An unexpected error occurred during template analysis' },
      { status: 500 }
    );
  }
});
