import { prisma } from '@/lib/prisma';
import bcrypt from 'bcryptjs';
import { TenantContext } from '@/types';
import {
  PERMISSIONS,
  getAllPermissions,
  DEFAULT_ROLE_PERMISSIONS,
  parsePermission
} from '@/constants/permissions';

export interface CreateTenantData {
  name: string;
  slug: string;
  adminEmail: string;
  adminPassword?: string;
  planId: string;
  adminFirstName?: string;
  adminLastName?: string;
  adminPhone?: string;
}

// Instant tenant provisioning service
export class TenantProvisioningService {
  async createTenant(tenantData: CreateTenantData): Promise<TenantContext> {
    return await prisma.$transaction(async (tx) => {
      try {
        // 1. Check if slug is available
        const existingTenant = await tx.tenant.findUnique({
          where: { slug: tenantData.slug }
        });

        if (existingTenant) {
          throw new Error('Tenant slug already exists');
        }

        // 2. Check if user already exists
        let user = await tx.user.findUnique({
          where: { email: tenantData.adminEmail }
        });

        if (!user) {
          // 3. Create new user if doesn't exist
          const hashedPassword = tenantData.adminPassword 
            ? await bcrypt.hash(tenantData.adminPassword, 12)
            : null;

          user = await tx.user.create({
            data: {
              email: tenantData.adminEmail,
              passwordHash: hashedPassword,
              firstName: tenantData.adminFirstName || 'Admin',
              lastName: tenantData.adminLastName || 'User',
              phone: tenantData.adminPhone,
              emailVerified: false,
              status: 'active'
            }
          });
        }

        // 4. Create tenant record (instant - no schema creation needed)
        const tenant = await tx.tenant.create({
          data: {
            name: tenantData.name,
            slug: tenantData.slug,
            status: 'active',
            settings: {},
            branding: {}
          }
        });

        // 5. Create default roles for this tenant
        await this.createDefaultRoles(tenant.id, tx);

        // 6. Create default permissions for this tenant
        await this.createDefaultPermissions(tenant.id, tx);

        // 7. Assign permissions to roles
        await this.assignPermissionsToRoles(tenant.id, tx);

        // 8. Get the tenant admin role
        const adminRole = await tx.role.findFirst({
          where: {
            tenantId: tenant.id,
            name: 'Tenant Admin'
          }
        });

        if (!adminRole) {
          throw new Error('Failed to create Tenant Admin role');
        }

        // 9. Link user to tenant as admin
        await tx.tenantUser.create({
          data: {
            tenantId: tenant.id,
            userId: user.id,
            isTenantAdmin: true,
            role: 'tenant_admin',
            status: 'active'
          }
        });

        // 10. Create UserRole assignment for proper RBAC
        await tx.userRole.create({
          data: {
            tenantId: tenant.id,
            userId: user.id,
            roleId: adminRole.id,
            assignedBy: user.id
          }
        });

        // 11. Create subscription
        const subscription = await this.createSubscription(tenant.id, tenantData.planId, tx);

        // 12. Set up default tenant settings
        await this.createDefaultSettings(tenant.id, tx);

        // 13. Initialize usage tracking
        await this.initializeUsageTracking(tenant.id, tx);

        return {
          id: tenant.id,
          name: tenant.name,
          slug: tenant.slug,
          subscription: {
            planId: subscription.planId,
            status: subscription.status,
            features: [],
            limits: {}
          },
          settings: tenant.settings as Record<string, any>,
          branding: tenant.branding as any
        };
      } catch (error) {
        console.error('Tenant creation failed:', error);
        throw error;
      }
    });
  }

  private async createDefaultRoles(tenantId: string, tx: any): Promise<void> {
    const defaultRoles = [
      {
        name: 'Tenant Admin',
        description: 'Tenant Administrator with full access to all resources',
        isSystemRole: true
      },
      {
        name: 'Manager',
        description: 'Manager with team management and most CRM capabilities',
        isSystemRole: true
      },
      {
        name: 'Sales Rep',
        description: 'Sales Representative with CRM access for contacts, leads, and opportunities',
        isSystemRole: true
      },
      {
        name: 'Viewer',
        description: 'Read-only access to assigned records',
        isSystemRole: true
      }
    ];

    for (const role of defaultRoles) {
      await tx.role.create({
        data: {
          tenantId,
          name: role.name,
          description: role.description,
          isSystemRole: role.isSystemRole
        }
      });
    }
  }

  private async createDefaultPermissions(tenantId: string, tx: any): Promise<void> {
    // Generate all permissions from constants to ensure consistency and prevent typos
    const allPermissionNames = getAllPermissions();

    for (const permissionName of allPermissionNames) {
      const parsed = parsePermission(permissionName);
      if (!parsed) {
        throw new Error(`Invalid permission format: ${permissionName}`);
      }

      // Determine category based on resource
      let category = 'resource';
      if (['users', 'roles', 'settings'].includes(parsed.resource)) {
        category = 'administrative';
      } else if (['ai_features', 'reports'].includes(parsed.resource)) {
        category = 'feature';
      }

      await tx.permission.create({
        data: {
          tenantId,
          name: permissionName,
          resource: parsed.resource,
          action: parsed.action,
          category,
          description: `${parsed.action.charAt(0) + parsed.action.slice(1).toLowerCase()} ${parsed.resource.replace('_', ' ')}`
        }
      });
    }
  }

  private async createSubscription(tenantId: string, planId: string, tx: any) {
    // Validate that the plan exists
    const plan = await tx.subscriptionPlan.findUnique({
      where: { id: planId }
    });

    if (!plan) {
      throw new Error(`Subscription plan with ID '${planId}' not found`);
    }

    const currentDate = new Date();
    const endDate = new Date();
    endDate.setMonth(endDate.getMonth() + 1); // 1 month trial

    return await tx.subscription.create({
      data: {
        tenantId,
        planId,
        status: 'active',
        currentPeriodStart: currentDate,
        currentPeriodEnd: endDate
      }
    });
  }

  private async assignPermissionsToRoles(tenantId: string, tx: any): Promise<void> {
    // Get all permissions for this tenant
    const permissions = await tx.permission.findMany({
      where: { tenantId }
    });

    // Get all roles for this tenant
    const roles = await tx.role.findMany({
      where: { tenantId }
    });

    // Define role-permission mappings using constants
    const rolePermissions = {
      'Tenant Admin': permissions.map((p: any) => p.id), // Admin gets all permissions
      'Manager': permissions.filter((p: any) =>
        DEFAULT_ROLE_PERMISSIONS.MANAGER.includes(p.name)
      ).map((p: any) => p.id),
      'Sales Rep': permissions.filter((p: any) =>
        DEFAULT_ROLE_PERMISSIONS.SALES_REP.includes(p.name)
      ).map((p: any) => p.id),
      'Viewer': permissions.filter((p: any) =>
        DEFAULT_ROLE_PERMISSIONS.VIEWER.includes(p.name)
      ).map((p: any) => p.id)
    };

    // Assign permissions to roles
    for (const role of roles) {
      const permissionIds = rolePermissions[role.name as keyof typeof rolePermissions] || [];

      for (const permissionId of permissionIds) {
        await tx.rolePermission.create({
          data: {
            tenantId,
            roleId: role.id,
            permissionId,
            grantedBy: null // System assignment
          }
        });
      }
    }
  }

  private async createDefaultSettings(tenantId: string, tx: any): Promise<void> {
    const defaultSettings = [
      { key: 'timezone', value: 'UTC' },
      { key: 'date_format', value: 'YYYY-MM-DD' },
      { key: 'currency', value: 'USD' },
      { key: 'language', value: 'en' },
      { key: 'theme', value: 'light' },
      { key: 'notifications_enabled', value: true }
    ];

    for (const setting of defaultSettings) {
      await tx.tenantSetting.create({
        data: {
          tenantId,
          settingKey: setting.key,
          settingValue: setting.value
        }
      });
    }
  }

  private async initializeUsageTracking(tenantId: string, tx: any): Promise<void> {
    const currentDate = new Date();
    const periodStart = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);
    const periodEnd = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0);
    
    const metrics = ['contacts', 'leads', 'users', 'ai_requests', 'storage_mb'];

    for (const metric of metrics) {
      await tx.usageMetric.create({
        data: {
          tenantId,
          metricName: metric,
          metricValue: 0,
          periodStart,
          periodEnd
        }
      });
    }
  }

  // Get tenant by slug
  async getTenantBySlug(slug: string) {
    return await prisma.tenant.findUnique({
      where: { slug },
      include: {
        subscriptions: {
          where: { status: 'active' },
          include: { plan: true },
          take: 1
        }
      }
    });
  }

  // Check if slug is available
  async isSlugAvailable(slug: string): Promise<boolean> {
    const existing = await prisma.tenant.findUnique({
      where: { slug }
    });
    return !existing;
  }
}
