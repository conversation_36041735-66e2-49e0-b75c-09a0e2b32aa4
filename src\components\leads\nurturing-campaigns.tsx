'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { EmptyState } from '@/components/ui/empty-state';
import {
  PlayIcon,
  PauseIcon,
  PlusIcon,
  EnvelopeIcon,
  UsersIcon,
  ChartBarIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
  EllipsisHorizontalIcon,
  PencilIcon,
  TrashIcon,
  EyeIcon
} from '@heroicons/react/24/outline';
import { formatDistanceToNow } from 'date-fns';
import { useToast } from '@/hooks/use-toast';
import { NurturingCampaign } from '@/services/lead-nurturing';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Skeleton } from '@/components/ui/skeleton';

interface NurturingCampaignsProps {
  tenantId: string;
}

export function NurturingCampaigns({ tenantId }: NurturingCampaignsProps) {
  const [campaigns, setCampaigns] = useState<NurturingCampaign[]>([]);
  const [filteredCampaigns, setFilteredCampaigns] = useState<NurturingCampaign[]>([]);
  const [loading, setLoading] = useState(true);
  const [executing, setExecuting] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<'all' | 'active' | 'inactive'>('all');
  const { toast } = useToast();
  const router = useRouter();

  useEffect(() => {
    fetchCampaigns();
  }, [tenantId]);

  useEffect(() => {
    filterCampaigns();
  }, [campaigns, searchTerm, statusFilter]);

  const fetchCampaigns = async () => {
    try {
      const response = await fetch('/api/leads/nurturing/campaigns', {
        headers: {
          'x-tenant-id': tenantId
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch campaigns');
      }

      const data = await response.json();
      setCampaigns(data.data || []);
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to fetch nurturing campaigns',
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  };

  const filterCampaigns = () => {
    let filtered = campaigns;

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(campaign =>
        campaign.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        campaign.description?.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Filter by status
    if (statusFilter !== 'all') {
      filtered = filtered.filter(campaign =>
        statusFilter === 'active' ? campaign.isActive : !campaign.isActive
      );
    }

    setFilteredCampaigns(filtered);
  };

  const executeCampaign = async (campaignId: string, dryRun: boolean = false) => {
    setExecuting(campaignId);
    try {
      const response = await fetch(`/api/leads/nurturing/campaigns/${campaignId}/execute`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-tenant-id': tenantId
        },
        body: JSON.stringify({ dryRun })
      });

      if (!response.ok) {
        throw new Error('Failed to execute campaign');
      }

      const data = await response.json();
      const result = data.data;

      toast({
        title: dryRun ? 'Dry Run Complete' : 'Campaign Executed',
        description: `Processed ${result.processed} leads, sent ${result.emailsSent} emails${result.errors.length > 0 ? `, ${result.errors.length} errors` : ''}`,
        variant: result.errors.length > 0 ? 'destructive' : 'default'
      });

      if (!dryRun) {
        fetchCampaigns(); // Refresh campaigns to update stats
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to execute campaign',
        variant: 'destructive'
      });
    } finally {
      setExecuting(null);
    }
  };

  const handleViewCampaign = (campaignId: string) => {
    router.push(`/leads/nurturing/${campaignId}`);
  };

  const handleEditCampaign = (campaignId: string) => {
    router.push(`/leads/nurturing/${campaignId}/edit`);
  };

  const handleDeleteCampaign = async (campaignId: string) => {
    if (!confirm('Are you sure you want to delete this campaign? This action cannot be undone.')) {
      return;
    }

    try {
      const response = await fetch(`/api/leads/nurturing/campaigns/${campaignId}`, {
        method: 'DELETE',
        headers: {
          'x-tenant-id': tenantId
        }
      });

      if (!response.ok) {
        throw new Error('Failed to delete campaign');
      }

      toast({
        title: 'Success',
        description: 'Campaign deleted successfully',
      });

      fetchCampaigns(); // Refresh the list
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to delete campaign',
        variant: 'destructive'
      });
    }
  };

  const CampaignSkeleton = () => (
    <Card>
      <CardHeader>
        <div className="flex items-start justify-between">
          <div className="space-y-2 flex-1">
            <Skeleton className="h-5 w-3/4" />
            <Skeleton className="h-4 w-1/2" />
          </div>
          <Skeleton className="h-6 w-16" />
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          {[1, 2, 3, 4].map((i) => (
            <div key={i} className="flex items-center space-x-2">
              <Skeleton className="h-4 w-4" />
              <Skeleton className="h-4 w-12" />
            </div>
          ))}
        </div>
        <div className="border-t pt-4 space-y-2">
          <Skeleton className="h-4 w-32" />
          <div className="flex gap-1">
            <Skeleton className="h-5 w-16" />
            <Skeleton className="h-5 w-20" />
          </div>
        </div>
        <div className="flex space-x-2 pt-2">
          <Skeleton className="h-8 w-20" />
          <Skeleton className="h-8 w-20" />
        </div>
        <Skeleton className="h-3 w-24" />
      </CardContent>
    </Card>
  );

  if (loading) {
    return (
      <div className="space-y-6">
        {/* Search and Filter Section */}
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="relative flex-1">
            <Skeleton className="h-10 w-full" />
          </div>
          <div className="flex gap-2">
            <Skeleton className="h-10 w-24" />
            <Skeleton className="h-10 w-10" />
          </div>
        </div>

        {/* Campaign Grid */}
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {[1, 2, 3, 4, 5, 6].map((i) => (
            <CampaignSkeleton key={i} />
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Search and Filter Section */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search campaigns..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        <div className="flex gap-2">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm">
                <FunnelIcon className="h-4 w-4 mr-2" />
                {statusFilter === 'all' ? 'All Status' : statusFilter === 'active' ? 'Active' : 'Inactive'}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => setStatusFilter('all')}>
                All Status
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setStatusFilter('active')}>
                Active
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setStatusFilter('inactive')}>
                Inactive
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
          <Button onClick={() => router.push('/leads/nurturing/new')} size="sm">
            <PlusIcon className="h-4 w-4 mr-2" />
            Create
          </Button>
        </div>
      </div>

      {filteredCampaigns.length === 0 ? (
        <Card>
          <CardContent>
            <EmptyState
              icon={<EnvelopeIcon className="w-12 h-12" />}
              title={campaigns.length === 0 ? "No campaigns yet" : "No campaigns found"}
              description={
                campaigns.length === 0
                  ? "Create your first nurturing campaign to start automating lead engagement"
                  : "Try adjusting your search or filter criteria"
              }
              action={
                campaigns.length === 0
                  ? {
                      label: 'Create Your First Campaign',
                      onClick: () => router.push('/leads/nurturing/new'),
                      variant: 'primary'
                    }
                  : {
                      label: 'Clear Filters',
                      onClick: () => {
                        setSearchTerm('');
                        setStatusFilter('all');
                      },
                      variant: 'outline'
                    }
              }
            />
          </CardContent>
        </Card>
      ) : (
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {filteredCampaigns.map((campaign) => (
            <Card key={campaign.id} className="relative hover:shadow-md transition-shadow">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="flex-1 min-w-0">
                    <CardTitle className="text-lg truncate">{campaign.name}</CardTitle>
                    <CardDescription className="mt-1 line-clamp-2">
                      {campaign.description || 'No description'}
                    </CardDescription>
                  </div>
                  <div className="flex items-center gap-2 ml-2">
                    <Badge variant={campaign.isActive ? 'default' : 'secondary'}>
                      {campaign.isActive ? 'Active' : 'Inactive'}
                    </Badge>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                          <EllipsisHorizontalIcon className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => handleViewCampaign(campaign.id)}>
                          <EyeIcon className="h-4 w-4 mr-2" />
                          View Details
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleEditCampaign(campaign.id)}>
                          <PencilIcon className="h-4 w-4 mr-2" />
                          Edit Campaign
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => handleDeleteCampaign(campaign.id)}
                          className="text-destructive"
                        >
                          <TrashIcon className="h-4 w-4 mr-2" />
                          Delete Campaign
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Campaign Stats */}
                <div className="space-y-3">
                  {/* Progress Bar */}
                  {campaign.stats.totalLeads > 0 && (
                    <div className="space-y-1">
                      <div className="flex items-center justify-between text-xs text-muted-foreground">
                        <span>Progress</span>
                        <span>{campaign.stats.processedLeads}/{campaign.stats.totalLeads} leads</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-1.5">
                        <div
                          className="bg-blue-600 h-1.5 rounded-full transition-all duration-300"
                          style={{
                            width: `${Math.min(100, (campaign.stats.processedLeads / campaign.stats.totalLeads) * 100)}%`
                          }}
                        ></div>
                      </div>
                    </div>
                  )}

                  {/* Stats Grid */}
                  <div className="grid grid-cols-2 gap-3 text-sm">
                    <div className="flex items-center space-x-2">
                      <UsersIcon className="h-4 w-4 text-muted-foreground" />
                      <span>{campaign.stats.totalLeads} eligible</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <EnvelopeIcon className="h-4 w-4 text-muted-foreground" />
                      <span>{campaign.stats.emailsSent} sent</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <ChartBarIcon className="h-4 w-4 text-muted-foreground" />
                      <span>{campaign.stats.responses} replies</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className="text-green-600">✓</span>
                      <span>{campaign.stats.conversions} converted</span>
                    </div>
                  </div>
                </div>

                {/* Campaign Details */}
                <div className="border-t pt-4 space-y-3">
                  {/* Target Criteria */}
                  <div>
                    <p className="text-sm text-muted-foreground mb-1">Target Status</p>
                    <div className="flex flex-wrap gap-1">
                      {campaign.targetStatus.map((status) => (
                        <Badge key={status} variant="outline" className="text-xs">
                          {status}
                        </Badge>
                      ))}
                    </div>
                  </div>

                  {/* Email Sequence */}
                  <div>
                    <p className="text-sm text-muted-foreground mb-1">
                      {campaign.emailSequence.length} email{campaign.emailSequence.length !== 1 ? 's' : ''} in sequence
                    </p>
                    <div className="flex flex-wrap gap-1">
                      {campaign.emailSequence.slice(0, 2).map((step, index) => (
                        <Badge key={index} variant="secondary" className="text-xs">
                          {step.emailType.replace('_', ' ')}
                        </Badge>
                      ))}
                      {campaign.emailSequence.length > 2 && (
                        <Badge variant="secondary" className="text-xs">
                          +{campaign.emailSequence.length - 2} more
                        </Badge>
                      )}
                    </div>
                  </div>
                </div>

                {/* Actions */}
                <div className="flex space-x-2 pt-2">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => executeCampaign(campaign.id, true)}
                    disabled={executing === campaign.id}
                  >
                    Test Run
                  </Button>
                  <Button
                    size="sm"
                    onClick={() => executeCampaign(campaign.id, false)}
                    disabled={executing === campaign.id || !campaign.isActive}
                  >
                    {executing === campaign.id ? (
                      <>
                        <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-white mr-2"></div>
                        Running...
                      </>
                    ) : (
                      <>
                        <PlayIcon className="h-3 w-3 mr-1" />
                        Execute
                      </>
                    )}
                  </Button>
                </div>

                {/* Last Updated */}
                <p className="text-xs text-muted-foreground">
                  Updated {formatDistanceToNow(new Date(campaign.updatedAt), { addSuffix: true })}
                </p>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}
