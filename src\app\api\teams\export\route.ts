import { NextResponse } from 'next/server';
import { TeamManagementService } from '@/services/team-management';
import { withPermission, AuthenticatedRequest } from '@/lib/permission-middleware';
import { PermissionResource, PermissionAction } from '@/types';
import { z } from 'zod';

const teamService = new TeamManagementService();

// Validation schema for export parameters
const exportQuerySchema = z.object({
  format: z.enum(['csv', 'json']).default('csv'),
  includeMembers: z.string().transform(val => val === 'true').default(false),
  includeInactive: z.string().transform(val => val === 'true').default(false),
});

/**
 * GET /api/teams/export
 * Export teams to CSV or JSON format
 */
export const GET = withPermission({
  resource: PermissionResource.USERS,
  action: PermissionAction.READ,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest) => {
  try {
    const { searchParams } = new URL(req.url);
    const queryParams = Object.fromEntries(searchParams.entries());
    
    const validatedQuery = exportQuerySchema.parse(queryParams);

    console.log('Export filters:', validatedQuery);

    const teams = await teamService.getTeams(req.tenantId, {
      includeInactive: validatedQuery.includeInactive,
      includeMembers: validatedQuery.includeMembers
    });

    console.log(`Exporting ${teams.length} teams`);

    if (validatedQuery.format === 'json') {
      return NextResponse.json({
        success: true,
        data: teams,
        total: teams.length,
        exported: new Date().toISOString()
      });
    }

    // Generate CSV
    const headers = validatedQuery.includeMembers 
      ? ['ID', 'Name', 'Description', 'Color', 'Status', 'Member Count', 'Members', 'Created At', 'Updated At']
      : ['ID', 'Name', 'Description', 'Color', 'Status', 'Member Count', 'Created At', 'Updated At'];

    const csvRows = [
      headers.join(','),
      ...teams.map(team => {
        const baseRow = [
          `"${team.id}"`,
          `"${(team.name || '').replace(/"/g, '""')}"`,
          `"${(team.description || '').replace(/"/g, '""')}"`,
          `"${(team.color || '').replace(/"/g, '""')}"`,
          `"${team.status}"`,
          team.memberCount || 0,
          `"${team.createdAt.toISOString()}"`,
          `"${team.updatedAt.toISOString()}"`
        ];

        if (validatedQuery.includeMembers) {
          const members = team.members ? 
            team.members.map(member => 
              `${member.firstName || ''} ${member.lastName || ''} (${member.email})`
            ).join('; ') : '';
          baseRow.splice(-2, 0, `"${members.replace(/"/g, '""')}"`);
        }

        return baseRow.join(',');
      })
    ];

    // Add UTF-8 BOM for proper Arabic character support
    const BOM = '\uFEFF';
    const csvContent = BOM + csvRows.join('\n');
    const filename = `teams-export-${new Date().toISOString().split('T')[0]}.csv`;

    return new NextResponse(csvContent, {
      status: 200,
      headers: {
        'Content-Type': 'text/csv; charset=utf-8',
        'Content-Disposition': `attachment; filename="${filename}"`,
        'Cache-Control': 'no-cache',
      },
    });

  } catch (error) {
    console.error('Export teams error:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid query parameters', details: error.errors },
        { status: 400 }
      );
    }

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
});
