import type { AppProps } from 'next/app';
import { SessionProvider } from 'next-auth/react';
import { ThemeProvider } from '../components/providers/theme-provider';
import { LocaleProvider } from '../components/providers/locale-provider';
import { TenantProvider } from '../components/providers/tenant-provider';
import { PermissionProvider } from '../components/providers/permission-provider';
import { EnhancedPermissionProvider } from '../components/providers/enhanced-permission-provider';
import { StatePersistenceProvider } from '../components/providers/state-persistence-provider';
import '../app/globals.css';

export default function App({
  Component,
  pageProps: { session, ...pageProps },
}: AppProps) {
  return (
    <SessionProvider session={session}>
      <ThemeProvider>
        <LocaleProvider>
          <TenantProvider>
            <PermissionProvider>
              <EnhancedPermissionProvider
                enableCache={true}
                enableRealTimeUpdates={true}
                cacheOptions={{
                  ttl: 3600000, // 1 hour
                  encrypt: true,
                  validateIntegrity: true
                }}
              >
                <StatePersistenceProvider>
                  <Component {...pageProps} />
                </StatePersistenceProvider>
              </EnhancedPermissionProvider>
            </PermissionProvider>
          </TenantProvider>
        </LocaleProvider>
      </ThemeProvider>
    </SessionProvider>
  );
}
