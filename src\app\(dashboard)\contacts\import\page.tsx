'use client';

import React, { useState, useRef } from 'react';
import { useRouter } from 'next/navigation';
import { ArrowLeft, Upload, FileText, AlertCircle, CheckCircle, Download, Eye } from 'lucide-react';
import { PermissionResource, PermissionAction } from '@/types';
import { PageLayout } from '@/components/layout/page-layout';
import { PermissionGate } from '@/components/ui/permission-gate';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { EmptyState } from '@/components/ui/empty-state';

interface ImportResult {
  success: boolean;
  imported: number;
  errors: Array<{
    row: number;
    field: string;
    message: string;
  }>;
}

export default function ContactImportPage() {
  const router = useRouter();
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  const [file, setFile] = useState<File | null>(null);
  const [importing, setImporting] = useState(false);
  const [result, setResult] = useState<ImportResult | null>(null);
  const [step, setStep] = useState<'upload' | 'preview' | 'result'>('upload');
  const [previewData, setPreviewData] = useState<Record<string, unknown>[]>([]);

  // Handle file selection
  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = event.target.files?.[0];
    if (selectedFile) {
      if (selectedFile.type !== 'text/csv' && !selectedFile.name.endsWith('.csv')) {
        alert('Please select a CSV file');
        return;
      }
      setFile(selectedFile);
      parseCSVPreview(selectedFile);
    }
  };

  // Parse CSV for preview
  const parseCSVPreview = (file: File) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      const text = e.target?.result as string;
      const lines = text.split('\n').filter(line => line.trim());
      
      if (lines.length < 2) {
        alert('CSV file must have at least a header row and one data row');
        return;
      }

      const headers = lines[0].split(',').map(h => h.trim().replace(/"/g, ''));
      const preview = lines.slice(1, 6).map((line, index) => {
        const values = line.split(',').map(v => v.trim().replace(/"/g, ''));
        const row: Record<string, unknown> = { _rowNumber: index + 2 };
        headers.forEach((header, i) => {
          row[header] = values[i] || '';
        });
        return row;
      });

      setPreviewData(preview);
      setStep('preview');
    };
    reader.readAsText(file);
  };

  // Handle import
  const handleImport = async () => {
    if (!file) return;

    setImporting(true);
    const formData = new FormData();
    formData.append('file', file);

    try {
      const response = await fetch('/api/contacts/import', {
        method: 'POST',
        body: formData,
      });

      const data = await response.json();
      setResult(data);
      setStep('result');
    } catch (err) {
      console.error('Import error:', err);
      alert('Failed to import contacts. Please try again.');
    } finally {
      setImporting(false);
    }
  };

  // Download sample CSV
  const downloadSample = () => {
    const sampleData = [
      ['firstName', 'lastName', 'email', 'phone', 'company'],
      ['John', 'Doe', '<EMAIL>', '******-0123', 'Acme Corp'],
      ['Jane', 'Smith', '<EMAIL>', '******-0124', 'Tech Solutions'],
      ['Bob', 'Johnson', '<EMAIL>', '', 'StartupXYZ'],
    ];

    try {
      const { downloadCSV } = require('@/lib/download-utils');
      downloadCSV(sampleData, 'contacts-sample.csv');
    } catch (error) {
      console.error('Error downloading sample:', error);
      // Fallback to manual download with UTF-8 BOM for Arabic support
      const BOM = '\uFEFF';
      const csvContent = BOM + sampleData.map(row => row.join(',')).join('\n');
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      if (typeof window !== 'undefined') {
        const url = window.URL.createObjectURL(blob);
        const a = window.document.createElement('a');
        a.href = url;
        a.download = 'contacts-sample.csv';
        window.document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        window.document.body.removeChild(a);
      }
    }
  };

  const actions = (
    <Button variant="outline" size="sm" onClick={() => router.push('/contacts')}>
      <ArrowLeft className="h-4 w-4 mr-2" />
      Back to Contacts
    </Button>
  );

  return (
    <PermissionGate
      resource={PermissionResource.CONTACTS}
      action={PermissionAction.CREATE}
      fallback={
        <PageLayout>
          <Card>
            <CardContent className="p-8">
              <EmptyState
                icon={<AlertCircle className="w-12 h-12 text-muted-foreground" />}
                title="Access Denied"
                description="You don't have permission to import contacts. Please contact your administrator for access."
                action={{
                  label: 'Back to Contacts',
                  onClick: () => router.push('/contacts'),
                  variant: 'primary'
                }}
              />
            </CardContent>
          </Card>
        </PageLayout>
      }
    >
      <PageLayout
        title="Import Contacts"
        description="Upload a CSV file to import multiple contacts at once"
        actions={actions}
      >

      {/* Progress Steps */}
      <Card className="mb-6">
        <CardContent>
          <nav aria-label="Progress">
            <ol className="flex items-center justify-center">
              <li className={`relative ${step === 'upload' ? 'text-primary-600' : step === 'preview' || step === 'result' ? 'text-green-600' : 'text-gray-500'}`}>
                <div className="flex flex-col items-center">
                  <span className={`w-10 h-10 rounded-full flex items-center justify-center text-sm font-semibold border-2 ${
                    step === 'upload'
                      ? 'bg-primary-100 border-primary-600 text-primary-600'
                      : step === 'preview' || step === 'result'
                        ? 'bg-green-100 border-green-600 text-green-600'
                        : 'bg-gray-100 border-gray-300 text-gray-500'
                  }`}>
                    {step === 'preview' || step === 'result' ? <CheckCircle className="w-5 h-5" /> : '1'}
                  </span>
                  <span className="mt-2 text-sm font-medium">Upload File</span>
                </div>
              </li>

              <div className="flex-1 h-0.5 bg-gray-200 mx-4 mt-5">
                <div className={`h-full transition-all duration-300 ${step === 'preview' || step === 'result' ? 'bg-green-500' : 'bg-gray-200'}`} />
              </div>

              <li className={`relative ${step === 'preview' ? 'text-primary-600' : step === 'result' ? 'text-green-600' : 'text-gray-500'}`}>
                <div className="flex flex-col items-center">
                  <span className={`w-10 h-10 rounded-full flex items-center justify-center text-sm font-semibold border-2 ${
                    step === 'preview'
                      ? 'bg-primary-100 border-primary-600 text-primary-600'
                      : step === 'result'
                        ? 'bg-green-100 border-green-600 text-green-600'
                        : 'bg-gray-100 border-gray-300 text-gray-500'
                  }`}>
                    {step === 'result' ? <CheckCircle className="w-5 h-5" /> : '2'}
                  </span>
                  <span className="mt-2 text-sm font-medium">Preview Data</span>
                </div>
              </li>

              <div className="flex-1 h-0.5 bg-gray-200 mx-4 mt-5">
                <div className={`h-full transition-all duration-300 ${step === 'result' ? 'bg-green-500' : 'bg-gray-200'}`} />
              </div>

              <li className={`relative ${step === 'result' ? 'text-primary-600' : 'text-gray-500'}`}>
                <div className="flex flex-col items-center">
                  <span className={`w-10 h-10 rounded-full flex items-center justify-center text-sm font-semibold border-2 ${
                    step === 'result'
                      ? 'bg-primary-100 border-primary-600 text-primary-600'
                      : 'bg-gray-100 border-gray-300 text-gray-500'
                  }`}>
                    3
                  </span>
                  <span className="mt-2 text-sm font-medium">Import Results</span>
                </div>
              </li>
            </ol>
          </nav>
        </CardContent>
      </Card>

      {/* Upload Step */}
      {step === 'upload' && (
        <Card>
          <CardHeader>
            <div className="text-center">
              <Upload className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <CardTitle>Upload CSV File</CardTitle>
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-center">
              <p className="text-gray-600 dark:text-gray-400 mb-6">
                Select a CSV file containing your contact data. The file should include columns for firstName, lastName, email, phone, and company.
              </p>

              <div className="mb-6">
                <Button
                  variant="outline"
                  onClick={downloadSample}
                >
                  <Download className="w-4 h-4 mr-2" />
                  Download Sample CSV
                </Button>
              </div>

              <div className="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-8 hover:border-primary-400 transition-colors">
                <input
                  ref={fileInputRef}
                  type="file"
                  accept=".csv"
                  onChange={handleFileSelect}
                  className="hidden"
                />
                <Button
                  variant="default"
                  size="lg"
                  onClick={() => fileInputRef.current?.click()}
                >
                  <FileText className="w-5 h-5 mr-2" />
                  Choose CSV File
                </Button>
                <p className="mt-3 text-sm text-gray-500 dark:text-gray-400">
                  or drag and drop your CSV file here
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Preview Step */}
      {step === 'preview' && (
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="flex items-center">
                  <Eye className="w-5 h-5 mr-2" />
                  Preview Import Data
                </CardTitle>
                <p className="text-gray-600 dark:text-gray-400 mt-1">Review the first few rows of your data before importing.</p>
              </div>
              <div className="flex space-x-3">
                <Button
                  variant="outline"
                  onClick={() => setStep('upload')}
                >
                  Back
                </Button>
                <Button
                  variant="default"
                  onClick={handleImport}
                  loading={importing}
                >
                  Import Contacts
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            {previewData && previewData.length > 0 && (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                  <thead className="bg-gray-50 dark:bg-gray-800">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Row
                      </th>
                      {previewData[0] && Object.keys(previewData[0]).filter(key => key !== '_rowNumber').map(key => (
                        <th key={key} className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                          {key}
                        </th>
                      ))}
                    </tr>
                  </thead>
                  <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    {previewData.map((row, index) => (
                      <tr key={index} className="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400 font-medium">
                          {row?._rowNumber || index + 1}
                        </td>
                        {row && Object.entries(row).filter(([key]) => key !== '_rowNumber').map(([key, value]) => (
                          <td key={key} className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                            {String(value || '')}
                          </td>
                        ))}
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Result Step */}
      {step === 'result' && result && (
        <Card>
          <CardContent>
            <div className="text-center">
              {result.success ? (
                <div className="w-16 h-16 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center mx-auto mb-4">
                  <CheckCircle className="w-8 h-8 text-green-600 dark:text-green-400" />
                </div>
              ) : (
                <div className="w-16 h-16 bg-red-100 dark:bg-red-900/20 rounded-full flex items-center justify-center mx-auto mb-4">
                  <AlertCircle className="w-8 h-8 text-red-600 dark:text-red-400" />
                </div>
              )}

              <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                Import {result.success ? 'Completed' : 'Failed'}
              </h3>

              <p className="text-gray-600 dark:text-gray-400 mb-6">
                {result.success
                  ? `Successfully imported ${result.imported} contacts.`
                  : 'Import failed with errors. Please review and try again.'
                }
              </p>

              {result.errors && result.errors.length > 0 && (
                <div className="mb-6">
                  <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2">Errors:</h4>
                  <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-4 text-left">
                    <ul className="text-sm text-red-700 dark:text-red-300 space-y-1">
                      {result.errors.slice(0, 10).map((error, index) => (
                        <li key={index}>
                          Row {error.row}: {error.field} - {error.message}
                        </li>
                      ))}
                      {result.errors.length > 10 && (
                        <li>... and {result.errors.length - 10} more errors</li>
                      )}
                    </ul>
                  </div>
                </div>
              )}

              <div className="flex justify-center space-x-3">
                <Button
                  variant="outline"
                  onClick={() => {
                    setStep('upload');
                    setFile(null);
                    setResult(null);
                    setPreviewData([]);
                  }}
                >
                  Import More
                </Button>
                <Button
                  variant="default"
                  onClick={() => router.push('/contacts')}
                >
                  View Contacts
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
      </PageLayout>
    </PermissionGate>
  );
}
