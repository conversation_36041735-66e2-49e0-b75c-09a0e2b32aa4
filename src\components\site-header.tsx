"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  B<PERSON>crumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu"
import { Separator } from "@/components/ui/separator"
import {
  SidebarTrigger,
} from "@/components/ui/sidebar"
import { usePathname, useRouter } from 'next/navigation'
import { useMemo } from 'react'
import {
  PlusIcon,
  ChevronDownIcon,
  UserIcon,
  BuildingOfficeIcon,
  UserGroupIcon,
  ChartBarIcon
} from '@heroicons/react/24/outline'
import { SmoothLink } from '@/components/ui/smooth-link'
import { NotificationBell } from '@/components/communication'

export function SiteHeader() {
  const pathname = usePathname()
  const router = useRouter()

  const quickCreateOptions = [
    {
      name: 'Contact',
      description: 'Create a new contact',
      href: '/contacts/new',
      icon: UserIcon,
      color: 'text-blue-600 dark:text-blue-400',
      bgColor: 'bg-blue-50 dark:bg-blue-900/20',
    },
    {
      name: 'Company',
      description: 'Create a new company',
      href: '/companies/new',
      icon: BuildingOfficeIcon,
      color: 'text-green-600 dark:text-green-400',
      bgColor: 'bg-green-50 dark:bg-green-900/20',
    },
    {
      name: 'Lead',
      description: 'Create a new lead',
      href: '/leads/new',
      icon: UserGroupIcon,
      color: 'text-purple-600 dark:text-purple-400',
      bgColor: 'bg-purple-50 dark:bg-purple-900/20',
    },
    {
      name: 'Opportunity',
      description: 'Create a new opportunity',
      href: '/opportunities/new',
      icon: ChartBarIcon,
      color: 'text-orange-600 dark:text-orange-400',
      bgColor: 'bg-orange-50 dark:bg-orange-900/20',
    },
  ]

  const handleQuickCreate = (href: string) => {
    router.push(href)
  }

  const breadcrumbs = useMemo(() => {
    const segments = pathname.split('/').filter(Boolean)
    const breadcrumbItems = []

    // Add Dashboard as root
    breadcrumbItems.push({
      label: 'Dashboard',
      href: '/dashboard',
      isLast: segments.length === 1 && segments[0] === 'dashboard'
    })

    // Add other segments
    let currentPath = ''
    segments.forEach((segment, index) => {
      if (segment === 'dashboard') return // Skip dashboard as it's already added

      currentPath += `/${segment}`
      const isLast = index === segments.length - 1

      // Format segment name
      const label = segment
        .split('-')
        .map(word => word.charAt(0).toUpperCase() + word.slice(1))
        .join(' ')

      breadcrumbItems.push({
        label,
        href: currentPath,
        isLast
      })
    })

    return breadcrumbItems
  }, [pathname])

  return (
    <header className="flex h-16 shrink-0 items-center gap-2 transition-[width,height] ease-linear group-has-data-[collapsible=icon]/sidebar-wrapper:h-12">
      <div className="flex items-center gap-2 px-4">
        <SidebarTrigger className="-ml-1" />
        <Separator orientation="vertical" className="mr-2 h-4" />
        <Breadcrumb>
          <BreadcrumbList>
            {breadcrumbs.map((breadcrumb, index) => (
              <div key={breadcrumb.href} className="flex items-center">
                {index > 0 && <BreadcrumbSeparator className="hidden md:block" />}
                <BreadcrumbItem className={index === 0 ? "hidden md:block" : ""}>
                  {breadcrumb.isLast ? (
                    <BreadcrumbPage>{breadcrumb.label}</BreadcrumbPage>
                  ) : (
                    <BreadcrumbLink asChild>
                      <SmoothLink href={breadcrumb.href}>
                        {breadcrumb.label}
                      </SmoothLink>
                    </BreadcrumbLink>
                  )}
                </BreadcrumbItem>
              </div>
            ))}
          </BreadcrumbList>
        </Breadcrumb>
      </div>
      <div className="ml-auto px-3 flex items-center space-x-2">
        <NotificationBell />
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button size="sm" className="gap-1.5">
              <PlusIcon className="h-4 w-4" />
              Quick Create
              <ChevronDownIcon className="h-3 w-3 opacity-50" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-56">
            {quickCreateOptions.map((option, index) => (
              <div key={option.name}>
                <DropdownMenuItem
                  onClick={() => handleQuickCreate(option.href)}
                  className="flex items-center gap-3 p-3 cursor-pointer"
                >
                  <div className={`flex-shrink-0 p-2 rounded-md ${option.bgColor}`}>
                    <option.icon className={`h-4 w-4 ${option.color}`} />
                  </div>
                  <div className="flex-1">
                    <div className="font-medium text-sm">
                      {option.name}
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {option.description}
                    </div>
                  </div>
                </DropdownMenuItem>
                {index < quickCreateOptions.length - 1 && <DropdownMenuSeparator />}
              </div>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </header>
  )
}
