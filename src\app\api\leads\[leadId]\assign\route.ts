import { NextResponse } from 'next/server';
import { withPermission } from '@/lib/permission-middleware';
import { leadManagementService } from '@/services/lead-management';
import { PermissionAction, PermissionResource } from '@/types';
import { AuthenticatedRequest } from '@/lib/permission-middleware';
import { z } from 'zod';

const assignLeadSchema = z.object({
  ownerId: z.string().min(1, 'Owner ID is required')
});

/**
 * PUT /api/leads/[leadId]/assign
 * Assign a lead to a user
 */
export const PUT = withPermission({
  resource: PermissionResource.LEADS,
  action: PermissionAction.UPDATE,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest) => {
  try {
    const url = new URL(req.url);
    const pathSegments = url.pathname.split('/');
    const leadId = pathSegments[pathSegments.indexOf('leads') + 1];

    const body = await req.json();
    const { ownerId } = assignLeadSchema.parse(body);

    const lead = await leadManagementService.assignLead(
      leadId,
      req.tenantId,
      ownerId,
      req.userId
    );

    return NextResponse.json({
      success: true,
      data: { lead },
      message: 'Lead assigned successfully'
    });

  } catch (error) {
    console.error('Assign lead API error:', error);
    
    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      { error: 'Failed to assign lead' },
      { status: 500 }
    );
  }
});
