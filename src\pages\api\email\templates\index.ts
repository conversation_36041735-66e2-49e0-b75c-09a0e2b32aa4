import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../../../../lib/auth';
import { EmailTemplateService } from '../../../../services/communication';
import { withTenant } from '../../../../lib/middleware/withTenant';
import { withRateLimit } from '../../../../lib/middleware/withRateLimit';

async function handler(req: NextApiRequest, res: NextApiResponse) {
  const session = await getServerSession(req, res, authOptions);
  
  if (!session?.user?.id) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  const tenantId = req.headers['x-tenant-id'] as string;
  const userId = session.user.id;

  try {
    switch (req.method) {
      case 'GET':
        const { category, type, locale, isActive } = req.query;
        const templates = await EmailTemplateService.getTemplates(
          tenantId,
          {
            category: category as string,
            type: type as string,
            locale: locale as string,
            isActive: isActive ? isActive === 'true' : undefined,
          }
        );
        return res.status(200).json(templates);

      case 'POST':
        const newTemplate = await EmailTemplateService.createTemplate(
          tenantId,
          userId,
          req.body
        );
        return res.status(201).json(newTemplate);

      default:
        res.setHeader('Allow', ['GET', 'POST']);
        return res.status(405).json({ error: `Method ${req.method} not allowed` });
    }
  } catch (error) {
    console.error('Email templates API error:', error);
    return res.status(500).json({ 
      error: error instanceof Error ? error.message : 'Internal server error' 
    });
  }
}

export default withRateLimit(withTenant(handler));
