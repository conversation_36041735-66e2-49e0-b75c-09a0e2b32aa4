import { NextResponse } from 'next/server';
import { z } from 'zod';
import { withPermission, AuthenticatedRequest } from '@/lib/permission-middleware';
import { roleManagementService } from '@/services/role-management';
import { PermissionAction, PermissionResource } from '@/types';

interface RouteParams {
  params: Promise<{ id: string }>;
}

const getUsersQuerySchema = z.object({
  page: z.string().optional().transform(val => val ? parseInt(val, 10) : 1),
  limit: z.string().optional().transform(val => val ? parseInt(val, 10) : 20),
  search: z.string().optional(),
  includeExpired: z.string().optional().transform(val => val === 'true')
});

/**
 * GET /api/roles/[id]/users
 * Get users assigned to a specific role
 */
export const GET = withPermission({
  resource: PermissionResource.ROLES,
  action: PermissionAction.READ,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest, { params }: RouteParams) => {
  try {
    const { id: roleId } = await params;
    const { searchParams } = new URL(req.url);
    const queryParams = Object.fromEntries(searchParams.entries());
    
    const validatedQuery = getUsersQuerySchema.parse(queryParams);

    const result = await roleManagementService.getRoleUsers(roleId, req.tenantId, {
      page: validatedQuery.page,
      limit: validatedQuery.limit,
      search: validatedQuery.search,
      includeExpired: validatedQuery.includeExpired
    });

    return NextResponse.json({
      success: true,
      data: {
        roleId,
        users: result.users,
        pagination: result.pagination
      }
    });
  } catch (error) {
    console.error('Get role users error:', error);
    console.error('Error details:', {
      message: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
      roleId,
      tenantId: req.tenantId
    });

    if (error instanceof Error && error.message.includes('not found')) {
      return NextResponse.json(
        { error: 'Role not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to fetch role users', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
});

/**
 * DELETE /api/roles/[id]/users
 * Remove a user from a role
 */
export const DELETE = withPermission({
  resource: PermissionResource.USERS,
  action: PermissionAction.MANAGE,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest, { params }: RouteParams) => {
  try {
    const { id: roleId } = await params;
    const body = await req.json();
    const { userId } = body;

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      );
    }

    await roleManagementService.removeRoleFromUser(
      req.tenantId,
      userId,
      roleId,
      req.userId
    );

    return NextResponse.json({
      success: true,
      message: 'User removed from role successfully'
    });
  } catch (error) {
    console.error('Remove user from role error:', error);
    
    if (error instanceof Error && error.message.includes('not found')) {
      return NextResponse.json(
        { error: 'Role or user not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to remove user from role' },
      { status: 500 }
    );
  }
});
