import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { withTenantContext } from '@/lib/tenant-context';
import { SubscriptionManagementService } from '@/services/subscription-management';
import { z } from 'zod';

/**
 * @swagger
 * /api/subscriptions/usage:
 *   get:
 *     summary: Get tenant usage metrics
 *     description: Retrieve current usage metrics and limits for the tenant
 *     tags:
 *       - Usage Tracking
 *     security:
 *       - bearerAuth: []
 *       - tenantHeader: []
 *     parameters:
 *       - in: query
 *         name: metric
 *         schema:
 *           type: string
 *         description: Specific metric to check (optional)
 *     responses:
 *       200:
 *         description: Usage metrics retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/ApiResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         usage:
 *                           type: object
 *                           additionalProperties:
 *                             type: object
 *                             properties:
 *                               current:
 *                                 type: number
 *                               limit:
 *                                 type: number
 *                               isExceeded:
 *                                 type: boolean
 *                               percentage:
 *                                 type: number
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
export const GET = withTenantContext(async (request: NextRequest) => {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const tenantId = (request as any).tenantId;
    const { searchParams } = new URL(request.url);
    const specificMetric = searchParams.get('metric');

    const subscriptionService = new SubscriptionManagementService();

    if (specificMetric) {
      // Get specific metric usage
      const usage = await subscriptionService.checkUsageLimit(tenantId, specificMetric);
      
      return NextResponse.json({
        success: true,
        data: {
          metric: specificMetric,
          usage
        }
      });
    } else {
      // Get all usage metrics
      const subscription = await subscriptionService.getSubscriptionWithUsage(tenantId);
      
      if (!subscription) {
        return NextResponse.json(
          { error: 'No subscription found' },
          { status: 404 }
        );
      }

      const metrics = ['contacts', 'leads', 'users', 'ai_requests', 'storage_mb'];
      const usageData: Record<string, any> = {};

      for (const metric of metrics) {
        try {
          usageData[metric] = await subscriptionService.checkUsageLimit(tenantId, metric);
        } catch (error) {
          console.error(`Error checking usage for ${metric}:`, error);
          usageData[metric] = {
            current: 0,
            limit: (subscription as any).plan?.limits?.[metric] || 0,
            isExceeded: false,
            percentage: 0
          };
        }
      }

      return NextResponse.json({
        success: true,
        data: {
          usage: usageData,
          plan: (subscription as any).plan ? {
            name: (subscription as any).plan.name,
            limits: (subscription as any).plan.limits
          } : null
        }
      });
    }

  } catch (error) {
    console.error('Get usage error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
});

const trackUsageSchema = z.object({
  metric: z.string().min(1, 'Metric name is required'),
  increment: z.number().min(1).default(1)
});

/**
 * @swagger
 * /api/subscriptions/usage:
 *   post:
 *     summary: Track usage for a metric
 *     description: Increment usage counter for a specific metric
 *     tags:
 *       - Usage Tracking
 *     security:
 *       - bearerAuth: []
 *       - tenantHeader: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - metric
 *             properties:
 *               metric:
 *                 type: string
 *                 description: Name of the metric to track
 *                 example: contacts
 *               increment:
 *                 type: number
 *                 minimum: 1
 *                 default: 1
 *                 description: Amount to increment the metric by
 *     responses:
 *       200:
 *         description: Usage tracked successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/ApiResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         metric:
 *                           type: string
 *                         newUsage:
 *                           type: object
 *                           properties:
 *                             current:
 *                               type: number
 *                             limit:
 *                               type: number
 *                             isExceeded:
 *                               type: boolean
 *                             percentage:
 *                               type: number
 *       400:
 *         description: Validation error or usage limit exceeded
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
export const POST = withTenantContext(async (request: NextRequest) => {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const tenantId = (request as any).tenantId;
    const body = await request.json();
    const validatedData = trackUsageSchema.parse(body);

    const subscriptionService = new SubscriptionManagementService();

    // Check current usage before incrementing
    const currentUsage = await subscriptionService.checkUsageLimit(tenantId, validatedData.metric);
    
    if (currentUsage.isExceeded) {
      return NextResponse.json(
        { 
          error: 'Usage limit exceeded',
          details: {
            metric: validatedData.metric,
            current: currentUsage.current,
            limit: currentUsage.limit
          }
        },
        { status: 400 }
      );
    }

    // Track the usage
    await subscriptionService.trackUsage(tenantId, validatedData.metric, validatedData.increment);

    // Get updated usage
    const newUsage = await subscriptionService.checkUsageLimit(tenantId, validatedData.metric);

    return NextResponse.json({
      success: true,
      data: {
        metric: validatedData.metric,
        newUsage
      },
      message: 'Usage tracked successfully'
    });

  } catch (error) {
    console.error('Track usage error:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          error: 'Validation failed',
          details: error.errors.map(e => ({
            field: e.path.join('.'),
            message: e.message
          }))
        },
        { status: 400 }
      );
    }

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
});
