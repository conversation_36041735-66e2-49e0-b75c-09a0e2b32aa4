'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Trash2, Plus, Edit3 } from 'lucide-react';
import { cn } from '@/lib/utils';

interface PlaceholderMapping {
  type: 'text' | 'image' | 'table' | 'list';
  description: string;
  required: boolean;
  defaultValue?: string;
  format?: string;
}

interface PlaceholderManagerProps {
  placeholderMappings: Record<string, PlaceholderMapping>;
  onChange: (mappings: Record<string, PlaceholderMapping>) => void;
  className?: string;
}

export function PlaceholderManager({
  placeholderMappings,
  onChange,
  className
}: PlaceholderManagerProps) {
  const [editingPlaceholder, setEditingPlaceholder] = useState<string | null>(null);
  const [newPlaceholder, setNewPlaceholder] = useState({
    name: '',
    type: 'text' as PlaceholderMapping['type'],
    description: '',
    required: false,
    defaultValue: '',
    format: 'none'
  });

  const handleAddPlaceholder = () => {
    if (!newPlaceholder.name.trim()) return;

    const placeholderKey = `{{${newPlaceholder.name}}}`;
    const updatedMappings = {
      ...placeholderMappings,
      [placeholderKey]: {
        type: newPlaceholder.type,
        description: newPlaceholder.description,
        required: newPlaceholder.required,
        defaultValue: newPlaceholder.defaultValue || undefined,
        format: (newPlaceholder.format && newPlaceholder.format !== 'none') ? newPlaceholder.format : undefined
      }
    };

    onChange(updatedMappings);
    setNewPlaceholder({
      name: '',
      type: 'text',
      description: '',
      required: false,
      defaultValue: '',
      format: 'none'
    });
  };

  const handleUpdatePlaceholder = (placeholderKey: string, updates: Partial<PlaceholderMapping>) => {
    const updatedMappings = {
      ...placeholderMappings,
      [placeholderKey]: {
        ...placeholderMappings[placeholderKey],
        ...updates
      }
    };
    onChange(updatedMappings);
  };

  const handleDeletePlaceholder = (placeholderKey: string) => {
    const updatedMappings = { ...placeholderMappings };
    delete updatedMappings[placeholderKey];
    onChange(updatedMappings);
  };

  const getTypeColor = (type: PlaceholderMapping['type']) => {
    switch (type) {
      case 'text': return 'bg-blue-100 text-blue-800';
      case 'image': return 'bg-green-100 text-green-800';
      case 'table': return 'bg-purple-100 text-purple-800';
      case 'list': return 'bg-orange-100 text-orange-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className={cn('space-y-4', className)}>
      {/* Add New Placeholder */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg flex items-center">
            <Plus className="w-5 h-5 mr-2" />
            Add New Placeholder
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="placeholder-name">Placeholder Name</Label>
              <Input
                id="placeholder-name"
                placeholder="e.g., client_name"
                value={newPlaceholder.name}
                onChange={(e) => setNewPlaceholder(prev => ({ ...prev, name: e.target.value }))}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="placeholder-type">Type</Label>
              <Select
                value={newPlaceholder.type}
                onValueChange={(value: PlaceholderMapping['type']) => 
                  setNewPlaceholder(prev => ({ ...prev, type: value }))
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="text">Text</SelectItem>
                  <SelectItem value="image">Image</SelectItem>
                  <SelectItem value="table">Table</SelectItem>
                  <SelectItem value="list">List</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="placeholder-description">Description</Label>
            <Textarea
              id="placeholder-description"
              placeholder="Describe what this placeholder represents..."
              value={newPlaceholder.description}
              onChange={(e) => setNewPlaceholder(prev => ({ ...prev, description: e.target.value }))}
              rows={2}
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="placeholder-default">Default Value (Optional)</Label>
              <Input
                id="placeholder-default"
                placeholder="Default value..."
                value={newPlaceholder.defaultValue}
                onChange={(e) => setNewPlaceholder(prev => ({ ...prev, defaultValue: e.target.value }))}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="placeholder-format">Format (Optional)</Label>
              <Select
                value={newPlaceholder.format || "none"}
                onValueChange={(value) => setNewPlaceholder(prev => ({
                  ...prev,
                  format: value === "none" ? "" : value
                }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select format..." />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="none">None</SelectItem>
                  <SelectItem value="currency">Currency</SelectItem>
                  <SelectItem value="date">Date</SelectItem>
                  <SelectItem value="uppercase">Uppercase</SelectItem>
                  <SelectItem value="lowercase">Lowercase</SelectItem>
                  <SelectItem value="capitalize">Capitalize</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <Switch
              id="placeholder-required"
              checked={newPlaceholder.required}
              onCheckedChange={(checked) => setNewPlaceholder(prev => ({ ...prev, required: checked }))}
            />
            <Label htmlFor="placeholder-required">Required field</Label>
          </div>

          <Button onClick={handleAddPlaceholder} disabled={!newPlaceholder.name.trim()}>
            Add Placeholder
          </Button>
        </CardContent>
      </Card>

      {/* Existing Placeholders */}
      {Object.keys(placeholderMappings).length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Existing Placeholders</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {Object.entries(placeholderMappings).map(([placeholderKey, mapping]) => (
                <div
                  key={placeholderKey}
                  className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50"
                >
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-1">
                      <code className="text-sm font-mono bg-gray-100 px-2 py-1 rounded">
                        {placeholderKey}
                      </code>
                      <Badge className={cn('text-xs', getTypeColor(mapping.type))}>
                        {mapping.type}
                      </Badge>
                      {mapping.required && (
                        <Badge variant="destructive" className="text-xs">
                          Required
                        </Badge>
                      )}
                    </div>
                    <p className="text-sm text-gray-600">{mapping.description}</p>
                    {mapping.defaultValue && (
                      <p className="text-xs text-gray-500 mt-1">
                        Default: {mapping.defaultValue}
                      </p>
                    )}
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setEditingPlaceholder(
                        editingPlaceholder === placeholderKey ? null : placeholderKey
                      )}
                    >
                      <Edit3 className="w-4 h-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDeletePlaceholder(placeholderKey)}
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
