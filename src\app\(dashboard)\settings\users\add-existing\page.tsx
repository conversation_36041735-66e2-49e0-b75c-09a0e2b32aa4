'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { PermissionProvider } from '@/components/providers/permission-provider';
import { PermissionGate } from '@/components/ui/permission-gate';
import { PermissionAction, PermissionResource } from '@/types';
import { useTenant } from '@/hooks/use-tenant';
import { PageLayout } from '@/components/layout/page-layout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ContactAvatar } from '@/components/ui/contact-avatar';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import {
  ArrowLeftIcon,
  UserPlusIcon,
  MagnifyingGlassIcon,
  ExclamationTriangleIcon,
  CheckIcon
} from '@heroicons/react/24/outline';

interface User {
  id: string;
  email: string;
  firstName: string | null;
  lastName: string | null;
  avatarUrl: string | null;
  status: string;
  lastLogin: string | null;
}

interface Role {
  id: string;
  name: string;
  description: string | null;
}

interface Team {
  id: string;
  name: string;
  color: string | null;
}

export default function AddExistingUserPage() {
  const { currentTenant } = useTenant();
  const router = useRouter();

  const [searchTerm, setSearchTerm] = useState('');
  const [availableUsers, setAvailableUsers] = useState<User[]>([]);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [selectedRole, setSelectedRole] = useState('');
  const [selectedTeam, setSelectedTeam] = useState('');
  const [department, setDepartment] = useState('');
  const [jobTitle, setJobTitle] = useState('');
  const [roles, setRoles] = useState<Role[]>([]);
  const [teams, setTeams] = useState<Team[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (currentTenant) {
      fetchRoles();
      fetchTeams();
    }
  }, [currentTenant]);

  useEffect(() => {
    if (searchTerm.length >= 3) {
      searchUsers();
    } else {
      setAvailableUsers([]);
    }
  }, [searchTerm]);

  const fetchRoles = async () => {
    try {
      const response = await fetch('/api/roles/assignable');
      if (response.ok) {
        const data = await response.json();
        // Ensure we always set an array
        const rolesData = data.data;
        const roles = Array.isArray(rolesData) ? rolesData : [];
        setRoles(roles);
        // Set default role
        if (roles.length > 0) {
          const defaultRole = roles.find((role: Role) =>
            role.name.toLowerCase().includes('user')
          ) || roles[0];
          setSelectedRole(defaultRole.name);
        }
      } else {
        console.error('Failed to fetch roles: HTTP', response.status);
        setRoles([]);
      }
    } catch (err) {
      console.error('Failed to fetch roles:', err);
      setRoles([]);
    }
  };

  const fetchTeams = async () => {
    try {
      const response = await fetch('/api/teams');
      if (response.ok) {
        const data = await response.json();
        // Ensure we always set an array
        const teamsData = data.data;
        setTeams(Array.isArray(teamsData) ? teamsData : []);
      } else {
        console.error('Failed to fetch teams: HTTP', response.status);
        setTeams([]);
      }
    } catch (err) {
      console.error('Failed to fetch teams:', err);
      setTeams([]);
    }
  };

  const searchUsers = async () => {
    try {
      setIsSearching(true);
      setError(null);

      const response = await fetch(`/api/users/search?q=${encodeURIComponent(searchTerm)}`);
      
      if (response.ok) {
        const data = await response.json();
        setAvailableUsers(data.data || []);
      } else {
        throw new Error('Failed to search users');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to search users');
    } finally {
      setIsSearching(false);
    }
  };

  const handleAddUser = async () => {
    if (!selectedUser || !selectedRole) {
      setError('Please select a user and role');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/users', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'invite',
          email: selectedUser.email,
          firstName: selectedUser.firstName || '',
          lastName: selectedUser.lastName || '',
          role: selectedRole,
          teamId: selectedTeam || undefined,
          department: department.trim() || undefined,
          jobTitle: jobTitle.trim() || undefined
        }),
      });

      if (response.ok) {
        router.push('/settings/users');
      } else {
        const errorData = await response.json();
        setError(errorData.error || 'Failed to add user to tenant');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to add user');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    router.push('/settings/users');
  };

  if (!currentTenant) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <ExclamationTriangleIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-500">No tenant context available</p>
        </div>
      </div>
    );
  }

  const actions = (
    <div className="flex items-center gap-2">
      <Button variant="outline" onClick={handleCancel}>
        <ArrowLeftIcon className="w-4 h-4 mr-2" />
        Back to Users
      </Button>
    </div>
  );

  return (
    <PermissionProvider>
      <PageLayout
        title="Add Existing User"
        description="Add an existing user to your organization"
        actions={actions}
      >
        <PermissionGate
          resource={PermissionResource.USERS}
          action={PermissionAction.CREATE}
          fallback={<AccessDeniedMessage />}
        >
          <div className="max-w-4xl mx-auto space-y-6">
            {error && (
              <div className="bg-red-50 border border-red-200 rounded-md p-4">
                <div className="flex">
                  <ExclamationTriangleIcon className="w-5 h-5 text-red-400 mr-2 flex-shrink-0 mt-0.5" />
                  <p className="text-sm text-red-600">{error}</p>
                </div>
              </div>
            )}

            {/* User Search */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <MagnifyingGlassIcon className="w-5 h-5" />
                  Search for User
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label htmlFor="search" className="block text-sm font-medium text-gray-700 mb-1">
                    Search by email or name
                  </label>
                  <div className="relative">
                    <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                    <input
                      type="text"
                      id="search"
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      placeholder="Enter email address or name (minimum 3 characters)"
                      className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>
                  <p className="text-xs text-gray-500 mt-1">
                    Search for users who are not already members of this tenant
                  </p>
                </div>

                {/* Search Results */}
                {isSearching && (
                  <div className="flex items-center justify-center py-8">
                    <LoadingSpinner size="sm" />
                    <span className="ml-2 text-gray-600">Searching...</span>
                  </div>
                )}

                {!isSearching && searchTerm.length >= 3 && availableUsers.length === 0 && (
                  <div className="text-center py-8">
                    <UserIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-500">No users found matching your search</p>
                  </div>
                )}

                {availableUsers.length > 0 && (
                  <div className="space-y-2">
                    <h4 className="text-sm font-medium text-gray-700">Available Users</h4>
                    <div className="max-h-64 overflow-y-auto border border-gray-200 rounded-md">
                      {availableUsers.map((user) => (
                        <UserSearchResult
                          key={user.id}
                          user={user}
                          isSelected={selectedUser?.id === user.id}
                          onSelect={() => setSelectedUser(user)}
                        />
                      ))}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* User Configuration */}
            {selectedUser && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <UserPlusIcon className="w-5 h-5" />
                    Configure User Access
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* Selected User Display */}
                  <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
                    <div className="flex items-center space-x-4">
                      <ContactAvatar
                        firstName={selectedUser.firstName}
                        lastName={selectedUser.lastName}
                        avatarUrl={selectedUser.avatarUrl}
                        size="md"
                      />
                      <div>
                        <h3 className="text-lg font-medium text-gray-900">
                          {selectedUser.firstName} {selectedUser.lastName}
                        </h3>
                        <p className="text-gray-600">{selectedUser.email}</p>
                        <div className="flex items-center gap-2 mt-1">
                          <CheckIcon className="w-4 h-4 text-green-500" />
                          <span className="text-sm text-green-600">User selected</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Role and Team Configuration */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label htmlFor="role" className="block text-sm font-medium text-gray-700 mb-1">
                        Role *
                      </label>
                      <select
                        id="role"
                        value={selectedRole}
                        onChange={(e) => setSelectedRole(e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      >
                        <option value="">Select a role</option>
                        {Array.isArray(roles) && roles.length > 0 ? (
                          roles.map((role) => (
                            <option key={role.id} value={role.name}>
                              {role.name}
                              {role.description && ` - ${role.description}`}
                            </option>
                          ))
                        ) : (
                          <option value="">No roles available</option>
                        )}
                      </select>
                    </div>

                    <div>
                      <label htmlFor="team" className="block text-sm font-medium text-gray-700 mb-1">
                        Team
                      </label>
                      <select
                        id="team"
                        value={selectedTeam}
                        onChange={(e) => setSelectedTeam(e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      >
                        <option value="">No team</option>
                        {Array.isArray(teams) && teams.length > 0 ? (
                          teams.map((team) => (
                            <option key={team.id} value={team.id}>
                              {team.name}
                            </option>
                          ))
                        ) : (
                          <option value="">No teams available</option>
                        )}
                      </select>
                    </div>
                  </div>

                  {/* Additional Information */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label htmlFor="department" className="block text-sm font-medium text-gray-700 mb-1">
                        Department
                      </label>
                      <input
                        type="text"
                        id="department"
                        value={department}
                        onChange={(e) => setDepartment(e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder="Enter department"
                      />
                    </div>

                    <div>
                      <label htmlFor="jobTitle" className="block text-sm font-medium text-gray-700 mb-1">
                        Job Title
                      </label>
                      <input
                        type="text"
                        id="jobTitle"
                        value={jobTitle}
                        onChange={(e) => setJobTitle(e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder="Enter job title"
                      />
                    </div>
                  </div>

                  {/* Form Actions */}
                  <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={handleCancel}
                      disabled={isLoading}
                    >
                      Cancel
                    </Button>
                    <Button
                      onClick={handleAddUser}
                      disabled={isLoading || !selectedUser || !selectedRole}
                      className="flex items-center gap-2"
                    >
                      {isLoading ? (
                        <>
                          <LoadingSpinner size="sm" />
                          Adding User...
                        </>
                      ) : (
                        <>
                          <UserPlusIcon className="w-4 h-4" />
                          Add User to Tenant
                        </>
                      )}
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </PermissionGate>
      </PageLayout>
    </PermissionProvider>
  );
}

interface UserSearchResultProps {
  user: User;
  isSelected: boolean;
  onSelect: () => void;
}

function UserSearchResult({ user, isSelected, onSelect }: UserSearchResultProps) {
  return (
    <div
      onClick={onSelect}
      className={`p-4 cursor-pointer border-b border-gray-200 last:border-b-0 hover:bg-gray-50 ${
        isSelected ? 'bg-blue-50 border-blue-200' : ''
      }`}
    >
      <div className="flex items-center space-x-3">
        <ContactAvatar
          firstName={user.firstName}
          lastName={user.lastName}
          avatarUrl={user.avatarUrl}
          size="sm"
        />
        <div className="flex-1">
          <div className="text-sm font-medium text-gray-900">
            {user.firstName} {user.lastName}
          </div>
          <div className="text-sm text-gray-500">{user.email}</div>
        </div>
        {isSelected && (
          <CheckIcon className="w-5 h-5 text-blue-600" />
        )}
      </div>
    </div>
  );
}

function AccessDeniedMessage() {
  return (
    <div className="text-center py-12">
      <ExclamationTriangleIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
      <h3 className="text-lg font-medium text-gray-900 mb-2">Access Denied</h3>
      <p className="text-gray-500 max-w-md mx-auto">
        You don't have permission to add users to this tenant.
      </p>
    </div>
  );
}
