import { prisma } from '@/lib/prisma';
import { z } from 'zod';
import { UnifiedNotificationService } from './unified-notification-service';
import { aiService, AIRequest } from './ai-service';

// Document analysis types
export type DocumentAnalysisType = 
  | 'contract_analysis'
  | 'proposal_analysis'
  | 'financial_analysis'
  | 'legal_review'
  | 'content_extraction'
  | 'sentiment_analysis'
  | 'risk_assessment';

// Validation schemas
export const documentAnalysisSchema = z.object({
  documentId: z.string(),
  tenantId: z.string(),
  analysisType: z.enum(['contract_analysis', 'proposal_analysis', 'financial_analysis', 'legal_review', 'content_extraction', 'sentiment_analysis', 'risk_assessment']),
  documentName: z.string(),
  documentType: z.string(),
  uploadedBy: z.string(),
  analysisPrompt: z.string().optional(),
  priority: z.enum(['low', 'medium', 'high', 'urgent']).default('medium'),
});

export type DocumentAnalysisRequest = z.infer<typeof documentAnalysisSchema>;

export interface DocumentAnalysisResult {
  documentId: string;
  analysisType: DocumentAnalysisType;
  summary: string;
  keyFindings: string[];
  riskFactors: string[];
  recommendations: string[];
  confidence: number; // 0-1
  sentiment?: 'positive' | 'neutral' | 'negative';
  extractedData?: Record<string, any>;
  flaggedIssues: {
    type: 'legal' | 'financial' | 'compliance' | 'quality';
    severity: 'low' | 'medium' | 'high' | 'critical';
    description: string;
    recommendation: string;
  }[];
  processingTime: number;
  timestamp: Date;
}

export class AIDocumentAnalysisNotificationService {
  private notificationService = new UnifiedNotificationService();

  /**
   * Process document analysis and trigger notifications
   */
  async processDocumentAnalysis(request: DocumentAnalysisRequest): Promise<DocumentAnalysisResult> {
    const validatedRequest = documentAnalysisSchema.parse(request);

    try {
      // Perform AI document analysis
      const analysisResult = await this.analyzeDocumentWithAI(validatedRequest);

      // Store analysis results
      await this.storeAnalysisResults(validatedRequest, analysisResult);

      // Trigger notifications based on analysis results
      await this.triggerDocumentAnalysisNotifications(validatedRequest, analysisResult);

      return analysisResult;

    } catch (error) {
      console.error('Document analysis failed:', error);
      
      // Trigger failure notification
      await this.triggerAnalysisFailureNotification(validatedRequest, error);
      
      throw error;
    }
  }

  /**
   * Analyze document using AI
   */
  private async analyzeDocumentWithAI(request: DocumentAnalysisRequest): Promise<DocumentAnalysisResult> {
    const startTime = Date.now();

    try {
      // Get document content (this would typically fetch from storage)
      const documentContent = await this.getDocumentContent(request.documentId);

      // Build analysis prompt based on document type
      const analysisPrompt = this.buildAnalysisPrompt(request, documentContent);

      const aiRequest: AIRequest = {
        prompt: analysisPrompt,
        systemPrompt: this.getSystemPrompt(request.analysisType),
        context: {
          documentId: request.documentId,
          documentName: request.documentName,
          documentType: request.documentType,
          analysisType: request.analysisType
        },
        tenantId: request.tenantId,
        userId: request.uploadedBy,
        feature: 'document_analysis'
      };

      const response = await aiService.generateResponse(aiRequest, {
        temperature: 0.2, // Lower temperature for more consistent analysis
        maxTokens: 2000
      });

      // Parse AI response into structured result
      const analysisResult = this.parseAnalysisResponse(response.content, request);
      analysisResult.processingTime = Date.now() - startTime;
      analysisResult.timestamp = new Date();

      return analysisResult;

    } catch (error) {
      console.error('AI document analysis failed:', error);
      throw new Error(`Document analysis failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get system prompt based on analysis type
   */
  private getSystemPrompt(analysisType: DocumentAnalysisType): string {
    const prompts = {
      contract_analysis: `You are an expert contract analyst. Analyze the provided contract document and identify key terms, obligations, risks, and recommendations. Focus on payment terms, liability clauses, termination conditions, and compliance requirements.`,
      
      proposal_analysis: `You are an expert proposal analyst. Analyze the provided proposal document and evaluate its completeness, competitiveness, and potential success factors. Identify strengths, weaknesses, and improvement opportunities.`,
      
      financial_analysis: `You are an expert financial analyst. Analyze the provided financial document and identify key metrics, trends, risks, and opportunities. Focus on revenue, costs, profitability, and financial health indicators.`,
      
      legal_review: `You are an expert legal reviewer. Analyze the provided document for legal compliance, potential risks, and regulatory requirements. Identify any clauses that may need legal attention.`,
      
      content_extraction: `You are an expert data extraction specialist. Extract and structure key information from the provided document. Focus on names, dates, amounts, addresses, and other structured data.`,
      
      sentiment_analysis: `You are an expert sentiment analyst. Analyze the tone, sentiment, and emotional indicators in the provided document. Identify positive, negative, and neutral sentiments with specific examples.`,
      
      risk_assessment: `You are an expert risk analyst. Identify and assess potential risks mentioned or implied in the provided document. Categorize risks by type and severity, and provide mitigation recommendations.`
    };

    return prompts[analysisType] || prompts.content_extraction;
  }

  /**
   * Build analysis prompt
   */
  private buildAnalysisPrompt(request: DocumentAnalysisRequest, documentContent: string): string {
    return `
Analyze the following ${request.documentType} document named "${request.documentName}":

${documentContent.substring(0, 8000)} ${documentContent.length > 8000 ? '...(truncated)' : ''}

Please provide a comprehensive analysis including:
1. Executive Summary
2. Key Findings (3-5 main points)
3. Risk Factors (if any)
4. Recommendations (actionable items)
5. Confidence Level (0-100%)
6. Flagged Issues (categorized by type and severity)

${request.analysisPrompt ? `Additional analysis requirements: ${request.analysisPrompt}` : ''}

Format your response as a structured JSON object with the following fields:
- summary: string
- keyFindings: string[]
- riskFactors: string[]
- recommendations: string[]
- confidence: number (0-100)
- sentiment: "positive" | "neutral" | "negative"
- extractedData: object
- flaggedIssues: array of {type, severity, description, recommendation}
`;
  }

  /**
   * Parse AI analysis response
   */
  private parseAnalysisResponse(content: string, request: DocumentAnalysisRequest): DocumentAnalysisResult {
    try {
      // Try to parse JSON response
      const parsed = JSON.parse(content);
      
      return {
        documentId: request.documentId,
        analysisType: request.analysisType,
        summary: parsed.summary || 'Analysis completed',
        keyFindings: Array.isArray(parsed.keyFindings) ? parsed.keyFindings : [],
        riskFactors: Array.isArray(parsed.riskFactors) ? parsed.riskFactors : [],
        recommendations: Array.isArray(parsed.recommendations) ? parsed.recommendations : [],
        confidence: typeof parsed.confidence === 'number' ? parsed.confidence / 100 : 0.5,
        sentiment: parsed.sentiment || 'neutral',
        extractedData: parsed.extractedData || {},
        flaggedIssues: Array.isArray(parsed.flaggedIssues) ? parsed.flaggedIssues : [],
        processingTime: 0, // Will be set by caller
        timestamp: new Date()
      };
    } catch (error) {
      console.error('Failed to parse AI analysis response:', error);
      
      // Fallback parsing for non-JSON responses
      return {
        documentId: request.documentId,
        analysisType: request.analysisType,
        summary: content.substring(0, 500),
        keyFindings: [],
        riskFactors: [],
        recommendations: [],
        confidence: 0.3,
        sentiment: 'neutral',
        extractedData: {},
        flaggedIssues: [],
        processingTime: 0,
        timestamp: new Date()
      };
    }
  }

  /**
   * Get document content (placeholder - would integrate with actual storage)
   */
  private async getDocumentContent(documentId: string): Promise<string> {
    // This would typically fetch document content from storage service
    // For now, return placeholder content
    return `Document content for ${documentId} would be fetched from storage service.`;
  }

  /**
   * Store analysis results
   */
  private async storeAnalysisResults(
    request: DocumentAnalysisRequest,
    result: DocumentAnalysisResult
  ): Promise<void> {
    try {
      await prisma.documentAnalysis.create({
        data: {
          documentId: request.documentId,
          tenantId: request.tenantId,
          analysisType: request.analysisType,
          summary: result.summary,
          keyFindings: result.keyFindings,
          riskFactors: result.riskFactors,
          recommendations: result.recommendations,
          confidence: result.confidence,
          sentiment: result.sentiment,
          extractedData: result.extractedData || {},
          flaggedIssues: result.flaggedIssues,
          processingTime: result.processingTime,
          analyzedBy: request.uploadedBy,
        }
      });
    } catch (error) {
      console.error('Failed to store analysis results:', error);
      // Don't throw error to avoid breaking the main operation
    }
  }

  /**
   * Trigger document analysis notifications
   */
  private async triggerDocumentAnalysisNotifications(
    request: DocumentAnalysisRequest,
    result: DocumentAnalysisResult
  ): Promise<void> {
    try {
      const targetUsers = [];
      
      // Always notify the uploader
      targetUsers.push(request.uploadedBy);

      // Notify relevant team members based on document type and findings
      const relevantUsers = await this.getRelevantUsers(request, result);
      targetUsers.push(...relevantUsers);

      // Remove duplicates
      const uniqueTargetUsers = [...new Set(targetUsers)];

      // Send completion notification
      for (const userId of uniqueTargetUsers) {
        await this.notificationService.createNotification({
          tenantId: request.tenantId,
          userId,
          type: 'document_analysis_complete',
          category: 'system',
          title: 'Document Analysis Complete',
          message: `AI analysis of "${request.documentName}" has been completed`,
          data: {
            documentId: request.documentId,
            documentName: request.documentName,
            analysisType: request.analysisType,
            summary: result.summary,
            keyFindings: result.keyFindings,
            riskFactors: result.riskFactors,
            recommendations: result.recommendations,
            confidence: result.confidence,
            flaggedIssuesCount: result.flaggedIssues.length,
            processingTime: result.processingTime,
            uploadedBy: request.uploadedBy
          },
          actionUrl: `/documents/${request.documentId}/analysis`,
          actionLabel: 'View Analysis'
        });
      }

      // Send critical issue alerts if any
      const criticalIssues = result.flaggedIssues.filter(issue => issue.severity === 'critical');
      if (criticalIssues.length > 0) {
        await this.triggerCriticalIssueAlert(request, result, criticalIssues);
      }

    } catch (error) {
      console.error('Failed to send document analysis notifications:', error);
    }
  }

  /**
   * Get relevant users for notifications
   */
  private async getRelevantUsers(
    request: DocumentAnalysisRequest,
    result: DocumentAnalysisResult
  ): Promise<string[]> {
    const relevantRoles = [];

    // Determine relevant roles based on analysis type
    switch (request.analysisType) {
      case 'contract_analysis':
      case 'legal_review':
        relevantRoles.push('Legal', 'Contract Manager', 'Compliance Officer');
        break;
      case 'financial_analysis':
        relevantRoles.push('Finance', 'CFO', 'Financial Analyst', 'Accounting');
        break;
      case 'proposal_analysis':
        relevantRoles.push('Sales Manager', 'Proposal Manager', 'Business Development');
        break;
      case 'risk_assessment':
        relevantRoles.push('Risk Manager', 'Compliance Officer', 'Manager');
        break;
      default:
        relevantRoles.push('Manager', 'Admin');
    }

    const users = await prisma.tenantUser.findMany({
      where: {
        tenantId: request.tenantId,
        OR: [
          { role: { in: relevantRoles } },
          { isTenantAdmin: true }
        ],
        status: 'active'
      },
      select: { userId: true }
    });

    return users.map(user => user.userId);
  }

  /**
   * Trigger critical issue alert
   */
  private async triggerCriticalIssueAlert(
    request: DocumentAnalysisRequest,
    result: DocumentAnalysisResult,
    criticalIssues: any[]
  ): Promise<void> {
    try {
      // Get senior management for critical alerts
      const seniorManagement = await prisma.tenantUser.findMany({
        where: {
          tenantId: request.tenantId,
          OR: [
            { role: { in: ['CEO', 'CTO', 'CFO', 'Legal Director', 'VP', 'Director'] } },
            { isTenantAdmin: true }
          ],
          status: 'active'
        },
        select: { userId: true }
      });

      for (const user of seniorManagement) {
        await this.notificationService.createNotification({
          tenantId: request.tenantId,
          userId: user.userId,
          type: 'critical_document_issues',
          category: 'system',
          title: 'Critical Issues Found in Document Analysis',
          message: `AI analysis of "${request.documentName}" has identified ${criticalIssues.length} critical issue(s) requiring immediate attention`,
          data: {
            documentId: request.documentId,
            documentName: request.documentName,
            criticalIssues: criticalIssues.map(issue => ({
              type: issue.type,
              description: issue.description,
              recommendation: issue.recommendation
            })),
            analysisType: request.analysisType,
            uploadedBy: request.uploadedBy
          },
          actionUrl: `/documents/${request.documentId}/analysis`,
          actionLabel: 'Review Issues'
        });
      }
    } catch (error) {
      console.error('Failed to send critical issue alert:', error);
    }
  }

  /**
   * Trigger analysis failure notification
   */
  private async triggerAnalysisFailureNotification(
    request: DocumentAnalysisRequest,
    error: any
  ): Promise<void> {
    try {
      await this.notificationService.createNotification({
        tenantId: request.tenantId,
        userId: request.uploadedBy,
        type: 'document_analysis_failed',
        category: 'system',
        title: 'Document Analysis Failed',
        message: `AI analysis of "${request.documentName}" failed. Please try again or contact support.`,
        data: {
          documentId: request.documentId,
          documentName: request.documentName,
          analysisType: request.analysisType,
          error: error instanceof Error ? error.message : 'Unknown error',
          uploadedBy: request.uploadedBy
        },
        actionUrl: `/documents/${request.documentId}`,
        actionLabel: 'View Document'
      });
    } catch (notificationError) {
      console.error('Failed to send analysis failure notification:', notificationError);
    }
  }
}

// Export singleton instance
export const aiDocumentAnalysisNotificationService = new AIDocumentAnalysisNotificationService();
