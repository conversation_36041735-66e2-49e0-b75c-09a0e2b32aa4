import { NextRequest, NextResponse } from 'next/server';
import { withSuperAdminAuth } from '@/lib/tenant-context';
import { SuperAdminService } from '@/services/super-admin';

/**
 * @swagger
 * /api/admin/platform/analytics/trends:
 *   get:
 *     summary: Get platform trend data for charts (Super Admin only)
 *     description: Retrieve time-series data for tenant and user growth trends
 *     tags:
 *       - Super Admin - Analytics
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: days
 *         schema:
 *           type: integer
 *           default: 30
 *         description: Number of days to retrieve trend data for
 *     responses:
 *       200:
 *         description: Trend data retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       date:
 *                         type: string
 *                         format: date
 *                       tenants:
 *                         type: number
 *                       users:
 *                         type: number
 *                       revenue:
 *                         type: number
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Super Admin access required
 */
export const GET = withSuperAdminAuth(async (request: NextRequest) => {
  try {
    const { searchParams } = new URL(request.url);
    const days = parseInt(searchParams.get('days') || '30');

    // Validate days parameter
    if (days < 1 || days > 365) {
      return NextResponse.json(
        { error: 'Days parameter must be between 1 and 365' },
        { status: 400 }
      );
    }

    const superAdminService = new SuperAdminService();
    const trends = await superAdminService.getTrendData(days);

    return NextResponse.json({
      success: true,
      data: trends
    });

  } catch (error) {
    console.error('Get platform trends error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
});
