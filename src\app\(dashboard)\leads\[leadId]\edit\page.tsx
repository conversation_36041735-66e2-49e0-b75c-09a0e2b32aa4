'use client';

import React, { useState, useEffect, use } from 'react';
import { useRouter } from 'next/navigation';
import { LeadWithRelations } from '@/services/lead-management';
import { LeadForm } from '@/components/leads/lead-form';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { FileText, ArrowLeft } from 'lucide-react';
import { toastFunctions as toast } from '@/hooks/use-toast';
import { PageLayout } from '@/components/layout/page-layout';
import { EmptyState } from '@/components/ui/empty-state';
import { PermissionGate } from '@/components/ui/permission-gate';
import { PermissionResource, PermissionAction } from '@/types';

interface EditLeadPageProps {
  params: Promise<{
    leadId: string;
  }>;
}

export default function EditLeadPage({ params }: EditLeadPageProps) {
  const router = useRouter();
  const resolvedParams = use(params);
  const leadId = resolvedParams.leadId;
  const [lead, setLead] = useState<LeadWithRelations | null>(null);
  const [loading, setLoading] = useState(true);

  // Load lead data
  useEffect(() => {
    const loadLead = async () => {
      try {
        const response = await fetch(`/api/leads/${leadId}`);
        const data = await response.json();

        if (data.success) {
          setLead(data.data.lead);
        } else {
          toast.error('Lead not found');
          router.push('/leads');
        }
      } catch (error) {
        console.error('Error loading lead:', error);
        toast.error('Failed to load lead');
        router.push('/leads');
      } finally {
        setLoading(false);
      }
    };

    if (leadId) {
      loadLead();
    }
  }, [leadId, router]);

  const handleSuccess = () => {
    router.push(`/leads/${leadId}`);
  };

  const handleCancel = () => {
    router.push(`/leads/${leadId}`);
  };

  const actions = (
    <div className="flex items-center gap-2">
      <Button variant="outline" size="sm" onClick={() => router.push(`/leads/${leadId}`)}>
        <ArrowLeft className="h-4 w-4 mr-2" />
        Back to Lead
      </Button>
      <Button variant="outline" size="sm" onClick={() => router.push('/leads')}>
        View All Leads
      </Button>
    </div>
  );

  if (loading) {
    return (
      <PageLayout
        title="Loading Lead..."
        description="Please wait while we load the lead details"
        actions={actions}
      >
        <div className="animate-pulse space-y-6">
          <div className="h-96 bg-gray-200 rounded"></div>
        </div>
      </PageLayout>
    );
  }

  if (!lead) {
    return (
      <PageLayout
        title="Lead Not Found"
        description="The lead you're trying to edit doesn't exist"
        actions={actions}
      >
        <Card>
          <CardContent className="text-center py-12">
            <EmptyState
              icon={<FileText className="w-12 h-12" />}
              title="Lead not found"
              description="The lead you're trying to edit doesn't exist or you don't have permission to edit it."
              action={{
                label: 'Back to Leads',
                onClick: () => router.push('/leads'),
                variant: 'primary'
              }}
            />
          </CardContent>
        </Card>
      </PageLayout>
    );
  }

  return (
    <PermissionGate
      resource={PermissionResource.LEADS}
      action={PermissionAction.UPDATE}
      fallback={
        <PageLayout
          title="Access Denied"
          description="You don't have permission to edit this lead"
          actions={actions}
        >
          <Card>
            <CardContent className="p-8">
              <EmptyState
                icon={<FileText className="w-12 h-12" />}
                title="Access Denied"
                description="You don't have permission to edit this lead. Please contact your administrator for access."
                action={{
                  label: 'Back to Lead',
                  onClick: () => router.push(`/leads/${leadId}`),
                  variant: 'primary'
                }}
              />
            </CardContent>
          </Card>
        </PageLayout>
      }
    >
      <PageLayout
        title={`Edit ${lead.title}`}
        description="Update lead information and details"
        actions={actions}
      >
        <LeadForm
          lead={lead}
          onSuccess={handleSuccess}
          onCancel={handleCancel}
        />
      </PageLayout>
    </PermissionGate>
  );
}
