import { prisma } from '@/lib/prisma';
import { Document } from '@prisma/client';
import { z } from 'zod';

// Validation schemas
export const createDocumentSchema = z.object({
  name: z.string().min(1, 'Document name is required').max(255),
  relatedToType: z.enum(['opportunity', 'contact', 'company', 'lead', 'proposal', 'project', 'handover']),
  relatedToId: z.string().min(1, 'Related entity ID is required'),
  description: z.string().optional(),
  tags: z.array(z.string()).default([]),
  isPublic: z.boolean().default(false),
});

export const updateDocumentSchema = createDocumentSchema.partial().omit({ relatedToType: true, relatedToId: true });

export const documentFiltersSchema = z.object({
  relatedToType: z.string().optional(),
  relatedToId: z.string().optional(),
  search: z.string().optional(),
  mimeType: z.string().optional(),
  uploadedBy: z.string().optional(),
  tags: z.array(z.string()).optional(),
  isPublic: z.boolean().optional(),
  page: z.coerce.number().min(1).default(1),
  limit: z.coerce.number().min(1).max(100).default(20),
  sortBy: z.enum(['name', 'createdAt', 'fileSize', 'mimeType']).default('createdAt'),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
});

// Types
type UserSummary = {
  id: string;
  firstName: string | null;
  lastName: string | null;
  email: string;
};

export interface DocumentWithRelations extends Document {
  uploadedBy?: UserSummary | null;
  _count?: {
    downloads?: number;
  };
}

export interface DocumentFilters {
  relatedToType?: string;
  relatedToId?: string;
  search?: string;
  mimeType?: string;
  uploadedBy?: string;
  tags?: string[];
  isPublic?: boolean;
  page?: number;
  limit?: number;
  sortBy?: 'name' | 'createdAt' | 'fileSize' | 'mimeType';
  sortOrder?: 'asc' | 'desc';
}

export interface DocumentUploadResult {
  document: DocumentWithRelations;
  uploadPath: string;
}

export interface DocumentStats {
  totalDocuments: number;
  totalSize: number;
  byType: Record<string, number>;
  byRelatedEntity: Record<string, number>;
  recentUploads: number;
}

// File type validation
const ALLOWED_FILE_TYPES = [
  // Documents
  'application/pdf',
  'application/msword',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  'application/vnd.ms-excel',
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  'application/vnd.ms-powerpoint',
  'application/vnd.openxmlformats-officedocument.presentationml.presentation',
  'text/plain',
  'text/csv',
  // Images
  'image/jpeg',
  'image/png',
  'image/gif',
  'image/webp',
  'image/svg+xml',
  // Archives
  'application/zip',
  'application/x-rar-compressed',
  'application/x-7z-compressed',
  // Other
  'application/json',
  'application/xml',
];

const MAX_FILE_SIZE = 50 * 1024 * 1024; // 50MB

export class DocumentManagementService {
  private readonly includeOptions = {
    uploadedBy: {
      select: {
        id: true,
        firstName: true,
        lastName: true,
        email: true,
      },
    },
  };

  /**
   * Validate file before upload
   */
  static validateFile(file: File): { valid: boolean; error?: string } {
    // Check file size
    if (file.size > MAX_FILE_SIZE) {
      return {
        valid: false,
        error: `File size exceeds maximum limit of ${MAX_FILE_SIZE / (1024 * 1024)}MB`,
      };
    }

    // Check file type
    if (!ALLOWED_FILE_TYPES.includes(file.type)) {
      return {
        valid: false,
        error: `File type ${file.type} is not allowed`,
      };
    }

    // Check file name
    if (!file.name || file.name.length > 255) {
      return {
        valid: false,
        error: 'Invalid file name',
      };
    }

    return { valid: true };
  }

  /**
   * Verify related entity exists
   */
  private async verifyRelatedEntity(
    relatedToType: string,
    relatedToId: string,
    tenantId: string
  ): Promise<void> {
    let exists = false;

    switch (relatedToType) {
      case 'opportunity':
        exists = !!(await prisma.opportunity.findFirst({
          where: { id: relatedToId, tenantId },
        }));
        break;
      case 'contact':
        exists = !!(await prisma.contact.findFirst({
          where: { id: relatedToId, tenantId },
        }));
        break;
      case 'company':
        exists = !!(await prisma.company.findFirst({
          where: { id: relatedToId, tenantId },
        }));
        break;
      case 'lead':
        exists = !!(await prisma.lead.findFirst({
          where: { id: relatedToId, tenantId },
        }));
        break;
      case 'proposal':
        exists = !!(await prisma.proposal.findFirst({
          where: { id: relatedToId, tenantId },
        }));
        break;
      case 'project':
        exists = !!(await prisma.project.findFirst({
          where: { id: relatedToId, tenantId },
        }));
        break;
      case 'handover':
        exists = !!(await prisma.projectHandover.findFirst({
          where: { id: relatedToId, tenantId },
        }));
        break;
      default:
        throw new Error(`Invalid related entity type: ${relatedToType}`);
    }

    if (!exists) {
      throw new Error(`Related ${relatedToType} not found`);
    }
  }

  /**
   * Create document record (file handling done separately in API)
   */
  async createDocument(
    tenantId: string,
    documentData: {
      name: string;
      filePath: string;
      fileSize: number;
      mimeType: string;
      relatedToType: string;
      relatedToId: string;
      description?: string;
      tags?: string[];
      isPublic?: boolean;
    },
    uploadedBy: string
  ): Promise<DocumentWithRelations> {
    // Validate document data
    const validatedData = createDocumentSchema.parse({
      name: documentData.name,
      relatedToType: documentData.relatedToType as any,
      relatedToId: documentData.relatedToId,
      description: documentData.description,
      tags: documentData.tags || [],
      isPublic: documentData.isPublic || false,
    });

    // Verify related entity exists
    await this.verifyRelatedEntity(
      validatedData.relatedToType,
      validatedData.relatedToId,
      tenantId
    );

    // Create document record
    const document = await prisma.document.create({
      data: {
        name: validatedData.name,
        filePath: documentData.filePath,
        fileSize: documentData.fileSize,
        mimeType: documentData.mimeType,
        relatedToType: validatedData.relatedToType,
        relatedToId: validatedData.relatedToId,
        tenantId,
        uploadedById: uploadedBy,
        description: validatedData.description,
        tags: validatedData.tags,
        isPublic: validatedData.isPublic,
      },
      include: this.includeOptions,
    });

    return document;
  }

  /**
   * Get documents with filters
   */
  async getDocuments(
    tenantId: string,
    filters: DocumentFilters = {}
  ): Promise<{
    documents: DocumentWithRelations[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      pages: number;
    };
  }> {
    const validatedFilters = documentFiltersSchema.parse(filters);
    const { page, limit, sortBy, sortOrder, ...filterOptions } = validatedFilters;

    const skip = (page - 1) * limit;

    // Build where clause
    const where: any = {
      tenantId,
      ...(filterOptions.relatedToType && { relatedToType: filterOptions.relatedToType }),
      ...(filterOptions.relatedToId && { relatedToId: filterOptions.relatedToId }),
      ...(filterOptions.mimeType && { mimeType: filterOptions.mimeType }),
      ...(filterOptions.uploadedBy && { uploadedById: filterOptions.uploadedBy }),
      ...(filterOptions.isPublic !== undefined && { isPublic: filterOptions.isPublic }),
    };

    // Add search filter
    if (filterOptions.search) {
      where.OR = [
        { name: { contains: filterOptions.search, mode: 'insensitive' } },
        { description: { contains: filterOptions.search, mode: 'insensitive' } },
      ];
    }

    // Add tags filter
    if (filterOptions.tags && filterOptions.tags.length > 0) {
      where.tags = {
        hasEvery: filterOptions.tags,
      };
    }

    // Get total count
    const total = await prisma.document.count({ where });

    // Get documents
    const documents = await prisma.document.findMany({
      where,
      include: this.includeOptions,
      orderBy: { [sortBy]: sortOrder },
      skip,
      take: limit,
    });

    return {
      documents,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    };
  }

  /**
   * Get document by ID
   */
  async getDocument(documentId: string, tenantId: string): Promise<DocumentWithRelations | null> {
    return prisma.document.findFirst({
      where: { id: documentId, tenantId },
      include: this.includeOptions,
    });
  }

  /**
   * Update document metadata
   */
  async updateDocument(
    documentId: string,
    tenantId: string,
    updateData: z.infer<typeof updateDocumentSchema>,
    updatedBy: string
  ): Promise<DocumentWithRelations> {
    const validatedData = updateDocumentSchema.parse(updateData);

    // Check if document exists
    const existingDocument = await this.getDocument(documentId, tenantId);
    if (!existingDocument) {
      throw new Error('Document not found');
    }

    return prisma.document.update({
      where: { id: documentId, tenantId },
      data: {
        ...validatedData,
        updatedAt: new Date(),
      },
      include: this.includeOptions,
    });
  }

  /**
   * Delete document (file deletion handled separately in API)
   */
  async deleteDocument(documentId: string, tenantId: string): Promise<DocumentWithRelations> {
    const document = await this.getDocument(documentId, tenantId);
    if (!document) {
      throw new Error('Document not found');
    }

    // Delete from database
    await prisma.document.delete({
      where: { id: documentId, tenantId },
    });

    return document;
  }

  /**
   * Get document statistics
   */
  async getDocumentStats(tenantId: string): Promise<DocumentStats> {
    const documents = await prisma.document.findMany({
      where: { tenantId },
      select: {
        fileSize: true,
        mimeType: true,
        relatedToType: true,
        createdAt: true,
      },
    });

    const totalSize = documents.reduce((sum, doc) => sum + (doc.fileSize || 0), 0);
    const byType: Record<string, number> = {};
    const byRelatedEntity: Record<string, number> = {};

    const oneWeekAgo = new Date();
    oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);

    let recentUploads = 0;

    documents.forEach((doc) => {
      // Count by MIME type
      if (doc.mimeType) {
        byType[doc.mimeType] = (byType[doc.mimeType] || 0) + 1;
      }

      // Count by related entity type
      if (doc.relatedToType) {
        byRelatedEntity[doc.relatedToType] = (byRelatedEntity[doc.relatedToType] || 0) + 1;
      }

      // Count recent uploads
      if (doc.createdAt > oneWeekAgo) {
        recentUploads++;
      }
    });

    return {
      totalDocuments: documents.length,
      totalSize,
      byType,
      byRelatedEntity,
      recentUploads,
    };
  }

}
