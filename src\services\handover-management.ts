import { prisma } from '@/lib/prisma';
import { z } from 'zod';
import { UnifiedNotificationService } from './unified-notification-service';
import { HandoverChecklistService } from './handover-checklist';

// Validation schemas
export const createHandoverSchema = z.object({
  opportunityId: z.string().min(1, 'Opportunity ID is required'),
  templateId: z.string().optional(),
  title: z.string().min(1, 'Title is required').max(255),
  description: z.string().optional(),
  priority: z.enum(['low', 'medium', 'high', 'urgent']).default('medium'),
  salesRepId: z.string().optional(),
  deliveryManagerId: z.string().optional(),
  assignedTeamIds: z.array(z.string()).default([]),
  scheduledDate: z.date().optional(),
  estimatedDuration: z.number().int().positive().optional(),
});

export const updateHandoverSchema = createHandoverSchema.partial().extend({
  status: z.enum(['pending', 'in_progress', 'completed', 'cancelled']).optional(),
  checklistProgress: z.number().min(0).max(100).optional(),
  qualityScore: z.number().min(1).max(5).optional(),
  clientSatisfaction: z.number().min(1).max(5).optional(),
  deliveryFeedback: z.string().optional(),
});

export const createChecklistItemSchema = z.object({
  title: z.string().min(1, 'Title is required').max(255),
  description: z.string().optional(),
  category: z.enum(['general', 'technical', 'legal', 'financial', 'client']).default('general'),
  orderIndex: z.number().int().min(0),
  isRequired: z.boolean().default(true),
  dependsOnIds: z.array(z.string()).default([]),
});

export const createQAThreadSchema = z.object({
  title: z.string().min(1, 'Title is required').max(255),
  category: z.enum(['general', 'technical', 'requirements', 'timeline', 'budget']).default('general'),
  priority: z.enum(['low', 'medium', 'high', 'urgent']).default('medium'),
  assignedToId: z.string().optional().nullable().transform(val => val === '' ? null : val),
});

export const createQAMessageSchema = z.object({
  content: z.string().min(1, 'Content is required'),
  messageType: z.enum(['message', 'answer', 'clarification', 'escalation']).default('message'),
  attachments: z.array(z.string()).default([]),
});

// Types
export type CreateHandoverData = z.infer<typeof createHandoverSchema>;
export type UpdateHandoverData = z.infer<typeof updateHandoverSchema>;
export type CreateChecklistItemData = z.infer<typeof createChecklistItemSchema>;
export type CreateQAThreadData = z.infer<typeof createQAThreadSchema>;
export type CreateQAMessageData = z.infer<typeof createQAMessageSchema>;

export interface HandoverWithDetails {
  id: string;
  tenantId: string;
  opportunityId: string;
  templateId?: string;
  title: string;
  description?: string;
  status: string;
  priority: string;
  salesRepId?: string;
  deliveryManagerId?: string;
  assignedTeamIds: string[];
  checklistProgress: number;
  documentsCount: number;
  qaThreadsCount: number;
  scheduledDate?: Date;
  startedAt?: Date;
  completedAt?: Date;
  estimatedDuration?: number;
  actualDuration?: number;
  qualityScore?: number;
  clientSatisfaction?: number;
  deliveryFeedback?: string;
  createdById?: string;
  createdAt: Date;
  updatedAt: Date;
  
  // Relations
  opportunity: {
    id: string;
    title: string;
    value?: number;
    stage: string;
    contact?: {
      id: string;
      firstName: string;
      lastName: string;
      email?: string;
    };
    company?: {
      id: string;
      name: string;
    };
  };
  template?: {
    id: string;
    name: string;
    description?: string;
  };
  salesRep?: {
    id: string;
    firstName?: string;
    lastName?: string;
    email: string;
  };
  deliveryManager?: {
    id: string;
    firstName?: string;
    lastName?: string;
    email: string;
  };
  checklistItems: Array<{
    id: string;
    title: string;
    description?: string;
    category: string;
    orderIndex: number;
    isRequired: boolean;
    isCompleted: boolean;
    completedAt?: Date;
    completedBy?: {
      id: string;
      firstName?: string;
      lastName?: string;
    };
    notes?: string;
    dependsOnIds: string[];
  }>;
  qaThreads: Array<{
    id: string;
    title: string;
    category: string;
    priority: string;
    status: string;
    createdBy: {
      id: string;
      firstName?: string;
      lastName?: string;
    };
    assignedTo?: {
      id: string;
      firstName?: string;
      lastName?: string;
    };
    responseTime?: number;
    isEscalated: boolean;
    createdAt: Date;
    _count: {
      messages: number;
    };
  }>;
}

export class HandoverManagementService {
  private notificationService = new UnifiedNotificationService();
  private checklistService = new HandoverChecklistService();

  private includeOptions = {
    opportunity: {
      select: {
        id: true,
        title: true,
        value: true,
        stage: true,
        contact: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          }
        },
        company: {
          select: {
            id: true,
            name: true,
          }
        }
      }
    },
    template: {
      select: {
        id: true,
        name: true,
        description: true,
      }
    },
    salesRep: {
      select: {
        id: true,
        firstName: true,
        lastName: true,
        email: true,
      }
    },
    deliveryManager: {
      select: {
        id: true,
        firstName: true,
        lastName: true,
        email: true,
      }
    },
    checklistItems: {
      include: {
        completedBy: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
          }
        }
      },
      orderBy: {
        orderIndex: 'asc'
      }
    },
    qaThreads: {
      include: {
        createdBy: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
          }
        },
        assignedTo: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
          }
        },
        _count: {
          select: {
            messages: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    }
  };

  /**
   * Create a new handover from an opportunity
   */
  async createHandover(
    tenantId: string,
    handoverData: CreateHandoverData,
    createdBy: string
  ): Promise<HandoverWithDetails> {
    const validatedData = createHandoverSchema.parse(handoverData);

    const handover = await prisma.$transaction(async (tx) => {
      // Verify opportunity exists and belongs to tenant
      const opportunity = await tx.opportunity.findUnique({
        where: {
          id: validatedData.opportunityId,
          tenantId
        }
      });

      if (!opportunity) {
        throw new Error('Opportunity not found or access denied');
      }

      // Check if handover already exists for this opportunity
      const existingHandover = await tx.projectHandover.findFirst({
        where: {
          opportunityId: validatedData.opportunityId,
          tenantId
        }
      });

      if (existingHandover) {
        throw new Error(`A handover "${existingHandover.title}" already exists for this opportunity. You can view it at /handovers/${existingHandover.id}`);
      }

      // Verify template exists if provided
      if (validatedData.templateId) {
        const template = await tx.handoverChecklistTemplate.findUnique({
          where: {
            id: validatedData.templateId,
            tenantId
          }
        });

        if (!template) {
          throw new Error('Handover template not found');
        }
      }

      // Verify users belong to tenant if provided
      if (validatedData.salesRepId) {
        const salesRep = await tx.user.findFirst({
          where: {
            id: validatedData.salesRepId,
            tenantUsers: {
              some: { tenantId }
            }
          }
        });

        if (!salesRep) {
          throw new Error('Sales representative not found or not part of tenant');
        }
      }

      if (validatedData.deliveryManagerId) {
        const deliveryManager = await tx.user.findFirst({
          where: {
            id: validatedData.deliveryManagerId,
            tenantUsers: {
              some: { tenantId }
            }
          }
        });

        if (!deliveryManager) {
          throw new Error('Delivery manager not found or not part of tenant');
        }
      }

      // Create the handover
      const handover = await tx.projectHandover.create({
        data: {
          tenantId,
          opportunityId: validatedData.opportunityId,
          templateId: validatedData.templateId,
          title: validatedData.title,
          description: validatedData.description,
          priority: validatedData.priority,
          salesRepId: validatedData.salesRepId,
          deliveryManagerId: validatedData.deliveryManagerId,
          assignedTeamIds: validatedData.assignedTeamIds,
          scheduledDate: validatedData.scheduledDate,
          estimatedDuration: validatedData.estimatedDuration,
          createdById: createdBy,
        },
        include: this.includeOptions,
      });

      // Trigger handover creation notification
      await this.triggerHandoverCreationNotification(handover as HandoverWithDetails, tenantId);

      return handover as HandoverWithDetails;
    });

    // Automatically create checklist from template if templateId is provided
    if (validatedData.templateId) {
      try {
        await this.checklistService.createChecklistFromTemplate(
          tenantId,
          handover.id,
          validatedData.templateId
        );
      } catch (error) {
        console.error('Failed to create checklist from template during handover creation:', error);
        // Don't throw error to avoid breaking the handover creation
        // The user can still manually create the checklist later
      }
    }

    return handover;
  }

  /**
   * Get handovers with filtering and pagination
   */
  async getHandovers(
    tenantId: string,
    options: {
      status?: string;
      priority?: string;
      salesRepId?: string;
      deliveryManagerId?: string;
      opportunityId?: string;
      search?: string;
      page?: number;
      limit?: number;
      sortBy?: 'createdAt' | 'updatedAt' | 'scheduledDate' | 'priority';
      sortOrder?: 'asc' | 'desc';
    } = {}
  ): Promise<{
    handovers: HandoverWithDetails[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  }> {
    const {
      status,
      priority,
      salesRepId,
      deliveryManagerId,
      opportunityId,
      search,
      page = 1,
      limit = 20,
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = options;

    const where: Record<string, unknown> = {
      tenantId,
      ...(status && { status }),
      ...(priority && { priority }),
      ...(salesRepId && { salesRepId }),
      ...(deliveryManagerId && { deliveryManagerId }),
      ...(opportunityId && { opportunityId }),
      ...(search && {
        OR: [
          { title: { contains: search, mode: 'insensitive' } },
          { description: { contains: search, mode: 'insensitive' } },
          { opportunity: { title: { contains: search, mode: 'insensitive' } } },
        ]
      })
    };

    const [handovers, total] = await Promise.all([
      prisma.projectHandover.findMany({
        where,
        include: this.includeOptions,
        orderBy: { [sortBy]: sortOrder },
        skip: (page - 1) * limit,
        take: limit,
      }),
      prisma.projectHandover.count({ where })
    ]);

    return {
      handovers: handovers as HandoverWithDetails[],
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit)
    };
  }

  /**
   * Get a single handover by ID
   */
  async getHandoverById(
    tenantId: string,
    handoverId: string
  ): Promise<HandoverWithDetails | null> {
    const handover = await prisma.projectHandover.findUnique({
      where: {
        id: handoverId,
        tenantId
      },
      include: this.includeOptions,
    });

    return handover as HandoverWithDetails | null;
  }

  /**
   * Update a handover
   */
  async updateHandover(
    tenantId: string,
    handoverId: string,
    updateData: UpdateHandoverData
  ): Promise<HandoverWithDetails> {
    const validatedData = updateHandoverSchema.parse(updateData);

    return await prisma.$transaction(async (tx) => {
      // Verify handover exists and belongs to tenant
      const existingHandover = await tx.projectHandover.findUnique({
        where: {
          id: handoverId,
          tenantId
        }
      });

      if (!existingHandover) {
        throw new Error('Handover not found or access denied');
      }

      // Verify users belong to tenant if provided
      if (validatedData.salesRepId) {
        const salesRep = await tx.user.findFirst({
          where: {
            id: validatedData.salesRepId,
            tenantUsers: {
              some: { tenantId }
            }
          }
        });

        if (!salesRep) {
          throw new Error('Sales representative not found or not part of tenant');
        }
      }

      if (validatedData.deliveryManagerId) {
        const deliveryManager = await tx.user.findFirst({
          where: {
            id: validatedData.deliveryManagerId,
            tenantUsers: {
              some: { tenantId }
            }
          }
        });

        if (!deliveryManager) {
          throw new Error('Delivery manager not found or not part of tenant');
        }
      }

      // Handle status changes
      const updateFields: any = { ...validatedData };

      if (validatedData.status === 'in_progress' && !existingHandover.startedAt) {
        updateFields.startedAt = new Date();
      }

      if (validatedData.status === 'completed' && !existingHandover.completedAt) {
        updateFields.completedAt = new Date();

        // Calculate actual duration if started
        if (existingHandover.startedAt) {
          const durationMs = new Date().getTime() - existingHandover.startedAt.getTime();
          updateFields.actualDuration = Math.round(durationMs / (1000 * 60 * 60)); // Convert to hours
        }
      }

      // Update the handover
      const handover = await tx.projectHandover.update({
        where: {
          id: handoverId,
          tenantId
        },
        data: updateFields,
        include: this.includeOptions,
      });

      // Trigger handover update notifications
      await this.handleHandoverUpdateNotifications(
        handover as HandoverWithDetails,
        existingHandover,
        tenantId
      );

      return handover as HandoverWithDetails;
    });
  }

  /**
   * Delete a handover and all its related dependencies
   */
  async deleteHandover(
    tenantId: string,
    handoverId: string
  ): Promise<void> {
    return await prisma.$transaction(async (tx) => {
      // Verify handover exists and belongs to tenant
      const handover = await tx.projectHandover.findUnique({
        where: {
          id: handoverId,
          tenantId
        },
        include: {
          checklistItems: true,
          documents: true,
          qaThreads: {
            include: {
              messages: true
            }
          },
          notifications: true,
          projects: true
        }
      });

      if (!handover) {
        throw new Error('Handover not found or access denied');
      }

      // Check if there are any projects that reference this handover
      if (handover.projects && handover.projects.length > 0) {
        const projectTitles = handover.projects.map(p => p.name).join(', ');
        throw new Error(`Cannot delete handover "${handover.title}" because it is referenced by the following projects: ${projectTitles}. Please remove the handover reference from these projects first.`);
      }

      // Delete related records in the correct order
      // Note: Most of these have CASCADE delete in the schema, but we'll be explicit for safety

      // Delete QA messages first (they depend on QA threads)
      for (const thread of handover.qaThreads) {
        await tx.handoverQAMessage.deleteMany({
          where: {
            threadId: thread.id,
            tenantId
          }
        });
      }

      // Delete QA threads
      await tx.handoverQAThread.deleteMany({
        where: {
          handoverId,
          tenantId
        }
      });

      // Delete handover notifications
      await tx.handoverNotification.deleteMany({
        where: {
          handoverId,
          tenantId
        }
      });

      // Delete handover documents
      await tx.handoverDocument.deleteMany({
        where: {
          handoverId,
          tenantId
        }
      });

      // Delete checklist items
      await tx.handoverChecklistItem.deleteMany({
        where: {
          handoverId,
          tenantId
        }
      });

      // Finally, delete the handover itself
      await tx.projectHandover.delete({
        where: {
          id: handoverId,
          tenantId
        }
      });
    });
  }

  /**
   * Trigger handover creation notification
   */
  private async triggerHandoverCreationNotification(
    handover: HandoverWithDetails,
    tenantId: string
  ): Promise<void> {
    try {
      const targetUsers = [];

      // Notify sales rep if assigned
      if (handover.salesRepId) {
        targetUsers.push(handover.salesRepId);
      }

      // Notify delivery manager if assigned
      if (handover.deliveryManagerId) {
        targetUsers.push(handover.deliveryManagerId);
      }

      // Notify assigned team members
      if (handover.assignedTeamIds && handover.assignedTeamIds.length > 0) {
        targetUsers.push(...handover.assignedTeamIds as string[]);
      }

      // Remove duplicates
      const uniqueTargetUsers = Array.from(new Set(targetUsers));

      // Create notification for each target user
      for (const userId of uniqueTargetUsers) {
        await this.notificationService.createNotification({
          tenantId,
          userId,
          type: 'handover_created',
          category: 'handover',
          title: 'New Handover Created',
          message: `Handover "${handover.title}" has been created for opportunity "${handover.opportunity.title}"`,
          data: {
            handoverId: handover.id,
            handoverTitle: handover.title,
            handoverPriority: handover.priority,
            opportunityId: handover.opportunityId,
            opportunityTitle: handover.opportunity.title,
            scheduledDate: handover.scheduledDate?.toISOString(),
            estimatedDuration: handover.estimatedDuration,
            salesRepId: handover.salesRepId,
            deliveryManagerId: handover.deliveryManagerId,
            assignedTeamIds: handover.assignedTeamIds
          },
          actionUrl: `/handovers/${handover.id}`,
          actionLabel: 'View Handover'
        });
      }
    } catch (error) {
      console.error('Failed to send handover creation notification:', error);
      // Don't throw error to avoid breaking the main operation
    }
  }

  /**
   * Handle notifications for handover updates
   */
  private async handleHandoverUpdateNotifications(
    updatedHandover: HandoverWithDetails,
    previousHandover: any,
    tenantId: string
  ): Promise<void> {
    try {
      // Check for status change
      if (updatedHandover.status !== previousHandover.status) {
        await this.triggerHandoverStatusChangeNotification(
          updatedHandover,
          tenantId,
          previousHandover.status
        );
      }

      // Check for priority change to urgent
      if (updatedHandover.priority === 'urgent' && previousHandover.priority !== 'urgent') {
        await this.triggerUrgentHandoverNotification(updatedHandover, tenantId);
      }

      // Check for delivery manager change
      if (updatedHandover.deliveryManagerId !== previousHandover.deliveryManagerId) {
        await this.triggerHandoverAssignmentNotification(
          updatedHandover,
          tenantId,
          previousHandover.deliveryManagerId
        );
      }

      // Check for checklist progress milestones
      const previousProgress = previousHandover.checklistProgress || 0;
      const currentProgress = updatedHandover.checklistProgress || 0;

      if (currentProgress === 100 && previousProgress < 100) {
        await this.triggerChecklistCompletedNotification(updatedHandover, tenantId);
      } else if (currentProgress >= 50 && previousProgress < 50) {
        await this.triggerChecklistMilestoneNotification(updatedHandover, tenantId, 50);
      }
    } catch (error) {
      console.error('Failed to handle handover update notifications:', error);
    }
  }

  /**
   * Trigger handover status change notification
   */
  private async triggerHandoverStatusChangeNotification(
    handover: HandoverWithDetails,
    tenantId: string,
    previousStatus: string
  ): Promise<void> {
    try {
      const targetUsers = [
        handover.salesRepId,
        handover.deliveryManagerId,
        ...(handover.assignedTeamIds as string[] || [])
      ].filter(Boolean);

      // Determine notification type based on status change
      let notificationType = 'handover_status_changed';
      let title = 'Handover Status Updated';
      let message = `Handover "${handover.title}" status changed from ${previousStatus} to ${handover.status}`;

      if (handover.status === 'completed') {
        notificationType = 'handover_completed';
        title = 'Handover Completed! 🎉';
        message = `Handover "${handover.title}" has been completed and is ready for project creation`;
      } else if (handover.status === 'in_progress' && previousStatus === 'pending') {
        notificationType = 'handover_started';
        title = 'Handover Started';
        message = `Work has started on handover "${handover.title}"`;
      } else if (handover.status === 'cancelled') {
        notificationType = 'handover_cancelled';
        title = 'Handover Cancelled';
        message = `Handover "${handover.title}" has been cancelled`;
      }

      for (const userId of Array.from(new Set(targetUsers))) {
        await this.notificationService.createNotification({
          tenantId,
          userId,
          type: notificationType,
          category: 'handover',
          title,
          message,
          data: {
            handoverId: handover.id,
            handoverTitle: handover.title,
            previousStatus,
            newStatus: handover.status,
            handoverPriority: handover.priority,
            checklistProgress: handover.checklistProgress,
            completedAt: handover.completedAt?.toISOString()
          },
          actionUrl: `/handovers/${handover.id}`,
          actionLabel: handover.status === 'completed' ? 'Create Project' : 'View Handover'
        });
      }
    } catch (error) {
      console.error('Failed to send handover status change notification:', error);
    }
  }

  /**
   * Trigger handover assignment notification
   */
  private async triggerHandoverAssignmentNotification(
    handover: HandoverWithDetails,
    tenantId: string,
    previousDeliveryManagerId?: string | null
  ): Promise<void> {
    try {
      // Notify new delivery manager
      if (handover.deliveryManagerId) {
        await this.notificationService.createNotification({
          tenantId,
          userId: handover.deliveryManagerId,
          type: 'handover_assigned',
          category: 'handover',
          title: 'Handover Assigned to You',
          message: `Handover "${handover.title}" has been assigned to you as delivery manager`,
          data: {
            handoverId: handover.id,
            handoverTitle: handover.title,
            handoverStatus: handover.status,
            handoverPriority: handover.priority,
            opportunityTitle: handover.opportunity.title,
            previousDeliveryManager: previousDeliveryManagerId
          },
          actionUrl: `/handovers/${handover.id}`,
          actionLabel: 'View Handover'
        });
      }

      // Notify previous delivery manager if different
      if (previousDeliveryManagerId && previousDeliveryManagerId !== handover.deliveryManagerId) {
        await this.notificationService.createNotification({
          tenantId,
          userId: previousDeliveryManagerId,
          type: 'handover_reassigned',
          category: 'handover',
          title: 'Handover Reassigned',
          message: `Handover "${handover.title}" has been reassigned to another delivery manager`,
          data: {
            handoverId: handover.id,
            handoverTitle: handover.title,
            newDeliveryManager: handover.deliveryManager ?
              `${handover.deliveryManager.firstName} ${handover.deliveryManager.lastName}` : 'Unknown'
          },
          actionUrl: `/handovers/${handover.id}`,
          actionLabel: 'View Handover'
        });
      }
    } catch (error) {
      console.error('Failed to send handover assignment notification:', error);
    }
  }

  /**
   * Trigger urgent handover notification
   */
  private async triggerUrgentHandoverNotification(
    handover: HandoverWithDetails,
    tenantId: string
  ): Promise<void> {
    try {
      const targetUsers = [
        handover.salesRepId,
        handover.deliveryManagerId,
        ...(handover.assignedTeamIds as string[] || [])
      ].filter(Boolean);

      // Notify managers and senior team
      // TODO: Add logic to get managers and senior team members

      for (const userId of Array.from(new Set(targetUsers))) {
        await this.notificationService.createNotification({
          tenantId,
          userId,
          type: 'urgent_handover_alert',
          category: 'handover',
          title: 'Urgent Handover Alert',
          message: `Urgent handover "${handover.title}" requires immediate attention`,
          data: {
            handoverId: handover.id,
            handoverTitle: handover.title,
            handoverPriority: handover.priority,
            handoverStatus: handover.status,
            opportunityTitle: handover.opportunity.title,
            scheduledDate: handover.scheduledDate?.toISOString()
          },
          actionUrl: `/handovers/${handover.id}`,
          actionLabel: 'View Handover'
        });
      }
    } catch (error) {
      console.error('Failed to send urgent handover notification:', error);
    }
  }

  /**
   * Trigger checklist completed notification
   */
  private async triggerChecklistCompletedNotification(
    handover: HandoverWithDetails,
    tenantId: string
  ): Promise<void> {
    try {
      const targetUsers = [
        handover.salesRepId,
        handover.deliveryManagerId,
        ...(handover.assignedTeamIds as string[] || [])
      ].filter(Boolean);

      for (const userId of Array.from(new Set(targetUsers))) {
        await this.notificationService.createNotification({
          tenantId,
          userId,
          type: 'checklist_completed',
          category: 'handover',
          title: 'Handover Checklist Completed! ✅',
          message: `All checklist items for handover "${handover.title}" have been completed`,
          data: {
            handoverId: handover.id,
            handoverTitle: handover.title,
            checklistProgress: handover.checklistProgress,
            opportunityTitle: handover.opportunity.title
          },
          actionUrl: `/handovers/${handover.id}`,
          actionLabel: 'View Handover'
        });
      }
    } catch (error) {
      console.error('Failed to send checklist completed notification:', error);
    }
  }

  /**
   * Trigger checklist milestone notification
   */
  private async triggerChecklistMilestoneNotification(
    handover: HandoverWithDetails,
    tenantId: string,
    milestone: number
  ): Promise<void> {
    try {
      const targetUsers = [
        handover.salesRepId,
        handover.deliveryManagerId
      ].filter(Boolean);

      for (const userId of Array.from(new Set(targetUsers))) {
        await this.notificationService.createNotification({
          tenantId,
          userId,
          type: 'checklist_milestone',
          category: 'handover',
          title: `Handover ${milestone}% Complete`,
          message: `Handover "${handover.title}" is ${milestone}% complete`,
          data: {
            handoverId: handover.id,
            handoverTitle: handover.title,
            checklistProgress: handover.checklistProgress,
            milestone,
            opportunityTitle: handover.opportunity.title
          },
          actionUrl: `/handovers/${handover.id}`,
          actionLabel: 'View Handover'
        });
      }
    } catch (error) {
      console.error('Failed to send checklist milestone notification:', error);
    }
  }
}

// Export singleton instance
export const handoverManagementService = new HandoverManagementService();
