import { NextResponse } from 'next/server';
import { withPermission } from '@/lib/permission-middleware';
import { 
  leadManagementService, 
  updateLeadSchema 
} from '@/services/lead-management';
import { PermissionAction, PermissionResource } from '@/types';
import { AuthenticatedRequest } from '@/lib/permission-middleware';

/**
 * GET /api/leads/[leadId]
 * Get a specific lead by ID
 */
export const GET = withPermission({
  resource: PermissionResource.LEADS,
  action: PermissionAction.READ,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest) => {
  try {
    const url = new URL(req.url);
    const pathSegments = url.pathname.split('/');
    const leadId = pathSegments[pathSegments.indexOf('leads') + 1];

    const lead = await leadManagementService.getLead(leadId, req.tenantId);

    if (!lead) {
      return NextResponse.json(
        { error: 'Lead not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: { lead },
      message: 'Lead retrieved successfully'
    });

  } catch (error) {
    console.error('Get lead API error:', error);
    
    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      { error: 'Failed to fetch lead' },
      { status: 500 }
    );
  }
});

/**
 * PUT /api/leads/[leadId]
 * Update a specific lead
 */
export const PUT = withPermission({
  resource: PermissionResource.LEADS,
  action: PermissionAction.UPDATE,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest) => {
  try {
    const url = new URL(req.url);
    const pathSegments = url.pathname.split('/');
    const leadId = pathSegments[pathSegments.indexOf('leads') + 1];

    const body = await req.json();
    const validatedData = updateLeadSchema.parse(body);

    const lead = await leadManagementService.updateLead(
      leadId,
      req.tenantId,
      validatedData
    );

    return NextResponse.json({
      success: true,
      data: { lead },
      message: 'Lead updated successfully'
    });

  } catch (error) {
    console.error('Update lead API error:', error);
    
    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      { error: 'Failed to update lead' },
      { status: 500 }
    );
  }
});

/**
 * DELETE /api/leads/[leadId]
 * Delete a specific lead
 */
export const DELETE = withPermission({
  resource: PermissionResource.LEADS,
  action: PermissionAction.DELETE,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest) => {
  try {
    const url = new URL(req.url);
    const pathSegments = url.pathname.split('/');
    const leadId = pathSegments[pathSegments.indexOf('leads') + 1];

    await leadManagementService.deleteLead(leadId, req.tenantId);

    return NextResponse.json({
      success: true,
      message: 'Lead deleted successfully'
    });

  } catch (error) {
    console.error('Delete lead API error:', error);
    
    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      { error: 'Failed to delete lead' },
      { status: 500 }
    );
  }
});
