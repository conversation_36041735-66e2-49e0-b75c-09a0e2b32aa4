# CRM Platform with AI-Powered Proposal Generation

A comprehensive CRM platform built with Next.js, featuring AI-powered proposal generation with Mermaid diagram PNG conversion capabilities.

## 🚀 Features

- **Multi-tenant CRM System**: Complete customer relationship management
- **AI-Powered Proposals**: Generate professional proposals with AI assistance
- **Mermaid Diagram PNG Generation**: Convert Mermaid diagrams to high-quality PNG images
- **Document Generation**: Export proposals to DOCX and PDF formats
- **Docker Support**: Full containerization with Chrome/Puppeteer support
- **Multi-language Support**: Internationalization ready
- **Real-time Updates**: Live data synchronization

## 🛠️ Technology Stack

- **Frontend**: Next.js 15, React, TypeScript, Tailwind CSS
- **Backend**: Next.js API Routes, Prisma ORM
- **Database**: PostgreSQL
- **Cache**: Redis
- **AI Integration**: Google AI, OpenAI, Anthropic
- **Diagram Generation**: Mermaid CLI, Puppeteer
- **Containerization**: Docker, Docker Compose

## 📋 Prerequisites

- Node.js 20.x or higher
- Docker and Docker Compose
- PostgreSQL (if running locally)
- Redis (if running locally)

## 🚀 Quick Start

### Using Docker (Recommended)

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd crm
   ```

2. **Configure environment**
   ```bash
   cp .env.docker .env.local
   # Edit .env.local with your configuration
   ```

3. **Start with Docker Compose**
   ```bash
   # Development
   docker-compose -f docker-compose.yml -f docker-compose.dev.yml up

   # Production
   docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d
   ```

4. **Test Mermaid PNG Generation**
   ```bash
   ./scripts/test-docker-mermaid.sh
   ```

### Local Development

1. **Install dependencies**
   ```bash
   npm install
   ```

2. **Setup database**
   ```bash
   npx prisma migrate dev
   npx prisma generate
   ```

3. **Install Chrome for Puppeteer**
   ```bash
   npx puppeteer browsers install chrome-headless-shell
   ```

4. **Start development server**
   ```bash
   npm run dev
   ```

## 🐳 Docker Configuration

The Docker setup includes:

- **Chrome/Chromium**: For Mermaid PNG generation
- **Puppeteer**: Headless browser automation
- **Shared Memory**: 2GB for Chrome operations
- **Security**: Proper container security settings
- **Volumes**: Persistent storage for uploads and temp files

### Docker Services

- **app**: Next.js application with Mermaid support
- **postgres**: PostgreSQL database
- **redis**: Redis cache
- **nginx**: Reverse proxy (production only)
- **pgadmin**: Database management UI

### Environment Variables

Key environment variables for Mermaid PNG generation:

```bash
# Puppeteer Configuration
PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true
PUPPETEER_EXECUTABLE_PATH=/usr/bin/chromium-browser
PUPPETEER_ARGS=--no-sandbox --disable-setuid-sandbox

# Mermaid Configuration
MERMAID_TEMP_DIR=/tmp/mermaid
MERMAID_PNG_WIDTH=800
MERMAID_PNG_HEIGHT=600
MERMAID_PNG_THEME=default
```

## 📊 Mermaid PNG Generation

The platform supports converting Mermaid diagrams to PNG images for:

- **Proposal Documents**: Embedded diagrams in DOCX/PDF exports
- **Architecture Diagrams**: System design visualizations
- **Process Flows**: Business process documentation
- **Timeline Charts**: Project timeline visualization

### Features

- **Dual Rendering**: Mermaid CLI with Puppeteer fallback
- **Configurable Options**: Theme, size, background customization
- **Automatic Cleanup**: Temporary file management
- **Error Handling**: Graceful degradation on failures

## 🧪 Testing

### Test Mermaid PNG Generation

```bash
# Docker environment
./scripts/test-docker-mermaid.sh

# Local environment
node test-png-conversion.js
```



## 📚 Documentation

- [Docker Mermaid Setup](docs/DOCKER_MERMAID_SETUP.md)
- [API Documentation](docs/API.md)
- [Deployment Guide](docs/DEPLOYMENT.md)

## 🔧 Configuration

### AI Providers

Configure AI providers in `.env`:

```bash
AI_PROVIDER=google  # Options: google, openai, anthropic
GOOGLE_AI_API_KEY=your_key_here
OPENAI_API_KEY=your_key_here
ANTHROPIC_API_KEY=your_key_here
```

### Database

```bash
DATABASE_URL=postgresql://user:password@localhost:5432/crm_platform
```

### File Uploads

```bash
UPLOAD_DIR=./uploads
MAX_FILE_SIZE_MB=50
```

## 🚀 Deployment

### Production Deployment

1. **Build and deploy**
   ```bash
   docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d
   ```

2. **Monitor services**
   ```bash
   docker-compose logs -f app
   ```

3. **Health checks**
   ```bash
   curl http://localhost/api/health
   ```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly (including Mermaid PNG generation)
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

For issues related to:
- **Mermaid PNG Generation**: Check Chrome installation and shared memory
- **Docker Setup**: Verify container permissions and volumes
- **Database Issues**: Check connection strings and migrations
- **AI Integration**: Verify API keys and rate limits

## 🔗 Links

- [Next.js Documentation](https://nextjs.org/docs)
- [Mermaid Documentation](https://mermaid.js.org/)
- [Docker Documentation](https://docs.docker.com/)
- [Puppeteer Documentation](https://pptr.dev/)
