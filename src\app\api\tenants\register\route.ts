import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { TenantProvisioningService } from '@/services/tenant-provisioning';
import { prisma } from '@/lib/prisma';

const registerTenantSchema = z.object({
  tenantName: z.string().min(2, 'Tenant name must be at least 2 characters'),
  tenantSlug: z.string()
    .min(3, 'Slug must be at least 3 characters')
    .max(50, 'Slug must be less than 50 characters')
    .regex(/^[a-z0-9-]+$/, 'Slug can only contain lowercase letters, numbers, and hyphens'),
  adminEmail: z.string().email('Invalid email address'),
  adminPassword: z.string().min(8, 'Password must be at least 8 characters'),
  adminFirstName: z.string().min(1, 'First name is required'),
  adminLastName: z.string().min(1, 'Last name is required'),
  adminPhone: z.string().optional(),
  planId: z.string().optional(),
  acceptTerms: z.boolean().refine(val => val === true, 'You must accept the terms and conditions')
});

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate input
    const validatedData = registerTenantSchema.parse(body);

    // Check if tenant registration is enabled
    if (process.env.ENABLE_TENANT_REGISTRATION !== 'true') {
      return NextResponse.json(
        { error: 'Tenant registration is currently disabled' },
        { status: 403 }
      );
    }

    // Check if user already has maximum number of tenants
    const existingUser = await prisma.user.findUnique({
      where: { email: validatedData.adminEmail },
      include: {
        tenantUsers: true
      }
    });

    const maxTenantsPerEmail = parseInt(process.env.MAX_TENANTS_PER_EMAIL || '5');
    if (existingUser && existingUser.tenantUsers.length >= maxTenantsPerEmail) {
      return NextResponse.json(
        { error: `Maximum ${maxTenantsPerEmail} tenants allowed per email address` },
        { status: 400 }
      );
    }

    // Check if slug is available
    const provisioningService = new TenantProvisioningService();
    const isSlugAvailable = await provisioningService.isSlugAvailable(validatedData.tenantSlug);
    
    if (!isSlugAvailable) {
      return NextResponse.json(
        { error: 'Tenant slug is already taken' },
        { status: 400 }
      );
    }

    // Get default plan
    let planId = validatedData.planId;
    console.log(`[Registration] Initial planId: ${planId} (${typeof planId})`);

    if (!planId || planId.trim() === '') {
      console.log('[Registration] No planId provided, looking for default Starter plan...');

      const defaultPlan = await prisma.subscriptionPlan.findFirst({
        where: { name: 'Starter', isActive: true }
      });

      if (!defaultPlan) {
        console.error('[Registration] Default Starter plan not found in database');
        return NextResponse.json(
          { error: 'Default subscription plan not found. Please contact support.' },
          { status: 500 }
        );
      }

      planId = defaultPlan.id;
      console.log(`[Registration] Using default plan: ${defaultPlan.name} (${planId})`);
    }

    // Validate that the plan exists and is active
    console.log(`[Registration] Validating plan: ${planId}`);

    const selectedPlan = await prisma.subscriptionPlan.findUnique({
      where: { id: planId }
    });

    if (!selectedPlan || !selectedPlan.isActive) {
      console.error(`[Registration] Plan validation failed: plan=${selectedPlan?.name || 'not found'}, active=${selectedPlan?.isActive}`);
      return NextResponse.json(
        { error: 'Selected subscription plan not found or is not active.' },
        { status: 400 }
      );
    }

    console.log(`[Registration] Plan validation passed: ${selectedPlan.name}`);

    // Create tenant
    const tenant = await provisioningService.createTenant({
      name: validatedData.tenantName,
      slug: validatedData.tenantSlug,
      adminEmail: validatedData.adminEmail,
      adminPassword: validatedData.adminPassword,
      adminFirstName: validatedData.adminFirstName,
      adminLastName: validatedData.adminLastName,
      adminPhone: validatedData.adminPhone,
      planId
    });

    // Log successful registration
    await prisma.auditLog.create({
      data: {
        tenantId: tenant.id,
        action: 'TENANT_REGISTERED',
        resourceType: 'tenant',
        resourceId: tenant.id,
        newValues: {
          name: tenant.name,
          slug: tenant.slug,
          adminEmail: validatedData.adminEmail
        }
      }
    });

    return NextResponse.json({
      success: true,
      data: {
        tenant: {
          id: tenant.id,
          name: tenant.name,
          slug: tenant.slug
        },
        message: 'Tenant created successfully'
      }
    }, { status: 201 });

  } catch (error) {
    console.error('Tenant registration error:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          error: 'Validation failed',
          details: error.errors.map(e => ({
            field: e.path.join('.'),
            message: e.message
          }))
        },
        { status: 400 }
      );
    }

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Check slug availability
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const slug = searchParams.get('slug');

    if (!slug) {
      return NextResponse.json(
        { error: 'Slug parameter is required' },
        { status: 400 }
      );
    }

    // Validate slug format
    const slugRegex = /^[a-z0-9-]+$/;
    if (!slugRegex.test(slug) || slug.length < 3 || slug.length > 50) {
      return NextResponse.json(
        { 
          available: false,
          error: 'Invalid slug format. Use lowercase letters, numbers, and hyphens only (3-50 characters)'
        },
        { status: 400 }
      );
    }

    const provisioningService = new TenantProvisioningService();
    const isAvailable = await provisioningService.isSlugAvailable(slug);

    return NextResponse.json({
      available: isAvailable,
      slug
    });

  } catch (error) {
    console.error('Slug check error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
