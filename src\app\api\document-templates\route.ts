import { NextRequest, NextResponse } from 'next/server';
import { withPermission, AuthenticatedRequest } from '@/lib/permission-middleware';
import { DocumentTemplateManagementService } from '@/services/document-template-management';
import { PermissionResource, PermissionAction } from '@/types';
import { z } from 'zod';

/**
 * GET /api/document-templates
 * Get document templates with filtering and pagination
 */
export const GET = withPermission({
  resource: PermissionResource.PROPOSALS,
  action: PermissionAction.READ,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest) => {
  try {
    const { searchParams } = new URL(req.url);
    
    const filters = {
      category: searchParams.get('category') || undefined,
      templateType: searchParams.get('templateType') || undefined,
      isActive: searchParams.get('isActive') ? searchParams.get('isActive') === 'true' : undefined,
      includeInactive: searchParams.get('includeInactive') === 'true',
      search: searchParams.get('search') || undefined,
      page: parseInt(searchParams.get('page') || '1'),
      limit: parseInt(searchParams.get('limit') || '20'),
      sortBy: (searchParams.get('sortBy') as 'name' | 'createdAt' | 'updatedAt' | 'usageCount') || 'createdAt',
      sortOrder: (searchParams.get('sortOrder') as 'asc' | 'desc') || 'desc'
    };

    const templateService = new DocumentTemplateManagementService();
    const result = await templateService.getTemplates(req.tenantId, filters);

    return NextResponse.json({
      success: true,
      data: {
        templates: result.templates,
        pagination: {
          currentPage: result.currentPage,
          totalPages: result.totalPages,
          totalCount: result.totalCount,
          limit: filters.limit
        }
      }
    });

  } catch (error) {
    console.error('Get templates error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch templates' },
      { status: 500 }
    );
  }
});

/**
 * POST /api/document-templates
 * Create a new document template
 */
export const POST = withPermission({
  resource: PermissionResource.PROPOSALS,
  action: PermissionAction.CREATE,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest) => {
  try {
    const formData = await req.formData();
    
    // Extract form fields
    const templateData: any = {
      name: formData.get('name') as string,
      description: formData.get('description') as string || undefined,
      category: formData.get('category') as 'proposal' | 'contract' | 'report',
      templateType: formData.get('templateType') as string,
      isDefault: formData.get('isDefault') === 'true',
      isActive: formData.get('isActive') === 'true',
      styling: JSON.parse(formData.get('styling') as string || '{}'),
      branding: JSON.parse(formData.get('branding') as string || '{}'),
      variables: JSON.parse(formData.get('variables') as string || '[]'),
      htmlTemplate: formData.get('htmlTemplate') as string || undefined,
    };

    // Handle file upload if present
    const file = formData.get('file') as File | null;
    if (file) {
      // TODO: Implement file upload logic
      // For now, we'll just store the filename
      templateData.filePath = file.name;
    }

    const templateService = new DocumentTemplateManagementService();
    const template = await templateService.createTemplate(
      req.tenantId,
      templateData,
      req.userId
    );

    return NextResponse.json({
      success: true,
      data: { template },
      message: 'Template created successfully'
    });

  } catch (error) {
    console.error('Create template error:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          error: 'Validation failed',
          details: error.errors
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to create template' },
      { status: 500 }
    );
  }
});
