"use client"

import { useEffect, useState, useRef, useMemo } from 'react'
import { usePathname } from 'next/navigation'
import { useUIStore } from '@/stores/ui-store'
import {
  DashboardSkeleton,
  ListPageSkeleton,
  FormPageSkeleton
} from '@/components/ui/skeleton-loaders'

interface PageLoadingWrapperProps {
  children: React.ReactNode
}

function getSkeletonForPath(pathname: string) {
  if (pathname === '/dashboard') {
    return <DashboardSkeleton />
  }

  if (pathname.includes('/new') || pathname.includes('/edit') || pathname.includes('/create-project')) {
    return <FormPageSkeleton />
  }

  if (pathname.includes('/contacts') || pathname.includes('/companies') || pathname.includes('/leads') ||
      pathname.includes('/handovers') || pathname.includes('/projects') || pathname.includes('/proposals')) {
    return <ListPageSkeleton />
  }

  return <ListPageSkeleton />
}

export function PageLoadingWrapper({ children }: PageLoadingWrapperProps) {
  const pathname = usePathname()
  const { isNavigating, pageLoading, apiLoading } = useUIStore()
  const [showSkeleton, setShowSkeleton] = useState(false)
  const [currentChildren, setCurrentChildren] = useState(children)
  const previousPathnameRef = useRef(pathname)
  const isInitialMount = useRef(true)
  const timeoutRef = useRef<NodeJS.Timeout>()

  // Only show skeleton for navigation loading, not API loading
  const shouldShowSkeleton = useMemo(() => {
    return isNavigating && pageLoading && !apiLoading
  }, [isNavigating, pageLoading, apiLoading])

  useEffect(() => {
    // Skip on initial mount
    if (isInitialMount.current) {
      isInitialMount.current = false
      setCurrentChildren(children)
      return
    }

    // Clear any existing timeout
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
    }

    // Only show skeleton for actual navigation, not API calls
    if (shouldShowSkeleton) {
      setShowSkeleton(true)
    } else {
      // Small delay to ensure smooth transition, but only update children if pathname changed
      const pathnameChanged = previousPathnameRef.current !== pathname

      timeoutRef.current = setTimeout(() => {
        setShowSkeleton(false)

        // Only update children if pathname actually changed or this is the first load
        if (pathnameChanged || !currentChildren) {
          setCurrentChildren(children)
          previousPathnameRef.current = pathname
        }
      }, shouldShowSkeleton ? 150 : 50) // Longer delay when transitioning from skeleton
    }

    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }
    }
  }, [shouldShowSkeleton, pathname, children])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }
    }
  }, [])

  return (
    <>
      {showSkeleton ? (
        <div key="skeleton">
          {getSkeletonForPath(pathname)}
        </div>
      ) : (
        <div key={`content-${pathname}`}>
          {currentChildren}
        </div>
      )}
    </>
  )
}
