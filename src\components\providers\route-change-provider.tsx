"use client"

import { useEffect, useRef } from 'react'
import { usePathname, useSearchParams } from 'next/navigation'
import { useUIStore } from '@/stores/ui-store'

interface RouteChangeProviderProps {
  children: React.ReactNode
}

export function RouteChangeProvider({ children }: RouteChangeProviderProps) {
  const pathname = usePathname()
  const { setCurrentPage } = useUIStore()
  const isInitialMount = useRef(true)
  const previousPathnameRef = useRef(pathname)

  useEffect(() => {
    // Skip on initial mount to prevent unnecessary loading states
    if (isInitialMount.current) {
      isInitialMount.current = false
      setCurrentPage(pathname)
      previousPathnameRef.current = pathname
      return
    }

    // Only handle actual route changes, not search param changes
    const pathnameChanged = previousPathnameRef.current !== pathname

    if (!pathnameChanged) {
      // Just update current page for search param changes
      setCurrentPage(pathname)
      return
    }

    // Update current page immediately for faster UI response
    setCurrentPage(pathname)
    previousPathnameRef.current = pathname

  }, [pathname, setCurrentPage])

  return <>{children}</>
}
