import { cacheService, CACHE_PREFIXES, CACHE_TTL } from '../redis';
import { TenantContextData } from '../tenant-context';

export interface CachedTenantData extends TenantContextData {
  cachedAt: number;
  version: number;
}

export interface TenantUsageData {
  contacts: number;
  leads: number;
  users: number;
  ai_requests: number;
  storage_mb: number;
  lastUpdated: number;
}

export class TenantCacheService {
  // Cache tenant data
  async setTenantData(tenantId: string, data: TenantContextData): Promise<boolean> {
    const cachedData: CachedTenantData = {
      ...data,
      cachedAt: Date.now(),
      version: 1,
    };

    return await cacheService.set(`data:${tenantId}`, cachedData, {
      ttl: CACHE_TTL.TENANT_DATA,
      prefix: CACHE_PREFIXES.TENANT_DATA,
    });
  }

  // Get cached tenant data
  async getTenantData(tenantId: string): Promise<CachedTenantData | null> {
    return await cacheService.get<CachedTenantData>(`data:${tenantId}`, {
      prefix: CACHE_PREFIXES.TENANT_DATA,
    });
  }

  // Cache tenant subscription data
  async setTenantSubscription(tenantId: string, subscription: any): Promise<boolean> {
    return await cacheService.set(`subscription:${tenantId}`, subscription, {
      ttl: CACHE_TTL.SUBSCRIPTION,
      prefix: CACHE_PREFIXES.TENANT_DATA,
    });
  }

  // Get cached tenant subscription
  async getTenantSubscription(tenantId: string): Promise<any | null> {
    return await cacheService.get(`subscription:${tenantId}`, {
      prefix: CACHE_PREFIXES.TENANT_DATA,
    });
  }

  // Cache tenant usage metrics
  async setTenantUsage(tenantId: string, usage: TenantUsageData): Promise<boolean> {
    const usageWithTimestamp = {
      ...usage,
      lastUpdated: Date.now(),
    };

    return await cacheService.set(`usage:${tenantId}`, usageWithTimestamp, {
      ttl: CACHE_TTL.USAGE_METRICS,
      prefix: CACHE_PREFIXES.TENANT_DATA,
    });
  }

  // Get cached tenant usage
  async getTenantUsage(tenantId: string): Promise<TenantUsageData | null> {
    return await cacheService.get<TenantUsageData>(`usage:${tenantId}`, {
      prefix: CACHE_PREFIXES.TENANT_DATA,
    });
  }

  // Increment usage metric
  async incrementUsageMetric(tenantId: string, metric: keyof Omit<TenantUsageData, 'lastUpdated'>, increment: number = 1): Promise<boolean> {
    try {
      const usage = await this.getTenantUsage(tenantId);
      if (!usage) {
        // Initialize usage if not exists
        const newUsage: TenantUsageData = {
          contacts: 0,
          leads: 0,
          users: 0,
          ai_requests: 0,
          storage_mb: 0,
          lastUpdated: Date.now(),
        };
        newUsage[metric] = increment;
        return await this.setTenantUsage(tenantId, newUsage);
      }

      usage[metric] += increment;
      usage.lastUpdated = Date.now();
      return await this.setTenantUsage(tenantId, usage);
    } catch (error) {
      console.error('Error incrementing usage metric:', error);
      return false;
    }
  }

  // Cache tenant settings
  async setTenantSettings(tenantId: string, settings: Record<string, any>): Promise<boolean> {
    return await cacheService.set(`settings:${tenantId}`, settings, {
      ttl: CACHE_TTL.TENANT_DATA,
      prefix: CACHE_PREFIXES.TENANT_DATA,
    });
  }

  // Get cached tenant settings
  async getTenantSettings(tenantId: string): Promise<Record<string, any> | null> {
    return await cacheService.get(`settings:${tenantId}`, {
      prefix: CACHE_PREFIXES.TENANT_DATA,
    });
  }

  // Cache tenant users list
  async setTenantUsers(tenantId: string, users: any[], page: number = 1): Promise<boolean> {
    return await cacheService.set(`users:${tenantId}:page:${page}`, users, {
      ttl: CACHE_TTL.TENANT_DATA,
      prefix: CACHE_PREFIXES.TENANT_DATA,
    });
  }

  // Get cached tenant users
  async getTenantUsers(tenantId: string, page: number = 1): Promise<any[] | null> {
    return await cacheService.get(`users:${tenantId}:page:${page}`, {
      prefix: CACHE_PREFIXES.TENANT_DATA,
    });
  }

  // Invalidate all tenant cache
  async invalidateTenantCache(tenantId: string): Promise<number> {
    const pattern = `*${tenantId}*`;
    return await cacheService.deletePattern(pattern, CACHE_PREFIXES.TENANT_DATA);
  }

  // Invalidate specific tenant data type
  async invalidateTenantDataType(tenantId: string, dataType: 'data' | 'subscription' | 'usage' | 'settings' | 'users'): Promise<boolean> {
    let pattern: string;
    
    switch (dataType) {
      case 'users':
        pattern = `users:${tenantId}:*`;
        break;
      default:
        pattern = `${dataType}:${tenantId}`;
    }

    const deletedCount = await cacheService.deletePattern(pattern, CACHE_PREFIXES.TENANT_DATA);
    return deletedCount > 0;
  }

  // Batch cache tenant data
  async batchSetTenantData(tenantDataMap: Map<string, TenantContextData>): Promise<boolean> {
    const keyValuePairs = Array.from(tenantDataMap.entries()).map(([tenantId, data]) => ({
      key: `data:${tenantId}`,
      value: {
        ...data,
        cachedAt: Date.now(),
        version: 1,
      } as CachedTenantData,
      ttl: CACHE_TTL.TENANT_DATA,
    }));

    return await cacheService.mset(keyValuePairs, CACHE_PREFIXES.TENANT_DATA);
  }

  // Get multiple tenant data
  async batchGetTenantData(tenantIds: string[]): Promise<Map<string, CachedTenantData | null>> {
    const keys = tenantIds.map(id => `data:${id}`);
    const results = await cacheService.mget<CachedTenantData>(keys, CACHE_PREFIXES.TENANT_DATA);
    
    const resultMap = new Map<string, CachedTenantData | null>();
    tenantIds.forEach((tenantId, index) => {
      resultMap.set(tenantId, results[index]);
    });
    
    return resultMap;
  }

  // Get cache statistics for tenant data
  async getTenantCacheStats(): Promise<{
    totalTenants: number;
    cachedTenants: number;
    cacheHitRate: number;
  }> {
    try {
      const pattern = `data:*`;
      const keys = await cacheService['redis'].keys(
        cacheService['buildKey'](pattern, CACHE_PREFIXES.TENANT_DATA)
      );
      
      return {
        totalTenants: 0, // This would need to be fetched from database
        cachedTenants: keys.length,
        cacheHitRate: 0, // This would need to be calculated based on cache hits/misses
      };
    } catch (error) {
      console.error('Error getting tenant cache stats:', error);
      return { totalTenants: 0, cachedTenants: 0, cacheHitRate: 0 };
    }
  }

  // Warm up cache for active tenants
  async warmUpCache(tenantIds: string[], getTenantDataFn: (id: string) => Promise<TenantContextData>): Promise<number> {
    let warmedCount = 0;
    
    for (const tenantId of tenantIds) {
      try {
        const exists = await cacheService.exists(`data:${tenantId}`, CACHE_PREFIXES.TENANT_DATA);
        if (!exists) {
          const tenantData = await getTenantDataFn(tenantId);
          await this.setTenantData(tenantId, tenantData);
          warmedCount++;
        }
      } catch (error) {
        console.error(`Error warming cache for tenant ${tenantId}:`, error);
      }
    }
    
    return warmedCount;
  }
}

export const tenantCache = new TenantCacheService();
