-- Seed data for development environment
-- This will create an initial tenant and admin user

-- Insert default subscription plan
INSERT INTO "subscription_plans" ("id", "name", "description", "price_monthly", "price_yearly", "features", "limits", "is_active", "created_at")
VALUES (
  'plan_starter',
  'Starter',
  'Basic CRM functionality for small businesses',
  49.99,
  499.99,
  '{"lead_management": true, "contact_management": true, "basic_reporting": true}',
  '{"contacts": 1000, "leads": 500, "users": 5, "ai_requests": 100, "storage_mb": 1000}',
  true,
  NOW()
) ON CONFLICT DO NOTHING;

-- Insert default tenant
INSERT INTO "tenants" ("id", "name", "slug", "status", "created_at", "updated_at", "settings", "branding")
VALUES (
  'tenant_default',
  'Demo Company',
  'demo-company',
  'active',
  NOW(),
  NOW(),
  '{"locale": "en", "timezone": "UTC"}',
  '{"logo": null, "primary_color": "#4f46e5", "secondary_color": "#10b981"}'
) ON CONFLICT DO NOTHING;

-- Insert admin user (password: Admin123!)
-- Note: In production, use proper password hashing
INSERT INTO "users" ("id", "email", "password_hash", "first_name", "last_name", "avatar_url", "locale", "timezone", "email_verified", "is_platform_admin", "status", "created_at", "updated_at")
VALUES (
  'user_admin',
  '<EMAIL>',
  '$2a$10$xVqYLGT5JJh/N9rFBR1zn.JbLB5.h9xcBYl1dIEBJfZS.6QtPfmfe', -- hashed 'Admin123!'
  'Admin',
  'User',
  NULL,
  'en',
  'UTC',
  true,
  true,
  'active',
  NOW(),
  NOW()
) ON CONFLICT DO NOTHING;

-- Connect admin user to tenant
INSERT INTO "tenant_users" ("id", "tenant_id", "user_id", "is_tenant_admin", "role", "status", "joined_at")
VALUES (
  'tenant_user_admin',
  'tenant_default',
  'user_admin',
  true,
  'admin',
  'active',
  NOW()
) ON CONFLICT DO NOTHING;

-- Create default roles
INSERT INTO "roles" ("id", "tenant_id", "name", "description", "is_system", "created_at", "updated_at", "created_by")
VALUES 
  ('role_admin', 'tenant_default', 'Admin', 'Full system access', true, NOW(), NOW(), 'user_admin'),
  ('role_user', 'tenant_default', 'User', 'Standard user access', true, NOW(), NOW(), 'user_admin'),
  ('role_viewer', 'tenant_default', 'Viewer', 'Read-only access', true, NOW(), NOW(), 'user_admin')
ON CONFLICT DO NOTHING;

-- Assign admin role to admin user
INSERT INTO "user_roles" ("id", "tenant_id", "user_id", "role_id", "assigned_at", "assigned_by")
VALUES (
  'user_role_admin',
  'tenant_default',
  'user_admin',
  'role_admin',
  NOW(),
  'user_admin'
) ON CONFLICT DO NOTHING;
