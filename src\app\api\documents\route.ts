import { NextResponse } from 'next/server';
import { DocumentManagementService, documentFiltersSchema } from '@/services/document-management';
import { FileStorageService } from '@/lib/file-storage';
import { withPermission, AuthenticatedRequest } from '@/lib/permission-middleware';
import { PermissionResource, PermissionAction } from '@/types';
import { z } from 'zod';

const documentService = new DocumentManagementService();

/**
 * GET /api/documents
 * Get documents with filtering and pagination
 */
export const GET = withPermission({
  resource: PermissionResource.DOCUMENTS,
  action: PermissionAction.READ,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest) => {
  try {
    const { searchParams } = new URL(req.url);
    const filters = Object.fromEntries(searchParams.entries());

    // Parse tags if provided
    if (filters.tags && typeof filters.tags === 'string') {
      try {
        filters.tags = JSON.parse(filters.tags);
      } catch {
        filters.tags = filters.tags.split(',');
      }
    }

    const result = await documentService.getDocuments(req.tenantId, filters);

    return NextResponse.json({
      success: true,
      data: result,
      message: 'Documents retrieved successfully'
    });

  } catch (error) {
    console.error('Get documents error:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          error: 'Invalid query parameters',
          details: error.errors.map(e => ({
            field: e.path.join('.'),
            message: e.message
          }))
        },
        { status: 400 }
      );
    }

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
});

/**
 * POST /api/documents
 * Upload a new document
 */
export const POST = withPermission({
  resource: PermissionResource.DOCUMENTS,
  action: PermissionAction.CREATE,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest) => {
  try {
    const formData = await req.formData();
    const file = formData.get('file') as File;

    if (!file) {
      return NextResponse.json(
        { error: 'No file provided' },
        { status: 400 }
      );
    }

    // Validate file
    const validation = DocumentManagementService.validateFile(file);
    if (!validation.valid) {
      return NextResponse.json(
        { error: validation.error },
        { status: 400 }
      );
    }

    // Generate file path and save file
    const filePath = FileStorageService.generateFilePath(file.name, req.tenantId);
    await FileStorageService.saveFile(file, filePath);

    try {
      // Extract document metadata
      const documentData = {
        name: formData.get('name') as string || file.name,
        filePath,
        fileSize: file.size,
        mimeType: file.type,
        relatedToType: formData.get('relatedToType') as string,
        relatedToId: formData.get('relatedToId') as string,
        description: formData.get('description') as string || undefined,
        tags: formData.get('tags') ? JSON.parse(formData.get('tags') as string) : [],
        isPublic: formData.get('isPublic') === 'true',
      };

      const document = await documentService.createDocument(
        req.tenantId,
        documentData,
        req.userId
      );

      return NextResponse.json({
        success: true,
        data: { document },
        message: 'Document uploaded successfully'
      }, { status: 201 });

    } catch (error) {
      // Clean up file if database operation fails
      try {
        await FileStorageService.deleteFile(filePath);
      } catch (unlinkError) {
        console.error('Failed to clean up file after database error:', unlinkError);
      }
      throw error;
    }

  } catch (error) {
    console.error('Upload document error:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          error: 'Validation failed',
          details: error.errors.map(e => ({
            field: e.path.join('.'),
            message: e.message
          }))
        },
        { status: 400 }
      );
    }

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
});
