'use client';

import React from 'react';
import { cn } from '@/lib/utils';
import { PageHeader, BreadcrumbItem } from '@/components/ui/page-header';
import { PermissionGate } from '@/components/ui/permission-gate';
import { PermissionResource, PermissionAction } from '@/types';

export interface ContactLayoutProps {
  title: string;
  description?: string;
  breadcrumbs?: BreadcrumbItem[];
  backButton?: {
    label?: string;
    onClick: () => void;
  };
  actions?: React.ReactNode;
  children: React.ReactNode;
  className?: string;
  maxWidth?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | '4xl' | '6xl' | '7xl' | 'full';
  requirePermission?: {
    resource: PermissionResource;
    action: PermissionAction;
    resourceOwnerId?: string;
  };
  permissionFallback?: React.ReactNode;
}

export function ContactLayout({
  title,
  description,
  breadcrumbs,
  backButton,
  actions,
  children,
  className,
  maxWidth = '6xl',
  requirePermission,
  permissionFallback
}: ContactLayoutProps) {
  const maxWidthClasses = {
    sm: 'max-w-sm',
    md: 'max-w-md',
    lg: 'max-w-lg',
    xl: 'max-w-xl',
    '2xl': 'max-w-2xl',
    '4xl': 'max-w-4xl',
    '6xl': 'max-w-6xl',
    '7xl': 'max-w-7xl',
    full: 'max-w-full'
  };

  const content = (
    <div className={cn(
      'mx-auto py-6 px-4 sm:px-6 lg:px-8',
      maxWidthClasses[maxWidth],
      className
    )}>
      <PageHeader
        title={title}
        description={description}
        breadcrumbs={breadcrumbs}
        backButton={backButton}
        actions={actions}
      />
      
      <div className="space-y-6">
        {children}
      </div>
    </div>
  );

  if (requirePermission) {
    return (
      <PermissionGate
        resource={requirePermission.resource}
        action={requirePermission.action}
        resourceOwnerId={requirePermission.resourceOwnerId}
        fallback={permissionFallback || (
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <div className="w-12 h-12 text-gray-400 mx-auto mb-4">
                <svg fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                </svg>
              </div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                Access Denied
              </h3>
              <p className="text-gray-500 dark:text-gray-400">
                You don't have permission to access this resource.
              </p>
            </div>
          </div>
        )}
      >
        {content}
      </PermissionGate>
    );
  }

  return content;
}

export interface ContactStatsProps {
  stats: Array<{
    label: string;
    value: string | number;
    change?: {
      value: string;
      trend: 'up' | 'down' | 'neutral';
    };
    icon?: React.ReactNode;
  }>;
  className?: string;
}

export function ContactStats({ stats, className }: ContactStatsProps) {
  return (
    <div className={cn('grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6', className)}>
      {stats.map((stat, index) => (
        <div
          key={index}
          className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6 shadow-sm"
        >
          <div className="flex items-center justify-between">
            <div className="flex-1">
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                {stat.label}
              </p>
              <p className="text-2xl font-semibold text-gray-900 dark:text-white mt-1">
                {stat.value}
              </p>
              {stat.change && (
                <p className={cn(
                  'text-sm mt-1',
                  stat.change.trend === 'up' && 'text-green-600 dark:text-green-400',
                  stat.change.trend === 'down' && 'text-red-600 dark:text-red-400',
                  stat.change.trend === 'neutral' && 'text-gray-600 dark:text-gray-400'
                )}>
                  {stat.change.value}
                </p>
              )}
            </div>
            {stat.icon && (
              <div className="flex-shrink-0 w-8 h-8 text-gray-400">
                {stat.icon}
              </div>
            )}
          </div>
        </div>
      ))}
    </div>
  );
}

export interface ContactQuickActionsProps {
  actions: Array<{
    label: string;
    icon?: React.ReactNode;
    onClick: () => void;
    variant?: 'primary' | 'secondary' | 'outline' | 'ghost';
    permission?: {
      resource: PermissionResource;
      action: PermissionAction;
    };
    disabled?: boolean;
  }>;
  className?: string;
}

export function ContactQuickActions({ actions, className }: ContactQuickActionsProps) {
  return (
    <div className={cn('flex flex-wrap gap-3', className)}>
      {actions.map((action, index) => {
        const button = (
          <button
            key={index}
            onClick={action.onClick}
            disabled={action.disabled}
            className={cn(
              'inline-flex items-center px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2',
              action.variant === 'primary' && 'bg-primary-600 hover:bg-primary-700 focus:ring-primary-500 text-white shadow-sm',
              action.variant === 'secondary' && 'bg-gray-600 hover:bg-gray-700 focus:ring-gray-500 text-white shadow-sm',
              action.variant === 'outline' && 'border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:ring-primary-500 text-gray-700 dark:text-gray-300 shadow-sm',
              (!action.variant || action.variant === 'ghost') && 'hover:bg-gray-100 dark:hover:bg-gray-800 focus:ring-gray-500 text-gray-700 dark:text-gray-300',
              action.disabled && 'opacity-50 cursor-not-allowed'
            )}
          >
            {action.icon && (
              <span className="w-4 h-4 mr-2">
                {action.icon}
              </span>
            )}
            {action.label}
          </button>
        );

        if (action.permission) {
          return (
            <PermissionGate
              key={index}
              resource={action.permission.resource}
              action={action.permission.action}
            >
              {button}
            </PermissionGate>
          );
        }

        return button;
      })}
    </div>
  );
}
