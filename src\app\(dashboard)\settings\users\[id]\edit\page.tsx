'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { PermissionProvider } from '@/components/providers/permission-provider';
import { PermissionGate } from '@/components/ui/permission-gate';
import { PermissionAction, PermissionResource } from '@/types';
import { useTenant } from '@/hooks/use-tenant';
import { PageLayout } from '@/components/layout/page-layout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { UserWithTenantInfo } from '@/services/user-management';
import {
  ArrowLeftIcon,
  UserIcon,
  PencilIcon,
  ExclamationTriangleIcon,
  KeyIcon
} from '@heroicons/react/24/outline';
import { PasswordInput } from '@/components/ui/password-input';
import { PasswordStrengthIndicator } from '@/components/ui/password-strength-indicator';
import { generateUserFriendlyPassword } from '@/lib/client-password-utils';
import { useToast } from '@/hooks/use-toast';

interface Team {
  id: string;
  name: string;
  color: string | null;
}

interface Role {
  id: string;
  name: string;
  description: string | null;
  isSystemRole: boolean;
}

interface EditUserFormData {
  firstName: string;
  lastName: string;
  phone: string;
  role: string;
  teamId: string;
  department: string;
  jobTitle: string;
  status: string;
  locale: string;
  timezone: string;
  changePassword: boolean;
  newPassword: string;
  confirmPassword: string;
}

export default function EditUserPage() {
  const { currentTenant } = useTenant();
  const router = useRouter();
  const params = useParams();
  const userId = params.id as string;
  const { toast } = useToast();

  const [user, setUser] = useState<UserWithTenantInfo | null>(null);
  const [teams, setTeams] = useState<Team[]>([]);
  const [roles, setRoles] = useState<Role[]>([]);
  const [formData, setFormData] = useState<EditUserFormData>({
    firstName: '',
    lastName: '',
    phone: '',
    role: 'user',
    teamId: '',
    department: '',
    jobTitle: '',
    status: 'active',
    locale: 'en',
    timezone: 'UTC',
    changePassword: false,
    newPassword: '',
    confirmPassword: ''
  });

  const [isLoading, setIsLoading] = useState(true);
  const [isLoadingTeams, setIsLoadingTeams] = useState(true);
  const [isLoadingRoles, setIsLoadingRoles] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (currentTenant && userId) {
      fetchUserDetails();
      fetchTeams();
      fetchRoles();
    }
  }, [currentTenant, userId]);

  const fetchUserDetails = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch(`/api/users/${userId}`);
      
      if (!response.ok) {
        if (response.status === 404) {
          throw new Error('User not found');
        }
        throw new Error('Failed to fetch user details');
      }

      const data = await response.json();
      
      if (data.success) {
        const userData = data.data;
        setUser(userData);
        
        // Populate form with user data
        setFormData({
          firstName: userData.firstName || '',
          lastName: userData.lastName || '',
          phone: userData.phone || '',
          role: userData.tenantUser.role || 'user',
          teamId: userData.tenantUser.teamId || '',
          department: userData.tenantUser.department || '',
          jobTitle: userData.tenantUser.jobTitle || '',
          status: userData.tenantUser.status || 'active',
          locale: userData.locale || 'en',
          timezone: userData.timezone || 'UTC',
          changePassword: false,
          newPassword: '',
          confirmPassword: ''
        });
      } else {
        throw new Error('Failed to fetch user details');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch user details');
    } finally {
      setIsLoading(false);
    }
  };

  const fetchTeams = async () => {
    try {
      setIsLoadingTeams(true);
      const response = await fetch('/api/teams');

      if (response.ok) {
        const data = await response.json();
        // Ensure we always set an array
        const teamsData = data.data;
        setTeams(Array.isArray(teamsData) ? teamsData : []);
      } else {
        console.error('Failed to fetch teams: HTTP', response.status);
        setTeams([]);
      }
    } catch (err) {
      console.error('Failed to fetch teams:', err);
      setTeams([]);
    } finally {
      setIsLoadingTeams(false);
    }
  };

  const fetchRoles = async () => {
    try {
      setIsLoadingRoles(true);
      const response = await fetch('/api/roles/assignable');

      if (response.ok) {
        const data = await response.json();
        // Ensure we always set an array
        const rolesData = data.data;
        setRoles(Array.isArray(rolesData) ? rolesData : []);
      } else {
        console.error('Failed to fetch roles: HTTP', response.status);
        setRoles([]);
      }
    } catch (err) {
      console.error('Failed to fetch roles:', err);
      setRoles([]);
    } finally {
      setIsLoadingRoles(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value, type, checked } = e.target as HTMLInputElement;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const handleGeneratePassword = () => {
    const newPassword = generateUserFriendlyPassword();
    setFormData(prev => ({
      ...prev,
      newPassword: newPassword,
      confirmPassword: newPassword
    }));
    toast({
      title: 'Password Generated',
      description: 'A secure password has been generated for you.',
      variant: 'success'
    });
  };

  const validateForm = (): string | null => {
    if (!formData.firstName.trim()) return 'First name is required';
    if (!formData.lastName.trim()) return 'Last name is required';

    if (formData.changePassword) {
      if (!formData.newPassword) return 'New password is required';
      if (formData.newPassword.length < 8) return 'Password must be at least 8 characters';
      if (formData.newPassword !== formData.confirmPassword) return 'Passwords do not match';
    }

    return null;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    const validationError = validateForm();
    if (validationError) {
      setError(validationError);
      return;
    }

    setIsSaving(true);
    setError(null);

    try {
      // Update user profile
      const profileResponse = await fetch(`/api/users/${userId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          updateType: 'profile',
          firstName: formData.firstName.trim(),
          lastName: formData.lastName.trim(),
          locale: formData.locale,
          timezone: formData.timezone,
          status: formData.status
        }),
      });

      if (!profileResponse.ok) {
        const errorData = await profileResponse.json();
        throw new Error(errorData.error || 'Failed to update user profile');
      }

      // Update tenant information
      const tenantResponse = await fetch(`/api/users/${userId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          updateType: 'tenant',
          role: formData.role,
          teamId: formData.teamId || undefined,
          department: formData.department.trim() || undefined,
          jobTitle: formData.jobTitle.trim() || undefined,
          status: formData.status
        }),
      });

      if (!tenantResponse.ok) {
        const errorData = await tenantResponse.json();
        throw new Error(errorData.error || 'Failed to update user tenant information');
      }

      // Update password if requested
      if (formData.changePassword && formData.newPassword) {
        const passwordResponse = await fetch(`/api/users/${userId}/password`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            newPassword: formData.newPassword
          }),
        });

        if (!passwordResponse.ok) {
          const errorData = await passwordResponse.json();
          throw new Error(errorData.error || 'Failed to update password');
        }
      }

      toast({
        title: 'User Updated Successfully',
        description: `${formData.firstName} ${formData.lastName} has been updated.${formData.changePassword ? ' Password has been changed.' : ''}`,
        variant: 'success'
      });

      router.push(`/settings/users/${userId}`);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update user');
    } finally {
      setIsSaving(false);
    }
  };

  const handleCancel = () => {
    router.push(`/settings/users/${userId}`);
  };

  if (!currentTenant) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <ExclamationTriangleIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-500">No tenant context available</p>
        </div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <PageLayout title="Loading..." description="Loading user details...">
        <div className="flex items-center justify-center h-64">
          <LoadingSpinner />
        </div>
      </PageLayout>
    );
  }

  if (error && !user) {
    return (
      <PageLayout title="Error" description="Failed to load user details">
        <div className="text-center py-12">
          <ExclamationTriangleIcon className="w-12 h-12 text-red-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Error</h3>
          <p className="text-gray-500 mb-4">{error}</p>
          <Button onClick={() => router.push('/settings/users')}>
            <ArrowLeftIcon className="w-4 h-4 mr-2" />
            Back to Users
          </Button>
        </div>
      </PageLayout>
    );
  }

  const actions = (
    <div className="flex items-center gap-2">
      <Button variant="outline" onClick={handleCancel}>
        <ArrowLeftIcon className="w-4 h-4 mr-2" />
        Back to User
      </Button>
    </div>
  );

  return (
    <PermissionProvider>
      <PageLayout
        title={`Edit ${user?.firstName} ${user?.lastName}`}
        description="Update user information and settings"
        actions={actions}
      >
        <PermissionGate
          resource={PermissionResource.USERS}
          action={PermissionAction.UPDATE}
          fallback={<AccessDeniedMessage />}
        >
          <div className="max-w-2xl mx-auto">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <PencilIcon className="w-5 h-5" />
                  Edit User Information
                </CardTitle>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleSubmit} className="space-y-6">
                  {error && (
                    <div className="bg-red-50 border border-red-200 rounded-md p-4">
                      <div className="flex">
                        <ExclamationTriangleIcon className="w-5 h-5 text-red-400 mr-2 flex-shrink-0 mt-0.5" />
                        <p className="text-sm text-red-600">{error}</p>
                      </div>
                    </div>
                  )}

                  {/* Basic Information */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label htmlFor="firstName" className="block text-sm font-medium text-gray-700 mb-1">
                        First Name *
                      </label>
                      <input
                        type="text"
                        id="firstName"
                        name="firstName"
                        value={formData.firstName}
                        onChange={handleInputChange}
                        required
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder="Enter first name"
                      />
                    </div>

                    <div>
                      <label htmlFor="lastName" className="block text-sm font-medium text-gray-700 mb-1">
                        Last Name *
                      </label>
                      <input
                        type="text"
                        id="lastName"
                        name="lastName"
                        value={formData.lastName}
                        onChange={handleInputChange}
                        required
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder="Enter last name"
                      />
                    </div>
                  </div>

                  {/* Role and Team */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label htmlFor="role" className="block text-sm font-medium text-gray-700 mb-1">
                        Role
                      </label>
                      <select
                        id="role"
                        name="role"
                        value={formData.role}
                        onChange={handleInputChange}
                        disabled={isLoadingRoles}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-50"
                      >
                        {isLoadingRoles ? (
                          <option value="">Loading roles...</option>
                        ) : roles.length === 0 ? (
                          <option value="">No roles available</option>
                        ) : (
                          roles.map((role) => (
                            <option key={role.id} value={role.name}>
                              {role.name}
                              {role.description && ` - ${role.description}`}
                            </option>
                          ))
                        )}
                      </select>
                    </div>

                    <div>
                      <label htmlFor="teamId" className="block text-sm font-medium text-gray-700 mb-1">
                        Team
                      </label>
                      <select
                        id="teamId"
                        name="teamId"
                        value={formData.teamId}
                        onChange={handleInputChange}
                        disabled={isLoadingTeams}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-50"
                      >
                        <option value="">No team</option>
                        {isLoadingTeams ? (
                          <option value="">Loading teams...</option>
                        ) : Array.isArray(teams) && teams.length > 0 ? (
                          teams.map((team) => (
                            <option key={team.id} value={team.id}>
                              {team.name}
                            </option>
                          ))
                        ) : (
                          <option value="">No teams available</option>
                        )}
                      </select>
                    </div>
                  </div>

                  {/* Additional Information */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label htmlFor="department" className="block text-sm font-medium text-gray-700 mb-1">
                        Department
                      </label>
                      <input
                        type="text"
                        id="department"
                        name="department"
                        value={formData.department}
                        onChange={handleInputChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder="Enter department"
                      />
                    </div>

                    <div>
                      <label htmlFor="jobTitle" className="block text-sm font-medium text-gray-700 mb-1">
                        Job Title
                      </label>
                      <input
                        type="text"
                        id="jobTitle"
                        name="jobTitle"
                        value={formData.jobTitle}
                        onChange={handleInputChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder="Enter job title"
                      />
                    </div>
                  </div>

                  {/* Status and Preferences */}
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-1">
                        Status
                      </label>
                      <select
                        id="status"
                        name="status"
                        value={formData.status}
                        onChange={handleInputChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      >
                        <option value="active">Active</option>
                        <option value="inactive">Inactive</option>
                        <option value="pending">Pending</option>
                      </select>
                    </div>

                    <div>
                      <label htmlFor="locale" className="block text-sm font-medium text-gray-700 mb-1">
                        Language
                      </label>
                      <select
                        id="locale"
                        name="locale"
                        value={formData.locale}
                        onChange={handleInputChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      >
                        <option value="en">English</option>
                        <option value="ar">Arabic</option>
                      </select>
                    </div>

                    <div>
                      <label htmlFor="timezone" className="block text-sm font-medium text-gray-700 mb-1">
                        Timezone
                      </label>
                      <select
                        id="timezone"
                        name="timezone"
                        value={formData.timezone}
                        onChange={handleInputChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      >
                        <option value="UTC">UTC</option>
                        <option value="America/New_York">Eastern Time</option>
                        <option value="America/Chicago">Central Time</option>
                        <option value="America/Denver">Mountain Time</option>
                        <option value="America/Los_Angeles">Pacific Time</option>
                        <option value="Europe/London">London</option>
                        <option value="Europe/Paris">Paris</option>
                        <option value="Asia/Dubai">Dubai</option>
                        <option value="Asia/Tokyo">Tokyo</option>
                      </select>
                    </div>
                  </div>

                  {/* Password Change Section */}
                  <div className="space-y-4 pt-6 border-t border-gray-200">
                    <div className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        id="changePassword"
                        name="changePassword"
                        checked={formData.changePassword}
                        onChange={handleInputChange}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                      <label htmlFor="changePassword" className="text-sm font-medium text-gray-700">
                        Change Password
                      </label>
                    </div>

                    {formData.changePassword && (
                      <div className="space-y-4 ml-6">
                        <div className="flex items-center justify-between">
                          <h4 className="text-sm font-medium text-gray-900">New Password</h4>
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={handleGeneratePassword}
                            className="flex items-center gap-2"
                          >
                            <KeyIcon className="w-4 h-4" />
                            Generate Password
                          </Button>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <label htmlFor="newPassword" className="block text-sm font-medium text-gray-700 mb-1">
                              New Password *
                            </label>
                            <PasswordInput
                              id="newPassword"
                              name="newPassword"
                              value={formData.newPassword}
                              onChange={handleInputChange}
                              required
                              placeholder="Enter new password"
                            />
                          </div>

                          <div>
                            <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-700 mb-1">
                              Confirm Password *
                            </label>
                            <PasswordInput
                              id="confirmPassword"
                              name="confirmPassword"
                              value={formData.confirmPassword}
                              onChange={handleInputChange}
                              required
                              placeholder="Confirm new password"
                              error={formData.newPassword && formData.confirmPassword && formData.newPassword !== formData.confirmPassword ? 'Passwords do not match' : undefined}
                            />
                          </div>
                        </div>

                        {/* Password Strength Indicator */}
                        {formData.newPassword && (
                          <PasswordStrengthIndicator
                            password={formData.newPassword}
                            minLength={8}
                            showRequirements={true}
                            className="mt-3"
                          />
                        )}
                      </div>
                    )}
                  </div>

                  {/* Form Actions */}
                  <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={handleCancel}
                      disabled={isSaving}
                    >
                      Cancel
                    </Button>
                    <Button
                      type="submit"
                      disabled={isSaving}
                      className="flex items-center gap-2"
                    >
                      {isSaving ? (
                        <>
                          <LoadingSpinner size="sm" />
                          Saving...
                        </>
                      ) : (
                        <>
                          <PencilIcon className="w-4 h-4" />
                          Save Changes
                        </>
                      )}
                    </Button>
                  </div>
                </form>
              </CardContent>
            </Card>
          </div>
        </PermissionGate>
      </PageLayout>
    </PermissionProvider>
  );
}

function AccessDeniedMessage() {
  return (
    <div className="text-center py-12">
      <ExclamationTriangleIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
      <h3 className="text-lg font-medium text-gray-900 mb-2">Access Denied</h3>
      <p className="text-gray-500 max-w-md mx-auto">
        You don't have permission to edit users.
      </p>
    </div>
  );
}
