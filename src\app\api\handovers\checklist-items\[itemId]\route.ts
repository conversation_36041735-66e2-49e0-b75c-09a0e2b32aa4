import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { handoverChecklistService } from '@/services/handover-checklist';
import { z } from 'zod';

// Request validation schemas
const updateChecklistItemSchema = z.object({
  title: z.string().min(1, 'Title is required').max(255).optional(),
  description: z.string().optional(),
  category: z.enum(['general', 'technical', 'legal', 'financial', 'client']).optional(),
  orderIndex: z.number().int().min(0).optional(),
  isRequired: z.boolean().optional(),
  isCompleted: z.boolean().optional(),
  notes: z.string().optional(),
  dependsOnIds: z.array(z.string()).optional(),
});

interface RouteParams {
  params: {
    itemId: string;
  };
}

/**
 * PUT /api/handovers/checklist-items/[itemId] - Update a checklist item
 */
export async function PUT(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get tenantId from user's tenants array (users have one tenant in this system)
    let tenantId: string | null = null;
    if (session.user.tenants && session.user.tenants.length > 0) {
      tenantId = session.user.tenants[0].id;
    }

    if (!tenantId) {
      return NextResponse.json({ error: 'Tenant not found' }, { status: 400 });
    }

    const body = await request.json();
    const validatedData = updateChecklistItemSchema.parse(body);

    const resolvedParams = await params;
    const item = await handoverChecklistService.updateChecklistItem(
      tenantId,
      resolvedParams.itemId,
      validatedData,
      session.user.id
    );

    return NextResponse.json(item);
  } catch (error) {
    console.error('Error updating checklist item:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request data', details: error.errors },
        { status: 400 }
      );
    }

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to update checklist item' },
      { status: 500 }
    );
  }
}
