import { prisma } from '@/lib/prisma';
import { z } from 'zod';
import { UnifiedNotificationService } from './unified-notification-service';

// External integration event types
export type IntegrationEventType = 
  | 'sync_started' 
  | 'sync_completed' 
  | 'sync_failed' 
  | 'webhook_received' 
  | 'webhook_failed'
  | 'api_rate_limit'
  | 'api_error'
  | 'connection_lost'
  | 'connection_restored';

// Validation schemas
export const integrationEventSchema = z.object({
  tenantId: z.string(),
  integrationId: z.string(),
  integrationType: z.string(), // 'salesforce', 'hubspot', 'zapier', etc.
  eventType: z.enum(['sync_started', 'sync_completed', 'sync_failed', 'webhook_received', 'webhook_failed', 'api_rate_limit', 'api_error', 'connection_lost', 'connection_restored']),
  timestamp: z.date().optional(),
  message: z.string(),
  details: z.record(z.any()).optional(),
  severity: z.enum(['info', 'warning', 'error', 'critical']).default('info'),
  recordsAffected: z.number().optional(),
  errorCode: z.string().optional(),
  retryCount: z.number().optional(),
});

export const bulkIntegrationEventSchema = z.object({
  events: z.array(integrationEventSchema),
});

export type IntegrationEvent = z.infer<typeof integrationEventSchema>;
export type BulkIntegrationEvent = z.infer<typeof bulkIntegrationEventSchema>;

export interface IntegrationLogWithDetails {
  id: string;
  tenantId: string;
  integrationId: string;
  integrationType: string;
  eventType: string;
  message: string;
  details: Record<string, any>;
  severity: string;
  recordsAffected?: number;
  errorCode?: string;
  retryCount?: number;
  timestamp: Date;
  createdAt: Date;
}

export class ExternalIntegrationNotificationService {
  private notificationService = new UnifiedNotificationService();

  /**
   * Process integration event and trigger notifications
   */
  async processIntegrationEvent(event: IntegrationEvent): Promise<void> {
    const validatedEvent = integrationEventSchema.parse(event);

    try {
      // Log the integration event
      const integrationLog = await this.logIntegrationEvent(validatedEvent);

      // Trigger appropriate notifications based on event type and severity
      await this.triggerIntegrationNotification(validatedEvent, integrationLog);

    } catch (error) {
      console.error('Error processing integration event:', error);
      throw error;
    }
  }

  /**
   * Process bulk integration events
   */
  async processBulkIntegrationEvents(bulkEvent: BulkIntegrationEvent): Promise<void> {
    const validatedBulkEvent = bulkIntegrationEventSchema.parse(bulkEvent);

    const results = await Promise.allSettled(
      validatedBulkEvent.events.map(event => this.processIntegrationEvent(event))
    );

    // Log any failures
    results.forEach((result, index) => {
      if (result.status === 'rejected') {
        console.error(`Failed to process integration event ${index}:`, result.reason);
      }
    });
  }

  /**
   * Log integration event to database
   */
  private async logIntegrationEvent(event: IntegrationEvent): Promise<IntegrationLogWithDetails> {
    const integrationLog = await prisma.integrationLog.create({
      data: {
        tenantId: event.tenantId,
        integrationId: event.integrationId,
        integrationType: event.integrationType,
        eventType: event.eventType,
        message: event.message,
        details: event.details || {},
        severity: event.severity,
        recordsAffected: event.recordsAffected,
        errorCode: event.errorCode,
        retryCount: event.retryCount,
        timestamp: event.timestamp || new Date(),
      }
    });

    return integrationLog as IntegrationLogWithDetails;
  }

  /**
   * Trigger integration notifications based on event type and severity
   */
  private async triggerIntegrationNotification(
    event: IntegrationEvent,
    integrationLog: IntegrationLogWithDetails
  ): Promise<void> {
    try {
      // Only notify on significant events
      const notifiableEvents: IntegrationEventType[] = [
        'sync_failed', 'webhook_failed', 'api_rate_limit', 'api_error', 
        'connection_lost', 'connection_restored'
      ];
      
      // Also notify on successful syncs if they affect many records
      if (event.eventType === 'sync_completed' && (event.recordsAffected || 0) > 100) {
        notifiableEvents.push('sync_completed');
      }

      if (!notifiableEvents.includes(event.eventType)) {
        return;
      }

      // Get target users (administrators and integration managers)
      const targetUsers = await this.getIntegrationNotificationTargets(event.tenantId);

      // Determine notification details based on event type
      const notificationDetails = this.getNotificationDetails(event, integrationLog);

      for (const userId of targetUsers) {
        await this.notificationService.createNotification({
          tenantId: event.tenantId,
          userId,
          type: notificationDetails.type,
          category: 'system',
          title: notificationDetails.title,
          message: notificationDetails.message,
          data: {
            integrationLogId: integrationLog.id,
            integrationId: event.integrationId,
            integrationType: event.integrationType,
            eventType: event.eventType,
            severity: event.severity,
            recordsAffected: event.recordsAffected,
            errorCode: event.errorCode,
            retryCount: event.retryCount,
            timestamp: integrationLog.timestamp.toISOString(),
            details: event.details
          },
          actionUrl: `/admin/integrations/${event.integrationId}/logs`,
          actionLabel: 'View Integration Logs'
        });
      }

      // For critical errors, also notify senior management
      if (event.severity === 'critical') {
        await this.triggerCriticalIntegrationAlert(event, integrationLog);
      }

    } catch (error) {
      console.error('Failed to send integration notification:', error);
    }
  }

  /**
   * Get notification targets for integration events
   */
  private async getIntegrationNotificationTargets(tenantId: string): Promise<string[]> {
    const targetUsers = await prisma.tenantUser.findMany({
      where: {
        tenantId,
        OR: [
          { role: { in: ['Admin', 'IT Manager', 'System Administrator'] } },
          { isTenantAdmin: true }
        ],
        status: 'active'
      },
      select: { userId: true }
    });

    return targetUsers.map(user => user.userId);
  }

  /**
   * Get notification details based on event type
   */
  private getNotificationDetails(
    event: IntegrationEvent,
    integrationLog: IntegrationLogWithDetails
  ): { type: string; title: string; message: string } {
    const integrationName = this.getIntegrationDisplayName(event.integrationType);

    switch (event.eventType) {
      case 'sync_completed':
        return {
          type: 'integration_sync_completed',
          title: 'Large Data Sync Completed',
          message: `${integrationName} sync completed successfully with ${event.recordsAffected} records processed`
        };

      case 'sync_failed':
        return {
          type: 'integration_sync_failed',
          title: 'Integration Sync Failed',
          message: `${integrationName} sync failed: ${event.message}`
        };

      case 'webhook_failed':
        return {
          type: 'integration_webhook_failed',
          title: 'Webhook Processing Failed',
          message: `Failed to process ${integrationName} webhook: ${event.message}`
        };

      case 'api_rate_limit':
        return {
          type: 'integration_rate_limit',
          title: 'API Rate Limit Reached',
          message: `${integrationName} API rate limit reached. Service may be temporarily unavailable`
        };

      case 'api_error':
        return {
          type: 'integration_api_error',
          title: 'Integration API Error',
          message: `${integrationName} API error: ${event.message}`
        };

      case 'connection_lost':
        return {
          type: 'integration_connection_lost',
          title: 'Integration Connection Lost',
          message: `Lost connection to ${integrationName}. Attempting to reconnect...`
        };

      case 'connection_restored':
        return {
          type: 'integration_connection_restored',
          title: 'Integration Connection Restored',
          message: `Connection to ${integrationName} has been restored successfully`
        };

      default:
        return {
          type: 'integration_event',
          title: 'Integration Event',
          message: `${integrationName}: ${event.message}`
        };
    }
  }

  /**
   * Trigger critical integration alert
   */
  private async triggerCriticalIntegrationAlert(
    event: IntegrationEvent,
    integrationLog: IntegrationLogWithDetails
  ): Promise<void> {
    try {
      // Get senior management and executives
      const seniorManagement = await prisma.tenantUser.findMany({
        where: {
          tenantId: event.tenantId,
          OR: [
            { role: { in: ['CEO', 'CTO', 'VP', 'Director', 'Senior Manager'] } },
            { isTenantAdmin: true }
          ],
          status: 'active'
        },
        select: { userId: true }
      });

      const integrationName = this.getIntegrationDisplayName(event.integrationType);

      for (const user of seniorManagement) {
        await this.notificationService.createNotification({
          tenantId: event.tenantId,
          userId: user.userId,
          type: 'critical_integration_alert',
          category: 'system',
          title: 'Critical Integration Failure',
          message: `CRITICAL: ${integrationName} integration has encountered a critical error that requires immediate attention`,
          data: {
            integrationLogId: integrationLog.id,
            integrationId: event.integrationId,
            integrationType: event.integrationType,
            errorCode: event.errorCode,
            severity: event.severity,
            timestamp: integrationLog.timestamp.toISOString()
          },
          actionUrl: `/admin/integrations/${event.integrationId}`,
          actionLabel: 'View Integration'
        });
      }
    } catch (error) {
      console.error('Failed to send critical integration alert:', error);
    }
  }

  /**
   * Get display name for integration type
   */
  private getIntegrationDisplayName(integrationType: string): string {
    const displayNames: Record<string, string> = {
      'salesforce': 'Salesforce',
      'hubspot': 'HubSpot',
      'zapier': 'Zapier',
      'mailchimp': 'Mailchimp',
      'slack': 'Slack',
      'microsoft': 'Microsoft',
      'google': 'Google Workspace',
      'zoom': 'Zoom',
      'calendly': 'Calendly',
      'stripe': 'Stripe',
      'quickbooks': 'QuickBooks'
    };

    return displayNames[integrationType.toLowerCase()] || integrationType;
  }

  /**
   * Get integration statistics for a tenant
   */
  async getIntegrationStats(
    tenantId: string,
    options: {
      startDate?: Date;
      endDate?: Date;
      integrationType?: string;
    } = {}
  ): Promise<{
    totalEvents: number;
    successfulSyncs: number;
    failedSyncs: number;
    webhookEvents: number;
    apiErrors: number;
    connectionIssues: number;
    errorRate: number;
  }> {
    const { startDate, endDate, integrationType } = options;

    const where: any = {
      tenantId,
      ...(startDate && { timestamp: { gte: startDate } }),
      ...(endDate && { timestamp: { lte: endDate } }),
      ...(integrationType && { integrationType }),
    };

    const [
      totalEvents,
      successfulSyncs,
      failedSyncs,
      webhookEvents,
      apiErrors,
      connectionIssues
    ] = await Promise.all([
      prisma.integrationLog.count({ where }),
      prisma.integrationLog.count({ where: { ...where, eventType: 'sync_completed' } }),
      prisma.integrationLog.count({ where: { ...where, eventType: 'sync_failed' } }),
      prisma.integrationLog.count({ where: { ...where, eventType: { in: ['webhook_received', 'webhook_failed'] } } }),
      prisma.integrationLog.count({ where: { ...where, eventType: 'api_error' } }),
      prisma.integrationLog.count({ where: { ...where, eventType: { in: ['connection_lost', 'connection_restored'] } } }),
    ]);

    const totalSyncs = successfulSyncs + failedSyncs;
    const errorRate = totalSyncs > 0 ? (failedSyncs / totalSyncs) * 100 : 0;

    return {
      totalEvents,
      successfulSyncs,
      failedSyncs,
      webhookEvents,
      apiErrors,
      connectionIssues,
      errorRate: Math.round(errorRate * 100) / 100,
    };
  }
}

// Export singleton instance
export const externalIntegrationNotificationService = new ExternalIntegrationNotificationService();
