import { useCallback } from 'react';
import { 
  formatCurrency, 
  formatNumber, 
  formatCompactCurrency, 
  formatPercentage,
  parseCurrency,
  isValidCurrency
} from '@/lib/formatters';

/**
 * Custom hook that provides formatting functions with consistent defaults
 * Can be customized per component if needed
 */
export function useFormatters(defaultCurrency: string = 'USD', defaultLocale: string = 'en-US') {
  const formatMoney = useCallback((
    value: number | string | null | undefined,
    options?: {
      currency?: string;
      locale?: string;
      minimumFractionDigits?: number;
      maximumFractionDigits?: number;
    }
  ) => {
    return formatCurrency(
      value,
      options?.currency || defaultCurrency,
      options?.locale || defaultLocale,
      options?.minimumFractionDigits,
      options?.maximumFractionDigits
    );
  }, [defaultCurrency, defaultLocale]);

  const formatNumberWithCommas = useCallback((
    value: number | string | null | undefined,
    options?: {
      locale?: string;
      minimumFractionDigits?: number;
      maximumFractionDigits?: number;
    }
  ) => {
    return formatNumber(
      value,
      options?.locale || defaultLocale,
      options?.minimumFractionDigits,
      options?.maximumFractionDigits
    );
  }, [defaultLocale]);

  const formatCompactMoney = useCallback((
    value: number | string | null | undefined,
    options?: {
      currency?: string;
      locale?: string;
    }
  ) => {
    return formatCompactCurrency(
      value,
      options?.currency || defaultCurrency,
      options?.locale || defaultLocale
    );
  }, [defaultCurrency, defaultLocale]);

  const formatPercent = useCallback((
    value: number | string | null | undefined,
    options?: {
      locale?: string;
      minimumFractionDigits?: number;
      maximumFractionDigits?: number;
    }
  ) => {
    return formatPercentage(
      value,
      options?.locale || defaultLocale,
      options?.minimumFractionDigits,
      options?.maximumFractionDigits
    );
  }, [defaultLocale]);

  return {
    formatMoney,
    formatNumberWithCommas,
    formatCompactMoney,
    formatPercent,
    parseCurrency,
    isValidCurrency,
  };
}
