import { NextResponse } from 'next/server';
import { z } from 'zod';
import { withPermission, AuthenticatedRequest } from '@/lib/permission-middleware';
import { scheduledMeetingService } from '@/services/scheduled-meeting-service';
import { meetingInvitationService } from '@/services/meeting-invitation-service';
import { PermissionAction, PermissionResource } from '@/types';

const sendInvitationSchema = z.object({
  recipientEmail: z.string().email('Invalid email address'),
  recipientName: z.string().optional(),
  organizerName: z.string().min(1, 'Organizer name is required'),
  organizerEmail: z.string().email('Invalid organizer email'),
  companyName: z.string().optional(),
  customMessage: z.string().optional(),
});

/**
 * POST /api/meetings/[meetingId]/send-invitation
 * Send meeting invitation email
 */
export const POST = withPermission({
  resource: PermissionResource.MEETINGS,
  action: PermissionAction.UPDATE,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest) => {
  try {
    const url = new URL(req.url);
    const pathSegments = url.pathname.split('/');
    const meetingId = pathSegments[pathSegments.indexOf('meetings') + 1];
    
    const body = await req.json();
    const validatedData = sendInvitationSchema.parse(body);

    // Get the meeting
    const meeting = await scheduledMeetingService.getScheduledMeeting(meetingId, req.tenantId);
    if (!meeting) {
      return NextResponse.json(
        { error: 'Meeting not found' },
        { status: 404 }
      );
    }

    // Send the invitation
    const result = await meetingInvitationService.sendInvitation(
      {
        meeting,
        organizerName: validatedData.organizerName,
        organizerEmail: validatedData.organizerEmail,
        companyName: validatedData.companyName,
      },
      validatedData.recipientEmail,
      validatedData.recipientName
    );

    if (!result.success) {
      return NextResponse.json(
        { error: result.error || 'Failed to send invitation' },
        { status: 500 }
      );
    }

    // Update meeting to mark invitation as sent
    await scheduledMeetingService.updateScheduledMeeting(
      meetingId,
      req.tenantId,
      { invitationSent: true }
    );

    return NextResponse.json({
      success: true,
      data: { 
        messageId: result.messageId,
        recipientEmail: validatedData.recipientEmail
      },
      message: 'Meeting invitation sent successfully'
    });

  } catch (error) {
    console.error('Send meeting invitation error:', error);

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
});

/**
 * GET /api/meetings/[meetingId]/send-invitation
 * Generate meeting invitation content for email composer
 */
export const GET = withPermission({
  resource: PermissionResource.MEETINGS,
  action: PermissionAction.READ,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest) => {
  try {
    const url = new URL(req.url);
    const pathSegments = url.pathname.split('/');
    const meetingId = pathSegments[pathSegments.indexOf('meetings') + 1];

    const { searchParams } = url;
    const organizerName = searchParams.get('organizerName') || 'Meeting Organizer';
    const organizerEmail = searchParams.get('organizerEmail') || '<EMAIL>';
    const companyName = searchParams.get('companyName') || undefined;
    const recipientEmail = searchParams.get('recipientEmail') || '';

    // Get the meeting
    const meeting = await scheduledMeetingService.getScheduledMeeting(meetingId, req.tenantId);
    if (!meeting) {
      return NextResponse.json(
        { error: 'Meeting not found' },
        { status: 404 }
      );
    }

    // Generate invitation content
    const invitation = await meetingInvitationService.generateInvitationContent({
      meeting,
      organizerName,
      organizerEmail,
      companyName,
    });

    return NextResponse.json({
      success: true,
      data: {
        subject: invitation.subject,
        content: invitation.textContent, // Use text content for email composer
        htmlContent: invitation.htmlContent,
        recipientEmail: recipientEmail,
        meeting: {
          id: meeting.id,
          title: meeting.title,
          scheduledAt: meeting.scheduledAt,
          duration: meeting.duration,
          meetingType: meeting.meetingType,
          meetingLink: meeting.meetingLink,
          location: meeting.location,
        },
        organizer: {
          name: organizerName,
          email: organizerEmail,
          company: companyName
        }
      },
      message: 'Meeting invitation content generated successfully'
    });

  } catch (error) {
    console.error('Generate meeting invitation content error:', error);

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
});
