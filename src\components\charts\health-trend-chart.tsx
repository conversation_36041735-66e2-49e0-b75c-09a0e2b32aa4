'use client';

import React from 'react';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  ReferenceLine
} from 'recharts';
import { BaseChart, BaseChartProps } from './base-chart';
import { SystemHealthHistory } from '@/services/super-admin';

interface HealthTrendChartProps extends Omit<BaseChartProps, 'children'> {
  data: SystemHealthHistory[];
  timeRange: string;
}

export function HealthTrendChart({
  data,
  timeRange,
  ...baseProps
}: HealthTrendChartProps) {
  const formatTime = (timestamp: string | Date) => {
    const date = new Date(timestamp);
    
    if (timeRange === '15m' || timeRange === '30m' || timeRange === '1h') {
      return date.toLocaleTimeString('en-US', { 
        hour: '2-digit', 
        minute: '2-digit',
        hour12: false 
      });
    } else if (timeRange === '6h' || timeRange === '12h') {
      return date.toLocaleTimeString('en-US', { 
        hour: '2-digit', 
        minute: '2-digit',
        hour12: true 
      });
    } else {
      return date.toLocaleDateString('en-US', { 
        month: 'short', 
        day: 'numeric',
        hour: '2-digit'
      });
    }
  };

  const formatTooltipValue = (value: number, name: string) => {
    switch (name) {
      case 'responseTime':
        return [`${Math.round(value)}ms`, 'Response Time'];
      case 'uptime':
        return [`${value.toFixed(2)}%`, 'Uptime'];
      case 'errorRate':
        return [`${value.toFixed(2)}%`, 'Error Rate'];
      case 'memoryUsage':
        return [`${value.toFixed(1)}%`, 'Memory Usage'];
      case 'cpuUsage':
        return [`${value.toFixed(1)}%`, 'CPU Usage'];
      case 'databaseConnections':
        return [`${Math.round(value)}`, 'DB Connections'];
      default:
        return [value.toString(), name];
    }
  };

  const formatTooltipLabel = (label: string) => {
    return `Time: ${formatTime(label)}`;
  };

  // Transform data for chart
  const chartData = data.map(item => ({
    ...item,
    timestamp: item.timestamp.toString(),
    formattedTime: formatTime(item.timestamp),
  }));

  return (
    <BaseChart {...baseProps}>
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Response Time & Error Rate */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-4">
          <h4 className="text-sm font-semibold mb-4">Response Time & Error Rate</h4>
          <ResponsiveContainer width="100%" height={250}>
            <LineChart data={chartData}>
              <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
              <XAxis 
                dataKey="formattedTime" 
                className="text-xs"
                interval="preserveStartEnd"
              />
              <YAxis 
                yAxisId="responseTime"
                orientation="left"
                className="text-xs"
                label={{ value: 'Response Time (ms)', angle: -90, position: 'insideLeft' }}
              />
              <YAxis 
                yAxisId="errorRate"
                orientation="right"
                className="text-xs"
                label={{ value: 'Error Rate (%)', angle: 90, position: 'insideRight' }}
              />
              <Tooltip
                formatter={formatTooltipValue}
                labelFormatter={formatTooltipLabel}
                contentStyle={{
                  backgroundColor: 'hsl(var(--card))',
                  border: '1px solid hsl(var(--border))',
                  borderRadius: '6px',
                  fontSize: '12px'
                }}
              />
              <Legend />
              
              <Line
                yAxisId="responseTime"
                type="monotone"
                dataKey="responseTime"
                stroke="hsl(var(--primary))"
                strokeWidth={3}
                dot={false}
                activeDot={{ r: 6, fill: 'hsl(var(--primary))', strokeWidth: 2 }}
                name="Response Time"
                connectNulls={true}
              />

              <Line
                yAxisId="errorRate"
                type="monotone"
                dataKey="errorRate"
                stroke="hsl(var(--destructive))"
                strokeWidth={3}
                dot={false}
                activeDot={{ r: 6, fill: 'hsl(var(--destructive))', strokeWidth: 2 }}
                name="Error Rate"
                connectNulls={true}
              />
              
              {/* Reference lines for thresholds */}
              <ReferenceLine yAxisId="responseTime" y={1000} stroke="orange" strokeDasharray="5 5" />
              <ReferenceLine yAxisId="errorRate" y={2} stroke="red" strokeDasharray="5 5" />
            </LineChart>
          </ResponsiveContainer>
        </div>

        {/* Memory & CPU Usage */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-4">
          <h4 className="text-sm font-semibold mb-4">Memory & CPU Usage</h4>
          <ResponsiveContainer width="100%" height={250}>
            <LineChart data={chartData}>
              <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
              <XAxis 
                dataKey="formattedTime" 
                className="text-xs"
                interval="preserveStartEnd"
              />
              <YAxis 
                className="text-xs"
                domain={[0, 100]}
                label={{ value: 'Usage (%)', angle: -90, position: 'insideLeft' }}
              />
              <Tooltip
                formatter={formatTooltipValue}
                labelFormatter={formatTooltipLabel}
                contentStyle={{
                  backgroundColor: 'hsl(var(--card))',
                  border: '1px solid hsl(var(--border))',
                  borderRadius: '6px',
                  fontSize: '12px'
                }}
              />
              <Legend />
              
              <Line
                type="monotone"
                dataKey="memoryUsage"
                stroke="hsl(var(--chart-2))"
                strokeWidth={3}
                dot={false}
                activeDot={{ r: 6, fill: 'hsl(var(--chart-2))', strokeWidth: 2 }}
                name="Memory Usage"
                connectNulls={true}
              />

              <Line
                type="monotone"
                dataKey="cpuUsage"
                stroke="hsl(var(--chart-3))"
                strokeWidth={3}
                dot={false}
                activeDot={{ r: 6, fill: 'hsl(var(--chart-3))', strokeWidth: 2 }}
                name="CPU Usage"
                connectNulls={true}
              />
              
              {/* Reference lines for thresholds */}
              <ReferenceLine y={80} stroke="orange" strokeDasharray="5 5" />
              <ReferenceLine y={90} stroke="red" strokeDasharray="5 5" />
            </LineChart>
          </ResponsiveContainer>
        </div>

        {/* Uptime & Database Connections */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-4">
          <h4 className="text-sm font-semibold mb-4">Uptime Percentage</h4>
          <ResponsiveContainer width="100%" height={250}>
            <LineChart data={chartData}>
              <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
              <XAxis 
                dataKey="formattedTime" 
                className="text-xs"
                interval="preserveStartEnd"
              />
              <YAxis 
                className="text-xs"
                domain={[99, 100]}
                label={{ value: 'Uptime (%)', angle: -90, position: 'insideLeft' }}
              />
              <Tooltip
                formatter={formatTooltipValue}
                labelFormatter={formatTooltipLabel}
                contentStyle={{
                  backgroundColor: 'hsl(var(--card))',
                  border: '1px solid hsl(var(--border))',
                  borderRadius: '6px',
                  fontSize: '12px'
                }}
              />
              <Legend />
              
              <Line
                type="monotone"
                dataKey="uptime"
                stroke="hsl(var(--chart-4))"
                strokeWidth={3}
                dot={false}
                activeDot={{ r: 6, fill: 'hsl(var(--chart-4))', strokeWidth: 2 }}
                name="Uptime"
                connectNulls={true}
              />
              
              {/* Reference line for 99.9% uptime */}
              <ReferenceLine y={99.9} stroke="green" strokeDasharray="5 5" />
            </LineChart>
          </ResponsiveContainer>
        </div>

        {/* Database Connections */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-4">
          <h4 className="text-sm font-semibold mb-4">Database Connections</h4>
          <ResponsiveContainer width="100%" height={250}>
            <LineChart data={chartData}>
              <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
              <XAxis 
                dataKey="formattedTime" 
                className="text-xs"
                interval="preserveStartEnd"
              />
              <YAxis 
                className="text-xs"
                label={{ value: 'Connections', angle: -90, position: 'insideLeft' }}
              />
              <Tooltip
                formatter={formatTooltipValue}
                labelFormatter={formatTooltipLabel}
                contentStyle={{
                  backgroundColor: 'hsl(var(--card))',
                  border: '1px solid hsl(var(--border))',
                  borderRadius: '6px',
                  fontSize: '12px'
                }}
              />
              <Legend />
              
              <Line
                type="monotone"
                dataKey="databaseConnections"
                stroke="hsl(var(--chart-5))"
                strokeWidth={3}
                dot={false}
                activeDot={{ r: 6, fill: 'hsl(var(--chart-5))', strokeWidth: 2 }}
                name="DB Connections"
                connectNulls={true}
              />
              
              {/* Reference line for connection limit warning */}
              <ReferenceLine y={15} stroke="orange" strokeDasharray="5 5" />
            </LineChart>
          </ResponsiveContainer>
        </div>
      </div>
    </BaseChart>
  );
}
