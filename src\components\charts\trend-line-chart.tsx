'use client';

import React from 'react';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  <PERSON>ltip,
  Legend,
  ResponsiveContainer
} from 'recharts';
import { BaseChart, BaseChartProps } from './base-chart';
import { TrendData } from '@/services/super-admin';

interface TrendLineChartProps extends Omit<BaseChartProps, 'children'> {
  data: TrendData[];
  showTenants?: boolean;
  showUsers?: boolean;
  showRevenue?: boolean;
}

export function TrendLineChart({
  data,
  showTenants = true,
  showUsers = true,
  showRevenue = false,
  ...baseProps
}: TrendLineChartProps) {
  const formatDate = (dateStr: string) => {
    const date = new Date(dateStr);
    return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
  };

  const formatTooltipValue = (value: number, name: string) => {
    if (name === 'revenue') {
      return [`$${value.toLocaleString()}`, 'Revenue'];
    }
    return [value.toLocaleString(), name === 'tenants' ? 'Tenants' : 'Users'];
  };

  return (
    <BaseChart {...baseProps}>
      <ResponsiveContainer width="100%" height={300}>
        <LineChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
          <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
          <XAxis
            dataKey="date"
            tickFormatter={formatDate}
            className="text-xs"
            interval="preserveStartEnd"
          />
          <YAxis className="text-xs" />
          <Tooltip
            formatter={formatTooltipValue}
            labelFormatter={(label) => `Date: ${formatDate(label)}`}
            contentStyle={{
              backgroundColor: 'hsl(var(--card))',
              border: '1px solid hsl(var(--border))',
              borderRadius: '6px',
              fontSize: '12px'
            }}
          />
          <Legend />

          {showTenants && (
            <Line
              type="monotone"
              dataKey="tenants"
              stroke="hsl(var(--primary))"
              strokeWidth={3}
              dot={false}
              activeDot={{ r: 6, fill: 'hsl(var(--primary))', strokeWidth: 2 }}
              name="Tenants"
              connectNulls={true}
            />
          )}

          {showUsers && (
            <Line
              type="monotone"
              dataKey="users"
              stroke="hsl(var(--chart-2))"
              strokeWidth={3}
              dot={false}
              activeDot={{ r: 6, fill: 'hsl(var(--chart-2))', strokeWidth: 2 }}
              name="Users"
              connectNulls={true}
            />
          )}

          {showRevenue && (
            <Line
              type="monotone"
              dataKey="revenue"
              stroke="hsl(var(--chart-3))"
              strokeWidth={3}
              dot={false}
              activeDot={{ r: 6, fill: 'hsl(var(--chart-3))', strokeWidth: 2 }}
              name="Revenue"
              connectNulls={true}
            />
          )}
        </LineChart>
      </ResponsiveContainer>
    </BaseChart>
  );
}
