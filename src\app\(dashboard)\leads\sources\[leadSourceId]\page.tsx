'use client';

import React, { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { PageLayout } from '@/components/layout/page-layout';
import { PermissionGate } from '@/components/ui/permission-gate';
import { PermissionResource, PermissionAction } from '@/types';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { EmptyState } from '@/components/ui/empty-state';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { 
  Edit, 
  Trash2, 
  Users, 
  Calendar, 
  Activity,
  TrendingUp,
  ArrowLeft
} from 'lucide-react';
import { ExclamationTriangleIcon } from '@heroicons/react/24/outline';
import { LeadSourceWithRelations } from '@/services/lead-source-management';
import { toastFunctions as toast } from '@/hooks/use-toast';

export default function LeadSourceDetailsPage() {
  const params = useParams();
  const router = useRouter();
  const [leadSource, setLeadSource] = useState<LeadSourceWithRelations | null>(null);
  const [loading, setLoading] = useState(true);
  const [notFound, setNotFound] = useState(false);

  const leadSourceId = params.leadSourceId as string;

  useEffect(() => {
    const fetchLeadSource = async () => {
      try {
        setLoading(true);
        const response = await fetch(`/api/lead-sources/${leadSourceId}`);
        const data = await response.json();

        if (data.success) {
          setLeadSource(data.data.leadSource);
        } else {
          setNotFound(true);
          toast.error('Lead source not found');
        }
      } catch (error) {
        console.error('Error fetching lead source:', error);
        setNotFound(true);
        toast.error('Failed to load lead source');
      } finally {
        setLoading(false);
      }
    };

    if (leadSourceId) {
      fetchLeadSource();
    }
  }, [leadSourceId]);

  const handleDelete = async () => {
    if (!leadSource) return;

    if (!confirm(`Are you sure you want to delete "${leadSource.name}"? This action cannot be undone.`)) {
      return;
    }

    try {
      const response = await fetch(`/api/lead-sources/${leadSource.id}`, {
        method: 'DELETE'
      });

      const data = await response.json();

      if (data.success) {
        toast.success('Lead source deleted successfully');
        router.push('/leads/sources');
      } else {
        toast.error(data.error || 'Failed to delete lead source');
      }
    } catch (error) {
      console.error('Error deleting lead source:', error);
      toast.error('Failed to delete lead source');
    }
  };

  const handleToggleStatus = async () => {
    if (!leadSource) return;

    try {
      const response = await fetch(`/api/lead-sources/${leadSource.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ isActive: !leadSource.isActive })
      });

      const data = await response.json();

      if (data.success) {
        setLeadSource(data.data.leadSource);
        toast.success(`Lead source ${leadSource.isActive ? 'deactivated' : 'activated'} successfully`);
      } else {
        toast.error(data.error || 'Failed to update lead source');
      }
    } catch (error) {
      console.error('Error updating lead source:', error);
      toast.error('Failed to update lead source');
    }
  };

  if (loading) {
    return (
      <PageLayout
        title="Lead Source Details"
        description="Loading lead source details..."
        breadcrumbs={[
          { label: 'Leads', href: '/leads' },
          { label: 'Lead Sources', href: '/leads/sources' },
          { label: 'Details' }
        ]}
      >
        <Card>
          <CardContent className="p-8">
            <div className="flex items-center justify-center">
              <LoadingSpinner size="lg" />
            </div>
          </CardContent>
        </Card>
      </PageLayout>
    );
  }

  if (notFound || !leadSource) {
    return (
      <PageLayout
        title="Lead Source Not Found"
        description="The requested lead source could not be found"
        breadcrumbs={[
          { label: 'Leads', href: '/leads' },
          { label: 'Lead Sources', href: '/leads/sources' },
          { label: 'Details' }
        ]}
      >
        <Card>
          <CardContent className="p-8">
            <EmptyState
              icon={<ExclamationTriangleIcon className="w-12 h-12 text-muted-foreground" />}
              title="Lead Source Not Found"
              description="The lead source you're looking for doesn't exist or you don't have permission to access it."
              action={{
                label: 'Back to Lead Sources',
                onClick: () => router.push('/leads/sources'),
                variant: 'primary'
              }}
            />
          </CardContent>
        </Card>
      </PageLayout>
    );
  }

  const actions = (
    <div className="flex space-x-2">
      <Button
        variant="outline"
        onClick={() => router.push('/leads/sources')}
      >
        <ArrowLeft className="w-4 h-4 mr-2" />
        Back
      </Button>
      <Button onClick={() => router.push(`/leads/sources/${leadSource.id}/edit`)}>
        <Edit className="w-4 h-4 mr-2" />
        Edit
      </Button>
    </div>
  );

  return (
    <PermissionGate
      resource={PermissionResource.LEADS}
      action={PermissionAction.READ}
      fallback={
        <PageLayout>
          <Card>
            <CardContent className="p-8">
              <EmptyState
                icon={<ExclamationTriangleIcon className="w-12 h-12 text-muted-foreground" />}
                title="Access Denied"
                description="You don't have permission to view lead sources. Please contact your administrator for access."
                action={{
                  label: 'Back to Dashboard',
                  onClick: () => router.push('/dashboard'),
                  variant: 'primary'
                }}
              />
            </CardContent>
          </Card>
        </PageLayout>
      }
    >
      <PageLayout
        title={leadSource.name}
        description={leadSource.description || 'Lead source details and statistics'}
        actions={actions}
        breadcrumbs={[
          { label: 'Leads', href: '/leads' },
          { label: 'Lead Sources', href: '/leads/sources' },
          { label: leadSource.name }
        ]}
      >
        <div className="space-y-6">
          {/* Lead Source Details */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-3">
                {leadSource.icon && (
                  <div 
                    className="w-10 h-10 rounded-lg flex items-center justify-center text-white text-lg font-medium"
                    style={{ backgroundColor: leadSource.color || '#6B7280' }}
                  >
                    {leadSource.icon}
                  </div>
                )}
                <div>
                  <div className="flex items-center space-x-2">
                    <span>{leadSource.name}</span>
                    <Badge variant={leadSource.isActive ? 'default' : 'secondary'}>
                      {leadSource.isActive ? 'Active' : 'Inactive'}
                    </Badge>
                  </div>
                  <p className="text-sm text-muted-foreground font-normal">
                    {leadSource.description}
                  </p>
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
                <div className="flex items-center space-x-3">
                  <div className="p-2 bg-blue-100 rounded-lg">
                    <TrendingUp className="w-5 h-5 text-blue-600" />
                  </div>
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Category</p>
                    <p className="text-lg font-semibold">
                      {leadSource.category.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                    </p>
                  </div>
                </div>

                <div className="flex items-center space-x-3">
                  <div className="p-2 bg-green-100 rounded-lg">
                    <Users className="w-5 h-5 text-green-600" />
                  </div>
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Total Leads</p>
                    <p className="text-lg font-semibold">{leadSource._count?.leads || 0}</p>
                  </div>
                </div>

                <div className="flex items-center space-x-3">
                  <div className="p-2 bg-purple-100 rounded-lg">
                    <Activity className="w-5 h-5 text-purple-600" />
                  </div>
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Status</p>
                    <p className="text-lg font-semibold">
                      {leadSource.isActive ? 'Active' : 'Inactive'}
                    </p>
                  </div>
                </div>

                <div className="flex items-center space-x-3">
                  <div className="p-2 bg-orange-100 rounded-lg">
                    <Calendar className="w-5 h-5 text-orange-600" />
                  </div>
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Created</p>
                    <p className="text-lg font-semibold">
                      {new Date(leadSource.createdAt).toLocaleDateString()}
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Actions</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex flex-wrap gap-4">
                <PermissionGate resource={PermissionResource.LEADS} action={PermissionAction.UPDATE}>
                  <Button
                    onClick={() => router.push(`/leads/sources/${leadSource.id}/edit`)}
                    variant="outline"
                  >
                    <Edit className="w-4 h-4 mr-2" />
                    Edit Lead Source
                  </Button>
                </PermissionGate>

                <PermissionGate resource={PermissionResource.LEADS} action={PermissionAction.UPDATE}>
                  <Button
                    onClick={handleToggleStatus}
                    variant="outline"
                  >
                    <Activity className="w-4 h-4 mr-2" />
                    {leadSource.isActive ? 'Deactivate' : 'Activate'}
                  </Button>
                </PermissionGate>

                <PermissionGate resource={PermissionResource.LEADS} action={PermissionAction.DELETE}>
                  <Button
                    onClick={handleDelete}
                    variant="destructive"
                    disabled={leadSource._count?.leads && leadSource._count.leads > 0}
                  >
                    <Trash2 className="w-4 h-4 mr-2" />
                    Delete Lead Source
                  </Button>
                </PermissionGate>
              </div>
              
              {leadSource._count?.leads && leadSource._count.leads > 0 && (
                <p className="text-sm text-muted-foreground mt-2">
                  * Cannot delete lead source with associated leads. Please reassign leads first.
                </p>
              )}
            </CardContent>
          </Card>
        </div>
      </PageLayout>
    </PermissionGate>
  );
}
