'use client';

import React, { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { SearchableSelect, SearchableSelectOption } from '@/components/ui/searchable-select';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { CalendarIcon, Loader2, X } from 'lucide-react';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';
import { toastFunctions as toast } from '@/hooks/use-toast';
import { OpportunityWithRelations } from '@/services/opportunity-management';

// Form validation schema
const opportunityFormSchema = z.object({
  title: z.string().min(1, 'Title is required').max(255),
  description: z.string().optional(),
  value: z.number().positive('Value must be positive').optional(),
  stage: z.string().min(1, 'Stage is required'),
  probability: z.number().min(0).max(100),
  expectedCloseDate: z.date().optional(),
  tags: z.array(z.string()).default([]),
  source: z.string().optional(),
  priority: z.enum(['low', 'medium', 'high', 'urgent']),
  closeReason: z.string().optional(),
  contactId: z.string().optional(),
  companyId: z.string().optional(),
  ownerId: z.string().optional(),
});

type OpportunityFormData = z.infer<typeof opportunityFormSchema>;

interface OpportunityFormProps {
  opportunity?: OpportunityWithRelations;
  leadId?: string;
  onSuccess?: (opportunityId: string) => void;
  onCancel?: () => void;
  className?: string;
}

export function OpportunityForm({
  opportunity,
  leadId,
  onSuccess,
  onCancel,
  className
}: OpportunityFormProps) {
  const { data: session } = useSession();
  const [loading, setLoading] = useState(false);
  const [stages, setStages] = useState<any[]>([]);
  const [contacts, setContacts] = useState<any[]>([]);
  const [companies, setCompanies] = useState<any[]>([]);
  const [users, setUsers] = useState<any[]>([]);
  const [newTag, setNewTag] = useState('');
  const [calendarOpen, setCalendarOpen] = useState(false);

  const isEditing = !!opportunity;

  const form = useForm<OpportunityFormData>({
    resolver: zodResolver(opportunityFormSchema),
    defaultValues: {
      title: opportunity?.title || '',
      description: opportunity?.description || '',
      value: opportunity?.value ? Number(opportunity.value) : undefined,
      stage: opportunity?.stage || 'qualification',
      probability: opportunity?.probability || 25,
      expectedCloseDate: opportunity?.expectedCloseDate ? new Date(opportunity.expectedCloseDate) : undefined,
      tags: Array.isArray(opportunity?.tags) ? opportunity.tags : [],
      source: opportunity?.source || '',
      priority: (opportunity?.priority as any) || 'medium',
      closeReason: opportunity?.closeReason || '',
      contactId: opportunity?.contactId || '',
      companyId: opportunity?.companyId || '',
      ownerId: opportunity?.ownerId || (opportunity ? '' : session?.user?.id || ''),
    }
  });

  // Load form data
  useEffect(() => {
    const loadFormData = async () => {
      try {
        const [stagesRes, contactsRes, companiesRes, usersRes] = await Promise.all([
          fetch('/api/opportunities/stages'),
          fetch('/api/contacts?limit=100'),
          fetch('/api/companies?limit=100'),
          fetch('/api/users')
        ]);

        if (stagesRes.ok) {
          const stagesData = await stagesRes.json();
          setStages(stagesData);
        }

        if (contactsRes.ok) {
          const contactsData = await contactsRes.json();
          // Fix: Access nested data structure correctly
          if (contactsData.success && contactsData.data) {
            setContacts(contactsData.data.contacts || []);
          } else {
            setContacts([]);
          }
        } else {
          setContacts([]);
        }

        if (companiesRes.ok) {
          const companiesData = await companiesRes.json();
          // Fix: Access nested data structure correctly
          if (companiesData.success && companiesData.data) {
            setCompanies(companiesData.data.companies || []);
          } else {
            setCompanies([]);
          }
        } else {
          setCompanies([]);
        }

        if (usersRes.ok) {
          const usersData = await usersRes.json();
          // Fix: Access data structure correctly (users API returns data directly)
          if (usersData.success && usersData.data) {
            setUsers(usersData.data || []);
          } else {
            setUsers([]);
          }
        } else {
          setUsers([]);
        }

        // If converting from lead, populate lead data
        if (leadId) {
          const leadRes = await fetch(`/api/leads/${leadId}`);
          if (leadRes.ok) {
            const leadData = await leadRes.json();
            form.setValue('title', leadData.title || '');
            form.setValue('description', leadData.description || '');
            form.setValue('value', leadData.value ? Number(leadData.value) : undefined);
            form.setValue('contactId', leadData.contactId || '');
            form.setValue('companyId', leadData.companyId || '');
            form.setValue('source', 'lead_conversion');
            form.setValue('priority', leadData.priority || 'medium');
          }
        }
      } catch (error) {
        console.error('Error loading form data:', error);
        toast.error('Failed to load form data');
      }
    };

    loadFormData();
  }, [leadId, form]);

  // Helper functions to convert data to SearchableSelectOption format
  const getStageOptions = (): SearchableSelectOption[] => {
    return stages.map(stage => ({
      value: stage.name,
      label: stage.name,
      description: `Stage: ${stage.name}`,
      icon: (
        <div
          className="w-3 h-3 rounded-full"
          style={{ backgroundColor: stage.color }}
        />
      )
    }));
  };

  const getPriorityOptions = (): SearchableSelectOption[] => {
    return [
      { value: 'low', label: 'Low', description: 'Low priority opportunity' },
      { value: 'medium', label: 'Medium', description: 'Medium priority opportunity' },
      { value: 'high', label: 'High', description: 'High priority opportunity' },
      { value: 'urgent', label: 'Urgent', description: 'Urgent priority opportunity' }
    ];
  };

  const getContactOptions = (): SearchableSelectOption[] => {
    const options: SearchableSelectOption[] = [
      { value: 'none', label: 'No contact', description: 'No contact selected' }
    ];

    contacts.forEach(contact => {
      options.push({
        value: contact.id,
        label: `${contact.firstName} ${contact.lastName}`,
        description: contact.email || undefined
      });
    });

    return options;
  };

  const getCompanyOptions = (): SearchableSelectOption[] => {
    const options: SearchableSelectOption[] = [
      { value: 'none', label: 'No company', description: 'No company selected' }
    ];

    companies.forEach(company => {
      options.push({
        value: company.id,
        label: company.name,
        description: `Company: ${company.name}`
      });
    });

    return options;
  };

  const getUserOptions = (): SearchableSelectOption[] => {
    const options: SearchableSelectOption[] = [
      { value: 'none', label: 'Unassigned', description: 'No owner assigned' }
    ];

    users.forEach(user => {
      options.push({
        value: user.id,
        label: `${user.firstName} ${user.lastName}`,
        description: user.email || undefined
      });
    });

    return options;
  };

  // Handle stage change to update probability
  const handleStageChange = (stageName: string) => {
    const stage = stages.find(s => s.name === stageName);
    if (stage) {
      const avgProbability = Math.floor((stage.probabilityMin + stage.probabilityMax) / 2);
      form.setValue('probability', avgProbability);
    }
    form.setValue('stage', stageName);
  };

  // Add tag
  const addTag = () => {
    if (newTag.trim() && !form.getValues('tags').includes(newTag.trim())) {
      const currentTags = form.getValues('tags');
      form.setValue('tags', [...currentTags, newTag.trim()]);
      setNewTag('');
    }
  };

  // Remove tag
  const removeTag = (tagToRemove: string) => {
    const currentTags = form.getValues('tags');
    form.setValue('tags', currentTags.filter(tag => tag !== tagToRemove));
  };



  // Submit form
  const onSubmit = async (data: OpportunityFormData) => {
    try {
      setLoading(true);

      const submitData = {
        ...data,
        expectedCloseDate: data.expectedCloseDate?.toISOString(),
        value: data.value || null,
      };

      let response;
      
      if (isEditing) {
        response = await fetch(`/api/opportunities/${opportunity.id}`, {
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(submitData)
        });
      } else if (leadId) {
        response = await fetch('/api/opportunities/convert-from-lead', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            leadId,
            opportunityData: submitData
          })
        });
      } else {
        response = await fetch('/api/opportunities', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(submitData)
        });
      }

      if (response.ok) {
        const result = await response.json();
        toast.success(isEditing ? 'Opportunity updated successfully' : 'Opportunity created successfully');
        onSuccess?.(result.id);
      } else {
        const error = await response.json();
        toast.error(error.error || 'Failed to save opportunity');
      }
    } catch (error) {
      console.error('Error saving opportunity:', error);
      toast.error('Failed to save opportunity');
    } finally {
      setLoading(false);
    }
  };

  return (
    <form onSubmit={form.handleSubmit(onSubmit)} className={`space-y-6 ${className}`}>
      <Card>
        <CardHeader>
          <CardTitle>
            {isEditing ? 'Edit Opportunity' : leadId ? 'Convert Lead to Opportunity' : 'Create New Opportunity'}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Basic Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <Label htmlFor="title">Title *</Label>
              <Input
                id="title"
                {...form.register('title')}
                placeholder="Enter opportunity title"
              />
              {form.formState.errors.title && (
                <p className="text-sm text-red-600">{form.formState.errors.title.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="value">Value ($)</Label>
              <Input
                id="value"
                type="number"
                {...form.register('value', { valueAsNumber: true })}
                placeholder="Enter opportunity value"
              />
              {form.formState.errors.value && (
                <p className="text-sm text-red-600">{form.formState.errors.value.message}</p>
              )}
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              {...form.register('description')}
              placeholder="Enter opportunity description"
              rows={3}
            />
          </div>

          <Separator />

          {/* Sales Information */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="space-y-2">
              <Label htmlFor="stage">Stage *</Label>
              <SearchableSelect
                options={getStageOptions()}
                value={form.watch('stage')}
                onValueChange={handleStageChange}
                placeholder="Select stage"
                searchPlaceholder="Search stages..."
                emptyMessage="No stages found"
              />
              {form.formState.errors.stage && (
                <p className="text-sm text-red-600">{form.formState.errors.stage.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="probability">Probability (%)</Label>
              <Input
                id="probability"
                type="number"
                min="0"
                max="100"
                {...form.register('probability', { valueAsNumber: true })}
                placeholder="Enter probability"
              />
              {form.formState.errors.probability && (
                <p className="text-sm text-red-600">{form.formState.errors.probability.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="priority">Priority</Label>
              <SearchableSelect
                options={getPriorityOptions()}
                value={form.watch('priority')}
                onValueChange={(value) => form.setValue('priority', value as any)}
                placeholder="Select priority"
                searchPlaceholder="Search priority..."
                emptyMessage="No priority options found"
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <Label>Expected Close Date</Label>
              <Popover open={calendarOpen} onOpenChange={setCalendarOpen}>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "w-full justify-start text-left font-normal",
                      !form.watch('expectedCloseDate') && "text-muted-foreground"
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {form.watch('expectedCloseDate') ? (
                      format(form.watch('expectedCloseDate')!, "PPP")
                    ) : (
                      <span>Pick a date</span>
                    )}
                  </Button>
                </PopoverTrigger>
                <PopoverContent
                  className="w-auto p-0 z-[1000]"
                  align="start"
                  side="bottom"
                  sideOffset={4}
                  avoidCollisions={true}
                  collisionPadding={20}
                >
                  <Calendar
                    mode="single"
                    selected={form.watch('expectedCloseDate')}
                    onSelect={(date) => {
                      form.setValue('expectedCloseDate', date);
                      setCalendarOpen(false);
                    }}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>

            <div className="space-y-2">
              <Label htmlFor="source">Source</Label>
              <Input
                id="source"
                {...form.register('source')}
                placeholder="Enter opportunity source"
              />
            </div>
          </div>

          <Separator />

          {/* Relationships */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="space-y-2">
              <Label htmlFor="contactId">Contact</Label>
              <SearchableSelect
                options={getContactOptions()}
                value={form.watch('contactId') || 'none'}
                onValueChange={(value) => form.setValue('contactId', value === 'none' ? '' : value)}
                placeholder={contacts.length > 0 ? "Select contact" : "Loading contacts..."}
                searchPlaceholder="Search contacts..."
                emptyMessage={contacts.length === 0 ? 'No contacts available' : 'No contacts found'}
                allowClear={true}
                clearLabel="Clear contact"
                disabled={contacts.length === 0}
              />
              {contacts.length === 0 && (
                <p className="text-xs text-muted-foreground">
                  No contacts found. Create contacts first to assign them to opportunities.
                </p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="companyId">Company</Label>
              <SearchableSelect
                options={getCompanyOptions()}
                value={form.watch('companyId') || 'none'}
                onValueChange={(value) => form.setValue('companyId', value === 'none' ? '' : value)}
                placeholder={companies.length > 0 ? "Select company" : "Loading companies..."}
                searchPlaceholder="Search companies..."
                emptyMessage={companies.length === 0 ? 'No companies available' : 'No companies found'}
                allowClear={true}
                clearLabel="Clear company"
                disabled={companies.length === 0}
              />
              {companies.length === 0 && (
                <p className="text-xs text-muted-foreground">
                  No companies found. Create companies first to assign them to opportunities.
                </p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="ownerId">Owner</Label>
              <SearchableSelect
                options={getUserOptions()}
                value={form.watch('ownerId') || 'none'}
                onValueChange={(value) => form.setValue('ownerId', value === 'none' ? '' : value)}
                placeholder={users.length > 0 ? "Select owner" : "Loading users..."}
                searchPlaceholder="Search users..."
                emptyMessage={users.length === 0 ? 'No users available' : 'No users found'}
                allowClear={true}
                clearLabel="Clear owner"
                disabled={users.length === 0}
              />
              {users.length === 0 && (
                <p className="text-xs text-muted-foreground">
                  No users found. Contact your administrator.
                </p>
              )}
            </div>
          </div>

          {/* Tags */}
          <div className="space-y-2">
            <Label>Tags</Label>
            <div className="flex flex-wrap gap-2 mb-2">
              {form.watch('tags').map((tag, index) => (
                <Badge key={index} variant="secondary" className="flex items-center gap-1">
                  {tag}
                  <X 
                    className="w-3 h-3 cursor-pointer" 
                    onClick={() => removeTag(tag)}
                  />
                </Badge>
              ))}
            </div>
            <div className="flex gap-2">
              <Input
                value={newTag}
                onChange={(e) => setNewTag(e.target.value)}
                placeholder="Add a tag"
                onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addTag())}
              />
              <Button type="button" variant="outline" onClick={addTag}>
                Add
              </Button>
            </div>
          </div>

          {/* Form Actions */}
          <div className="flex justify-end space-x-4 pt-6">
            {onCancel && (
              <Button type="button" variant="outline" onClick={onCancel}>
                Cancel
              </Button>
            )}
            <Button type="submit" disabled={loading}>
              {loading ? (
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
              ) : null}
              {isEditing ? 'Update Opportunity' : 'Create Opportunity'}
            </Button>
          </div>
        </CardContent>
      </Card>
    </form>
  );
}
