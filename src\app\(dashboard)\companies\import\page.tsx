'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { PageLayout } from '@/components/layout/page-layout';
import { PermissionGate } from '@/components/ui/permission-gate';
import { PermissionResource, PermissionAction } from '@/types';
import { 
  Upload, 
  Download, 
  FileText, 
  CheckCircle, 
  XCircle, 
  AlertCircle,
  ArrowLeft,
  Loader2
} from 'lucide-react';

interface ImportError {
  row: number;
  field: string;
  message: string;
}

interface ImportResult {
  success: boolean;
  imported: number;
  errors: ImportError[];
  message: string;
}

export default function CompanyImportPage() {
  const router = useRouter();
  const [step, setStep] = useState<'upload' | 'preview' | 'result'>('upload');
  const [file, setFile] = useState<File | null>(null);
  const [previewData, setPreviewData] = useState<any[]>([]);
  const [importing, setImporting] = useState(false);
  const [result, setResult] = useState<ImportResult | null>(null);

  // Download sample CSV
  const downloadSample = () => {
    const sampleData = [
      ['Name', 'Website', 'Industry', 'Size', 'OwnerEmail', 'OwnerName'],
      ['Acme Corporation', 'https://acme.com', 'Technology', '51-200 employees', '<EMAIL>', 'John Doe'],
      ['Global Industries', 'https://global.com', 'Manufacturing', '201-500 employees', '<EMAIL>', 'Jane Smith'],
      ['Tech Startup', '', 'Technology', '1-10 employees', '', 'Mike Johnson']
    ];

    // Add UTF-8 BOM for proper Arabic character support
    const BOM = '\uFEFF';
    const csvContent = BOM + sampleData.map(row =>
      row.map(cell => `"${cell}"`).join(',')
    ).join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'company-import-sample.csv';
    document.body.appendChild(a);
    a.click();
    window.URL.revokeObjectURL(url);
    document.body.removeChild(a);
  };

  // Handle file selection
  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = event.target.files?.[0];
    if (selectedFile) {
      setFile(selectedFile);
      parseCSVPreview(selectedFile);
    }
  };

  // Parse CSV for preview
  const parseCSVPreview = (file: File) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      const text = e.target?.result as string;
      const lines = text.split('\n').filter(line => line.trim());
      
      if (lines.length < 2) {
        alert('CSV file must have at least a header row and one data row');
        return;
      }

      const headers = lines[0].split(',').map(h => h.trim().replace(/"/g, ''));
      const preview = lines.slice(1, 6).map((line, index) => {
        const values = line.split(',').map(v => v.trim().replace(/"/g, ''));
        const row: Record<string, unknown> = { _rowNumber: index + 2 };
        headers.forEach((header, i) => {
          row[header] = values[i] || '';
        });
        return row;
      });

      setPreviewData(preview);
      setStep('preview');
    };
    reader.readAsText(file);
  };

  // Handle import
  const handleImport = async () => {
    if (!file) return;

    setImporting(true);
    const formData = new FormData();
    formData.append('file', file);

    try {
      const response = await fetch('/api/companies/import', {
        method: 'POST',
        body: formData,
      });

      const data = await response.json();
      setResult(data);
      setStep('result');
    } catch (err) {
      console.error('Import error:', err);
      alert('Failed to import companies. Please try again.');
    } finally {
      setImporting(false);
    }
  };

  // Reset to start over
  const resetImport = () => {
    setStep('upload');
    setFile(null);
    setPreviewData([]);
    setResult(null);
  };

  return (
    <PermissionGate
      resource={PermissionResource.COMPANIES}
      action={PermissionAction.CREATE}
      fallback={
        <PageLayout>
          <Card>
            <CardContent className="p-8">
              <div className="text-center">
                <XCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
                  Access Denied
                </h3>
                <p className="text-gray-600 dark:text-gray-400 mb-4">
                  You don't have permission to import companies.
                </p>
                <Button onClick={() => router.push('/companies')}>
                  Back to Companies
                </Button>
              </div>
            </CardContent>
          </Card>
        </PageLayout>
      }
    >
      <PageLayout
        title="Import Companies"
        description="Import company data from CSV files"
        actions={
          <Button
            variant="outline"
            onClick={() => router.push('/companies')}
            className="gap-2"
          >
            <ArrowLeft className="w-4 h-4" />
            Back to Companies
          </Button>
        }
      >
        <div className="max-w-4xl mx-auto space-y-6">
          {/* Progress Steps */}
          <div className="flex items-center justify-center space-x-8 mb-8">
            <div className={`flex items-center space-x-2 ${step === 'upload' ? 'text-blue-600' : step === 'preview' || step === 'result' ? 'text-green-600' : 'text-gray-400'}`}>
              <div className={`w-8 h-8 rounded-full flex items-center justify-center ${step === 'upload' ? 'bg-blue-100 text-blue-600' : step === 'preview' || step === 'result' ? 'bg-green-100 text-green-600' : 'bg-gray-100 text-gray-400'}`}>
                1
              </div>
              <span className="font-medium">Upload</span>
            </div>
            
            <div className={`w-16 h-0.5 ${step === 'preview' || step === 'result' ? 'bg-green-600' : 'bg-gray-300'}`} />
            
            <div className={`flex items-center space-x-2 ${step === 'preview' ? 'text-blue-600' : step === 'result' ? 'text-green-600' : 'text-gray-400'}`}>
              <div className={`w-8 h-8 rounded-full flex items-center justify-center ${step === 'preview' ? 'bg-blue-100 text-blue-600' : step === 'result' ? 'bg-green-100 text-green-600' : 'bg-gray-100 text-gray-400'}`}>
                2
              </div>
              <span className="font-medium">Preview</span>
            </div>
            
            <div className={`w-16 h-0.5 ${step === 'result' ? 'bg-green-600' : 'bg-gray-300'}`} />
            
            <div className={`flex items-center space-x-2 ${step === 'result' ? 'text-blue-600' : 'text-gray-400'}`}>
              <div className={`w-8 h-8 rounded-full flex items-center justify-center ${step === 'result' ? 'bg-blue-100 text-blue-600' : 'bg-gray-100 text-gray-400'}`}>
                3
              </div>
              <span className="font-medium">Results</span>
            </div>
          </div>

          {/* Upload Step */}
          {step === 'upload' && (
            <Card>
              <CardHeader>
                <div className="text-center">
                  <Upload className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <CardTitle>Upload CSV File</CardTitle>
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-center">
                  <p className="text-gray-600 dark:text-gray-400 mb-6">
                    Select a CSV file containing your company data. The file should include columns for Name, Website, Industry, Size, and Owner information.
                  </p>

                  <div className="mb-6">
                    <Button
                      variant="outline"
                      onClick={downloadSample}
                    >
                      <Download className="w-4 h-4 mr-2" />
                      Download Sample CSV
                    </Button>
                  </div>

                  <div className="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-8">
                    <input
                      type="file"
                      accept=".csv"
                      onChange={handleFileSelect}
                      className="hidden"
                      id="csv-upload"
                    />
                    <label
                      htmlFor="csv-upload"
                      className="cursor-pointer flex flex-col items-center"
                    >
                      <FileText className="w-12 h-12 text-gray-400 mb-4" />
                      <span className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
                        Choose CSV file
                      </span>
                      <span className="text-gray-500 dark:text-gray-400">
                        or drag and drop it here
                      </span>
                    </label>
                  </div>

                  {file && (
                    <div className="mt-4 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                      <p className="text-blue-800 dark:text-blue-200">
                        Selected: {file.name} ({(file.size / 1024).toFixed(1)} KB)
                      </p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Preview Step */}
          {step === 'preview' && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="w-5 h-5" />
                  Preview Data
                </CardTitle>
                <p className="text-gray-600 dark:text-gray-400">
                  Review the first few rows of your data before importing
                </p>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="overflow-x-auto">
                    <table className="w-full border-collapse border border-gray-300 dark:border-gray-600">
                      <thead>
                        <tr className="bg-gray-50 dark:bg-gray-800">
                          {previewData.length > 0 && Object.keys(previewData[0])
                            .filter(key => key !== '_rowNumber')
                            .map((header) => (
                              <th key={header} className="border border-gray-300 dark:border-gray-600 px-4 py-2 text-left font-medium">
                                {header}
                              </th>
                            ))}
                        </tr>
                      </thead>
                      <tbody>
                        {previewData.map((row, index) => (
                          <tr key={index} className="hover:bg-gray-50 dark:hover:bg-gray-800">
                            {Object.entries(row)
                              .filter(([key]) => key !== '_rowNumber')
                              .map(([key, value], cellIndex) => (
                                <td key={cellIndex} className="border border-gray-300 dark:border-gray-600 px-4 py-2">
                                  {String(value)}
                                </td>
                              ))}
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>

                  <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
                    <h4 className="font-medium text-blue-800 dark:text-blue-200 mb-2">
                      Import Requirements:
                    </h4>
                    <ul className="text-blue-700 dark:text-blue-300 text-sm space-y-1">
                      <li>• <strong>Name</strong> column is required</li>
                      <li>• Website should be a valid URL (optional)</li>
                      <li>• OwnerEmail or OwnerName can be used to assign ownership (optional)</li>
                      <li>• Industry and Size are optional fields</li>
                    </ul>
                  </div>

                  <div className="flex justify-between">
                    <Button
                      variant="outline"
                      onClick={() => setStep('upload')}
                    >
                      Back
                    </Button>
                    <Button
                      onClick={handleImport}
                      disabled={importing}
                      className="gap-2"
                    >
                      {importing ? (
                        <>
                          <Loader2 className="w-4 h-4 animate-spin" />
                          Importing...
                        </>
                      ) : (
                        <>
                          <Upload className="w-4 h-4" />
                          Import Companies
                        </>
                      )}
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Results Step */}
          {step === 'result' && result && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  {result.success ? (
                    <CheckCircle className="w-5 h-5 text-green-600" />
                  ) : (
                    <AlertCircle className="w-5 h-5 text-yellow-600" />
                  )}
                  Import Results
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className={`p-4 rounded-lg ${result.success ? 'bg-green-50 dark:bg-green-900/20' : 'bg-yellow-50 dark:bg-yellow-900/20'}`}>
                    <p className={`font-medium ${result.success ? 'text-green-800 dark:text-green-200' : 'text-yellow-800 dark:text-yellow-200'}`}>
                      {result.message}
                    </p>
                    <p className={`text-sm mt-1 ${result.success ? 'text-green-700 dark:text-green-300' : 'text-yellow-700 dark:text-yellow-300'}`}>
                      Successfully imported: {result.imported} companies
                    </p>
                  </div>

                  {result.errors.length > 0 && (
                    <div className="bg-red-50 dark:bg-red-900/20 p-4 rounded-lg">
                      <h4 className="font-medium text-red-800 dark:text-red-200 mb-2">
                        Errors ({result.errors.length}):
                      </h4>
                      <div className="max-h-60 overflow-y-auto">
                        <table className="w-full text-sm">
                          <thead>
                            <tr className="text-red-700 dark:text-red-300">
                              <th className="text-left py-1">Row</th>
                              <th className="text-left py-1">Field</th>
                              <th className="text-left py-1">Error</th>
                            </tr>
                          </thead>
                          <tbody className="text-red-600 dark:text-red-400">
                            {result.errors.map((error, index) => (
                              <tr key={index}>
                                <td className="py-1">{error.row}</td>
                                <td className="py-1">{error.field}</td>
                                <td className="py-1">{error.message}</td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    </div>
                  )}

                  <div className="flex justify-between">
                    <Button
                      variant="outline"
                      onClick={resetImport}
                    >
                      Import Another File
                    </Button>
                    <Button
                      onClick={() => router.push('/companies')}
                    >
                      View Companies
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </PageLayout>
    </PermissionGate>
  );
}
