'use client';

import { Fragment } from 'react';
import { Menu, Transition } from '@headlessui/react';
import { 
  PlusIcon,
  ChevronDownIcon,
  UserPlusIcon,
  BuildingOfficeIcon,
  UserGroupIcon,
  ChartBarIcon,
  DocumentTextIcon,
  FolderIcon
} from '@heroicons/react/24/outline';
import { useTranslation } from '@/components/providers/locale-provider';
import { cn } from '@/lib/utils';

const quickActions = [
  {
    name: 'Add Contact',
    description: 'Create a new contact',
    href: '/contacts/new',
    icon: UserPlusIcon,
    color: 'text-blue-600 dark:text-blue-400',
    bgColor: 'bg-blue-50 dark:bg-blue-900/20',
  },
  {
    name: 'Add Company',
    description: 'Create a new company',
    href: '/companies/new',
    icon: BuildingOfficeIcon,
    color: 'text-green-600 dark:text-green-400',
    bgColor: 'bg-green-50 dark:bg-green-900/20',
  },
  {
    name: 'Add Lead',
    description: 'Create a new lead',
    href: '/leads/new',
    icon: UserGroupIcon,
    color: 'text-purple-600 dark:text-purple-400',
    bgColor: 'bg-purple-50 dark:bg-purple-900/20',
  },
  {
    name: 'Add Opportunity',
    description: 'Create a new opportunity',
    href: '/opportunities/new',
    icon: ChartBarIcon,
    color: 'text-orange-600 dark:text-orange-400',
    bgColor: 'bg-orange-50 dark:bg-orange-900/20',
  },
  {
    name: 'Create Proposal',
    description: 'Create a new proposal',
    href: '/proposals/new',
    icon: DocumentTextIcon,
    color: 'text-indigo-600 dark:text-indigo-400',
    bgColor: 'bg-indigo-50 dark:bg-indigo-900/20',
  },
  {
    name: 'Start Project',
    description: 'Create a new project',
    href: '/projects/new',
    icon: FolderIcon,
    color: 'text-pink-600 dark:text-pink-400',
    bgColor: 'bg-pink-50 dark:bg-pink-900/20',
  },
];

export function QuickActions() {
  const { t } = useTranslation();

  const handleActionClick = (href: string) => {
    // TODO: Navigate to the specified route
    console.log('Navigate to:', href);
  };

  return (
    <Menu as="div" className="relative inline-block text-left">
      <div>
        <Menu.Button className="inline-flex items-center gap-x-1.5 rounded-md bg-primary-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-primary-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-primary-600 transition-colors">
          <PlusIcon className="-ml-0.5 h-5 w-5" aria-hidden="true" />
          Quick Actions
          <ChevronDownIcon className="-mr-1 h-5 w-5" aria-hidden="true" />
        </Menu.Button>
      </div>

      <Transition
        as={Fragment}
        enter="transition ease-out duration-100"
        enterFrom="transform opacity-0 scale-95"
        enterTo="transform opacity-100 scale-100"
        leave="transition ease-in duration-75"
        leaveFrom="transform opacity-100 scale-100"
        leaveTo="transform opacity-0 scale-95"
      >
        <Menu.Items className="absolute right-0 z-10 mt-2 w-72 origin-top-right rounded-md bg-white dark:bg-gray-800 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
          <div className="py-1">
            <div className="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
              <h3 className="text-sm font-medium text-gray-900 dark:text-white">
                Quick Actions
              </h3>
              <p className="text-xs text-gray-500 dark:text-gray-400">
                Create new records quickly
              </p>
            </div>
            
            <div className="py-1">
              {quickActions.map((action) => (
                <Menu.Item key={action.name}>
                  {({ active }) => (
                    <button
                      onClick={() => handleActionClick(action.href)}
                      className={cn(
                        active 
                          ? 'bg-gray-100 dark:bg-gray-700' 
                          : 'bg-white dark:bg-gray-800',
                        'group flex w-full items-center px-4 py-3 text-sm transition-colors'
                      )}
                    >
                      <div className={cn(
                        'flex-shrink-0 p-2 rounded-md mr-3',
                        action.bgColor
                      )}>
                        <action.icon 
                          className={cn('h-5 w-5', action.color)} 
                          aria-hidden="true" 
                        />
                      </div>
                      <div className="flex-1 text-left">
                        <p className="font-medium text-gray-900 dark:text-white">
                          {action.name}
                        </p>
                        <p className="text-xs text-gray-500 dark:text-gray-400">
                          {action.description}
                        </p>
                      </div>
                    </button>
                  )}
                </Menu.Item>
              ))}
            </div>

            <div className="border-t border-gray-200 dark:border-gray-700">
              <Menu.Item>
                {({ active }) => (
                  <button
                    onClick={() => console.log('View all actions')}
                    className={cn(
                      active 
                        ? 'bg-gray-100 dark:bg-gray-700' 
                        : 'bg-white dark:bg-gray-800',
                      'block w-full px-4 py-3 text-sm text-center text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors'
                    )}
                  >
                    View all actions
                  </button>
                )}
              </Menu.Item>
            </div>
          </div>
        </Menu.Items>
      </Transition>
    </Menu>
  );
}
