import { GoogleGenAI } from '@google/genai';
import { aiService } from '@/services/ai-service';
import { AIRequest } from '@/services/ai-service';
import fs from 'fs/promises';
import path from 'path';
import os from 'os';

export interface DocumentAnalysisRequest {
  file: File;
  tenantId: string;
  userId: string;
  language?: 'english' | 'arabic';
  context?: {
    relatedToType: string;
    relatedToId: string;
    opportunityTitle?: string;
    companyName?: string;
  };
}

export interface DocumentAnalysisResponse {
  suggestedTitle: string;
  suggestedDescription: string;
  documentType: string;
  keyTopics: string[];
  summary: string;
  confidence: number;
  processingTime: number;
}

export class AIDocumentAnalysisService {
  private genAI: GoogleGenAI;

  constructor() {
    const apiKey = process.env.GOOGLE_AI_API_KEY;
    if (!apiKey) {
      throw new Error('Google AI API key not configured');
    }
    this.genAI = new GoogleGenAI({ apiKey });
  }

  /**
   * Analyze a document using Gemini AI to generate title and description
   */
  async analyzeDocument(request: DocumentAnalysisRequest): Promise<DocumentAnalysisResponse> {
    const startTime = Date.now();

    try {
      // Upload file to Gemini for analysis
      const uploadResponse = await this.uploadFileToGemini(request.file);
      
      // Wait for file processing
      let file = uploadResponse;
      while (file.state === 'PROCESSING') {
        await new Promise(resolve => setTimeout(resolve, 2000));
        file = await this.genAI.files.get({ name: file.name! });
      }

      if (file.state === 'FAILED') {
        throw new Error('Gemini file processing failed');
      }

      // Generate analysis using AI
      const analysisPrompt = this.buildAnalysisPrompt(request);
      
      const aiRequest: AIRequest = {
        prompt: analysisPrompt,
        systemPrompt: this.getSystemPrompt(),
        fileUris: [file.uri!],
        tenantId: request.tenantId,
        userId: request.userId,
        feature: 'document_analysis'
      };

      const response = await aiService.generateResponse(aiRequest, {
        temperature: 0.3,
        maxTokens: 1000
      });

      // Parse the AI response
      const analysis = this.parseAnalysisResponse(response.content);
      
      // Clean up the uploaded file from Gemini
      try {
        await this.genAI.files.delete({ name: file.name! });
      } catch (error) {
        console.warn('Failed to clean up Gemini file:', error);
      }

      const processingTime = Date.now() - startTime;

      return {
        ...analysis,
        processingTime
      };

    } catch (error) {
      console.error('Document analysis error:', error);
      
      // Return fallback analysis
      return this.getFallbackAnalysis(request.file, Date.now() - startTime);
    }
  }

  /**
   * Upload file to Gemini API
   */
  private async uploadFileToGemini(file: File) {
    // Convert File to buffer for Gemini upload
    const arrayBuffer = await file.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);

    // Write to temporary file for Gemini upload
    const tempDir = os.tmpdir();
    const tempPath = path.join(tempDir, `${Date.now()}_${file.name}`);

    try {
      await fs.writeFile(tempPath, buffer);

      // Upload to Gemini using file path
      const uploadResponse = await this.genAI.files.upload({
        file: tempPath,
        config: {
          mimeType: file.type || 'application/octet-stream',
          displayName: file.name
        }
      });

      // Clean up temporary file
      try {
        await fs.unlink(tempPath);
      } catch (error) {
        console.warn('Failed to clean up temporary file:', error);
      }

      return uploadResponse;
    } catch (error) {
      // Clean up temporary file on error
      try {
        await fs.unlink(tempPath);
      } catch (unlinkError) {
        console.warn('Failed to clean up temporary file after error:', unlinkError);
      }
      throw error;
    }
  }

  /**
   * Build analysis prompt based on context
   */
  private buildAnalysisPrompt(request: DocumentAnalysisRequest): string {
    const { context, file, language = 'english' } = request;

    let contextInfo = '';
    if (context) {
      contextInfo = `
Context Information:
- Related to: ${context.relatedToType} (ID: ${context.relatedToId})
${context.opportunityTitle ? `- Opportunity: ${context.opportunityTitle}` : ''}
${context.companyName ? `- Company: ${context.companyName}` : ''}
`;
    }

    const languageInstruction = language === 'arabic'
      ? 'Please provide the summary in Arabic language (العربية). All other fields can be in English.'
      : 'Please provide all content in English.';

    return `
Analyze the uploaded document and provide a comprehensive summary for document management.

${contextInfo}

File Information:
- Filename: ${file.name}
- File Type: ${file.type}
- File Size: ${this.formatFileSize(file.size)}

${languageInstruction}

Please analyze the document content and provide:

1. **Suggested Title**: A clear, descriptive title (max 100 characters)
2. **Suggested Description**: A concise summary of the document's purpose and content (max 300 characters)
3. **Document Type**: Category of the document (e.g., "Contract", "Proposal", "Technical Specification", "Financial Report", etc.)
4. **Key Topics**: 3-5 main topics or keywords from the document
5. **Summary**: A detailed overview of the document content (max 1000 characters) - This is the most important field
6. **Confidence**: Your confidence level in the analysis (0-100)

Focus on creating a comprehensive and useful summary that captures the main points, key information, and business value of the document.
Consider the context provided to make the summary more relevant.

Respond in the following JSON format:
{
  "suggestedTitle": "...",
  "suggestedDescription": "...",
  "documentType": "...",
  "keyTopics": ["topic1", "topic2", "topic3"],
  "summary": "...",
  "confidence": 85
}`;
  }

  /**
   * Get system prompt for document analysis
   */
  private getSystemPrompt(): string {
    return `You are an AI assistant specialized in document analysis for business document management systems. 

Your role is to:
- Analyze document content and extract key information
- Generate clear, professional titles and descriptions
- Identify document types and key topics
- Provide concise summaries suitable for business use
- Consider business context when making suggestions

Guidelines:
- Keep titles concise but descriptive
- Make descriptions actionable and informative
- Use professional business language
- Focus on the document's business value and purpose
- Be accurate and confident in your assessments`;
  }

  /**
   * Parse AI response into structured format
   */
  private parseAnalysisResponse(content: string): Omit<DocumentAnalysisResponse, 'processingTime'> {
    try {
      // Try to extract JSON from the response
      const jsonMatch = content.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const parsed = JSON.parse(jsonMatch[0]);
        return {
          suggestedTitle: parsed.suggestedTitle || '',
          suggestedDescription: parsed.suggestedDescription || '',
          documentType: parsed.documentType || 'Document',
          keyTopics: Array.isArray(parsed.keyTopics) ? parsed.keyTopics : [],
          summary: parsed.summary || '',
          confidence: typeof parsed.confidence === 'number' ? parsed.confidence : 50
        };
      }
    } catch (error) {
      console.error('Failed to parse AI response:', error);
    }

    // Fallback parsing if JSON parsing fails
    return this.extractFromText(content);
  }

  /**
   * Extract information from text response as fallback
   */
  private extractFromText(content: string): Omit<DocumentAnalysisResponse, 'processingTime'> {
    const lines = content.split('\n').map(line => line.trim()).filter(Boolean);
    
    return {
      suggestedTitle: this.extractField(lines, 'title') || 'Document',
      suggestedDescription: this.extractField(lines, 'description') || 'Business document',
      documentType: this.extractField(lines, 'type') || 'Document',
      keyTopics: this.extractTopics(lines),
      summary: this.extractField(lines, 'summary') || 'Document content summary',
      confidence: 50
    };
  }

  /**
   * Extract field from text lines
   */
  private extractField(lines: string[], fieldName: string): string | null {
    const pattern = new RegExp(`${fieldName}[:\\-]?\\s*(.+)`, 'i');
    for (const line of lines) {
      const match = line.match(pattern);
      if (match) {
        return match[1].trim();
      }
    }
    return null;
  }

  /**
   * Extract topics from text lines
   */
  private extractTopics(lines: string[]): string[] {
    const topicsLine = lines.find(line => 
      line.toLowerCase().includes('topic') || 
      line.toLowerCase().includes('keyword')
    );
    
    if (topicsLine) {
      const topics = topicsLine.split(/[,;]/).map(t => t.trim()).filter(Boolean);
      return topics.slice(0, 5);
    }
    
    return [];
  }

  /**
   * Get fallback analysis when AI fails
   */
  private getFallbackAnalysis(file: File, processingTime: number): DocumentAnalysisResponse {
    const extension = file.name.split('.').pop()?.toLowerCase() || '';
    const typeMap: Record<string, string> = {
      'pdf': 'PDF Document',
      'doc': 'Word Document',
      'docx': 'Word Document',
      'xls': 'Excel Spreadsheet',
      'xlsx': 'Excel Spreadsheet',
      'ppt': 'PowerPoint Presentation',
      'pptx': 'PowerPoint Presentation',
      'txt': 'Text Document',
      'jpg': 'Image',
      'jpeg': 'Image',
      'png': 'Image',
      'gif': 'Image'
    };

    return {
      suggestedTitle: file.name.replace(/\.[^/.]+$/, ''), // Remove extension
      suggestedDescription: `${typeMap[extension] || 'Document'} uploaded for business use`,
      documentType: typeMap[extension] || 'Document',
      keyTopics: [extension.toUpperCase(), 'Business Document'],
      summary: `A ${typeMap[extension] || 'document'} file uploaded to the system`,
      confidence: 30,
      processingTime
    };
  }

  /**
   * Format file size for display
   */
  private formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
}

export const aiDocumentAnalysisService = new AIDocumentAnalysisService();
