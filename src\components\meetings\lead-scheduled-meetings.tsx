'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { Phone, Plus, Calendar, Clock, Video, MapPin, Users, TrendingUp } from 'lucide-react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { Badge } from '@/components/ui/badge';
import { MeetingCard } from './meeting-card';
import { ScheduleMeetingDialog } from './schedule-meeting-dialog';
import { MeetingDetailsDialog } from './meeting-details-dialog';
import { ScheduledMeetingWithRelations } from '@/services/scheduled-meeting-service';
import { useToast } from '@/hooks/use-toast';
import { usePermissions } from '@/components/providers/permission-provider';
import { PermissionResource, PermissionAction } from '@/types';
import { PermissionGate } from '@/components/ui/permission-gate';
import { cn } from '@/lib/utils';

interface LeadScheduledMeetingsProps {
  leadId: string;
  contactId?: string | null;
  className?: string;
  onSendMeetingInvitation?: (meetingId: string) => void;
}

interface User {
  id: string;
  firstName: string | null;
  lastName: string | null;
  email: string;
}

interface Contact {
  id: string;
  firstName: string;
  lastName: string;
  email: string | null;
}

interface Lead {
  id: string;
  title: string;
  status: string;
  contact?: {
    firstName: string;
    lastName: string;
  } | null;
}

export function LeadScheduledMeetings({ leadId, contactId, className, onSendMeetingInvitation }: LeadScheduledMeetingsProps) {
  const { toast } = useToast();
  const { hasPermission } = usePermissions();
  const { data: session } = useSession();
  const [meetings, setMeetings] = useState<ScheduledMeetingWithRelations[]>([]);
  const [loading, setLoading] = useState(true);
  const [scheduleDialogOpen, setScheduleDialogOpen] = useState(false);
  const [editingMeeting, setEditingMeeting] = useState<ScheduledMeetingWithRelations | null>(null);
  const [detailsDialogOpen, setDetailsDialogOpen] = useState(false);
  const [selectedMeeting, setSelectedMeeting] = useState<ScheduledMeetingWithRelations | null>(null);
  const [users, setUsers] = useState<User[]>([]);
  const [contacts, setContacts] = useState<Contact[]>([]);
  const [leads, setLeads] = useState<Lead[]>([]);
  const [currentLead, setCurrentLead] = useState<Lead | null>(null);
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const [dataLoading, setDataLoading] = useState(true);

  // Load scheduled meetings for this lead
  const loadMeetings = async () => {
    try {
      const response = await fetch(`/api/meetings?leadId=${leadId}&sortBy=scheduledAt&sortOrder=asc`);
      const data = await response.json();

      if (data.success) {
        setMeetings(data.data.meetings);
      } else {
        console.error('Failed to load meetings:', data.error);
      }
    } catch (error) {
      console.error('Error loading meetings:', error);
    } finally {
      setLoading(false);
    }
  };

  // Load users for assignment (tenant users only)
  const loadUsers = async () => {
    try {
      const response = await fetch('/api/users');
      const data = await response.json();

      if (data.success) {
        setUsers(data.data || []);
      }
    } catch (error) {
      console.error('Error loading users:', error);
    }
  };

  // Set current user from session
  useEffect(() => {
    if (session?.user) {
      setCurrentUser({
        id: session.user.id,
        firstName: session.user.name?.split(' ')[0] || null,
        lastName: session.user.name?.split(' ').slice(1).join(' ') || null,
        email: session.user.email || '',
      });
    }
  }, [session]);

  // Load contacts for selection
  const loadContacts = async () => {
    try {
      const response = await fetch('/api/contacts');
      const data = await response.json();

      if (data.success) {
        setContacts(data.data.contacts || []);
      }
    } catch (error) {
      console.error('Error loading contacts:', error);
    }
  };

  // Load leads for selection
  const loadLeads = async () => {
    try {
      const response = await fetch('/api/leads');
      const data = await response.json();

      if (data.success) {
        setLeads(data.data.leads || []);
      }
    } catch (error) {
      console.error('Error loading leads:', error);
    }
  };

  // Load current lead details
  const loadCurrentLead = async () => {
    try {
      const response = await fetch(`/api/leads/${leadId}`);
      const data = await response.json();

      if (data.success) {
        setCurrentLead(data.data.lead);
      }
    } catch (error) {
      console.error('Error loading current lead:', error);
    }
  };

  useEffect(() => {
    const loadAllData = async () => {
      setDataLoading(true);
      try {
        await Promise.all([
          loadMeetings(),
          loadUsers(),
          loadContacts(),
          loadLeads(),
          loadCurrentLead()
        ]);
      } catch (error) {
        console.error('Error loading data:', error);
      } finally {
        setDataLoading(false);
      }
    };

    loadAllData();
  }, [leadId]);

  const handleScheduleMeeting = async (meetingData: any) => {
    try {
      const response = await fetch('/api/meetings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...meetingData,
          leadId,
          contactId: contactId || undefined,
        }),
      });

      const data = await response.json();

      if (data.success) {
        setMeetings([...meetings, data.data.scheduledMeeting]);
        toast({
          title: 'Success',
          description: 'Meeting scheduled successfully.',
        });
      } else {
        throw new Error(data.error || 'Failed to schedule meeting');
      }
    } catch (error) {
      console.error('Error scheduling meeting:', error);
      toast({
        title: 'Error',
        description: 'Failed to schedule meeting. Please try again.',
        variant: 'destructive',
      });
      throw error;
    }
  };

  const handleUpdateMeeting = async (meetingData: any) => {
    if (!editingMeeting) return;

    try {
      const response = await fetch(`/api/meetings/${editingMeeting.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(meetingData),
      });

      const data = await response.json();

      if (data.success) {
        setMeetings(meetings.map(meeting => 
          meeting.id === editingMeeting.id ? data.data.scheduledMeeting : meeting
        ));
        setEditingMeeting(null);
        toast({
          title: 'Success',
          description: 'Meeting updated successfully.',
        });
      } else {
        throw new Error(data.error || 'Failed to update meeting');
      }
    } catch (error) {
      console.error('Error updating meeting:', error);
      toast({
        title: 'Error',
        description: 'Failed to update meeting. Please try again.',
        variant: 'destructive',
      });
      throw error;
    }
  };

  const handleDeleteMeeting = async (meetingId: string) => {
    try {
      const response = await fetch(`/api/meetings/${meetingId}`, {
        method: 'DELETE',
      });

      const data = await response.json();

      if (data.success) {
        setMeetings(meetings.filter(meeting => meeting.id !== meetingId));
        toast({
          title: 'Success',
          description: 'Meeting deleted successfully.',
        });
      } else {
        throw new Error(data.error || 'Failed to delete meeting');
      }
    } catch (error) {
      console.error('Error deleting meeting:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete meeting. Please try again.',
        variant: 'destructive',
      });
    }
  };

  const handleCompleteMeeting = async (meeting: ScheduledMeetingWithRelations) => {
    try {
      const response = await fetch(`/api/meetings/${meeting.id}/complete`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          meetingNotes: '', // Could be enhanced with a dialog to collect notes
          followUpRequired: false,
        }),
      });

      const data = await response.json();

      if (data.success) {
        setMeetings(meetings.map(m => 
          m.id === meeting.id ? data.data.scheduledMeeting : m
        ));
        toast({
          title: 'Success',
          description: 'Meeting marked as completed.',
        });
      } else {
        throw new Error(data.error || 'Failed to complete meeting');
      }
    } catch (error) {
      console.error('Error completing meeting:', error);
      toast({
        title: 'Error',
        description: 'Failed to complete meeting. Please try again.',
        variant: 'destructive',
      });
    }
  };

  const handleEditMeeting = (meeting: ScheduledMeetingWithRelations) => {
    setEditingMeeting(meeting);
    setScheduleDialogOpen(true);
  };

  const handleViewMeetingDetails = (meeting: ScheduledMeetingWithRelations) => {
    setSelectedMeeting(meeting);
    setDetailsDialogOpen(true);
  };

  const handleSendInvitation = async (meeting: ScheduledMeetingWithRelations) => {
    if (onSendMeetingInvitation) {
      onSendMeetingInvitation(meeting.id);
    } else {
      // Fallback to direct sending if no callback provided
      try {
        const response = await fetch(`/api/meetings/${meeting.id}/send-invitation`, {
          method: 'POST',
        });

        const data = await response.json();

        if (data.success) {
          setMeetings(meetings.map(m =>
            m.id === meeting.id ? { ...m, invitationSent: true } : m
          ));
          toast({
            title: 'Success',
            description: 'Meeting invitation sent successfully.',
          });
        } else {
          throw new Error(data.error || 'Failed to send invitation');
        }
      } catch (error) {
        console.error('Error sending invitation:', error);
        toast({
          title: 'Error',
          description: 'Failed to send invitation. Please try again.',
          variant: 'destructive',
        });
      }
    }
  };



  const upcomingMeetings = meetings.filter(meeting => 
    meeting.status === 'scheduled' && new Date(meeting.scheduledAt) > new Date()
  );

  const pastMeetings = meetings.filter(meeting => 
    meeting.status === 'completed' || new Date(meeting.scheduledAt) <= new Date()
  );

  // Calculate meeting statistics
  const totalMeetings = meetings.length;
  const completedMeetings = meetings.filter(meeting => meeting.status === 'completed').length;
  const upcomingMeetingsCount = upcomingMeetings.length;
  const meetingTypes = meetings.reduce((acc, meeting) => {
    acc[meeting.meetingType] = (acc[meeting.meetingType] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  if (loading || dataLoading) {
    return (
      <Card className={cn("bg-white dark:bg-gray-800 shadow-soft border-0 ring-1 ring-gray-200 dark:ring-gray-700", className)}>
        <CardHeader className="pb-4">
          <CardTitle className="flex items-center space-x-2 text-lg font-semibold">
            <div className="p-2 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
              <Calendar className="w-5 h-5 text-blue-600 dark:text-blue-400" />
            </div>
            <span>Scheduled Meetings</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            {/* Loading Statistics */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {Array.from({ length: 3 }).map((_, i) => (
                <div key={i} className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4">
                  <Skeleton className="h-4 w-16 mb-2" />
                  <Skeleton className="h-6 w-8" />
                </div>
              ))}
            </div>
            {/* Loading Meeting Cards */}
            <div className="space-y-4">
              {Array.from({ length: 2 }).map((_, i) => (
                <div key={i} className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4">
                  <div className="space-y-3">
                    <div className="flex items-center space-x-3">
                      <Skeleton className="h-10 w-10 rounded-lg" />
                      <div className="flex-1">
                        <Skeleton className="h-4 w-3/4 mb-2" />
                        <Skeleton className="h-3 w-1/2" />
                      </div>
                    </div>
                    <Skeleton className="h-4 w-full" />
                  </div>
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <>
      <Card className={cn("bg-white dark:bg-gray-800 shadow-soft border-0 ring-1 ring-gray-200 dark:ring-gray-700", className)}>
        <CardHeader className="pb-4">
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center space-x-2 text-lg font-semibold">
              <div className="p-2 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
                <Calendar className="w-5 h-5 text-blue-600 dark:text-blue-400" />
              </div>
              <span>Scheduled Meetings</span>
              {totalMeetings > 0 && (
                <Badge variant="secondary" className="ml-2">
                  {totalMeetings} total
                </Badge>
              )}
            </CardTitle>
            <PermissionGate
              resource={PermissionResource.MEETINGS}
              action={PermissionAction.CREATE}
            >
              <Button
                size="sm"
                onClick={() => setScheduleDialogOpen(true)}
                className="bg-blue-600 hover:bg-blue-700 text-white"
              >
                <Plus className="w-4 h-4 mr-2" />
                Schedule Meeting
              </Button>
            </PermissionGate>
          </div>
        </CardHeader>

        <CardContent className="space-y-6">
          {meetings.length === 0 ? (
            <div className="text-center py-12">
              <div className="bg-gray-100 dark:bg-gray-700 rounded-full w-20 h-20 flex items-center justify-center mx-auto mb-6">
                <Calendar className="h-10 w-10 text-gray-400" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
                No scheduled meetings
              </h3>
              <p className="text-gray-500 dark:text-gray-400 mb-6 max-w-sm mx-auto">
                Schedule a meeting to start engaging with this lead and move them through your sales process
              </p>
              <PermissionGate
                resource={PermissionResource.MEETINGS}
                action={PermissionAction.CREATE}
              >
                <Button
                  onClick={() => setScheduleDialogOpen(true)}
                  className="bg-blue-600 hover:bg-blue-700 text-white"
                >
                  <Plus className="w-4 h-4 mr-2" />
                  Schedule First Meeting
                </Button>
              </PermissionGate>
            </div>
          ) : (
            <>
              {/* Meeting Statistics */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg p-4">
                  <div className="flex items-center space-x-3">
                    <div className="p-2 bg-blue-100 dark:bg-blue-800 rounded-lg">
                      <Clock className="w-5 h-5 text-blue-600 dark:text-blue-300" />
                    </div>
                    <div>
                      <p className="text-sm font-medium text-blue-900 dark:text-blue-100">Upcoming</p>
                      <p className="text-2xl font-bold text-blue-700 dark:text-blue-300">{upcomingMeetingsCount}</p>
                    </div>
                  </div>
                </div>

                <div className="bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-lg p-4">
                  <div className="flex items-center space-x-3">
                    <div className="p-2 bg-green-100 dark:bg-green-800 rounded-lg">
                      <TrendingUp className="w-5 h-5 text-green-600 dark:text-green-300" />
                    </div>
                    <div>
                      <p className="text-sm font-medium text-green-900 dark:text-green-100">Completed</p>
                      <p className="text-2xl font-bold text-green-700 dark:text-green-300">{completedMeetings}</p>
                    </div>
                  </div>
                </div>

                <div className="bg-gradient-to-br from-purple-50 to-violet-50 dark:from-purple-900/20 dark:to-violet-900/20 rounded-lg p-4">
                  <div className="flex items-center space-x-3">
                    <div className="p-2 bg-purple-100 dark:bg-purple-800 rounded-lg">
                      <Users className="w-5 h-5 text-purple-600 dark:text-purple-300" />
                    </div>
                    <div>
                      <p className="text-sm font-medium text-purple-900 dark:text-purple-100">Total Meetings</p>
                      <p className="text-2xl font-bold text-purple-700 dark:text-purple-300">{totalMeetings}</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Upcoming Meetings */}
              {upcomingMeetings.length > 0 && (
                <div>
                  <div className="flex items-center justify-between mb-4">
                    <h4 className="font-semibold text-gray-900 dark:text-gray-100 flex items-center space-x-2">
                      <div className="p-1.5 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
                        <Clock className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                      </div>
                      <span>Upcoming Meetings</span>
                      <Badge variant="outline" className="ml-2">
                        {upcomingMeetings.length}
                      </Badge>
                    </h4>
                  </div>
                  <div className="space-y-4">
                    {upcomingMeetings.map((meeting) => (
                      <MeetingCard
                        key={meeting.id}
                        meeting={meeting}
                        onEdit={handleEditMeeting}
                        onDelete={handleDeleteMeeting}
                        onComplete={handleCompleteMeeting}
                        onSendInvitation={handleSendInvitation}
                        onClick={handleViewMeetingDetails}
                        variant="upcoming"
                        className="border-l-4 border-l-blue-500 bg-blue-50/50 dark:bg-blue-900/10"
                      />
                    ))}
                  </div>
                </div>
              )}

              {/* Past Meetings */}
              {pastMeetings.length > 0 && (
                <div>
                  <div className="flex items-center justify-between mb-4">
                    <h4 className="font-semibold text-gray-900 dark:text-gray-100 flex items-center space-x-2">
                      <div className="p-1.5 bg-gray-100 dark:bg-gray-700 rounded-lg">
                        <Calendar className="w-4 h-4 text-gray-600 dark:text-gray-400" />
                      </div>
                      <span>Past Meetings</span>
                      <Badge variant="outline" className="ml-2">
                        {pastMeetings.length}
                      </Badge>
                    </h4>
                  </div>
                  <div className="space-y-4">
                    {pastMeetings.slice(0, 5).map((meeting) => (
                      <MeetingCard
                        key={meeting.id}
                        meeting={meeting}
                        onEdit={handleEditMeeting}
                        onDelete={handleDeleteMeeting}
                        onComplete={handleCompleteMeeting}
                        onSendInvitation={handleSendInvitation}
                        onClick={handleViewMeetingDetails}
                        variant="past"
                        className="opacity-75"
                      />
                    ))}
                    {pastMeetings.length > 5 && (
                      <div className="text-center">
                        <p className="text-sm text-gray-500 dark:text-gray-400">
                          And {pastMeetings.length - 5} more past meetings...
                        </p>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Meeting Type Distribution */}
              {Object.keys(meetingTypes).length > 0 && (
                <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4">
                  <h4 className="font-medium text-gray-900 dark:text-gray-100 mb-3 flex items-center">
                    <Calendar className="w-4 h-4 mr-2 text-gray-500" />
                    Meeting Types
                  </h4>
                  <div className="flex flex-wrap gap-2">
                    {Object.entries(meetingTypes).map(([type, count]) => {
                      const Icon = type === 'phone' ? Phone : type === 'video' ? Video : MapPin;
                      const label = type === 'phone' ? 'Phone Call' : type === 'video' ? 'Video Meeting' : 'In-Person';
                      return (
                        <div key={type} className="flex items-center space-x-2 bg-white dark:bg-gray-600 rounded-lg px-3 py-2">
                          <Icon className="w-4 h-4 text-gray-500" />
                          <span className="text-sm font-medium text-gray-700 dark:text-gray-300">{label}</span>
                          <Badge variant="secondary" className="text-xs">{count}</Badge>
                        </div>
                      );
                    })}
                  </div>
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>

      {/* Schedule Meeting Dialog */}
      <ScheduleMeetingDialog
        open={scheduleDialogOpen}
        onOpenChange={(open) => {
          setScheduleDialogOpen(open);
          if (!open) {
            setEditingMeeting(null);
          }
        }}
        onSubmit={editingMeeting ? handleUpdateMeeting : handleScheduleMeeting}
        meeting={editingMeeting}
        leadId={leadId}
        contactId={contactId}
        currentUserId={currentUser?.id}
        users={users}
        contacts={contacts}
        leads={leads}
      />

      {/* Meeting Details Dialog */}
      <MeetingDetailsDialog
        open={detailsDialogOpen}
        onOpenChange={(open) => {
          setDetailsDialogOpen(open);
          if (!open) {
            setSelectedMeeting(null);
          }
        }}
        meeting={selectedMeeting}
      />
    </>
  );
}
