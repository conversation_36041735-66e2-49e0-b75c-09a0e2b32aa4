import { NextRequest, NextResponse } from 'next/server';
import { ProjectManagementService } from '@/services/project-management';
import { withPermission, AuthenticatedRequest } from '@/lib/permission-middleware';
import { PermissionResource, PermissionAction } from '@/types';

const projectService = new ProjectManagementService();

/**
 * GET /api/projects/attention
 * Get projects that need attention (overdue, at risk, no activity)
 */
export const GET = withPermission({
  resource: PermissionResource.PROJECTS,
  action: PermissionAction.READ,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest) => {
  try {
    const projectsNeedingAttention = await projectService.getProjectsNeedingAttention(req.tenantId);

    return NextResponse.json({
      success: true,
      data: projectsNeedingAttention,
      message: 'Projects needing attention retrieved successfully'
    });
  } catch (error) {
    console.error('Get projects needing attention error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch projects needing attention' },
      { status: 500 }
    );
  }
});
