'use client';

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { AlertTriangle, Trash2, Loader2, X } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { CompanyWithRelations } from '@/services/company-management';
import { cn } from '@/lib/utils';

interface DeleteConfirmationDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => Promise<void>;
  company: CompanyWithRelations | null;
  className?: string;
}

export function DeleteConfirmationDialog({
  isOpen,
  onClose,
  onConfirm,
  company,
  className
}: DeleteConfirmationDialogProps) {
  const [isDeleting, setIsDeleting] = useState(false);

  const handleConfirm = async () => {
    if (!company || isDeleting) return;

    try {
      setIsDeleting(true);
      await onConfirm();
      onClose();
    } catch (error) {
      console.error('Failed to delete company:', error);
    } finally {
      setIsDeleting(false);
    }
  };

  const handleClose = () => {
    if (isDeleting) return;
    onClose();
  };

  // Handle keyboard navigation
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape' && !isDeleting) {
      handleClose();
    } else if (e.key === 'Enter' && !isDeleting) {
      handleConfirm();
    }
  };

  if (!company) return null;

  const hasRelatedData = company._count && (
    company._count.contacts > 0 || 
    company._count.leads > 0 || 
    company._count.opportunities > 0
  );

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent 
        className={cn(
          "sm:max-w-md",
          className
        )}
        onKeyDown={handleKeyDown}
        aria-describedby="delete-dialog-description"
      >
        <DialogHeader className="text-center sm:text-left">
          <div className="flex items-center gap-3">
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ duration: 0.2, delay: 0.1 }}
              className="flex h-12 w-12 items-center justify-center rounded-full bg-red-100 dark:bg-red-900/20"
            >
              <AlertTriangle className="h-6 w-6 text-red-600 dark:text-red-400" />
            </motion.div>
            <div className="flex-1">
              <DialogTitle className="text-left text-lg font-semibold text-red-900 dark:text-red-100">
                Delete Company
              </DialogTitle>
            </div>
          </div>
        </DialogHeader>

        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.2, delay: 0.2 }}
          className="space-y-4"
        >
          <DialogDescription 
            id="delete-dialog-description"
            className="text-sm text-gray-600 dark:text-gray-300"
          >
            Are you sure you want to delete{' '}
            <span className="font-semibold text-gray-900 dark:text-gray-100">
              {company.name}
            </span>
            ? This action cannot be undone.
          </DialogDescription>

          {hasRelatedData && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              transition={{ duration: 0.3, delay: 0.3 }}
              className="rounded-lg border border-amber-200 bg-amber-50 p-3 dark:border-amber-800 dark:bg-amber-900/20"
            >
              <div className="flex items-start gap-2">
                <AlertTriangle className="h-4 w-4 text-amber-600 dark:text-amber-400 mt-0.5 flex-shrink-0" />
                <div className="text-sm text-amber-800 dark:text-amber-200">
                  <p className="font-medium mb-1">This will also delete:</p>
                  <ul className="space-y-1 text-xs">
                    {company._count?.contacts > 0 && (
                      <li>• {company._count.contacts} contact{company._count.contacts !== 1 ? 's' : ''}</li>
                    )}
                    {company._count?.leads > 0 && (
                      <li>• {company._count.leads} lead{company._count.leads !== 1 ? 's' : ''}</li>
                    )}
                    {company._count?.opportunities > 0 && (
                      <li>• {company._count.opportunities} opportunit{company._count.opportunities !== 1 ? 'ies' : 'y'}</li>
                    )}
                  </ul>
                </div>
              </div>
            </motion.div>
          )}

          <div className="rounded-lg border border-red-200 bg-red-50 p-3 dark:border-red-800 dark:bg-red-900/20">
            <div className="flex items-start gap-2">
              <Trash2 className="h-4 w-4 text-red-600 dark:text-red-400 mt-0.5 flex-shrink-0" />
              <p className="text-sm text-red-800 dark:text-red-200">
                <span className="font-medium">Warning:</span> This action is permanent and cannot be reversed. 
                All company data and associated records will be permanently deleted from the system.
              </p>
            </div>
          </div>
        </motion.div>

        <DialogFooter className="gap-2 sm:gap-0">
          <Button
            variant="outline"
            onClick={handleClose}
            disabled={isDeleting}
            className="sm:mr-2"
          >
            Cancel
          </Button>
          <Button
            variant="destructive"
            onClick={handleConfirm}
            disabled={isDeleting}
            className="gap-2"
          >
            {isDeleting ? (
              <>
                <Loader2 className="h-4 w-4 animate-spin" />
                Deleting...
              </>
            ) : (
              <>
                <Trash2 className="h-4 w-4" />
                Delete Company
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

// Hook for easier usage
export function useDeleteConfirmationDialog() {
  const [dialog, setDialog] = useState<{
    isOpen: boolean;
    company: CompanyWithRelations | null;
    onConfirm: (() => Promise<void>) | null;
  }>({
    isOpen: false,
    company: null,
    onConfirm: null,
  });

  const showDeleteDialog = (
    company: CompanyWithRelations,
    onConfirm: () => Promise<void>
  ) => {
    setDialog({
      isOpen: true,
      company,
      onConfirm,
    });
  };

  const closeDialog = () => {
    setDialog({
      isOpen: false,
      company: null,
      onConfirm: null,
    });
  };

  const DialogComponent = () => (
    <DeleteConfirmationDialog
      isOpen={dialog.isOpen}
      onClose={closeDialog}
      onConfirm={dialog.onConfirm || (() => Promise.resolve())}
      company={dialog.company}
    />
  );

  return {
    showDeleteDialog,
    closeDialog,
    DialogComponent,
  };
}
