'use client';

import React, { useState, useEffect, use } from 'react';
import { useRouter } from 'next/navigation';
import { useSession } from 'next-auth/react';
import {
  Star,
  Calendar,
  DollarSign,
  User,
  Building,
  Flag,
  Globe,
  Clock,
  Target,
  Mail,
  Phone,
  MessageSquare,
  FileText,
  Activity,
  ArrowLeft,
  MoreHorizontal,
  ArrowRightLeft
} from 'lucide-react';
import { LeadWithRelations } from '@/services/lead-management';

import { PermissionResource, PermissionAction } from '@/types';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import { Separator } from '@/components/ui/separator';
import { LeadQuickActions } from '@/components/leads/lead-quick-actions';
import { ActivityTimeline } from '@/components/contacts/activity-timeline';
import { useEnhancedDeleteDialog, deleteConfigurations } from '@/components/ui/enhanced-delete-dialog';
import { toastFunctions as toast } from '@/hooks/use-toast';
import { PageLayout } from '@/components/layout/page-layout';
import { EmptyState } from '@/components/ui/empty-state';
import { EmailComposer } from '@/components/email/email-composer';
import { EmailHistory } from '@/components/email/email-history';
import { LeadScheduledMeetings } from '@/components/meetings/lead-scheduled-meetings';
import { ScheduleMeetingDialog } from '@/components/meetings/schedule-meeting-dialog';
import { ConvertLeadDialog } from '@/components/leads/convert-lead-dialog';
import { useIsMobile } from '@/hooks/use-mobile';
import { useFormatters } from '@/hooks/use-formatters';

interface LeadDetailPageProps {
  params: Promise<{
    leadId: string;
  }>;
}

export default function LeadDetailPage({ params }: LeadDetailPageProps) {
  const router = useRouter();
  const { data: session } = useSession();
  const resolvedParams = use(params);
  const leadId = resolvedParams.leadId;
  const [lead, setLead] = useState<LeadWithRelations | null>(null);
  const [loading, setLoading] = useState(true);
  const [emailComposerOpen, setEmailComposerOpen] = useState(false);
  const [scheduleMeetingDialogOpen, setScheduleMeetingDialogOpen] = useState(false);
  const [convertDialogOpen, setConvertDialogOpen] = useState(false);
  const [meetingInvitationMeetingId, setMeetingInvitationMeetingId] = useState<string | null>(null);
  const [users, setUsers] = useState<Array<{
    id: string;
    firstName: string | null;
    lastName: string | null;
    email: string;
  }>>([]);
  const [contacts, setContacts] = useState<Array<{
    id: string;
    firstName: string;
    lastName: string;
    email: string | null;
  }>>([]);
  const [leads, setLeads] = useState<Array<{
    id: string;
    title: string;
    status: string;
    contact?: {
      firstName: string;
      lastName: string;
    } | null;
  }>>([]);
  const [dataLoading, setDataLoading] = useState(true);
  const isMobile = useIsMobile();
  const { formatMoney } = useFormatters();

  const { showDeleteDialog } = useEnhancedDeleteDialog();

  // Load lead data
  const loadLead = async () => {
    try {
      const response = await fetch(`/api/leads/${leadId}`);
      const data = await response.json();

      if (data.success) {
        setLead(data.data.lead);
      } else {
        toast.error('Lead not found');
        router.push('/leads');
      }
    } catch (error) {
      console.error('Error loading lead:', error);
      toast.error('Failed to load lead');
      router.push('/leads');
    }
  };

  // Load users for assignment (tenant users only)
  const loadUsers = async () => {
    try {
      const response = await fetch('/api/users');
      const data = await response.json();

      if (data.success) {
        setUsers(data.data || []);
      }
    } catch (error) {
      console.error('Error loading users:', error);
    }
  };

  // Load contacts for selection
  const loadContacts = async () => {
    try {
      const response = await fetch('/api/contacts');
      const data = await response.json();

      if (data.success) {
        setContacts(data.data.contacts || []);
      }
    } catch (error) {
      console.error('Error loading contacts:', error);
    }
  };

  // Load leads for selection
  const loadLeads = async () => {
    try {
      const response = await fetch('/api/leads');
      const data = await response.json();

      if (data.success) {
        setLeads(data.data.leads || []);
      }
    } catch (error) {
      console.error('Error loading leads:', error);
    }
  };

  useEffect(() => {
    const loadAllData = async () => {
      setDataLoading(true);
      try {
        await Promise.all([
          loadLead(),
          loadUsers(),
          loadContacts(),
          loadLeads()
        ]);
      } catch (error) {
        console.error('Error loading data:', error);
      } finally {
        setLoading(false);
        setDataLoading(false);
      }
    };

    if (leadId) {
      loadAllData();
    }
  }, [leadId, router]);

  // Handle delete lead
  const handleDeleteLead = async () => {
    if (!lead) return;

    const confirmed = await showDeleteDialog({
      ...deleteConfigurations.lead,
      itemName: lead.title
    });

    if (confirmed) {
      try {
        const response = await fetch(`/api/leads/${leadId}`, {
          method: 'DELETE'
        });

        if (response.ok) {
          toast.success('Lead deleted successfully');
          router.push('/leads');
        } else {
          toast.error('Failed to delete lead');
        }
      } catch (error) {
        console.error('Error deleting lead:', error);
        toast.error('Failed to delete lead');
      }
    }
  };

  const handleEmailSent = (emailLogId: string) => {
    toast.success('Email sent successfully');
    // Optionally refresh lead data to update last contact date
    if (leadId) {
      const loadLead = async () => {
        try {
          const response = await fetch(`/api/leads/${leadId}`);
          const data = await response.json();
          if (data.success) {
            setLead(data.data.lead);
          }
        } catch (error) {
          console.error('Error refreshing lead:', error);
        }
      };
      loadLead();
    }
  };

  const handleScheduleMeeting = () => {
    setScheduleMeetingDialogOpen(true);
  };

  const handleMeetingScheduled = async (meetingData: any) => {
    try {
      const response = await fetch('/api/meetings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...meetingData,
          leadId,
          contactId: lead?.contactId || undefined,
        }),
      });

      const data = await response.json();

      if (data.success) {
        toast.success('Meeting scheduled successfully');
        setScheduleMeetingDialogOpen(false);
        // Optionally refresh lead data or trigger a refresh of the scheduled meetings component
      } else {
        throw new Error(data.error || 'Failed to schedule meeting');
      }
    } catch (error) {
      console.error('Error scheduling meeting:', error);
      toast.error('Failed to schedule meeting. Please try again.');
      throw error;
    }
  };

  const handleConvertLead = () => {
    setConvertDialogOpen(true);
  };

  const handleConversionSuccess = () => {
    setConvertDialogOpen(false);
    // Redirect to opportunities page or show success message
    toast.success('Lead successfully converted to opportunity');
    router.push('/opportunities');
  };

  const handleSendMeetingInvitation = (meetingId: string) => {
    setMeetingInvitationMeetingId(meetingId);
    setEmailComposerOpen(true);
  };

  const handleEmailComposerClose = () => {
    setEmailComposerOpen(false);
    setMeetingInvitationMeetingId(null);
  };

  // Status badge component
  const StatusBadge = ({ status }: { status: string }) => {
    const statusConfig = {
      new: { color: 'bg-blue-100 text-blue-800', label: 'New' },
      contacted: { color: 'bg-yellow-100 text-yellow-800', label: 'Contacted' },
      qualified: { color: 'bg-green-100 text-green-800', label: 'Qualified' },
      proposal: { color: 'bg-purple-100 text-purple-800', label: 'Proposal' },
      negotiation: { color: 'bg-orange-100 text-orange-800', label: 'Negotiation' },
      closed_won: { color: 'bg-emerald-100 text-emerald-800', label: 'Won' },
      closed_lost: { color: 'bg-red-100 text-red-800', label: 'Lost' }
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.new;
    
    return (
      <Badge className={config.color}>
        {config.label}
      </Badge>
    );
  };

  // Priority badge component
  const PriorityBadge = ({ priority }: { priority: string }) => {
    const priorityConfig = {
      low: { color: 'bg-gray-100 text-gray-800', label: 'Low', icon: Flag },
      medium: { color: 'bg-blue-100 text-blue-800', label: 'Medium', icon: Flag },
      high: { color: 'bg-orange-100 text-orange-800', label: 'High', icon: Flag },
      urgent: { color: 'bg-red-100 text-red-800', label: 'Urgent', icon: Flag }
    };

    const config = priorityConfig[priority as keyof typeof priorityConfig] || priorityConfig.medium;
    const IconComponent = config.icon;
    
    return (
      <Badge className={`${config.color} flex items-center space-x-1`}>
        <IconComponent className="w-3 h-3" />
        <span>{config.label}</span>
      </Badge>
    );
  };

  const actions = lead ? (
    <div className="flex items-center gap-2">
      <Button variant="outline" size="sm" onClick={() => router.push('/leads')}>
        <ArrowLeft className="h-4 w-4 mr-2" />
        Back to Leads
      </Button>

      {/* Quick Actions with Lead Analysis, Edit, and Delete */}
      <LeadQuickActions
        leadId={leadId}
        leadData={lead}
        onEmailClick={() => setEmailComposerOpen(true)}
        onMeetingClick={handleScheduleMeeting}
        onConvertClick={handleConvertLead}
        onEditClick={() => router.push(`/leads/${leadId}/edit`)}
        onDeleteClick={handleDeleteLead}
        onScoreUpdate={(score) => {
          setLead(prev => prev ? { ...prev, score: score.overall } : null);
        }}
      />
    </div>
  ) : (
    <Button variant="outline" size="sm" onClick={() => router.push('/leads')}>
      <ArrowLeft className="h-4 w-4 mr-2" />
      Back to Leads
    </Button>
  );

  if (loading) {
    return (
      <PageLayout
        title="Loading Lead..."
        description="Please wait while we load the lead details"
        actions={actions}
      >
        <div className="animate-pulse space-y-6">
          {/* Enhanced Loading Header */}
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-soft border border-gray-200 dark:border-gray-700 p-4 sm:p-6">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
              <div className="flex flex-wrap items-center gap-3">
                <div className="h-6 w-16 bg-gray-200 dark:bg-gray-700 rounded"></div>
                <div className="h-6 w-20 bg-gray-200 dark:bg-gray-700 rounded"></div>
                <div className="h-6 w-24 bg-gray-200 dark:bg-gray-700 rounded"></div>
              </div>
              <div className="flex items-center gap-4">
                <div className="h-4 w-32 bg-gray-200 dark:bg-gray-700 rounded"></div>
                <div className="h-4 w-24 bg-gray-200 dark:bg-gray-700 rounded"></div>
              </div>
            </div>
          </div>

          {/* Enhanced Loading Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-4 sm:gap-6">
            <div className="lg:col-span-3 space-y-4 sm:space-y-6">
              <div className="h-80 bg-gray-200 dark:bg-gray-700 rounded-xl"></div>
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6">
                <div className="lg:col-span-2 h-64 bg-gray-200 dark:bg-gray-700 rounded-xl"></div>
                <div className="lg:col-span-2 h-64 bg-gray-200 dark:bg-gray-700 rounded-xl"></div>
              </div>
              <div className="h-96 bg-gray-200 dark:bg-gray-700 rounded-xl"></div>
            </div>
            <div className="lg:col-span-1 space-y-4 sm:space-y-6">
              <div className="h-64 bg-gray-200 dark:bg-gray-700 rounded-xl"></div>
              <div className="h-48 bg-gray-200 dark:bg-gray-700 rounded-xl"></div>
              <div className="h-32 bg-gray-200 dark:bg-gray-700 rounded-xl"></div>
            </div>
          </div>
        </div>
      </PageLayout>
    );
  }

  if (!lead) {
    return (
      <PageLayout
        title="Lead Not Found"
        description="The lead you're looking for doesn't exist"
        actions={actions}
      >
        <Card>
          <CardContent className="text-center py-12">
            <EmptyState
              icon={<FileText className="w-12 h-12" />}
              title="Lead not found"
              description="The lead you're looking for doesn't exist or you don't have permission to view it."
              action={{
                label: 'Back to Leads',
                onClick: () => router.push('/leads'),
                variant: 'primary'
              }}
            />
          </CardContent>
        </Card>
      </PageLayout>
    );
  }

  return (
    <PageLayout
      title={lead.title}
      description={`Lead details and management for ${lead.title}`}
      actions={actions}
    >
      {/* Enhanced Lead Status Header */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-soft border border-gray-200 dark:border-gray-700 p-4 sm:p-6 mb-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div className="flex flex-wrap items-center gap-3">
            <StatusBadge status={lead.status} />
            <PriorityBadge priority={(lead as LeadWithRelations & { priority?: string }).priority || 'medium'} />
            <div className="flex items-center space-x-2 bg-yellow-50 dark:bg-yellow-900/20 px-3 py-1.5 rounded-lg">
              <Star className="w-4 h-4 text-yellow-500" />
              <span className="font-semibold text-yellow-700 dark:text-yellow-300">{lead.score}</span>
              <span className="text-xs text-yellow-600 dark:text-yellow-400">Score</span>
            </div>
          </div>

          {/* Quick Stats */}
          <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-4 text-sm text-gray-600 dark:text-gray-400">
            <div className="flex items-center space-x-1">
              <Calendar className="w-4 h-4" />
              <span>Created {new Date(lead.createdAt).toLocaleDateString()}</span>
            </div>
            {(lead as LeadWithRelations & { value?: number }).value && (
              <div className="flex items-center space-x-1 text-green-600 dark:text-green-400">
                <DollarSign className="w-4 h-4" />
                <span className="font-medium">{formatMoney((lead as LeadWithRelations & { value?: number }).value)}</span>
              </div>
            )}
          </div>
        </div>
      </div>



      {/* Full Width Layout */}
      <div className="space-y-6">
          {/* Lead Information - Enhanced Design */}
          <Card className="bg-white dark:bg-gray-800 shadow-soft border-0 ring-1 ring-gray-200 dark:ring-gray-700">
            <CardHeader className="pb-4">
              <CardTitle className="flex items-center space-x-2 text-lg font-semibold">
                <FileText className="w-5 h-5 text-blue-500" />
                <span>Lead Information</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Description Section */}
              {(lead as LeadWithRelations & { description?: string }).description && (
                <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4">
                  <h4 className="font-medium text-gray-900 dark:text-gray-100 mb-2 flex items-center">
                    <MessageSquare className="w-4 h-4 mr-2 text-gray-500" />
                    Description
                  </h4>
                  <p className="text-gray-700 dark:text-gray-300 leading-relaxed">
                    {(lead as LeadWithRelations & { description?: string }).description}
                  </p>
                </div>
              )}

              {/* Key Details Grid - Responsive */}
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                {lead.source && (
                  <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
                    <h4 className="font-medium text-blue-900 dark:text-blue-100 mb-2 flex items-center text-sm">
                      <Globe className="w-4 h-4 mr-2" />
                      Source
                    </h4>
                    <p className="text-blue-700 dark:text-blue-300 font-medium">{lead.source}</p>
                  </div>
                )}

                {(lead as LeadWithRelations & { value?: number }).value && (
                  <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-4">
                    <h4 className="font-medium text-green-900 dark:text-green-100 mb-2 flex items-center text-sm">
                      <DollarSign className="w-4 h-4 mr-2" />
                      Estimated Value
                    </h4>
                    <p className="text-green-700 dark:text-green-300 font-bold text-lg">
                      {formatMoney((lead as LeadWithRelations & { value?: number }).value)}
                    </p>
                  </div>
                )}

                {(lead as LeadWithRelations & { expectedCloseDate?: string | Date }).expectedCloseDate && (
                  <div className="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-4">
                    <h4 className="font-medium text-purple-900 dark:text-purple-100 mb-2 flex items-center text-sm">
                      <Calendar className="w-4 h-4 mr-2" />
                      Expected Close
                    </h4>
                    <p className="text-purple-700 dark:text-purple-300 font-medium">
                      {new Date((lead as LeadWithRelations & { expectedCloseDate?: string | Date }).expectedCloseDate!).toLocaleDateString()}
                    </p>
                  </div>
                )}

                {lead.owner && (
                  <div className="bg-orange-50 dark:bg-orange-900/20 rounded-lg p-4">
                    <h4 className="font-medium text-orange-900 dark:text-orange-100 mb-3 flex items-center text-sm">
                      <User className="w-4 h-4 mr-2" />
                      Owner
                    </h4>
                    <div className="flex items-center space-x-3">
                      <Avatar className="w-8 h-8">
                        <AvatarImage
                          src={lead.owner.avatar || undefined}
                          alt={`${lead.owner.firstName} ${lead.owner.lastName}`}
                        />
                        <AvatarFallback className="bg-orange-100 dark:bg-orange-800 text-orange-600 dark:text-orange-300 text-sm font-medium">
                          {lead.owner.firstName?.charAt(0)}{lead.owner.lastName?.charAt(0)}
                        </AvatarFallback>
                      </Avatar>
                      <span className="text-orange-700 dark:text-orange-300 font-medium">
                        {lead.owner.firstName} {lead.owner.lastName}
                      </span>
                    </div>
                  </div>
                )}
              </div>

              {/* Lead Metadata Section */}
              <div className="border-t border-gray-200 dark:border-gray-700 pt-6">
                <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-4 flex items-center">
                  <Clock className="w-5 h-5 mr-2 text-gray-500" />
                  Lead Details
                </h4>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4">
                    <div className="flex items-center space-x-2 mb-2">
                      <Calendar className="w-4 h-4 text-gray-500" />
                      <span className="text-sm font-medium text-gray-600 dark:text-gray-400">Created</span>
                    </div>
                    <p className="text-gray-900 dark:text-gray-100 font-semibold">
                      {new Date(lead.createdAt).toLocaleDateString('en-US', {
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric'
                      })}
                    </p>
                  </div>

                  <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4">
                    <div className="flex items-center space-x-2 mb-2">
                      <Clock className="w-4 h-4 text-gray-500" />
                      <span className="text-sm font-medium text-gray-600 dark:text-gray-400">Last Updated</span>
                    </div>
                    <p className="text-gray-900 dark:text-gray-100 font-semibold">
                      {new Date(lead.updatedAt).toLocaleDateString('en-US', {
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric'
                      })}
                    </p>
                  </div>

                  {lead.createdBy && (
                    <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4">
                      <div className="flex items-center space-x-2 mb-3">
                        <User className="w-4 h-4 text-gray-500" />
                        <span className="text-sm font-medium text-gray-600 dark:text-gray-400">Created By</span>
                      </div>
                      <div className="flex items-center space-x-3">
                        <Avatar className="w-6 h-6">
                          <AvatarImage
                            src={lead.createdBy.avatar || undefined}
                            alt={`${lead.createdBy.firstName} ${lead.createdBy.lastName}`}
                          />
                          <AvatarFallback className="bg-gray-200 dark:bg-gray-600 text-gray-600 dark:text-gray-300 text-xs font-medium">
                            {lead.createdBy.firstName?.charAt(0)}{lead.createdBy.lastName?.charAt(0)}
                          </AvatarFallback>
                        </Avatar>
                        <span className="text-gray-900 dark:text-gray-100 font-semibold text-sm">
                          {lead.createdBy.firstName} {lead.createdBy.lastName}
                        </span>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Contact and Company Information - Enhanced Layout */}
              {(lead.contact || lead.company) && (
                <div className="border-t border-gray-200 dark:border-gray-700 pt-6">
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    {lead.contact && (
                      <div className="bg-gradient-to-br from-indigo-50 to-blue-50 dark:from-indigo-900/20 dark:to-blue-900/20 rounded-lg p-5">
                        <h4 className="font-semibold text-indigo-900 dark:text-indigo-100 mb-3 flex items-center">
                          <User className="w-5 h-5 mr-2" />
                          Contact Information
                        </h4>
                        <div className="space-y-3">
                          <div className="flex items-center space-x-3">
                            <div className="w-8 h-8 bg-indigo-100 dark:bg-indigo-800 rounded-full flex items-center justify-center">
                              <User className="w-4 h-4 text-indigo-600 dark:text-indigo-300" />
                            </div>
                            <span className="text-indigo-800 dark:text-indigo-200 font-medium">
                              {lead.contact.firstName} {lead.contact.lastName}
                            </span>
                          </div>
                          {lead.contact.email && (
                            <div className="flex items-center space-x-3">
                              <div className="w-8 h-8 bg-indigo-100 dark:bg-indigo-800 rounded-full flex items-center justify-center">
                                <Mail className="w-4 h-4 text-indigo-600 dark:text-indigo-300" />
                              </div>
                              <span className="text-indigo-700 dark:text-indigo-300">{lead.contact.email}</span>
                            </div>
                          )}
                          {lead.contact.phone && (
                            <div className="flex items-center space-x-3">
                              <div className="w-8 h-8 bg-indigo-100 dark:bg-indigo-800 rounded-full flex items-center justify-center">
                                <Phone className="w-4 h-4 text-indigo-600 dark:text-indigo-300" />
                              </div>
                              <span className="text-indigo-700 dark:text-indigo-300">{lead.contact.phone}</span>
                            </div>
                          )}
                        </div>
                      </div>
                    )}

                    {lead.company && (
                      <div className="bg-gradient-to-br from-emerald-50 to-green-50 dark:from-emerald-900/20 dark:to-green-900/20 rounded-lg p-5">
                        <h4 className="font-semibold text-emerald-900 dark:text-emerald-100 mb-3 flex items-center">
                          <Building className="w-5 h-5 mr-2" />
                          Company Information
                        </h4>
                        <div className="flex items-center space-x-3">
                          <div className="w-8 h-8 bg-emerald-100 dark:bg-emerald-800 rounded-full flex items-center justify-center">
                            <Building className="w-4 h-4 text-emerald-600 dark:text-emerald-300" />
                          </div>
                          <span className="text-emerald-800 dark:text-emerald-200 font-medium">{lead.company.name}</span>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Communication & Activity Section - Enhanced Layout */}
          <div className="space-y-4 sm:space-y-6">
            {/* Scheduled Meetings - Enhanced Card */}
            <LeadScheduledMeetings
              leadId={leadId}
              contactId={lead.contactId}
              onSendMeetingInvitation={handleSendMeetingInvitation}
            />

            {/* Email History - Enhanced Card */}
            <EmailHistory
              leadId={leadId}
              contactId={lead.contactId || undefined}
            />
          </div>

          {/* Activity Timeline - Full Width Enhanced */}
          <Card className="bg-white dark:bg-gray-800 shadow-soft border-0 ring-1 ring-gray-200 dark:ring-gray-700">
            <CardHeader className="pb-4">
              <CardTitle className="flex items-center space-x-2 text-lg font-semibold">
                <Activity className="w-5 h-5 text-purple-500" />
                <span>Activity Timeline</span>
                <Badge variant="secondary" className="ml-auto">
                  Recent Activity
                </Badge>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ActivityTimeline
                relatedToType="lead"
                relatedToId={leadId}
              />
            </CardContent>
          </Card>
      </div>

      {/* Email Composer */}
      <EmailComposer
        isOpen={emailComposerOpen}
        onClose={handleEmailComposerClose}
        lead={lead}
        meetingId={meetingInvitationMeetingId || undefined}
        onEmailSent={handleEmailSent}
      />

      {/* Schedule Meeting Dialog */}
      {lead && (
        <ScheduleMeetingDialog
          open={scheduleMeetingDialogOpen}
          onOpenChange={setScheduleMeetingDialogOpen}
          onSubmit={handleMeetingScheduled}
          leadId={leadId}
          contactId={lead.contactId || undefined}
          currentUserId={session?.user?.id}
          users={users}
          contacts={contacts}
          leads={leads}
        />
      )}

      {/* Convert Lead Dialog */}
      {lead && (
        <ConvertLeadDialog
          open={convertDialogOpen}
          onOpenChange={setConvertDialogOpen}
          leadId={leadId}
          leadTitle={lead.title}
          leadOwnerId={lead.ownerId || undefined}
          leadValue={(lead as LeadWithRelations & { value?: number }).value}
          onSuccess={handleConversionSuccess}
        />
      )}
    </PageLayout>
  );
}
