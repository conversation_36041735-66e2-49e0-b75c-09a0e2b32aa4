'use client';

import { useState, useEffect } from 'react';
import { Search, Filter, Calendar, Plus } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { CallCard } from './call-card';
import { ScheduledCallWithRelations } from '@/services/scheduled-call-service';
import { useToast } from '@/hooks/use-toast';

interface CallsListProps {
  calls: ScheduledCallWithRelations[];
  loading?: boolean;
  onEdit?: (call: ScheduledCallWithRelations) => void;
  onDelete?: (callId: string) => void;
  onComplete?: (call: ScheduledCallWithRelations) => void;
  onScheduleNew?: () => void;
  showFilters?: boolean;
  showActions?: boolean;
  title?: string;
  emptyMessage?: string;
  compact?: boolean;
}

interface Filters {
  search: string;
  status: string;
  callType: string;
  dateRange: string;
}

export function CallsList({
  calls,
  loading = false,
  onEdit,
  onDelete,
  onComplete,
  onScheduleNew,
  showFilters = true,
  showActions = true,
  title = 'Scheduled Calls',
  emptyMessage = 'No scheduled calls found.',
  compact = false,
}: CallsListProps) {
  const { toast } = useToast();
  const [filters, setFilters] = useState<Filters>({
    search: '',
    status: 'all',
    callType: 'all',
    dateRange: 'all',
  });

  const [filteredCalls, setFilteredCalls] = useState<ScheduledCallWithRelations[]>(calls);

  // Filter calls based on current filters
  useEffect(() => {
    let filtered = [...calls];

    // Search filter
    if (filters.search) {
      const searchLower = filters.search.toLowerCase();
      filtered = filtered.filter(call => 
        call.title.toLowerCase().includes(searchLower) ||
        call.description?.toLowerCase().includes(searchLower) ||
        (call.contact && 
          `${call.contact.firstName} ${call.contact.lastName}`.toLowerCase().includes(searchLower)) ||
        (call.lead?.contact && 
          `${call.lead.contact.firstName} ${call.lead.contact.lastName}`.toLowerCase().includes(searchLower))
      );
    }

    // Status filter
    if (filters.status !== 'all') {
      filtered = filtered.filter(call => call.status === filters.status);
    }

    // Call type filter
    if (filters.callType !== 'all') {
      filtered = filtered.filter(call => call.callType === filters.callType);
    }

    // Date range filter
    if (filters.dateRange !== 'all') {
      const now = new Date();
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
      const tomorrow = new Date(today);
      tomorrow.setDate(tomorrow.getDate() + 1);
      const nextWeek = new Date(today);
      nextWeek.setDate(nextWeek.getDate() + 7);

      filtered = filtered.filter(call => {
        const callDate = new Date(call.scheduledAt);
        
        switch (filters.dateRange) {
          case 'today':
            return callDate >= today && callDate < tomorrow;
          case 'upcoming':
            return callDate >= now;
          case 'this-week':
            return callDate >= today && callDate < nextWeek;
          case 'past':
            return callDate < now;
          default:
            return true;
        }
      });
    }

    setFilteredCalls(filtered);
  }, [calls, filters]);

  const handleFilterChange = (key: keyof Filters, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  const handleDelete = async (callId: string) => {
    if (!onDelete) return;
    
    try {
      await onDelete(callId);
      toast({
        title: 'Success',
        description: 'Scheduled call deleted successfully.',
      });
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to delete scheduled call.',
        variant: 'destructive',
      });
    }
  };

  const handleComplete = async (call: ScheduledCallWithRelations) => {
    if (!onComplete) return;
    
    try {
      await onComplete(call);
      toast({
        title: 'Success',
        description: 'Call marked as completed successfully.',
      });
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to complete call.',
        variant: 'destructive',
      });
    }
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>{title}</CardTitle>
            {onScheduleNew && (
              <Button disabled>
                <Plus className="h-4 w-4 mr-2" />
                Schedule Call
              </Button>
            )}
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {Array.from({ length: 3 }).map((_, i) => (
              <div key={i} className="space-y-3">
                <Skeleton className="h-4 w-3/4" />
                <Skeleton className="h-4 w-1/2" />
                <Skeleton className="h-20 w-full" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle>{title}</CardTitle>
          {onScheduleNew && (
            <Button onClick={onScheduleNew}>
              <Plus className="h-4 w-4 mr-2" />
              Schedule Call
            </Button>
          )}
        </div>

        {showFilters && (
          <div className="flex flex-col sm:flex-row gap-4 mt-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search calls..."
                value={filters.search}
                onChange={(e) => handleFilterChange('search', e.target.value)}
                className="pl-10"
              />
            </div>
            
            <Select value={filters.status} onValueChange={(value) => handleFilterChange('status', value)}>
              <SelectTrigger className="w-full sm:w-40">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="scheduled">Scheduled</SelectItem>
                <SelectItem value="completed">Completed</SelectItem>
                <SelectItem value="cancelled">Cancelled</SelectItem>
                <SelectItem value="rescheduled">Rescheduled</SelectItem>
              </SelectContent>
            </Select>

            <Select value={filters.callType} onValueChange={(value) => handleFilterChange('callType', value)}>
              <SelectTrigger className="w-full sm:w-40">
                <SelectValue placeholder="Type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                <SelectItem value="phone">Phone</SelectItem>
                <SelectItem value="video">Video</SelectItem>
                <SelectItem value="in-person">In-Person</SelectItem>
              </SelectContent>
            </Select>

            <Select value={filters.dateRange} onValueChange={(value) => handleFilterChange('dateRange', value)}>
              <SelectTrigger className="w-full sm:w-40">
                <SelectValue placeholder="Date" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Dates</SelectItem>
                <SelectItem value="today">Today</SelectItem>
                <SelectItem value="upcoming">Upcoming</SelectItem>
                <SelectItem value="this-week">This Week</SelectItem>
                <SelectItem value="past">Past</SelectItem>
              </SelectContent>
            </Select>
          </div>
        )}
      </CardHeader>

      <CardContent>
        {filteredCalls.length === 0 ? (
          <div className="text-center py-8">
            <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500">{emptyMessage}</p>
            {onScheduleNew && (
              <Button onClick={onScheduleNew} className="mt-4">
                <Plus className="h-4 w-4 mr-2" />
                Schedule Your First Call
              </Button>
            )}
          </div>
        ) : (
          <div className="space-y-4">
            {filteredCalls.map((call) => (
              <CallCard
                key={call.id}
                call={call}
                onEdit={onEdit}
                onDelete={handleDelete}
                onComplete={handleComplete}
                showActions={showActions}
                compact={compact}
              />
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
