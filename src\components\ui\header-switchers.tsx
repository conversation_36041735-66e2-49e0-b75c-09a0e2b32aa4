'use client';

import React from 'react';
import { cn } from '@/lib/utils';
import { ThemeToggle } from '@/components/ui/theme-toggle';
import { LanguageSwitcher } from '@/components/layout/language-switcher';
import { Separator } from '@/components/ui/separator';

interface HeaderSwitchersProps {
  className?: string;
  showLabels?: boolean;
  orientation?: 'horizontal' | 'vertical';
  size?: 'sm' | 'default' | 'lg';
  variant?: 'default' | 'compact';
}

export function HeaderSwitchers({ 
  className,
  showLabels = false,
  orientation = 'horizontal',
  size = 'default',
  variant = 'default'
}: HeaderSwitchersProps) {
  if (variant === 'compact') {
    return (
      <div className={cn(
        "flex items-center gap-1",
        orientation === 'vertical' && "flex-col",
        className
      )}>
        <ThemeToggle variant="button" size={size} />
        <LanguageSwitcher size={size} />
      </div>
    );
  }

  return (
    <div className={cn(
      "flex items-center",
      orientation === 'horizontal' ? "gap-2" : "flex-col gap-2",
      className
    )}>
      <ThemeToggle 
        variant="dropdown" 
        showLabel={showLabels} 
        size={size} 
      />
      {orientation === 'horizontal' && <Separator orientation="vertical" className="h-6" />}
      <LanguageSwitcher 
        showLabel={showLabels} 
        size={size} 
      />
    </div>
  );
}

// Individual header components
export function HeaderThemeToggle({ 
  showLabel = false, 
  size = 'default' as const,
  className 
}: { 
  showLabel?: boolean; 
  size?: 'sm' | 'default' | 'lg';
  className?: string;
}) {
  return (
    <ThemeToggle 
      variant="dropdown" 
      showLabel={showLabel} 
      size={size}
      className={className}
    />
  );
}

export function HeaderLanguageSwitcher({ 
  showLabel = false, 
  size = 'default' as const,
  className 
}: { 
  showLabel?: boolean; 
  size?: 'sm' | 'default' | 'lg';
  className?: string;
}) {
  return (
    <LanguageSwitcher 
      showLabel={showLabel} 
      size={size}
      className={className}
    />
  );
}
