'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { 
  Activity, 
  Zap, 
  Clock, 
  Database,
  RefreshCw,
  AlertTriangle,
  CheckCircle,
  TrendingUp,
  TrendingDown
} from 'lucide-react';

interface PerformanceMetrics {
  responseTime: number;
  cacheHitRate: number;
  memoryUsage: number;
  dbResponseTime: number;
  errorRate: number;
  timestamp: number;
}

interface PerformanceMonitorProps {
  autoRefresh?: boolean;
  refreshInterval?: number;
  showDetails?: boolean;
}

export function PerformanceMonitor({ 
  autoRefresh = true, 
  refreshInterval = 30000,
  showDetails = false 
}: PerformanceMonitorProps) {
  const [metrics, setMetrics] = useState<PerformanceMetrics | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [history, setHistory] = useState<PerformanceMetrics[]>([]);

  const fetchMetrics = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch('/api/admin/performance');
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      
      if (data.success) {
        const newMetrics: PerformanceMetrics = {
          responseTime: data.data.apiMetrics?.averageResponseTime || 0,
          cacheHitRate: data.data.cacheMetrics?.hitRate || 0,
          memoryUsage: data.data.memoryMetrics?.heapUsedPercentage || 0,
          dbResponseTime: data.data.databaseMetrics?.queryStats?.average || 0,
          errorRate: data.data.apiMetrics?.errorRate || 0,
          timestamp: Date.now()
        };

        setMetrics(newMetrics);
        
        // Keep last 20 data points for trend analysis
        setHistory(prev => [...prev.slice(-19), newMetrics]);
      } else {
        throw new Error(data.error || 'Failed to fetch metrics');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      setError(errorMessage);
      console.error('Performance metrics fetch error:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchMetrics();

    if (autoRefresh) {
      const interval = setInterval(fetchMetrics, refreshInterval);
      return () => clearInterval(interval);
    }
  }, [fetchMetrics, autoRefresh, refreshInterval]);

  const getHealthStatus = (value: number, thresholds: { good: number; warning: number }) => {
    if (value <= thresholds.good) return { status: 'good', color: 'green', icon: CheckCircle };
    if (value <= thresholds.warning) return { status: 'warning', color: 'yellow', icon: AlertTriangle };
    return { status: 'critical', color: 'red', icon: AlertTriangle };
  };

  const getTrend = (current: number, previous: number) => {
    if (!previous) return null;
    const change = ((current - previous) / previous) * 100;
    if (Math.abs(change) < 5) return null; // No significant change
    return change > 0 ? 'up' : 'down';
  };

  const formatDuration = (ms: number) => {
    if (ms < 1000) return `${Math.round(ms)}ms`;
    return `${(ms / 1000).toFixed(1)}s`;
  };

  if (loading && !metrics) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <RefreshCw className="h-6 w-6 animate-spin mr-2" />
            <span>Loading performance metrics...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center text-red-600">
              <AlertTriangle className="h-5 w-5 mr-2" />
              <span>Failed to load metrics: {error}</span>
            </div>
            <Button onClick={fetchMetrics} variant="outline" size="sm">
              <RefreshCw className="h-4 w-4 mr-2" />
              Retry
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!metrics) return null;

  const responseTimeHealth = getHealthStatus(metrics.responseTime, { good: 200, warning: 500 });
  const dbHealth = getHealthStatus(metrics.dbResponseTime, { good: 100, warning: 300 });
  const memoryHealth = getHealthStatus(metrics.memoryUsage, { good: 70, warning: 85 });
  const errorRateHealth = getHealthStatus(metrics.errorRate, { good: 1, warning: 5 });

  const previousMetrics = history[history.length - 2];
  const responseTimeTrend = previousMetrics ? getTrend(metrics.responseTime, previousMetrics.responseTime) : null;
  const memoryTrend = previousMetrics ? getTrend(metrics.memoryUsage, previousMetrics.memoryUsage) : null;

  return (
    <div className="space-y-4">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold">Performance Monitor</h3>
        <div className="flex items-center gap-2">
          <Badge variant="outline" className="text-xs">
            Last updated: {new Date(metrics.timestamp).toLocaleTimeString()}
          </Badge>
          <Button onClick={fetchMetrics} variant="outline" size="sm" disabled={loading}>
            <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
          </Button>
        </div>
      </div>

      {/* Metrics Grid */}
      <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
        {/* Response Time */}
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Zap className="h-4 w-4 text-blue-500" />
                <span className="text-sm font-medium">Response</span>
              </div>
              {responseTimeTrend && (
                <div className="flex items-center">
                  {responseTimeTrend === 'up' ? (
                    <TrendingUp className="h-3 w-3 text-red-500" />
                  ) : (
                    <TrendingDown className="h-3 w-3 text-green-500" />
                  )}
                </div>
              )}
            </div>
            <div className="mt-2">
              <div className="text-lg font-bold">{formatDuration(metrics.responseTime)}</div>
              <Badge 
                variant={responseTimeHealth.status === 'good' ? 'default' : 
                        responseTimeHealth.status === 'warning' ? 'secondary' : 'destructive'}
                className="text-xs"
              >
                {responseTimeHealth.status.toUpperCase()}
              </Badge>
            </div>
          </CardContent>
        </Card>

        {/* Database Response */}
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Database className="h-4 w-4 text-green-500" />
              <span className="text-sm font-medium">Database</span>
            </div>
            <div className="mt-2">
              <div className="text-lg font-bold">{formatDuration(metrics.dbResponseTime)}</div>
              <Badge 
                variant={dbHealth.status === 'good' ? 'default' : 
                        dbHealth.status === 'warning' ? 'secondary' : 'destructive'}
                className="text-xs"
              >
                {dbHealth.status.toUpperCase()}
              </Badge>
            </div>
          </CardContent>
        </Card>

        {/* Cache Hit Rate */}
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Activity className="h-4 w-4 text-purple-500" />
              <span className="text-sm font-medium">Cache</span>
            </div>
            <div className="mt-2">
              <div className="text-lg font-bold">{metrics.cacheHitRate.toFixed(1)}%</div>
              <Progress value={metrics.cacheHitRate} className="mt-1 h-2" />
            </div>
          </CardContent>
        </Card>

        {/* Memory Usage */}
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Clock className="h-4 w-4 text-orange-500" />
                <span className="text-sm font-medium">Memory</span>
              </div>
              {memoryTrend && (
                <div className="flex items-center">
                  {memoryTrend === 'up' ? (
                    <TrendingUp className="h-3 w-3 text-red-500" />
                  ) : (
                    <TrendingDown className="h-3 w-3 text-green-500" />
                  )}
                </div>
              )}
            </div>
            <div className="mt-2">
              <div className="text-lg font-bold">{metrics.memoryUsage.toFixed(1)}%</div>
              <Progress 
                value={metrics.memoryUsage} 
                className="mt-1 h-2"
                // @ts-ignore
                indicatorClassName={
                  metrics.memoryUsage > 85 ? 'bg-red-500' :
                  metrics.memoryUsage > 70 ? 'bg-yellow-500' : 'bg-green-500'
                }
              />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Error Rate */}
      {metrics.errorRate > 0 && (
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <AlertTriangle className="h-4 w-4 text-red-500" />
                <span className="text-sm font-medium">Error Rate</span>
              </div>
              <Badge variant="destructive">
                {metrics.errorRate.toFixed(2)}%
              </Badge>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Performance Summary */}
      {showDetails && (
        <Card>
          <CardHeader>
            <CardTitle className="text-sm">Performance Summary</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span>Overall Health:</span>
                <Badge variant={
                  responseTimeHealth.status === 'good' && 
                  dbHealth.status === 'good' && 
                  memoryHealth.status === 'good' && 
                  errorRateHealth.status === 'good' ? 'default' : 'secondary'
                }>
                  {responseTimeHealth.status === 'good' && 
                   dbHealth.status === 'good' && 
                   memoryHealth.status === 'good' && 
                   errorRateHealth.status === 'good' ? 'Healthy' : 'Needs Attention'}
                </Badge>
              </div>
              <div className="flex justify-between">
                <span>Data Points:</span>
                <span>{history.length}</span>
              </div>
              <div className="flex justify-between">
                <span>Monitoring Since:</span>
                <span>
                  {history.length > 0 ? 
                    new Date(history[0].timestamp).toLocaleTimeString() : 
                    'Just started'
                  }
                </span>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
