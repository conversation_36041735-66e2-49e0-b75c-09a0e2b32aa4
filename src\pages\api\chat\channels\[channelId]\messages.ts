import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../../../../../lib/auth';
import { ChatMessageService } from '../../../../../services/communication';
import { withTenant } from '../../../../../lib/middleware/withTenant';
import { withRateLimit } from '../../../../../lib/middleware/withRateLimit';

async function handler(req: NextApiRequest, res: NextApiResponse) {
  const session = await getServerSession(req, res, authOptions);
  
  if (!session?.user?.id) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  const tenantId = req.headers['x-tenant-id'] as string;
  const userId = session.user.id;
  const channelId = req.query.channelId as string;

  if (!channelId) {
    return res.status(400).json({ error: 'Channel ID is required' });
  }

  try {
    switch (req.method) {
      case 'GET':
        const { limit, offset, parentMessageId } = req.query;
        const messages = await ChatMessageService.getMessages(
          tenantId,
          channelId,
          userId,
          {
            limit: limit ? parseInt(limit as string) : undefined,
            offset: offset ? parseInt(offset as string) : undefined,
            parentMessageId: parentMessageId as string,
          }
        );
        return res.status(200).json(messages);

      case 'POST':
        const newMessage = await ChatMessageService.createMessage(
          tenantId,
          userId,
          {
            ...req.body,
            channelId,
          }
        );
        return res.status(201).json(newMessage);

      default:
        res.setHeader('Allow', ['GET', 'POST']);
        return res.status(405).json({ error: `Method ${req.method} not allowed` });
    }
  } catch (error) {
    console.error('Chat messages API error:', error);
    return res.status(500).json({ 
      error: error instanceof Error ? error.message : 'Internal server error' 
    });
  }
}

export default withRateLimit(withTenant(handler));
