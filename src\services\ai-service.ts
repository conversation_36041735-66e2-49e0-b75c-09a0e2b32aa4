import { GoogleGenAI } from '@google/genai';
import OpenAI from 'openai';
import Anthropic from '@anthropic-ai/sdk';
import { Redis } from 'ioredis';

export type AIProvider = 'google' | 'openai' | 'anthropic';

export interface AIConfig {
  provider: AIProvider;
  model: string;
  maxTokens?: number;
  temperature?: number;
  topP?: number;
  apiKey?: string;
}

export interface AIRequest {
  prompt: string;
  systemPrompt?: string;
  context?: Record<string, unknown>;
  fileUris?: string[]; // Gemini file URIs for context
  tenantId: string;
  userId: string;
  feature: string; // e.g., 'lead_scoring', 'email_generation', etc.
}

export interface AIResponse {
  content: string;
  provider: AIProvider;
  model: string;
  tokensUsed: number;
  cost: number;
  cached: boolean;
  requestId: string;
  timestamp: Date;
}

export interface AIUsageMetrics {
  tenantId: string;
  provider: AIProvider;
  feature: string;
  requestCount: number;
  tokensUsed: number;
  cost: number;
  period: string; // 'daily', 'monthly', etc.
}

export class AIService {
  private googleAI: GoogleGenAI | null = null;
  private openAI: OpenAI | null = null;
  private anthropic: Anthropic | null = null;
  private redis: Redis | null = null;
  private defaultConfig: AIConfig;

  constructor() {
    const defaultProvider = (process.env.AI_PROVIDER as AIProvider) || 'google';
    this.defaultConfig = {
      provider: defaultProvider,
      model: this.getDefaultModel(defaultProvider),
      maxTokens: parseInt(process.env.GOOGLE_AI_MAX_TOKENS || process.env.OPENAI_MAX_TOKENS || process.env.ANTHROPIC_MAX_TOKENS || '1000'),
      temperature: parseFloat(process.env.GOOGLE_AI_TEMPERATURE || process.env.OPENAI_TEMPERATURE || process.env.ANTHROPIC_TEMPERATURE || '0.7'),
      topP: 0.9
    };

    this.initializeProviders();
    this.initializeRedis();
  }

  private initializeProviders(): void {
    // Initialize Google AI
    if (process.env.GOOGLE_AI_API_KEY) {
      this.googleAI = new GoogleGenAI({
        apiKey: process.env.GOOGLE_AI_API_KEY
      });
    }

    // Initialize OpenAI
    if (process.env.OPENAI_API_KEY) {
      this.openAI = new OpenAI({
        apiKey: process.env.OPENAI_API_KEY,
      });
    }

    // Initialize Anthropic
    if (process.env.ANTHROPIC_API_KEY) {
      this.anthropic = new Anthropic({
        apiKey: process.env.ANTHROPIC_API_KEY,
      });
    }
  }

  private initializeRedis(): void {
    if (process.env.REDIS_URL) {
      // Use separate Redis instance for AI operations with longer timeouts
      this.redis = new Redis(process.env.REDIS_URL, {
        maxRetriesPerRequest: 3,
        connectTimeout: 10000,
        commandTimeout: 15000, // Moderate timeout for AI operations
        lazyConnect: true,
        keepAlive: 30000,
        enableReadyCheck: false,
        // Use different database for AI cache to avoid conflicts
        db: 1,
      });

      // Handle Redis connection events
      this.redis.on('error', (error) => {
        console.error('❌ AI Service Redis connection error:', error);
        // Don't let Redis errors break AI functionality
      });

      this.redis.on('reconnecting', () => {
        console.log('🔄 AI Service Redis reconnecting...');
      });
    }
  }

  private getDefaultModel(provider: AIProvider): string {
    const modelMap = {
      google: process.env.GOOGLE_AI_MODEL || 'gemini-1.5-flash',
      openai: process.env.OPENAI_MODEL || 'gpt-4o-mini',
      anthropic: process.env.ANTHROPIC_MODEL || 'claude-3-haiku-20240307'
    };
    return modelMap[provider];
  }

  private generateCacheKey(request: AIRequest, config: AIConfig): string {
    const key = `ai:${config.provider}:${config.model}:${request.feature}:${request.tenantId}`;
    const hash = Buffer.from(JSON.stringify({
      prompt: request.prompt,
      systemPrompt: request.systemPrompt,
      context: request.context,
      fileUris: request.fileUris,
      maxTokens: config.maxTokens,
      temperature: config.temperature
    })).toString('base64');
    return `${key}:${hash}`;
  }

  private async getCachedResponse(cacheKey: string): Promise<AIResponse | null> {
    if (!this.redis) return null;

    try {
      // Add shorter timeout for cache operations to avoid blocking
      const cached = await Promise.race([
        this.redis.get(cacheKey),
        new Promise<null>((_, reject) =>
          setTimeout(() => reject(new Error('Cache timeout')), 2000) // Reduced to 2 seconds
        )
      ]);

      if (cached) {
        const response = JSON.parse(cached) as AIResponse;
        response.cached = true;
        return response;
      }
    } catch (error) {
      console.warn('AI Cache retrieval failed:', error);
      // Cache retrieval failed, continue without cache
    }
    return null;
  }

  private async setCachedResponse(cacheKey: string, response: AIResponse): Promise<void> {
    if (!this.redis) return;

    try {
      // Cache for 1 hour with shorter timeout to avoid blocking
      await Promise.race([
        this.redis.setex(cacheKey, 3600, JSON.stringify(response)),
        new Promise<void>((_, reject) =>
          setTimeout(() => reject(new Error('Cache timeout')), 2000) // Reduced to 2 seconds
        )
      ]);
    } catch (error) {
      console.warn('AI Cache storage failed:', error);
      // Cache storage failed, continue without caching
    }
  }

  private async callGoogleAI(request: AIRequest, config: AIConfig): Promise<AIResponse> {
    if (!this.googleAI) {
      throw new Error('Google AI not initialized');
    }

    // Prepare content parts
    const parts: Record<string, unknown>[] = [];

    // Add file context if available
    if (request.fileUris && request.fileUris.length > 0) {
      for (const fileUri of request.fileUris) {
        // Determine MIME type from file URI or context
        let mimeType = 'application/pdf'; // Default

        // Check if we have context about the file type
        if (request.context?.mimeType) {
          mimeType = request.context.mimeType as string;
        } else if (fileUri.includes('docx') || request.feature === 'template_analysis') {
          mimeType = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
        } else if (fileUri.includes('pdf')) {
          mimeType = 'application/pdf';
        } else if (fileUri.includes('jpg') || fileUri.includes('jpeg')) {
          mimeType = 'image/jpeg';
        } else if (fileUri.includes('png')) {
          mimeType = 'image/png';
        }

        parts.push({
          fileData: {
            fileUri: fileUri,
            mimeType: mimeType
          }
        });
      }
    }

    // Add text content
    const textContent = request.systemPrompt
      ? `${request.systemPrompt}\n\nUser: ${request.prompt}`
      : request.prompt;

    parts.push({ text: textContent });

    const result = await this.googleAI.models.generateContent({
      model: config.model,
      contents: [{
        role: 'user',
        parts: parts
      }],
      config: {
        maxOutputTokens: config.maxTokens,
        temperature: config.temperature,
        topP: config.topP,
      }
    });

    const content = result.candidates?.[0]?.content?.parts?.[0]?.text || '';
    const tokensUsed = result.usageMetadata?.totalTokenCount || 0;

    return {
      content,
      provider: 'google',
      model: config.model,
      tokensUsed,
      cost: this.calculateCost('google', config.model, tokensUsed),
      cached: false,
      requestId: crypto.randomUUID(),
      timestamp: new Date()
    };
  }

  private async callOpenAI(request: AIRequest, config: AIConfig): Promise<AIResponse> {
    if (!this.openAI) {
      throw new Error('OpenAI not initialized');
    }

    const messages: OpenAI.Chat.Completions.ChatCompletionMessageParam[] = [];
    
    if (request.systemPrompt) {
      messages.push({ role: 'system', content: request.systemPrompt });
    }
    
    messages.push({ role: 'user', content: request.prompt });

    const completion = await this.openAI.chat.completions.create({
      model: config.model,
      messages,
      max_tokens: config.maxTokens,
      temperature: config.temperature,
      top_p: config.topP,
    });

    const content = completion.choices[0]?.message?.content || '';
    const tokensUsed = completion.usage?.total_tokens || 0;

    return {
      content,
      provider: 'openai',
      model: config.model,
      tokensUsed,
      cost: this.calculateCost('openai', config.model, tokensUsed),
      cached: false,
      requestId: crypto.randomUUID(),
      timestamp: new Date()
    };
  }

  private async callAnthropic(request: AIRequest, config: AIConfig): Promise<AIResponse> {
    if (!this.anthropic) {
      throw new Error('Anthropic not initialized');
    }

    const message = await this.anthropic.messages.create({
      model: config.model,
      max_tokens: config.maxTokens || 1000,
      temperature: config.temperature,
      top_p: config.topP,
      system: request.systemPrompt,
      messages: [
        { role: 'user', content: request.prompt }
      ]
    });

    const content = message.content[0]?.type === 'text' ? message.content[0].text : '';
    const tokensUsed = message.usage.input_tokens + message.usage.output_tokens;

    return {
      content,
      provider: 'anthropic',
      model: config.model,
      tokensUsed,
      cost: this.calculateCost('anthropic', config.model, tokensUsed),
      cached: false,
      requestId: crypto.randomUUID(),
      timestamp: new Date()
    };
  }

  private calculateCost(provider: AIProvider, model: string, tokens: number): number {
    // Simplified cost calculation - should be updated with actual pricing
    const costPerToken = {
      google: {
        'gemini-1.5-flash': 0.00000015,
        'gemini-1.5-pro': 0.00000125,
        'gemini-2.0-flash': 0.00000015,
        'gemini-2.0-flash-exp': 0.00000015,
      },
      openai: {
        'gpt-4o-mini': 0.00000015,
        'gpt-4o': 0.000005,
        'gpt-4-turbo': 0.00001,
      },
      anthropic: {
        'claude-3-haiku-20240307': 0.00000025,
        'claude-3-sonnet-20240229': 0.000003,
        'claude-3-opus-20240229': 0.000015,
      }
    };

    return ((costPerToken[provider] as Record<string, number>)?.[model] || 0.000001) * tokens;
  }

  private async logUsage(request: AIRequest, response: AIResponse): Promise<void> {
    try {
      // Log to database - implement based on your audit log structure
      // TODO: Implement proper audit logging for AI usage
    } catch (error) {
      // Failed to log AI usage, continue silently
    }
  }

  public async generateResponse(
    request: AIRequest, 
    config?: Partial<AIConfig>
  ): Promise<AIResponse> {
    const finalConfig: AIConfig = { ...this.defaultConfig, ...config };
    
    // Check cache first
    const cacheKey = this.generateCacheKey(request, finalConfig);
    const cachedResponse = await this.getCachedResponse(cacheKey);
    
    if (cachedResponse) {
      await this.logUsage(request, cachedResponse);
      return cachedResponse;
    }

    let response: AIResponse;

    try {
      switch (finalConfig.provider) {
        case 'google':
          response = await this.callGoogleAI(request, finalConfig);
          break;
        case 'openai':
          response = await this.callOpenAI(request, finalConfig);
          break;
        case 'anthropic':
          response = await this.callAnthropic(request, finalConfig);
          break;
        default:
          throw new Error(`Unsupported AI provider: ${finalConfig.provider}`);
      }

      // Cache the response
      await this.setCachedResponse(cacheKey, response);
      
      // Log usage
      await this.logUsage(request, response);

      return response;
    } catch (error) {
      // Try fallback provider if available
      const fallbackProvider = process.env.AI_FALLBACK_PROVIDER as AIProvider;
      if (fallbackProvider && finalConfig.provider !== fallbackProvider && this.isProviderAvailable(fallbackProvider)) {
        return this.generateResponse(request, {
          ...finalConfig,
          provider: fallbackProvider,
          model: this.getDefaultModel(fallbackProvider)
        });
      } else if (finalConfig.provider !== this.defaultConfig.provider) {
        return this.generateResponse(request, { ...finalConfig, provider: this.defaultConfig.provider });
      }
      
      throw error;
    }
  }

  public async getUsageMetrics(_tenantId: string, _period: string = 'monthly'): Promise<AIUsageMetrics[]> {
    // Implement based on your database structure
    // This is a placeholder implementation
    return [];
  }

  public isProviderAvailable(provider: AIProvider): boolean {
    switch (provider) {
      case 'google':
        return !!this.googleAI;
      case 'openai':
        return !!this.openAI;
      case 'anthropic':
        return !!this.anthropic;
      default:
        return false;
    }
  }

  public getAvailableProviders(): AIProvider[] {
    const providers: AIProvider[] = [];
    if (this.googleAI) providers.push('google');
    if (this.openAI) providers.push('openai');
    if (this.anthropic) providers.push('anthropic');
    return providers;
  }
}

// Singleton instance
export const aiService = new AIService();
