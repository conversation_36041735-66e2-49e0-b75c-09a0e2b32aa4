import { NextResponse } from 'next/server';
import { LeadManagementService, GetLeadsQuery } from '@/services/lead-management';
import { withPermission, AuthenticatedRequest } from '@/lib/permission-middleware';
import { PermissionResource, PermissionAction } from '@/types';
import { z } from 'zod';

const leadService = new LeadManagementService();

// Validation schema for export parameters
const exportQuerySchema = z.object({
  format: z.enum(['csv', 'json']).default('csv'),
  search: z.string().optional(),
  status: z.string().optional(),
  leadSourceId: z.string().optional(),
  ownerId: z.string().optional(),
  priority: z.string().optional(),
  includeArchived: z.string().transform(val => val === 'true').optional(),
  includeConverted: z.string().transform(val => val === 'true').optional(),
});

/**
 * GET /api/leads/export
 * Export leads to CSV or JSON format
 */
export const GET = withPermission({
  resource: PermissionResource.LEADS,
  action: PermissionAction.READ,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest) => {
  try {
    const { searchParams } = new URL(req.url);
    const queryParams = Object.fromEntries(searchParams.entries());
    
    const validatedQuery = exportQuerySchema.parse(queryParams);
    
    const filters: GetLeadsQuery = {
      search: validatedQuery.search,
      status: validatedQuery.status,
      leadSourceId: validatedQuery.leadSourceId,
      ownerId: validatedQuery.ownerId,
      priority: validatedQuery.priority,
      includeArchived: validatedQuery.includeArchived,
      includeConverted: validatedQuery.includeConverted,
      page: 1,
      limit: 10000, // Large limit for export
      sortBy: 'createdAt',
      sortOrder: 'desc',
    };

    console.log('Export filters:', filters);

    const result = await leadService.getLeads(req.tenantId, filters, req.userId);
    const leads = result.leads;

    console.log(`Exporting ${leads.length} leads`);

    if (validatedQuery.format === 'json') {
      return NextResponse.json({
        success: true,
        data: leads,
        total: leads.length,
        exported: new Date().toISOString()
      });
    }

    // Generate CSV with all lead fields
    const headers = [
      'ID',
      'Title',
      'Description',
      'Status',
      'Priority',
      'Score',
      'Value',
      'Expected Close Date',
      'Contact Name',
      'Contact Email',
      'Contact Phone',
      'Company Name',
      'Company Website',
      'Company Industry',
      'Lead Source',
      'Lead Source Category',
      'Owner Name',
      'Owner Email',
      'Created By',
      'Tags',
      'Custom Fields',
      'Is Converted',
      'Converted At',
      'Converted By',
      'Conversion Notes',
      'Last Contact Date',
      'Created At',
      'Updated At'
    ];

    const csvRows = [
      headers.join(','),
      ...leads.map(lead => [
        `"${lead.id}"`,
        `"${(lead.title || '').replace(/"/g, '""')}"`,
        `"${(lead.description || '').replace(/"/g, '""')}"`,
        `"${lead.status}"`,
        `"${lead.priority}"`,
        lead.score || 0,
        lead.value ? Number(lead.value) : '',
        lead.expectedCloseDate ? `"${lead.expectedCloseDate.toISOString()}"` : '',
        `"${lead.contact ? `${lead.contact.firstName || ''} ${lead.contact.lastName || ''}`.trim() : ''}"`,
        `"${(lead.contact?.email || '').replace(/"/g, '""')}"`,
        `"${(lead.contact?.phone || '').replace(/"/g, '""')}"`,
        `"${(lead.company?.name || '').replace(/"/g, '""')}"`,
        `"${(lead.company?.website || '').replace(/"/g, '""')}"`,
        `"${(lead.company?.industry || '').replace(/"/g, '""')}"`,
        `"${(lead.leadSource?.name || '').replace(/"/g, '""')}"`,
        `"${(lead.leadSource?.category || '').replace(/"/g, '""')}"`,
        `"${lead.owner ? `${lead.owner.firstName || ''} ${lead.owner.lastName || ''}`.trim() : ''}"`,
        `"${(lead.owner?.email || '').replace(/"/g, '""')}"`,
        `"${lead.createdBy ? `${lead.createdBy.firstName || ''} ${lead.createdBy.lastName || ''}`.trim() : ''}"`,
        `"${Array.isArray(lead.tags) ? lead.tags.join('; ') : ''}"`,
        `"${lead.customFields ? JSON.stringify(lead.customFields).replace(/"/g, '""') : ''}"`,
        lead.isConverted ? 'Yes' : 'No',
        lead.convertedAt ? `"${lead.convertedAt.toISOString()}"` : '',
        `"${lead.convertedBy ? `${lead.convertedBy.firstName || ''} ${lead.convertedBy.lastName || ''}`.trim() : ''}"`,
        `"${(lead.conversionNotes || '').replace(/"/g, '""')}"`,
        lead.lastContactDate ? `"${lead.lastContactDate.toISOString()}"` : '',
        `"${lead.createdAt.toISOString()}"`,
        `"${lead.updatedAt.toISOString()}"`
      ].join(','))
    ];

    // Add UTF-8 BOM for proper Arabic character support
    const BOM = '\uFEFF';
    const csvContent = BOM + csvRows.join('\n');
    const filename = `leads-export-${new Date().toISOString().split('T')[0]}.csv`;

    return new NextResponse(csvContent, {
      status: 200,
      headers: {
        'Content-Type': 'text/csv; charset=utf-8',
        'Content-Disposition': `attachment; filename="${filename}"`,
        'Cache-Control': 'no-cache',
      },
    });

  } catch (error) {
    console.error('Export leads error:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid query parameters', details: error.errors },
        { status: 400 }
      );
    }

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
});
