import { NextResponse } from 'next/server';
import { z } from 'zod';
import { ContactManagementService } from '@/services/contact-management';
import { withPermission, AuthenticatedRequest } from '@/lib/permission-middleware';
import { PermissionResource, PermissionAction } from '@/types';

const contactService = new ContactManagementService();

// Validation schema for search query
const searchQuerySchema = z.object({
  email: z.string().email('Invalid email format').optional(),
  q: z.string().min(1, 'Search query is required').optional(),
}).refine(data => data.email || data.q, {
  message: "Either email or search query (q) is required"
});

/**
 * GET /api/contacts/search
 * Search contacts by email or general search
 */
export const GET = withPermission({
  resource: PermissionResource.CONTACTS,
  action: PermissionAction.READ,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest) => {
  try {
    const { searchParams } = new URL(req.url);
    const queryParams = Object.fromEntries(searchParams.entries());
    
    const validatedQuery = searchQuerySchema.parse(queryParams);
    
    let contact = null;
    
    if (validatedQuery.email) {
      // Search by email
      contact = await contactService.findContactByEmail(
        validatedQuery.email,
        req.tenantId
      );
      
      return NextResponse.json({
        success: true,
        data: { contact },
        message: contact ? 'Contact found' : 'Contact not found'
      });
    }

    // For general search, use the existing getContacts method with search filter
    if (validatedQuery.q) {
      const result = await contactService.getContacts(req.tenantId, {
        search: validatedQuery.q,
        limit: 20
      });
      
      return NextResponse.json({
        success: true,
        data: result,
        message: 'Contact search completed successfully'
      });
    }

    return NextResponse.json({
      success: false,
      error: 'No search criteria provided'
    }, { status: 400 });

  } catch (error) {
    console.error('Search contacts error:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          error: 'Invalid search parameters',
          details: error.errors.map(e => ({
            field: e.path.join('.'),
            message: e.message
          }))
        },
        { status: 400 }
      );
    }

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
});
