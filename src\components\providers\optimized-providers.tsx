'use client';

import React, { memo, useMemo } from 'react';
import { SessionProvider } from 'next-auth/react';
import { ThemeProvider } from './theme-provider';
import { LocaleProvider } from './locale-provider';
import { TenantProvider } from './tenant-provider';
import { EnhancedPermissionProvider } from './enhanced-permission-provider';
import { StatePersistenceProvider } from './state-persistence-provider';

interface OptimizedProvidersProps {
  children: React.ReactNode;
  session?: any;
}

// Memoized inner providers to prevent unnecessary re-renders
const InnerProviders = memo(function InnerProviders({ 
  children 
}: { 
  children: React.ReactNode 
}) {
  // Memoize permission cache options to prevent re-creation
  const permissionCacheOptions = useMemo(() => ({
    ttl: 3600000, // 1 hour
    encrypt: true,
    validateIntegrity: true
  }), []);

  return (
    <ThemeProvider>
      <LocaleProvider>
        <TenantProvider>
          <EnhancedPermissionProvider
            enableCache={true}
            enableRealTimeUpdates={true}
            cacheOptions={permissionCacheOptions}
          >
            <StatePersistenceProvider>
              {children}
            </StatePersistenceProvider>
          </EnhancedPermissionProvider>
        </TenantProvider>
      </LocaleProvider>
    </ThemeProvider>
  );
});

export const OptimizedProviders = memo(function OptimizedProviders({ 
  children, 
  session 
}: OptimizedProvidersProps) {
  return (
    <SessionProvider session={session}>
      <InnerProviders>
        {children}
      </InnerProviders>
    </SessionProvider>
  );
});
