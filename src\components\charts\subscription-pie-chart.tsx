'use client';

import React from 'react';
import {
  <PERSON><PERSON>hart,
  Pie,
  Cell,
  Tooltip,
  Legend,
  ResponsiveContainer
} from 'recharts';
import { BaseChart, BaseChartProps } from './base-chart';

interface SubscriptionData {
  planName: string;
  count: number;
  revenue: number;
  percentage: number;
}

interface SubscriptionPieChartProps extends Omit<BaseChartProps, 'children'> {
  data: SubscriptionData[];
  showRevenue?: boolean;
}

const COLORS = [
  'hsl(var(--chart-1))',
  'hsl(var(--chart-2))',
  'hsl(var(--chart-3))',
  'hsl(var(--chart-4))',
  'hsl(var(--chart-5))'
];

export function SubscriptionPieChart({
  data,
  showRevenue = false,
  ...baseProps
}: SubscriptionPieChartProps) {
  const formatTooltipValue = (value: number, name: string, props: any) => {
    if (showRevenue) {
      return [`$${props.payload.revenue.toLocaleString()}`, 'Revenue'];
    }
    return [`${value} subscriptions`, 'Count'];
  };

  const renderCustomLabel = (entry: any) => {
    return `${entry.planName} (${entry.percentage}%)`;
  };

  return (
    <BaseChart {...baseProps}>
      <PieChart>
        <Pie
          data={data}
          cx="50%"
          cy="50%"
          labelLine={false}
          label={renderCustomLabel}
          outerRadius={80}
          fill="#8884d8"
          dataKey={showRevenue ? "revenue" : "count"}
        >
          {data.map((entry, index) => (
            <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
          ))}
        </Pie>
        <Tooltip formatter={formatTooltipValue} />
        <Legend />
      </PieChart>
    </BaseChart>
  );
}
