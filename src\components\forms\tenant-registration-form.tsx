'use client';

import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useTranslation } from '@/components/providers/locale-provider';
import { EyeIcon, EyeSlashIcon, CheckIcon, XMarkIcon } from '@heroicons/react/24/outline';

const tenantRegistrationSchema = z.object({
  tenantName: z.string().min(2, 'Tenant name must be at least 2 characters'),
  tenantSlug: z.string()
    .min(3, 'Slug must be at least 3 characters')
    .max(50, 'Slug must be less than 50 characters')
    .regex(/^[a-z0-9-]+$/, 'Slug can only contain lowercase letters, numbers, and hyphens'),
  adminEmail: z.string().email('Invalid email address'),
  adminPassword: z.string().min(8, 'Password must be at least 8 characters'),
  adminFirstName: z.string().min(1, 'First name is required'),
  adminLastName: z.string().min(1, 'Last name is required'),
  acceptTerms: z.boolean().refine(val => val === true, 'You must accept the terms and conditions')
});

type TenantRegistrationData = z.infer<typeof tenantRegistrationSchema>;

interface TenantRegistrationFormProps {
  onSuccess?: (tenant: any) => void;
  onError?: (error: string) => void;
}

export function TenantRegistrationForm({ onSuccess, onError }: TenantRegistrationFormProps) {
  const { t } = useTranslation();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [slugAvailability, setSlugAvailability] = useState<{
    checking: boolean;
    available: boolean | null;
    error: string | null;
  }>({ checking: false, available: null, error: null });

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors }
  } = useForm<TenantRegistrationData>({
    resolver: zodResolver(tenantRegistrationSchema)
  });

  const tenantName = watch('tenantName');
  const tenantSlug = watch('tenantSlug');

  // Auto-generate slug from tenant name
  React.useEffect(() => {
    if (tenantName && !tenantSlug) {
      const generatedSlug = tenantName
        .toLowerCase()
        .replace(/[^a-z0-9\s-]/g, '')
        .replace(/\s+/g, '-')
        .replace(/-+/g, '-')
        .trim();
      setValue('tenantSlug', generatedSlug);
    }
  }, [tenantName, tenantSlug, setValue]);

  // Check slug availability
  React.useEffect(() => {
    if (!tenantSlug || tenantSlug.length < 3) {
      setSlugAvailability({ checking: false, available: null, error: null });
      return;
    }

    const checkSlug = async () => {
      setSlugAvailability({ checking: true, available: null, error: null });
      
      try {
        const response = await fetch(`/api/tenants/register?slug=${encodeURIComponent(tenantSlug)}`);
        const data = await response.json();
        
        if (response.ok) {
          setSlugAvailability({ 
            checking: false, 
            available: data.available, 
            error: data.error || null 
          });
        } else {
          setSlugAvailability({ 
            checking: false, 
            available: false, 
            error: data.error || 'Failed to check availability' 
          });
        }
      } catch (error) {
        setSlugAvailability({ 
          checking: false, 
          available: false, 
          error: 'Failed to check availability' 
        });
      }
    };

    const timeoutId = setTimeout(checkSlug, 500);
    return () => clearTimeout(timeoutId);
  }, [tenantSlug]);

  const onSubmit = async (data: TenantRegistrationData) => {
    setIsSubmitting(true);

    try {
      const response = await fetch('/api/tenants/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      const result = await response.json();

      if (response.ok && result.success) {
        onSuccess?.(result.data);
      } else {
        const errorMessage = result.error || 'Registration failed';
        onError?.(errorMessage);
      }
    } catch (error) {
      console.error('Registration error:', error);
      onError?.('Network error. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      {/* Tenant Information */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium text-gray-900">Tenant Information</h3>
        
        <div>
          <label htmlFor="tenantName" className="block text-sm font-medium text-gray-700">
            Organization Name
          </label>
          <input
            {...register('tenantName')}
            type="text"
            className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
            placeholder="Your Organization Name"
          />
          {errors.tenantName && (
            <p className="mt-1 text-sm text-red-600">{errors.tenantName.message}</p>
          )}
        </div>

        <div>
          <label htmlFor="tenantSlug" className="block text-sm font-medium text-gray-700">
            Tenant URL
          </label>
          <div className="mt-1 flex rounded-md shadow-sm">
            <span className="inline-flex items-center px-3 rounded-l-md border border-r-0 border-gray-300 bg-gray-50 text-gray-500 text-sm">
              https://app.crm.com/
            </span>
            <input
              {...register('tenantSlug')}
              type="text"
              className="flex-1 block w-full px-3 py-2 border border-gray-300 rounded-none rounded-r-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
              placeholder="your-organization"
            />
            <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
              {slugAvailability.checking && (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary-600"></div>
              )}
              {!slugAvailability.checking && slugAvailability.available === true && (
                <CheckIcon className="h-4 w-4 text-green-500" />
              )}
              {!slugAvailability.checking && slugAvailability.available === false && (
                <XMarkIcon className="h-4 w-4 text-red-500" />
              )}
            </div>
          </div>
          {errors.tenantSlug && (
            <p className="mt-1 text-sm text-red-600">{errors.tenantSlug.message}</p>
          )}
          {slugAvailability.error && (
            <p className="mt-1 text-sm text-red-600">{slugAvailability.error}</p>
          )}
          {slugAvailability.available === false && !slugAvailability.error && (
            <p className="mt-1 text-sm text-red-600">This URL is already taken</p>
          )}
        </div>
      </div>

      {/* Admin User Information */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium text-gray-900">Administrator Account</h3>
        
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
          <div>
            <label htmlFor="adminFirstName" className="block text-sm font-medium text-gray-700">
              First Name
            </label>
            <input
              {...register('adminFirstName')}
              type="text"
              className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
            />
            {errors.adminFirstName && (
              <p className="mt-1 text-sm text-red-600">{errors.adminFirstName.message}</p>
            )}
          </div>

          <div>
            <label htmlFor="adminLastName" className="block text-sm font-medium text-gray-700">
              Last Name
            </label>
            <input
              {...register('adminLastName')}
              type="text"
              className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
            />
            {errors.adminLastName && (
              <p className="mt-1 text-sm text-red-600">{errors.adminLastName.message}</p>
            )}
          </div>
        </div>

        <div>
          <label htmlFor="adminEmail" className="block text-sm font-medium text-gray-700">
            Email Address
          </label>
          <input
            {...register('adminEmail')}
            type="email"
            className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
          />
          {errors.adminEmail && (
            <p className="mt-1 text-sm text-red-600">{errors.adminEmail.message}</p>
          )}
        </div>

        <div>
          <label htmlFor="adminPassword" className="block text-sm font-medium text-gray-700">
            Password
          </label>
          <div className="mt-1 relative">
            <input
              {...register('adminPassword')}
              type={showPassword ? 'text' : 'password'}
              className="block w-full px-3 py-2 pr-10 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
            />
            <button
              type="button"
              className="absolute inset-y-0 right-0 pr-3 flex items-center"
              onClick={() => setShowPassword(!showPassword)}
            >
              {showPassword ? (
                <EyeSlashIcon className="h-4 w-4 text-gray-400" />
              ) : (
                <EyeIcon className="h-4 w-4 text-gray-400" />
              )}
            </button>
          </div>
          {errors.adminPassword && (
            <p className="mt-1 text-sm text-red-600">{errors.adminPassword.message}</p>
          )}
        </div>
      </div>

      {/* Terms and Conditions */}
      <div className="flex items-center">
        <input
          {...register('acceptTerms')}
          type="checkbox"
          className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
        />
        <label htmlFor="acceptTerms" className="ml-2 block text-sm text-gray-900">
          I agree to the{' '}
          <a href="/terms" className="text-primary-600 hover:text-primary-500">
            Terms of Service
          </a>{' '}
          and{' '}
          <a href="/privacy" className="text-primary-600 hover:text-primary-500">
            Privacy Policy
          </a>
        </label>
      </div>
      {errors.acceptTerms && (
        <p className="mt-1 text-sm text-red-600">{errors.acceptTerms.message}</p>
      )}

      {/* Submit Button */}
      <button
        type="submit"
        disabled={isSubmitting || slugAvailability.available === false}
        className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed"
      >
        {isSubmitting ? 'Creating Tenant...' : 'Create Tenant'}
      </button>
    </form>
  );
}
