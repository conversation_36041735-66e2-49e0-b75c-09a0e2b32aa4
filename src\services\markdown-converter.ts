import { Document, Paragraph, TextRun, AlignmentType, <PERSON>er, HeadingLevel, PageBreak, ImageRun, Table, TableRow, TableCell, WidthType, BorderStyle } from 'docx';
import { DocumentStyling } from '@/types/proposal';
import fs from 'fs/promises';
import path from 'path';

export interface MarkdownDocumentContent {
  title: string;
  metadata: {
    proposalId: string;
    generatedAt: string;
    generatedBy: string;
    opportunity?: {
      title: string;
      company?: string;
      contact?: string;
      // Note: value/price is intentionally excluded from document generation
    } | null;
  };
  branding?: Record<string, unknown>;
  sections: Array<{
    id: string;
    type: string;
    title: string;
    content: string;
    wordCount?: number;
    isAiGenerated?: boolean;
    generatedAt?: Date;
  }>;
  charts: Array<{
    id: string;
    type: string;
    title: string;
    description?: string;
    // Note: mermaidCode is intentionally excluded from document generation
    imagePath?: string;
    isAiGenerated?: boolean;
  }>;
}

export class MarkdownConverter {
  /**
   * Convert document content to markdown format
   */
  static convertToMarkdown(content: MarkdownDocumentContent): string {
    let markdown = '';

    // Add Table of Contents
    markdown += '[TOC]\n\n';

    // Add title
    markdown += `# ${content.title}\n\n`;

    // Add metadata section
    markdown += '## Document Information\n\n';
    markdown += `**Generated:** ${new Date(content.metadata.generatedAt).toLocaleDateString()}\n\n`;
    markdown += `**Generated By:** ${content.metadata.generatedBy}\n\n`;
    
    if (content.metadata.opportunity) {
      markdown += `**Opportunity:** ${content.metadata.opportunity.title}\n\n`;
      if (content.metadata.opportunity.company) {
        markdown += `**Company:** ${content.metadata.opportunity.company}\n\n`;
      }
      if (content.metadata.opportunity.contact) {
        markdown += `**Contact:** ${content.metadata.opportunity.contact}\n\n`;
      }
      // Note: Proposal value/price is intentionally excluded from generated documents
    }

    markdown += '---\n\n';

    // Add page break before main content
    markdown += '\\pagebreak\n\n';

    // Add sections
    content.sections.forEach((section, index) => {
      // Add section title as H2
      markdown += `## ${section.title}\n\n`;
      
      // Add section content
      if (section.content) {
        // Convert HTML-like content to markdown
        const markdownContent = this.convertHtmlToMarkdown(section.content);
        markdown += markdownContent + '\n\n';
      } else {
        markdown += '*No content available for this section.*\n\n';
      }
      // Add page break between major sections (except last one)
      if (index < content.sections.length - 1) {
        markdown += '\\pagebreak\n\n';
      }
    });

    // Add charts section if any charts exist
    if (content.charts.length > 0) {
      markdown += '\\pagebreak\n\n';
      markdown += '## Architecture Diagrams\n\n';
      
      content.charts.forEach((chart) => {
        markdown += `### ${chart.title}\n\n`;
        
        if (chart.description) {
          markdown += `${chart.description}\n\n`;
        }

        // Add chart image if available
        if (chart.imagePath) {
          // Convert to absolute path for embedding
          const absolutePath = path.resolve(chart.imagePath);
          markdown += `![${chart.title}](${absolutePath})\n\n`;
        }

        // Note: Mermaid code is intentionally excluded from generated documents
        // Only the rendered image is included

        markdown += '---\n\n';
      });
    }

    // Add footer
    markdown += '\\pagebreak\n\n';
    markdown += '## Document Footer\n\n';
    markdown += `**Proposal ID:** ${content.metadata.proposalId}\n\n`;
    markdown += `*Generated on ${new Date().toLocaleDateString()} at ${new Date().toLocaleTimeString()}*\n\n`;

    return markdown;
  }

  /**
   * Convert HTML-like content to markdown
   */
  private static convertHtmlToMarkdown(html: string): string {
    if (!html) return '';

    return html
      // Convert HTML tags to markdown
      .replace(/<strong>(.*?)<\/strong>/g, '**$1**')
      .replace(/<b>(.*?)<\/b>/g, '**$1**')
      .replace(/<em>(.*?)<\/em>/g, '*$1*')
      .replace(/<i>(.*?)<\/i>/g, '*$1*')
      .replace(/<code>(.*?)<\/code>/g, '`$1`')
      .replace(/<br\s*\/?>/g, '\n')
      .replace(/<\/p><p>/g, '\n\n')
      .replace(/<p>/g, '')
      .replace(/<\/p>/g, '\n')
      // Handle lists
      .replace(/<ul>/g, '')
      .replace(/<\/ul>/g, '')
      .replace(/<ol>/g, '')
      .replace(/<\/ol>/g, '')
      .replace(/<li>(.*?)<\/li>/g, '- $1\n')
      // Handle headings
      .replace(/<h1>(.*?)<\/h1>/g, '# $1\n')
      .replace(/<h2>(.*?)<\/h2>/g, '## $1\n')
      .replace(/<h3>(.*?)<\/h3>/g, '### $1\n')
      .replace(/<h4>(.*?)<\/h4>/g, '#### $1\n')
      .replace(/<h5>(.*?)<\/h5>/g, '##### $1\n')
      .replace(/<h6>(.*?)<\/h6>/g, '###### $1\n')
      // Handle blockquotes
      .replace(/<blockquote>(.*?)<\/blockquote>/g, '> $1\n')
      // Clean up extra whitespace
      .replace(/\n\s*\n\s*\n/g, '\n\n')
      .trim();
  }

  /**
   * Generate DOCX document from markdown
   */
  static async generateDocx(
    markdown: string,
    outputPath: string,
    styling?: DocumentStyling
  ): Promise<void> {
    try {
      // Parse markdown and create document elements
      const documentElements = await this.parseMarkdownToDocxElements(markdown, styling);

      // Create the document
      const doc = new Document({
        sections: [{
          properties: {},
          children: documentElements
        }]
      });

      // Generate buffer
      const buffer = await Packer.toBuffer(doc);

      // Write to file
      await fs.writeFile(outputPath, buffer);

    } catch (error) {
      console.error('Error generating DOCX:', error);
      throw new Error(`Failed to generate DOCX document: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Parse markdown content and convert to DOCX elements
   */
  private static async parseMarkdownToDocxElements(markdown: string, styling?: DocumentStyling): Promise<(Paragraph | Table)[]> {
    const elements: (Paragraph | Table)[] = [];
    const lines = markdown.split('\n');

    // Default styling with improved font sizes and Arial font
    const defaultStyling = {
      fontFamily: 'Arial',
      titleSize: 36,
      heading1Size: 32,
      heading2Size: 28,
      heading3Size: 24,
      heading4Size: 20,
      heading5Size: 18,
      paragraphSize: 16,
      paragraphAlignment: 'LEFT' as const,
      headingAlignment: 'LEFT' as const,
    };

    const finalStyling = { ...defaultStyling, ...styling };

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();

      // Skip empty lines
      if (!line) {
        elements.push(new Paragraph({}));
        continue;
      }

      // Handle page breaks
      if (line === '\\pagebreak') {
        elements.push(new Paragraph({
          children: [new PageBreak()]
        }));
        continue;
      }

      // Handle TOC placeholder
      if (line === '[TOC]') {
        elements.push(new Paragraph({
          children: [new TextRun({
            text: 'Table of Contents',
            bold: true,
            size: finalStyling.titleSize,
            font: finalStyling.fontFamily || 'Arial'
          })],
          alignment: AlignmentType.CENTER
        }));
        elements.push(new Paragraph({})); // Empty line after TOC
        continue;
      }

      // Handle headings
      if (line.startsWith('#')) {
        const match = line.match(/^(#+)\s*(.+)$/);
        if (match) {
          const level = match[1].length;
          const text = match[2];

          let headingLevel: HeadingLevel;
          let fontSize: number;

          switch (level) {
            case 1:
              headingLevel = HeadingLevel.HEADING_1;
              fontSize = finalStyling.heading1Size || 28;
              break;
            case 2:
              headingLevel = HeadingLevel.HEADING_2;
              fontSize = finalStyling.heading2Size || 24;
              break;
            case 3:
              headingLevel = HeadingLevel.HEADING_3;
              fontSize = finalStyling.heading3Size || 20;
              break;
            case 4:
              headingLevel = HeadingLevel.HEADING_4;
              fontSize = finalStyling.heading4Size || 18;
              break;
            case 5:
              headingLevel = HeadingLevel.HEADING_5;
              fontSize = finalStyling.heading5Size || 16;
              break;
            default:
              headingLevel = HeadingLevel.HEADING_6;
              fontSize = 14;
          }

          elements.push(new Paragraph({
            children: [new TextRun({
              text: text,
              bold: true,
              size: fontSize,
              font: finalStyling.fontFamily || 'Arial'
            })],
            heading: headingLevel,
            alignment: this.getAlignment(finalStyling.headingAlignment)
          }));
          continue;
        }
      }

      // Handle horizontal rules
      if (line.match(/^---+$/)) {
        elements.push(new Paragraph({
          children: [new TextRun({
            text: '_______________________________________________',
            size: finalStyling.paragraphSize,
            font: finalStyling.fontFamily || 'Arial'
          })],
          alignment: AlignmentType.CENTER
        }));
        continue;
      }

      // Handle lists
      if (line.startsWith('- ') || line.startsWith('* ')) {
        const text = line.substring(2);
        elements.push(new Paragraph({
          children: [new TextRun({
            text: `• ${this.parseInlineFormatting(text)}`,
            size: finalStyling.paragraphSize,
            font: finalStyling.fontFamily || 'Arial'
          })],
          indent: { left: 720 }
        }));
        continue;
      }

      // Handle numbered lists
      if (line.match(/^\d+\.\s/)) {
        const text = line.replace(/^\d+\.\s/, '');
        const number = line.match(/^(\d+)\./)?.[1] || '1';
        elements.push(new Paragraph({
          children: [new TextRun({
            text: `${number}. ${this.parseInlineFormatting(text)}`,
            size: finalStyling.paragraphSize,
            font: finalStyling.fontFamily || 'Arial'
          })],
          indent: { left: 720 }
        }));
        continue;
      }

      // Handle blockquotes
      if (line.startsWith('> ')) {
        const text = line.substring(2);
        elements.push(new Paragraph({
          children: [new TextRun({
            text: this.parseInlineFormatting(text),
            italics: true,
            size: finalStyling.paragraphSize,
            font: finalStyling.fontFamily || 'Arial'
          })],
          indent: { left: 720 },
          alignment: AlignmentType.LEFT
        }));
        continue;
      }

      // Handle images
      const imageMatch = line.match(/!\[([^\]]*)\]\(([^)]+)\)/);
      if (imageMatch) {
        const altText = imageMatch[1];
        const imagePath = imageMatch[2];

        try {
          // Check if image file exists
          await fs.access(imagePath);

          // Read image file
          const imageBuffer = await fs.readFile(imagePath);

          // Add image to document
          elements.push(new Paragraph({
            children: [
              new ImageRun({
                data: imageBuffer,
                transformation: {
                  width: 600,
                  height: 400,
                },
                type: 'png'
              })
            ],
            alignment: AlignmentType.CENTER
          }));

          // Add caption if alt text exists
          if (altText) {
            elements.push(new Paragraph({
              children: [new TextRun({
                text: altText,
                italics: true,
                size: Math.max(finalStyling.paragraphSize - 2, 12),
                font: finalStyling.fontFamily || 'Arial'
              })],
              alignment: AlignmentType.CENTER
            }));
          }
        } catch (error) {
          console.warn(`Failed to load image: ${imagePath}`, error);
          // Add placeholder text instead
          elements.push(new Paragraph({
            children: [new TextRun({
              text: `[Image: ${altText || imagePath}]`,
              italics: true,
              size: finalStyling.paragraphSize,
              font: finalStyling.fontFamily || 'Arial'
            })],
            alignment: AlignmentType.CENTER
          }));
        }
        continue;
      }

      // Handle code blocks
      if (line.startsWith('```')) {
        const codeLines: string[] = [];
        i++; // Move to next line

        while (i < lines.length && !lines[i].trim().startsWith('```')) {
          codeLines.push(lines[i]);
          i++;
        }

        elements.push(new Paragraph({
          children: [new TextRun({
            text: codeLines.join('\n'),
            font: 'Courier New',
            size: Math.max(finalStyling.paragraphSize - 2, 12) // Ensure code blocks are readable
          })],
          indent: { left: 720 }
        }));
        continue;
      }

      // Handle tables
      if (line.includes('|') && line.trim().startsWith('|') && line.trim().endsWith('|')) {
        const tableLines: string[] = [line];
        let nextIndex = i + 1;

        // Collect all table lines
        while (nextIndex < lines.length) {
          const nextLine = lines[nextIndex].trim();
          if (nextLine.includes('|') && nextLine.startsWith('|') && nextLine.endsWith('|')) {
            tableLines.push(nextLine);
            nextIndex++;
          } else if (nextLine === '') {
            // Allow empty lines within table
            nextIndex++;
          } else {
            break;
          }
        }

        // Parse and create table
        const table = this.parseMarkdownTable(tableLines, finalStyling);
        if (table) {
          elements.push(table);
          i = nextIndex - 1; // Adjust index to skip processed lines
        }
        continue;
      }

      // Regular paragraphs
      if (line.length > 0) {
        const textRuns = this.parseInlineFormattingToRuns(line, finalStyling.paragraphSize, finalStyling.fontFamily);
        elements.push(new Paragraph({
          children: textRuns,
          alignment: this.getAlignment(finalStyling.paragraphAlignment)
        }));
      }
    }

    return elements;
  }

  /**
   * Parse inline formatting (bold, italic, code)
   */
  private static parseInlineFormatting(text: string): string {
    return text
      .replace(/\*\*(.*?)\*\*/g, '$1') // Remove bold markers for plain text
      .replace(/\*(.*?)\*/g, '$1')     // Remove italic markers for plain text
      .replace(/`(.*?)`/g, '$1');      // Remove code markers for plain text
  }

  /**
   * Parse inline formatting and return TextRun array
   */
  private static parseInlineFormattingToRuns(text: string, fontSize: number, fontFamily?: string): TextRun[] {
    const runs: TextRun[] = [];
    const currentText = text;

    // Simple implementation - just handle basic formatting
    // In a full implementation, you'd want a proper parser

    // For now, create a single text run
    runs.push(new TextRun({
      text: this.parseInlineFormatting(currentText),
      size: fontSize,
      font: fontFamily || 'Arial'
    }));

    return runs;
  }

  /**
   * Convert alignment string to AlignmentType
   */
  private static getAlignment(alignment?: string) {
    switch (alignment?.toUpperCase()) {
      case 'CENTER':
        return AlignmentType.CENTER;
      case 'RIGHT':
        return AlignmentType.RIGHT;
      case 'JUSTIFIED':
        return AlignmentType.JUSTIFIED;
      default:
        return AlignmentType.LEFT;
    }
  }

  /**
   * Parse markdown table and convert to DOCX Table
   */
  private static parseMarkdownTable(tableLines: string[], styling: any): Table | null {
    if (tableLines.length < 2) return null;

    const rows: string[][] = [];
    let headerRowIndex = -1;

    // Parse table rows
    for (let i = 0; i < tableLines.length; i++) {
      const line = tableLines[i].trim();

      // Skip separator lines (e.g., |---|---|)
      if (line.match(/^\|[\s\-\|:]+\|$/)) {
        if (headerRowIndex === -1 && i > 0) {
          headerRowIndex = i - 1; // Previous row was header
        }
        continue;
      }

      // Parse table cells
      const cells = line
        .split('|')
        .slice(1, -1) // Remove first and last empty elements
        .map(cell => cell.trim());

      if (cells.length > 0) {
        rows.push(cells);
      }
    }

    if (rows.length === 0) return null;

    // Create table rows
    const tableRows: TableRow[] = [];

    rows.forEach((rowCells, rowIndex) => {
      const isHeader = headerRowIndex >= 0 ? rowIndex === headerRowIndex : rowIndex === 0;

      const cells = rowCells.map(cellText => {
        return new TableCell({
          children: [
            new Paragraph({
              children: [
                new TextRun({
                  text: this.parseInlineFormatting(cellText),
                  bold: isHeader,
                  size: styling.paragraphSize || 16,
                  font: styling.fontFamily || 'Arial'
                })
              ],
              alignment: AlignmentType.LEFT
            })
          ],
          width: {
            size: 100 / rowCells.length,
            type: WidthType.PERCENTAGE
          },
          margins: {
            top: 100,
            bottom: 100,
            left: 100,
            right: 100
          }
        });
      });

      tableRows.push(new TableRow({
        children: cells
      }));
    });

    // Create table with borders
    return new Table({
      rows: tableRows,
      width: {
        size: 100,
        type: WidthType.PERCENTAGE
      },
      borders: {
        top: { style: BorderStyle.SINGLE, size: 1 },
        bottom: { style: BorderStyle.SINGLE, size: 1 },
        left: { style: BorderStyle.SINGLE, size: 1 },
        right: { style: BorderStyle.SINGLE, size: 1 },
        insideHorizontal: { style: BorderStyle.SINGLE, size: 1 },
        insideVertical: { style: BorderStyle.SINGLE, size: 1 }
      }
    });
  }

  /**
   * Validate markdown content
   */
  static validateMarkdown(markdown: string): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!markdown || markdown.trim().length === 0) {
      errors.push('Markdown content is empty');
    }

    if (markdown.length > 1000000) { // 1MB limit
      errors.push('Markdown content is too large (max 1MB)');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }
}
