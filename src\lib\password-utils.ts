// Browser-compatible crypto functions
const isBrowser = typeof window !== 'undefined';

// Browser-compatible random number generator
function getRandomInt(min: number, max: number): number {
  if (isBrowser) {
    // Use Web Crypto API in browser
    const array = new Uint32Array(1);
    crypto.getRandomValues(array);
    return min + (array[0] % (max - min));
  } else {
    // Use Node.js crypto in server environment
    const crypto = require('crypto');
    return crypto.randomInt(min, max);
  }
}

// Browser-compatible random bytes generator
function getRandomBytes(size: number): Uint8Array {
  if (isBrowser) {
    const array = new Uint8Array(size);
    crypto.getRandomValues(array);
    return array;
  } else {
    const crypto = require('crypto');
    return new Uint8Array(crypto.randomBytes(size));
  }
}

export interface PasswordGenerationOptions {
  length?: number;
  includeUppercase?: boolean;
  includeLowercase?: boolean;
  includeNumbers?: boolean;
  includeSpecialChars?: boolean;
  excludeSimilar?: boolean; // Exclude similar looking characters like 0, O, l, 1
  excludeAmbiguous?: boolean; // Exclude ambiguous characters
}

const UPPERCASE = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
const LOWERCASE = 'abcdefghijklmnopqrstuvwxyz';
const NUMBERS = '0123456789';
const SPECIAL_CHARS = '!@#$%^&*()_+-=[]{}|;:,.<>?';

const SIMILAR_CHARS = '0O1lI';
const AMBIGUOUS_CHARS = '{}[]()\/\\\'"`~,;.<>';

/**
 * Generate a secure temporary password
 */
export function generateTemporaryPassword(options: PasswordGenerationOptions = {}): string {
  const {
    length = 12,
    includeUppercase = true,
    includeLowercase = true,
    includeNumbers = true,
    includeSpecialChars = true,
    excludeSimilar = true,
    excludeAmbiguous = true,
  } = options;

  let charset = '';
  let requiredChars = '';

  // Build character set
  if (includeUppercase) {
    let chars = UPPERCASE;
    if (excludeSimilar) chars = chars.replace(/[0O1lI]/g, '');
    charset += chars;
    requiredChars += chars.charAt(getRandomInt(0, chars.length));
  }

  if (includeLowercase) {
    let chars = LOWERCASE;
    if (excludeSimilar) chars = chars.replace(/[0O1lI]/g, '');
    charset += chars;
    requiredChars += chars.charAt(getRandomInt(0, chars.length));
  }

  if (includeNumbers) {
    let chars = NUMBERS;
    if (excludeSimilar) chars = chars.replace(/[0O1lI]/g, '');
    charset += chars;
    requiredChars += chars.charAt(getRandomInt(0, chars.length));
  }

  if (includeSpecialChars) {
    let chars = SPECIAL_CHARS;
    if (excludeAmbiguous) {
      chars = chars.split('').filter(char => !AMBIGUOUS_CHARS.includes(char)).join('');
    }
    charset += chars;
    requiredChars += chars.charAt(getRandomInt(0, chars.length));
  }

  if (charset.length === 0) {
    throw new Error('No character types selected for password generation');
  }

  // Generate remaining characters
  const remainingLength = length - requiredChars.length;
  let password = requiredChars;

  for (let i = 0; i < remainingLength; i++) {
    const randomIndex = getRandomInt(0, charset.length);
    password += charset[randomIndex];
  }

  // Shuffle the password to avoid predictable patterns
  return shuffleString(password);
}

/**
 * Shuffle a string randomly
 */
function shuffleString(str: string): string {
  const array = str.split('');
  for (let i = array.length - 1; i > 0; i--) {
    const j = getRandomInt(0, i + 1);
    [array[i], array[j]] = [array[j], array[i]];
  }
  return array.join('');
}

/**
 * Generate a secure random token for password reset
 */
export function generatePasswordResetToken(): string {
  const bytes = getRandomBytes(32);
  return Array.from(bytes, byte => byte.toString(16).padStart(2, '0')).join('');
}

/**
 * Generate a secure random token for email verification
 */
export function generateEmailVerificationToken(): string {
  const bytes = getRandomBytes(32);
  return Array.from(bytes, byte => byte.toString(16).padStart(2, '0')).join('');
}

/**
 * Check if a password meets minimum security requirements
 */
export function validatePasswordStrength(password: string, minLength: number = 8): {
  isValid: boolean;
  errors: string[];
  score: number;
} {
  const errors: string[] = [];
  let score = 0;

  // Length check
  if (password.length < minLength) {
    errors.push(`Password must be at least ${minLength} characters long`);
  } else {
    score += 1;
  }

  // Uppercase check
  if (!/[A-Z]/.test(password)) {
    errors.push('Password must contain at least one uppercase letter');
  } else {
    score += 1;
  }

  // Lowercase check
  if (!/[a-z]/.test(password)) {
    errors.push('Password must contain at least one lowercase letter');
  } else {
    score += 1;
  }

  // Number check
  if (!/\d/.test(password)) {
    errors.push('Password must contain at least one number');
  } else {
    score += 1;
  }

  // Special character check
  if (!/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
    errors.push('Password must contain at least one special character');
  } else {
    score += 1;
  }

  // Bonus points for longer passwords
  if (password.length >= 12) score += 1;
  if (password.length >= 16) score += 1;

  return {
    isValid: errors.length === 0,
    errors,
    score: Math.min(score, 5), // Cap at 5
  };
}

/**
 * Generate a user-friendly password that's still secure
 */
export function generateUserFriendlyPassword(): string {
  const adjectives = [
    'Happy', 'Bright', 'Swift', 'Calm', 'Bold', 'Smart', 'Quick', 'Wise',
    'Strong', 'Gentle', 'Brave', 'Clear', 'Fresh', 'Warm', 'Cool', 'Sharp'
  ];

  const nouns = [
    'Tiger', 'Eagle', 'River', 'Mountain', 'Ocean', 'Forest', 'Star', 'Moon',
    'Sun', 'Cloud', 'Wind', 'Fire', 'Stone', 'Tree', 'Flower', 'Bird'
  ];

  const adjective = adjectives[getRandomInt(0, adjectives.length)];
  const noun = nouns[getRandomInt(0, nouns.length)];
  const number = getRandomInt(10, 99);
  const specialChar = '!@#$%^&*'[getRandomInt(0, 8)];

  return `${adjective}${noun}${number}${specialChar}`;
}

/**
 * Hash a password using bcrypt (server-side only)
 */
export async function hashPassword(password: string): Promise<string> {
  if (isBrowser) {
    throw new Error('Password hashing should only be done on the server side');
  }
  const bcrypt = await import('bcryptjs');
  return bcrypt.hash(password, 12);
}

/**
 * Verify a password against a hash (server-side only)
 */
export async function verifyPassword(password: string, hash: string): Promise<boolean> {
  if (isBrowser) {
    throw new Error('Password verification should only be done on the server side');
  }
  const bcrypt = await import('bcryptjs');
  return bcrypt.compare(password, hash);
}
