'use client';

import React, { useState, useEffect } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  ShieldCheckIcon, 
  ExclamationTriangleIcon,
  InformationCircleIcon,
  UserGroupIcon,
  KeyIcon
} from '@heroicons/react/24/outline';
import { usePermissionContext } from '@/components/providers/permission-provider';
import { PermissionResource, PermissionAction } from '@/types';

interface Permission {
  id: string;
  name: string;
  resource: string;
  action: string;
  description: string;
  category: string;
}

interface Role {
  id: string;
  name: string;
  description: string;
  isSystemRole: boolean;
  permissions: Permission[];
}

interface UserRole {
  id: string;
  name: string;
  description: string;
  isSystemRole: boolean;
  assignedAt: Date;
  expiresAt?: Date;
}

interface AdvancedPermissionAssignmentProps {
  userId: string;
  currentRoles: UserRole[];
  onPermissionsUpdated: () => void;
}

export function AdvancedPermissionAssignment({
  userId,
  currentRoles,
  onPermissionsUpdated,
}: AdvancedPermissionAssignmentProps) {
  const { hasPermission, isTenantAdmin, permissions: userPermissions } = usePermissionContext();
  const [availableRoles, setAvailableRoles] = useState<Role[]>([]);
  const [availablePermissions, setAvailablePermissions] = useState<Permission[]>([]);
  const [selectedRoles, setSelectedRoles] = useState<string[]>([]);
  const [selectedPermissions, setSelectedPermissions] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [privilegeEscalationWarnings, setPrivilegeEscalationWarnings] = useState<string[]>([]);

  // Check if user can manage permissions
  const canManagePermissions = hasPermission(PermissionResource.USERS, PermissionAction.MANAGE) || isTenantAdmin;

  useEffect(() => {
    if (canManagePermissions) {
      fetchAvailableRoles();
      fetchAvailablePermissions();
    }
  }, [canManagePermissions]);

  useEffect(() => {
    // Check for privilege escalation when selections change
    checkPrivilegeEscalation();
  }, [selectedRoles, selectedPermissions]);

  const fetchAvailableRoles = async () => {
    try {
      const response = await fetch('/api/roles');
      if (!response.ok) throw new Error('Failed to fetch roles');
      
      const data = await response.json();
      setAvailableRoles(data.data || []);
    } catch (error) {
      console.error('Error fetching roles:', error);
      setError('Failed to load available roles');
    }
  };

  const fetchAvailablePermissions = async () => {
    try {
      const response = await fetch('/api/permissions');
      if (!response.ok) throw new Error('Failed to fetch permissions');
      
      const data = await response.json();
      setAvailablePermissions(data.data || []);
    } catch (error) {
      console.error('Error fetching permissions:', error);
      setError('Failed to load available permissions');
    }
  };

  const checkPrivilegeEscalation = () => {
    const warnings: string[] = [];
    
    // Check if user is trying to assign roles they don't have
    const selectedRoleObjects = availableRoles.filter(role => selectedRoles.includes(role.id));
    
    for (const role of selectedRoleObjects) {
      for (const permission of role.permissions) {
        const hasThisPermission = userPermissions.some(p => 
          p.resource === permission.resource && p.action === permission.action
        );
        
        if (!hasThisPermission && !isTenantAdmin) {
          warnings.push(`You cannot assign the role "${role.name}" because it contains permissions you don't have.`);
          break;
        }
      }
    }

    // Check individual permissions
    const selectedPermissionObjects = availablePermissions.filter(p => selectedPermissions.includes(p.id));
    
    for (const permission of selectedPermissionObjects) {
      const hasThisPermission = userPermissions.some(p => 
        p.resource === permission.resource && p.action === permission.action
      );
      
      if (!hasThisPermission && !isTenantAdmin) {
        warnings.push(`You cannot assign the permission "${permission.name}" because you don't have it yourself.`);
      }
    }

    setPrivilegeEscalationWarnings(warnings);
  };

  const handleRoleToggle = (roleId: string) => {
    setSelectedRoles(prev => 
      prev.includes(roleId) 
        ? prev.filter(id => id !== roleId)
        : [...prev, roleId]
    );
  };

  const handlePermissionToggle = (permissionId: string) => {
    setSelectedPermissions(prev => 
      prev.includes(permissionId) 
        ? prev.filter(id => id !== permissionId)
        : [...prev, permissionId]
    );
  };

  const handleSaveChanges = async () => {
    if (privilegeEscalationWarnings.length > 0) {
      setError('Cannot save changes due to privilege escalation warnings. Please review your selections.');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      // Update roles
      const response = await fetch('/api/users/permissions', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          userId,
          roles: selectedRoles,
          permissions: selectedPermissions,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update permissions');
      }

      onPermissionsUpdated();
      setSelectedRoles([]);
      setSelectedPermissions([]);
    } catch (error) {
      console.error('Error updating permissions:', error);
      setError(error instanceof Error ? error.message : 'Failed to update permissions');
    } finally {
      setLoading(false);
    }
  };

  const groupPermissionsByCategory = (permissions: Permission[]) => {
    return permissions.reduce((groups, permission) => {
      const category = permission.category || 'Other';
      if (!groups[category]) {
        groups[category] = [];
      }
      groups[category].push(permission);
      return groups;
    }, {} as Record<string, Permission[]>);
  };

  if (!canManagePermissions) {
    return (
      <Alert>
        <ExclamationTriangleIcon className="h-4 w-4" />
        <AlertDescription>
          You don't have permission to manage user permissions.
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <ShieldCheckIcon className="w-5 h-5" />
          Advanced Permission Assignment
        </CardTitle>
      </CardHeader>
      <CardContent>
        {error && (
          <Alert variant="destructive" className="mb-4">
            <ExclamationTriangleIcon className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {privilegeEscalationWarnings.length > 0 && (
          <Alert variant="destructive" className="mb-4">
            <ExclamationTriangleIcon className="h-4 w-4" />
            <AlertDescription>
              <div className="space-y-1">
                <p className="font-medium">Privilege Escalation Warning:</p>
                {privilegeEscalationWarnings.map((warning, index) => (
                  <p key={index} className="text-sm">{warning}</p>
                ))}
              </div>
            </AlertDescription>
          </Alert>
        )}

        <Tabs defaultValue="roles" className="space-y-4">
          <TabsList>
            <TabsTrigger value="roles" className="flex items-center gap-2">
              <UserGroupIcon className="w-4 h-4" />
              Roles
            </TabsTrigger>
            <TabsTrigger value="permissions" className="flex items-center gap-2">
              <KeyIcon className="w-4 h-4" />
              Individual Permissions
            </TabsTrigger>
          </TabsList>

          <TabsContent value="roles" className="space-y-4">
            <div className="space-y-3">
              <h4 className="text-sm font-medium text-gray-900">Current Roles</h4>
              <div className="flex flex-wrap gap-2">
                {currentRoles.map((role) => (
                  <Badge key={role.id} variant="secondary">
                    {role.name}
                    {role.isSystemRole && (
                      <span className="ml-1 text-xs">(System)</span>
                    )}
                  </Badge>
                ))}
                {currentRoles.length === 0 && (
                  <p className="text-sm text-gray-500">No roles assigned</p>
                )}
              </div>
            </div>

            <div className="space-y-3">
              <h4 className="text-sm font-medium text-gray-900">Available Roles</h4>
              <div className="space-y-2 max-h-60 overflow-y-auto">
                {availableRoles.map((role) => (
                  <div key={role.id} className="flex items-start space-x-3 p-3 border rounded-lg">
                    <Checkbox
                      id={`role-${role.id}`}
                      checked={selectedRoles.includes(role.id)}
                      onCheckedChange={() => handleRoleToggle(role.id)}
                      disabled={role.isSystemRole && !isTenantAdmin}
                    />
                    <div className="flex-1 min-w-0">
                      <label
                        htmlFor={`role-${role.id}`}
                        className="text-sm font-medium text-gray-900 cursor-pointer"
                      >
                        {role.name}
                        {role.isSystemRole && (
                          <Badge variant="outline" className="ml-2">System</Badge>
                        )}
                      </label>
                      {role.description && (
                        <p className="text-sm text-gray-500 mt-1">{role.description}</p>
                      )}
                      <div className="flex flex-wrap gap-1 mt-2">
                        {role.permissions.slice(0, 3).map((permission) => (
                          <Badge key={permission.id} variant="outline" className="text-xs">
                            {permission.name}
                          </Badge>
                        ))}
                        {role.permissions.length > 3 && (
                          <Badge variant="outline" className="text-xs">
                            +{role.permissions.length - 3} more
                          </Badge>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </TabsContent>

          <TabsContent value="permissions" className="space-y-4">
            <Alert>
              <InformationCircleIcon className="h-4 w-4" />
              <AlertDescription>
                Individual permissions provide granular control. Use with caution as they can override role-based permissions.
              </AlertDescription>
            </Alert>

            <div className="space-y-4">
              {Object.entries(groupPermissionsByCategory(availablePermissions)).map(([category, permissions]) => (
                <div key={category} className="space-y-2">
                  <h4 className="text-sm font-medium text-gray-900">{category}</h4>
                  <div className="space-y-2 pl-4">
                    {permissions.map((permission) => (
                      <div key={permission.id} className="flex items-start space-x-3">
                        <Checkbox
                          id={`permission-${permission.id}`}
                          checked={selectedPermissions.includes(permission.id)}
                          onCheckedChange={() => handlePermissionToggle(permission.id)}
                        />
                        <div className="flex-1 min-w-0">
                          <label
                            htmlFor={`permission-${permission.id}`}
                            className="text-sm font-medium text-gray-900 cursor-pointer"
                          >
                            {permission.name}
                          </label>
                          {permission.description && (
                            <p className="text-sm text-gray-500">{permission.description}</p>
                          )}
                          <p className="text-xs text-gray-400">
                            {permission.resource}.{permission.action}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </TabsContent>
        </Tabs>

        <div className="flex justify-end space-x-2 pt-4 border-t">
          <Button
            variant="outline"
            onClick={() => {
              setSelectedRoles([]);
              setSelectedPermissions([]);
              setError(null);
            }}
          >
            Cancel
          </Button>
          <Button
            onClick={handleSaveChanges}
            disabled={loading || privilegeEscalationWarnings.length > 0}
          >
            {loading ? 'Saving...' : 'Save Changes'}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
