'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Plus, Search, Filter, MoreHorizontal, Edit, Trash2, Building, Users, Globe, TrendingUp, Download, Upload, Eye } from 'lucide-react';
import { CompanyWithRelations } from '@/services/company-management';
import { usePermissions } from '@/components/providers/permission-provider';
import { PermissionResource, PermissionAction } from '@/types';
import { PermissionGate } from '@/components/ui/permission-gate';
import { PermissionAwareTable } from '@/components/ui/permission-aware-table';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select } from '@/components/ui/select';
import { EmptyState } from '@/components/ui/empty-state';
import { useDeleteConfirmationDialog } from './delete-confirmation-dialog';


interface CompanyManagementProps {
  className?: string;
}

export function CompanyManagement({ className }: CompanyManagementProps) {
  const router = useRouter();
  const [companies, setCompanies] = useState<CompanyWithRelations[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [showAdvancedSearch, setShowAdvancedSearch] = useState(false);
  const [filters, setFilters] = useState({
    industry: '',
    size: '',
    ownerId: '',
    hasWebsite: '',
  });

  // Delete confirmation dialog
  const { showDeleteDialog, DialogComponent } = useDeleteConfirmationDialog();

  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [users, setUsers] = useState<any[]>([]);

  const { hasPermission, canAccessResource } = usePermissions();

  // Fetch companies with advanced filters
  const fetchCompanies = async (page: number = 1, search: string = '', advancedFilters: any = {}) => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        page: page.toString(),
        limit: '20',
        ...(search && { search }),
        ...(advancedFilters.industry && { industry: advancedFilters.industry }),
        ...(advancedFilters.size && { size: advancedFilters.size }),
        ...(advancedFilters.ownerId && { ownerId: advancedFilters.ownerId }),
        ...(advancedFilters.hasWebsite && { hasWebsite: advancedFilters.hasWebsite }),
      });

      const response = await fetch(`/api/companies?${params}`);
      const data = await response.json();

      if (data.success) {
        setCompanies(data.data.companies);
        setTotalPages(data.data.totalPages);
        setCurrentPage(data.data.page);
      } else {
        setError(data.error || 'Failed to fetch companies');
      }
    } catch (err) {
      setError('Failed to fetch companies');
      console.error('Fetch companies error:', err);
    } finally {
      setLoading(false);
    }
  };

  // Fetch users for filters
  const fetchFilterData = async () => {
    try {
      const usersRes = await fetch('/api/users?limit=100');
      const usersData = await usersRes.json();

      if (usersData.success) {
        setUsers(usersData.data.users || []);
      }
    } catch (err) {
      console.error('Failed to fetch filter data:', err);
    }
  };

  // Load companies and filter data on component mount
  useEffect(() => {
    fetchCompanies();
    fetchFilterData();
  }, []);

  // Handle search
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    fetchCompanies(1, searchTerm, filters);
  };

  // Handle advanced search
  const handleAdvancedSearch = () => {
    fetchCompanies(1, searchTerm, filters);
    setShowAdvancedSearch(false);
  };

  // Clear filters
  const clearFilters = () => {
    setFilters({
      industry: '',
      size: '',
      ownerId: '',
      hasWebsite: '',
    });
    setSearchTerm('');
    fetchCompanies(1, '', {});
  };

  // Handle delete company
  const handleDeleteCompany = async (company: CompanyWithRelations) => {
    showDeleteDialog(company, async () => {
      try {
        const response = await fetch(`/api/companies?companyId=${company.id}`, {
          method: 'DELETE',
        });

        const data = await response.json();

        if (data.success) {
          fetchCompanies(currentPage, searchTerm, filters);
        } else {
          setError(data.error || 'Failed to delete company');
          throw new Error(data.error || 'Failed to delete company');
        }
      } catch (err) {
        setError('Failed to delete company');
        console.error('Delete company error:', err);
        throw err;
      }
    });
  };

  // Handle export
  const handleExport = async (format: 'csv' | 'xlsx') => {
    try {
      const params = new URLSearchParams({
        format,
        ...(searchTerm && { search: searchTerm }),
        ...(filters.industry && { industry: filters.industry }),
        ...(filters.size && { size: filters.size }),
        ...(filters.ownerId && { ownerId: filters.ownerId }),
        ...(filters.hasWebsite && { hasWebsite: filters.hasWebsite }),
      });

      const response = await fetch(`/api/companies/export?${params}`);

      if (response.ok) {
        const blob = await response.blob();
        const filename = `companies-${new Date().toISOString().split('T')[0]}.${format}`;

        if (typeof window !== 'undefined') {
          const url = window.URL.createObjectURL(blob);
          const a = window.document.createElement('a');
          a.href = url;
          a.download = filename;
          window.document.body.appendChild(a);
          a.click();
          window.URL.revokeObjectURL(url);
          window.document.body.removeChild(a);
        }
      } else {
        setError('Failed to export companies');
      }
    } catch (err) {
      setError('Failed to export companies');
      console.error('Export error:', err);
    }
  };





  // Table columns configuration
  const columns = [
    {
      key: 'name',
      label: 'Company',
      render: (company: CompanyWithRelations) => (
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
            <Building className="w-4 h-4 text-green-600" />
          </div>
          <div>
            <div className="font-medium text-gray-900">
              <button
                onClick={() => router.push(`/companies/${company.id}`)}
                className="hover:text-blue-600 hover:underline transition-colors text-left"
              >
                {company.name}
              </button>
            </div>
            {company.website && (
              <div className="text-sm text-gray-500 flex items-center">
                <Globe className="w-3 h-3 mr-1" />
                <a
                  href={company.website}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="hover:text-blue-600"
                >
                  {company.website.replace(/^https?:\/\//, '')}
                </a>
              </div>
            )}
          </div>
        </div>
      ),
      sortable: true,
    },
    {
      key: 'industry',
      label: 'Industry',
      render: (company: CompanyWithRelations) => (
        company.industry ? (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
            {company.industry}
          </span>
        ) : (
          <span className="text-gray-400">Not specified</span>
        )
      ),
    },
    {
      key: 'size',
      label: 'Size',
      render: (company: CompanyWithRelations) => (
        company.size ? (
          <span className="text-gray-900">{company.size}</span>
        ) : (
          <span className="text-gray-400">Not specified</span>
        )
      ),
    },
    {
      key: 'contacts',
      label: 'Contacts',
      render: (company: CompanyWithRelations) => (
        <div className="flex items-center text-gray-900">
          <Users className="w-4 h-4 mr-2 text-gray-400" />
          {company._count?.contacts || 0}
        </div>
      ),
    },
    {
      key: 'opportunities',
      label: 'Opportunities',
      render: (company: CompanyWithRelations) => (
        <div className="flex items-center text-gray-900">
          <TrendingUp className="w-4 h-4 mr-2 text-gray-400" />
          {company._count?.opportunities || 0}
        </div>
      ),
    },
    {
      key: 'owner',
      label: 'Owner',
      render: (company: CompanyWithRelations) => (
        company.owner ? (
          <span className="text-gray-900">
            {company.owner.firstName} {company.owner.lastName}
          </span>
        ) : (
          <span className="text-gray-400">Unassigned</span>
        )
      ),
    },
    {
      key: 'createdAt',
      label: 'Created',
      render: (company: CompanyWithRelations) => (
        <span className="text-gray-500">
          {new Date(company.createdAt).toLocaleDateString()}
        </span>
      ),
      sortable: true,
    },
  ];

  // Row actions
  const rowActions = [
    {
      label: 'Edit',
      icon: Edit,
      onClick: (company: CompanyWithRelations) => router.push(`/companies/${company.id}/edit`),
      permission: { resource: PermissionResource.COMPANIES, action: PermissionAction.UPDATE },
      requireOwnership: true,
      ownershipField: 'ownerId',
    },
    {
      label: 'Delete',
      icon: Trash2,
      onClick: (company: CompanyWithRelations) => handleDeleteCompany(company),
      permission: { resource: PermissionResource.COMPANIES, action: PermissionAction.DELETE },
      requireOwnership: true,
      ownershipField: 'ownerId',
      variant: 'destructive' as const,
    },
  ];

  // Handle bulk delete
  const handleBulkDelete = async (selectedCompanies: CompanyWithRelations[]) => {
    if (selectedCompanies.length === 0) return;

    // For bulk delete, we'll use a simple confirm for now
    // Could be enhanced with a custom bulk delete dialog later
    const confirmed = confirm(
      `Are you sure you want to delete ${selectedCompanies.length} companies? This action cannot be undone.`
    );

    if (!confirmed) return;

    try {
      const deletePromises = selectedCompanies.map(company =>
        fetch(`/api/companies?companyId=${company.id}`, { method: 'DELETE' })
      );

      await Promise.all(deletePromises);
      fetchCompanies(currentPage, searchTerm, filters);
    } catch (err) {
      setError('Failed to delete companies');
      console.error('Bulk delete error:', err);
    }
  };

  // Bulk actions
  const bulkActions = [
    {
      label: 'Delete Selected',
      icon: Trash2,
      onClick: handleBulkDelete,
      permission: { resource: PermissionResource.COMPANIES, action: PermissionAction.DELETE },
      requireOwnership: true,
      ownershipField: 'ownerId',
      variant: 'destructive' as const,
    },
  ];

  if (loading && companies.length === 0) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>

      {/* Search and Filters */}
      <Card>
        <CardContent>
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <Input
                placeholder="Search companies..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                leftIcon={<Search className="w-4 h-4" />}
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    e.preventDefault();
                    fetchCompanies(1, searchTerm, filters);
                  }
                }}
              />
            </div>
            <div className="flex gap-2">
              <Button
                variant={showAdvancedSearch || Object.values(filters).some(v => v) ? 'default' : 'outline'}
                onClick={() => setShowAdvancedSearch(!showAdvancedSearch)}
              >
                <Filter className="w-4 h-4 mr-2" />
                Filters
              </Button>
              {Object.values(filters).some(v => v) && (
                <Button
                  variant="ghost"
                  onClick={clearFilters}
                >
                  Clear
                </Button>
              )}
            </div>
          </div>

          {/* Advanced Search Panel */}
          {showAdvancedSearch && (
            <Card variant="outlined" className="mt-4">
              <CardHeader>
                <CardTitle as="h3">Advanced Filters</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  <Select
                    label="Industry"
                    value={filters.industry}
                    onChange={(e: React.ChangeEvent<HTMLSelectElement>) => setFilters(prev => ({ ...prev, industry: e.target.value }))}
                    options={[
                      { value: '', label: 'All industries' },
                      { value: 'Technology', label: 'Technology' },
                      { value: 'Healthcare', label: 'Healthcare' },
                      { value: 'Finance', label: 'Finance' },
                      { value: 'Manufacturing', label: 'Manufacturing' },
                      { value: 'Retail', label: 'Retail' },
                      { value: 'Education', label: 'Education' },
                      { value: 'Real Estate', label: 'Real Estate' },
                      { value: 'Other', label: 'Other' }
                    ]}
                  />

                  <Select
                    label="Company Size"
                    value={filters.size}
                    onChange={(e: React.ChangeEvent<HTMLSelectElement>) => setFilters(prev => ({ ...prev, size: e.target.value }))}
                    options={[
                      { value: '', label: 'All sizes' },
                      { value: '1-10', label: '1-10 employees' },
                      { value: '11-50', label: '11-50 employees' },
                      { value: '51-200', label: '51-200 employees' },
                      { value: '201-500', label: '201-500 employees' },
                      { value: '501-1000', label: '501-1000 employees' },
                      { value: '1000+', label: '1000+ employees' }
                    ]}
                  />

                  <Select
                    label="Owner"
                    value={filters.ownerId}
                    onChange={(e: React.ChangeEvent<HTMLSelectElement>) => setFilters(prev => ({ ...prev, ownerId: e.target.value }))}
                    options={[
                      { value: '', label: 'All owners' },
                      ...users.map(user => ({
                        value: user.id,
                        label: `${user.firstName} ${user.lastName}`
                      }))
                    ]}
                  />

                  <Select
                    label="Has Website"
                    value={filters.hasWebsite}
                    onChange={(e: React.ChangeEvent<HTMLSelectElement>) => setFilters(prev => ({ ...prev, hasWebsite: e.target.value }))}
                    options={[
                      { value: '', label: 'Any' },
                      { value: 'true', label: 'Yes' },
                      { value: 'false', label: 'No' }
                    ]}
                  />
                </div>

                <div className="flex items-center justify-end space-x-3 mt-6">
                  <Button
                    variant="outline"
                    onClick={() => setShowAdvancedSearch(false)}
                  >
                    Cancel
                  </Button>
                  <Button
                    variant="default"
                    onClick={handleAdvancedSearch}
                  >
                    Apply Filters
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}
        </CardContent>
      </Card>

      {/* Error Message */}
      {error && (
        <Card variant="outlined" className="border-red-200 dark:border-red-800">
          <CardContent>
            <div className="flex items-center space-x-3">
              <div className="flex-shrink-0">
                <svg className="w-5 h-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="text-red-800 dark:text-red-200">{error}</div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Companies Table */}
      {companies.length === 0 && !loading ? (
        <Card>
          <CardContent>
            <EmptyState
              icon={<Building className="w-12 h-12" />}
              title="No companies found"
              description="Get started by creating your first company or importing from a CSV file."
              action={{
                label: 'Add Company',
                onClick: () => router.push('/companies/new'),
                variant: 'primary'
              }}
              secondaryAction={{
                label: 'Import CSV',
                onClick: () => router.push('/companies/import')
              }}
            />
          </CardContent>
        </Card>
      ) : (
        <Card>
          <PermissionAwareTable
            data={companies}
            columns={columns}
            actions={rowActions}
            bulkActions={bulkActions}
            resource={PermissionResource.COMPANIES}
            idField="id"
            ownershipField="ownerId"
            loading={loading}
            emptyMessage="No companies found"
          />
        </Card>
      )}

      {/* Pagination */}
      {totalPages > 1 && (
        <Card>
          <CardContent>
            <div className="flex items-center justify-between">
              <div className="text-sm text-gray-700 dark:text-gray-300">
                Page {currentPage} of {totalPages}
              </div>
              <div className="flex space-x-2">
                <Button
                  variant="outline"
                  onClick={() => fetchCompanies(currentPage - 1, searchTerm, filters)}
                  disabled={currentPage === 1}
                  size="sm"
                >
                  Previous
                </Button>
                <Button
                  variant="outline"
                  onClick={() => fetchCompanies(currentPage + 1, searchTerm, filters)}
                  disabled={currentPage === totalPages}
                  size="sm"
                >
                  Next
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Delete Confirmation Dialog */}
      <DialogComponent />
    </div>
  );
}
