'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import {
  KeyIcon,
  CheckIcon,
  XMarkIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
  InformationCircleIcon
} from '@heroicons/react/24/outline';

interface Permission {
  id: string;
  name: string;
  resource: string;
  action: string;
  category: string;
  description: string;
}

interface PermissionManagerProps {
  permissions: Permission[];
  groupedPermissions: Record<string, Permission[]>;
  selectedPermissions: string[];
  onPermissionToggle: (permissionId: string) => void;
  onSelectAllCategory: (category: string) => void;
  isSystemRole?: boolean;
  disabled?: boolean;
  showSearch?: boolean;
  showStats?: boolean;
}

export function PermissionManager({
  permissions,
  groupedPermissions,
  selectedPermissions,
  onPermissionToggle,
  onSelectAllCategory,
  isSystemRole = false,
  disabled = false,
  showSearch = true,
  showStats = true
}: PermissionManagerProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [expandedCategories, setExpandedCategories] = useState<Set<string>>(new Set());

  // Initialize with all categories expanded
  useEffect(() => {
    setExpandedCategories(new Set(Object.keys(groupedPermissions)));
  }, [groupedPermissions]);

  const toggleCategory = (category: string) => {
    const newExpanded = new Set(expandedCategories);
    if (newExpanded.has(category)) {
      newExpanded.delete(category);
    } else {
      newExpanded.add(category);
    }
    setExpandedCategories(newExpanded);
  };

  const filteredCategories = Object.entries(groupedPermissions).filter(([category, categoryPermissions]) => {
    if (selectedCategory !== 'all' && category !== selectedCategory) {
      return false;
    }

    if (!searchTerm) {
      return true;
    }

    return categoryPermissions.some(permission =>
      permission.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      permission.description.toLowerCase().includes(searchTerm.toLowerCase())
    );
  });

  const filteredPermissions = (categoryPermissions: Permission[]) => {
    if (!searchTerm) {
      return categoryPermissions;
    }

    return categoryPermissions.filter(permission =>
      permission.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      permission.description.toLowerCase().includes(searchTerm.toLowerCase())
    );
  };

  const totalPermissions = permissions.length;
  const selectedCount = selectedPermissions.length;
  const categories = Object.keys(groupedPermissions);

  if (Object.keys(groupedPermissions).length === 0) {
    return (
      <div className="text-center py-8">
        <KeyIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
        <p className="text-gray-500">No permissions available</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Search and Filter Controls */}
      {showSearch && (
        <Card>
          <CardContent className="p-4">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="relative flex-1">
                <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <input
                  type="text"
                  placeholder="Search permissions..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  disabled={disabled}
                />
              </div>
              <div className="flex items-center gap-2">
                <FunnelIcon className="w-4 h-4 text-gray-400" />
                <select
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  disabled={disabled}
                >
                  <option value="all">All Categories</option>
                  {categories.map((category) => (
                    <option key={category} value={category}>
                      {category.replace('_', ' ').toUpperCase()}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Stats */}
      {showStats && (
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <div className="flex items-center gap-2">
                  <CheckIcon className="w-5 h-5 text-green-500" />
                  <span className="text-sm font-medium">
                    {selectedCount} of {totalPermissions} permissions selected
                  </span>
                </div>
                {isSystemRole && (
                  <div className="flex items-center gap-2 text-orange-600">
                    <InformationCircleIcon className="w-4 h-4" />
                    <span className="text-xs">System Role</span>
                  </div>
                )}
              </div>
              <div className="text-sm text-gray-500">
                {categories.length} categories
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Permission Categories */}
      <div className="space-y-4">
        {filteredCategories.map(([category, categoryPermissions]) => {
          const filteredCategoryPermissions = filteredPermissions(categoryPermissions);
          const categoryPermissionIds = filteredCategoryPermissions.map(p => p.id);
          const selectedInCategory = categoryPermissionIds.filter(id => 
            selectedPermissions.includes(id)
          ).length;
          const allSelected = selectedInCategory === categoryPermissionIds.length && categoryPermissionIds.length > 0;
          const isExpanded = expandedCategories.has(category);

          return (
            <Card key={category} className={`${isSystemRole ? 'border-orange-200' : 'border-gray-200'}`}>
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => toggleCategory(category)}
                      disabled={disabled}
                      className="p-1"
                    >
                      {isExpanded ? (
                        <XMarkIcon className="w-4 h-4" />
                      ) : (
                        <KeyIcon className="w-4 h-4" />
                      )}
                    </Button>
                    <div>
                      <CardTitle className="text-lg capitalize">
                        {category.replace('_', ' ')}
                      </CardTitle>
                      <p className="text-sm text-gray-600">
                        {selectedInCategory}/{categoryPermissionIds.length} selected
                      </p>
                    </div>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onSelectAllCategory(category)}
                    disabled={disabled || categoryPermissionIds.length === 0}
                    className={isSystemRole ? 'border-orange-300 text-orange-700 hover:bg-orange-50' : ''}
                  >
                    {allSelected ? 'Deselect All' : 'Select All'}
                  </Button>
                </div>
              </CardHeader>
              
              {isExpanded && (
                <CardContent className="pt-0">
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                    {filteredCategoryPermissions.map((permission) => (
                      <label
                        key={permission.id}
                        className={`flex items-start gap-3 p-3 border rounded-lg hover:bg-gray-50 cursor-pointer transition-colors ${
                          disabled ? 'opacity-50 cursor-not-allowed' : ''
                        } ${
                          selectedPermissions.includes(permission.id)
                            ? isSystemRole
                              ? 'border-orange-300 bg-orange-50'
                              : 'border-blue-300 bg-blue-50'
                            : 'border-gray-200'
                        }`}
                      >
                        <input
                          type="checkbox"
                          checked={selectedPermissions.includes(permission.id)}
                          onChange={() => !disabled && onPermissionToggle(permission.id)}
                          disabled={disabled}
                          className={`mt-0.5 h-4 w-4 border-gray-300 rounded focus:ring-2 ${
                            isSystemRole
                              ? 'text-orange-600 focus:ring-orange-500'
                              : 'text-blue-600 focus:ring-blue-500'
                          }`}
                        />
                        <div className="min-w-0 flex-1">
                          <p className="font-medium text-sm text-gray-900">
                            {permission.name}
                          </p>
                          {permission.description && (
                            <p className="text-xs text-gray-500 mt-1 line-clamp-2">
                              {permission.description}
                            </p>
                          )}
                          <div className="flex items-center gap-2 mt-1">
                            <span className="text-xs bg-gray-100 text-gray-600 px-2 py-0.5 rounded">
                              {permission.resource}
                            </span>
                            <span className="text-xs bg-gray-100 text-gray-600 px-2 py-0.5 rounded">
                              {permission.action}
                            </span>
                          </div>
                        </div>
                      </label>
                    ))}
                  </div>
                  
                  {filteredCategoryPermissions.length === 0 && searchTerm && (
                    <div className="text-center py-8">
                      <MagnifyingGlassIcon className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                      <p className="text-gray-500 text-sm">
                        No permissions found matching "{searchTerm}"
                      </p>
                    </div>
                  )}
                </CardContent>
              )}
            </Card>
          );
        })}
      </div>

      {filteredCategories.length === 0 && searchTerm && (
        <Card>
          <CardContent className="p-12 text-center">
            <MagnifyingGlassIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No Results Found</h3>
            <p className="text-gray-500">
              No permissions or categories match your search for "{searchTerm}"
            </p>
            <Button
              variant="outline"
              onClick={() => setSearchTerm('')}
              className="mt-4"
            >
              Clear Search
            </Button>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
