'use client';

import { SessionProvider } from 'next-auth/react';
import { TenantProvider } from './tenant-provider';
import { LocaleProvider } from './locale-provider';
import { ThemeProvider } from './theme-provider';
import { EnhancedPermissionProvider } from './enhanced-permission-provider';
import { StatePersistenceProvider } from './state-persistence-provider';
import { PermissionProvider } from './permission-provider';

interface ProvidersProps {
  children: React.ReactNode;
}

export function Providers({ children }: ProvidersProps) {
  return (
    <SessionProvider>
      <ThemeProvider>
        <LocaleProvider>
          <TenantProvider>
            <EnhancedPermissionProvider
              enableCache={true}
              enableRealTimeUpdates={true}
              cacheOptions={{
                ttl: 3600000, // 1 hour
                encrypt: true,
                validateIntegrity: true
              }}
            >
              <StatePersistenceProvider>
                {children}
              </StatePersistenceProvider>
            </EnhancedPermissionProvider>
          </TenantProvider>
        </LocaleProvider>
      </ThemeProvider>
    </SessionProvider>
  );
}
