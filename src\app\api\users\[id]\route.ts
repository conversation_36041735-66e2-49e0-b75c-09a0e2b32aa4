import { NextResponse } from 'next/server';
import { withPermission, AuthenticatedRequest } from '@/lib/permission-middleware';
import { UserManagementService } from '@/services/user-management';
import { PermissionAction, PermissionResource } from '@/types';
import { z } from 'zod';

const updateUserTenantSchema = z.object({
  role: z.string().optional(),
  teamId: z.string().optional(),
  department: z.string().optional(),
  jobTitle: z.string().optional(),
  status: z.enum(['active', 'inactive', 'pending']).optional()
});

const updateUserProfileSchema = z.object({
  firstName: z.string().min(1, 'First name is required').optional(),
  lastName: z.string().min(1, 'Last name is required').optional(),
  locale: z.enum(['en', 'ar']).optional(),
  timezone: z.string().optional(),
  avatarUrl: z.string().url().optional(),
  status: z.enum(['active', 'inactive', 'pending']).optional()
});

interface RouteParams {
  params: Promise<{
    id: string;
  }>;
}

// Get single user
export const GET = withPermission({
  resource: PermissionResource.USERS,
  action: PermissionAction.READ,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest, { params }: RouteParams) => {
  try {
    const { id: userId } = await params;

    const userService = new UserManagementService();
    const user = await userService.getUserById(userId, req.tenantId);

    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: user
    });

  } catch (error) {
    console.error('Get user error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
});

// Update user
export const PUT = withPermission({
  resource: PermissionResource.USERS,
  action: PermissionAction.UPDATE,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest, { params }: RouteParams) => {
  try {
    const { id: userId } = await params;
    const body = await req.json();
    const { updateType = 'profile' } = body;

    const userService = new UserManagementService();

    if (updateType === 'tenant') {
      // Update tenant-specific information
      const validatedData = updateUserTenantSchema.parse(body);

      await userService.updateUserTenantInfo(
        userId,
        req.tenantId,
        validatedData,
        req.userId
      );

      return NextResponse.json({
        success: true,
        message: 'User tenant information updated successfully'
      });

    } else {
      // Update user profile
      const validatedData = updateUserProfileSchema.parse(body);

      const updatedUser = await userService.updateUser(userId, validatedData);

      return NextResponse.json({
        success: true,
        data: {
          id: updatedUser.id,
          email: updatedUser.email,
          firstName: updatedUser.firstName,
          lastName: updatedUser.lastName,
          locale: updatedUser.locale,
          timezone: updatedUser.timezone,
          avatarUrl: updatedUser.avatarUrl,
          status: updatedUser.status,
          updatedAt: updatedUser.updatedAt
        },
        message: 'User profile updated successfully'
      });
    }

  } catch (error) {
    console.error('Update user error:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          error: 'Validation failed',
          details: error.errors.map(e => ({
            field: e.path.join('.'),
            message: e.message
          }))
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
});

// Remove user from tenant
export const DELETE = withPermission({
  resource: PermissionResource.USERS,
  action: PermissionAction.DELETE,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest, { params }: RouteParams) => {
  try {
    const { id: userId } = await params;

    // Users cannot remove themselves
    if (userId === req.userId) {
      return NextResponse.json(
        { error: 'You cannot remove yourself from the tenant' },
        { status: 400 }
      );
    }

    const userService = new UserManagementService();
    await userService.removeUserFromTenant(req.tenantId, userId, req.userId);

    return NextResponse.json({
      success: true,
      message: 'User removed from tenant successfully'
    });

  } catch (error) {
    console.error('Remove user error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
});
