"use client"

import { useEffect, useCallback } from 'react'
import { usePathname } from 'next/navigation'
import { useStatePersistence } from '@/components/providers/state-persistence-provider'

export function useFormPersistence<T extends Record<string, any>>(
  formData: T,
  setFormData: (data: T) => void,
  options?: {
    key?: string
    debounceMs?: number
    clearOnSubmit?: boolean
  }
) {
  const pathname = usePathname()
  const { saveFormData, restoreFormData, clearFormData } = useStatePersistence()
  const key = options?.key || pathname
  const debounceMs = options?.debounceMs || 500
  const clearOnSubmit = options?.clearOnSubmit ?? true

  // Restore form data on mount
  useEffect(() => {
    const savedData = restoreFormData(key)
    if (savedData) {
      setFormData({ ...formData, ...savedData })
    }
  }, [key]) // Only run on mount and key change

  // Save form data with debouncing
  useEffect(() => {
    const timer = setTimeout(() => {
      // Only save if form has meaningful data
      const hasData = Object.values(formData).some(value => {
        if (typeof value === 'string') return value.trim() !== ''
        if (typeof value === 'number') return value !== 0
        if (Array.isArray(value)) return value.length > 0
        if (typeof value === 'object' && value !== null) return Object.keys(value).length > 0
        return Boolean(value)
      })

      if (hasData) {
        saveFormData(key, formData)
      }
    }, debounceMs)

    return () => clearTimeout(timer)
  }, [formData, key, debounceMs, saveFormData])

  const clearPersistedData = useCallback(() => {
    clearFormData(key)
  }, [key, clearFormData])

  const handleSubmit = useCallback(() => {
    if (clearOnSubmit) {
      clearPersistedData()
    }
  }, [clearOnSubmit, clearPersistedData])

  return {
    clearPersistedData,
    handleSubmit,
  }
}
