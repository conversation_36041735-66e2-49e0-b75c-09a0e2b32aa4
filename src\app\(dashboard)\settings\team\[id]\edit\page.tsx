'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { PermissionProvider } from '@/components/providers/permission-provider';
import { PermissionGate } from '@/components/ui/permission-gate';
import { PermissionAction, PermissionResource } from '@/types';
import { useTenant } from '@/hooks/use-tenant';
import { PageLayout } from '@/components/layout/page-layout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
// Removed LegacySelect import - using standard HTML select elements
import { TeamWithDetails, User } from '@/types';
import {
  ArrowLeftIcon,
  UserGroupIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';

interface EditTeamFormData {
  name: string;
  description: string;
  color: string;
  managerId: string;
  parentTeamId: string;
  isActive: boolean;
}

export default function EditTeamPage() {
  const { currentTenant } = useTenant();
  const router = useRouter();
  const params = useParams();
  const teamId = params.id as string;

  const [team, setTeam] = useState<TeamWithDetails | null>(null);
  const [formData, setFormData] = useState<EditTeamFormData>({
    name: '',
    description: '',
    color: '#6B7280',
    managerId: '',
    parentTeamId: '',
    isActive: true
  });
  const [availableManagers, setAvailableManagers] = useState<User[]>([]);
  const [existingTeams, setExistingTeams] = useState<TeamWithDetails[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const colorOptions = [
    { value: '#6B7280', label: 'Gray', color: '#6B7280' },
    { value: '#EF4444', label: 'Red', color: '#EF4444' },
    { value: '#F97316', label: 'Orange', color: '#F97316' },
    { value: '#EAB308', label: 'Yellow', color: '#EAB308' },
    { value: '#22C55E', label: 'Green', color: '#22C55E' },
    { value: '#06B6D4', label: 'Cyan', color: '#06B6D4' },
    { value: '#3B82F6', label: 'Blue', color: '#3B82F6' },
    { value: '#8B5CF6', label: 'Purple', color: '#8B5CF6' },
    { value: '#EC4899', label: 'Pink', color: '#EC4899' },
  ];

  useEffect(() => {
    if (currentTenant && teamId) {
      fetchTeamDetails();
      fetchAvailableManagers();
      fetchExistingTeams();
    }
  }, [currentTenant, teamId]);

  const fetchTeamDetails = async () => {
    try {
      const response = await fetch(`/api/teams/${teamId}`);
      if (response.ok) {
        const data = await response.json();
        const teamData = data.data.team;
        setTeam(teamData);
        setFormData({
          name: teamData.name,
          description: teamData.description || '',
          color: teamData.color || '#6B7280',
          managerId: teamData.managerId || '',
          parentTeamId: teamData.parentTeamId || '',
          isActive: teamData.isActive
        });
      } else {
        setError('Failed to fetch team details');
      }
    } catch (error) {
      console.error('Failed to fetch team details:', error);
      setError('Failed to fetch team details');
    } finally {
      setIsLoading(false);
    }
  };

  const fetchAvailableManagers = async () => {
    try {
      const response = await fetch('/api/users');
      if (response.ok) {
        const data = await response.json();
        setAvailableManagers(data.data || []);
      }
    } catch (error) {
      console.error('Failed to fetch managers:', error);
    }
  };

  const fetchExistingTeams = async () => {
    try {
      const response = await fetch('/api/teams');
      if (response.ok) {
        const data = await response.json();
        // Filter out the current team and its descendants to prevent circular references
        const teams = data.data?.teams || [];
        const filteredTeams = teams.filter((t: TeamWithDetails) => t.id !== teamId);
        setExistingTeams(filteredTeams);
      }
    } catch (error) {
      console.error('Failed to fetch teams:', error);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);

    if (!formData.name.trim()) {
      setError('Team name is required');
      return;
    }

    // Check for duplicate team names (excluding current team)
    const isDuplicate = existingTeams.some(
      t => t.name.toLowerCase() === formData.name.toLowerCase() && t.id !== teamId
    );

    if (isDuplicate) {
      setError('A team with this name already exists');
      return;
    }

    setIsSaving(true);

    try {
      const response = await fetch(`/api/teams/${teamId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: formData.name.trim(),
          description: formData.description.trim() || undefined,
          color: formData.color,
          managerId: formData.managerId || undefined,
          parentTeamId: formData.parentTeamId || undefined,
          isActive: formData.isActive
        }),
      });

      if (response.ok) {
        router.push(`/settings/team/${teamId}`);
      } else {
        const errorData = await response.json();
        setError(errorData.error || 'Failed to update team');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update team');
    } finally {
      setIsSaving(false);
    }
  };

  const handleCancel = () => {
    router.push(`/settings/team/${teamId}`);
  };

  if (!currentTenant) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <ExclamationTriangleIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-500">No tenant context available</p>
        </div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <PageLayout title="Loading..." description="Loading team details...">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </PageLayout>
    );
  }

  if (error && !team) {
    return (
      <PageLayout title="Error" description="Failed to load team details">
        <div className="text-center py-12">
          <ExclamationTriangleIcon className="w-12 h-12 text-red-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Error</h3>
          <p className="text-gray-500 mb-4">{error}</p>
          <Button onClick={() => router.push('/settings/team')}>
            <ArrowLeftIcon className="w-4 h-4 mr-2" />
            Back to Teams
          </Button>
        </div>
      </PageLayout>
    );
  }

  const actions = (
    <div className="flex items-center gap-2">
      <Button variant="outline" onClick={handleCancel}>
        Cancel
      </Button>
      <Button 
        type="submit" 
        form="edit-team-form"
        loading={isSaving}
        disabled={!formData.name.trim()}
      >
        Save Changes
      </Button>
    </div>
  );

  return (
    <PermissionProvider>
      <PageLayout
        title={`Edit Team: ${team?.name || 'Loading...'}`}
        description="Update team information and settings"
        actions={actions}
      >
        <PermissionGate
          resource={PermissionResource.USERS}
          action={PermissionAction.MANAGE}
          fallback={<AccessDeniedMessage />}
        >
          <div className="max-w-2xl">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <UserGroupIcon className="w-5 h-5" />
                  Team Information
                </CardTitle>
              </CardHeader>
              <CardContent>
                <form id="edit-team-form" onSubmit={handleSubmit} className="space-y-6">
                  {error && (
                    <div className="bg-red-50 border border-red-200 rounded-md p-4">
                      <p className="text-red-600">{error}</p>
                    </div>
                  )}

                  {/* Team Name */}
                  <div>
                    <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
                      Team Name *
                    </label>
                    <Input
                      id="name"
                      type="text"
                      value={formData.name}
                      onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                      placeholder="Enter team name"
                      required
                    />
                  </div>

                  {/* Description */}
                  <div>
                    <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-2">
                      Description
                    </label>
                    <textarea
                      id="description"
                      value={formData.description}
                      onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                      placeholder="Enter team description"
                      rows={3}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>

                  {/* Team Color */}
                  <div>
                    <label htmlFor="color" className="block text-sm font-medium text-gray-700 mb-2">
                      Team Color
                    </label>
                    <div className="flex items-center gap-3">
                      <div 
                        className="w-8 h-8 rounded-full border-2 border-gray-300"
                        style={{ backgroundColor: formData.color }}
                      />
                      <select
                        value={formData.color}
                        onChange={(e) => setFormData({ ...formData, color: e.target.value })}
                        className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      >
                        {colorOptions.map((option) => (
                          <option key={option.value} value={option.value}>
                            {option.label}
                          </option>
                        ))}
                      </select>
                    </div>
                  </div>

                  {/* Team Manager */}
                  <div>
                    <label htmlFor="managerId" className="block text-sm font-medium text-gray-700 mb-2">
                      Team Manager
                    </label>
                    <select
                      value={formData.managerId}
                      onChange={(e) => setFormData({ ...formData, managerId: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      <option value="">Select a manager (optional)</option>
                      {availableManagers.map((manager) => (
                        <option key={manager.id} value={manager.id}>
                          {manager.firstName} {manager.lastName} {manager.email ? `(${manager.email})` : ''}
                        </option>
                      ))}
                    </select>
                  </div>

                  {/* Parent Team */}
                  <div>
                    <label htmlFor="parentTeamId" className="block text-sm font-medium text-gray-700 mb-2">
                      Parent Team
                    </label>
                    <select
                      value={formData.parentTeamId}
                      onChange={(e) => setFormData({ ...formData, parentTeamId: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      <option value="">No parent team</option>
                      {existingTeams.map((team) => (
                        <option key={team.id} value={team.id}>
                          {team.name}
                        </option>
                      ))}
                    </select>
                  </div>

                  {/* Team Status */}
                  <div>
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={formData.isActive}
                        onChange={(e) => setFormData({ ...formData, isActive: e.target.checked })}
                        className="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                      />
                      <span className="ml-2 text-sm text-gray-700">Team is active</span>
                    </label>
                  </div>
                </form>
              </CardContent>
            </Card>
          </div>
        </PermissionGate>
      </PageLayout>
    </PermissionProvider>
  );
}

function AccessDeniedMessage() {
  return (
    <div className="text-center py-12">
      <ExclamationTriangleIcon className="w-12 h-12 text-red-400 mx-auto mb-4" />
      <h3 className="text-lg font-medium text-gray-900 mb-2">Access Denied</h3>
      <p className="text-gray-500">
        You don't have permission to edit teams. Contact your administrator for access.
      </p>
    </div>
  );
}
