'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { usePermissions } from '@/components/providers/permission-provider';
import { PermissionGate } from '@/components/ui/permission-gate';
import { PermissionAction, PermissionResource, TeamWithDetails } from '@/types';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { TeamsGrid } from '@/components/admin/teams-grid';
import { TeamsList } from '@/components/admin/teams-list';
import { Card, CardContent } from '@/components/ui/card';
import {
  UserGroupIcon,
  MagnifyingGlassIcon,
  ArrowDownTrayIcon,
  Squares2X2Icon,
  ListBulletIcon
} from '@heroicons/react/24/outline';

interface EnhancedTeamManagementProps {
  tenantId: string;
}

export function EnhancedTeamManagement({ tenantId }: EnhancedTeamManagementProps) {
  const router = useRouter();
  const [teams, setTeams] = useState<TeamWithDetails[]>([]);
  const [filteredTeams, setFilteredTeams] = useState<TeamWithDetails[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('active');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');

  const { hasPermission } = usePermissions();

  useEffect(() => {
    fetchTeams();
  }, [tenantId]);

  useEffect(() => {
    filterTeams();
  }, [teams, searchTerm, statusFilter]);

  const fetchTeams = async () => {
    try {
      setIsLoading(true);
      const response = await fetch(`/api/teams?includeMembers=true&includeInactive=${statusFilter === 'all'}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch teams');
      }

      const data = await response.json();
      setTeams(data.data.teams);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch teams');
    } finally {
      setIsLoading(false);
    }
  };

  const filterTeams = () => {
    let filtered = teams;

    if (searchTerm) {
      filtered = filtered.filter(team =>
        team.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        team.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        team.manager?.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        team.manager?.lastName.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    if (statusFilter !== 'all') {
      filtered = filtered.filter(team => 
        statusFilter === 'active' ? team.isActive : !team.isActive
      );
    }

    setFilteredTeams(filtered);
  };

  const handleTeamSelect = (team: TeamWithDetails) => {
    router.push(`/settings/team/${team.id}`);
  };

  const handleDeleteTeam = async (teamId: string) => {
    if (!confirm('Are you sure you want to delete this team? This action cannot be undone.')) {
      return;
    }

    try {
      const response = await fetch(`/api/teams/${teamId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Failed to delete team');
      }

      await fetchTeams();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete team');
    }
  };

  const handleExportTeams = async () => {
    try {
      const response = await fetch('/api/teams/export?format=csv&includeMembers=true');
      
      if (!response.ok) {
        throw new Error('Failed to export teams');
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'teams.csv';
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to export teams');
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Filters and Search */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <MagnifyingGlassIcon className="w-5 h-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search teams..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            </div>

            <div className="flex space-x-3">
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="active">Active Teams</option>
                <option value="inactive">Inactive Teams</option>
                <option value="all">All Teams</option>
              </select>

              <div className="flex border border-gray-300 rounded-md">
                <button
                  onClick={() => setViewMode('grid')}
                  className={`px-3 py-2 flex items-center gap-1 ${
                    viewMode === 'grid'
                      ? 'bg-blue-50 text-blue-600'
                      : 'text-gray-500 hover:text-gray-700'
                  }`}
                >
                  <Squares2X2Icon className="w-4 h-4" />
                  Grid
                </button>
                <button
                  onClick={() => setViewMode('list')}
                  className={`px-3 py-2 flex items-center gap-1 border-l border-gray-300 ${
                    viewMode === 'list'
                      ? 'bg-blue-50 text-blue-600'
                      : 'text-gray-500 hover:text-gray-700'
                  }`}
                >
                  <ListBulletIcon className="w-4 h-4" />
                  List
                </button>
              </div>

              <button
                onClick={handleExportTeams}
                className="bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700 transition-colors flex items-center space-x-2"
              >
                <ArrowDownTrayIcon className="w-4 h-4" />
                <span>Export</span>
              </button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Error Display */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <p className="text-red-600">{error}</p>
          <button
            onClick={() => setError(null)}
            className="text-red-600 hover:text-red-800 underline mt-2"
          >
            Dismiss
          </button>
        </div>
      )}

      {/* Teams Display */}
      {viewMode === 'grid' ? (
        <TeamsGrid
          teams={filteredTeams}
          onTeamSelect={handleTeamSelect}
          onTeamDelete={handleDeleteTeam}
        />
      ) : (
        <TeamsList
          teams={filteredTeams}
          onTeamSelect={handleTeamSelect}
          onTeamDelete={handleDeleteTeam}
        />
      )}
    </div>
  );
}
