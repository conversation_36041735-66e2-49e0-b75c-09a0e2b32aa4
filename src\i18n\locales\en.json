{"common": {"loading": "Loading...", "save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "create": "Create", "update": "Update", "search": "Search", "filter": "Filter", "export": "Export", "import": "Import", "yes": "Yes", "no": "No", "confirm": "Confirm", "back": "Back", "next": "Next", "previous": "Previous", "close": "Close", "open": "Open", "view": "View", "actions": "Actions", "status": "Status", "active": "Active", "inactive": "Inactive", "name": "Name", "email": "Email", "phone": "Phone", "address": "Address", "date": "Date", "time": "Time", "description": "Description", "notes": "Notes", "settings": "Settings", "profile": "Profile", "logout": "Logout", "login": "<PERSON><PERSON>", "register": "Register", "dashboard": "Dashboard", "welcome": "Welcome", "error": "Error", "success": "Success", "warning": "Warning", "info": "Information"}, "navigation": {"main": "Main Navigation", "dashboard": "Dashboard", "contacts": "Contacts", "companies": "Companies", "leads": "Leads", "opportunities": "Opportunities", "proposals": "Proposals", "projects": "Projects", "activities": "Activities", "reports": "Reports", "settings": "Settings", "admin": "Administration", "users": "Users", "teams": "Teams", "roles": "Roles & Permissions", "automation": "Process Automation", "billing": "Billing", "admin_settings": "<PERSON><PERSON>s"}, "auth": {"signIn": "Sign In", "signOut": "Sign Out", "signUp": "Sign Up", "email": "Email Address", "password": "Password", "confirmPassword": "Confirm Password", "forgotPassword": "Forgot Password?", "resetPassword": "Reset Password", "rememberMe": "Remember me", "createAccount": "Create Account", "alreadyHaveAccount": "Already have an account?", "dontHaveAccount": "Don't have an account?", "invalidCredentials": "Invalid email or password", "accountCreated": "Account created successfully", "passwordReset": "Password reset email sent", "emailVerification": "Please verify your email address"}, "contacts": {"title": "Contacts", "addContact": "Add Contact", "editContact": "Edit Contact", "deleteContact": "Delete Contact", "firstName": "First Name", "lastName": "Last Name", "company": "Company", "position": "Position", "contactCreated": "Contact created successfully", "contactUpdated": "Contact updated successfully", "contactDeleted": "Contact deleted successfully", "noContacts": "No contacts found", "searchContacts": "Search contacts..."}, "companies": {"title": "Companies", "addCompany": "Add Company", "editCompany": "Edit Company", "deleteCompany": "Delete Company", "companyName": "Company Name", "website": "Website", "industry": "Industry", "size": "Company Size", "companyCreated": "Company created successfully", "companyUpdated": "Company updated successfully", "companyDeleted": "Company deleted successfully", "noCompanies": "No companies found", "searchCompanies": "Search companies..."}, "leads": {"title": "Leads", "addLead": "Add Lead", "editLead": "Edit Lead", "deleteLead": "Delete Lead", "leadTitle": "Lead Title", "source": "Source", "score": "Score", "leadCreated": "Lead created successfully", "leadUpdated": "Lead updated successfully", "leadDeleted": "Lead deleted successfully", "noLeads": "No leads found", "searchLeads": "Search leads...", "convertToOpportunity": "Convert to Opportunity", "contact": "Contact", "company": "Company", "status": "Status", "priority": "Priority", "value": "Value", "owner": "Owner", "expectedCloseDate": "Expected Close Date", "description": "Description", "tags": "Tags", "customFields": "Custom Fields", "leadNotFound": "Lead not found", "leadNotFoundDescription": "The lead you're looking for doesn't exist or you don't have permission to view it.", "backToLeads": "Back to Leads", "createNewLead": "Create New Lead", "updateLead": "Update Lead", "saving": "Saving...", "createLead": "Create Lead", "failedToLoadLeads": "Failed to load leads", "failedToDeleteLead": "Failed to delete lead", "leadsDeletedSuccessfully": "Leads deleted successfully", "failedToDeleteLeads": "Failed to delete leads", "deleteSelected": "Delete Selected", "importCSV": "Import CSV", "view": "View", "edit": "Edit", "delete": "Delete", "all": "All", "new": "New", "contacted": "Contacted", "qualified": "Qualified", "proposal": "Proposal", "negotiation": "Negotiation", "closedWon": "Won", "closedLost": "Lost", "low": "Low", "medium": "Medium", "high": "High", "urgent": "<PERSON><PERSON>", "none": "None", "titleRequired": "Title is required", "titleTooLong": "Title too long", "firstNameRequired": "First name is required", "lastNameRequired": "Last name is required", "validEmailRequired": "Valid email is required", "failedToSaveLead": "Failed to save lead", "failedToLoadLead": "Failed to load lead", "getStartedDescription": "Get started by creating your first lead or importing from a CSV file.", "capture": {"title": "Get in Touch", "description": "Fill out the form below and we'll get back to you as soon as possible.", "firstName": "First Name", "lastName": "Last Name", "email": "Email", "phone": "Phone", "company": "Company", "jobTitle": "Job Title", "website": "Website", "interest": "Area of Interest", "message": "Message", "budget": "Budget Range", "timeline": "Timeline", "submit": "Submit Information", "submitting": "Submitting...", "thankYou": "Thank you! Your information has been submitted successfully.", "failedToSubmit": "Failed to submit your information. Please try again.", "selectInterest": "Select your area of interest", "selectBudget": "Select your budget range", "selectTimeline": "Select your timeline", "interests": {"sales": "Sales & CRM", "marketing": "Marketing Automation", "support": "Customer Support", "analytics": "Analytics & Reporting", "integration": "System Integration", "other": "Other"}, "budgets": {"under10k": "Under $10,000", "10k-50k": "$10,000 - $50,000", "50k-100k": "$50,000 - $100,000", "over100k": "Over $100,000"}, "timelines": {"immediate": "Immediate (within 1 month)", "quarter": "This quarter (1-3 months)", "halfYear": "This half year (3-6 months)", "year": "This year (6-12 months)", "future": "Future planning (12+ months)"}}}, "ai": {"leadScoring": {"title": "AI Lead Scoring", "description": "Analyze lead quality and potential using artificial intelligence", "includeQualification": "Include qualification analysis", "analyze": "Analyze", "analyzing": "Analyzing...", "scoreUpdated": "Lead score updated successfully", "analysisComplete": "AI analysis completed", "error": "Failed to analyze lead", "noData": "Insufficient data for analysis", "confidence": "Confidence Level", "recommendations": "AI Recommendations", "factors": "Scoring Factors", "overallScore": "Overall Score", "demographic": "Demographic", "behavioral": "Behavioral", "engagement": "Engagement", "intent": "Intent", "fit": "Fit", "reasoning": "AI Reasoning", "nextActions": "Next Actions", "bantQualification": "BANT Qualification", "budget": "Budget", "authority": "Authority", "need": "Need", "timeline": "Timeline", "qualificationReasoning": "Qualification Analysis", "suggestedQuestions": "Suggested Questions", "qualification": {"title": "Lead Qualification", "budget": "Budget Assessment", "authority": "Decision Authority", "need": "Need Analysis", "timeline": "Timeline Evaluation"}}}, "opportunities": {"title": "Opportunities", "addOpportunity": "Add Opportunity", "editOpportunity": "Edit Opportunity", "deleteOpportunity": "Delete Opportunity", "opportunityTitle": "Opportunity Title", "value": "Value", "stage": "Stage", "probability": "Probability", "expectedCloseDate": "Expected Close Date", "opportunityCreated": "Opportunity created successfully", "opportunityUpdated": "Opportunity updated successfully", "opportunityDeleted": "Opportunity deleted successfully", "noOpportunities": "No opportunities found", "searchOpportunities": "Search opportunities..."}, "errors": {"generic": "An error occurred. Please try again.", "network": "Network error. Please check your connection.", "unauthorized": "You are not authorized to perform this action.", "notFound": "The requested resource was not found.", "validation": "Please check your input and try again.", "server": "Server error. Please try again later."}, "validation": {"required": "This field is required", "email": "Please enter a valid email address", "minLength": "Must be at least {min} characters", "maxLength": "Must be no more than {max} characters", "numeric": "Must be a number", "positive": "Must be a positive number"}, "theme": {"light": "Light Mode", "dark": "Dark Mode", "system": "System", "toggle": "Toggle theme"}, "sidebar": {"expand": "Expand sidebar", "collapse": "Collapse sidebar"}, "ui": {"loading": "Loading...", "retry": "Retry", "refresh": "Refresh", "minimize": "Minimize", "maximize": "Maximize", "fullscreen": "Fullscreen", "exitFullscreen": "Exit fullscreen"}}