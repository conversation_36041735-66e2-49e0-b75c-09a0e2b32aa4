import { Permission, Role } from '@/types';
import CryptoJS from 'crypto-js';

export interface CachedPermissionData {
  userId: string;
  tenantId: string;
  permissions: Permission[];
  roles: Role[];
  effectivePermissions: Permission[];
  lastUpdated: string;
  cacheVersion: number;
  checksum: string;
}

export interface PermissionCacheOptions {
  ttl?: number; // Time to live in milliseconds
  encrypt?: boolean;
  validateIntegrity?: boolean;
}

export class PermissionCacheService {
  private static readonly CACHE_KEY_PREFIX = 'perm_cache_';
  private static readonly CACHE_VERSION = 1;
  private static readonly DEFAULT_TTL = 60 * 60 * 1000; // 1 hour in milliseconds
  private static readonly ENCRYPTION_KEY = process.env.NEXT_PUBLIC_CACHE_ENCRYPTION_KEY || 'default-key-change-in-production';

  /**
   * Store permissions in cache with encryption and integrity validation
   */
  static async setPermissions(
    userId: string,
    tenantId: string,
    data: {
      permissions: Permission[];
      roles: Role[];
      effectivePermissions: Permission[];
      lastUpdated: string;
    },
    options: PermissionCacheOptions = {}
  ): Promise<boolean> {
    try {
      const {
        ttl = this.DEFAULT_TTL,
        encrypt = true,
        validateIntegrity = true
      } = options;

      const cacheData: CachedPermissionData = {
        userId,
        tenantId,
        ...data,
        cacheVersion: this.CACHE_VERSION,
        checksum: validateIntegrity ? this.generateChecksum(data) : ''
      };

      const serializedData = JSON.stringify(cacheData);
      const dataToStore = encrypt ? this.encrypt(serializedData) : serializedData;

      const cacheKey = this.getCacheKey(userId, tenantId);
      const expiryTime = Date.now() + ttl;

      // Store in localStorage with expiry
      const cacheEntry = {
        data: dataToStore,
        expiry: expiryTime,
        encrypted: encrypt
      };

      localStorage.setItem(cacheKey, JSON.stringify(cacheEntry));

      // Also store a backup in sessionStorage for tab-specific caching
      sessionStorage.setItem(cacheKey, JSON.stringify(cacheEntry));

      return true;
    } catch (error) {
      console.error('Failed to cache permissions:', error);
      return false;
    }
  }

  /**
   * Retrieve permissions from cache with validation
   */
  static async getPermissions(
    userId: string,
    tenantId: string,
    options: PermissionCacheOptions = {}
  ): Promise<CachedPermissionData | null> {
    try {
      const { validateIntegrity = true } = options;
      const cacheKey = this.getCacheKey(userId, tenantId);

      // Try localStorage first, then sessionStorage
      let cacheEntryStr = localStorage.getItem(cacheKey) || sessionStorage.getItem(cacheKey);
      
      if (!cacheEntryStr) {
        return null;
      }

      const cacheEntry = JSON.parse(cacheEntryStr);

      // Check if cache has expired
      if (Date.now() > cacheEntry.expiry) {
        this.invalidateCache(userId, tenantId);
        return null;
      }

      // Decrypt if necessary
      const serializedData = cacheEntry.encrypted 
        ? this.decrypt(cacheEntry.data)
        : cacheEntry.data;

      const cachedData: CachedPermissionData = JSON.parse(serializedData);

      // Validate cache version
      if (cachedData.cacheVersion !== this.CACHE_VERSION) {
        this.invalidateCache(userId, tenantId);
        return null;
      }

      // Validate integrity if enabled
      if (validateIntegrity && cachedData.checksum) {
        const expectedChecksum = this.generateChecksum({
          permissions: cachedData.permissions,
          roles: cachedData.roles,
          effectivePermissions: cachedData.effectivePermissions,
          lastUpdated: cachedData.lastUpdated
        });

        if (expectedChecksum !== cachedData.checksum) {
          console.warn('Permission cache integrity check failed');
          this.invalidateCache(userId, tenantId);
          return null;
        }
      }

      return cachedData;
    } catch (error) {
      console.error('Failed to retrieve cached permissions:', error);
      this.invalidateCache(userId, tenantId);
      return null;
    }
  }

  /**
   * Invalidate cached permissions for a user/tenant
   */
  static invalidateCache(userId: string, tenantId: string): void {
    const cacheKey = this.getCacheKey(userId, tenantId);
    localStorage.removeItem(cacheKey);
    sessionStorage.removeItem(cacheKey);
  }

  /**
   * Clear all permission caches
   */
  static clearAllCaches(): void {
    const keys = Object.keys(localStorage);
    keys.forEach(key => {
      if (key.startsWith(this.CACHE_KEY_PREFIX)) {
        localStorage.removeItem(key);
      }
    });

    const sessionKeys = Object.keys(sessionStorage);
    sessionKeys.forEach(key => {
      if (key.startsWith(this.CACHE_KEY_PREFIX)) {
        sessionStorage.removeItem(key);
      }
    });
  }

  /**
   * Check if permissions are cached and valid
   */
  static isCached(userId: string, tenantId: string): boolean {
    const cacheKey = this.getCacheKey(userId, tenantId);
    const cacheEntryStr = localStorage.getItem(cacheKey) || sessionStorage.getItem(cacheKey);
    
    if (!cacheEntryStr) {
      return false;
    }

    try {
      const cacheEntry = JSON.parse(cacheEntryStr);
      return Date.now() <= cacheEntry.expiry;
    } catch {
      return false;
    }
  }

  /**
   * Get cache statistics
   */
  static getCacheStats(): {
    totalCaches: number;
    validCaches: number;
    expiredCaches: number;
    totalSize: number;
  } {
    const keys = Object.keys(localStorage);
    const permissionKeys = keys.filter(key => key.startsWith(this.CACHE_KEY_PREFIX));
    
    let validCaches = 0;
    let expiredCaches = 0;
    let totalSize = 0;

    permissionKeys.forEach(key => {
      const value = localStorage.getItem(key);
      if (value) {
        totalSize += value.length;
        try {
          const cacheEntry = JSON.parse(value);
          if (Date.now() <= cacheEntry.expiry) {
            validCaches++;
          } else {
            expiredCaches++;
          }
        } catch {
          expiredCaches++;
        }
      }
    });

    return {
      totalCaches: permissionKeys.length,
      validCaches,
      expiredCaches,
      totalSize
    };
  }

  /**
   * Generate cache key for user/tenant combination
   */
  private static getCacheKey(userId: string, tenantId: string): string {
    return `${this.CACHE_KEY_PREFIX}${userId}_${tenantId}`;
  }

  /**
   * Generate checksum for data integrity validation
   */
  private static generateChecksum(data: any): string {
    const dataString = JSON.stringify(data, Object.keys(data).sort());
    return CryptoJS.SHA256(dataString).toString();
  }

  /**
   * Encrypt sensitive cache data
   */
  private static encrypt(data: string): string {
    return CryptoJS.AES.encrypt(data, this.ENCRYPTION_KEY).toString();
  }

  /**
   * Decrypt cache data
   */
  private static decrypt(encryptedData: string): string {
    const bytes = CryptoJS.AES.decrypt(encryptedData, this.ENCRYPTION_KEY);
    return bytes.toString(CryptoJS.enc.Utf8);
  }

  /**
   * Clean up expired caches
   */
  static cleanupExpiredCaches(): number {
    const keys = Object.keys(localStorage);
    const permissionKeys = keys.filter(key => key.startsWith(this.CACHE_KEY_PREFIX));
    let cleanedCount = 0;

    permissionKeys.forEach(key => {
      const value = localStorage.getItem(key);
      if (value) {
        try {
          const cacheEntry = JSON.parse(value);
          if (Date.now() > cacheEntry.expiry) {
            localStorage.removeItem(key);
            cleanedCount++;
          }
        } catch {
          localStorage.removeItem(key);
          cleanedCount++;
        }
      }
    });

    return cleanedCount;
  }
}
