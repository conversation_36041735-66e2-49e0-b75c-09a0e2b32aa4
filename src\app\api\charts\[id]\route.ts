import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { hasPermission } from '@/lib/permissions';
import { PermissionResource, PermissionAction } from '@/types';
import { prisma } from '@/lib/prisma';
import path from 'path';
import fs from 'fs/promises';

/**
 * DELETE /api/charts/[id]
 * Delete a specific chart
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user's current tenant
    const currentTenant = session.user.tenants?.[0];
    if (!currentTenant) {
      return NextResponse.json({ error: 'No tenant access' }, { status: 403 });
    }

    // Check permission for proposal management (charts are part of proposals)
    const hasAccess = await hasPermission(
      PermissionResource.PROPOSALS,
      PermissionAction.DELETE,
      currentTenant.id
    );

    if (!hasAccess) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const chartId = params.id;

    // Verify chart exists and belongs to the tenant
    const chart = await prisma.proposalChart.findFirst({
      where: {
        id: chartId,
        tenantId: currentTenant.id
      },
      include: {
        proposal: {
          select: {
            id: true,
            title: true
          }
        }
      }
    });

    if (!chart) {
      return NextResponse.json(
        { error: 'Chart not found or access denied' },
        { status: 404 }
      );
    }

    // Clean up rendered image file if it exists
    if (chart.renderedImagePath) {
      try {
        const uploadsDir = path.join(process.cwd(), 'uploads');
        const imagePath = path.join(uploadsDir, chart.renderedImagePath);
        await fs.unlink(imagePath);
      } catch (error) {
        // File might already be deleted or not exist, continue with chart deletion
        console.warn(`Could not delete image file for chart ${chartId}:`, error);
      }
    }

    // Delete the chart
    await prisma.proposalChart.delete({
      where: { id: chartId }
    });

    return NextResponse.json({
      success: true,
      message: 'Chart deleted successfully',
      data: {
        chartId,
        chartTitle: chart.title,
        proposalId: chart.proposal.id,
        proposalTitle: chart.proposal.title
      }
    });

  } catch (error) {
    console.error('Error deleting chart:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
