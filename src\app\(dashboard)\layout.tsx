'use client';

import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { useEffect, useMemo } from 'react';
import { DashboardLayout } from '@/components/layout/dashboard-layout';
import { PermissionProvider } from '@/components/providers/permission-provider';


interface DashboardGroupLayoutProps {
  children: React.ReactNode;
}

export default function DashboardGroupLayout({ children }: DashboardGroupLayoutProps) {
  const { data: session, status } = useSession();
  const router = useRouter();

  // Memoize session checks to prevent unnecessary re-renders
  const sessionState = useMemo(() => {
    if (status === 'loading') return 'loading';
    if (!session) return 'unauthenticated';
    if (session.user.isPlatformAdmin) return 'admin';
    return 'authenticated';
  }, [session, status]);

  useEffect(() => {
    if (sessionState === 'loading') return;

    if (sessionState === 'unauthenticated') {
      router.replace('/auth/signin');
      return;
    }

    if (sessionState === 'admin') {
      router.replace('/admin/dashboard');
      return;
    }
  }, [sessionState, router]);

  if (sessionState === 'loading') {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  if (sessionState !== 'authenticated') {
    return null;
  }

  return (
    <PermissionProvider>
      <DashboardLayout>
        {children}
      </DashboardLayout>

    </PermissionProvider>
  );
}
