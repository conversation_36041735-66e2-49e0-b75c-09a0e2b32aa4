import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Seeding database with basic data...');

  // Create a test tenant admin user
  const hashedPassword = await bcrypt.hash('admin123', 12);

  // Create or update user
  const user = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      firstName: 'Admin',
      lastName: 'User',
      passwordHash: hashedPassword,
      emailVerified: true,
      isPlatformAdmin: false,
      locale: 'en'
    }
  });

  console.log('✅ Created user:', user.email);

  // Create or update tenant
  const tenant = await prisma.tenant.upsert({
    where: { slug: 'test-company' },
    update: {},
    create: {
      name: 'Test Company',
      slug: 'test-company',
      domain: 'test-company.com',
      isActive: true,
      settings: {},
      metadata: {}
    }
  });

  console.log('✅ Created tenant:', tenant.name);

  // Create tenant-user relationship
  const tenantUser = await prisma.tenantUser.upsert({
    where: {
      tenantId_userId: {
        tenantId: tenant.id,
        userId: user.id
      }
    },
    update: {
      isTenantAdmin: true,
      status: 'active',
      role: 'admin'
    },
    create: {
      tenantId: tenant.id,
      userId: user.id,
      isTenantAdmin: true,
      status: 'active',
      role: 'admin'
    }
  });

  console.log('✅ Created tenant-user relationship');

  // Create basic roles
  const adminRole = await prisma.role.upsert({
    where: {
      tenantId_name: {
        tenantId: tenant.id,
        name: 'Admin'
      }
    },
    update: {},
    create: {
      tenantId: tenant.id,
      name: 'Admin',
      description: 'Full administrative access',
      isSystemRole: true,
      createdById: user.id
    }
  });

  const userRole = await prisma.role.upsert({
    where: {
      tenantId_name: {
        tenantId: tenant.id,
        name: 'User'
      }
    },
    update: {},
    create: {
      tenantId: tenant.id,
      name: 'User',
      description: 'Basic user access',
      isSystemRole: true,
      createdById: user.id
    }
  });

  console.log('✅ Created basic roles');

  // Create basic permissions
  const permissions = [
    { resource: 'USERS', action: 'READ' },
    { resource: 'USERS', action: 'CREATE' },
    { resource: 'USERS', action: 'UPDATE' },
    { resource: 'USERS', action: 'DELETE' },
    { resource: 'USERS', action: 'MANAGE' },
    { resource: 'ROLES', action: 'READ' },
    { resource: 'ROLES', action: 'CREATE' },
    { resource: 'ROLES', action: 'UPDATE' },
    { resource: 'ROLES', action: 'DELETE' },
    { resource: 'ROLES', action: 'MANAGE' },
    { resource: 'CONTACTS', action: 'READ' },
    { resource: 'CONTACTS', action: 'CREATE' },
    { resource: 'CONTACTS', action: 'UPDATE' },
    { resource: 'CONTACTS', action: 'DELETE' },
    { resource: 'LEADS', action: 'READ' },
    { resource: 'LEADS', action: 'CREATE' },
    { resource: 'LEADS', action: 'UPDATE' },
    { resource: 'LEADS', action: 'DELETE' }
  ];

  for (const perm of permissions) {
    await prisma.permission.upsert({
      where: {
        tenantId_resource_action: {
          tenantId: tenant.id,
          resource: perm.resource,
          action: perm.action
        }
      },
      update: {},
      create: {
        tenantId: tenant.id,
        resource: perm.resource,
        action: perm.action,
        description: `${perm.action} access to ${perm.resource}`
      }
    });
  }

  console.log('✅ Created basic permissions');

  // Assign all permissions to admin role
  const allPermissions = await prisma.permission.findMany({
    where: { tenantId: tenant.id }
  });

  for (const permission of allPermissions) {
    await prisma.rolePermission.upsert({
      where: {
        roleId_permissionId: {
          roleId: adminRole.id,
          permissionId: permission.id
        }
      },
      update: {},
      create: {
        roleId: adminRole.id,
        permissionId: permission.id,
        grantedById: user.id
      }
    });
  }

  console.log('✅ Assigned permissions to admin role');

  // Assign admin role to user
  await prisma.userRole.upsert({
    where: {
      userId_roleId: {
        userId: user.id,
        roleId: adminRole.id
      }
    },
    update: {},
    create: {
      userId: user.id,
      roleId: adminRole.id,
      assignedById: user.id
    }
  });

  console.log('✅ Assigned admin role to user');

  console.log('🎉 Seeding completed successfully!');
  console.log('📧 Login with: <EMAIL>');
  console.log('🔑 Password: admin123');
}

main()
  .catch((e) => {
    console.error('❌ Seeding failed:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
