'use client';

import { 
  UsersIcon, 
  BuildingOfficeIcon, 
  ChartBarIcon, 
  CurrencyDollarIcon,
  ArrowUpIcon,
  ArrowDownIcon
} from '@heroicons/react/24/outline';
import { cn, formatCurrency, formatNumber } from '@/lib/utils';

interface StatCardProps {
  title: string;
  value: string | number;
  change: {
    value: number;
    type: 'increase' | 'decrease';
    period: string;
  };
  icon: React.ComponentType<{ className?: string }>;
  color: 'blue' | 'green' | 'purple' | 'orange';
}

function StatCard({ title, value, change, icon: Icon, color }: StatCardProps) {
  const colorClasses = {
    blue: {
      bg: 'bg-chart-2/10',
      icon: 'text-chart-2',
      text: 'text-chart-2',
    },
    green: {
      bg: 'bg-chart-1/10',
      icon: 'text-chart-1',
      text: 'text-chart-1',
    },
    purple: {
      bg: 'bg-chart-4/10',
      icon: 'text-chart-4',
      text: 'text-chart-4',
    },
    orange: {
      bg: 'bg-chart-3/10',
      icon: 'text-chart-3',
      text: 'text-chart-3',
    },
  };

  const colors = colorClasses[color];

  return (
    <div className="bg-card overflow-hidden shadow-sm rounded-lg border border-border hover:shadow-md transition-all duration-200">
      <div className="p-6">
        <div className="flex items-center">
          <div className="flex-shrink-0">
            <div className={cn('p-3 rounded-lg', colors.bg)}>
              <Icon className={cn('h-6 w-6', colors.icon)} aria-hidden="true" />
            </div>
          </div>
          <div className="ml-5 w-0 flex-1">
            <dl>
              <dt className="text-sm font-medium text-muted-foreground truncate">
                {title}
              </dt>
              <dd className="flex items-baseline">
                <div className="text-2xl font-semibold text-card-foreground">
                  {typeof value === 'number' ? formatNumber(value) : value}
                </div>
                <div className={cn(
                  'ml-2 flex items-baseline text-sm font-semibold',
                  change.type === 'increase'
                    ? 'text-green-600'
                    : 'text-red-600'
                )}>
                  {change.type === 'increase' ? (
                    <ArrowUpIcon className="self-center flex-shrink-0 h-4 w-4" aria-hidden="true" />
                  ) : (
                    <ArrowDownIcon className="self-center flex-shrink-0 h-4 w-4" aria-hidden="true" />
                  )}
                  <span className="sr-only">
                    {change.type === 'increase' ? 'Increased' : 'Decreased'} by
                  </span>
                  {Math.abs(change.value)}%
                </div>
              </dd>
            </dl>
          </div>
        </div>
      </div>
      <div className="bg-muted px-6 py-3">
        <div className="text-sm">
          <span className="font-medium text-muted-foreground">
            {change.period}
          </span>
        </div>
      </div>
    </div>
  );
}

export function DashboardStats() {
  // Mock data - in a real app, this would come from an API
  const stats = [
    {
      title: 'Total Contacts',
      value: 1247,
      change: {
        value: 12.5,
        type: 'increase' as const,
        period: 'from last month',
      },
      icon: UsersIcon,
      color: 'green' as const, // Green for contacts (chart-1)
    },
    {
      title: 'Active Leads',
      value: 89,
      change: {
        value: 8.2,
        type: 'increase' as const,
        period: 'from last week',
      },
      icon: ChartBarIcon,
      color: 'blue' as const, // Blue for leads (chart-2)
    },
    {
      title: 'Opportunities',
      value: 156,
      change: {
        value: 3.1,
        type: 'decrease' as const,
        period: 'from last month',
      },
      icon: BuildingOfficeIcon,
      color: 'orange' as const, // Orange for opportunities (chart-3)
    },
    {
      title: 'Revenue',
      value: formatCurrency(24500),
      change: {
        value: 15.6,
        type: 'increase' as const,
        period: 'from last month',
      },
      icon: CurrencyDollarIcon,
      color: 'purple' as const, // Purple for revenue (chart-4)
    },
  ];

  return (
    <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
      {stats.map((stat, index) => (
        <StatCard
          key={index}
          title={stat.title}
          value={stat.value}
          change={stat.change}
          icon={stat.icon}
          color={stat.color}
        />
      ))}
    </div>
  );
}
