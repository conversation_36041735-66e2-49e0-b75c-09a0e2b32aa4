import { NextResponse } from 'next/server';
import { withPermission } from '@/lib/permission-middleware';
import { smtpService, emailDataSchema, EmailData } from '@/services/smtp-service';
import { PermissionAction, PermissionResource } from '@/types';
import { AuthenticatedRequest } from '@/lib/permission-middleware';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';

// Extended email data schema with tracking information
const sendEmailSchema = emailDataSchema.extend({
  leadId: z.string().min(1).optional(), // Accept CUID format
  contactId: z.string().min(1).optional(), // Accept CUID format
  campaignId: z.string().min(1).optional(), // Accept CUID format
  templateId: z.string().min(1).optional(), // Accept CUID format
  trackOpens: z.boolean().default(true),
  trackClicks: z.boolean().default(true),
  tags: z.array(z.string()).optional(),
  metadata: z.record(z.any()).optional()
});

type SendEmailData = z.infer<typeof sendEmailSchema>;

async function postHandler(req: AuthenticatedRequest) {
  try {
    const body = await req.json();

    // Log the incoming request for debugging
    console.log('Email send request body:', JSON.stringify(body, null, 2));

    // Validate request body
    const validationResult = sendEmailSchema.safeParse(body);
    if (!validationResult.success) {
      console.error('Email validation failed:', validationResult.error.errors);
      return NextResponse.json(
        {
          error: 'Validation failed',
          details: validationResult.error.errors,
          receivedData: body
        },
        { status: 400 }
      );
    }

    const emailData: SendEmailData = validationResult.data;

    // Verify lead/contact belongs to user's tenant if provided
    if (emailData.leadId) {
      const lead = await prisma.lead.findUnique({
        where: { id: emailData.leadId, tenantId: req.tenantId }
      });
      if (!lead) {
        return NextResponse.json(
          { error: 'Lead not found or access denied' },
          { status: 403 }
        );
      }
    }

    if (emailData.contactId) {
      const contact = await prisma.contact.findUnique({
        where: { id: emailData.contactId, tenantId: req.tenantId }
      });
      if (!contact) {
        return NextResponse.json(
          { error: 'Contact not found or access denied' },
          { status: 403 }
        );
      }
    }

    // Check if SMTP service is ready
    if (!smtpService.isReady()) {
      const config = smtpService.getConfig();
      const connectionTest = await smtpService.testConnection();

      console.error('❌ SMTP service not ready:', {
        isEnabled: process.env.ENABLE_EMAIL_SENDING,
        config,
        connectionTest
      });

      return NextResponse.json(
        {
          error: 'Email service not available',
          details: 'SMTP service is not configured or disabled',
          debug: {
            enabled: process.env.ENABLE_EMAIL_SENDING === 'true',
            host: config.host,
            port: config.port,
            fromEmail: config.from.email,
            connectionTest
          }
        },
        { status: 503 }
      );
    }

    // Add tracking pixels and click tracking if enabled
    let processedHtml = emailData.html;
    if (processedHtml && (emailData.trackOpens || emailData.trackClicks)) {
      processedHtml = await addEmailTracking(
        processedHtml,
        emailData,
        req.tenantId,
        req.userId
      );
    }

    // Prepare email for sending
    const emailToSend: EmailData = {
      to: emailData.to,
      cc: emailData.cc,
      bcc: emailData.bcc,
      subject: emailData.subject,
      text: emailData.text,
      html: processedHtml,
      attachments: emailData.attachments,
      replyTo: emailData.replyTo,
      priority: emailData.priority,
      headers: emailData.headers
    };

    // Send email
    const result = await smtpService.sendEmail(emailToSend);

    if (!result.success) {
      return NextResponse.json(
        { 
          error: 'Failed to send email',
          details: result.error
        },
        { status: 500 }
      );
    }

    // Log email activity (using raw query until Prisma client is regenerated)
    const emailLogId = `email_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

    try {
      console.log('📧 Logging email activity to database...');
      console.log('📧 Email log data:', {
        emailLogId,
        tenantId: req.tenantId,
        userId: req.userId,
        leadId: emailData.leadId,
        contactId: emailData.contactId,
        subject: emailData.subject,
        recipients: result.recipients,
        provider: result.provider,
        timestamp: result.timestamp
      });

      const insertResult = await prisma.$executeRaw`
        INSERT INTO email_logs (
          id, tenant_id, sent_by, lead_id, contact_id, campaign_id, template_id,
          recipients, subject, content, message_id, provider, status, sent_at,
          track_opens, track_clicks, tags, metadata, created_at
        ) VALUES (
          ${emailLogId}, ${req.tenantId}, ${req.userId}, ${emailData.leadId || null},
          ${emailData.contactId || null}, ${emailData.campaignId || null}, ${emailData.templateId || null},
          ${JSON.stringify(result.recipients)}::jsonb, ${emailData.subject}, ${emailData.html || emailData.text || ''},
          ${result.messageId || null}, ${result.provider}, 'sent', ${result.timestamp},
          ${emailData.trackOpens}, ${emailData.trackClicks}, ${JSON.stringify(emailData.tags || [])}::jsonb,
          ${JSON.stringify(emailData.metadata || {})}::jsonb, NOW()
        )
      `;

      console.log('✅ Email activity logged successfully with ID:', emailLogId);
      console.log('📊 Insert result:', insertResult);

      // Verify the record was created
      const verifyResult = await prisma.$queryRaw`
        SELECT id, subject, status FROM email_logs WHERE id = ${emailLogId}
      ` as any[];

      console.log('🔍 Verification result:', verifyResult);

    } catch (error) {
      console.error('❌ Failed to log email activity:', error);
      console.error('❌ Error details:', {
        message: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined
      });
      // Continue execution even if logging fails
    }

    // Update lead/contact last contact date if applicable (using raw queries)
    if (emailData.leadId) {
      try {
        await prisma.$executeRaw`
          UPDATE leads
          SET last_contact_date = NOW(), updated_at = NOW()
          WHERE id = ${emailData.leadId}
        `;
      } catch (error) {
        console.error('Failed to update lead last contact date:', error);
      }
    }

    if (emailData.contactId) {
      try {
        await prisma.$executeRaw`
          UPDATE contacts
          SET last_contact_date = NOW(), updated_at = NOW()
          WHERE id = ${emailData.contactId}
        `;
      } catch (error) {
        console.error('Failed to update contact last contact date:', error);
      }
    }

    return NextResponse.json({
      success: true,
      data: {
        emailLogId,
        messageId: result.messageId,
        recipients: result.recipients,
        sentAt: result.timestamp,
        provider: result.provider
      }
    });

  } catch (error) {
    console.error('Email sending API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

async function addEmailTracking(
  html: string,
  emailData: SendEmailData,
  tenantId: string,
  userId: string
): Promise<string> {
  let processedHtml = html;

  // Add open tracking pixel if enabled
  if (emailData.trackOpens) {
    const trackingId = `${tenantId}-${userId}-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
    const trackingPixel = `<img src="${process.env.NEXTAUTH_URL}/api/email/track/open/${trackingId}" width="1" height="1" style="display:none;" alt="" />`;
    
    // Insert tracking pixel before closing body tag or at the end
    if (processedHtml.includes('</body>')) {
      processedHtml = processedHtml.replace('</body>', `${trackingPixel}</body>`);
    } else {
      processedHtml += trackingPixel;
    }
  }

  // Add click tracking if enabled
  if (emailData.trackClicks) {
    // This would require more complex URL rewriting
    // For now, we'll just mark it as enabled in the database
    // Implementation would involve replacing all links with tracking URLs
  }

  return processedHtml;
}

// GET endpoint for email service status
async function getHandler(_req: AuthenticatedRequest) {
  try {
    const isReady = smtpService.isReady();
    const config = smtpService.getConfig();
    
    let connectionTest = null;
    if (isReady) {
      connectionTest = await smtpService.testConnection();
    }

    return NextResponse.json({
      success: true,
      data: {
        isReady,
        config: {
          host: config.host,
          port: config.port,
          secure: config.secure,
          fromEmail: config.from.email,
          fromName: config.from.name
        },
        connectionTest
      }
    });

  } catch (error) {
    console.error('Email service status API error:', error);
    return NextResponse.json(
      { error: 'Failed to get email service status' },
      { status: 500 }
    );
  }
}

// Export with permission middleware
export const POST = withPermission({
  resource: PermissionResource.CONTACTS,
  action: PermissionAction.CREATE
})(postHandler);

export const GET = withPermission({
  resource: PermissionResource.CONTACTS,
  action: PermissionAction.READ
})(getHandler);
