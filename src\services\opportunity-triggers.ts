import { prisma } from '@/lib/prisma';
import { ProcessAutomationService } from './process-automation';
import { HandoverNotificationService } from './handover-notifications';

export interface OpportunityStatusChange {
  opportunityId: string;
  tenantId: string;
  oldStatus: string;
  newStatus: string;
  changedBy: string;
  changedAt: Date;
  metadata?: Record<string, any>;
}

export interface TriggerRule {
  id: string;
  name: string;
  fromStatus?: string;
  toStatus: string;
  actions: TriggerAction[];
  conditions?: Record<string, any>;
  isActive: boolean;
}

export interface TriggerAction {
  type: 'create_handover' | 'send_notification' | 'create_project' | 'assign_team' | 'update_probability';
  config: Record<string, any>;
  delay?: number; // Delay in minutes
}

export class OpportunityTriggersService {
  private automationService: ProcessAutomationService;
  private notificationService: HandoverNotificationService;

  constructor() {
    this.automationService = new ProcessAutomationService();
    this.notificationService = new HandoverNotificationService();
  }

  /**
   * Handle opportunity status change and trigger automation
   */
  async handleOpportunityStatusChange(change: OpportunityStatusChange): Promise<void> {
    try {
      console.log(`🔄 Processing opportunity status change: ${change.opportunityId} from ${change.oldStatus} to ${change.newStatus}`);

      // Execute automation triggers
      await this.automationService.executeOpportunityStatusTrigger(
        change.tenantId,
        change.opportunityId,
        change.oldStatus,
        change.newStatus,
        change.changedBy
      );

      // Handle specific status changes
      await this.handleSpecificStatusChanges(change);

      // Log the trigger execution
      await this.logTriggerExecution(change);

    } catch (error) {
      console.error('Error handling opportunity status change:', error);
      await this.logTriggerError(change, error);
      throw error;
    }
  }

  /**
   * Handle specific status changes with built-in logic
   */
  private async handleSpecificStatusChanges(change: OpportunityStatusChange): Promise<void> {
    switch (change.newStatus) {
      case 'Closed Won':
        await this.handleClosedWonOpportunity(change);
        break;
      
      case 'Closed Lost':
        await this.handleClosedLostOpportunity(change);
        break;
      
      case 'Proposal':
        await this.handleProposalStage(change);
        break;
      
      case 'Negotiation':
        await this.handleNegotiationStage(change);
        break;
      
      default:
        // Handle general stage progression
        await this.handleGeneralStageProgression(change);
        break;
    }
  }

  /**
   * Handle closed won opportunity
   */
  private async handleClosedWonOpportunity(change: OpportunityStatusChange): Promise<void> {
    console.log(`🎉 Opportunity ${change.opportunityId} closed won - triggering handover creation`);

    // Get opportunity details
    const opportunity = await prisma.opportunity.findUnique({
      where: { id: change.opportunityId, tenantId: change.tenantId },
      include: {
        contact: true,
        company: true,
        owner: true
      }
    });

    if (!opportunity) {
      throw new Error('Opportunity not found');
    }

    // Check if handover already exists
    const existingHandover = await prisma.projectHandover.findFirst({
      where: {
        opportunityId: change.opportunityId,
        tenantId: change.tenantId
      }
    });

    if (existingHandover) {
      console.log(`Handover already exists for opportunity ${change.opportunityId}`);
      return;
    }

    // Get default handover template for tenant
    const defaultTemplate = await prisma.handoverTemplate.findFirst({
      where: {
        tenantId: change.tenantId,
        isDefault: true,
        isActive: true
      }
    });

    // Get default delivery manager (could be configured per tenant)
    const deliveryManager = await this.getDefaultDeliveryManager(change.tenantId);

    // Create handover data
    const handoverData = {
      opportunityId: change.opportunityId,
      templateId: defaultTemplate?.id,
      title: `Sales Handover: ${opportunity.title}`,
      description: `Automated handover created for closed won opportunity. Client: ${
        opportunity.company?.name || `${opportunity.contact?.firstName} ${opportunity.contact?.lastName}`
      }`,
      priority: this.determinePriority(opportunity),
      salesRepId: opportunity.ownerId,
      deliveryManagerId: deliveryManager?.id,
      assignedTeamIds: await this.getDefaultTeamIds(change.tenantId),
      scheduledDate: new Date(Date.now() + 24 * 60 * 60 * 1000), // Schedule for tomorrow
      estimatedDuration: 120 // 2 hours default
    };

    // Create the handover
    const handover = await prisma.projectHandover.create({
      data: {
        tenantId: change.tenantId,
        ...handoverData,
        createdById: 'system'
      }
    });

    console.log(`✅ Handover created: ${handover.id} for opportunity ${change.opportunityId}`);

    // Send notifications
    await this.sendHandoverCreatedNotifications(change.tenantId, handover.id, opportunity);

    // Create initial project structure if configured
    await this.createInitialProjectStructure(change.tenantId, change.opportunityId, handover.id);
  }

  /**
   * Handle closed lost opportunity
   */
  private async handleClosedLostOpportunity(change: OpportunityStatusChange): Promise<void> {
    console.log(`❌ Opportunity ${change.opportunityId} closed lost - triggering follow-up actions`);

    // Send notification to sales rep for follow-up
    await this.sendClosedLostNotification(change);

    // Schedule follow-up reminder (for future opportunities)
    await this.scheduleFollowUpReminder(change);
  }

  /**
   * Handle proposal stage
   */
  private async handleProposalStage(change: OpportunityStatusChange): Promise<void> {
    console.log(`📄 Opportunity ${change.opportunityId} moved to proposal stage`);

    // Notify relevant team members
    await this.sendProposalStageNotification(change);

    // Set reminder for proposal follow-up
    await this.setProposalFollowUpReminder(change);
  }

  /**
   * Handle negotiation stage
   */
  private async handleNegotiationStage(change: OpportunityStatusChange): Promise<void> {
    console.log(`🤝 Opportunity ${change.opportunityId} moved to negotiation stage`);

    // Notify sales manager
    await this.sendNegotiationStageNotification(change);

    // Update probability if not manually set
    await this.updateNegotiationProbability(change);
  }

  /**
   * Handle general stage progression
   */
  private async handleGeneralStageProgression(change: OpportunityStatusChange): Promise<void> {
    // Log stage progression for analytics
    await prisma.opportunityStageHistory.create({
      data: {
        tenantId: change.tenantId,
        opportunityId: change.opportunityId,
        fromStage: change.oldStatus,
        toStage: change.newStatus,
        changedById: change.changedBy,
        changedAt: change.changedAt,
        notes: 'Automated stage progression tracking'
      }
    });

    // Update opportunity probability based on stage
    await this.updateStageProbability(change);
  }

  /**
   * Get default delivery manager for tenant
   */
  private async getDefaultDeliveryManager(tenantId: string) {
    return await prisma.user.findFirst({
      where: {
        tenantUsers: {
          some: {
            tenantId,
            role: 'delivery_manager'
          }
        }
      }
    });
  }

  /**
   * Get default team IDs for handover
   */
  private async getDefaultTeamIds(tenantId: string): Promise<string[]> {
    const deliveryTeam = await prisma.user.findMany({
      where: {
        tenantUsers: {
          some: {
            tenantId,
            role: { in: ['delivery_manager', 'project_manager', 'developer'] }
          }
        }
      },
      select: { id: true },
      take: 3 // Default to 3 team members
    });

    return deliveryTeam.map(user => user.id);
  }

  /**
   * Determine handover priority based on opportunity
   */
  private determinePriority(opportunity: any): 'low' | 'medium' | 'high' | 'urgent' {
    const value = Number(opportunity.value) || 0;
    
    if (value > 100000) return 'urgent';
    if (value > 50000) return 'high';
    if (value > 10000) return 'medium';
    return 'low';
  }

  /**
   * Send handover created notifications
   */
  private async sendHandoverCreatedNotifications(
    tenantId: string,
    handoverId: string,
    opportunity: any
  ): Promise<void> {
    await this.notificationService.notifyHandoverCreated(
      tenantId,
      handoverId,
      'system'
    );
  }

  /**
   * Create initial project structure
   */
  private async createInitialProjectStructure(
    tenantId: string,
    opportunityId: string,
    handoverId: string
  ): Promise<void> {
    // This could create initial project milestones, tasks, etc.
    console.log(`📋 Creating initial project structure for opportunity ${opportunityId}`);
    
    // Implementation would depend on project management requirements
    // For now, we'll just log the action
  }

  /**
   * Send various notification types
   */
  private async sendClosedLostNotification(change: OpportunityStatusChange): Promise<void> {
    // Implementation for closed lost notifications
  }

  private async sendProposalStageNotification(change: OpportunityStatusChange): Promise<void> {
    // Implementation for proposal stage notifications
  }

  private async sendNegotiationStageNotification(change: OpportunityStatusChange): Promise<void> {
    // Implementation for negotiation stage notifications
  }

  /**
   * Schedule and update various reminders and probabilities
   */
  private async scheduleFollowUpReminder(change: OpportunityStatusChange): Promise<void> {
    // Implementation for follow-up reminders
  }

  private async setProposalFollowUpReminder(change: OpportunityStatusChange): Promise<void> {
    // Implementation for proposal follow-up reminders
  }

  private async updateNegotiationProbability(change: OpportunityStatusChange): Promise<void> {
    // Update probability to 75% for negotiation stage if not manually set
    await prisma.opportunity.update({
      where: { id: change.opportunityId, tenantId: change.tenantId },
      data: { probability: 75 }
    });
  }

  private async updateStageProbability(change: OpportunityStatusChange): Promise<void> {
    // Get stage probability mapping
    const stage = await prisma.salesStage.findFirst({
      where: {
        tenantId: change.tenantId,
        name: change.newStatus
      }
    });

    if (stage) {
      const avgProbability = Math.floor((stage.probabilityMin + stage.probabilityMax) / 2);
      await prisma.opportunity.update({
        where: { id: change.opportunityId, tenantId: change.tenantId },
        data: { probability: avgProbability }
      });
    }
  }

  /**
   * Logging methods
   */
  private async logTriggerExecution(change: OpportunityStatusChange): Promise<void> {
    await prisma.auditLog.create({
      data: {
        tenantId: change.tenantId,
        userId: change.changedBy,
        action: 'OPPORTUNITY_STATUS_TRIGGER',
        resourceType: 'opportunity',
        resourceId: change.opportunityId,
        newValues: {
          oldStatus: change.oldStatus,
          newStatus: change.newStatus,
          triggeredAt: change.changedAt
        }
      }
    });
  }

  private async logTriggerError(change: OpportunityStatusChange, error: any): Promise<void> {
    await prisma.auditLog.create({
      data: {
        tenantId: change.tenantId,
        userId: change.changedBy,
        action: 'OPPORTUNITY_STATUS_TRIGGER_ERROR',
        resourceType: 'opportunity',
        resourceId: change.opportunityId,
        newValues: {
          error: error.message,
          oldStatus: change.oldStatus,
          newStatus: change.newStatus,
          triggeredAt: change.changedAt
        }
      }
    });
  }
}

export const opportunityTriggersService = new OpportunityTriggersService();
