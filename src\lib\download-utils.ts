/**
 * Utility functions for handling file downloads in the browser
 */

/**
 * Download a file from a URL using fetch and trigger browser download
 */
export async function downloadFile(url: string, filename?: string): Promise<void> {
  // Check if we're in the browser environment
  if (typeof window === 'undefined') {
    throw new Error('Download functionality is only available in the browser');
  }

  try {
    const response = await fetch(url);
    
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ error: 'Unknown error' }));
      throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
    }

    const blob = await response.blob();
    
    // Get filename from response headers if not provided
    let downloadFilename = filename;
    if (!downloadFilename) {
      const contentDisposition = response.headers.get('content-disposition');
      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/);
        if (filenameMatch && filenameMatch[1]) {
          downloadFilename = filenameMatch[1].replace(/['"]/g, '');
        }
      }
    }
    
    // Fallback filename
    if (!downloadFilename) {
      downloadFilename = 'download';
    }

    triggerDownload(blob, downloadFilename);
  } catch (error) {
    console.error('Download error:', error);
    throw error;
  }
}

/**
 * Download a blob as a file
 */
export function downloadBlob(blob: Blob, filename: string): void {
  if (typeof window === 'undefined') {
    throw new Error('Download functionality is only available in the browser');
  }

  triggerDownload(blob, filename);
}

/**
 * Create and trigger a download link
 */
function triggerDownload(blob: Blob, filename: string): void {
  const url = window.URL.createObjectURL(blob);
  const a = window.document.createElement('a');
  
  a.href = url;
  a.download = filename;
  a.style.display = 'none';
  
  window.document.body.appendChild(a);
  a.click();
  
  // Cleanup
  window.URL.revokeObjectURL(url);
  window.document.body.removeChild(a);
}

/**
 * Download data as CSV file with proper UTF-8 encoding for Arabic characters
 */
export function downloadCSV(data: string[][], filename: string): void {
  const csvContent = data.map(row =>
    row.map(cell =>
      // Escape quotes and wrap in quotes if contains comma, quote, or newline
      typeof cell === 'string' && (cell.includes(',') || cell.includes('"') || cell.includes('\n'))
        ? `"${cell.replace(/"/g, '""')}"`
        : cell
    ).join(',')
  ).join('\n');

  // Add UTF-8 BOM for proper Arabic character support in Excel and other applications
  const BOM = '\uFEFF';
  const csvWithBOM = BOM + csvContent;

  const blob = new Blob([csvWithBOM], { type: 'text/csv;charset=utf-8;' });
  downloadBlob(blob, filename.endsWith('.csv') ? filename : `${filename}.csv`);
}

/**
 * Download data as JSON file
 */
export function downloadJSON(data: any, filename: string): void {
  const jsonContent = JSON.stringify(data, null, 2);
  const blob = new Blob([jsonContent], { type: 'application/json;charset=utf-8;' });
  downloadBlob(blob, filename.endsWith('.json') ? filename : `${filename}.json`);
}
