"use client"

import { useRouter } from 'next/navigation'
import { useUIStore } from '@/stores/ui-store'
import { useCallback } from 'react'

export function useSmoothNavigation() {
  const router = useRouter()

  const navigate = useCallback((href: string, options?: { replace?: boolean }) => {
    // Navigate immediately without artificial delays
    if (options?.replace) {
      router.replace(href)
    } else {
      router.push(href)
    }
  }, [router])

  const navigateWithTransition = useCallback((href: string, options?: { replace?: boolean }) => {
    return new Promise<void>((resolve) => {
      navigate(href, options)

      // Resolve after navigation is likely complete
      setTimeout(() => {
        resolve()
      }, 300)
    })
  }, [navigate])

  return {
    navigate,
    navigateWithTransition,
    push: (href: string) => navigate(href),
    replace: (href: string) => navigate(href, { replace: true }),
  }
}
