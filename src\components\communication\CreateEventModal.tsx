import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, <PERSON>alogHeader, DialogTitle, DialogTrigger } from '../ui/dialog';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Textarea } from '../ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { Switch } from '../ui/switch';
import { Plus, Calendar, Clock, MapPin, Video, Users } from 'lucide-react';
import { format } from 'date-fns';

interface CreateEventModalProps {
  onCreateEvent: (eventData: {
    title: string;
    description?: string;
    startTime: string;
    endTime: string;
    timezone?: string;
    isAllDay?: boolean;
    location?: string;
    meetingLink?: string;
    eventType?: string;
    attendeeIds?: string[];
  }) => Promise<void>;
  trigger?: React.ReactNode;
}

export default function CreateEventModal({ onCreateEvent, trigger }: CreateEventModalProps) {
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    startDate: format(new Date(), 'yyyy-MM-dd'),
    startTime: format(new Date(), 'HH:mm'),
    endDate: format(new Date(), 'yyyy-MM-dd'),
    endTime: format(new Date(Date.now() + 60 * 60 * 1000), 'HH:mm'), // 1 hour later
    timezone: 'UTC',
    isAllDay: false,
    location: '',
    meetingLink: '',
    eventType: 'meeting',
    attendeeEmails: '',
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.title.trim()) return;

    setLoading(true);
    try {
      const startDateTime = formData.isAllDay 
        ? `${formData.startDate}T00:00:00.000Z`
        : `${formData.startDate}T${formData.startTime}:00.000Z`;
      
      const endDateTime = formData.isAllDay 
        ? `${formData.endDate}T23:59:59.999Z`
        : `${formData.endDate}T${formData.endTime}:00.000Z`;

      await onCreateEvent({
        title: formData.title.trim(),
        description: formData.description.trim() || undefined,
        startTime: startDateTime,
        endTime: endDateTime,
        timezone: formData.timezone,
        isAllDay: formData.isAllDay,
        location: formData.location.trim() || undefined,
        meetingLink: formData.meetingLink.trim() || undefined,
        eventType: formData.eventType,
        attendeeIds: [], // TODO: Convert emails to user IDs
      });
      
      // Reset form and close modal
      setFormData({
        title: '',
        description: '',
        startDate: format(new Date(), 'yyyy-MM-dd'),
        startTime: format(new Date(), 'HH:mm'),
        endDate: format(new Date(), 'yyyy-MM-dd'),
        endTime: format(new Date(Date.now() + 60 * 60 * 1000), 'HH:mm'),
        timezone: 'UTC',
        isAllDay: false,
        location: '',
        meetingLink: '',
        eventType: 'meeting',
        attendeeEmails: '',
      });
      setOpen(false);
    } catch (error) {
      console.error('Failed to create event:', error);
    } finally {
      setLoading(false);
    }
  };

  const getEventTypeIcon = (type: string) => {
    switch (type) {
      case 'meeting':
        return <Users className="h-4 w-4" />;
      case 'task':
        return <Clock className="h-4 w-4" />;
      case 'reminder':
        return <Clock className="h-4 w-4" />;
      case 'deadline':
        return <Clock className="h-4 w-4" />;
      case 'holiday':
        return <Calendar className="h-4 w-4" />;
      default:
        return <Calendar className="h-4 w-4" />;
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {trigger || (
          <Button>
            <Plus className="h-4 w-4 mr-2" />
            New Event
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Create New Event</DialogTitle>
        </DialogHeader>
        
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Event Title */}
          <div className="space-y-2">
            <Label htmlFor="title">Event Title *</Label>
            <Input
              id="title"
              value={formData.title}
              onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
              placeholder="e.g., Team Meeting, Client Call, Project Review"
              required
            />
          </div>

          {/* Description */}
          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              placeholder="What's this event about?"
              rows={3}
            />
          </div>

          {/* Event Type */}
          <div className="space-y-2">
            <Label htmlFor="eventType">Event Type</Label>
            <Select
              value={formData.eventType}
              onValueChange={(value) => setFormData(prev => ({ ...prev, eventType: value }))}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select event type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="meeting">
                  <div className="flex items-center space-x-2">
                    <Users className="h-4 w-4" />
                    <span>Meeting</span>
                  </div>
                </SelectItem>
                <SelectItem value="task">
                  <div className="flex items-center space-x-2">
                    <Clock className="h-4 w-4" />
                    <span>Task</span>
                  </div>
                </SelectItem>
                <SelectItem value="reminder">
                  <div className="flex items-center space-x-2">
                    <Clock className="h-4 w-4" />
                    <span>Reminder</span>
                  </div>
                </SelectItem>
                <SelectItem value="deadline">
                  <div className="flex items-center space-x-2">
                    <Clock className="h-4 w-4" />
                    <span>Deadline</span>
                  </div>
                </SelectItem>
                <SelectItem value="holiday">
                  <div className="flex items-center space-x-2">
                    <Calendar className="h-4 w-4" />
                    <span>Holiday</span>
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* All Day Toggle */}
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="allDay">All Day Event</Label>
              <p className="text-xs text-muted-foreground">
                This event lasts the entire day
              </p>
            </div>
            <Switch
              id="allDay"
              checked={formData.isAllDay}
              onCheckedChange={(checked) => setFormData(prev => ({ ...prev, isAllDay: checked }))}
            />
          </div>

          {/* Date and Time */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="startDate">Start Date *</Label>
              <Input
                id="startDate"
                type="date"
                value={formData.startDate}
                onChange={(e) => setFormData(prev => ({ ...prev, startDate: e.target.value }))}
                required
              />
            </div>
            
            {!formData.isAllDay && (
              <div className="space-y-2">
                <Label htmlFor="startTime">Start Time *</Label>
                <Input
                  id="startTime"
                  type="time"
                  value={formData.startTime}
                  onChange={(e) => setFormData(prev => ({ ...prev, startTime: e.target.value }))}
                  required
                />
              </div>
            )}
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="endDate">End Date *</Label>
              <Input
                id="endDate"
                type="date"
                value={formData.endDate}
                onChange={(e) => setFormData(prev => ({ ...prev, endDate: e.target.value }))}
                required
              />
            </div>
            
            {!formData.isAllDay && (
              <div className="space-y-2">
                <Label htmlFor="endTime">End Time *</Label>
                <Input
                  id="endTime"
                  type="time"
                  value={formData.endTime}
                  onChange={(e) => setFormData(prev => ({ ...prev, endTime: e.target.value }))}
                  required
                />
              </div>
            )}
          </div>

          {/* Location */}
          <div className="space-y-2">
            <Label htmlFor="location">Location</Label>
            <div className="relative">
              <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                id="location"
                value={formData.location}
                onChange={(e) => setFormData(prev => ({ ...prev, location: e.target.value }))}
                placeholder="e.g., Conference Room A, 123 Main St, Online"
                className="pl-10"
              />
            </div>
          </div>

          {/* Meeting Link */}
          <div className="space-y-2">
            <Label htmlFor="meetingLink">Meeting Link</Label>
            <div className="relative">
              <Video className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                id="meetingLink"
                type="url"
                value={formData.meetingLink}
                onChange={(e) => setFormData(prev => ({ ...prev, meetingLink: e.target.value }))}
                placeholder="https://zoom.us/j/123456789"
                className="pl-10"
              />
            </div>
          </div>

          {/* Attendees */}
          <div className="space-y-2">
            <Label htmlFor="attendees">Attendees (Email addresses)</Label>
            <Textarea
              id="attendees"
              value={formData.attendeeEmails}
              onChange={(e) => setFormData(prev => ({ ...prev, attendeeEmails: e.target.value }))}
              placeholder="Enter email addresses separated by commas"
              rows={2}
            />
            <p className="text-xs text-muted-foreground">
              Separate multiple email addresses with commas
            </p>
          </div>

          {/* Actions */}
          <div className="flex justify-end space-x-2">
            <Button
              type="button"
              variant="outline"
              onClick={() => setOpen(false)}
              disabled={loading}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={loading || !formData.title.trim()}>
              {loading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Creating...
                </>
              ) : (
                <>
                  {getEventTypeIcon(formData.eventType)}
                  <span className="ml-2">Create Event</span>
                </>
              )}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
