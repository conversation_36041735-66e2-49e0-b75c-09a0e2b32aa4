'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';
import { useSession } from 'next-auth/react';
import { TenantContext as TenantContextType } from '@/types';

interface TenantProviderContextType {
  currentTenant: TenantContextType | null;
  setCurrentTenant: (tenant: TenantContextType | null) => void;
  isLoading: boolean;
  error: string | null;
}

const TenantProviderContext = createContext<TenantProviderContextType | undefined>(undefined);

export function TenantProvider({ children }: { children: React.ReactNode }) {
  const { data: session, status } = useSession();
  const [currentTenant, setCurrentTenant] = useState<TenantContextType | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Load user's tenant when session is available
  useEffect(() => {
    const loadTenant = async () => {
      if (status === 'authenticated' && session?.user) {
        try {
          const response = await fetch('/api/tenants');
          if (response.ok) {
            const data = await response.json();
            const tenants = data.data || [];

            // Set the user's single tenant
            if (tenants.length > 0) {
              setCurrentTenant({
                id: tenants[0].id,
                name: tenants[0].name,
                slug: tenants[0].slug,
                subscription: tenants[0].subscription || {
                  planId: '',
                  status: 'inactive',
                  features: [],
                  limits: {}
                },
                settings: tenants[0].settings || {},
                branding: tenants[0].branding || {}
              });
            }
          }
        } catch (error) {
          console.error('Failed to load tenant:', error);
          setError('Failed to load tenant');
        }
      }
    };

    loadTenant();
  }, [status, session]);

  // Clear tenant when user logs out
  useEffect(() => {
    if (status === 'unauthenticated') {
      setCurrentTenant(null);
      setIsLoading(false);
      setError(null);
    } else if (status === 'authenticated') {
      setIsLoading(false);
    }
  }, [status]);

  const value = {
    currentTenant,
    setCurrentTenant,
    isLoading,
    error
  };

  return (
    <TenantProviderContext.Provider value={value}>
      {children}
    </TenantProviderContext.Provider>
  );
}

export function useTenantContext() {
  const context = useContext(TenantProviderContext);
  if (context === undefined) {
    throw new Error('useTenantContext must be used within a TenantProvider');
  }
  return context;
}

// HOC for components that require tenant context
export function withTenantContext<P extends object>(
  Component: React.ComponentType<P>
) {
  return function TenantWrappedComponent(props: P) {
    const { currentTenant, isLoading } = useTenantContext();

    if (isLoading) {
      return (
        <div className="flex items-center justify-center min-h-screen">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary-600"></div>
        </div>
      );
    }

    if (!currentTenant) {
      return (
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">
              No Tenant Selected
            </h2>
            <p className="text-gray-600">
              Please select a tenant to continue.
            </p>
          </div>
        </div>
      );
    }

    return <Component {...props} />;
  };
}
