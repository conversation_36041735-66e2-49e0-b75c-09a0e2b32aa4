import { NextResponse } from 'next/server';
import { z } from 'zod';
import { withPermission, AuthenticatedRequest } from '@/lib/permission-middleware';
import { roleManagementService } from '@/services/role-management';
import { PermissionAction, PermissionResource } from '@/types';

const updateSystemRoleSchema = z.object({
  name: z.string().min(1, 'Role name is required').max(100, 'Role name too long').optional(),
  description: z.string().optional(),
  permissions: z.array(z.string()).optional()
});

const updateSystemRolePermissionsSchema = z.object({
  permissions: z.array(z.string()).min(0, 'Permissions array is required')
});

interface RouteParams {
  params: Promise<{
    id: string;
  }>;
}

/**
 * PUT /api/roles/[id]/system
 * Update a system role (requires MANAGE_SYSTEM_ROLES permission)
 */
export const PUT = withPermission({
  resource: PermissionResource.ROLES,
  action: PermissionAction.MANAGE_SYSTEM_ROLES,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest, { params }: RouteParams) => {
  try {
    const { id: roleId } = await params;
    const body = await req.json();
    const { updateType = 'role', ...updateData } = body;

    if (updateType === 'permissions') {
      // Update only permissions
      const validatedData = updateSystemRolePermissionsSchema.parse(updateData);
      
      await roleManagementService.updateSystemRolePermissions(
        roleId,
        req.tenantId,
        validatedData.permissions,
        req.userId
      );

      return NextResponse.json({
        success: true,
        message: 'System role permissions updated successfully'
      });
    } else {
      // Update role details and optionally permissions
      const validatedData = updateSystemRoleSchema.parse(updateData);

      const role = await roleManagementService.updateSystemRole(
        roleId,
        req.tenantId,
        validatedData,
        req.userId
      );

      return NextResponse.json({
        success: true,
        data: { role },
        message: 'System role updated successfully'
      });
    }
  } catch (error) {
    console.error('Update system role error:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          error: 'Validation failed',
          details: error.errors.map(e => ({
            field: e.path.join('.'),
            message: e.message
          }))
        },
        { status: 400 }
      );
    }

    if (error instanceof Error) {
      if (error.message.includes('not found')) {
        return NextResponse.json(
          { error: 'Role not found' },
          { status: 404 }
        );
      }
      if (error.message.includes('privilege escalation')) {
        return NextResponse.json(
          { error: 'Cannot assign permissions you do not have' },
          { status: 403 }
        );
      }
    }

    return NextResponse.json(
      { error: 'Failed to update system role' },
      { status: 500 }
    );
  }
});
