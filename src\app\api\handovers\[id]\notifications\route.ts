import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { handoverNotificationService } from '@/services/handover-notifications';
import { z } from 'zod';

// Request validation schemas
const getNotificationsQuerySchema = z.object({
  isRead: z.coerce.boolean().optional(),
  type: z.string().optional(),
  limit: z.coerce.number().int().positive().max(100).default(20),
  offset: z.coerce.number().int().min(0).default(0),
});

const createNotificationSchema = z.object({
  userIds: z.array(z.string()).min(1, 'At least one user ID is required'),
  type: z.enum([
    'handover_created',
    'handover_assigned',
    'checklist_completed',
    'checklist_item_completed',
    'document_uploaded',
    'document_approved',
    'qa_thread_created',
    'qa_response_received',
    'qa_thread_escalated',
    'handover_completed',
    'project_created',
    'handover_overdue'
  ]),
  title: z.string().min(1, 'Title is required').max(255),
  message: z.string().min(1, 'Message is required'),
  data: z.record(z.any()).default({}),
  actionUrl: z.string().optional(),
  actionLabel: z.string().optional(),
});

interface RouteParams {
  params: {
    id: string;
  };
}

/**
 * GET /api/handovers/[id]/notifications - Get notifications for a handover
 */
export async function GET(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get tenantId from user's tenants array (users have one tenant in this system)
    let tenantId: string | null = null;
    if (session.user.tenants && session.user.tenants.length > 0) {
      tenantId = session.user.tenants[0].id;
    }

    if (!tenantId) {
      return NextResponse.json({ error: 'Tenant not found' }, { status: 400 });
    }

    // Parse and validate query parameters
    const url = new URL(request.url);
    const queryParams = Object.fromEntries(url.searchParams.entries());
    
    const validatedQuery = getNotificationsQuerySchema.parse(queryParams);

    const resolvedParams = await params;
    const result = await handoverNotificationService.getUserNotifications(
      tenantId,
      session.user.id,
      {
        ...validatedQuery,
        handoverId: resolvedParams.id
      }
    );

    return NextResponse.json(result);
  } catch (error) {
    console.error('Error fetching handover notifications:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid query parameters', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to fetch handover notifications' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/handovers/[id]/notifications - Create notifications for a handover
 */
export async function POST(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get tenantId from user's tenants array (users have one tenant in this system)
    let tenantId: string | null = null;
    if (session.user.tenants && session.user.tenants.length > 0) {
      tenantId = session.user.tenants[0].id;
    }

    if (!tenantId) {
      return NextResponse.json({ error: 'Tenant not found' }, { status: 400 });
    }

    const body = await request.json();
    const validatedData = createNotificationSchema.parse(body);

    const notifications = await handoverNotificationService.createBulkNotifications(
      tenantId,
      params.id,
      {
        userIds: validatedData.userIds,
        notification: {
          type: validatedData.type,
          title: validatedData.title,
          message: validatedData.message,
          data: validatedData.data,
          actionUrl: validatedData.actionUrl,
          actionLabel: validatedData.actionLabel,
        }
      }
    );

    return NextResponse.json({ notifications }, { status: 201 });
  } catch (error) {
    console.error('Error creating handover notifications:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request data', details: error.errors },
        { status: 400 }
      );
    }

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to create handover notifications' },
      { status: 500 }
    );
  }
}
