@echo off
REM Windows batch version of generate-ssl-certs.sh for CRM Platform

setlocal enabledelayedexpansion

REM Configuration
set "SSL_DIR=config\nginx\ssl"
set "DOMAIN=crm-platform.local"
set "DAYS=365"

REM Colors for terminal output
set "RED=[91m"
set "GREEN=[92m"
set "YELLOW=[93m"
set "NC=[0m"

echo %YELLOW%Generating SSL certificates for %DOMAIN%...%NC%

REM Check if OpenSSL is installed
where openssl >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo %RED%Error: OpenSSL is not installed or not in PATH%NC%
    echo %YELLOW%Please install OpenSSL or use Docker to generate certificates:%NC%
    echo docker run --rm -v "%cd%:/app" -w /app alpine/openssl req -x509 -nodes -days 365 -newkey rsa:2048 -keyout /app/%SSL_DIR%/private.key -out /app/%SSL_DIR%/certificate.crt -subj "/CN=%DOMAIN%"
    exit /b 1
)

REM Create SSL directory if it doesn't exist
if not exist "%SSL_DIR%" (
    echo %YELLOW%Creating SSL directory: %SSL_DIR%%NC%
    mkdir "%SSL_DIR%"
)

REM Generate private key
echo %YELLOW%Generating private key...%NC%
openssl genrsa -out "%SSL_DIR%\private.key" 2048

REM Generate certificate signing request
echo %YELLOW%Generating certificate signing request...%NC%
openssl req -new -key "%SSL_DIR%\private.key" -out "%SSL_DIR%\request.csr" -subj "/CN=%DOMAIN%"

REM Generate self-signed certificate
echo %YELLOW%Generating self-signed certificate...%NC%
openssl x509 -req -days %DAYS% -in "%SSL_DIR%\request.csr" -signkey "%SSL_DIR%\private.key" -out "%SSL_DIR%\certificate.crt"

REM Clean up CSR file
del "%SSL_DIR%\request.csr"

echo %GREEN%SSL certificates generated successfully!%NC%
echo %YELLOW%Certificate location: %SSL_DIR%\certificate.crt%NC%
echo %YELLOW%Private key location: %SSL_DIR%\private.key%NC%

endlocal
