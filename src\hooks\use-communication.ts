import { useState, useEffect, useCallback } from 'react';
import { useSession } from 'next-auth/react';
import { useTenant } from './use-tenant';

interface ChatChannel {
  id: string;
  name: string;
  description?: string;
  type: string;
  isPrivate: boolean;
  _count: {
    messages: number;
    members: number;
  };
}

interface ChatMessage {
  id: string;
  content: string;
  messageType: string;
  createdAt: string;
  isEdited: boolean;
  user: {
    id: string;
    firstName: string | null;
    lastName: string | null;
    email: string;
    avatarUrl: string | null;
  };
  reactions?: Record<string, string[]>;
  threadCount: number;
  parentMessage?: ChatMessage;
}

interface CalendarEvent {
  id: string;
  title: string;
  description?: string;
  startTime: string;
  endTime: string;
  timezone: string;
  isAllDay: boolean;
  location?: string;
  meetingLink?: string;
  eventType: string;
  status: string;
  visibility: string;
  createdBy: {
    id: string;
    firstName: string | null;
    lastName: string | null;
    email: string;
    avatarUrl: string | null;
  };
  attendees: Array<{
    id: string;
    status: string;
    user: {
      id: string;
      firstName: string | null;
      lastName: string | null;
      email: string;
      avatarUrl: string | null;
    };
  }>;
}

interface UnreadCount {
  channelId: string;
  channelName: string;
  unreadCount: number;
}

interface CommunicationUser {
  id: string;
  email: string;
  firstName?: string | null;
  lastName?: string | null;
}

export function useCommunication(user?: CommunicationUser) {
  const { data: session } = useSession();
  const { currentTenant } = useTenant();

  // Use provided user data or fall back to session
  const currentUser = user || session?.user;
  const [channels, setChannels] = useState<ChatChannel[]>([]);
  const [events, setEvents] = useState<CalendarEvent[]>([]);
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [unreadCounts, setUnreadCounts] = useState<UnreadCount[]>([]);
  const [selectedChannel, setSelectedChannel] = useState<ChatChannel | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleError = useCallback((error: any, context: string) => {
    console.error(`${context} error:`, error);
    setError(error instanceof Error ? error.message : 'An error occurred');
  }, []);

  // Helper function to get headers with tenant context
  const getHeaders = useCallback(() => {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
    };

    if (currentTenant?.id) {
      headers['x-tenant-id'] = currentTenant.id;
    }

    return headers;
  }, [currentTenant?.id]);

  const loadChannels = useCallback(async () => {
    try {
      const response = await fetch('/api/chat/channels', {
        headers: getHeaders(),
      });
      if (!response.ok) throw new Error('Failed to load channels');

      const data = await response.json();
      setChannels(data);

      if (data.length > 0 && !selectedChannel) {
        setSelectedChannel(data[0]);
      }
    } catch (error) {
      handleError(error, 'Load channels');
    }
  }, [selectedChannel, handleError, getHeaders]);

  const loadEvents = useCallback(async (options: {
    startDate?: Date;
    endDate?: Date;
    eventType?: string;
  } = {}) => {
    try {
      const params = new URLSearchParams();
      if (options.startDate) params.append('startDate', options.startDate.toISOString());
      if (options.endDate) params.append('endDate', options.endDate.toISOString());
      if (options.eventType) params.append('eventType', options.eventType);

      const response = await fetch(`/api/calendar/events?${params}`, {
        headers: getHeaders(),
      });
      if (!response.ok) throw new Error('Failed to load events');

      const data = await response.json();
      setEvents(data);
    } catch (error) {
      handleError(error, 'Load events');
    }
  }, [handleError, getHeaders]);

  const loadMessages = useCallback(async (channelId: string, options: {
    limit?: number;
    offset?: number;
    parentMessageId?: string;
  } = {}) => {
    if (!channelId) return;

    try {
      const params = new URLSearchParams();
      if (options.limit) params.append('limit', options.limit.toString());
      if (options.offset) params.append('offset', options.offset.toString());
      if (options.parentMessageId) params.append('parentMessageId', options.parentMessageId);

      const response = await fetch(`/api/chat/channels/${channelId}/messages?${params}`, {
        headers: getHeaders(),
      });
      if (!response.ok) throw new Error('Failed to load messages');

      const data = await response.json();
      setMessages(data);
    } catch (error) {
      handleError(error, 'Load messages');
    }
  }, [handleError, getHeaders]);

  const loadUnreadCounts = useCallback(async () => {
    try {
      const response = await fetch('/api/communication/unread-counts', {
        headers: getHeaders(),
      });
      if (!response.ok) throw new Error('Failed to load unread counts');

      const data = await response.json();
      setUnreadCounts(data);
    } catch (error) {
      handleError(error, 'Load unread counts');
    }
  }, [handleError, getHeaders]);

  const sendMessage = useCallback(async (content: string, parentMessageId?: string) => {
    if (!selectedChannel) return;

    try {
      const response = await fetch(`/api/chat/channels/${selectedChannel.id}/messages`, {
        method: 'POST',
        headers: getHeaders(),
        body: JSON.stringify({
          content,
          parentMessageId,
        }),
      });

      if (!response.ok) throw new Error('Failed to send message');

      const newMessage = await response.json();
      setMessages(prev => [...prev, newMessage]);

      // Reload unread counts
      loadUnreadCounts();
    } catch (error) {
      handleError(error, 'Send message');
    }
  }, [selectedChannel, handleError, loadUnreadCounts, getHeaders]);

  const createChannel = useCallback(async (channelData: {
    name: string;
    description?: string;
    type?: string;
    isPrivate?: boolean;
    relatedToType?: string;
    relatedToId?: string;
  }) => {
    try {
      const response = await fetch('/api/chat/channels', {
        method: 'POST',
        headers: getHeaders(),
        body: JSON.stringify(channelData),
      });

      if (!response.ok) throw new Error('Failed to create channel');

      const newChannel = await response.json();
      setChannels(prev => [newChannel, ...prev]);
      setSelectedChannel(newChannel);

      return newChannel;
    } catch (error) {
      handleError(error, 'Create channel');
      throw error;
    }
  }, [handleError, getHeaders]);

  const createEvent = useCallback(async (eventData: {
    title: string;
    description?: string;
    startTime: string;
    endTime: string;
    timezone?: string;
    isAllDay?: boolean;
    location?: string;
    meetingLink?: string;
    eventType?: string;
    attendeeIds?: string[];
  }) => {
    try {
      const response = await fetch('/api/calendar/events', {
        method: 'POST',
        headers: getHeaders(),
        body: JSON.stringify(eventData),
      });

      if (!response.ok) throw new Error('Failed to create event');

      const newEvent = await response.json();
      setEvents(prev => [newEvent, ...prev]);

      return newEvent;
    } catch (error) {
      handleError(error, 'Create event');
      throw error;
    }
  }, [handleError, getHeaders]);

  const addReaction = useCallback(async (messageId: string, emoji: string) => {
    try {
      const response = await fetch(`/api/chat/messages/${messageId}/reactions`, {
        method: 'POST',
        headers: getHeaders(),
        body: JSON.stringify({ emoji }),
      });

      if (!response.ok) throw new Error('Failed to add reaction');

      const updatedMessage = await response.json();
      setMessages(prev => prev.map(msg =>
        msg.id === messageId ? updatedMessage : msg
      ));
    } catch (error) {
      handleError(error, 'Add reaction');
    }
  }, [handleError, getHeaders]);

  const updateEventAttendance = useCallback(async (
    eventId: string,
    status: 'accepted' | 'declined' | 'tentative',
    response?: string
  ) => {
    try {
      const res = await fetch(`/api/calendar/events/${eventId}/attendance`, {
        method: 'PUT',
        headers: getHeaders(),
        body: JSON.stringify({ status, response }),
      });

      if (!res.ok) throw new Error('Failed to update attendance');

      const updatedAttendance = await res.json();

      // Update the event in the events list
      setEvents(prev => prev.map(event => {
        if (event.id === eventId) {
          return {
            ...event,
            attendees: event.attendees.map(attendee =>
              attendee.user.id === currentUser?.id
                ? { ...attendee, status }
                : attendee
            )
          };
        }
        return event;
      }));

      return updatedAttendance;
    } catch (error) {
      handleError(error, 'Update attendance');
      throw error;
    }
  }, [currentUser?.id, handleError, getHeaders]);

  const loadCommunicationData = useCallback(async () => {
    setLoading(true);
    setError(null);
    
    try {
      await Promise.all([
        loadChannels(),
        loadEvents(),
        loadUnreadCounts(),
      ]);
    } catch (error) {
      handleError(error, 'Load communication data');
    } finally {
      setLoading(false);
    }
  }, [loadChannels, loadEvents, loadUnreadCounts, handleError]);

  const getTotalUnreadCount = useCallback(() => {
    return unreadCounts.reduce((total, channel) => total + channel.unreadCount, 0);
  }, [unreadCounts]);

  return {
    // State
    channels,
    events,
    messages,
    unreadCounts,
    selectedChannel,
    loading,
    error,
    
    // Actions
    setSelectedChannel,
    loadCommunicationData,
    loadMessages,
    sendMessage,
    createChannel,
    createEvent,
    addReaction,
    updateEventAttendance,
    
    // Computed
    getTotalUnreadCount,
  };
}
