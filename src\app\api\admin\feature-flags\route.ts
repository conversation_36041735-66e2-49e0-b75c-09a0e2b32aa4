import { NextRequest, NextResponse } from 'next/server';
import { withSuperAdminAuth } from '@/lib/tenant-context';
import { FeatureFlagService } from '@/services/feature-flag';
import { z } from 'zod';

const createFeatureFlagSchema = z.object({
  name: z.string().min(1, 'Name is required').max(100, 'Name too long'),
  description: z.string().optional(),
  isEnabled: z.boolean().optional(),
  requiredPlanLevel: z.number().int().min(1).max(10).optional()
});

const updateFeatureFlagSchema = z.object({
  name: z.string().min(1).max(100).optional(),
  description: z.string().optional(),
  isEnabled: z.boolean().optional(),
  requiredPlanLevel: z.number().int().min(1).max(10).optional()
});

const bulkUpdateSchema = z.object({
  updates: z.array(z.object({
    id: z.string().uuid(),
    isEnabled: z.boolean()
  }))
});

/**
 * @swagger
 * /api/admin/feature-flags:
 *   get:
 *     summary: Get all feature flags (Super Admin only)
 *     description: Retrieve all feature flags with their current status
 *     tags:
 *       - Super Admin - Feature Flags
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Feature flags retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: string
 *                       name:
 *                         type: string
 *                       description:
 *                         type: string
 *                       isEnabled:
 *                         type: boolean
 *                       requiredPlanLevel:
 *                         type: number
 *                       createdAt:
 *                         type: string
 *                         format: date-time
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Super Admin access required
 */
export const GET = withSuperAdminAuth(async (request: NextRequest) => {
  try {
    const featureFlagService = new FeatureFlagService();
    const featureFlags = await featureFlagService.getAllFeatureFlags();

    return NextResponse.json({
      success: true,
      data: featureFlags
    });

  } catch (error) {
    console.error('Get feature flags error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
});

/**
 * @swagger
 * /api/admin/feature-flags:
 *   post:
 *     summary: Create new feature flag (Super Admin only)
 *     description: Create a new feature flag for the platform
 *     tags:
 *       - Super Admin - Feature Flags
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *             properties:
 *               name:
 *                 type: string
 *                 description: Unique name for the feature flag
 *               description:
 *                 type: string
 *                 description: Description of the feature
 *               isEnabled:
 *                 type: boolean
 *                 description: Whether the feature is enabled
 *                 default: true
 *               requiredPlanLevel:
 *                 type: number
 *                 description: Minimum plan level required for this feature
 *                 default: 1
 *     responses:
 *       201:
 *         description: Feature flag created successfully
 *       400:
 *         description: Invalid input data
 *       409:
 *         description: Feature flag with this name already exists
 */
export const POST = withSuperAdminAuth(async (request: NextRequest) => {
  try {
    const body = await request.json();
    const validatedData = createFeatureFlagSchema.parse(body);

    const featureFlagService = new FeatureFlagService();
    const featureFlag = await featureFlagService.createFeatureFlag(validatedData);

    return NextResponse.json({
      success: true,
      data: featureFlag
    }, { status: 201 });

  } catch (error) {
    console.error('Create feature flag error:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid input data', details: error.errors },
        { status: 400 }
      );
    }

    if (error instanceof Error && error.message.includes('already exists')) {
      return NextResponse.json(
        { error: error.message },
        { status: 409 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
});

/**
 * @swagger
 * /api/admin/feature-flags:
 *   put:
 *     summary: Bulk update feature flags (Super Admin only)
 *     description: Update multiple feature flags at once
 *     tags:
 *       - Super Admin - Feature Flags
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - updates
 *             properties:
 *               updates:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: string
 *                     isEnabled:
 *                       type: boolean
 *     responses:
 *       200:
 *         description: Feature flags updated successfully
 *       400:
 *         description: Invalid input data
 */
export const PUT = withSuperAdminAuth(async (request: NextRequest) => {
  try {
    const body = await request.json();
    const validatedData = bulkUpdateSchema.parse(body);

    const featureFlagService = new FeatureFlagService();
    await featureFlagService.bulkUpdateFeatureFlags(validatedData.updates);

    return NextResponse.json({
      success: true,
      message: 'Feature flags updated successfully'
    });

  } catch (error) {
    console.error('Bulk update feature flags error:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid input data', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
});
