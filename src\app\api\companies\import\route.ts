import { NextResponse } from 'next/server';
import { CompanyManagementService, createCompanySchema } from '@/services/company-management';
import { withPermission, AuthenticatedRequest } from '@/lib/permission-middleware';
import { PermissionResource, PermissionAction } from '@/types';
import { prisma } from '@/lib/prisma';

const companyService = new CompanyManagementService();

interface ImportError {
  row: number;
  field: string;
  message: string;
}

// Helper function to parse CSV row with proper quote handling
function parseCSVRow(row: string): string[] {
  const result: string[] = [];
  let current = '';
  let inQuotes = false;

  for (let i = 0; i < row.length; i++) {
    const char = row[i];

    if (char === '"') {
      if (inQuotes && row[i + 1] === '"') {
        // Escaped quote
        current += '"';
        i++; // Skip next quote
      } else {
        // Toggle quote state
        inQuotes = !inQuotes;
      }
    } else if (char === ',' && !inQuotes) {
      // End of field
      result.push(current.trim());
      current = '';
    } else {
      current += char;
    }
  }

  // Add the last field
  result.push(current.trim());

  return result;
}

/**
 * POST /api/companies/import
 * Import companies from CSV file
 */
export const POST = withPermission({
  resource: PermissionResource.COMPANIES,
  action: PermissionAction.CREATE,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest) => {
  try {
    console.log('Company Import API called');
    const formData = await req.formData();
    const file = formData.get('file') as File;

    console.log('File received:', file?.name, file?.type, file?.size);

    if (!file) {
      return NextResponse.json(
        { error: 'No file provided' },
        { status: 400 }
      );
    }

    if (!file.name.endsWith('.csv') && file.type !== 'text/csv') {
      return NextResponse.json(
        { error: 'Only CSV files are supported' },
        { status: 400 }
      );
    }

    // Read file content
    const text = await file.text();
    console.log('File content length:', text.length);

    if (!text.trim()) {
      return NextResponse.json(
        { error: 'File is empty' },
        { status: 400 }
      );
    }

    // Parse CSV - handle different line endings
    const lines = text.replace(/\r\n/g, '\n').replace(/\r/g, '\n').split('\n').filter(line => line.trim());
    console.log('Total lines:', lines.length);

    if (lines.length < 2) {
      return NextResponse.json(
        { error: 'CSV file must have at least a header row and one data row' },
        { status: 400 }
      );
    }

    // Parse headers - handle quoted headers and normalize case
    const headers = lines[0].split(',').map(h => h.trim().replace(/^"|"$/g, '').toLowerCase());
    console.log('CSV headers:', headers);

    // Validate required headers
    const requiredHeaders = ['name'];
    const missingHeaders = requiredHeaders.filter(header => !headers.includes(header));

    if (missingHeaders.length > 0) {
      return NextResponse.json(
        { error: `Missing required columns: ${missingHeaders.join(', ')}. Found headers: ${headers.join(', ')}` },
        { status: 400 }
      );
    }

    // Get header indices
    const getHeaderIndex = (headerName: string) => headers.indexOf(headerName.toLowerCase());
    
    const nameIndex = getHeaderIndex('name');
    const websiteIndex = getHeaderIndex('website');
    const industryIndex = getHeaderIndex('industry');
    const sizeIndex = getHeaderIndex('size');
    const ownerEmailIndex = getHeaderIndex('owneremail');
    const ownerNameIndex = getHeaderIndex('ownername');

    // Process data rows
    const errors: ImportError[] = [];
    const imported: any[] = [];
    let importedCount = 0;

    // Cache for users to avoid duplicate lookups
    const userCache = new Map<string, string>();

    for (let i = 1; i < lines.length; i++) {
      const rowNumber = i + 1;
      // Better CSV parsing - handle quoted values with commas
      const values = parseCSVRow(lines[i]);

      try {
        // Extract values safely
        const name = values[nameIndex]?.trim() || '';
        const website = values[websiteIndex]?.trim() || null;
        const industry = values[industryIndex]?.trim() || null;
        const size = values[sizeIndex]?.trim() || null;
        const ownerEmail = values[ownerEmailIndex]?.trim() || '';
        const ownerName = values[ownerNameIndex]?.trim() || '';

        // Validate required fields
        if (!name) {
          errors.push({
            row: rowNumber,
            field: 'name',
            message: 'Company name is required'
          });
          continue;
        }

        // Validate website format if provided
        if (website && website !== '' && !/^https?:\/\/.+\..+/.test(website)) {
          errors.push({
            row: rowNumber,
            field: 'website',
            message: 'Invalid website URL format. Must start with http:// or https://'
          });
          continue;
        }

        // Find owner by email or name
        let ownerId: string | null = null;
        if (ownerEmail || ownerName) {
          const cacheKey = ownerEmail || ownerName;
          
          if (userCache.has(cacheKey)) {
            ownerId = userCache.get(cacheKey)!;
          } else {
            let user = null;
            
            if (ownerEmail) {
              user = await prisma.user.findFirst({
                where: {
                  email: ownerEmail,
                  tenantUsers: {
                    some: {
                      tenantId: req.tenantId,
                      status: 'active'
                    }
                  }
                }
              });
            } else if (ownerName) {
              const [firstName, ...lastNameParts] = ownerName.split(' ');
              const lastName = lastNameParts.join(' ');
              
              user = await prisma.user.findFirst({
                where: {
                  firstName: { contains: firstName, mode: 'insensitive' },
                  lastName: lastName ? { contains: lastName, mode: 'insensitive' } : undefined,
                  tenantUsers: {
                    some: {
                      tenantId: req.tenantId,
                      status: 'active'
                    }
                  }
                }
              });
            }

            if (user) {
              ownerId = user.id;
              userCache.set(cacheKey, user.id);
            } else {
              userCache.set(cacheKey, '');
              console.log(`Owner not found for: ${cacheKey}`);
            }
          }
        }

        // Check for duplicate company name
        const existingCompany = await prisma.company.findFirst({
          where: {
            name: { equals: name, mode: 'insensitive' },
            tenantId: req.tenantId
          }
        });

        if (existingCompany) {
          errors.push({
            row: rowNumber,
            field: 'name',
            message: `Company with name "${name}" already exists`
          });
          continue;
        }

        // Validate company data
        const companyData = {
          name,
          website,
          industry,
          size,
          ownerId,
        };

        try {
          createCompanySchema.parse(companyData);
        } catch (validationError: any) {
          errors.push({
            row: rowNumber,
            field: 'validation',
            message: validationError.errors?.[0]?.message || 'Validation failed'
          });
          continue;
        }

        // Create company
        try {
          const company = await companyService.createCompany(
            req.tenantId,
            companyData,
            req.userId
          );
          imported.push(company);
          importedCount++;
        } catch (createError: any) {
          errors.push({
            row: rowNumber,
            field: 'creation',
            message: createError.message || 'Failed to create company'
          });
        }

      } catch (rowError: any) {
        errors.push({
          row: rowNumber,
          field: 'parsing',
          message: 'Failed to parse row data'
        });
      }
    }

    // Return results
    const success = importedCount > 0 && errors.length === 0;
    
    return NextResponse.json({
      success,
      imported: importedCount,
      errors: errors.slice(0, 100), // Limit errors to prevent large responses
      message: success 
        ? `Successfully imported ${importedCount} companies`
        : `Imported ${importedCount} companies with ${errors.length} errors`
    });

  } catch (error) {
    console.error('Import companies error:', error);

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
});
