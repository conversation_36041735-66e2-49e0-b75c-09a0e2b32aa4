import { prisma } from '@/lib/prisma';
import { Company } from '@prisma/client';
import { z } from 'zod';
import { UnifiedNotificationService } from './unified-notification-service';

// Validation schemas
export const createCompanySchema = z.object({
  name: z.string().min(1, 'Company name is required').max(255),
  website: z.string().url('Invalid website URL').optional().nullable(),
  industry: z.string().max(100).optional().nullable(),
  size: z.string().max(50).optional().nullable(),
  ownerId: z.string().optional().nullable(),
});

export const updateCompanySchema = createCompanySchema.partial();

// Types
type UserSummary = {
  id: string;
  firstName: string | null;
  lastName: string | null;
  email: string;
};

type ContactSummary = {
  id: string;
  firstName: string | null;
  lastName: string | null;
  email: string | null;
  phone: string | null;
};

export interface CompanyWithRelations extends Company {
  owner?: UserSummary | null;
  createdBy?: UserSummary | null;
  contacts?: ContactSummary[];
  _count?: {
    contacts: number;
    leads: number;
    opportunities: number;
  };
}

export interface CompanyFilters {
  search?: string;
  industry?: string;
  size?: string;
  ownerId?: string;
  hasWebsite?: boolean;
  page?: number;
  limit?: number;
  sortBy?: 'name' | 'industry' | 'size' | 'createdAt' | 'updatedAt';
  sortOrder?: 'asc' | 'desc';
}

export class CompanyManagementService {
  private notificationService = new UnifiedNotificationService();

  /**
   * Create a new company
   */
  async createCompany(
    tenantId: string,
    companyData: z.infer<typeof createCompanySchema>,
    createdBy: string
  ): Promise<CompanyWithRelations> {
    const validatedData = createCompanySchema.parse(companyData);

    // Verify owner exists if provided
    if (validatedData.ownerId) {
      const owner = await prisma.user.findFirst({
        where: {
          id: validatedData.ownerId,
          tenantUsers: {
            some: {
              tenantId,
              status: 'active'
            }
          }
        }
      });

      if (!owner) {
        throw new Error('Owner not found or not part of tenant');
      }
    }

    const company = await prisma.company.create({
      data: {
        ...validatedData,
        tenantId,
        createdById: createdBy,
        ownerId: validatedData.ownerId || createdBy
      },
      include: {
        owner: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true
          }
        },
        createdBy: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true
          }
        },
        _count: {
          select: {
            contacts: true,
            leads: true,
            opportunities: true
          }
        }
      }
    });

    // Trigger company creation notification
    await this.triggerCompanyCreationNotification(company, tenantId);

    return company;
  }

  /**
   * Get company by ID
   */
  async getCompany(companyId: string, tenantId: string): Promise<CompanyWithRelations | null> {
    return await prisma.company.findFirst({
      where: {
        id: companyId,
        tenantId
      },
      include: {
        owner: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true
          }
        },
        createdBy: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true
          }
        },
        contacts: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            phone: true
          },
          orderBy: {
            firstName: 'asc'
          }
        },
        _count: {
          select: {
            contacts: true,
            leads: true,
            opportunities: true
          }
        }
      }
    });
  }

  /**
   * Get companies with filtering and pagination
   */
  async getCompanies(tenantId: string, filters: CompanyFilters = {}): Promise<{
    companies: CompanyWithRelations[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  }> {
    const {
      search,
      industry,
      size,
      ownerId,
      hasWebsite,
      page = 1,
      limit = 20,
      sortBy = 'name',
      sortOrder = 'asc'
    } = filters;

    const skip = (page - 1) * limit;

    // Build where clause
    const where: Record<string, unknown> = {
      tenantId
    };

    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { website: { contains: search, mode: 'insensitive' } },
        { industry: { contains: search, mode: 'insensitive' } }
      ];
    }

    if (industry) {
      where.industry = { contains: industry, mode: 'insensitive' };
    }

    if (size) {
      where.size = size;
    }

    if (ownerId) {
      where.ownerId = ownerId;
    }

    if (hasWebsite !== undefined) {
      if (hasWebsite) {
        where.website = { not: null };
      } else {
        where.website = null;
      }
    }

    // Get total count
    const total = await prisma.company.count({ where });

    // Get companies
    const companies = await prisma.company.findMany({
      where,
      include: {
        owner: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true
          }
        },
        createdBy: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true
          }
        },
        _count: {
          select: {
            contacts: true,
            leads: true,
            opportunities: true
          }
        }
      },
      orderBy: {
        [sortBy]: sortOrder
      },
      skip,
      take: limit
    });

    return {
      companies,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit)
    };
  }

  /**
   * Update company
   */
  async updateCompany(
    companyId: string,
    tenantId: string,
    updateData: z.infer<typeof updateCompanySchema>,
    updatedBy: string
  ): Promise<CompanyWithRelations> {
    const validatedData = updateCompanySchema.parse(updateData);

    // Verify company exists
    const existingCompany = await prisma.company.findFirst({
      where: {
        id: companyId,
        tenantId
      }
    });

    if (!existingCompany) {
      throw new Error('Company not found');
    }

    // Verify owner exists if provided
    if (validatedData.ownerId) {
      const owner = await prisma.user.findFirst({
        where: {
          id: validatedData.ownerId,
          tenantUsers: {
            some: {
              tenantId,
              status: 'active'
            }
          }
        }
      });

      if (!owner) {
        throw new Error('Owner not found or not part of tenant');
      }
    }

    const company = await prisma.company.update({
      where: {
        id: companyId
      },
      data: validatedData,
      include: {
        owner: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true
          }
        },
        createdBy: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true
          }
        },
        _count: {
          select: {
            contacts: true,
            leads: true,
            opportunities: true
          }
        }
      }
    });

    return company;
  }

  /**
   * Delete company
   */
  async deleteCompany(companyId: string, tenantId: string): Promise<void> {
    const company = await prisma.company.findFirst({
      where: {
        id: companyId,
        tenantId
      },
      include: {
        _count: {
          select: {
            contacts: true,
            leads: true,
            opportunities: true
          }
        }
      }
    });

    if (!company) {
      throw new Error('Company not found');
    }

    // Check if company has related records
    if (company._count.contacts > 0 || company._count.leads > 0 || company._count.opportunities > 0) {
      throw new Error('Cannot delete company with associated contacts, leads, or opportunities');
    }

    await prisma.company.delete({
      where: {
        id: companyId
      }
    });
  }

  /**
   * Search companies by name
   */
  async searchCompaniesByName(name: string, tenantId: string, limit: number = 10): Promise<CompanyWithRelations[]> {
    return await prisma.company.findMany({
      where: {
        name: {
          contains: name,
          mode: 'insensitive'
        },
        tenantId
      },
      include: {
        owner: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true
          }
        },
        _count: {
          select: {
            contacts: true,
            leads: true,
            opportunities: true
          }
        }
      },
      orderBy: {
        name: 'asc'
      },
      take: limit
    });
  }

  /**
   * Get company statistics
   */
  async getCompanyStats(tenantId: string): Promise<{
    totalCompanies: number;
    companiesWithContacts: number;
    companiesWithLeads: number;
    companiesWithOpportunities: number;
    topIndustries: Array<{ industry: string; count: number }>;
    companySizes: Array<{ size: string; count: number }>;
  }> {
    const [
      totalCompanies,
      companiesWithContacts,
      companiesWithLeads,
      companiesWithOpportunities,
      topIndustries,
      companySizes
    ] = await Promise.all([
      // Total companies
      prisma.company.count({
        where: { tenantId }
      }),
      
      // Companies with contacts
      prisma.company.count({
        where: {
          tenantId,
          contacts: {
            some: {}
          }
        }
      }),
      
      // Companies with leads
      prisma.company.count({
        where: {
          tenantId,
          leads: {
            some: {}
          }
        }
      }),
      
      // Companies with opportunities
      prisma.company.count({
        where: {
          tenantId,
          opportunities: {
            some: {}
          }
        }
      }),
      
      // Top industries
      prisma.company.groupBy({
        by: ['industry'],
        where: {
          tenantId,
          industry: {
            not: null
          }
        },
        _count: {
          industry: true
        },
        orderBy: {
          _count: {
            industry: 'desc'
          }
        },
        take: 5
      }),
      
      // Company sizes
      prisma.company.groupBy({
        by: ['size'],
        where: {
          tenantId,
          size: {
            not: null
          }
        },
        _count: {
          size: true
        },
        orderBy: {
          _count: {
            size: 'desc'
          }
        }
      })
    ]);

    return {
      totalCompanies,
      companiesWithContacts,
      companiesWithLeads,
      companiesWithOpportunities,
      topIndustries: topIndustries.map(item => ({
        industry: item.industry || 'Unknown',
        count: item._count.industry
      })),
      companySizes: companySizes.map(item => ({
        size: item.size || 'Unknown',
        count: item._count.size
      }))
    };
  }

  /**
   * Trigger company creation notification
   */
  private async triggerCompanyCreationNotification(
    company: CompanyWithRelations,
    tenantId: string
  ): Promise<void> {
    try {
      const targetUsers = [];

      // Notify company owner if assigned and different from creator
      if (company.ownerId && company.ownerId !== company.createdById) {
        targetUsers.push(company.ownerId);
      }

      // Notify sales team members and managers
      const salesTeam = await prisma.tenantUser.findMany({
        where: {
          tenantId,
          OR: [
            { role: { in: ['Sales Rep', 'Sales Manager', 'Manager', 'Admin'] } },
            { isTenantAdmin: true }
          ],
          status: 'active'
        },
        select: { userId: true }
      });

      targetUsers.push(...salesTeam.map(member => member.userId));

      // Remove duplicates and exclude creator if they're already in the list
      const uniqueTargetUsers = [...new Set(targetUsers)].filter(userId => userId !== company.createdById);

      for (const userId of uniqueTargetUsers) {
        await this.notificationService.createNotification({
          tenantId,
          userId,
          type: 'company_created',
          category: 'system',
          title: 'New Company Created',
          message: `New company "${company.name}" has been added to the system`,
          data: {
            companyId: company.id,
            companyName: company.name,
            companyIndustry: company.industry,
            companySize: company.size,
            companyWebsite: company.website,
            companyLocation: company.location,
            ownerId: company.ownerId,
            ownerName: company.owner ?
              `${company.owner.firstName} ${company.owner.lastName}` : null,
            createdBy: company.createdById,
            createdByName: company.createdBy ?
              `${company.createdBy.firstName} ${company.createdBy.lastName}` : null
          },
          actionUrl: `/companies/${company.id}`,
          actionLabel: 'View Company'
        });
      }

      // Notify the company owner specifically if assigned
      if (company.ownerId && company.ownerId !== company.createdById) {
        await this.notificationService.createNotification({
          tenantId,
          userId: company.ownerId,
          type: 'company_assigned',
          category: 'system',
          title: 'Company Assigned to You',
          message: `Company "${company.name}" has been assigned to you`,
          data: {
            companyId: company.id,
            companyName: company.name,
            companyIndustry: company.industry,
            companyWebsite: company.website,
            assignedBy: company.createdById
          },
          actionUrl: `/companies/${company.id}`,
          actionLabel: 'View Company'
        });
      }

    } catch (error) {
      console.error('Failed to send company creation notification:', error);
      // Don't throw error to avoid breaking the main operation
    }
  }
}
