# Process Automation Troubleshooting Guide

## Problem: Automation Status Always Empty

If the automation status shows as empty when changing opportunity status, follow this troubleshooting guide:

### Quick Diagnosis

1. **Check Database Tables**
   ```bash
   # Run this to create automation tables
   npm run db:push
   ```

2. **Setup Default Triggers**
   ```bash
   # Run the quick setup script
   node src/scripts/setup-automation-quick.ts
   ```

3. **Test Automation**
   - Go to any opportunity page
   - Click the "Test" button in the Automation Status section
   - Or manually change opportunity stage to "Closed Won"

### Step-by-Step Troubleshooting

#### Step 1: Verify Database Schema

**Problem**: Automation tables don't exist
**Solution**:
```bash
# Check if tables exist
npx prisma db execute --stdin <<< "SELECT name FROM sqlite_master WHERE type='table' AND name LIKE 'automation_%';"

# Create tables if missing
npm run db:push
```

#### Step 2: Check for Automation Triggers

**Problem**: No automation triggers configured
**Diagnosis**:
```bash
# Check automation health
curl http://localhost:3000/api/automation/health
```

**Solution**:
```bash
# Run setup script
node src/scripts/setup-automation-quick.ts
```

#### Step 3: Verify Opportunity Updates

**Problem**: Opportunity updates not triggering automation
**Check**: Look at browser console for errors when updating opportunities

**Common Issues**:
- API errors (check Network tab)
- Permission errors
- Database connection issues

#### Step 4: Test Manual Trigger

**Problem**: Automation not executing
**Test**:
1. Go to any opportunity page
2. Look for "Automation Status" section
3. Click "Test" button
4. Check for success/error messages

#### Step 5: Check Execution Logs

**Problem**: Triggers exist but not executing
**Check**:
1. Visit `/admin/automation`
2. Look at "Executions" tab
3. Check for failed executions

### Common Issues and Solutions

#### Issue 1: "Automation tables not found" Error

**Symptoms**:
- 503 error when accessing automation APIs
- "Setup Required" message in UI

**Solution**:
```bash
# Create database tables
npm run db:push

# Verify tables created
npx prisma studio
# Check for automation_triggers and automation_executions tables
```

#### Issue 2: "No triggers configured" Warning

**Symptoms**:
- Automation status shows "No automation activity"
- No triggers in admin dashboard

**Solution**:
```bash
# Run setup script
node src/scripts/setup-automation-quick.ts

# Or manually create triggers via admin dashboard
```

#### Issue 3: Triggers Exist But Not Executing

**Symptoms**:
- Triggers visible in admin dashboard
- No executions when changing opportunity status

**Diagnosis**:
1. Check trigger conditions match exactly
2. Verify trigger is active
3. Check for errors in server logs

**Solution**:
```bash
# Test specific trigger
curl -X POST http://localhost:3000/api/automation/test-trigger \
  -H "Content-Type: application/json" \
  -d '{"opportunityId": "your-opp-id", "newStatus": "Closed Won"}'
```

#### Issue 4: Permission Errors

**Symptoms**:
- 403 Forbidden errors
- "Access Denied" messages

**Solution**:
1. Ensure user has admin permissions
2. Check user has SETTINGS resource access
3. Verify tenant membership

#### Issue 5: Database Connection Issues

**Symptoms**:
- 500 errors on automation APIs
- Database connection timeouts

**Solution**:
1. Check DATABASE_URL in .env
2. Verify database server is running
3. Test connection: `npx prisma db execute --stdin <<< "SELECT 1;"`

### Testing Automation System

#### Manual Testing Steps

1. **Create Test Opportunity**:
   - Go to `/opportunities/new`
   - Create opportunity with stage "Negotiation"
   - Save opportunity

2. **Test Status Change**:
   - Edit opportunity
   - Change stage to "Closed Won"
   - Save changes

3. **Verify Automation**:
   - Check "Automation Status" section
   - Should show execution details
   - Check for created handovers

#### API Testing

```bash
# Test automation health
curl http://localhost:3000/api/automation/health

# Get test opportunities
curl http://localhost:3000/api/automation/test-trigger

# Manual trigger test
curl -X POST http://localhost:3000/api/automation/test-trigger \
  -H "Content-Type: application/json" \
  -d '{"opportunityId": "opp-123", "newStatus": "Closed Won"}'
```

### Debugging Tools

#### 1. Browser Console
- Check for JavaScript errors
- Look for failed API calls
- Monitor network requests

#### 2. Server Logs
- Check application logs for automation errors
- Look for database connection issues
- Monitor trigger execution logs

#### 3. Database Inspection
```bash
# Open Prisma Studio
npx prisma studio

# Check automation tables:
# - automation_triggers
# - automation_executions
```

#### 4. API Health Check
Visit: `http://localhost:3000/api/automation/health`

### Expected Behavior

When automation is working correctly:

1. **Opportunity Status Change**:
   - User changes opportunity stage
   - Automation triggers execute automatically
   - Executions logged in database

2. **UI Updates**:
   - "Automation Status" section shows activity
   - Recent executions displayed
   - Success/failure status visible

3. **Created Resources**:
   - Handovers created for "Closed Won"
   - Notifications sent to team members
   - Audit trail maintained

### Getting Help

If automation still doesn't work after following this guide:

1. **Check Setup**:
   ```bash
   node src/scripts/test-automation-trigger.ts
   ```

2. **Review Logs**:
   - Check browser console
   - Review server logs
   - Inspect database state

3. **Verify Configuration**:
   - Database connection
   - User permissions
   - Tenant setup

4. **Test Components**:
   - API endpoints
   - Database queries
   - UI components

The automation system should work reliably once properly set up. Most issues are related to missing database tables or unconfigured triggers.
