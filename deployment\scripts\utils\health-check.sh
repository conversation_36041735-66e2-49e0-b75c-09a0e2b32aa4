#!/bin/bash
# =============================================================================
# CRM Platform Health Check Script
# Comprehensive health monitoring for all services
# =============================================================================

set -euo pipefail

# -----------------------------------------------------------------------------
# Configuration
# -----------------------------------------------------------------------------
readonly SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
readonly PROJECT_ROOT="$(cd "${SCRIPT_DIR}/../../.." && pwd)"
readonly DEPLOYMENT_DIR="$(cd "${SCRIPT_DIR}/../.." && pwd)"

COMPOSE_FILE="${DEPLOYMENT_DIR}/docker/docker-compose.new.yml"
ENVIRONMENT="${1:-development}"
VERBOSE="${VERBOSE:-false}"

# Colors for output
readonly RED='\033[0;31m'
readonly GREEN='\033[0;32m'
readonly YELLOW='\033[1;33m'
readonly BLUE='\033[0;34m'
readonly NC='\033[0m'

# -----------------------------------------------------------------------------
# Utility Functions
# -----------------------------------------------------------------------------
print_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
print_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
print_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
print_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# -----------------------------------------------------------------------------
# Health Check Functions
# -----------------------------------------------------------------------------

# Check if Docker services are running
check_docker_services() {
  print_info "Checking Docker services..."
  
  cd "$PROJECT_ROOT"
  
  local services=("app" "postgres" "redis")
  local all_healthy=true
  
  for service in "${services[@]}"; do
    if docker-compose -f "$COMPOSE_FILE" ps "$service" | grep -q "Up"; then
      print_success "$service is running"
    else
      print_error "$service is not running"
      all_healthy=false
    fi
  done
  
  return $([[ "$all_healthy" == "true" ]] && echo 0 || echo 1)
}

# Check application health endpoint
check_app_health() {
  print_info "Checking application health endpoint..."
  
  local max_attempts=5
  local attempt=1
  
  while [[ $attempt -le $max_attempts ]]; do
    if curl -f -s http://localhost:3010/api/health > /dev/null 2>&1; then
      print_success "Application health endpoint is responding"
      return 0
    fi
    
    print_warning "Health endpoint not responding (attempt $attempt/$max_attempts)"
    sleep 5
    ((attempt++))
  done
  
  print_error "Application health endpoint is not responding"
  return 1
}

# Check database connectivity
check_database() {
  print_info "Checking database connectivity..."
  
  cd "$PROJECT_ROOT"
  
  if docker-compose -f "$COMPOSE_FILE" exec -T postgres pg_isready -U admin -d crm_platform > /dev/null 2>&1; then
    print_success "Database is accessible"
    return 0
  else
    print_error "Database is not accessible"
    return 1
  fi
}

# Check Redis connectivity
check_redis() {
  print_info "Checking Redis connectivity..."
  
  cd "$PROJECT_ROOT"
  
  if docker-compose -f "$COMPOSE_FILE" exec -T redis redis-cli ping | grep -q "PONG"; then
    print_success "Redis is accessible"
    return 0
  else
    print_error "Redis is not accessible"
    return 1
  fi
}

# Check disk space
check_disk_space() {
  print_info "Checking disk space..."
  
  local available_space=$(df / | awk 'NR==2 {print $4}')
  local available_gb=$((available_space / 1024 / 1024))
  
  if [[ $available_gb -gt 5 ]]; then
    print_success "Sufficient disk space available: ${available_gb}GB"
  elif [[ $available_gb -gt 1 ]]; then
    print_warning "Low disk space: ${available_gb}GB available"
  else
    print_error "Critical disk space: ${available_gb}GB available"
    return 1
  fi
}

# Check memory usage
check_memory() {
  print_info "Checking memory usage..."
  
  local memory_info=$(free -m | awk 'NR==2{printf "%.1f", $3*100/$2}')
  local memory_usage=${memory_info%.*}
  
  if [[ $memory_usage -lt 80 ]]; then
    print_success "Memory usage is normal: ${memory_usage}%"
  elif [[ $memory_usage -lt 90 ]]; then
    print_warning "High memory usage: ${memory_usage}%"
  else
    print_error "Critical memory usage: ${memory_usage}%"
    return 1
  fi
}

# Check Docker volumes
check_volumes() {
  print_info "Checking Docker volumes..."
  
  local volumes=("crm-postgres-data" "crm-redis-data")
  
  for volume in "${volumes[@]}"; do
    if docker volume inspect "$volume" > /dev/null 2>&1; then
      print_success "Volume $volume exists"
    else
      print_error "Volume $volume is missing"
      return 1
    fi
  done
}

# Generate health report
generate_report() {
  print_info "Generating health report..."
  
  local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
  local report_file="/tmp/crm-health-report-$(date +%Y%m%d-%H%M%S).txt"
  
  {
    echo "CRM Platform Health Report"
    echo "========================="
    echo "Timestamp: $timestamp"
    echo "Environment: $ENVIRONMENT"
    echo ""
    
    echo "Docker Services:"
    cd "$PROJECT_ROOT"
    docker-compose -f "$COMPOSE_FILE" ps
    echo ""
    
    echo "System Resources:"
    echo "Memory: $(free -h | awk 'NR==2{printf "%s/%s (%.1f%%)", $3,$2,$3*100/$2}')"
    echo "Disk: $(df -h / | awk 'NR==2{printf "%s/%s (%s)", $3,$2,$5}')"
    echo ""
    
    echo "Container Stats:"
    docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}\t{{.BlockIO}}"
    
  } > "$report_file"
  
  print_success "Health report saved to: $report_file"
}

# -----------------------------------------------------------------------------
# Main Function
# -----------------------------------------------------------------------------

main() {
  print_info "🏥 CRM Platform Health Check"
  print_info "============================"
  
  local overall_health=true
  
  # Run all health checks
  check_docker_services || overall_health=false
  check_app_health || overall_health=false
  check_database || overall_health=false
  check_redis || overall_health=false
  check_disk_space || overall_health=false
  check_memory || overall_health=false
  check_volumes || overall_health=false
  
  echo ""
  
  if [[ "$overall_health" == "true" ]]; then
    print_success "🎉 All health checks passed!"
    exit 0
  else
    print_error "❌ Some health checks failed!"
    
    if [[ "${GENERATE_REPORT:-false}" == "true" ]]; then
      generate_report
    fi
    
    exit 1
  fi
}

# -----------------------------------------------------------------------------
# Usage
# -----------------------------------------------------------------------------

usage() {
  cat << EOF
Usage: $0 [ENVIRONMENT] [OPTIONS]

ENVIRONMENTS:
  development    Check development environment (default)
  staging        Check staging environment
  production     Check production environment

OPTIONS:
  --report       Generate detailed health report
  --verbose      Enable verbose output
  --help         Show this help message

EXAMPLES:
  $0                    # Check development environment
  $0 production         # Check production environment
  $0 --report           # Generate health report

EOF
  exit 1
}

# Parse arguments
while [[ $# -gt 0 ]]; do
  case $1 in
    --report)
      GENERATE_REPORT="true"
      shift
      ;;
    --verbose)
      VERBOSE="true"
      shift
      ;;
    --help|-h)
      usage
      ;;
    -*)
      print_error "Unknown option: $1"
      usage
      ;;
    *)
      ENVIRONMENT="$1"
      shift
      ;;
  esac
done

# Run main function
main
