import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { prisma } from '@/lib/prisma';

const checkEmailSchema = z.object({
  email: z.string().email('Invalid email address'),
});

/**
 * @swagger
 * /api/users/check-email:
 *   post:
 *     summary: Check if email is available for registration
 *     description: Validates if an email address is available for new user registration
 *     tags:
 *       - Users
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *                 description: The email address to check
 *                 example: "<EMAIL>"
 *     responses:
 *       200:
 *         description: Email availability status
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 available:
 *                   type: boolean
 *                   description: Whether the email is available for registration
 *                 email:
 *                   type: string
 *                   description: The checked email address
 *                 message:
 *                   type: string
 *                   description: Additional information about availability
 *       400:
 *         description: Validation error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                 details:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       field:
 *                         type: string
 *                       message:
 *                         type: string
 *       500:
 *         description: Internal server error
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const validatedData = checkEmailSchema.parse(body);

    // Check if user exists with this email
    const existingUser = await prisma.user.findUnique({
      where: {
        email: validatedData.email
      },
      select: {
        id: true,
        status: true,
        tenantUsers: {
          select: {
            id: true
          }
        }
      }
    });

    if (!existingUser) {
      return NextResponse.json({
        available: true,
        email: validatedData.email,
        message: 'Email is available for registration'
      });
    }

    // Check if user is inactive (could potentially be reactivated)
    if (existingUser.status === 'inactive') {
      return NextResponse.json({
        available: false,
        email: validatedData.email,
        message: 'Email is associated with an inactive account'
      });
    }

    // Check tenant limit
    const maxTenantsPerEmail = parseInt(process.env.MAX_TENANTS_PER_EMAIL || '5');
    if (existingUser.tenantUsers.length >= maxTenantsPerEmail) {
      return NextResponse.json({
        available: false,
        email: validatedData.email,
        message: `Email has reached the maximum limit of ${maxTenantsPerEmail} organizations`
      });
    }

    // User exists but can join more tenants
    return NextResponse.json({
      available: true,
      email: validatedData.email,
      message: 'Email can be used to join this organization'
    });

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          error: 'Validation failed',
          details: error.errors.map(e => ({
            field: e.path.join('.'),
            message: e.message
          }))
        },
        { status: 400 }
      );
    }

    console.error('Check email error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * GET endpoint for checking email availability via query parameter
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const email = searchParams.get('email');

    if (!email) {
      return NextResponse.json(
        { error: 'Email parameter is required' },
        { status: 400 }
      );
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        { 
          available: false,
          error: 'Invalid email format'
        },
        { status: 400 }
      );
    }

    // Use the same logic as POST
    const existingUser = await prisma.user.findUnique({
      where: { email },
      select: {
        id: true,
        status: true,
        tenantUsers: {
          select: {
            id: true
          }
        }
      }
    });

    if (!existingUser) {
      return NextResponse.json({
        available: true,
        email,
        message: 'Email is available for registration'
      });
    }

    if (existingUser.status === 'inactive') {
      return NextResponse.json({
        available: false,
        email,
        message: 'Email is associated with an inactive account'
      });
    }

    const maxTenantsPerEmail = parseInt(process.env.MAX_TENANTS_PER_EMAIL || '5');
    if (existingUser.tenantUsers.length >= maxTenantsPerEmail) {
      return NextResponse.json({
        available: false,
        email,
        message: `Email has reached the maximum limit of ${maxTenantsPerEmail} organizations`
      });
    }

    return NextResponse.json({
      available: true,
      email,
      message: 'Email can be used to join this organization'
    });

  } catch (error) {
    console.error('Check email error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
