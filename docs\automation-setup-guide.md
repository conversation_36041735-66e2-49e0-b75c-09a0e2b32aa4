# Process Automation Setup Guide

## Quick Setup

The Process Automation system requires database tables to be created. Follow these steps to set it up:

### Step 1: Create Database Tables

Run one of these commands to create the automation tables:

```bash
# Option 1: Push schema changes directly (recommended for development)
npm run db:push

# Option 2: Create and run a migration (recommended for production)
npm run db:migrate
```

### Step 2: Initialize Default Triggers

After the tables are created, set up default automation triggers:

```bash
# Run the setup script
node src/scripts/setup-automation-system.ts
```

### Step 3: Verify Setup

1. Go to **Admin** → **Process Automation** in the application
2. You should see the automation dashboard with default triggers
3. Test by changing an opportunity stage to "Closed Won"

## Troubleshooting

### Error: "Automation tables not found"

**Problem**: The database doesn't have the automation tables yet.

**Solution**: 
```bash
npm run db:push
```

### Error: "No triggers configured"

**Problem**: The automation tables exist but no triggers are set up.

**Solution**:
```bash
node src/scripts/setup-automation-system.ts
```

### Error: Database connection issues

**Problem**: Can't connect to the database.

**Solutions**:
1. Check your `.env` file has correct `DATABASE_URL`
2. Ensure your database server is running
3. Verify database credentials

### Permission Errors

**Problem**: User doesn't have access to automation features.

**Solution**: Ensure the user has admin permissions or `SETTINGS` resource access.

## Health Check

You can check the automation system health by visiting:
```
GET /api/automation/health
```

This endpoint will tell you:
- ✅ Database connection status
- ✅ Table existence
- ✅ Trigger configuration
- ✅ Setup instructions if needed

## Manual Database Setup

If the automated setup doesn't work, you can manually create the tables:

### 1. Check Current Schema

```sql
-- Check if automation tables exist
SELECT table_name 
FROM information_schema.tables 
WHERE table_name IN ('automation_triggers', 'automation_executions');
```

### 2. Create Tables Manually (if needed)

The tables are defined in `prisma/schema.prisma`. Use Prisma to generate and apply:

```bash
# Generate Prisma client
npx prisma generate

# Apply schema changes
npx prisma db push
```

### 3. Verify Tables

```sql
-- Check automation_triggers table
DESCRIBE automation_triggers;

-- Check automation_executions table  
DESCRIBE automation_executions;
```

## Default Triggers Created

The setup script creates these default triggers:

1. **Opportunity Closed Won → Create Handover**
   - Triggers when opportunity stage changes to "Closed Won"
   - Creates handover document automatically
   - Sends notifications to delivery team

2. **Opportunity → Proposal Stage**
   - Triggers when opportunity moves to "Proposal"
   - Notifies sales team

3. **Opportunity → Negotiation Stage**
   - Triggers when opportunity moves to "Negotiation"  
   - High-priority notifications

## Testing the Setup

### Method 1: API Test

```bash
# Test health check
curl http://localhost:3000/api/automation/health

# Create test execution
curl -X POST http://localhost:3000/api/automation/executions/test \
  -H "Content-Type: application/json" \
  -d '{"entityType": "opportunity", "entityId": "test-123"}'
```

### Method 2: UI Test

1. Go to any opportunity in the system
2. Change the stage to "Closed Won"
3. Check the "Automation Status" section on the opportunity page
4. Visit **Admin** → **Process Automation** to see execution logs

## Production Deployment

For production environments:

### 1. Use Migrations

```bash
# Create migration
npx prisma migrate dev --name add-automation-tables

# Apply in production
npx prisma migrate deploy
```

### 2. Environment Variables

Ensure these are set:
```env
DATABASE_URL="your-production-database-url"
NEXTAUTH_SECRET="your-secret-key"
```

### 3. Permissions

Ensure production users have appropriate permissions for automation features.

## Support

If you encounter issues:

1. **Check logs**: Look at server logs for detailed error messages
2. **Health check**: Use `/api/automation/health` endpoint
3. **Database**: Verify database connection and table structure
4. **Permissions**: Ensure user has admin access

For additional help, check the main automation documentation or contact your system administrator.
