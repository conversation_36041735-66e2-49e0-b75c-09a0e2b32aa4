'use client';

import { PermissionGate } from '@/components/ui/permission-gate';
import { PermissionResource, PermissionAction } from '@/types';
import { ModernCompanyForm } from '@/components/companies/modern-company-form';
import { Card, CardContent } from '@/components/ui/card';
import { EmptyState } from '@/components/ui/empty-state';
import { ExclamationTriangleIcon, ArrowLeftIcon } from '@heroicons/react/24/outline';
import { PageLayout } from '@/components/layout/page-layout';
import { Button } from '@/components/ui/button';
import { useRouter } from 'next/navigation';

export default function NewCompanyPage() {
  const router = useRouter();

  const actions = (
    <Button variant="outline" size="sm" onClick={() => router.push('/companies')}>
      <ArrowLeftIcon className="h-4 w-4 mr-2" />
      Back to Companies
    </Button>
  );

  return (
    <PermissionGate
      resource={PermissionResource.COMPANIES}
      action={PermissionAction.CREATE}
      fallback={
        <PageLayout>
          <Card>
            <CardContent className="p-8">
              <EmptyState
                icon={<ExclamationTriangleIcon className="w-12 h-12 text-muted-foreground" />}
                title="Access Denied"
                description="You don't have permission to create companies. Please contact your administrator for access."
                action={{
                  label: 'Back to Companies',
                  onClick: () => router.push('/companies'),
                  variant: 'primary'
                }}
              />
            </CardContent>
          </Card>
        </PageLayout>
      }
    >
      <PageLayout
        title="Add New Company"
        description="Create a new company in your CRM system"
        actions={actions}
      >
        <ModernCompanyForm />
      </PageLayout>
    </PermissionGate>
  );
}
