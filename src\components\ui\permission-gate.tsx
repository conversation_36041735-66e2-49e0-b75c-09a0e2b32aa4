'use client';

import React from 'react';
import { usePermissions } from '@/components/providers/permission-provider';
import { PermissionAction, PermissionResource } from '@/types';
import { LoadingSpinner } from './loading-spinner';

export interface PermissionGateProps {
  resource: PermissionResource;
  action: PermissionAction;
  resourceOwnerId?: string;
  requireOwnership?: boolean;
  ownerId?: string | null;
  requireAll?: boolean;
  fallback?: React.ReactNode;
  loadingFallback?: React.ReactNode;
  children: React.ReactNode;
}

/**
 * Component that conditionally renders children based on user permissions
 */
export function PermissionGate({
  resource,
  action,
  resourceOwnerId,
  requireOwnership,
  ownerId,
  fallback = null,
  loadingFallback = <LoadingSpinner />,
  children
}: PermissionGateProps) {
  const { canAccessResource, isLoading } = usePermissions();

  if (isLoading) {
    return <>{loadingFallback}</>;
  }

  // Use ownerId if requireOwnership is true, otherwise use resourceOwnerId
  const ownerIdToCheck = requireOwnership ? ownerId : resourceOwnerId;
  const hasAccess = canAccessResource(resource, action, ownerIdToCheck);

  if (!hasAccess) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
}

/**
 * Component for checking multiple permissions
 */
export interface MultiPermissionGateProps {
  permissions: Array<{ resource: PermissionResource; action: PermissionAction }>;
  requireAll?: boolean;
  fallback?: React.ReactNode;
  loadingFallback?: React.ReactNode;
  children: React.ReactNode;
}

export function MultiPermissionGate({
  permissions,
  requireAll = false,
  fallback = null,
  loadingFallback = <LoadingSpinner />,
  children
}: MultiPermissionGateProps) {
  const { hasAnyPermission, hasAllPermissions, isLoading } = usePermissions();

  if (isLoading) {
    return <>{loadingFallback}</>;
  }

  const hasAccess = requireAll 
    ? hasAllPermissions(permissions)
    : hasAnyPermission(permissions);

  if (!hasAccess) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
}

/**
 * Component for tenant admin only content
 */
export interface TenantAdminGateProps {
  fallback?: React.ReactNode;
  loadingFallback?: React.ReactNode;
  children: React.ReactNode;
}

export function TenantAdminGate({
  fallback = null,
  loadingFallback = <LoadingSpinner />,
  children
}: TenantAdminGateProps) {
  const { isTenantAdmin, isLoading } = usePermissions();

  if (isLoading) {
    return <>{loadingFallback}</>;
  }

  if (!isTenantAdmin) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
}

/**
 * Access denied component
 */
export interface AccessDeniedProps {
  message?: string;
  showIcon?: boolean;
  className?: string;
}

export function AccessDenied({
  message = "You don't have permission to access this content.",
  showIcon = true,
  className = ""
}: AccessDeniedProps) {
  return (
    <div className={`flex flex-col items-center justify-center p-8 text-center ${className}`}>
      {showIcon && (
        <div className="w-16 h-16 mb-4 text-gray-400">
          <svg
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 15v2m0 0v2m0-2h2m-2 0H10m2-5V9m0 0V7m0 2h2m-2 0H10m2-5a9 9 0 110 18 9 9 0 010-18z"
            />
          </svg>
        </div>
      )}
      <h3 className="text-lg font-medium text-gray-900 mb-2">Access Denied</h3>
      <p className="text-gray-600 max-w-md">{message}</p>
    </div>
  );
}

/**
 * Permission-aware button component
 */
export interface PermissionButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  resource: PermissionResource;
  action: PermissionAction;
  resourceOwnerId?: string;
  fallback?: React.ReactNode;
  loadingFallback?: React.ReactNode;
  children: React.ReactNode;
}

export function PermissionButton({
  resource,
  action,
  resourceOwnerId,
  fallback = null,
  loadingFallback = <LoadingSpinner />,
  children,
  ...buttonProps
}: PermissionButtonProps) {
  const { canAccessResource, isLoading } = usePermissions();

  if (isLoading) {
    return <>{loadingFallback}</>;
  }

  const hasAccess = canAccessResource(resource, action, resourceOwnerId);

  if (!hasAccess) {
    return <>{fallback}</>;
  }

  return <button {...buttonProps}>{children}</button>;
}

/**
 * Permission-aware link component
 */
export interface PermissionLinkProps {
  resource: PermissionResource;
  action: PermissionAction;
  href: string;
  resourceOwnerId?: string;
  fallback?: React.ReactNode;
  loadingFallback?: React.ReactNode;
  className?: string;
  children: React.ReactNode;
}

export function PermissionLink({
  resource,
  action,
  href,
  resourceOwnerId,
  fallback = null,
  loadingFallback = <LoadingSpinner />,
  className = "",
  children
}: PermissionLinkProps) {
  const { canAccessResource, isLoading } = usePermissions();

  if (isLoading) {
    return <>{loadingFallback}</>;
  }

  const hasAccess = canAccessResource(resource, action, resourceOwnerId);

  if (!hasAccess) {
    return <>{fallback}</>;
  }

  return (
    <a href={href} className={className}>
      {children}
    </a>
  );
}

/**
 * HOC for wrapping components with permission checking
 */
export function withPermission<P extends object>(
  Component: React.ComponentType<P>,
  resource: PermissionResource,
  action: PermissionAction,
  options?: {
    fallback?: React.ReactNode;
    loadingFallback?: React.ReactNode;
  }
) {
  return function PermissionWrappedComponent(props: P) {
    return (
      <PermissionGate
        resource={resource}
        action={action}
        fallback={options?.fallback}
        loadingFallback={options?.loadingFallback}
      >
        <Component {...props} />
      </PermissionGate>
    );
  };
}

/**
 * Hook for conditional rendering based on permissions
 */
export function useConditionalRender(
  resource: PermissionResource,
  action: PermissionAction,
  resourceOwnerId?: string
) {
  const { canAccessResource, isLoading } = usePermissions();

  return {
    canRender: canAccessResource(resource, action, resourceOwnerId),
    isLoading,
    renderIf: (component: React.ReactNode, fallback?: React.ReactNode) => {
      if (isLoading) return <LoadingSpinner />;
      return canAccessResource(resource, action, resourceOwnerId) ? component : (fallback || null);
    }
  };
}
