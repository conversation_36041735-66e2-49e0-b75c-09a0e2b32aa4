import { NextRequest, NextResponse } from 'next/server';
import { withPermission, AuthenticatedRequest } from '@/lib/permission-middleware';
import { DocumentTemplateManagementService } from '@/services/document-template-management';
import { PermissionResource, PermissionAction } from '@/types';

interface RouteParams {
  params: Promise<{ id: string }>;
}

/**
 * GET /api/document-templates/[id]/preview
 * Get template preview information including structure and placeholders
 */
export const GET = withPermission({
  resource: PermissionResource.PROPOSALS,
  action: PermissionAction.READ,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest, { params }: RouteParams) => {
  try {
    const { id: templateId } = await params;

    // Get the template
    const templateService = new DocumentTemplateManagementService();
    const template = await templateService.getTemplate(templateId, req.tenantId);

    if (!template) {
      return NextResponse.json(
        { error: 'Template not found' },
        { status: 404 }
      );
    }

    // Extract preview information
    const previewData = {
      template: {
        id: template.id,
        name: template.name,
        description: template.description,
        templateType: template.templateType,
        category: template.category,
        isActive: template.isActive,
        usageCount: template.usageCount,
        lastUsedAt: template.lastUsedAt,
        createdAt: template.createdAt,
        updatedAt: template.updatedAt
      },
      styling: template.styling,
      branding: template.branding,
      variables: template.variables,
      hasHtmlTemplate: !!template.htmlTemplate
    };

    return NextResponse.json({
      success: true,
      data: previewData,
      message: 'Template preview retrieved successfully'
    });

  } catch (error) {
    console.error('Get template preview error:', error);
    
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to get template preview' },
      { status: 500 }
    );
  }
});


