/**
 * Language detection utilities for content enhancement
 */

export type SupportedLanguage = 'en' | 'ar' | 'mixed';

/**
 * Detect the primary language of text content
 */
export function detectLanguage(text: string): SupportedLanguage {
  if (!text || text.trim().length === 0) {
    return 'en'; // Default to English for empty content
  }

  // Remove common punctuation and numbers for better detection
  const cleanText = text.replace(/[0-9\s\.,;:!?\-\(\)\[\]"']/g, '');
  
  if (cleanText.length === 0) {
    return 'en'; // Default to English if only punctuation/numbers
  }

  // Count Arabic characters (Unicode ranges for Arabic script)
  const arabicChars = cleanText.match(/[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]/g);
  const arabicCount = arabicChars ? arabicChars.length : 0;

  // Count Latin characters (basic Latin + Latin-1 supplement)
  const latinChars = cleanText.match(/[a-zA-Z\u00C0-\u00FF]/g);
  const latinCount = latinChars ? latinChars.length : 0;

  const totalChars = cleanText.length;
  const arabicRatio = arabicCount / totalChars;
  const latinRatio = latinCount / totalChars;

  // Determine language based on character ratios
  if (arabicRatio > 0.6) {
    return 'ar';
  } else if (latinRatio > 0.6) {
    return 'en';
  } else if (arabicRatio > 0.3 && latinRatio > 0.3) {
    return 'mixed';
  } else if (arabicRatio > latinRatio) {
    return 'ar';
  } else {
    return 'en';
  }
}

/**
 * Get language-specific instructions for AI prompts
 */
export function getLanguageInstructions(language: SupportedLanguage): string {
  switch (language) {
    case 'ar':
      return `
CRITICAL: Respond ONLY in Arabic (العربية). Do not use any English words or phrases.
- Use proper Arabic grammar and vocabulary
- Maintain right-to-left text direction
- Use appropriate Arabic punctuation (، ؛ ؟ !)
- Keep the same Arabic writing style and formality level`;

    case 'en':
      return `
CRITICAL: Respond ONLY in English. Do not use any Arabic or other language words.
- Use proper English grammar and vocabulary
- Maintain left-to-right text direction
- Use appropriate English punctuation
- Keep the same English writing style and formality level`;

    case 'mixed':
      return `
CRITICAL: Maintain the EXACT same language mix as the original content.
- If a sentence is in Arabic, keep it in Arabic
- If a sentence is in English, keep it in English
- Do not translate between languages
- Preserve the original language distribution and flow`;

    default:
      return `
CRITICAL: Maintain the same language as the original content.
- Do not change the language of the content
- Preserve original vocabulary and style`;
  }
}

/**
 * Get language name for display purposes
 */
export function getLanguageName(language: SupportedLanguage): string {
  switch (language) {
    case 'ar':
      return 'Arabic (العربية)';
    case 'en':
      return 'English';
    case 'mixed':
      return 'Mixed (Arabic & English)';
    default:
      return 'Unknown';
  }
}

/**
 * Validate that the enhanced content maintains the original language
 */
export function validateLanguageConsistency(
  originalText: string,
  enhancedText: string
): { isConsistent: boolean; originalLang: SupportedLanguage; enhancedLang: SupportedLanguage; warning?: string } {
  const originalLang = detectLanguage(originalText);
  const enhancedLang = detectLanguage(enhancedText);

  let isConsistent = true;
  let warning: string | undefined;

  if (originalLang !== enhancedLang) {
    isConsistent = false;
    warning = `Language mismatch detected: Original content is ${getLanguageName(originalLang)} but enhanced content is ${getLanguageName(enhancedLang)}`;
  }

  // Additional checks for mixed content
  if (originalLang === 'mixed' || enhancedLang === 'mixed') {
    // For mixed content, we need more sophisticated validation
    // This is a simplified check - in production, you might want more detailed analysis
    const originalArabicRatio = getArabicRatio(originalText);
    const enhancedArabicRatio = getArabicRatio(enhancedText);
    
    if (Math.abs(originalArabicRatio - enhancedArabicRatio) > 0.3) {
      isConsistent = false;
      warning = `Language distribution changed significantly: Original had ${(originalArabicRatio * 100).toFixed(1)}% Arabic, enhanced has ${(enhancedArabicRatio * 100).toFixed(1)}% Arabic`;
    }
  }

  return {
    isConsistent,
    originalLang,
    enhancedLang,
    warning
  };
}

/**
 * Helper function to calculate Arabic character ratio
 */
function getArabicRatio(text: string): number {
  const cleanText = text.replace(/[0-9\s\.,;:!?\-\(\)\[\]"']/g, '');
  if (cleanText.length === 0) return 0;

  const arabicChars = cleanText.match(/[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]/g);
  const arabicCount = arabicChars ? arabicChars.length : 0;
  
  return arabicCount / cleanText.length;
}

/**
 * Get sample text for language detection testing
 */
export function getLanguageSamples() {
  return {
    arabic: 'هذا نص تجريبي باللغة العربية لاختبار نظام الكشف عن اللغة',
    english: 'This is a sample text in English for testing the language detection system',
    mixed: 'This is mixed content هذا محتوى مختلط with both languages في نفس النص'
  };
}
