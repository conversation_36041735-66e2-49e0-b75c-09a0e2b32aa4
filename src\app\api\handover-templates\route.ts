import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { handoverChecklistService } from '@/services/handover-checklist';
import { z } from 'zod';

// Request validation schemas
const getTemplatesQuerySchema = z.object({
  category: z.string().optional(),
  search: z.string().optional(),
  page: z.coerce.number().int().positive().default(1),
  limit: z.coerce.number().int().positive().max(100).default(20),
});

const createTemplateSchema = z.object({
  name: z.string().min(1, 'Name is required').max(255),
  description: z.string().optional(),
  category: z.enum(['general', 'technical', 'legal', 'financial']).default('general'),
  items: z.array(z.object({
    title: z.string().min(1, 'Title is required'),
    description: z.string().optional(),
    category: z.enum(['general', 'technical', 'legal', 'financial', 'client']).default('general'),
    orderIndex: z.number().int().min(0),
    isRequired: z.boolean().default(true),
    dependsOnIds: z.array(z.string()).default([]),
  })).default([]),
  estimatedDuration: z.number().int().positive().optional(),
  isDefault: z.boolean().default(false),
});

/**
 * GET /api/handover-templates - Get handover templates
 */
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get tenantId from user's tenants array (users have one tenant in this system)
    let tenantId: string | null = null;
    if (session.user.tenants && session.user.tenants.length > 0) {
      tenantId = session.user.tenants[0].id;
    }

    if (!tenantId) {
      return NextResponse.json({ error: 'Tenant not found' }, { status: 400 });
    }

    // Parse and validate query parameters
    const url = new URL(request.url);
    const queryParams = Object.fromEntries(url.searchParams.entries());
    
    const validatedQuery = getTemplatesQuerySchema.parse(queryParams);

    const result = await handoverChecklistService.getTemplates(tenantId, validatedQuery);

    return NextResponse.json(result);
  } catch (error) {
    console.error('Error fetching handover templates:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid query parameters', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to fetch handover templates' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/handover-templates - Create a new handover template
 */
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get tenantId from user's tenants array (users have one tenant in this system)
    let tenantId: string | null = null;
    if (session.user.tenants && session.user.tenants.length > 0) {
      tenantId = session.user.tenants[0].id;
    }

    if (!tenantId) {
      return NextResponse.json({ error: 'Tenant not found' }, { status: 400 });
    }

    const body = await request.json();
    const validatedData = createTemplateSchema.parse(body);

    const template = await handoverChecklistService.createTemplate(
      tenantId,
      validatedData,
      session.user.id
    );

    return NextResponse.json(template, { status: 201 });
  } catch (error) {
    console.error('Error creating handover template:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request data', details: error.errors },
        { status: 400 }
      );
    }

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to create handover template' },
      { status: 500 }
    );
  }
}
