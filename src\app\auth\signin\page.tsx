'use client';

import { AuthLayout } from '@/components/auth/auth-layout';
import { LoginForm } from '@/components/auth/login-form';
import { useRedirectIfAuthenticated } from '@/hooks/use-auth';

export default function SignInPage() {
  // Redirect if already authenticated
  useRedirectIfAuthenticated();

  return (
    <AuthLayout
      title="Login to your account"
      subtitle="Enter your email below to login to your account"
    >
      <LoginForm />
    </AuthLayout>
  );
}
