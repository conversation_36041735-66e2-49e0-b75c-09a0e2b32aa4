'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  RefreshCw, 
  Trash2, 
  Database, 
  Clock, 
  Users, 
  Shield, 
  Activity,
  CheckCircle,
  AlertTriangle,
  Info
} from 'lucide-react';
import { useEnhancedPermissions } from '@/components/providers/enhanced-permission-provider';
import { PermissionCacheService } from '@/lib/cache/permission-cache';

interface CacheStats {
  totalCaches: number;
  validCaches: number;
  expiredCaches: number;
  totalSize: number;
  hitRate: number;
  lastUpdate: string | null;
  isFromCache: boolean;
  enabled: boolean;
}

interface WebSocketStatus {
  connected: boolean;
  connectedClients: number;
  lastActivity: string | null;
}

export function PermissionCacheManager() {
  const { 
    cacheStats, 
    clearCache, 
    refreshPermissions, 
    isLoading,
    isCacheEnabled,
    cacheHitRate,
    lastCacheUpdate,
    isFromCache
  } = useEnhancedPermissions();

  const [stats, setStats] = useState<CacheStats | null>(null);
  const [wsStatus, setWsStatus] = useState<WebSocketStatus | null>(null);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [isClearing, setIsClearing] = useState(false);
  const [lastAction, setLastAction] = useState<string | null>(null);

  // Load cache statistics
  const loadStats = async () => {
    try {
      const cacheStatsData = cacheStats();
      setStats(cacheStatsData);
    } catch (error) {
      console.error('Failed to load cache stats:', error);
    }
  };

  // Load WebSocket status
  const loadWebSocketStatus = async () => {
    try {
      const response = await fetch('/api/socket/permissions');
      if (response.ok) {
        const data = await response.json();
        setWsStatus({
          connected: true,
          connectedClients: data.connectedClients || 0,
          lastActivity: data.timestamp
        });
      } else {
        setWsStatus({
          connected: false,
          connectedClients: 0,
          lastActivity: null
        });
      }
    } catch (error) {
      setWsStatus({
        connected: false,
        connectedClients: 0,
        lastActivity: null
      });
    }
  };

  // Handle cache refresh
  const handleRefresh = async () => {
    setIsRefreshing(true);
    try {
      await refreshPermissions(true);
      setLastAction('Cache refreshed successfully');
      await loadStats();
    } catch (error) {
      setLastAction('Failed to refresh cache');
    } finally {
      setIsRefreshing(false);
    }
  };

  // Handle cache clear
  const handleClear = async () => {
    setIsClearing(true);
    try {
      clearCache();
      PermissionCacheService.clearAllCaches();
      setLastAction('Cache cleared successfully');
      await loadStats();
    } catch (error) {
      setLastAction('Failed to clear cache');
    } finally {
      setIsClearing(false);
    }
  };

  // Handle cleanup expired caches
  const handleCleanup = async () => {
    try {
      const cleanedCount = PermissionCacheService.cleanupExpiredCaches();
      setLastAction(`Cleaned up ${cleanedCount} expired caches`);
      await loadStats();
    } catch (error) {
      setLastAction('Failed to cleanup expired caches');
    }
  };

  // Load data on component mount and set up refresh interval
  useEffect(() => {
    loadStats();
    loadWebSocketStatus();

    const interval = setInterval(() => {
      loadStats();
      loadWebSocketStatus();
    }, 30000); // Refresh every 30 seconds

    return () => clearInterval(interval);
  }, []);

  // Format file size
  const formatSize = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // Format date
  const formatDate = (dateString: string | null): string => {
    if (!dateString) return 'Never';
    return new Date(dateString).toLocaleString();
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Permission Cache Manager</h2>
          <p className="text-muted-foreground">
            Monitor and manage permission caching and real-time updates
          </p>
        </div>
        <div className="flex gap-2">
          <Button
            onClick={handleRefresh}
            disabled={isRefreshing || isLoading}
            variant="outline"
            size="sm"
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button
            onClick={handleClear}
            disabled={isClearing}
            variant="destructive"
            size="sm"
          >
            <Trash2 className="h-4 w-4 mr-2" />
            Clear Cache
          </Button>
        </div>
      </div>

      {lastAction && (
        <Alert>
          <Info className="h-4 w-4" />
          <AlertDescription>{lastAction}</AlertDescription>
        </Alert>
      )}

      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="realtime">Real-time</TabsTrigger>
          <TabsTrigger value="maintenance">Maintenance</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Cache Status</CardTitle>
                <Database className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {isCacheEnabled ? (
                    <Badge variant="default" className="bg-green-500">
                      <CheckCircle className="h-3 w-3 mr-1" />
                      Enabled
                    </Badge>
                  ) : (
                    <Badge variant="destructive">
                      <AlertTriangle className="h-3 w-3 mr-1" />
                      Disabled
                    </Badge>
                  )}
                </div>
                <p className="text-xs text-muted-foreground">
                  Current cache state
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Hit Rate</CardTitle>
                <Activity className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{cacheHitRate.toFixed(1)}%</div>
                <Progress value={cacheHitRate} className="mt-2" />
                <p className="text-xs text-muted-foreground">
                  Cache effectiveness
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Caches</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats?.totalCaches || 0}</div>
                <p className="text-xs text-muted-foreground">
                  {stats?.validCaches || 0} valid, {stats?.expiredCaches || 0} expired
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Last Update</CardTitle>
                <Clock className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-sm font-bold">
                  {formatDate(lastCacheUpdate)}
                </div>
                <p className="text-xs text-muted-foreground">
                  {isFromCache ? 'From cache' : 'From API'}
                </p>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Cache Details</CardTitle>
              <CardDescription>
                Detailed information about permission cache usage
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">Cache Size</span>
                  <span className="text-sm">{formatSize(stats?.totalSize || 0)}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">Valid Caches</span>
                  <span className="text-sm">{stats?.validCaches || 0}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">Expired Caches</span>
                  <span className="text-sm">{stats?.expiredCaches || 0}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">Encryption</span>
                  <Badge variant="outline">Enabled</Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">Integrity Validation</span>
                  <Badge variant="outline">Enabled</Badge>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="performance" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Performance Metrics</CardTitle>
              <CardDescription>
                Cache performance and optimization statistics
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div>
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-sm font-medium">Cache Hit Rate</span>
                    <span className="text-sm">{cacheHitRate.toFixed(1)}%</span>
                  </div>
                  <Progress value={cacheHitRate} />
                  <p className="text-xs text-muted-foreground mt-1">
                    Higher is better. Target: &gt;80%
                  </p>
                </div>

                <div>
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-sm font-medium">Cache Efficiency</span>
                    <span className="text-sm">
                      {stats ? ((stats.validCaches / Math.max(stats.totalCaches, 1)) * 100).toFixed(1) : 0}%
                    </span>
                  </div>
                  <Progress 
                    value={stats ? (stats.validCaches / Math.max(stats.totalCaches, 1)) * 100 : 0} 
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    Percentage of valid vs total caches
                  </p>
                </div>

                <div className="grid grid-cols-2 gap-4 pt-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">
                      {stats?.validCaches || 0}
                    </div>
                    <p className="text-sm text-muted-foreground">Valid Caches</p>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-red-600">
                      {stats?.expiredCaches || 0}
                    </div>
                    <p className="text-sm text-muted-foreground">Expired Caches</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="realtime" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Real-time Updates</CardTitle>
              <CardDescription>
                WebSocket connection status and real-time permission synchronization
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">WebSocket Status</span>
                  {wsStatus?.connected ? (
                    <Badge variant="default" className="bg-green-500">
                      <CheckCircle className="h-3 w-3 mr-1" />
                      Connected
                    </Badge>
                  ) : (
                    <Badge variant="destructive">
                      <AlertTriangle className="h-3 w-3 mr-1" />
                      Disconnected
                    </Badge>
                  )}
                </div>

                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Connected Clients</span>
                  <span className="text-sm">{wsStatus?.connectedClients || 0}</span>
                </div>

                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Last Activity</span>
                  <span className="text-sm">{formatDate(wsStatus?.lastActivity)}</span>
                </div>

                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Cross-tab Sync</span>
                  <Badge variant="outline">Enabled</Badge>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="maintenance" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Cache Maintenance</CardTitle>
              <CardDescription>
                Tools for maintaining and optimizing permission cache
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="text-sm font-medium">Cleanup Expired Caches</h4>
                    <p className="text-xs text-muted-foreground">
                      Remove expired cache entries to free up storage
                    </p>
                  </div>
                  <Button onClick={handleCleanup} variant="outline" size="sm">
                    <Trash2 className="h-4 w-4 mr-2" />
                    Cleanup
                  </Button>
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="text-sm font-medium">Force Refresh All</h4>
                    <p className="text-xs text-muted-foreground">
                      Invalidate all caches and fetch fresh permissions
                    </p>
                  </div>
                  <Button 
                    onClick={handleRefresh} 
                    disabled={isRefreshing}
                    variant="outline" 
                    size="sm"
                  >
                    <RefreshCw className={`h-4 w-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
                    Refresh All
                  </Button>
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="text-sm font-medium">Clear All Caches</h4>
                    <p className="text-xs text-muted-foreground">
                      Remove all cached permission data (use with caution)
                    </p>
                  </div>
                  <Button 
                    onClick={handleClear} 
                    disabled={isClearing}
                    variant="destructive" 
                    size="sm"
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    Clear All
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
