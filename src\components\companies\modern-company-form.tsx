'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useSession } from 'next-auth/react';
import * as z from 'zod';
import { Save, X, Building, Users, Briefcase, Loader2 } from 'lucide-react';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { LegacySelect } from '@/components/ui/select';
import { SearchableSelect, SearchableSelectOption } from '@/components/ui/searchable-select';
import { CompanyWithRelations } from '@/services/company-management';
import { motion } from 'framer-motion';

// Form validation schema
const companyFormSchema = z.object({
  name: z.string().min(1, 'Company name is required').max(100, 'Company name must be less than 100 characters'),
  website: z.string().url('Please enter a valid URL').optional().or(z.literal('')),
  industry: z.string().optional(),
  size: z.string().optional(),
  ownerId: z.string().optional(),
  description: z.string().max(500, 'Description must be less than 500 characters').optional(),
});

type CompanyFormValues = z.infer<typeof companyFormSchema>;

interface User {
  id: string;
  firstName: string;
  lastName: string;
}

interface ModernCompanyFormProps {
  company?: CompanyWithRelations;
  onSubmit?: (data: CompanyFormValues) => Promise<void>;
  onCancel?: () => void;
  className?: string;
}

export function ModernCompanyForm({
  company,
  onSubmit,
  onCancel,
  className
}: ModernCompanyFormProps) {
  const router = useRouter();
  const { data: session } = useSession();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [users, setUsers] = useState<User[]>([]);

  // Initialize form with default values or company data if available
  const [formData, setFormData] = useState(() => {
    if (company) {
      return {
        name: company.name ?? '',
        website: company.website ?? '',
        industry: company.industry ?? '',
        size: company.size ?? '',
        ownerId: company.ownerId ?? '',
      };
    }
    return {
      name: '',
      website: '',
      industry: '',
      size: '',
      ownerId: '',
    };
  });

  // Update form data when company prop changes (for edit mode)
  useEffect(() => {
    if (company) {
      const newFormData = {
        name: company.name ?? '',
        website: company.website ?? '',
        industry: company.industry ?? '',
        size: company.size ?? '',
        ownerId: company.ownerId ?? '',
      };
      setFormData(newFormData);
    } else {
      // For new companies, auto-populate current user as owner
      setFormData({
        name: '',
        website: '',
        industry: '',
        size: '',
        ownerId: session?.user?.id || '',
      });
    }
  }, [company, session?.user?.id]);

  // Fetch users for dropdown
  useEffect(() => {
    const fetchUsers = async () => {
      try {
        const usersRes = await fetch('/api/users?limit=100');
        const usersData = await usersRes.json();

        if (usersData.success) {
          setUsers(usersData.data || []);
        }
      } catch (err) {
        console.error('Failed to fetch users:', err);
      }
    };

    fetchUsers();
  }, []);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      if (onSubmit) {
        await onSubmit({
          ...formData,
          ownerId: formData.ownerId || undefined,
        });
      } else {
        // Default submission logic
        const url = company ? `/api/companies/${company.id}` : '/api/companies';
        const method = company ? 'PUT' : 'POST';

        const response = await fetch(url, {
          method,
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            ...formData,
            ownerId: formData.ownerId || undefined,
          }),
        });

        const data = await response.json();

        if (data.success) {
          router.push('/companies');
        } else {
          throw new Error(data.error || 'Failed to save company');
        }
      }
    } catch (err) {
      console.error('Error saving company:', err);
      alert('Failed to save company. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    if (onCancel) {
      onCancel();
    } else {
      router.back();
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const industryOptions = [
    { value: '', label: 'Select an industry (optional)' },
    { value: 'Technology', label: 'Technology' },
    { value: 'Healthcare', label: 'Healthcare' },
    { value: 'Finance', label: 'Finance' },
    { value: 'Manufacturing', label: 'Manufacturing' },
    { value: 'Retail', label: 'Retail' },
    { value: 'Education', label: 'Education' },
    { value: 'Real Estate', label: 'Real Estate' },
    { value: 'Government', label: 'Government' },
    { value: 'Other', label: 'Other' }
  ];

  const sizeOptions = [
    { value: '', label: 'Select company size (optional)' },
    { value: '1-10 employees', label: '1-10 employees' },
    { value: '11-50 employees', label: '11-50 employees' },
    { value: '51-200 employees', label: '51-200 employees' },
    { value: '201-500 employees', label: '201-500 employees' },
    { value: '501-1000 employees', label: '501-1000 employees' },
    { value: '1000+ employees', label: '1000+ employees' }
  ];

  const ownerOptions = [
    { value: '', label: 'Select an owner (optional)' },
    ...users.map(user => ({
      value: user.id,
      label: `${user.firstName} ${user.lastName} ${user.email ? `(${user.email})` : ''}`
    }))
  ];

  // Helper functions to convert data to SearchableSelectOption format
  const getIndustryOptions = (): SearchableSelectOption[] => {
    const options: SearchableSelectOption[] = [
      { value: '', label: 'Select industry (optional)', description: 'Choose the primary industry' }
    ];

    industryOptions.slice(1).forEach(industry => {
      options.push({
        value: industry.value,
        label: industry.label,
        description: `${industry.label} industry sector`
      });
    });

    return options;
  };

  const getSizeOptions = (): SearchableSelectOption[] => {
    const options: SearchableSelectOption[] = [
      { value: '', label: 'Select company size (optional)', description: 'Choose the company size range' }
    ];

    sizeOptions.slice(1).forEach(size => {
      options.push({
        value: size.value,
        label: size.label,
        description: `Company with ${size.label.toLowerCase()}`
      });
    });

    return options;
  };

  const getUserOptions = (): SearchableSelectOption[] => {
    const options: SearchableSelectOption[] = [
      { value: '', label: 'Select an owner (optional)', description: 'Assign a team member to manage this company' }
    ];

    users.forEach(user => {
      options.push({
        value: user.id,
        label: `${user.firstName} ${user.lastName}`,
        description: user.email || undefined
      });
    });

    return options;
  };

  return (
    <motion.div
      className={`max-w-4xl mx-auto space-y-6 ${className}`}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <form onSubmit={handleSubmit} className="space-y-8">
        {/* Basic Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Building className="w-5 h-5" />
              Basic Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="md:col-span-2">
                <div className="space-y-2">
                  <label className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                    Company Name *
                  </label>
                  <Input
                    name="name"
                    value={formData.name}
                    onChange={handleChange}
                    placeholder="Enter company name..."
                    required
                  />
                </div>
              </div>

              <div className="md:col-span-2">
                <div className="space-y-2">
                  <label className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                    Website
                  </label>
                  <Input
                    name="website"
                    type="url"
                    value={formData.website}
                    onChange={handleChange}
                    placeholder="https://example.com"
                  />
                  <p className="text-sm text-muted-foreground">
                    Include the full URL with https://
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Company Details */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Briefcase className="w-5 h-5" />
              Company Details
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <label className="text-sm font-medium">Industry</label>
                <SearchableSelect
                  options={getIndustryOptions()}
                  value={formData.industry}
                  onValueChange={(value) => setFormData(prev => ({ ...prev, industry: value }))}
                  placeholder="Select industry (optional)"
                  searchPlaceholder="Search industries..."
                  emptyMessage="No industries found"
                  allowClear={true}
                  clearLabel="Clear industry"
                />
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Company Size</label>
                <SearchableSelect
                  options={getSizeOptions()}
                  value={formData.size}
                  onValueChange={(value) => setFormData(prev => ({ ...prev, size: value }))}
                  placeholder="Select company size (optional)"
                  searchPlaceholder="Search company sizes..."
                  emptyMessage="No size options found"
                  allowClear={true}
                  clearLabel="Clear company size"
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Organization & Assignment */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="w-5 h-5" />
              Organization & Assignment
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <label className="text-sm font-medium">Owner</label>
                <SearchableSelect
                  options={getUserOptions()}
                  value={formData.ownerId}
                  onValueChange={(value) => setFormData(prev => ({ ...prev, ownerId: value }))}
                  placeholder="Select an owner (optional)"
                  searchPlaceholder="Search users..."
                  emptyMessage="No users found"
                  allowClear={true}
                  clearLabel="Clear owner"
                />
                <p className="text-xs text-muted-foreground">Assign a team member to manage this company</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Form Actions */}
        <div className="flex flex-col sm:flex-row gap-3 sm:justify-end pt-6">
          <Button
            type="button"
            variant="outline"
            onClick={handleCancel}
            disabled={isSubmitting}
            className="sm:w-auto w-full"
          >
            <X className="w-4 h-4 mr-2" />
            Cancel
          </Button>
          <Button
            type="submit"
            variant="default"
            disabled={isSubmitting}
            className="sm:w-auto w-full"
          >
            {isSubmitting && <Loader2 className="w-4 h-4 mr-2 animate-spin" />}
            {!isSubmitting && <Save className="w-4 h-4 mr-2" />}
            {company ? 'Update Company' : 'Create Company'}
          </Button>
        </div>
      </form>
    </motion.div>
  );
}
