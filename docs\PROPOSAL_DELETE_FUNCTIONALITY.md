# Enhanced Proposal Delete Functionality

This document describes the comprehensive proposal deletion system implemented for the opportunity view with confirmation dialogs and bulk operations.

## Overview

The enhanced delete functionality provides:
- **Individual proposal deletion** with detailed confirmation dialogs
- **Bulk proposal deletion** with multi-selection capabilities
- **Related data awareness** showing sections and charts that will be deleted
- **Permission-based access control** ensuring only authorized users can delete
- **Comprehensive error handling** with user-friendly feedback
- **Data integrity protection** with transaction-based operations

## Features Implemented

### 1. Individual Proposal Deletion

#### **Enhanced Confirmation Dialog**
- **Detailed information** about what will be deleted
- **Related data display** showing sections and charts count
- **Custom warnings** specific to proposal deletion
- **Confirmation text requirement** for proposals with content
- **Permission validation** before allowing deletion

#### **Dialog Content**
```typescript
{
  title: 'Delete Proposal',
  description: 'Detailed description with proposal name',
  itemName: proposalTitle,
  itemType: 'proposal',
  relatedData: [
    { type: 'sections', count: X, label: 'sections', description: 'content sections with text and data' },
    { type: 'charts', count: Y, label: 'charts', description: 'diagrams and visualizations' }
  ],
  customWarnings: [
    'All proposal sections and content will be permanently deleted.',
    'Any generated charts and diagrams will be removed.',
    'This action cannot be undone and the proposal cannot be recovered.',
    'Consider downloading the proposal before deletion if you need to keep a copy.',
    'The proposal will be removed from this opportunity permanently.'
  ],
  requireConfirmation: true,
  confirmationText: proposalTitle
}
```

### 2. Bulk Proposal Deletion

#### **Multi-Selection Interface**
- **Select all checkbox** with partial selection support
- **Individual checkboxes** for each proposal
- **Selection counter** showing number of selected proposals
- **Clear selection** button for easy deselection
- **Bulk delete button** with loading states

#### **Bulk Selection Features**
```typescript
// Selection state management
const [selectedProposals, setSelectedProposals] = useState<string[]>([]);

// Selection handlers
const handleSelectProposal = (proposalId: string, checked: boolean) => {
  // Add or remove from selection
};

const handleSelectAll = (checked: boolean) => {
  // Select all or clear all
};

// Selection status indicators
const isAllSelected = proposals.length > 0 && selectedProposals.length === proposals.length;
const isPartiallySelected = selectedProposals.length > 0 && selectedProposals.length < proposals.length;
```

#### **Bulk Delete Confirmation**
- **Aggregated statistics** showing total sections and charts across all selected proposals
- **Enhanced warnings** for bulk operations
- **Confirmation text requirement** with specific format
- **Transaction-based deletion** ensuring data integrity

### 3. API Endpoints

#### **Individual Delete**
```typescript
DELETE /api/proposals/[id]
```

#### **Bulk Delete**
```typescript
DELETE /api/proposals/bulk-delete
{
  "proposalIds": ["id1", "id2", "id3"]
}
```

**Features:**
- **Validation** of proposal IDs and ownership
- **Permission checking** for each proposal
- **Generation status validation** (cannot delete proposals being generated)
- **Transaction-based deletion** for data integrity
- **Comprehensive error handling** with detailed feedback

## User Interface Components

### 1. Bulk Actions Header

```tsx
<div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg border">
  <div className="flex items-center space-x-4">
    <Checkbox
      checked={isAllSelected}
      onCheckedChange={handleSelectAll}
      className={isPartiallySelected ? "data-[state=checked]:bg-blue-600" : ""}
    />
    <span className="text-sm font-medium">
      {selectedProposals.length === 0 
        ? `Select all ${proposals.length} proposals`
        : `${selectedProposals.length} proposals selected`
      }
    </span>
  </div>
  
  {selectedProposals.length > 0 && (
    <div className="flex items-center space-x-2">
      <Button variant="outline" size="sm" onClick={() => setSelectedProposals([])}>
        Clear Selection
      </Button>
      <Button variant="destructive" size="sm" onClick={handleBulkDeleteProposals}>
        Delete Selected ({selectedProposals.length})
      </Button>
    </div>
  )}
</div>
```

### 2. Individual Proposal Cards

```tsx
<Card className="border border-gray-200 hover:border-gray-300 transition-colors">
  <CardContent className="p-6">
    <div className="flex items-start space-x-4">
      {/* Selection Checkbox */}
      <div className="pt-1">
        <Checkbox
          checked={selectedProposals.includes(proposal.id)}
          onCheckedChange={(checked) => handleSelectProposal(proposal.id, checked)}
        />
      </div>
      
      {/* Proposal Content */}
      <div className="flex-1 flex items-start justify-between">
        {/* Proposal details and actions */}
      </div>
    </div>
  </CardContent>
</Card>
```

## Permission Integration

### **Permission Gates**
All delete functionality is protected by permission gates:

```tsx
<PermissionGate
  resource={PermissionResource.PROPOSALS}
  action={PermissionAction.DELETE}
>
  {/* Delete buttons and functionality */}
</PermissionGate>
```

### **API Permission Validation**
```typescript
const hasAccess = await hasPermission(
  PermissionResource.PROPOSALS,
  PermissionAction.DELETE,
  currentTenant.id
);
```

## Error Handling

### **Client-Side Error Handling**
- **User-friendly error messages** with specific details
- **Loading states** during deletion operations
- **Success feedback** with confirmation messages
- **Graceful fallbacks** for network errors

### **Server-Side Error Handling**
- **Validation errors** with detailed field information
- **Permission errors** with clear access denied messages
- **Business logic errors** (e.g., cannot delete generating proposals)
- **Database errors** with transaction rollback

### **Error Response Format**
```typescript
{
  error: "Error message",
  details: {
    notFoundIds: ["id1", "id2"],
    generatingProposals: [
      { id: "id3", title: "Proposal Title" }
    ]
  }
}
```

## Data Integrity

### **Transaction-Based Operations**
```typescript
const result = await prisma.$transaction(async (tx) => {
  // Delete proposals (cascade will handle sections and charts)
  const deletedProposals = await tx.proposal.deleteMany({
    where: {
      id: { in: proposalIds },
      tenantId: currentTenant.id
    }
  });

  return {
    deletedCount: deletedProposals.count,
    deletedProposals: existingProposals.map(p => ({ id: p.id, title: p.title }))
  };
});
```

### **Cascade Deletion**
- **Proposal sections** are automatically deleted
- **Proposal charts** are automatically deleted
- **File associations** are cleaned up
- **Audit trails** are maintained

## Security Considerations

### **Authorization**
- **Tenant isolation** ensures users can only delete their tenant's proposals
- **Permission validation** at both UI and API levels
- **Session validation** for all operations

### **Data Protection**
- **Soft delete option** can be implemented if needed
- **Audit logging** for all deletion operations
- **Backup recommendations** before bulk operations

## Usage Examples

### **Individual Delete**
```typescript
// User clicks delete button in dropdown menu
handleDeleteProposal(proposal.id, proposal.title);

// System shows enhanced confirmation dialog
// User confirms deletion
// Proposal is deleted with success feedback
```

### **Bulk Delete**
```typescript
// User selects multiple proposals
setSelectedProposals(['id1', 'id2', 'id3']);

// User clicks bulk delete button
handleBulkDeleteProposals();

// System shows bulk confirmation dialog with aggregated stats
// User confirms with required confirmation text
// All selected proposals are deleted in a transaction
```

## Testing

### **Test Scenarios**
1. **Individual proposal deletion** with and without content
2. **Bulk proposal deletion** with various selection sizes
3. **Permission validation** for unauthorized users
4. **Error handling** for network failures and invalid data
5. **Data integrity** verification after deletion operations

### **Test Data Requirements**
- Proposals with different content levels (sections, charts)
- Proposals in different generation states
- Users with different permission levels
- Network error simulation scenarios

## Future Enhancements

1. **Soft Delete**: Implement soft delete with recovery options
2. **Deletion Scheduling**: Allow scheduled deletion for future dates
3. **Bulk Operations**: Extend to other bulk operations (status updates, etc.)
4. **Audit Dashboard**: Comprehensive deletion audit and reporting
5. **Undo Functionality**: Short-term undo capability for accidental deletions
