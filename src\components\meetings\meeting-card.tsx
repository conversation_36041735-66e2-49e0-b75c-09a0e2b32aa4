'use client';

import { useState } from 'react';
import { format, formatDistanceToNow, isToday, isTomorrow, isYesterday } from 'date-fns';
import { 
  Calendar, 
  Clock, 
  User, 
  Phone, 
  Video, 
  MapPin, 
  MoreHorizontal, 
  Edit, 
  Trash2, 
  CheckCircle,
  Link,
  MapPinIcon,
  Mail
} from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { ScheduledMeetingWithRelations } from '@/services/scheduled-meeting-service';
import { cn } from '@/lib/utils';

interface MeetingCardProps {
  meeting: ScheduledMeetingWithRelations;
  onEdit?: (meeting: ScheduledMeetingWithRelations) => void;
  onDelete?: (meetingId: string) => void;
  onComplete?: (meeting: ScheduledMeetingWithRelations) => void;
  onSendInvitation?: (meeting: ScheduledMeetingWithRelations) => void;
  onClick?: (meeting: ScheduledMeetingWithRelations) => void;
  showActions?: boolean;
  variant?: 'default' | 'past' | 'upcoming';
  className?: string;
}

export function MeetingCard({
  meeting,
  onEdit,
  onDelete,
  onComplete,
  onSendInvitation,
  onClick,
  showActions = true,
  variant = 'default',
  className
}: MeetingCardProps) {
  const [isLoading, setIsLoading] = useState(false);

  const meetingDate = new Date(meeting.scheduledAt);
  const isUpcoming = meetingDate > new Date() && meeting.status === 'scheduled';
  const isPast = meetingDate <= new Date() || meeting.status === 'completed';
  const isCompleted = meeting.status === 'completed';

  const getMeetingTypeIcon = () => {
    switch (meeting.meetingType) {
      case 'video':
        return <Video className="w-4 h-4" />;
      case 'phone':
        return <Phone className="w-4 h-4" />;
      case 'in-person':
        return <MapPin className="w-4 h-4" />;
      default:
        return <Calendar className="w-4 h-4" />;
    }
  };

  const getMeetingTypeLabel = () => {
    switch (meeting.meetingType) {
      case 'video':
        return 'Video Meeting';
      case 'phone':
        return 'Phone Call';
      case 'in-person':
        return 'In-Person Meeting';
      default:
        return 'Meeting';
    }
  };

  const getStatusBadge = () => {
    switch (meeting.status) {
      case 'scheduled':
        return (
          <Badge variant={isUpcoming ? 'default' : 'secondary'} className="text-xs">
            {isUpcoming ? 'Upcoming' : 'Scheduled'}
          </Badge>
        );
      case 'completed':
        return (
          <Badge variant="outline" className="text-xs border-green-200 text-green-700 bg-green-50">
            Completed
          </Badge>
        );
      case 'cancelled':
        return (
          <Badge variant="outline" className="text-xs border-red-200 text-red-700 bg-red-50">
            Cancelled
          </Badge>
        );
      case 'rescheduled':
        return (
          <Badge variant="outline" className="text-xs border-yellow-200 text-yellow-700 bg-yellow-50">
            Rescheduled
          </Badge>
        );
      default:
        return null;
    }
  };

  const getEnhancedTimeDisplay = () => {
    if (isToday(meetingDate)) {
      if (isUpcoming) {
        return `Today at ${format(meetingDate, 'h:mm a')} • ${formatDistanceToNow(meetingDate, { addSuffix: true })}`;
      }
      return `Today at ${format(meetingDate, 'h:mm a')}`;
    }

    if (isTomorrow(meetingDate)) {
      return `Tomorrow at ${format(meetingDate, 'h:mm a')}`;
    }

    if (isYesterday(meetingDate)) {
      return `Yesterday at ${format(meetingDate, 'h:mm a')}`;
    }

    if (isUpcoming && meetingDate.getTime() - new Date().getTime() < 7 * 24 * 60 * 60 * 1000) {
      return `${format(meetingDate, 'EEEE')} at ${format(meetingDate, 'h:mm a')} • ${formatDistanceToNow(meetingDate, { addSuffix: true })}`;
    }

    return format(meetingDate, 'MMM d, yyyy h:mm a');
  };

  const handleCardClick = () => {
    if (onClick) {
      onClick(meeting);
    }
  };

  const handleComplete = async () => {
    if (!onComplete) return;
    setIsLoading(true);
    try {
      await onComplete(meeting);
    } finally {
      setIsLoading(false);
    }
  };

  const handleDelete = async () => {
    if (!onDelete) return;
    setIsLoading(true);
    try {
      await onDelete(meeting.id);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSendInvitation = async () => {
    if (!onSendInvitation) return;
    setIsLoading(true);
    try {
      await onSendInvitation(meeting);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card
      className={cn(
        "transition-all duration-200 hover:shadow-md border-l-4 cursor-pointer",
        isCompleted
          ? "border-l-green-500 bg-green-50/30 dark:bg-green-900/10 hover:bg-green-50/50 dark:hover:bg-green-900/20"
          : isUpcoming
            ? "border-l-blue-500 bg-blue-50/30 dark:bg-blue-900/10 hover:bg-blue-50/50 dark:hover:bg-blue-900/20"
            : "border-l-gray-300 bg-gray-50/30 dark:bg-gray-800/50 hover:bg-gray-50/50 dark:hover:bg-gray-800/70",
        variant === 'past' && "opacity-75",
        onClick && "hover:scale-[1.01]",
        className
      )}
      onClick={handleCardClick}
    >
      <CardContent className="p-4">
        <div className="flex items-start justify-between">
          <div className="flex-1 space-y-3">
            {/* Header */}
            <div className="flex items-start justify-between">
              <div className="flex items-center space-x-3">
                <div className={cn(
                  "p-2 rounded-lg",
                  isCompleted 
                    ? "bg-green-100 dark:bg-green-900/20 text-green-600 dark:text-green-400"
                    : isUpcoming 
                      ? "bg-blue-100 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400"
                      : "bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400"
                )}>
                  {getMeetingTypeIcon()}
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900 dark:text-gray-100 text-sm">
                    {meeting.title}
                  </h3>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    {getMeetingTypeLabel()}
                  </p>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                {getStatusBadge()}
                {showActions && (
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-8 w-8 p-0"
                        onClick={(e) => e.stopPropagation()}
                      >
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end" onClick={(e) => e.stopPropagation()}>
                      {onEdit && (
                        <DropdownMenuItem onClick={(e) => {
                          e.stopPropagation();
                          onEdit(meeting);
                        }}>
                          <Edit className="mr-2 h-4 w-4" />
                          Edit
                        </DropdownMenuItem>
                      )}
                      {onSendInvitation && (
                        <DropdownMenuItem onClick={(e) => {
                          e.stopPropagation();
                          handleSendInvitation();
                        }}>
                          <Mail className="mr-2 h-4 w-4" />
                          Send Invitation
                        </DropdownMenuItem>
                      )}
                      {onComplete && !isCompleted && (
                        <DropdownMenuItem onClick={(e) => {
                          e.stopPropagation();
                          handleComplete();
                        }}>
                          <CheckCircle className="mr-2 h-4 w-4" />
                          Mark Complete
                        </DropdownMenuItem>
                      )}
                      {onDelete && (
                        <>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem
                            onClick={(e) => {
                              e.stopPropagation();
                              handleDelete();
                            }}
                            className="text-red-600 focus:text-red-600"
                          >
                            <Trash2 className="mr-2 h-4 w-4" />
                            Delete
                          </DropdownMenuItem>
                        </>
                      )}
                    </DropdownMenuContent>
                  </DropdownMenu>
                )}
              </div>
            </div>

            {/* Meeting Details */}
            <div className="space-y-2 text-sm">
              <div className="flex items-center space-x-2 text-gray-600 dark:text-gray-400">
                <Clock className="w-4 h-4" />
                <span className="font-medium">{getEnhancedTimeDisplay()}</span>
              </div>
              <div className="flex items-center space-x-2 text-gray-500 dark:text-gray-500 text-xs">
                <span>Duration: {meeting.duration} minutes</span>
                {meeting.timezone !== 'UTC' && (
                  <>
                    <span>•</span>
                    <span>{meeting.timezone}</span>
                  </>
                )}
              </div>
            </div>

            {/* Meeting Link or Location */}
            {meeting.meetingType === 'video' && meeting.meetingLink && (
              <div className="flex items-center space-x-2 text-sm">
                <Link className="w-4 h-4 text-blue-500" />
                <a
                  href={meeting.meetingLink}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 underline truncate"
                  onClick={(e) => e.stopPropagation()}
                >
                  Join Meeting
                </a>
              </div>
            )}

            {meeting.meetingType === 'in-person' && meeting.location && (
              <div className="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400">
                <MapPinIcon className="w-4 h-4" />
                <span className="truncate">{meeting.location}</span>
              </div>
            )}

            {/* Description */}
            {meeting.description && (
              <div className="text-sm text-gray-600 dark:text-gray-400">
                <p className="line-clamp-2">{meeting.description}</p>
              </div>
            )}

            {/* Assigned User */}
            {meeting.assignedToUser && (
              <div className="flex items-center space-x-2">
                <Avatar className="w-6 h-6">
                  <AvatarImage 
                    src={meeting.assignedToUser.avatar || undefined} 
                    alt={`${meeting.assignedToUser.firstName} ${meeting.assignedToUser.lastName}`}
                  />
                  <AvatarFallback className="text-xs">
                    {meeting.assignedToUser.firstName?.charAt(0)}
                    {meeting.assignedToUser.lastName?.charAt(0)}
                  </AvatarFallback>
                </Avatar>
                <span className="text-sm text-gray-600 dark:text-gray-400">
                  {meeting.assignedToUser.firstName} {meeting.assignedToUser.lastName}
                </span>
              </div>
            )}

            {/* Meeting Notes (for completed meetings) */}
            {isCompleted && meeting.meetingNotes && (
              <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-3">
                <h4 className="text-xs font-medium text-gray-500 dark:text-gray-400 mb-1">
                  Meeting Notes
                </h4>
                <p className="text-sm text-gray-700 dark:text-gray-300 line-clamp-3">
                  {meeting.meetingNotes}
                </p>
              </div>
            )}

            {/* Follow-up indicator */}
            {meeting.followUpRequired && (
              <div className="flex items-center space-x-2 text-sm">
                <Badge variant="outline" className="text-xs border-orange-200 text-orange-700 bg-orange-50">
                  Follow-up Required
                </Badge>
                {meeting.nextMeetingDate && (
                  <span className="text-gray-500 dark:text-gray-400 text-xs">
                    Next: {format(new Date(meeting.nextMeetingDate), 'MMM d')}
                  </span>
                )}
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
