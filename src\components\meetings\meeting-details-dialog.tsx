'use client';

import { format, formatDistanceToNow } from 'date-fns';
import {
  Calendar,
  Clock,
  User,
  Phone,
  Video,
  MapPin,
  Link,
  MapPinIcon,
  Mail,
  FileText,
  CheckCircle,
  AlertCircle,
  Users,
  Edit,
  ExternalLink,
  Copy,
  Share
} from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Separator } from '@/components/ui/separator';
import { useToast } from '@/hooks/use-toast';
import { ScheduledMeetingWithRelations } from '@/services/scheduled-meeting-service';
import { cn } from '@/lib/utils';

interface MeetingDetailsDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  meeting: ScheduledMeetingWithRelations | null;
  onEdit?: (meeting: ScheduledMeetingWithRelations) => void;
  onSendInvitation?: (meeting: ScheduledMeetingWithRelations) => void;
}

export function MeetingDetailsDialog({
  open,
  onOpenChange,
  meeting,
  onEdit,
  onSendInvitation
}: MeetingDetailsDialogProps) {
  const { toast } = useToast();

  if (!meeting) return null;

  const meetingDate = new Date(meeting.scheduledAt);
  const isUpcoming = meetingDate > new Date() && meeting.status === 'scheduled';
  const isPast = meetingDate <= new Date() || meeting.status === 'completed';
  const isCompleted = meeting.status === 'completed';

  const getMeetingTypeIcon = () => {
    switch (meeting.meetingType) {
      case 'video':
        return <Video className="w-5 h-5" />;
      case 'phone':
        return <Phone className="w-5 h-5" />;
      case 'in-person':
        return <MapPin className="w-5 h-5" />;
      default:
        return <Calendar className="w-5 h-5" />;
    }
  };

  const getMeetingTypeLabel = () => {
    switch (meeting.meetingType) {
      case 'video':
        return 'Video Meeting';
      case 'phone':
        return 'Phone Call';
      case 'in-person':
        return 'In-Person Meeting';
      default:
        return 'Meeting';
    }
  };

  const getStatusBadge = () => {
    if (isCompleted) {
      return (
        <Badge className="bg-green-100 text-green-800 border-green-200">
          <CheckCircle className="w-3 h-3 mr-1" />
          Completed
        </Badge>
      );
    }
    if (isUpcoming) {
      return (
        <Badge className="bg-blue-100 text-blue-800 border-blue-200">
          <Clock className="w-3 h-3 mr-1" />
          Scheduled
        </Badge>
      );
    }
    return (
      <Badge variant="secondary">
        <AlertCircle className="w-3 h-3 mr-1" />
        Past Due
      </Badge>
    );
  };

  const getTimeDisplay = () => {
    if (isUpcoming) {
      return `${formatDistanceToNow(meetingDate, { addSuffix: true })} • ${format(meetingDate, 'MMM d, yyyy h:mm a')}`;
    }
    return format(meetingDate, 'MMM d, yyyy h:mm a');
  };

  const copyMeetingLink = async () => {
    if (meeting.meetingLink) {
      try {
        await navigator.clipboard.writeText(meeting.meetingLink);
        toast({
          title: "Link copied",
          description: "Meeting link has been copied to clipboard.",
        });
      } catch (error) {
        toast({
          title: "Failed to copy",
          description: "Could not copy meeting link to clipboard.",
          variant: "destructive",
        });
      }
    }
  };

  const shareMeeting = async () => {
    const shareData = {
      title: meeting.title,
      text: `Meeting: ${meeting.title}\nDate: ${format(meetingDate, 'EEEE, MMMM d, yyyy h:mm a')}\nDuration: ${meeting.duration} minutes`,
      url: meeting.meetingLink || undefined,
    };

    if (navigator.share) {
      try {
        await navigator.share(shareData);
      } catch (error) {
        // Fallback to copying text
        await copyMeetingDetails();
      }
    } else {
      await copyMeetingDetails();
    }
  };

  const copyMeetingDetails = async () => {
    const details = `Meeting: ${meeting.title}
Date: ${format(meetingDate, 'EEEE, MMMM d, yyyy h:mm a')}
Duration: ${meeting.duration} minutes
Type: ${getMeetingTypeLabel()}
${meeting.meetingLink ? `Link: ${meeting.meetingLink}` : ''}
${meeting.location ? `Location: ${meeting.location}` : ''}
${meeting.description ? `Description: ${meeting.description}` : ''}`;

    try {
      await navigator.clipboard.writeText(details);
      toast({
        title: "Details copied",
        description: "Meeting details have been copied to clipboard.",
      });
    } catch (error) {
      toast({
        title: "Failed to copy",
        description: "Could not copy meeting details to clipboard.",
        variant: "destructive",
      });
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[700px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <div className="flex items-start justify-between">
            <div className="flex items-center space-x-3">
              <div className={cn(
                "p-3 rounded-lg",
                isCompleted
                  ? "bg-green-100 dark:bg-green-900/20 text-green-600 dark:text-green-400"
                  : isUpcoming
                    ? "bg-blue-100 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400"
                    : "bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400"
              )}>
                {getMeetingTypeIcon()}
              </div>
              <div>
                <DialogTitle className="text-xl font-semibold text-left">
                  {meeting.title}
                </DialogTitle>
                <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                  {getMeetingTypeLabel()}
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              {getStatusBadge()}
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex items-center justify-between pt-4">
            <div className="flex items-center space-x-2">
              {onEdit && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onEdit(meeting)}
                  className="flex items-center space-x-2"
                >
                  <Edit className="w-4 h-4" />
                  <span>Edit</span>
                </Button>
              )}

              {meeting.meetingLink && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => window.open(meeting.meetingLink, '_blank')}
                  className="flex items-center space-x-2"
                >
                  <ExternalLink className="w-4 h-4" />
                  <span>Join</span>
                </Button>
              )}

              {onSendInvitation && !meeting.invitationSent && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onSendInvitation(meeting)}
                  className="flex items-center space-x-2"
                >
                  <Mail className="w-4 h-4" />
                  <span>Send Invite</span>
                </Button>
              )}
            </div>

            <div className="flex items-center space-x-2">
              {meeting.meetingLink && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={copyMeetingLink}
                  className="flex items-center space-x-2"
                >
                  <Copy className="w-4 h-4" />
                  <span>Copy Link</span>
                </Button>
              )}

              <Button
                variant="ghost"
                size="sm"
                onClick={shareMeeting}
                className="flex items-center space-x-2"
              >
                <Share className="w-4 h-4" />
                <span>Share</span>
              </Button>
            </div>
          </div>
        </DialogHeader>

        <div className="space-y-6">
          {/* Date and Time */}
          <div className="bg-gray-50 dark:bg-gray-800/50 rounded-lg p-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex items-center space-x-3">
                <Calendar className="w-5 h-5 text-gray-500" />
                <div>
                  <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                    {format(meetingDate, 'EEEE, MMMM d, yyyy')}
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    {getTimeDisplay()}
                  </p>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <Clock className="w-5 h-5 text-gray-500" />
                <div>
                  <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                    {format(meetingDate, 'h:mm a')} ({meeting.duration} min)
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    Timezone: {meeting.timezone}
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Meeting Link or Location */}
          {meeting.meetingType === 'video' && meeting.meetingLink && (
            <div className="bg-blue-50 dark:bg-blue-900/10 rounded-lg p-4 border border-blue-200 dark:border-blue-800">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3 flex-1">
                  <Link className="w-5 h-5 text-blue-500" />
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-1">
                      Meeting Link
                    </p>
                    <p className="text-sm text-blue-600 dark:text-blue-400 truncate">
                      {meeting.meetingLink}
                    </p>
                  </div>
                </div>
                <div className="flex items-center space-x-2 ml-4">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={copyMeetingLink}
                    className="flex items-center space-x-1"
                  >
                    <Copy className="w-3 h-3" />
                    <span className="hidden sm:inline">Copy</span>
                  </Button>
                  <Button
                    size="sm"
                    onClick={() => window.open(meeting.meetingLink, '_blank')}
                    className="flex items-center space-x-1"
                  >
                    <ExternalLink className="w-3 h-3" />
                    <span>Join</span>
                  </Button>
                </div>
              </div>
            </div>
          )}

          {meeting.meetingType === 'in-person' && meeting.location && (
            <div className="bg-green-50 dark:bg-green-900/10 rounded-lg p-4 border border-green-200 dark:border-green-800">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3 flex-1">
                  <MapPinIcon className="w-5 h-5 text-green-500" />
                  <div className="flex-1">
                    <p className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-1">
                      Location
                    </p>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      {meeting.location}
                    </p>
                  </div>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    const encodedLocation = encodeURIComponent(meeting.location!);
                    window.open(`https://maps.google.com/maps?q=${encodedLocation}`, '_blank');
                  }}
                  className="flex items-center space-x-1"
                >
                  <MapPinIcon className="w-3 h-3" />
                  <span>Directions</span>
                </Button>
              </div>
            </div>
          )}

          <Separator />

          {/* Participants */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4 flex items-center">
              <Users className="w-5 h-5 mr-2" />
              Participants
            </h3>
            <div className="space-y-3">
              {/* Assigned User */}
              {meeting.assignedToUser && (
                <div className="flex items-center space-x-3 p-3 bg-gray-50 dark:bg-gray-800/50 rounded-lg">
                  <Avatar className="w-10 h-10">
                    <AvatarImage 
                      src={meeting.assignedToUser.avatar || undefined} 
                      alt={`${meeting.assignedToUser.firstName} ${meeting.assignedToUser.lastName}`}
                    />
                    <AvatarFallback>
                      {meeting.assignedToUser.firstName?.charAt(0)}
                      {meeting.assignedToUser.lastName?.charAt(0)}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1">
                    <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                      {meeting.assignedToUser.firstName} {meeting.assignedToUser.lastName}
                    </p>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      {meeting.assignedToUser.email}
                    </p>
                  </div>
                  <Badge variant="outline" className="text-xs">
                    Organizer
                  </Badge>
                </div>
              )}

              {/* Contact */}
              {meeting.contact && (
                <div className="flex items-center space-x-3 p-3 bg-gray-50 dark:bg-gray-800/50 rounded-lg">
                  <Avatar className="w-10 h-10">
                    <AvatarFallback>
                      {meeting.contact.firstName?.charAt(0)}
                      {meeting.contact.lastName?.charAt(0)}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1">
                    <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                      {meeting.contact.firstName} {meeting.contact.lastName}
                    </p>
                    {meeting.contact.email && (
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        {meeting.contact.email}
                      </p>
                    )}
                  </div>
                  <Badge variant="outline" className="text-xs">
                    Contact
                  </Badge>
                </div>
              )}

              {/* Lead Information */}
              {meeting.lead && (
                <div className="p-3 bg-blue-50 dark:bg-blue-900/10 rounded-lg">
                  <div className="flex items-center space-x-2 mb-2">
                    <FileText className="w-4 h-4 text-blue-500" />
                    <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                      Related Lead
                    </span>
                  </div>
                  <p className="text-sm text-gray-700 dark:text-gray-300">
                    {meeting.lead.title}
                  </p>
                  <Badge variant="outline" className="text-xs mt-2">
                    {meeting.lead.status}
                  </Badge>
                </div>
              )}
            </div>
          </div>

          {/* Description */}
          {meeting.description && (
            <>
              <Separator />
              <div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-3 flex items-center">
                  <FileText className="w-5 h-5 mr-2" />
                  Description
                </h3>
                <div className="bg-gray-50 dark:bg-gray-800/50 rounded-lg p-4">
                  <p className="text-sm text-gray-700 dark:text-gray-300 whitespace-pre-wrap">
                    {meeting.description}
                  </p>
                </div>
              </div>
            </>
          )}

          {/* Meeting Notes (for completed meetings) */}
          {isCompleted && meeting.meetingNotes && (
            <>
              <Separator />
              <div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-3 flex items-center">
                  <FileText className="w-5 h-5 mr-2" />
                  Meeting Notes
                </h3>
                <div className="bg-green-50 dark:bg-green-900/10 rounded-lg p-4">
                  <p className="text-sm text-gray-700 dark:text-gray-300 whitespace-pre-wrap">
                    {meeting.meetingNotes}
                  </p>
                </div>
              </div>
            </>
          )}

          {/* Follow-up Information */}
          {meeting.followUpRequired && (
            <>
              <Separator />
              <div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-3 flex items-center">
                  <AlertCircle className="w-5 h-5 mr-2 text-orange-500" />
                  Follow-up Required
                </h3>
                <div className="bg-orange-50 dark:bg-orange-900/10 rounded-lg p-4">
                  {meeting.nextMeetingDate && (
                    <p className="text-sm text-gray-700 dark:text-gray-300">
                      Next meeting scheduled for: {format(new Date(meeting.nextMeetingDate), 'MMM d, yyyy h:mm a')}
                    </p>
                  )}
                  {!meeting.nextMeetingDate && (
                    <p className="text-sm text-gray-700 dark:text-gray-300">
                      Follow-up action required - no next meeting scheduled yet.
                    </p>
                  )}
                </div>
              </div>
            </>
          )}

          {/* Meeting Status Information */}
          <div className="bg-gray-50 dark:bg-gray-800/50 rounded-lg p-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <p className="text-gray-500 dark:text-gray-400">Created</p>
                <p className="font-medium text-gray-900 dark:text-gray-100">
                  {format(new Date(meeting.createdAt), 'MMM d, yyyy h:mm a')}
                </p>
              </div>
              <div>
                <p className="text-gray-500 dark:text-gray-400">Last Updated</p>
                <p className="font-medium text-gray-900 dark:text-gray-100">
                  {format(new Date(meeting.updatedAt), 'MMM d, yyyy h:mm a')}
                </p>
              </div>
              {meeting.completedAt && (
                <div>
                  <p className="text-gray-500 dark:text-gray-400">Completed</p>
                  <p className="font-medium text-gray-900 dark:text-gray-100">
                    {format(new Date(meeting.completedAt), 'MMM d, yyyy h:mm a')}
                  </p>
                </div>
              )}
              <div>
                <p className="text-gray-500 dark:text-gray-400">Invitation Status</p>
                <p className="font-medium text-gray-900 dark:text-gray-100">
                  {meeting.invitationSent ? 'Sent' : 'Not Sent'}
                </p>
              </div>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
