import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

/**
 * GET /api/automation/health
 * Check if automation system is properly set up
 */
export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user's current tenant
    const currentTenant = session.user.tenants?.[0];
    if (!currentTenant) {
      return NextResponse.json({ error: 'No tenant access' }, { status: 403 });
    }

    const healthCheck = {
      database: { status: 'unknown', message: '' },
      tables: { status: 'unknown', message: '', details: {} },
      triggers: { status: 'unknown', message: '', count: 0 },
      executions: { status: 'unknown', message: '', count: 0 },
      overall: 'unknown'
    };

    // 1. Check database connection
    try {
      await prisma.$queryRaw`SELECT 1`;
      healthCheck.database = { status: 'healthy', message: 'Database connection successful' };
    } catch (error) {
      healthCheck.database = { 
        status: 'error', 
        message: `Database connection failed: ${error.message}` 
      };
      return NextResponse.json({ 
        success: false, 
        healthCheck,
        message: 'Database connection failed'
      }, { status: 503 });
    }

    // 2. Check if automation tables exist
    try {
      // Try to query automation tables
      const [triggerCount, executionCount] = await Promise.all([
        prisma.automationTrigger.count({ where: { tenantId: currentTenant.id } }),
        prisma.automationExecution.count({ where: { tenantId: currentTenant.id } })
      ]);

      healthCheck.tables = { 
        status: 'healthy', 
        message: 'Automation tables exist and accessible',
        details: {
          automationTriggers: 'exists',
          automationExecutions: 'exists'
        }
      };

      healthCheck.triggers = {
        status: triggerCount > 0 ? 'healthy' : 'warning',
        message: triggerCount > 0 ? `${triggerCount} triggers configured` : 'No triggers configured',
        count: triggerCount
      };

      healthCheck.executions = {
        status: 'healthy',
        message: `${executionCount} executions logged`,
        count: executionCount
      };

    } catch (error) {
      healthCheck.tables = { 
        status: 'error', 
        message: `Automation tables not found or inaccessible: ${error.message}`,
        details: {
          error: error.message,
          suggestion: 'Run: npm run db:push to create tables'
        }
      };
    }

    // 3. Determine overall health
    if (healthCheck.database.status === 'healthy' && healthCheck.tables.status === 'healthy') {
      if (healthCheck.triggers.count > 0) {
        healthCheck.overall = 'healthy';
      } else {
        healthCheck.overall = 'warning'; // Tables exist but no triggers configured
      }
    } else {
      healthCheck.overall = 'error';
    }

    // 4. Provide setup instructions if needed
    const setupInstructions = [];
    
    if (healthCheck.tables.status === 'error') {
      setupInstructions.push({
        step: 1,
        title: 'Create Database Tables',
        command: 'npm run db:push',
        description: 'Create automation tables in the database'
      });
    }
    
    if (healthCheck.triggers.count === 0) {
      setupInstructions.push({
        step: healthCheck.tables.status === 'error' ? 2 : 1,
        title: 'Initialize Default Triggers',
        command: 'node src/scripts/setup-automation-system.ts',
        description: 'Create default automation triggers for your tenant'
      });
    }

    return NextResponse.json({
      success: healthCheck.overall !== 'error',
      healthCheck,
      setupInstructions: setupInstructions.length > 0 ? setupInstructions : null,
      recommendations: generateRecommendations(healthCheck)
    });

  } catch (error) {
    console.error('Error checking automation health:', error);
    return NextResponse.json(
      { 
        success: false,
        error: 'Health check failed',
        details: process.env.NODE_ENV === 'development' ? error.message : undefined
      },
      { status: 500 }
    );
  }
}

function generateRecommendations(healthCheck: any): string[] {
  const recommendations = [];

  if (healthCheck.overall === 'error') {
    recommendations.push('🔧 Set up the automation system by following the setup instructions above');
  } else if (healthCheck.overall === 'warning') {
    recommendations.push('⚙️ Configure automation triggers to start automating your processes');
  } else {
    recommendations.push('✅ Automation system is healthy and ready to use');
  }

  if (healthCheck.executions.count === 0) {
    recommendations.push('🧪 Test the automation by changing an opportunity stage to "Closed Won"');
  }

  if (healthCheck.triggers.count > 0 && healthCheck.executions.count > 0) {
    recommendations.push('📊 Monitor automation performance in the admin dashboard');
  }

  return recommendations;
}

/**
 * POST /api/automation/health/fix
 * Attempt to fix common automation setup issues
 */
export async function POST(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const currentTenant = session.user.tenants?.[0];
    if (!currentTenant) {
      return NextResponse.json({ error: 'No tenant access' }, { status: 403 });
    }

    const body = await req.json();
    const { action } = body;

    if (action === 'create_test_data') {
      // Create a test trigger and execution
      const trigger = await prisma.automationTrigger.create({
        data: {
          tenantId: currentTenant.id,
          name: 'Test Automation Trigger',
          description: 'Test trigger created by health check',
          triggerType: 'opportunity_status_change',
          triggerConditions: { toStatus: 'Closed Won' },
          actions: [
            {
              type: 'create_handover',
              config: { priority: 'high' }
            }
          ],
          isActive: true,
          priority: 5,
          createdById: session.user.id
        }
      });

      return NextResponse.json({
        success: true,
        message: 'Test trigger created successfully',
        data: { triggerId: trigger.id }
      });
    }

    return NextResponse.json(
      { error: 'Unknown action' },
      { status: 400 }
    );

  } catch (error) {
    console.error('Error fixing automation setup:', error);
    return NextResponse.json(
      { error: 'Failed to fix automation setup' },
      { status: 500 }
    );
  }
}
