import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '../ui/avatar';
import { 
  MessageSquare, 
  Calendar, 
  Mail, 
  Users, 
  ArrowRight,
  Clock,
  TrendingUp
} from 'lucide-react';
import { SmoothLink } from '../ui/smooth-link';
import { formatDistanceToNow } from 'date-fns';

interface CommunicationStats {
  totalChannels: number;
  unreadMessages: number;
  todaysEvents: number;
  upcomingEvents: number;
  activeUsers: number;
  recentActivity: Array<{
    id: string;
    type: 'message' | 'event' | 'channel';
    title: string;
    subtitle: string;
    timestamp: string;
    user?: {
      name: string;
      avatar?: string;
    };
  }>;
}

interface CommunicationWidgetProps {
  className?: string;
}

export default function CommunicationWidget({ className }: CommunicationWidgetProps) {
  const [stats, setStats] = useState<CommunicationStats | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadCommunicationStats();
  }, []);

  const loadCommunicationStats = async () => {
    try {
      // Load communication statistics
      const [channelsRes, unreadRes, eventsRes] = await Promise.all([
        fetch('/api/chat/channels'),
        fetch('/api/communication/unread-counts'),
        fetch('/api/calendar/events'),
      ]);

      const channels = channelsRes.ok ? await channelsRes.json() : [];
      const unreadCounts = unreadRes.ok ? await unreadRes.json() : [];
      const events = eventsRes.ok ? await eventsRes.json() : [];

      const today = new Date();
      const todaysEvents = events.filter((event: any) => 
        new Date(event.startTime).toDateString() === today.toDateString()
      );
      const upcomingEvents = events.filter((event: any) => 
        new Date(event.startTime) > today
      );

      const totalUnread = unreadCounts.reduce((total: number, channel: any) => 
        total + channel.unreadCount, 0
      );

      // Mock recent activity (in a real app, this would come from an API)
      const recentActivity = [
        {
          id: '1',
          type: 'message' as const,
          title: 'New message in #general',
          subtitle: 'Team standup discussion',
          timestamp: new Date(Date.now() - 5 * 60 * 1000).toISOString(),
          user: { name: 'John Doe', avatar: '/avatars/john.jpg' }
        },
        {
          id: '2',
          type: 'event' as const,
          title: 'Client meeting starting soon',
          subtitle: 'Project review with ABC Corp',
          timestamp: new Date(Date.now() + 15 * 60 * 1000).toISOString(),
        },
        {
          id: '3',
          type: 'channel' as const,
          title: 'New channel created',
          subtitle: '#project-alpha',
          timestamp: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
          user: { name: 'Jane Smith', avatar: '/avatars/jane.jpg' }
        },
      ];

      setStats({
        totalChannels: channels.length,
        unreadMessages: totalUnread,
        todaysEvents: todaysEvents.length,
        upcomingEvents: upcomingEvents.length,
        activeUsers: 12, // Mock data
        recentActivity,
      });
    } catch (error) {
      console.error('Failed to load communication stats:', error);
    } finally {
      setLoading(false);
    }
  };

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'message':
        return <MessageSquare className="h-4 w-4" />;
      case 'event':
        return <Calendar className="h-4 w-4" />;
      case 'channel':
        return <Users className="h-4 w-4" />;
      default:
        return <MessageSquare className="h-4 w-4" />;
    }
  };

  const getActivityColor = (type: string) => {
    switch (type) {
      case 'message':
        return 'text-blue-600';
      case 'event':
        return 'text-green-600';
      case 'channel':
        return 'text-purple-600';
      default:
        return 'text-gray-600';
    }
  };

  if (loading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <MessageSquare className="h-5 w-5" />
            <span>Communication</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!stats) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <MessageSquare className="h-5 w-5" />
            <span>Communication</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground">
            Unable to load communication data
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center space-x-2">
            <MessageSquare className="h-5 w-5" />
            <span>Communication</span>
          </CardTitle>
          <SmoothLink href="/communication">
            <Button variant="ghost" size="sm">
              <ArrowRight className="h-4 w-4" />
            </Button>
          </SmoothLink>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Quick Stats */}
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm text-muted-foreground">Channels</span>
              <span className="text-lg font-semibold">{stats.totalChannels}</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-muted-foreground">Unread</span>
              <div className="flex items-center space-x-1">
                <span className="text-lg font-semibold">{stats.unreadMessages}</span>
                {stats.unreadMessages > 0 && (
                  <Badge variant="destructive" className="text-xs">
                    New
                  </Badge>
                )}
              </div>
            </div>
          </div>
          
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm text-muted-foreground">Today</span>
              <span className="text-lg font-semibold">{stats.todaysEvents}</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-muted-foreground">Upcoming</span>
              <span className="text-lg font-semibold">{stats.upcomingEvents}</span>
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="grid grid-cols-3 gap-2">
          <SmoothLink href="/communication?tab=chat">
            <Button variant="outline" size="sm" className="w-full">
              <MessageSquare className="h-4 w-4 mr-1" />
              Chat
            </Button>
          </SmoothLink>
          <SmoothLink href="/communication?tab=calendar">
            <Button variant="outline" size="sm" className="w-full">
              <Calendar className="h-4 w-4 mr-1" />
              Calendar
            </Button>
          </SmoothLink>
          <SmoothLink href="/communication?tab=templates">
            <Button variant="outline" size="sm" className="w-full">
              <Mail className="h-4 w-4 mr-1" />
              Templates
            </Button>
          </SmoothLink>
        </div>

        {/* Recent Activity */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <h4 className="text-sm font-medium">Recent Activity</h4>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </div>
          
          <div className="space-y-3">
            {stats.recentActivity.slice(0, 3).map((activity) => (
              <div key={activity.id} className="flex items-start space-x-3">
                <div className={`p-1 rounded-full ${getActivityColor(activity.type)}`}>
                  {getActivityIcon(activity.type)}
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium truncate">{activity.title}</p>
                  <p className="text-xs text-muted-foreground truncate">
                    {activity.subtitle}
                  </p>
                  <div className="flex items-center space-x-2 mt-1">
                    {activity.user && (
                      <Avatar className="h-4 w-4">
                        <AvatarImage src={activity.user.avatar} />
                        <AvatarFallback className="text-xs">
                          {activity.user.name[0]}
                        </AvatarFallback>
                      </Avatar>
                    )}
                    <span className="text-xs text-muted-foreground">
                      {formatDistanceToNow(new Date(activity.timestamp), { addSuffix: true })}
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>
          
          {stats.recentActivity.length > 3 && (
            <SmoothLink href="/communication">
              <Button variant="ghost" size="sm" className="w-full">
                View all activity
              </Button>
            </SmoothLink>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
