import { z } from 'zod';
import { prisma } from '@/lib/prisma';
import { aiEmailGenerationService, EmailType, EmailContext } from './ai-email-generation';
import { LeadWithRelations } from './lead-management';
import { sendEmail } from '@/lib/email';

// Validation schemas
export const createNurturingCampaignSchema = z.object({
  name: z.string().min(1, 'Campaign name is required').max(255, 'Name too long'),
  description: z.string().optional(),
  targetStatus: z.array(z.enum(['new', 'contacted', 'qualified', 'proposal', 'negotiation'])).default(['new', 'contacted']),
  targetPriority: z.array(z.enum(['low', 'medium', 'high', 'urgent'])).optional(),
  emailSequence: z.array(z.object({
    delayDays: z.number().int().min(0).max(365),
    emailType: z.enum(['cold_outreach', 'follow_up', 'meeting_request', 'nurturing', 're_engagement']),
    subject: z.string().optional(),
    customPrompt: z.string().optional()
  })).min(1, 'At least one email in sequence required'),
  isActive: z.boolean().default(true),
  maxLeadsPerDay: z.number().int().min(1).max(100).default(10)
});

export const nurturingExecutionSchema = z.object({
  campaignId: z.string(),
  leadIds: z.array(z.string()).optional(),
  dryRun: z.boolean().default(false)
});

export type CreateNurturingCampaignData = z.infer<typeof createNurturingCampaignSchema>;
export type NurturingExecutionData = z.infer<typeof nurturingExecutionSchema>;

export interface NurturingCampaign {
  id: string;
  tenantId: string;
  name: string;
  description?: string;
  targetStatus: string[];
  targetPriority?: string[];
  emailSequence: EmailSequenceStep[];
  isActive: boolean;
  maxLeadsPerDay: number;
  createdAt: Date;
  updatedAt: Date;
  createdById: string;
  stats: {
    totalLeads: number;
    processedLeads: number;
    emailsSent: number;
    responses: number;
    conversions: number;
  };
}

export interface EmailSequenceStep {
  delayDays: number;
  emailType: EmailType;
  subject?: string;
  customPrompt?: string;
}

export interface NurturingExecution {
  id: string;
  campaignId: string;
  leadId: string;
  currentStep: number;
  nextExecutionDate: Date;
  status: 'active' | 'paused' | 'completed' | 'failed';
  emailsSent: number;
  lastEmailSentAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface NurturingEmailLog {
  id: string;
  executionId: string;
  leadId: string;
  stepNumber: number;
  emailType: EmailType;
  subject: string;
  content: string;
  sentAt: Date;
  status: 'sent' | 'failed' | 'bounced' | 'opened' | 'clicked';
  errorMessage?: string;
}

export class LeadNurturingService {
  /**
   * Create a new nurturing campaign
   */
  async createCampaign(
    tenantId: string,
    campaignData: CreateNurturingCampaignData,
    createdBy: string
  ): Promise<NurturingCampaign> {
    const campaign = await prisma.nurturingCampaign.create({
      data: {
        tenantId,
        name: campaignData.name,
        description: campaignData.description,
        targetStatus: campaignData.targetStatus,
        targetPriority: campaignData.targetPriority || [],
        emailSequence: campaignData.emailSequence,
        isActive: campaignData.isActive,
        maxLeadsPerDay: campaignData.maxLeadsPerDay,
        createdById: createdBy
      }
    });

    return {
      ...campaign,
      description: campaign.description || undefined,
      emailSequence: campaign.emailSequence as EmailSequenceStep[],
      targetStatus: campaign.targetStatus as string[],
      targetPriority: campaign.targetPriority as string[],
      stats: {
        totalLeads: 0,
        processedLeads: 0,
        emailsSent: 0,
        responses: 0,
        conversions: 0
      }
    };
  }

  /**
   * Get a single campaign by ID
   */
  async getCampaign(campaignId: string, tenantId: string): Promise<NurturingCampaign | null> {
    const campaign = await prisma.nurturingCampaign.findUnique({
      where: { id: campaignId, tenantId, deletedAt: null }
    });

    if (!campaign) {
      return null;
    }

    const stats = await this.getCampaignStats(campaign.id);
    return {
      ...campaign,
      description: campaign.description || undefined,
      emailSequence: campaign.emailSequence as EmailSequenceStep[],
      targetStatus: campaign.targetStatus as string[],
      targetPriority: campaign.targetPriority as string[],
      stats
    };
  }

  /**
   * Get campaigns for a tenant
   */
  async getCampaigns(tenantId: string): Promise<NurturingCampaign[]> {
    const campaigns = await prisma.nurturingCampaign.findMany({
      where: { tenantId, deletedAt: null },
      orderBy: { createdAt: 'desc' }
    });

    // Get stats for each campaign
    const campaignsWithStats = await Promise.all(
      campaigns.map(async (campaign) => {
        const stats = await this.getCampaignStats(campaign.id);
        return {
          ...campaign,
          description: campaign.description || undefined,
          emailSequence: campaign.emailSequence as EmailSequenceStep[],
          targetStatus: campaign.targetStatus as string[],
          targetPriority: campaign.targetPriority as string[],
          stats
        };
      })
    );

    return campaignsWithStats;
  }

  /**
   * Update a nurturing campaign
   */
  async updateCampaign(
    campaignId: string,
    tenantId: string,
    campaignData: CreateNurturingCampaignData
  ): Promise<NurturingCampaign> {
    const campaign = await prisma.nurturingCampaign.update({
      where: { id: campaignId, tenantId, deletedAt: null },
      data: {
        name: campaignData.name,
        description: campaignData.description,
        targetStatus: campaignData.targetStatus,
        targetPriority: campaignData.targetPriority || [],
        emailSequence: campaignData.emailSequence,
        isActive: campaignData.isActive,
        maxLeadsPerDay: campaignData.maxLeadsPerDay,
        updatedAt: new Date()
      }
    });

    const stats = await this.getCampaignStats(campaign.id);
    return {
      ...campaign,
      description: campaign.description || undefined,
      emailSequence: campaign.emailSequence as EmailSequenceStep[],
      targetStatus: campaign.targetStatus as string[],
      targetPriority: campaign.targetPriority as string[],
      stats
    };
  }

  /**
   * Delete a nurturing campaign (soft delete)
   */
  async deleteCampaign(campaignId: string, tenantId: string): Promise<void> {
    await prisma.nurturingCampaign.update({
      where: { id: campaignId, tenantId, deletedAt: null },
      data: {
        deletedAt: new Date()
      }
    });
  }

  /**
   * Execute nurturing campaign for eligible leads
   */
  async executeCampaign(
    campaignId: string,
    tenantId: string,
    options: { leadIds?: string[]; dryRun?: boolean } = {}
  ): Promise<{
    processed: number;
    emailsSent: number;
    errors: string[];
    dryRun: boolean;
  }> {
    const campaign = await prisma.nurturingCampaign.findUnique({
      where: { id: campaignId, tenantId, deletedAt: null }
    });

    if (!campaign || !campaign.isActive) {
      throw new Error('Campaign not found or inactive');
    }

    const emailSequence = campaign.emailSequence as EmailSequenceStep[];
    let processed = 0;
    let emailsSent = 0;
    const errors: string[] = [];

    // Get eligible leads
    const eligibleLeads = await this.getEligibleLeads(
      tenantId,
      campaign,
      options.leadIds
    );

    // Process each lead
    for (const lead of eligibleLeads) {
      try {
        const result = await this.processLeadNurturing(
          lead,
          campaign,
          emailSequence,
          options.dryRun || false
        );
        
        processed++;
        if (result.emailSent) {
          emailsSent++;
        }
      } catch (error) {
        errors.push(`Lead ${lead.id}: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }

    return {
      processed,
      emailsSent,
      errors,
      dryRun: options.dryRun || false
    };
  }

  /**
   * Get eligible leads for nurturing
   */
  private async getEligibleLeads(
    tenantId: string,
    campaign: NurturingCampaign,
    specificLeadIds?: string[]
  ): Promise<LeadWithRelations[]> {
    const where: Record<string, unknown> = {
      tenantId,
      deletedAt: null,
      status: { in: campaign.targetStatus }
    };

    if (campaign.targetPriority && campaign.targetPriority.length > 0) {
      where.priority = { in: campaign.targetPriority };
    }

    if (specificLeadIds && specificLeadIds.length > 0) {
      where.id = { in: specificLeadIds };
    }

    const leads = await prisma.lead.findMany({
      where,
      include: {
        contact: true,
        company: true,
        owner: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            avatarUrl: true
          }
        }
      },
      take: campaign.maxLeadsPerDay
    });

    return leads;
  }

  /**
   * Process nurturing for a single lead
   */
  private async processLeadNurturing(
    lead: LeadWithRelations,
    campaign: NurturingCampaign,
    emailSequence: EmailSequenceStep[],
    dryRun: boolean
  ): Promise<{ emailSent: boolean }> {
    // Check if lead is already in this campaign
    let execution = await prisma.nurturingExecution.findFirst({
      where: {
        campaignId: campaign.id,
        leadId: lead.id,
        status: { in: ['active', 'paused'] }
      }
    });

    // Create new execution if not exists
    if (!execution) {
      if (!dryRun) {
        execution = await prisma.nurturingExecution.create({
          data: {
            campaignId: campaign.id,
            leadId: lead.id,
            currentStep: 0,
            nextExecutionDate: new Date(),
            status: 'active',
            emailsSent: 0
          }
        });
      } else {
        // For dry run, create a mock execution
        execution = {
          id: 'dry-run',
          campaignId: campaign.id,
          leadId: lead.id,
          currentStep: 0,
          nextExecutionDate: new Date(),
          status: 'active' as const,
          emailsSent: 0,
          lastEmailSentAt: null,
          createdAt: new Date(),
          updatedAt: new Date()
        };
      }
    }

    // Check if it's time to send the next email
    if (execution.nextExecutionDate > new Date()) {
      return { emailSent: false };
    }

    // Get current step
    const currentStep = emailSequence[execution.currentStep];
    if (!currentStep) {
      // Campaign completed
      if (!dryRun) {
        await prisma.nurturingExecution.update({
          where: { id: execution.id },
          data: { status: 'completed' }
        });
      }
      return { emailSent: false };
    }

    // Generate and send email
    const emailSent = await this.sendNurturingEmail(
      lead,
      currentStep,
      execution,
      dryRun
    );

    // Update execution
    if (!dryRun && emailSent) {
      const nextStep = execution.currentStep + 1;
      const nextStepData = emailSequence[nextStep];
      const nextExecutionDate = nextStepData 
        ? new Date(Date.now() + nextStepData.delayDays * 24 * 60 * 60 * 1000)
        : null;

      await prisma.nurturingExecution.update({
        where: { id: execution.id },
        data: {
          currentStep: nextStep,
          nextExecutionDate,
          emailsSent: execution.emailsSent + 1,
          lastEmailSentAt: new Date(),
          status: nextStepData ? 'active' : 'completed'
        }
      });
    }

    return { emailSent };
  }

  /**
   * Send nurturing email to lead
   */
  private async sendNurturingEmail(
    lead: LeadWithRelations,
    step: EmailSequenceStep,
    execution: NurturingExecution,
    dryRun: boolean
  ): Promise<boolean> {
    if (!lead.contact?.email) {
      throw new Error('Lead has no email address');
    }

    // Prepare email context
    const emailContext: EmailContext = {
      recipientName: `${lead.contact.firstName || ''} ${lead.contact.lastName || ''}`.trim() || 'there',
      recipientEmail: lead.contact.email,
      senderName: lead.owner ? `${lead.owner.firstName || ''} ${lead.owner.lastName || ''}`.trim() : 'Our Team',
      senderCompany: lead.company?.name || 'your company',
      recipientCompany: lead.company?.name || '',
      relationship: 'prospect',
      locale: 'en' // TODO: Get from lead or tenant settings
    };

    try {
      // Generate email using AI
      const generatedEmail = await aiEmailGenerationService.generateEmail(
        step.emailType,
        emailContext,
        lead.createdById || 'system',
        {
          customPrompt: step.customPrompt,
          subject: step.subject
        }
      );

      if (dryRun) {
        // In dry run mode, we don't actually send emails
        return true;
      }

      // Send email
      await sendEmail({
        to: lead.contact.email,
        subject: generatedEmail.subject,
        html: generatedEmail.content
      });

      // Log email
      await prisma.nurturingEmailLog.create({
        data: {
          executionId: execution.id,
          leadId: lead.id,
          stepNumber: execution.currentStep,
          emailType: step.emailType,
          subject: generatedEmail.subject,
          content: generatedEmail.content,
          sentAt: new Date(),
          status: 'sent'
        }
      });

      return true;
    } catch (error) {
      // Log failed email
      if (!dryRun) {
        await prisma.nurturingEmailLog.create({
          data: {
            executionId: execution.id,
            leadId: lead.id,
            stepNumber: execution.currentStep,
            emailType: step.emailType,
            subject: step.subject || `${step.emailType} email`,
            content: '',
            sentAt: new Date(),
            status: 'failed',
            errorMessage: error instanceof Error ? error.message : 'Unknown error'
          }
        });
      }
      throw error;
    }
  }

  /**
   * Get campaign statistics
   */
  private async getCampaignStats(campaignId: string) {
    // Get the campaign to access its criteria
    const campaign = await prisma.nurturingCampaign.findUnique({
      where: { id: campaignId }
    });

    if (!campaign) {
      return {
        totalLeads: 0,
        emailsSent: 0,
        responses: 0,
        conversions: 0
      };
    }

    // Count leads that match the campaign criteria
    const targetStatus = campaign.targetStatus as string[];
    const targetPriority = campaign.targetPriority as string[];

    const whereClause: any = {
      tenantId: campaign.tenantId,
      status: {
        in: targetStatus
      }
    };

    // Add priority filter if specified
    if (targetPriority && targetPriority.length > 0) {
      whereClause.priority = {
        in: targetPriority
      };
    }

    const totalLeads = await prisma.lead.count({
      where: whereClause
    });

    // Debug logging (remove in production)
    console.log(`Campaign ${campaignId} stats:`, {
      tenantId: campaign.tenantId,
      targetStatus,
      targetPriority,
      whereClause,
      totalLeads
    });

    // Count leads that have been processed (executions)
    const processedLeads = await prisma.nurturingExecution.count({
      where: { campaignId }
    });

    const emailsSent = await prisma.nurturingEmailLog.count({
      where: {
        execution: { campaignId },
        status: 'sent'
      }
    });

    // Count responses (emails with replies)
    const responses = await prisma.nurturingEmailLog.count({
      where: {
        execution: { campaignId },
        status: 'sent',
        // TODO: Add response tracking when implemented
      }
    });

    // Count conversions (leads that moved to qualified+ status after campaign)
    const conversions = await prisma.nurturingExecution.count({
      where: {
        campaignId,
        lead: {
          status: {
            in: ['qualified', 'proposal', 'negotiation', 'won']
          }
        }
      }
    });

    return {
      totalLeads,
      processedLeads,
      emailsSent,
      responses,
      conversions
    };
  }

  /**
   * Pause/Resume campaign execution for a lead
   */
  async pauseLeadExecution(executionId: string, tenantId: string): Promise<void> {
    await prisma.nurturingExecution.updateMany({
      where: { 
        id: executionId,
        campaign: { tenantId }
      },
      data: { status: 'paused' }
    });
  }

  async resumeLeadExecution(executionId: string, tenantId: string): Promise<void> {
    await prisma.nurturingExecution.updateMany({
      where: { 
        id: executionId,
        campaign: { tenantId }
      },
      data: { status: 'active' }
    });
  }
}

export const leadNurturingService = new LeadNurturingService();
