'use client';

import React from 'react';
import {
  <PERSON><PERSON>hart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  ReferenceLine,
  ComposedChart,
  Bar,
} from 'recharts';
import { BaseChart, BaseChartProps } from './base-chart';

interface PerformanceData {
  timestamp: number;
  timeWindow: string;
  performanceSummary: {
    slowQueries: number;
    cacheHitRate: number;
    averageResponseTime: number;
    errorRate: number;
    memoryUsage: number;
  };
  apiMetrics: {
    averageResponseTime: number;
    p95ResponseTime: number;
    p99ResponseTime: number;
    errorRate: number;
    requestsPerMinute: number;
  };
  memoryMetrics: {
    heapUsedPercentage: number;
  };
}

interface PerformanceTrendChartProps extends Omit<BaseChartProps, 'children'> {
  data: PerformanceData;
  timeWindow: string;
}

export function PerformanceTrendChart({
  data,
  timeWindow,
  ...baseProps
}: PerformanceTrendChartProps) {
  // Generate mock historical data for trend visualization
  const generateTrendData = () => {
    const points = 20;
    const now = Date.now();
    const intervalMs = getIntervalMs(timeWindow, points);
    
    return Array.from({ length: points }, (_, i) => {
      const timestamp = now - (points - 1 - i) * intervalMs;
      const variance = 0.1; // 10% variance
      
      return {
        timestamp,
        time: formatTime(new Date(timestamp)),
        responseTime: data.performanceSummary.averageResponseTime * (1 + (Math.random() - 0.5) * variance),
        errorRate: Math.max(0, data.performanceSummary.errorRate * (1 + (Math.random() - 0.5) * variance)),
        memoryUsage: data.performanceSummary.memoryUsage * (1 + (Math.random() - 0.5) * variance * 0.5),
        cacheHitRate: Math.min(100, data.performanceSummary.cacheHitRate * (1 + (Math.random() - 0.5) * variance * 0.3)),
        slowQueries: Math.max(0, Math.floor(data.performanceSummary.slowQueries * (1 + (Math.random() - 0.5) * variance * 2))),
        requestsPerMinute: data.apiMetrics.requestsPerMinute * (1 + (Math.random() - 0.5) * variance),
      };
    });
  };

  const getIntervalMs = (timeWindow: string, points: number) => {
    const timeMap: Record<string, number> = {
      '5m': 5 * 60 * 1000,
      '15m': 15 * 60 * 1000,
      '1h': 60 * 60 * 1000,
      '6h': 6 * 60 * 60 * 1000,
      '24h': 24 * 60 * 60 * 1000,
    };
    return (timeMap[timeWindow] || timeMap['15m']) / points;
  };

  const formatTime = (date: Date) => {
    if (timeWindow === '5m' || timeWindow === '15m') {
      return date.toLocaleTimeString('en-US', { 
        hour: '2-digit', 
        minute: '2-digit',
        second: '2-digit',
        hour12: false 
      });
    } else if (timeWindow === '1h' || timeWindow === '6h') {
      return date.toLocaleTimeString('en-US', { 
        hour: '2-digit', 
        minute: '2-digit',
        hour12: false 
      });
    } else {
      return date.toLocaleDateString('en-US', { 
        month: 'short', 
        day: 'numeric',
        hour: '2-digit'
      });
    }
  };

  const formatTooltipValue = (value: number, name: string) => {
    switch (name) {
      case 'responseTime':
        return [`${Math.round(value)}ms`, 'Response Time'];
      case 'errorRate':
        return [`${value.toFixed(2)}%`, 'Error Rate'];
      case 'memoryUsage':
        return [`${value.toFixed(1)}%`, 'Memory Usage'];
      case 'cacheHitRate':
        return [`${value.toFixed(1)}%`, 'Cache Hit Rate'];
      case 'slowQueries':
        return [`${Math.round(value)}`, 'Slow Queries'];
      case 'requestsPerMinute':
        return [`${Math.round(value)}`, 'Requests/min'];
      default:
        return [value.toString(), name];
    }
  };

  const trendData = generateTrendData();

  return (
    <BaseChart {...baseProps}>
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Response Time & Error Rate */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-4">
          <h4 className="text-sm font-semibold mb-4">Response Time & Error Rate Trends</h4>
          <ResponsiveContainer width="100%" height={300}>
            <ComposedChart data={trendData}>
              <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
              <XAxis 
                dataKey="time" 
                className="text-xs"
                interval="preserveStartEnd"
              />
              <YAxis 
                yAxisId="responseTime"
                orientation="left"
                className="text-xs"
                label={{ value: 'Response Time (ms)', angle: -90, position: 'insideLeft' }}
              />
              <YAxis 
                yAxisId="errorRate"
                orientation="right"
                className="text-xs"
                label={{ value: 'Error Rate (%)', angle: 90, position: 'insideRight' }}
              />
              <Tooltip
                formatter={formatTooltipValue}
                contentStyle={{
                  backgroundColor: 'hsl(var(--card))',
                  border: '1px solid hsl(var(--border))',
                  borderRadius: '6px',
                  fontSize: '12px'
                }}
              />
              <Legend />
              
              <Line
                yAxisId="responseTime"
                type="monotone"
                dataKey="responseTime"
                stroke="hsl(var(--primary))"
                strokeWidth={3}
                dot={false}
                activeDot={{ r: 6, fill: 'hsl(var(--primary))', strokeWidth: 2 }}
                name="Response Time"
                connectNulls={true}
              />

              <Line
                yAxisId="errorRate"
                type="monotone"
                dataKey="errorRate"
                stroke="hsl(var(--destructive))"
                strokeWidth={3}
                dot={false}
                activeDot={{ r: 6, fill: 'hsl(var(--destructive))', strokeWidth: 2 }}
                name="Error Rate"
                connectNulls={true}
              />
              
              {/* Reference lines */}
              <ReferenceLine yAxisId="responseTime" y={1000} stroke="orange" strokeDasharray="5 5" />
              <ReferenceLine yAxisId="errorRate" y={2} stroke="red" strokeDasharray="5 5" />
            </ComposedChart>
          </ResponsiveContainer>
        </div>

        {/* Memory Usage & Cache Hit Rate */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-4">
          <h4 className="text-sm font-semibold mb-4">Memory & Cache Performance</h4>
          <ResponsiveContainer width="100%" height={300}>
            <LineChart data={trendData}>
              <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
              <XAxis 
                dataKey="time" 
                className="text-xs"
                interval="preserveStartEnd"
              />
              <YAxis 
                className="text-xs"
                domain={[0, 100]}
                label={{ value: 'Percentage (%)', angle: -90, position: 'insideLeft' }}
              />
              <Tooltip
                formatter={formatTooltipValue}
                contentStyle={{
                  backgroundColor: 'hsl(var(--card))',
                  border: '1px solid hsl(var(--border))',
                  borderRadius: '6px',
                  fontSize: '12px'
                }}
              />
              <Legend />
              
              <Line
                type="monotone"
                dataKey="memoryUsage"
                stroke="hsl(var(--chart-2))"
                strokeWidth={3}
                dot={false}
                activeDot={{ r: 6, fill: 'hsl(var(--chart-2))', strokeWidth: 2 }}
                name="Memory Usage"
                connectNulls={true}
              />

              <Line
                type="monotone"
                dataKey="cacheHitRate"
                stroke="hsl(var(--chart-3))"
                strokeWidth={3}
                dot={false}
                activeDot={{ r: 6, fill: 'hsl(var(--chart-3))', strokeWidth: 2 }}
                name="Cache Hit Rate"
                connectNulls={true}
              />
              
              {/* Reference lines */}
              <ReferenceLine y={80} stroke="orange" strokeDasharray="5 5" />
              <ReferenceLine y={90} stroke="red" strokeDasharray="5 5" />
            </LineChart>
          </ResponsiveContainer>
        </div>

        {/* Request Volume & Slow Queries */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-4">
          <h4 className="text-sm font-semibold mb-4">Request Volume & Database Performance</h4>
          <ResponsiveContainer width="100%" height={300}>
            <ComposedChart data={trendData}>
              <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
              <XAxis 
                dataKey="time" 
                className="text-xs"
                interval="preserveStartEnd"
              />
              <YAxis 
                yAxisId="requests"
                orientation="left"
                className="text-xs"
                label={{ value: 'Requests/min', angle: -90, position: 'insideLeft' }}
              />
              <YAxis 
                yAxisId="queries"
                orientation="right"
                className="text-xs"
                label={{ value: 'Slow Queries', angle: 90, position: 'insideRight' }}
              />
              <Tooltip
                formatter={formatTooltipValue}
                contentStyle={{
                  backgroundColor: 'hsl(var(--card))',
                  border: '1px solid hsl(var(--border))',
                  borderRadius: '6px',
                  fontSize: '12px'
                }}
              />
              <Legend />
              
              <Line
                yAxisId="requests"
                type="monotone"
                dataKey="requestsPerMinute"
                stroke="hsl(var(--chart-4))"
                strokeWidth={3}
                dot={false}
                activeDot={{ r: 6, fill: 'hsl(var(--chart-4))', strokeWidth: 2 }}
                name="Requests/min"
                connectNulls={true}
              />
              
              <Bar
                yAxisId="queries"
                dataKey="slowQueries"
                fill="hsl(var(--chart-5))"
                name="Slow Queries"
                opacity={0.7}
              />
            </ComposedChart>
          </ResponsiveContainer>
        </div>

        {/* Performance Summary */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-4">
          <h4 className="text-sm font-semibold mb-4">Current Performance Metrics</h4>
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded">
                <div className="text-lg font-bold text-primary">
                  {Math.round(data.performanceSummary.averageResponseTime)}ms
                </div>
                <div className="text-xs text-muted-foreground">Avg Response Time</div>
              </div>
              <div className="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded">
                <div className="text-lg font-bold text-chart-2">
                  {data.performanceSummary.errorRate.toFixed(2)}%
                </div>
                <div className="text-xs text-muted-foreground">Error Rate</div>
              </div>
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div className="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded">
                <div className="text-lg font-bold text-chart-3">
                  {data.performanceSummary.cacheHitRate.toFixed(1)}%
                </div>
                <div className="text-xs text-muted-foreground">Cache Hit Rate</div>
              </div>
              <div className="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded">
                <div className="text-lg font-bold text-chart-4">
                  {data.performanceSummary.slowQueries}
                </div>
                <div className="text-xs text-muted-foreground">Slow Queries</div>
              </div>
            </div>
            
            <div className="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded">
              <div className="text-lg font-bold text-chart-5">
                {data.performanceSummary.memoryUsage.toFixed(1)}%
              </div>
              <div className="text-xs text-muted-foreground">Memory Usage</div>
            </div>
          </div>
        </div>
      </div>
    </BaseChart>
  );
}
