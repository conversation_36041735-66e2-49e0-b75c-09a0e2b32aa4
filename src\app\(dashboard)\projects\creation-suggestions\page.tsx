'use client';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Skeleton } from '@/components/ui/skeleton';
import {
  Lightbulb,
  ArrowRight,
  FileText,
  CheckCircle,
  Clock,
  DollarSign,
  Building2,
  AlertCircle,
  ArrowLeft,
  RefreshCw
} from 'lucide-react';
import Link from 'next/link';
import { PageLayout } from '@/components/layout/page-layout';
import { useRouter } from 'next/navigation';
import { useProjectCreationSuggestions } from '@/hooks/use-project-creation-suggestions';

export default function ProjectCreationSuggestionsPage() {
  const router = useRouter();
  const { suggestions, loading, error, refresh } = useProjectCreationSuggestions({
    limit: 20,
    includeWithHandovers: true,
  });

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
    }).format(value);
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      completed: { label: 'Completed', color: 'bg-green-100 text-green-800', icon: CheckCircle },
      in_progress: { label: 'In Progress', color: 'bg-blue-100 text-blue-800', icon: Clock },
      pending: { label: 'Pending', color: 'bg-yellow-100 text-yellow-800', icon: Clock },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending;
    const Icon = config.icon;
    
    return (
      <Badge className={config.color}>
        <Icon className="w-3 h-3 mr-1" />
        {config.label}
      </Badge>
    );
  };

  const actions = (
    <div className="flex items-center gap-2">
      <Button
        variant="outline"
        size="sm"
        onClick={refresh}
        disabled={loading}
      >
        <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
        Refresh
      </Button>
      <Button variant="outline" size="sm" onClick={() => router.push('/projects')}>
        <ArrowLeft className="h-4 w-4 mr-2" />
        Back to Projects
      </Button>
    </div>
  );

  return (
      <PageLayout
        title="Project Creation Suggestions"
        description="Opportunities ready for project creation based on handover status"
        actions={actions}
      >
        <div className="space-y-6">
          {/* Info Alert */}
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              These are closed won opportunities that are ready for project creation.
              Opportunities with completed handovers are prioritized.
            </AlertDescription>
          </Alert>

          {/* Error Alert */}
          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                {error}
              </AlertDescription>
            </Alert>
          )}

        {/* Loading State */}
        {loading && (
          <div className="space-y-4">
            {[...Array(3)].map((_, index) => (
              <Card key={index}>
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <div className="flex-1 space-y-2">
                      <Skeleton className="h-6 w-3/4" />
                      <Skeleton className="h-4 w-1/2" />
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div className="space-y-2">
                        <Skeleton className="h-4 w-16" />
                        <Skeleton className="h-4 w-24" />
                        <Skeleton className="h-4 w-20" />
                      </div>
                      <div className="space-y-2">
                        <Skeleton className="h-4 w-12" />
                        <Skeleton className="h-4 w-20" />
                      </div>
                      <div className="space-y-2">
                        <Skeleton className="h-4 w-12" />
                        <Skeleton className="h-6 w-24" />
                      </div>
                    </div>
                    <div className="flex gap-2 pt-4 border-t">
                      <Skeleton className="h-8 w-32" />
                      <Skeleton className="h-8 w-28" />
                      <Skeleton className="h-8 w-36" />
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}

        {/* Suggestions List */}
        {!loading && (
          <div className="space-y-4">
            {suggestions.map((suggestion) => (
            <Card key={suggestion.opportunity.id} className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <CardTitle className="flex items-center gap-2">
                      <Building2 className="w-5 h-5" />
                      {suggestion.opportunity.title}
                      {suggestion.hasHandover && (
                        <Badge variant="outline" className="text-xs">
                          Has Handover
                        </Badge>
                      )}
                      {suggestion.readyForProject && (
                        <Badge className="bg-green-100 text-green-800 text-xs">
                          Ready
                        </Badge>
                      )}
                    </CardTitle>
                    <CardDescription className="mt-2">
                      Suggested project name: {suggestion.suggestedProjectName}
                    </CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {/* Opportunity Details */}
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                    <div>
                      <div className="font-medium text-gray-900 mb-1">Client</div>
                      <div className="text-gray-600">
                        <div>{suggestion.opportunity.company.name}</div>
                        <div>{suggestion.opportunity.contact.firstName} {suggestion.opportunity.contact.lastName}</div>
                      </div>
                    </div>
                    
                    <div>
                      <div className="font-medium text-gray-900 mb-1">Value</div>
                      <div className="flex items-center gap-1 text-gray-600">
                        <DollarSign className="w-4 h-4" />
                        {formatCurrency(suggestion.opportunity.value)}
                      </div>
                    </div>
                    
                    <div>
                      <div className="font-medium text-gray-900 mb-1">Stage</div>
                      <Badge variant="outline">{suggestion.opportunity.stage}</Badge>
                    </div>
                  </div>

                  {/* Handover Details */}
                  {suggestion.handover && (
                    <div className="border-t pt-4">
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                        <div>
                          <div className="font-medium text-gray-900 mb-1">Handover Status</div>
                          {getStatusBadge(suggestion.handover.status)}
                        </div>
                        
                        <div>
                          <div className="font-medium text-gray-900 mb-1">Progress</div>
                          <div className="flex items-center gap-2">
                            <div className="w-16 bg-gray-200 rounded-full h-2">
                              <div 
                                className="bg-blue-600 h-2 rounded-full" 
                                style={{ width: `${suggestion.handover.checklistProgress}%` }}
                              ></div>
                            </div>
                            <span className="text-gray-600">{suggestion.handover.checklistProgress}%</span>
                          </div>
                        </div>
                        
                        <div>
                          <div className="font-medium text-gray-900 mb-1">Team</div>
                          <div className="text-gray-600">
                            <div>Sales: {suggestion.handover.salesRep.firstName} {suggestion.handover.salesRep.lastName}</div>
                            <div>Delivery: {suggestion.handover.deliveryManager.firstName} {suggestion.handover.deliveryManager.lastName}</div>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Actions */}
                  <div className="flex gap-2 pt-4 border-t">
                    <Button variant="outline" size="sm" asChild>
                      <Link href={`/opportunities/${suggestion.opportunity.id}`}>
                        View Opportunity
                      </Link>
                    </Button>
                    
                    {suggestion.hasHandover && (
                      <Button variant="outline" size="sm" asChild>
                        <Link href={`/handovers/${suggestion.handover?.id}`}>
                          View Handover
                        </Link>
                      </Button>
                    )}
                    
                    {suggestion.hasHandover && suggestion.readyForProject ? (
                      <Button size="sm" asChild>
                        <Link href={`/handovers/${suggestion.handover?.id}/create-project`} className="flex items-center">
                          <ArrowRight className="w-4 h-4 mr-2" />
                          Create Project from Handover
                        </Link>
                      </Button>
                    ) : (
                      <Button size="sm" asChild>
                        <Link href={`/opportunities/${suggestion.opportunity.id}/create-project`} className="flex items-center">
                          <FileText className="w-4 h-4 mr-2" />
                          Create Project
                        </Link>
                      </Button>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
            ))}
          </div>
        )}

        {/* Empty State */}
        {!loading && suggestions.length === 0 && (
          <Card>
            <CardContent className="pt-6">
              <div className="text-center py-8">
                <Lightbulb className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No suggestions available</h3>
                <p className="text-gray-600 mb-4">
                  There are currently no closed won opportunities ready for project creation
                </p>
                <Button variant="outline" asChild>
                  <Link href="/opportunities">
                    View Opportunities
                  </Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Summary Stats */}
        {!loading && (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardContent className="pt-6">
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">{suggestions.length}</div>
                  <div className="text-sm text-gray-600">Total Suggestions</div>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="pt-6">
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">
                    {suggestions.filter(s => s.hasHandover).length}
                  </div>
                  <div className="text-sm text-gray-600">With Handovers</div>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="pt-6">
                <div className="text-center">
                  <div className="text-2xl font-bold text-purple-600">
                    {formatCurrency(suggestions.reduce((sum, s) => sum + s.opportunity.value, 0))}
                  </div>
                  <div className="text-sm text-gray-600">Total Value</div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}
        </div>
      </PageLayout>
  );
}
