import { z } from 'zod';
import { prisma } from '@/lib/prisma';
import { aiService } from './ai-service';
import { leadManagementService } from './lead-management';
import { leadNotificationService } from './lead-notifications';

// Validation schemas
export const emailLeadCaptureSchema = z.object({
  fromEmail: z.string().email('Invalid email address'),
  fromName: z.string().optional(),
  subject: z.string().min(1, 'Subject is required'),
  content: z.string().min(1, 'Email content is required'),
  receivedAt: z.union([
    z.date(),
    z.string().datetime('Invalid date format')
  ]).transform((val) => typeof val === 'string' ? new Date(val) : val).default(() => new Date()),
  emailProvider: z.string().optional(),
  messageId: z.string().optional(),
  inReplyTo: z.string().optional(),
  references: z.string().optional()
});

export const sentimentAnalysisSchema = z.object({
  sentiment: z.enum(['positive', 'negative', 'neutral']),
  confidence: z.number().min(0).max(1),
  emotions: z.array(z.string()).optional(),
  urgency: z.enum(['low', 'medium', 'high']),
  intent: z.enum(['inquiry', 'complaint', 'request', 'follow_up', 'other']),
  keyPhrases: z.array(z.string()).optional()
});

export type EmailLeadCaptureData = z.infer<typeof emailLeadCaptureSchema>;
export type SentimentAnalysis = z.infer<typeof sentimentAnalysisSchema>;

export interface EmailLeadCapture {
  id: string;
  tenantId: string;
  fromEmail: string;
  fromName?: string;
  subject: string;
  content: string;
  receivedAt: Date;
  processed: boolean;
  leadId?: string;
  contactId?: string;
  sentimentAnalysis?: SentimentAnalysis;
  extractedData?: Record<string, any>;
  processingError?: string;
  createdAt: Date;
}

export interface ExtractedLeadData {
  firstName?: string;
  lastName?: string;
  email: string;
  phone?: string;
  company?: string;
  title?: string;
  description: string;
  source: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  value?: number;
  expectedCloseDate?: Date;
}

export class EmailLeadCaptureService {
  /**
   * Process incoming email and extract lead information
   */
  async processIncomingEmail(
    tenantId: string,
    emailData: EmailLeadCaptureData
  ): Promise<{
    emailCapture: EmailLeadCapture;
    lead?: any;
    contact?: any;
    sentimentAnalysis: SentimentAnalysis;
  }> {
    try {
      // 1. Perform sentiment analysis
      const sentimentAnalysis = await this.analyzeSentiment(
        emailData.content,
        emailData.subject
      );

      // 2. Extract lead data from email content
      const extractedData = await this.extractLeadData(
        emailData,
        sentimentAnalysis
      );

      // 3. Create email capture record
      const emailCapture = await prisma.emailLeadCapture.create({
        data: {
          tenantId,
          fromEmail: emailData.fromEmail,
          fromName: emailData.fromName,
          subject: emailData.subject,
          content: emailData.content,
          receivedAt: emailData.receivedAt,
          processed: false,
          sentimentAnalysis,
          extractedData
        }
      });

      // 4. Check if contact already exists
      let contact = await prisma.contact.findFirst({
        where: {
          tenantId,
          email: emailData.fromEmail
        }
      });

      // 5. Create contact if doesn't exist
      if (!contact && extractedData.firstName) {
        contact = await prisma.contact.create({
          data: {
            tenantId,
            firstName: extractedData.firstName,
            lastName: extractedData.lastName || '',
            email: extractedData.email,
            phone: extractedData.phone,
            createdById: null // System created
          }
        });
      }

      // 6. Get or create "Email" lead source
      let emailLeadSource = await prisma.leadSource.findFirst({
        where: {
          tenantId,
          name: 'Email'
        }
      });

      if (!emailLeadSource) {
        emailLeadSource = await prisma.leadSource.create({
          data: {
            tenantId,
            name: 'Email',
            description: 'Leads captured from incoming emails',
            category: 'digital',
            icon: 'envelope',
            color: '#3B82F6'
          }
        });
      }

      // 7. Create or update lead
      let lead = null;
      if (extractedData.title) {
        // Check if lead already exists for this contact
        const existingLead = await prisma.lead.findFirst({
          where: {
            tenantId,
            contactId: contact?.id,
            status: { in: ['new', 'contacted', 'qualified'] }
          }
        });

        if (existingLead) {
          // Update existing lead with new information
          lead = await leadManagementService.updateLead(
            existingLead.id,
            tenantId,
            {
              description: `${existingLead.description || ''}\n\nEmail received: ${emailData.subject}\n${emailData.content}`.trim(),
              priority: this.determinePriority(sentimentAnalysis),
              leadSourceId: emailLeadSource.id
            },
            null
          );
        } else {
          // Create new lead
          lead = await leadManagementService.createLead(
            tenantId,
            {
              title: extractedData.title,
              description: extractedData.description,
              contactId: contact?.id,
              companyId: null, // TODO: Extract company info
              leadSourceId: emailLeadSource.id,
              priority: extractedData.priority,
              value: extractedData.value,
              expectedCloseDate: extractedData.expectedCloseDate
            },
            null
          );
        }

        // Update email capture with lead ID
        await prisma.emailLeadCapture.update({
          where: { id: emailCapture.id },
          data: {
            leadId: lead.id,
            contactId: contact?.id,
            processed: true
          }
        });

        // Send notification for new lead from email
        if (lead) {
          await leadNotificationService.triggerLeadNotification(
            'new_lead',
            lead,
            {
              source: 'email',
              sentiment: sentimentAnalysis.sentiment,
              urgency: sentimentAnalysis.urgency
            }
          );
        }
      }

      return {
        emailCapture: emailCapture as EmailLeadCapture,
        lead,
        contact,
        sentimentAnalysis
      };
    } catch (error) {
      // Create error record
      const emailCapture = await prisma.emailLeadCapture.create({
        data: {
          tenantId,
          fromEmail: emailData.fromEmail,
          fromName: emailData.fromName,
          subject: emailData.subject,
          content: emailData.content,
          receivedAt: emailData.receivedAt,
          processed: false,
          processingError: error instanceof Error ? error.message : 'Unknown error'
        }
      });

      throw error;
    }
  }

  /**
   * Analyze sentiment of email content
   */
  private async analyzeSentiment(
    content: string,
    subject: string
  ): Promise<SentimentAnalysis> {
    try {
      const prompt = `
        Analyze the sentiment and intent of this email:
        
        Subject: ${subject}
        Content: ${content}
        
        Provide analysis in this JSON format:
        {
          "sentiment": "positive|negative|neutral",
          "confidence": 0.0-1.0,
          "emotions": ["emotion1", "emotion2"],
          "urgency": "low|medium|high",
          "intent": "inquiry|complaint|request|follow_up|other",
          "keyPhrases": ["phrase1", "phrase2"]
        }
      `;

      const response = await aiService.generateResponse({
        prompt,
        systemPrompt: 'You are an expert at analyzing email sentiment and intent. Respond only with valid JSON.',
        context: { content, subject },
        tenantId: 'system',
        userId: 'system',
        feature: 'sentiment_analysis'
      });

      const analysis = JSON.parse(response.content);
      return sentimentAnalysisSchema.parse(analysis);
    } catch (error) {
      // Return default analysis
      return {
        sentiment: 'neutral',
        confidence: 0.5,
        urgency: 'medium',
        intent: 'inquiry'
      };
    }
  }

  /**
   * Extract lead data from email content
   */
  private async extractLeadData(
    emailData: EmailLeadCaptureData,
    sentimentAnalysis: SentimentAnalysis
  ): Promise<ExtractedLeadData> {
    try {
      const prompt = `
        Extract lead information from this email:
        
        From: ${emailData.fromName || ''} <${emailData.fromEmail}>
        Subject: ${emailData.subject}
        Content: ${emailData.content}
        
        Extract the following information in JSON format:
        {
          "firstName": "string|null",
          "lastName": "string|null",
          "email": "string",
          "phone": "string|null",
          "company": "string|null",
          "title": "string (lead title/summary)",
          "description": "string (detailed description)",
          "source": "email",
          "priority": "low|medium|high|urgent",
          "value": number|null,
          "expectedCloseDate": "ISO date string|null"
        }
        
        Guidelines:
        - Extract names from email signature or content
        - Create a meaningful lead title based on the inquiry
        - Set priority based on urgency and content
        - Estimate value if mentioned or implied
        - Extract phone numbers in any format
      `;

      const response = await aiService.generateResponse({
        prompt,
        systemPrompt: 'You are an expert at extracting structured data from emails. Respond only with valid JSON.',
        context: { emailData, sentimentAnalysis },
        tenantId: 'system',
        userId: 'system',
        feature: 'lead_extraction'
      });

      const extractedData = JSON.parse(response.content);
      
      return {
        firstName: extractedData.firstName,
        lastName: extractedData.lastName,
        email: emailData.fromEmail,
        phone: extractedData.phone,
        company: extractedData.company,
        title: extractedData.title || `Inquiry from ${emailData.fromEmail}`,
        description: extractedData.description || emailData.content,
        source: 'email',
        priority: this.determinePriority(sentimentAnalysis),
        value: extractedData.value,
        expectedCloseDate: extractedData.expectedCloseDate ? new Date(extractedData.expectedCloseDate) : undefined
      };
    } catch (error) {
      // Return basic extracted data
      return {
        email: emailData.fromEmail,
        title: `Email Inquiry: ${emailData.subject}`,
        description: emailData.content,
        source: 'email',
        priority: this.determinePriority(sentimentAnalysis)
      };
    }
  }

  /**
   * Determine lead priority based on sentiment analysis
   */
  private determinePriority(sentimentAnalysis: SentimentAnalysis): 'low' | 'medium' | 'high' | 'urgent' {
    if (sentimentAnalysis.urgency === 'high') return 'urgent';
    if (sentimentAnalysis.urgency === 'medium') return 'high';
    if (sentimentAnalysis.sentiment === 'negative') return 'high';
    if (sentimentAnalysis.intent === 'complaint') return 'urgent';
    return 'medium';
  }

  /**
   * Get email captures for a tenant
   */
  async getEmailCaptures(
    tenantId: string,
    options: {
      limit?: number;
      offset?: number;
      processed?: boolean;
    } = {}
  ): Promise<{ captures: EmailLeadCapture[]; total: number }> {
    const { limit = 50, offset = 0, processed } = options;

    const where = {
      tenantId,
      ...(processed !== undefined && { processed })
    };

    const [captures, total] = await Promise.all([
      prisma.emailLeadCapture.findMany({
        where,
        include: {
          lead: {
            include: {
              contact: true,
              company: true
            }
          },
          contact: true
        },
        orderBy: { receivedAt: 'desc' },
        take: limit,
        skip: offset
      }),
      prisma.emailLeadCapture.count({ where })
    ]);

    return {
      captures: captures as EmailLeadCapture[],
      total
    };
  }

  /**
   * Reprocess failed email captures
   */
  async reprocessFailedCaptures(tenantId: string): Promise<{
    processed: number;
    errors: string[];
  }> {
    const failedCaptures = await prisma.emailLeadCapture.findMany({
      where: {
        tenantId,
        processed: false,
        processingError: { not: null }
      },
      take: 10 // Process in batches
    });

    let processed = 0;
    const errors: string[] = [];

    for (const capture of failedCaptures) {
      try {
        await this.processIncomingEmail(tenantId, {
          fromEmail: capture.fromEmail,
          fromName: capture.fromName || undefined,
          subject: capture.subject,
          content: capture.content,
          receivedAt: capture.receivedAt
        });
        processed++;
      } catch (error) {
        errors.push(`Capture ${capture.id}: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }

    return { processed, errors };
  }
}

export const emailLeadCaptureService = new EmailLeadCaptureService();
