'use client';

import React from 'react';
import {
  RadarChart,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis,
  Radar,
  ResponsiveContainer,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Cell
} from 'recharts';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

interface ScoringFactor {
  score: number;
  weight: number;
  reasoning: string;
}

interface ScoringChartProps {
  factors: {
    dealSize: ScoringFactor;
    timeline: ScoringFactor;
    authority: ScoringFactor;
    need: ScoringFactor;
    competition: ScoringFactor;
  };
  totalScore: number;
  confidence: number;
}

export function ScoringChart({ factors, totalScore, confidence }: ScoringChartProps) {
  // Prepare data for radar chart
  const radarData = Object.entries(factors).map(([key, factor]) => ({
    factor: key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase()),
    score: factor.score,
    fullMark: 100
  }));

  // Prepare data for bar chart
  const barData = Object.entries(factors).map(([key, factor]) => ({
    name: key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase()),
    score: factor.score,
    weight: factor.weight
  }));

  // Color scheme for charts
  const getScoreColor = (score: number) => {
    if (score >= 80) return '#10B981'; // Green
    if (score >= 60) return '#F59E0B'; // Yellow
    if (score >= 40) return '#EF4444'; // Red
    return '#6B7280'; // Gray
  };

  const COLORS = ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6'];

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      {/* Radar Chart */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Scoring Factors Overview</CardTitle>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={300}>
            <RadarChart data={radarData}>
              <PolarGrid />
              <PolarAngleAxis 
                dataKey="factor" 
                className="text-xs"
                tick={{ fontSize: 12 }}
              />
              <PolarRadiusAxis 
                angle={90} 
                domain={[0, 100]} 
                className="text-xs"
                tick={{ fontSize: 10 }}
              />
              <Radar
                name="Score"
                dataKey="score"
                stroke="#3B82F6"
                fill="#3B82F6"
                fillOpacity={0.3}
                strokeWidth={2}
              />
            </RadarChart>
          </ResponsiveContainer>
          
          {/* Overall Score Display */}
          <div className="mt-4 text-center">
            <div className="text-3xl font-bold" style={{ color: getScoreColor(totalScore) }}>
              {totalScore}/100
            </div>
            <div className="text-sm text-muted-foreground">
              Overall Score • {Math.round(confidence * 100)}% confidence
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Bar Chart */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Factor Breakdown</CardTitle>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={barData} layout="horizontal">
              <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
              <XAxis 
                type="number" 
                domain={[0, 100]}
                className="text-xs"
              />
              <YAxis 
                type="category" 
                dataKey="name"
                className="text-xs"
                width={80}
              />
              <Tooltip
                formatter={(value: number, name: string) => [
                  `${value}/100`,
                  name === 'score' ? 'Score' : 'Weight'
                ]}
                contentStyle={{
                  backgroundColor: 'white',
                  border: '1px solid #e5e7eb',
                  borderRadius: '8px',
                  fontSize: '12px'
                }}
              />
              <Bar dataKey="score" radius={[0, 4, 4, 0]}>
                {barData.map((entry, index) => (
                  <Cell 
                    key={`cell-${index}`} 
                    fill={getScoreColor(entry.score)} 
                  />
                ))}
              </Bar>
            </BarChart>
          </ResponsiveContainer>

          {/* Weight Information */}
          <div className="mt-4 space-y-2">
            <h4 className="text-sm font-medium">Factor Weights</h4>
            <div className="grid grid-cols-2 gap-2 text-xs">
              {Object.entries(factors).map(([key, factor]) => (
                <div key={key} className="flex justify-between">
                  <span className="capitalize">
                    {key.replace(/([A-Z])/g, ' $1')}:
                  </span>
                  <span className="font-medium">{factor.weight}%</span>
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

// Forecast Timeline Chart Component
interface ForecastTimelineProps {
  predictedCloseDate: string;
  originalCloseDate: string;
  confidence: number;
  stageHistory?: Array<{
    stage: string;
    date: string;
  }>;
}

export function ForecastTimelineChart({ 
  predictedCloseDate, 
  originalCloseDate, 
  confidence,
  stageHistory = []
}: ForecastTimelineProps) {
  // Prepare timeline data
  const timelineData = [
    ...stageHistory.map(item => ({
      date: item.date,
      stage: item.stage,
      type: 'historical',
      probability: 0 // Would be calculated based on stage
    })),
    {
      date: originalCloseDate,
      stage: 'Original Target',
      type: 'target',
      probability: 0
    },
    {
      date: predictedCloseDate,
      stage: 'AI Prediction',
      type: 'prediction',
      probability: confidence * 100
    }
  ].sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());

  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleDateString('en-US', { 
        month: 'short', 
        day: 'numeric' 
      });
    } catch {
      return dateString;
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'historical': return '#6B7280';
      case 'target': return '#F59E0B';
      case 'prediction': return '#3B82F6';
      default: return '#6B7280';
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg">Deal Timeline Forecast</CardTitle>
      </CardHeader>
      <CardContent>
        <ResponsiveContainer width="100%" height={200}>
          <BarChart data={timelineData}>
            <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
            <XAxis 
              dataKey="date"
              tickFormatter={formatDate}
              className="text-xs"
            />
            <YAxis 
              domain={[0, 100]}
              className="text-xs"
              label={{ value: 'Confidence %', angle: -90, position: 'insideLeft' }}
            />
            <Tooltip
              formatter={(value: number) => [`${value}%`, 'Confidence']}
              labelFormatter={(label) => `Date: ${formatDate(label)}`}
              contentStyle={{
                backgroundColor: 'white',
                border: '1px solid #e5e7eb',
                borderRadius: '8px',
                fontSize: '12px'
              }}
            />
            <Bar dataKey="probability" radius={[4, 4, 0, 0]}>
              {timelineData.map((entry, index) => (
                <Cell 
                  key={`cell-${index}`} 
                  fill={getTypeColor(entry.type)} 
                />
              ))}
            </Bar>
          </BarChart>
        </ResponsiveContainer>

        {/* Timeline Legend */}
        <div className="mt-4 flex flex-wrap gap-4 text-xs">
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-gray-500 rounded"></div>
            <span>Historical</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-yellow-500 rounded"></div>
            <span>Original Target</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-blue-500 rounded"></div>
            <span>AI Prediction</span>
          </div>
        </div>

        {/* Date Comparison */}
        <div className="mt-4 grid grid-cols-2 gap-4 text-sm">
          <div>
            <div className="font-medium">Original Target</div>
            <div className="text-muted-foreground">{formatDate(originalCloseDate)}</div>
          </div>
          <div>
            <div className="font-medium">AI Prediction</div>
            <div className="text-muted-foreground">{formatDate(predictedCloseDate)}</div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
