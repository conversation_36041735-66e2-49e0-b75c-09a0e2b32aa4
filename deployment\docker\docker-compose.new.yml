# =============================================================================
# CRM Platform Docker Compose Configuration
# Base configuration for all environments
# =============================================================================

version: '3.8'

services:
  # -----------------------------------------------------------------------------
  # Next.js Application Service
  # -----------------------------------------------------------------------------
  app:
    build:
      context: ../../  # Build from project root
      dockerfile: deployment/docker/Dockerfile.new
      target: production
    container_name: crm-app
    restart: unless-stopped
    ports:
      - "${APP_PORT:-3010}:3000"
    env_file:
      - ../../.env.docker
    environment:
      # Application configuration
      - NODE_ENV=production
      - PORT=3000
      - HOSTNAME=0.0.0.0
      
      # Mermaid PNG generation environment variables
      - PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true
      - PUPPETEER_EXECUTABLE_PATH=/usr/bin/chromium-browser
      - PUPPETEER_ARGS=--no-sandbox --disable-setuid-sandbox --disable-dev-shm-usage --disable-accelerated-2d-canvas --no-first-run --no-zygote --disable-gpu
      - MERMAID_TEMP_DIR=/tmp/mermaid
      - CHROME_BIN=/usr/bin/chromium-browser
      
      # Database initialization flags
      - AUTO_MIGRATE=${AUTO_MIGRATE:-true}
      - AUTO_SEED=${AUTO_SEED:-false}
      
      # Security headers
      - NEXT_TELEMETRY_DISABLED=1
    volumes:
      - ../../uploads:/app/uploads:rw
      - mermaid-temp:/tmp/mermaid:rw
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - crm-network
    # Shared memory for Chrome/Puppeteer
    shm_size: 2gb
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    security_opt:
      - no-new-privileges:true
    deploy:
      resources:
        limits:
          cpus: '2'
          memory: 2G
        reservations:
          cpus: '0.5'
          memory: 512M
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # -----------------------------------------------------------------------------
  # PostgreSQL Database Service
  # -----------------------------------------------------------------------------
  postgres:
    image: postgres:16-alpine
    container_name: crm-postgres
    restart: unless-stopped
    ports:
      - "${POSTGRES_PORT:-5434}:5432"
    environment:
      - POSTGRES_USER=${POSTGRES_USER:-admin}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-admin}
      - POSTGRES_DB=${POSTGRES_DB:-crm_platform}
      - POSTGRES_INITDB_ARGS=--auth-host=scram-sha-256
      - POSTGRES_HOST_AUTH_METHOD=scram-sha-256
      - PGDATA=/var/lib/postgresql/data/pgdata
    volumes:
      - postgres-data:/var/lib/postgresql/data
      - ../../scripts/init-db:/docker-entrypoint-initdb.d:ro
    networks:
      - crm-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-admin} -d ${POSTGRES_DB:-crm_platform}"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    security_opt:
      - no-new-privileges:true
    deploy:
      resources:
        limits:
          cpus: '1'
          memory: 1G
        reservations:
          cpus: '0.25'
          memory: 256M
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # -----------------------------------------------------------------------------
  # Redis Cache Service
  # -----------------------------------------------------------------------------
  redis:
    image: redis:7-alpine
    container_name: crm-redis
    restart: unless-stopped
    ports:
      - "${REDIS_PORT:-6380}:6379"
    volumes:
      - redis-data:/data
      - ../config/redis/redis.conf:/usr/local/etc/redis/redis.conf:ro
    command: redis-server /usr/local/etc/redis/redis.conf --appendonly yes
    networks:
      - crm-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    security_opt:
      - no-new-privileges:true
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 512M
        reservations:
          cpus: '0.1'
          memory: 128M
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # -----------------------------------------------------------------------------
  # pgAdmin Database Management (Optional)
  # -----------------------------------------------------------------------------
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: crm-pgadmin
    restart: unless-stopped
    ports:
      - "${PGADMIN_PORT:-8082}:80"
    environment:
      - PGADMIN_DEFAULT_EMAIL=${PGADMIN_EMAIL:-<EMAIL>}
      - PGADMIN_DEFAULT_PASSWORD=${PGADMIN_PASSWORD:-admin}
      - PGADMIN_CONFIG_SERVER_MODE=False
      - PGADMIN_CONFIG_MASTER_PASSWORD_REQUIRED=False
    volumes:
      - pgadmin-data:/var/lib/pgadmin
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - crm-network
    profiles:
      - tools  # Only start with --profile tools
    security_opt:
      - no-new-privileges:true
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 512M
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

# =============================================================================
# Networks Configuration
# =============================================================================
networks:
  crm-network:
    driver: bridge
    name: crm-network

# =============================================================================
# Volumes Configuration
# =============================================================================
volumes:
  postgres-data:
    name: crm-postgres-data
  redis-data:
    name: crm-redis-data
  mermaid-temp:
    name: crm-mermaid-temp
  pgadmin-data:
    name: crm-pgadmin-data
