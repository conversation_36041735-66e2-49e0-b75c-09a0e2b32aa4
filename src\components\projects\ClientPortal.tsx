'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { 
  Calendar, 
  Clock, 
  DollarSign, 
  FileText, 
  MessageSquare, 
  CheckCircle, 
  AlertCircle,
  Download,
  Eye
} from 'lucide-react';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { EmptyState } from '@/components/ui/empty-state';
import { DocumentDetailsDialog } from '@/components/documents/document-details-dialog';

interface Project {
  id: string;
  name: string;
  description?: string;
  status: string;
  priority: string;
  progress: number;
  startDate?: string;
  endDate?: string;
  budget?: number;
  estimatedHours?: number;
  actualHours?: number;
  manager?: {
    firstName?: string;
    lastName?: string;
    email: string;
  };
}

interface Task {
  id: string;
  title: string;
  description?: string;
  status: string;
  priority: string;
  dueDate?: string;
  completedAt?: string;
  estimatedHours?: number;
  actualHours?: number;
}

interface Milestone {
  id: string;
  title: string;
  description?: string;
  status: string;
  dueDate: string;
  progress: number;
  completedAt?: string;
}

interface Document {
  id: string;
  name: string;
  fileSize?: number;
  mimeType?: string;
  uploadedAt: string;
}

interface ClientPortalProps {
  projectId: string;
  accessToken: string;
  canComment?: boolean;
  canViewTasks?: boolean;
  canViewMilestones?: boolean;
  canViewDocuments?: boolean;
}

export function ClientPortal({
  projectId,
  accessToken,
  canComment = false,
  canViewTasks = true,
  canViewMilestones = true,
  canViewDocuments = true,
}: ClientPortalProps) {
  const [project, setProject] = useState<Project | null>(null);
  const [tasks, setTasks] = useState<Task[]>([]);
  const [milestones, setMilestones] = useState<Milestone[]>([]);
  const [documents, setDocuments] = useState<Document[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [comment, setComment] = useState('');
  const [submittingComment, setSubmittingComment] = useState(false);
  const [detailsDialogOpen, setDetailsDialogOpen] = useState(false);
  const [selectedDocument, setSelectedDocument] = useState<Document | null>(null);
  const [aiAvailable, setAiAvailable] = useState(false);

  useEffect(() => {
    fetchProjectData();
    checkAiAvailability();
  }, [projectId, accessToken]);

  const fetchProjectData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Fetch project details
      const projectRes = await fetch(`/api/client-portal/projects/${projectId}`, {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
        },
      });

      if (!projectRes.ok) {
        throw new Error('Failed to fetch project data');
      }

      const projectData = await projectRes.json();
      setProject(projectData.data.project);

      // Fetch additional data based on permissions
      const promises = [];

      if (canViewTasks) {
        promises.push(
          fetch(`/api/client-portal/projects/${projectId}/tasks`, {
            headers: { 'Authorization': `Bearer ${accessToken}` },
          }).then(res => res.json()).then(data => setTasks(data.data.tasks || []))
        );
      }

      if (canViewMilestones) {
        promises.push(
          fetch(`/api/client-portal/projects/${projectId}/milestones`, {
            headers: { 'Authorization': `Bearer ${accessToken}` },
          }).then(res => res.json()).then(data => setMilestones(data.data.milestones || []))
        );
      }

      if (canViewDocuments) {
        promises.push(
          fetch(`/api/client-portal/projects/${projectId}/documents`, {
            headers: { 'Authorization': `Bearer ${accessToken}` },
          }).then(res => res.json()).then(data => setDocuments(data.data.documents || []))
        );
      }

      await Promise.all(promises);
    } catch (err) {
      console.error('Error fetching project data:', err);
      setError('Failed to load project data');
    } finally {
      setLoading(false);
    }
  };

  // Check AI availability
  const checkAiAvailability = async () => {
    try {
      const response = await fetch('/api/documents/analyze');
      const data = await response.json();
      setAiAvailable(data.success && data.data.available);
    } catch (error) {
      console.error('Failed to check AI availability:', error);
      setAiAvailable(false);
    }
  };

  // Document handlers
  const viewDocument = (document: Document) => {
    setSelectedDocument(document);
    setDetailsDialogOpen(true);
  };

  const downloadDocument = (document: Document) => {
    window.open(`/api/documents/${document.id}/download`, '_blank');
  };

  const openDocumentFile = (document: Document) => {
    window.open(`/api/documents/${document.id}/download`, '_blank');
  };

  const submitComment = async () => {
    if (!comment.trim() || !canComment) return;

    try {
      setSubmittingComment(true);
      const response = await fetch(`/api/client-portal/projects/${projectId}/comments`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${accessToken}`,
        },
        body: JSON.stringify({ comment: comment.trim() }),
      });

      if (!response.ok) {
        throw new Error('Failed to submit comment');
      }

      setComment('');
      // You might want to show a success message here
    } catch (err) {
      console.error('Error submitting comment:', err);
      // You might want to show an error message here
    } finally {
      setSubmittingComment(false);
    }
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      planning: { label: 'Planning', color: 'bg-blue-100 text-blue-800' },
      in_progress: { label: 'In Progress', color: 'bg-green-100 text-green-800' },
      on_hold: { label: 'On Hold', color: 'bg-yellow-100 text-yellow-800' },
      completed: { label: 'Completed', color: 'bg-gray-100 text-gray-800' },
      cancelled: { label: 'Cancelled', color: 'bg-red-100 text-red-800' },
      todo: { label: 'To Do', color: 'bg-gray-100 text-gray-800' },
      review: { label: 'Review', color: 'bg-orange-100 text-orange-800' },
      done: { label: 'Done', color: 'bg-green-100 text-green-800' },
      pending: { label: 'Pending', color: 'bg-blue-100 text-blue-800' },
      overdue: { label: 'Overdue', color: 'bg-red-100 text-red-800' },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.planning;
    return (
      <Badge className={config.color}>
        {config.label}
      </Badge>
    );
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const formatFileSize = (bytes: number) => {
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    if (bytes === 0) return '0 Bytes';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (error || !project) {
    return (
      <Card>
        <CardContent className="pt-6">
          <EmptyState
            icon={<AlertCircle className="w-12 h-12 text-red-500" />}
            title="Error Loading Project"
            description={error || 'Project not found'}
          />
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Project Header */}
      <Card>
        <CardHeader>
          <div className="flex items-start justify-between">
            <div>
              <CardTitle className="text-2xl">{project.name}</CardTitle>
              {project.description && (
                <CardDescription className="mt-2 text-base">
                  {project.description}
                </CardDescription>
              )}
            </div>
            <div className="flex items-center gap-2">
              {getStatusBadge(project.status)}
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="space-y-4">
              <div>
                <h4 className="font-medium text-sm text-muted-foreground">Progress</h4>
                <div className="flex items-center gap-2 mt-1">
                  <Progress value={project.progress} className="flex-1" />
                  <span className="text-sm font-medium">{Math.round(project.progress)}%</span>
                </div>
              </div>
              
              {project.manager && (
                <div>
                  <h4 className="font-medium text-sm text-muted-foreground">Project Manager</h4>
                  <p className="text-sm mt-1">
                    {project.manager.firstName} {project.manager.lastName}
                  </p>
                </div>
              )}
            </div>

            <div className="space-y-4">
              {project.startDate && project.endDate && (
                <div>
                  <h4 className="font-medium text-sm text-muted-foreground">Timeline</h4>
                  <div className="flex items-center gap-1 text-sm mt-1">
                    <Calendar className="w-4 h-4" />
                    {formatDate(project.startDate)} - {formatDate(project.endDate)}
                  </div>
                </div>
              )}

              {project.estimatedHours && (
                <div>
                  <h4 className="font-medium text-sm text-muted-foreground">Estimated Hours</h4>
                  <div className="flex items-center gap-1 text-sm mt-1">
                    <Clock className="w-4 h-4" />
                    {project.estimatedHours} hours
                    {project.actualHours && ` (${project.actualHours} actual)`}
                  </div>
                </div>
              )}
            </div>

            <div className="space-y-4">
              {project.budget && (
                <div>
                  <h4 className="font-medium text-sm text-muted-foreground">Budget</h4>
                  <div className="flex items-center gap-1 text-sm mt-1">
                    <DollarSign className="w-4 h-4" />
                    {formatCurrency(project.budget)}
                  </div>
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Content Tabs */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          {canViewTasks && <TabsTrigger value="tasks">Tasks</TabsTrigger>}
          {canViewMilestones && <TabsTrigger value="milestones">Milestones</TabsTrigger>}
          {canViewDocuments && <TabsTrigger value="documents">Documents</TabsTrigger>}
          {canComment && <TabsTrigger value="comments">Comments</TabsTrigger>}
        </TabsList>

        <TabsContent value="overview">
          <Card>
            <CardHeader>
              <CardTitle>Project Overview</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {canViewTasks && (
                  <div className="text-center p-4 border rounded-lg">
                    <div className="text-2xl font-bold text-blue-600">{tasks.length}</div>
                    <div className="text-sm text-muted-foreground">Total Tasks</div>
                  </div>
                )}
                
                {canViewMilestones && (
                  <div className="text-center p-4 border rounded-lg">
                    <div className="text-2xl font-bold text-green-600">{milestones.length}</div>
                    <div className="text-sm text-muted-foreground">Milestones</div>
                  </div>
                )}
                
                {canViewDocuments && (
                  <div className="text-center p-4 border rounded-lg">
                    <div className="text-2xl font-bold text-purple-600">{documents.length}</div>
                    <div className="text-sm text-muted-foreground">Documents</div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {canViewTasks && (
          <TabsContent value="tasks">
            <Card>
              <CardHeader>
                <CardTitle>Project Tasks</CardTitle>
                <CardDescription>Track the progress of individual tasks</CardDescription>
              </CardHeader>
              <CardContent>
                {tasks.length === 0 ? (
                  <EmptyState
                    icon={<CheckCircle className="w-12 h-12 text-muted-foreground" />}
                    title="No tasks available"
                    description="Tasks will appear here as they are created"
                  />
                ) : (
                  <div className="space-y-3">
                    {tasks.map((task) => (
                      <div key={task.id} className="flex items-center justify-between p-3 border rounded-lg">
                        <div className="flex-1">
                          <h4 className="font-medium">{task.title}</h4>
                          {task.description && (
                            <p className="text-sm text-muted-foreground mt-1">{task.description}</p>
                          )}
                          <div className="flex items-center gap-4 mt-2 text-xs text-muted-foreground">
                            {task.dueDate && (
                              <span>Due: {formatDate(task.dueDate)}</span>
                            )}
                            {task.estimatedHours && (
                              <span>{task.estimatedHours}h estimated</span>
                            )}
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          {getStatusBadge(task.status)}
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        )}

        {canViewMilestones && (
          <TabsContent value="milestones">
            <Card>
              <CardHeader>
                <CardTitle>Project Milestones</CardTitle>
                <CardDescription>Key project milestones and deliverables</CardDescription>
              </CardHeader>
              <CardContent>
                {milestones.length === 0 ? (
                  <EmptyState
                    icon={<Calendar className="w-12 h-12 text-muted-foreground" />}
                    title="No milestones set"
                    description="Milestones will appear here as they are defined"
                  />
                ) : (
                  <div className="space-y-4">
                    {milestones.map((milestone) => (
                      <div key={milestone.id} className="p-4 border rounded-lg">
                        <div className="flex items-start justify-between mb-2">
                          <h4 className="font-medium">{milestone.title}</h4>
                          {getStatusBadge(milestone.status)}
                        </div>
                        {milestone.description && (
                          <p className="text-sm text-muted-foreground mb-3">{milestone.description}</p>
                        )}
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <Progress value={milestone.progress} className="w-32" />
                            <span className="text-sm">{Math.round(milestone.progress)}%</span>
                          </div>
                          <div className="text-sm text-muted-foreground">
                            Due: {formatDate(milestone.dueDate)}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        )}

        {canViewDocuments && (
          <TabsContent value="documents">
            <Card>
              <CardHeader>
                <CardTitle>Project Documents</CardTitle>
                <CardDescription>Shared project files and documents</CardDescription>
              </CardHeader>
              <CardContent>
                {documents.length === 0 ? (
                  <EmptyState
                    icon={<FileText className="w-12 h-12 text-muted-foreground" />}
                    title="No documents available"
                    description="Documents will appear here as they are shared"
                  />
                ) : (
                  <div className="space-y-3">
                    {documents.map((document) => (
                      <div key={document.id} className="flex items-center justify-between p-3 border rounded-lg">
                        <div className="flex items-center gap-3">
                          <FileText className="w-5 h-5 text-muted-foreground" />
                          <div>
                            <h4 className="font-medium">{document.name}</h4>
                            <div className="flex items-center gap-2 text-xs text-muted-foreground">
                              {document.fileSize && (
                                <span>{formatFileSize(document.fileSize)}</span>
                              )}
                              <span>Uploaded {formatDate(document.uploadedAt)}</span>
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => viewDocument(document)}
                          >
                            <Eye className="w-4 h-4 mr-1" />
                            View Details
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => downloadDocument(document)}
                          >
                            <Download className="w-4 h-4 mr-1" />
                            Download
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        )}

        {canComment && (
          <TabsContent value="comments">
            <Card>
              <CardHeader>
                <CardTitle>Project Comments</CardTitle>
                <CardDescription>Share feedback and communicate with the project team</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <Textarea
                      placeholder="Share your feedback or ask questions..."
                      value={comment}
                      onChange={(e) => setComment(e.target.value)}
                      rows={4}
                    />
                    <div className="flex justify-end mt-2">
                      <Button 
                        onClick={submitComment}
                        disabled={!comment.trim() || submittingComment}
                      >
                        {submittingComment ? (
                          <>
                            <LoadingSpinner size="sm" className="mr-2" />
                            Submitting...
                          </>
                        ) : (
                          <>
                            <MessageSquare className="w-4 h-4 mr-2" />
                            Submit Comment
                          </>
                        )}
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        )}
      </Tabs>

      {/* Document Details Dialog */}
      <DocumentDetailsDialog
        open={detailsDialogOpen}
        onOpenChange={setDetailsDialogOpen}
        document={selectedDocument as any} // Type conversion needed due to different Document interface
        onDownload={(doc) => downloadDocument(selectedDocument!)}
        onView={(doc) => openDocumentFile(selectedDocument!)}
        aiAvailable={aiAvailable}
      />
    </div>
  );
}
