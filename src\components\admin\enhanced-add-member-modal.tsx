'use client';

import React, { useState, useEffect } from 'react';
import { XMarkIcon, UserPlusIcon, PlusIcon, UsersIcon } from '@heroicons/react/24/outline';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { ContactAvatar } from '@/components/ui/contact-avatar';

interface EnhancedAddMemberModalProps {
  teamId: string;
  onClose: () => void;
  onMemberAdded: () => void;
}

interface User {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  avatarUrl?: string;
}

interface Role {
  id: string;
  name: string;
  description: string | null;
  isSystemRole: boolean;
}

interface CreateUserFormData {
  email: string;
  firstName: string;
  lastName: string;
  role: string;
  department: string;
  jobTitle: string;
}

type ModalMode = 'select' | 'create';

export function EnhancedAddMemberModal({ teamId, onClose, onMemberAdded }: EnhancedAddMemberModalProps) {
  const [mode, setMode] = useState<ModalMode>('select');
  const [availableUsers, setAvailableUsers] = useState<User[]>([]);
  const [roles, setRoles] = useState<Role[]>([]);
  const [selectedUsers, setSelectedUsers] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingRoles, setIsLoadingRoles] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // Create user form state
  const [createUserForm, setCreateUserForm] = useState<CreateUserFormData>({
    email: '',
    firstName: '',
    lastName: '',
    role: '',
    department: '',
    jobTitle: ''
  });

  useEffect(() => {
    if (mode === 'select') {
      fetchAvailableUsers();
    }
    fetchRoles();
  }, [mode]);

  const fetchAvailableUsers = async () => {
    try {
      const response = await fetch('/api/users?available=true');
      if (response.ok) {
        const data = await response.json();
        setAvailableUsers(data.data || []);
      }
    } catch (err) {
      setError('Failed to fetch available users');
    }
  };

  const fetchRoles = async () => {
    try {
      setIsLoadingRoles(true);
      const response = await fetch('/api/roles/assignable');

      if (response.ok) {
        const data = await response.json();
        setRoles(data.data || []);

        // Set default role to the first non-admin role if available
        if (data.data && data.data.length > 0) {
          const defaultRole = data.data.find((role: Role) =>
            role.name.toLowerCase().includes('user') ||
            role.name.toLowerCase().includes('sales rep')
          ) || data.data[0];

          setCreateUserForm(prev => ({
            ...prev,
            role: defaultRole.name
          }));
        }
      }
    } catch (err) {
      console.error('Failed to fetch roles:', err);
    } finally {
      setIsLoadingRoles(false);
    }
  };

  const handleAddExistingMembers = async () => {
    if (selectedUsers.length === 0) {
      setError('Please select at least one user');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/teams/members', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          teamId,
          userIds: selectedUsers
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to add members');
      }

      onMemberAdded();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to add members');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCreateAndAddUser = async () => {
    if (!createUserForm.email || !createUserForm.firstName || !createUserForm.lastName || !createUserForm.role) {
      setError('Please fill in all required fields including role');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      // Create user with team assignment
      const response = await fetch('/api/users', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'invite', // Use invite to create without password
          email: createUserForm.email,
          firstName: createUserForm.firstName,
          lastName: createUserForm.lastName,
          role: createUserForm.role,
          teamId: teamId, // Automatically assign to this team
          department: createUserForm.department,
          jobTitle: createUserForm.jobTitle
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create user');
      }

      onMemberAdded();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create user');
    } finally {
      setIsLoading(false);
    }
  };

  const toggleUserSelection = (userId: string) => {
    setSelectedUsers(prev => 
      prev.includes(userId) 
        ? prev.filter(id => id !== userId)
        : [...prev, userId]
    );
  };

  const handleCreateUserFormChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setCreateUserForm(prev => ({
      ...prev,
      [name]: value
    }));
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg w-full max-w-2xl p-6 max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-semibold text-gray-900">Add Team Members</h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 p-1"
          >
            <XMarkIcon className="w-6 h-6" />
          </button>
        </div>

        {/* Mode Selection */}
        <div className="flex space-x-1 mb-6 bg-gray-100 p-1 rounded-lg">
          <button
            onClick={() => setMode('select')}
            className={`flex-1 flex items-center justify-center space-x-2 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
              mode === 'select'
                ? 'bg-white text-gray-900 shadow-sm'
                : 'text-gray-500 hover:text-gray-700'
            }`}
          >
            <UsersIcon className="w-4 h-4" />
            <span>Select Existing Users</span>
          </button>
          <button
            onClick={() => setMode('create')}
            className={`flex-1 flex items-center justify-center space-x-2 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
              mode === 'create'
                ? 'bg-white text-gray-900 shadow-sm'
                : 'text-gray-500 hover:text-gray-700'
            }`}
          >
            <PlusIcon className="w-4 h-4" />
            <span>Create New User</span>
          </button>
        </div>

        {/* Error Display */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-md p-3 mb-4">
            <p className="text-red-600 text-sm">{error}</p>
          </div>
        )}

        {/* Content based on mode */}
        {mode === 'select' ? (
          <>
            {/* User Selection */}
            <div className="space-y-3 max-h-64 overflow-y-auto mb-6">
              {availableUsers.length === 0 ? (
                <div className="text-center py-8">
                  <UsersIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">No available users found</p>
                  <p className="text-sm text-gray-400 mt-1">
                    All users are already members of teams or you can create a new user
                  </p>
                </div>
              ) : (
                availableUsers.map((user) => (
                  <label
                    key={user.id}
                    className="flex items-center space-x-3 p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer"
                  >
                    <input
                      type="checkbox"
                      checked={selectedUsers.includes(user.id)}
                      onChange={() => toggleUserSelection(user.id)}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <ContactAvatar
                      firstName={user.firstName}
                      lastName={user.lastName}
                      avatarUrl={user.avatarUrl}
                      size="sm"
                    />
                    <div>
                      <p className="font-medium text-gray-900">
                        {user.firstName} {user.lastName}
                      </p>
                      <p className="text-sm text-gray-500">{user.email}</p>
                    </div>
                  </label>
                ))
              )}
            </div>

            {/* Actions for Select Mode */}
            <div className="flex space-x-3">
              <button
                type="button"
                onClick={onClose}
                className="flex-1 px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={handleAddExistingMembers}
                disabled={isLoading || selectedUsers.length === 0}
                className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2"
              >
                {isLoading ? (
                  <LoadingSpinner size="sm" />
                ) : (
                  <UserPlusIcon className="w-4 h-4" />
                )}
                <span>
                  {isLoading 
                    ? 'Adding...' 
                    : `Add ${selectedUsers.length} Member${selectedUsers.length !== 1 ? 's' : ''}`
                  }
                </span>
              </button>
            </div>
          </>
        ) : (
          <>
            {/* Create User Form */}
            <div className="space-y-4 mb-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label htmlFor="firstName" className="block text-sm font-medium text-gray-700 mb-1">
                    First Name *
                  </label>
                  <input
                    type="text"
                    id="firstName"
                    name="firstName"
                    value={createUserForm.firstName}
                    onChange={handleCreateUserFormChange}
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Enter first name"
                  />
                </div>

                <div>
                  <label htmlFor="lastName" className="block text-sm font-medium text-gray-700 mb-1">
                    Last Name *
                  </label>
                  <input
                    type="text"
                    id="lastName"
                    name="lastName"
                    value={createUserForm.lastName}
                    onChange={handleCreateUserFormChange}
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Enter last name"
                  />
                </div>
              </div>

              <div>
                <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                  Email Address *
                </label>
                <input
                  type="email"
                  id="email"
                  name="email"
                  value={createUserForm.email}
                  onChange={handleCreateUserFormChange}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter email address"
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label htmlFor="role" className="block text-sm font-medium text-gray-700 mb-1">
                    Role
                  </label>
                  <select
                    id="role"
                    name="role"
                    value={createUserForm.role}
                    onChange={handleCreateUserFormChange}
                    disabled={isLoadingRoles}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-50"
                  >
                    {isLoadingRoles ? (
                      <option value="">Loading roles...</option>
                    ) : roles.length === 0 ? (
                      <option value="">No roles available</option>
                    ) : (
                      <>
                        <option value="">Select a role</option>
                        {roles.map((role) => (
                          <option key={role.id} value={role.name}>
                            {role.name}
                            {role.description && ` - ${role.description}`}
                          </option>
                        ))}
                      </>
                    )}
                  </select>
                </div>

                <div>
                  <label htmlFor="department" className="block text-sm font-medium text-gray-700 mb-1">
                    Department
                  </label>
                  <input
                    type="text"
                    id="department"
                    name="department"
                    value={createUserForm.department}
                    onChange={handleCreateUserFormChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Enter department"
                  />
                </div>
              </div>

              <div>
                <label htmlFor="jobTitle" className="block text-sm font-medium text-gray-700 mb-1">
                  Job Title
                </label>
                <input
                  type="text"
                  id="jobTitle"
                  name="jobTitle"
                  value={createUserForm.jobTitle}
                  onChange={handleCreateUserFormChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter job title"
                />
              </div>

              <div className="bg-blue-50 border border-blue-200 rounded-md p-3">
                <p className="text-sm text-blue-600">
                  <strong>Note:</strong> The user will be invited via email and automatically added to this team. 
                  They will need to set their password when they first log in.
                </p>
              </div>
            </div>

            {/* Actions for Create Mode */}
            <div className="flex space-x-3">
              <button
                type="button"
                onClick={onClose}
                className="flex-1 px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={handleCreateAndAddUser}
                disabled={isLoading || !createUserForm.email || !createUserForm.firstName || !createUserForm.lastName || !createUserForm.role}
                className="flex-1 px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2"
              >
                {isLoading ? (
                  <LoadingSpinner size="sm" />
                ) : (
                  <PlusIcon className="w-4 h-4" />
                )}
                <span>{isLoading ? 'Creating...' : 'Create & Add User'}</span>
              </button>
            </div>
          </>
        )}
      </div>
    </div>
  );
}
