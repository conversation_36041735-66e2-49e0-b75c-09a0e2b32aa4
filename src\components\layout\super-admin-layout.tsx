'use client';

import { SuperAdminSidebar } from "@/components/super-admin-sidebar"
import {
  SidebarInset,
  SidebarProvider,
} from "@/components/ui/sidebar"
import { SimplePageWrapper } from "@/components/layout/simple-page-wrapper"
import { useLocale } from "@/components/providers/locale-provider"

interface SuperAdminLayoutProps {
  children: React.ReactNode;
}

export default function SuperAdminLayout({ children }: SuperAdminLayoutProps) {
  // Get locale context for RTL support
  const { sidebarSide, isRTL } = useLocale()

  return (
    <SidebarProvider
      style={
        {
          "--sidebar-width": "calc(var(--spacing) * 72)",
          "--header-height": "calc(var(--spacing) * 12)",
        } as React.CSSProperties
      }
    >
      <SuperAdminSidebar variant="inset" side={sidebarSide} />
      <SidebarInset className={isRTL ? "rtl:pr-[--sidebar-width]" : ""}>
        <SimplePageWrapper>
          {children}
        </SimplePageWrapper>
      </SidebarInset>
    </SidebarProvider>
  );
}
