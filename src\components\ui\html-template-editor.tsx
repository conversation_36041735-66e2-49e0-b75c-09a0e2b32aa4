'use client';

import React, { useState, useCallback, useRef, useEffect } from 'react';
import Editor from '@monaco-editor/react';

import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import {
  Eye,
  Code2,
  FileText,
  Braces,
  Download,
  Upload,
  Settings,
  Copy,
  Check,
  AlertTriangle
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { toast } from 'sonner';

interface HTMLTemplateEditorProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
  minHeight?: string;
  maxHeight?: string;
  disabled?: boolean;
  onPlaceholderInsert?: (placeholder: string) => void;
}

export function HTMLTemplateEditor({
  value,
  onChange,
  placeholder = 'Enter your HTML template content...',
  className,
  minHeight = '400px',
  maxHeight = '800px',
  disabled = false,
  onPlaceholderInsert
}: HTMLTemplateEditorProps) {
  const [viewMode, setViewMode] = useState<'code' | 'preview'>('code');
  const [showPlaceholders, setShowPlaceholders] = useState(true);
  const [copied, setCopied] = useState(false);
  const editorRef = useRef<any>(null);

  // Common Arabic proposal placeholders
  const commonPlaceholders = [
    'Client_Name',
    'Client_Name_Arabic',
    'Project_Name',
    'Project_Name_Arabic',
    'Company_Name',
    'Company_Name_Arabic',
    'Competition_Number',
    'Approver_Name',
    'Approver_Title',
    'Project_Duration_Months',
    'Submission_Date_Hijri',
    'Submission_Date_Gregorian',
    'Company_Logo',
    'Contact_Table',
    'Team_CVs',
    'Project_Timeline',
    'Technical_Specifications',
    'Financial_Details'
  ];

  // Handle content changes
  const handleContentChange = useCallback((content: string) => {
    onChange(content);
  }, [onChange]);

  // Insert placeholder at cursor position
  const insertPlaceholder = useCallback((placeholder: string) => {
    if (editorRef.current && !disabled) {
      const editor = editorRef.current;
      const selection = editor.getSelection();
      const placeholderText = `{{${placeholder}}}`;

      editor.executeEdits('insert-placeholder', [{
        range: selection,
        text: placeholderText
      }]);

      // Focus the editor
      editor.focus();

      onPlaceholderInsert?.(placeholder);
      toast.success(`Inserted placeholder: {{${placeholder}}}`);
    }
  }, [disabled, onPlaceholderInsert]);

  // Copy to clipboard
  const copyToClipboard = useCallback(async () => {
    try {
      await navigator.clipboard.writeText(value);
      setCopied(true);
      toast.success('HTML copied to clipboard');
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      toast.error('Failed to copy to clipboard');
    }
  }, [value]);

  // Export HTML content
  const exportHTML = useCallback(() => {
    const blob = new Blob([value], { type: 'text/html' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'template.html';
    a.click();
    URL.revokeObjectURL(url);
    toast.success('HTML template exported');
  }, [value]);

  // Import HTML content
  const importHTML = useCallback(() => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.html,.htm';
    input.onchange = (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (file) {
        const reader = new FileReader();
        reader.onload = (e) => {
          const content = e.target?.result as string;
          onChange(content);
          toast.success('HTML template imported');
        };
        reader.readAsText(file);
      }
    };
    input.click();
  }, [onChange]);

  // Format HTML using Monaco's built-in formatter
  const formatHTML = useCallback(() => {
    if (editorRef.current) {
      editorRef.current.getAction('editor.action.formatDocument')?.run();
      toast.success('HTML formatted');
    }
  }, []);

  // Handle editor mount
  const handleEditorDidMount = useCallback((editor: any, monaco: any) => {
    editorRef.current = editor;

    // Configure HTML language features
    monaco.languages.html.htmlDefaults.setOptions({
      format: {
        tabSize: 2,
        insertSpaces: true,
        wrapLineLength: 120,
        unformatted: 'default"',
        contentUnformatted: 'pre,code,textarea',
        indentInnerHtml: false,
        preserveNewLines: true,
        maxPreserveNewLines: undefined,
        indentHandlebars: false,
        endWithNewline: false,
        extraLiners: 'head, body, /html',
        wrapAttributes: 'auto'
      }
    });
  }, []);

  return (
    <div className={cn('border rounded-lg overflow-hidden bg-white', className)}>
      {/* Toolbar */}
      <div className="border-b bg-gray-50 p-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            {/* View Mode Toggle */}
            <div className="flex items-center border rounded-md">
              <Button
                variant={viewMode === 'code' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setViewMode('code')}
                className="rounded-r-none"
              >
                <Code2 className="w-4 h-4 mr-1" />
                HTML Editor
              </Button>
              <Button
                variant={viewMode === 'preview' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setViewMode('preview')}
                className="rounded-l-none"
              >
                <Eye className="w-4 h-4 mr-1" />
                Preview
              </Button>
            </div>

            <Separator orientation="vertical" className="h-6" />

            {/* Placeholder Toggle */}
            <Button
              variant={showPlaceholders ? 'default' : 'outline'}
              size="sm"
              onClick={() => setShowPlaceholders(!showPlaceholders)}
            >
              <Braces className="w-4 h-4 mr-1" />
              Placeholders
            </Button>
          </div>

          <div className="flex items-center space-x-2">
            {/* Format HTML */}
            <Button variant="outline" size="sm" onClick={formatHTML}>
              <Settings className="w-4 h-4 mr-1" />
              Format
            </Button>

            {/* Copy to Clipboard */}
            <Button variant="outline" size="sm" onClick={copyToClipboard}>
              {copied ? (
                <Check className="w-4 h-4 mr-1 text-green-600" />
              ) : (
                <Copy className="w-4 h-4 mr-1" />
              )}
              {copied ? 'Copied' : 'Copy'}
            </Button>

            {/* Export/Import */}
            <Button variant="outline" size="sm" onClick={exportHTML}>
              <Download className="w-4 h-4 mr-1" />
              Export
            </Button>
            <Button variant="outline" size="sm" onClick={importHTML}>
              <Upload className="w-4 h-4 mr-1" />
              Import
            </Button>
          </div>
        </div>

        {/* Important Notice */}
        <div className="mt-3 pt-3 border-t bg-blue-50 border border-blue-200 rounded p-3">
          <div className="flex items-start space-x-2">
            <AlertTriangle className="w-4 h-4 text-blue-600 mt-0.5 flex-shrink-0" />
            <div className="text-sm text-blue-800">
              <p className="font-medium mb-1">HTML Template Editor</p>
              <p>This editor preserves all HTML styling, classes, and attributes from AI-generated templates. Your custom styles, Tailwind CSS classes, and inline styles will be maintained exactly as written.</p>
            </div>
          </div>
        </div>

        {/* Placeholders Panel */}
        {showPlaceholders && (
          <div className="mt-3 pt-3 border-t">
            <div className="flex items-center mb-2">
              <span className="text-sm font-medium text-gray-700">Available Placeholders:</span>
              <span className="ml-2 text-xs text-gray-500">Click to insert at cursor position</span>
            </div>
            <div className="flex flex-wrap gap-2">
              {commonPlaceholders.map((placeholder) => (
                <Badge
                  key={placeholder}
                  variant="outline"
                  className="cursor-pointer hover:bg-blue-50 hover:border-blue-300 transition-colors"
                  onClick={() => insertPlaceholder(placeholder)}
                >
                  {`{{${placeholder}}}`}
                </Badge>
              ))}
            </div>
            <div className="mt-2 text-xs text-gray-500">
              <AlertTriangle className="w-3 h-3 inline mr-1" />
              These placeholders will be replaced with actual data when generating documents
            </div>
          </div>
        )}
      </div>

      {/* Editor Content */}
      <div className="relative">
        {viewMode === 'code' && (
          <div style={{ minHeight, maxHeight }} className="overflow-hidden">
            <Editor
              height={minHeight}
              defaultLanguage="html"
              value={value}
              onChange={(newValue) => handleContentChange(newValue || '')}
              onMount={handleEditorDidMount}
              options={{
                readOnly: disabled,
                minimap: { enabled: false },
                scrollBeyondLastLine: false,
                fontSize: 14,
                lineNumbers: 'on',
                wordWrap: 'on',
                automaticLayout: true,
                formatOnPaste: true,
                formatOnType: true,
                tabSize: 2,
                insertSpaces: true,
                folding: true,
                bracketMatching: 'always',
                autoIndent: 'full',
                colorDecorators: true,
                renderWhitespace: 'selection',
                suggest: {
                  showKeywords: true,
                  showSnippets: true,
                  showClasses: true,
                  showFunctions: true,
                  showVariables: true
                }
              }}
              theme="vs"
            />
          </div>
        )}

        {viewMode === 'preview' && (
          <div
            style={{ minHeight, maxHeight }}
            className="overflow-auto p-4 bg-white"
          >
            {value ? (
              <div
                className="max-w-none"
                dangerouslySetInnerHTML={{ __html: value }}
              />
            ) : (
              <div className="flex items-center justify-center h-full text-gray-500 italic">
                <div className="text-center">
                  <FileText className="w-12 h-12 mx-auto mb-2 opacity-50" />
                  <p>No content to preview</p>
                  <p className="text-sm">Switch to HTML Editor to create your template</p>
                </div>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Status Bar */}
      <div className="border-t bg-gray-50 px-3 py-2 text-xs text-gray-600 flex justify-between">
        <span>
          {value.length} characters | {value.split(/\s+/).filter(word => word.length > 0).length} words | HTML preserved
        </span>
        <span>
          Mode: {viewMode === 'code' ? 'HTML Editor (Monaco)' : 'Preview'} | {disabled ? 'Read-only' : 'Editable'}
        </span>
      </div>
    </div>
  );
}
