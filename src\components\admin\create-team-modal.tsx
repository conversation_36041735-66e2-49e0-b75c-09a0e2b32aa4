'use client';

import React, { useState, useEffect } from 'react';
import { TeamWithDetails, User } from '@/types';
import { ContactAvatar } from '@/components/ui/contact-avatar';
import {
  XMarkIcon,
  UserGroupIcon,
  ColorSwatchIcon
} from '@heroicons/react/24/outline';

interface CreateTeamModalProps {
  onSubmit: (data: {
    name: string;
    description?: string;
    color?: string;
    managerId?: string;
    parentTeamId?: string;
  }) => void;
  onCancel: () => void;
  existingTeams: TeamWithDetails[];
}

export function CreateTeamModal({ onSubmit, onCancel, existingTeams }: CreateTeamModalProps) {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    color: '#6B7280',
    managerId: '',
    parentTeamId: ''
  });
  const [availableManagers, setAvailableManagers] = useState<User[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const colorOptions = [
    '#6B7280', // Gray
    '#EF4444', // Red
    '#F97316', // Orange
    '#EAB308', // Yellow
    '#22C55E', // Green
    '#06B6D4', // Cyan
    '#3B82F6', // Blue
    '#8B5CF6', // Purple
    '#EC4899', // Pink
  ];

  useEffect(() => {
    fetchAvailableManagers();
  }, []);

  const fetchAvailableManagers = async () => {
    try {
      const response = await fetch('/api/users?role=manager');
      if (response.ok) {
        const data = await response.json();
        setAvailableManagers(data.data || []);
      }
    } catch (err) {
      console.error('Failed to fetch managers:', err);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);

    if (!formData.name.trim()) {
      setError('Team name is required');
      return;
    }

    // Check for duplicate team names
    const isDuplicate = existingTeams.some(
      team => team.name.toLowerCase() === formData.name.toLowerCase()
    );

    if (isDuplicate) {
      setError('A team with this name already exists');
      return;
    }

    setIsLoading(true);

    try {
      await onSubmit({
        name: formData.name.trim(),
        description: formData.description.trim() || undefined,
        color: formData.color,
        managerId: formData.managerId || undefined,
        parentTeamId: formData.parentTeamId || undefined
      });
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create team');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg w-full max-w-md p-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-semibold text-gray-900">Create New Team</h3>
          <button
            onClick={onCancel}
            className="text-gray-400 hover:text-gray-600 p-1"
          >
            <XMarkIcon className="w-6 h-6" />
          </button>
        </div>

        {/* Error Display */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-md p-3 mb-4">
            <p className="text-red-600 text-sm">{error}</p>
          </div>
        )}

        {/* Form */}
        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Team Name */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Team Name *
            </label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
              placeholder="Enter team name"
              required
            />
          </div>

          {/* Description */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Description
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
              placeholder="Enter team description"
            />
          </div>

          {/* Color */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Team Color
            </label>
            <div className="flex space-x-2">
              {colorOptions.map((color) => (
                <button
                  key={color}
                  type="button"
                  onClick={() => setFormData({ ...formData, color })}
                  className={`w-8 h-8 rounded-full border-2 ${
                    formData.color === color ? 'border-gray-900' : 'border-gray-300'
                  }`}
                  style={{ backgroundColor: color }}
                />
              ))}
            </div>
          </div>

          {/* Team Manager */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Team Manager
            </label>
            <select
              value={formData.managerId}
              onChange={(e) => setFormData({ ...formData, managerId: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">Select a manager</option>
              {availableManagers.map((manager) => (
                <option key={manager.id} value={manager.id}>
                  {manager.firstName} {manager.lastName} ({manager.email})
                </option>
              ))}
            </select>
          </div>

          {/* Parent Team */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Parent Team
            </label>
            <select
              value={formData.parentTeamId}
              onChange={(e) => setFormData({ ...formData, parentTeamId: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">No parent team</option>
              {existingTeams.map((team) => (
                <option key={team.id} value={team.id}>
                  {team.name}
                </option>
              ))}
            </select>
          </div>

          {/* Actions */}
          <div className="flex space-x-3 pt-4">
            <button
              type="button"
              onClick={onCancel}
              className="flex-1 px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isLoading}
              className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? 'Creating...' : 'Create Team'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
