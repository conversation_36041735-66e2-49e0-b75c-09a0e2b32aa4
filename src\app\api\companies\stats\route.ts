import { NextResponse } from 'next/server';
import { CompanyManagementService } from '@/services/company-management';
import { withPermission, AuthenticatedRequest } from '@/lib/permission-middleware';
import { PermissionResource, PermissionAction } from '@/types';

const companyService = new CompanyManagementService();

/**
 * GET /api/companies/stats
 * Get company statistics for the tenant
 */
export const GET = withPermission({
  resource: PermissionResource.COMPANIES,
  action: PermissionAction.READ,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest) => {
  try {
    const stats = await companyService.getCompanyStats(req.tenantId);

    return NextResponse.json({
      success: true,
      data: { stats },
      message: 'Company statistics retrieved successfully'
    });

  } catch (error) {
    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
});
