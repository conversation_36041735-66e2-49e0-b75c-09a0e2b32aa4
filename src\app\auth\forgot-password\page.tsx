'use client';

import { AuthLayout } from '@/components/auth/auth-layout';
import { ForgotPasswordForm } from '@/components/auth/forgot-password-form';
import { useRedirectIfAuthenticated } from '@/hooks/use-auth';

export default function ForgotPasswordPage() {
  // Redirect if already authenticated
  useRedirectIfAuthenticated();

  return (
    <AuthLayout
      title="Forgot your password?"
      subtitle="Enter your email below to reset your password"
    >
      <ForgotPasswordForm />
    </AuthLayout>
  );
}
