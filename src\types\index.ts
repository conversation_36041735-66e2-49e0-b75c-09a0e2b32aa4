import { User, Tenant, Contact, Company, Lead, Opportunity, Proposal, Project, ScheduledMeeting } from '@prisma/client';

// Re-export Prisma types
export type { User, Tenant, Contact, Company, Lead, Opportunity, Proposal, Project, ScheduledMeeting, Team, TenantUser };

// Extended User type with tenant information
export interface UserWithTenants extends User {
  tenants: TenantInfo[];
}

export interface TenantInfo {
  id: string;
  name: string;
  slug: string;
  role: string;
  isTenantAdmin: boolean;
}

// Tenant context interface
export interface TenantContext {
  id: string;
  name: string;
  slug: string;
  subscription: {
    planId: string;
    status: string;
    features: string[];
    limits: Record<string, number>;
  };
  settings: Record<string, any>;
  branding: {
    logo?: string;
    primaryColor?: string;
    secondaryColor?: string;
  };
}

// Super Admin specific types
export interface SuperAdminUser extends User {
  isPlatformAdmin: true;
  tenants: never; // Super Admin doesn't belong to specific tenants
}

export interface PlatformMetrics {
  totalTenants: number;
  activeTenants: number;
  totalUsers: number;
  activeUsers: number;
  totalRevenue: number;
  monthlyGrowth: number;
}

export interface TenantSummary {
  id: string;
  name: string;
  slug: string;
  status: string;
  userCount: number;
  createdAt: Date;
  subscription?: {
    planName: string;
    status: string;
  };
}

export interface UserSummary {
  id: string;
  email: string;
  firstName: string | null;
  lastName: string | null;
  status: string;
  isPlatformAdmin: boolean;
  tenantCount: number;
  lastLogin: Date | null;
  createdAt: Date;
}

// Permission types
export enum PermissionAction {
  CREATE = 'CREATE',
  READ = 'READ',
  UPDATE = 'UPDATE',
  DELETE = 'DELETE',
  EXPORT = 'EXPORT',
  IMPORT = 'IMPORT',
  MANAGE = 'MANAGE',
  ASSIGN = 'ASSIGN',
  MANAGE_ROLES = 'MANAGE_ROLES',
  MANAGE_SYSTEM_ROLES = 'MANAGE_SYSTEM_ROLES'
}

export enum PermissionResource {
  CONTACTS = 'contacts',
  COMPANIES = 'companies',
  LEADS = 'leads',
  OPPORTUNITIES = 'opportunities',
  PROPOSALS = 'proposals',
  PROJECTS = 'projects',
  HANDOVERS = 'handovers',
  COMMUNICATION = 'communication',
  USERS = 'users',
  ROLES = 'roles',
  PERMISSIONS = 'permissions',
  TENANTS = 'tenants',
  SETTINGS = 'settings',
  AI_FEATURES = 'ai_features',
  REPORTS = 'reports',
  INTEGRATIONS = 'integrations',
  MEETINGS = 'meetings',
  DOCUMENTS = 'documents',
  DASHBOARD = 'dashboard'
}

export enum PermissionCategory {
  RESOURCE = 'resource',
  FEATURE = 'feature',
  ADMINISTRATIVE = 'administrative'
}

export interface Permission {
  id: string;
  name: string;
  resource: PermissionResource;
  action: PermissionAction;
  category: PermissionCategory;
  description: string;
}

export interface Role {
  id: string;
  name: string;
  description: string;
  isSystemRole: boolean;
  parentRoleId?: string;
  permissions: Permission[];
  childRoles?: Role[];
  userCount?: number;
}

export interface UserWithPermissions extends User {
  roles: Role[];
  directPermissions: Permission[];
  effectivePermissions: Permission[];
  locale: string;
}

// Team Management types
export interface TeamMember {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  avatarUrl?: string;
  role: string;
  department?: string;
  jobTitle?: string;
  joinedAt: Date;
  lastLogin?: Date;
  status: string;
  roles?: Role[];
}

export interface TeamWithDetails extends Team {
  memberCount: number;
  manager?: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
  };
  parentTeam?: {
    id: string;
    name: string;
  };
  members?: TeamMember[];
  childTeams?: TeamWithDetails[];
  _count?: {
    members: number;
  };
}

export interface TeamWithMembers extends TeamWithDetails {
  members: Array<{
    id: string;
    firstName: string;
    lastName: string;
    email: string;
    avatarUrl?: string;
    role?: string;
    joinedAt: Date;
  }>;
  createdAt: Date;
  updatedAt: Date;
}

export interface BulkOperationResult {
  successful: string[];
  failed: Array<{
    id: string;
    error: string;
  }>;
}

export interface TeamAnalytics {
  totalMembers: number;
  activeMembers: number;
  membersByRole: Record<string, number>;
  membersByDepartment: Record<string, number>;
  recentActivity: Array<{
    type: string;
    user: string;
    timestamp: Date;
    description: string;
  }>;
}

// AI Configuration types
export interface AIConfig {
  provider: 'google' | 'openai' | 'anthropic';
  fallbackProvider?: 'google' | 'openai' | 'anthropic';
  models: {
    [key: string]: string; // task -> model mapping
  };
  rateLimits: {
    [key: string]: {
      requests: number;
      window: string;
      cost: number;
    };
  };
  temperature: number;
  maxTokens: number;
  enableFallback: boolean;
  healthCheckInterval: number;
}

// Subscription types
export interface SubscriptionPlan {
  id: string;
  name: string;
  priceMonthly: number;
  priceYearly: number;
  features: {
    contacts: number | 'unlimited';
    leads: number | 'unlimited';
    users: number | 'unlimited';
    aiRequests: number | 'unlimited';
    storage: number; // in MB
    customIntegrations: boolean;
    prioritySupport: boolean;
    customBranding: boolean;
  };
  limits: {
    [key: string]: number;
  };
}

// Extended CRM types
export interface ContactWithRelations extends Contact {
  company?: Company;
  owner?: User;
  createdBy?: User;
}

export interface LeadWithRelations extends Lead {
  contact?: Contact;
  company?: Company;
  owner?: User;
  createdBy?: User;
}

export interface OpportunityWithRelations extends Opportunity {
  lead?: Lead;
  contact?: Contact;
  company?: Company;
  owner?: User;
  createdBy?: User;
}

// API Response types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Form types
export interface ContactFormData {
  firstName: string;
  lastName: string;
  email?: string;
  phone?: string;
  companyId?: string;
}

export interface CompanyFormData {
  name: string;
  website?: string;
  industry?: string;
  size?: string;
}

export interface LeadFormData {
  title: string;
  contactId?: string;
  companyId?: string;
  source?: string;
  status: string;
}

// Locale types
export type Locale = 'en' | 'ar';

export interface LocaleConfig {
  locale: Locale;
  direction: 'ltr' | 'rtl';
  name: string;
  flag: string;
}
