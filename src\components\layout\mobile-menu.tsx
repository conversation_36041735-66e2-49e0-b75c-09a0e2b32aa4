'use client';

import { Fragment } from 'react';
import { Dialog, Transition } from '@headlessui/react';
import { XMarkIcon, SparklesIcon } from '@heroicons/react/24/outline';
import { useTranslation } from '@/components/providers/locale-provider';
import { usePathname } from 'next/navigation';
import { SmoothLink } from '@/components/ui/smooth-link';
import {
  HomeIcon,
  UsersIcon,
  BuildingOfficeIcon,
  UserGroupIcon,
  ChartBarIcon,
  DocumentTextIcon,
  FolderIcon,
  ClockIcon,
  Cog6ToothIcon,
  ShieldCheckIcon,
  CurrencyDollarIcon,
  BoltIcon
} from '@heroicons/react/24/outline';
import { cn } from '@/lib/utils';
import { ThemeToggle } from '@/components/ui/theme-toggle';
import { useLocale } from '@/components/providers/locale-provider';

const navigationItems = [
  {
    name: 'dashboard',
    href: '/dashboard',
    icon: HomeIcon,
  },
  {
    name: 'contacts',
    href: '/contacts',
    icon: UsersIcon,
  },
  {
    name: 'companies',
    href: '/companies',
    icon: BuildingOfficeIcon,
  },
  {
    name: 'leads',
    href: '/leads',
    icon: UserGroupIcon,
  },
  {
    name: 'opportunities',
    href: '/opportunities',
    icon: ChartBarIcon,
  },
  {
    name: 'proposals',
    href: '/proposals',
    icon: DocumentTextIcon,
  },
  {
    name: 'projects',
    href: '/projects',
    icon: FolderIcon,
  },
  {
    name: 'activities',
    href: '/activities',
    icon: ClockIcon,
  },
];

const adminItems = [
  {
    name: 'users',
    href: '/admin/users',
    icon: UsersIcon,
  },
  {
    name: 'roles',
    href: '/admin/roles',
    icon: ShieldCheckIcon,
  },
  {
    name: 'automation',
    href: '/admin/automation',
    icon: BoltIcon,
  },
  {
    name: 'billing',
    href: '/admin/billing',
    icon: CurrencyDollarIcon,
  },
  {
    name: 'settings',
    href: '/admin/settings',
    icon: Cog6ToothIcon,
  },
];

interface MobileMenuProps {
  isOpen: boolean;
  onClose: () => void;
}

export function MobileMenu({ isOpen, onClose }: MobileMenuProps) {
  const { t } = useTranslation();
  const { isRTL } = useLocale();
  const pathname = usePathname();

  return (
    <Transition.Root show={isOpen} as={Fragment}>
      <Dialog as="div" className="relative z-50 lg:hidden" onClose={onClose}>
        <Transition.Child
          as={Fragment}
          enter="transition-opacity ease-linear duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="transition-opacity ease-linear duration-300"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-gray-900/80" />
        </Transition.Child>

        <div className="fixed inset-0 flex">
          <Transition.Child
            as={Fragment}
            enter="transition ease-in-out duration-300 transform"
            enterFrom="-translate-x-full"
            enterTo="translate-x-0"
            leave="transition ease-in-out duration-300 transform"
            leaveFrom="translate-x-0"
            leaveTo="-translate-x-full"
          >
            <Dialog.Panel className="relative mr-16 flex w-full max-w-xs flex-1">
              <Transition.Child
                as={Fragment}
                enter="ease-in-out duration-300"
                enterFrom="opacity-0"
                enterTo="opacity-100"
                leave="ease-in-out duration-300"
                leaveFrom="opacity-100"
                leaveTo="opacity-0"
              >
                <div className="absolute left-full top-0 flex w-16 justify-center pt-5">
                  <button type="button" className="-m-2.5 p-2.5" onClick={onClose}>
                    <span className="sr-only">Close sidebar</span>
                    <XMarkIcon className="h-6 w-6 text-white" aria-hidden="true" />
                  </button>
                </div>
              </Transition.Child>

              <div className="flex grow flex-col gap-y-5 overflow-y-auto bg-white dark:bg-gray-800 px-6 pb-4">
                {/* Logo */}
                <div className="flex h-16 shrink-0 items-center">
                  <div className={cn(
                    "flex items-center",
                    isRTL ? "space-x-reverse space-x-3" : "space-x-3"
                  )}>
                    <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-primary-600">
                      <SparklesIcon className="h-5 w-5 text-white" />
                    </div>
                    <div>
                      <h1 className="text-lg font-bold text-gray-900 dark:text-white">
                        CRM Platform
                      </h1>
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        Multi-Tenant SaaS
                      </p>
                    </div>
                  </div>
                </div>

                {/* Navigation */}
                <nav className="flex flex-1 flex-col">
                  <ul role="list" className="flex flex-1 flex-col gap-y-7">
                    {/* Main Navigation */}
                    <li>
                      <ul role="list" className="-mx-2 space-y-1">
                        {navigationItems.map((item) => {
                          const isActive = pathname.includes(item.href);
                          return (
                            <li key={item.name}>
                              <SmoothLink
                                href={item.href}
                                onClick={onClose}
                                className={cn(
                                  isActive
                                    ? 'bg-primary-50 text-primary-700 dark:bg-primary-900/20 dark:text-primary-300'
                                    : 'text-gray-700 hover:text-primary-700 hover:bg-gray-50 dark:text-gray-300 dark:hover:text-primary-300 dark:hover:bg-gray-700',
                                  'group flex rounded-md p-2 text-sm leading-6 font-medium transition-colors',
                                  isRTL ? "gap-x-reverse gap-x-3" : "gap-x-3"
                                )}
                              >
                                <item.icon
                                  className={cn(
                                    isActive
                                      ? 'text-primary-700 dark:text-primary-300'
                                      : 'text-gray-400 group-hover:text-primary-700 dark:group-hover:text-primary-300',
                                    'h-6 w-6 shrink-0 transition-colors'
                                  )}
                                  aria-hidden="true"
                                />
                                {t(item.name)}
                              </SmoothLink>
                            </li>
                          );
                        })}
                      </ul>
                    </li>

                    {/* Admin Section */}
                    <li>
                      <div className="text-xs font-semibold leading-6 text-gray-400 dark:text-gray-500 uppercase tracking-wide">
                        {t('navigation.admin')}
                      </div>
                      <ul role="list" className="-mx-2 mt-2 space-y-1">
                        {adminItems.map((item) => {
                          const isActive = pathname.includes(item.href);
                          return (
                            <li key={item.name}>
                              <SmoothLink
                                href={item.href}
                                onClick={onClose}
                                className={cn(
                                  isActive
                                    ? 'bg-primary-50 text-primary-700 dark:bg-primary-900/20 dark:text-primary-300'
                                    : 'text-gray-700 hover:text-primary-700 hover:bg-gray-50 dark:text-gray-300 dark:hover:text-primary-300 dark:hover:bg-gray-700',
                                  'group flex rounded-md p-2 text-sm leading-6 font-medium transition-colors',
                                  isRTL ? "gap-x-reverse gap-x-3" : "gap-x-3"
                                )}
                              >
                                <item.icon
                                  className={cn(
                                    isActive
                                      ? 'text-primary-700 dark:text-primary-300'
                                      : 'text-gray-400 group-hover:text-primary-700 dark:group-hover:text-primary-300',
                                    'h-6 w-6 shrink-0 transition-colors'
                                  )}
                                  aria-hidden="true"
                                />
                                {t(item.name)}
                              </SmoothLink>
                            </li>
                          );
                        })}
                      </ul>
                    </li>

                    {/* Theme Toggle */}
                    <li className="mt-auto pt-4 border-t border-gray-200 dark:border-gray-700">
                      <div className="px-2">
                        <ThemeToggle variant="dropdown" showLabel />
                      </div>
                    </li>
                  </ul>
                </nav>
              </div>
            </Dialog.Panel>
          </Transition.Child>
        </div>
      </Dialog>
    </Transition.Root>
  );
}
