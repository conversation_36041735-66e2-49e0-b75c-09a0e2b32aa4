'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Activity, 
  Database, 
  TrendingUp, 
  Clock, 
  AlertTriangle, 
  CheckCircle,
  RefreshCw,
  BarChart3
} from 'lucide-react';

interface DatabaseHealth {
  status: 'healthy' | 'unhealthy' | 'error';
  responseTime: number;
  activeConnections?: number;
  databaseSize?: string;
  timestamp: Date;
  error?: string;
}

interface PerformanceSummary {
  totalQueries: number;
  slowQueries: number;
  averageResponseTime: number;
  slowQueryRate: number;
}

interface QueryStats {
  [queryName: string]: {
    avg: number;
    count: number;
    max: number;
  };
}

export default function DatabasePerformanceDashboard() {
  const [health, setHealth] = useState<DatabaseHealth | null>(null);
  const [performance, setPerformance] = useState<PerformanceSummary | null>(null);
  const [queryStats, setQueryStats] = useState<QueryStats | null>(null);
  const [recommendations, setRecommendations] = useState<string[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  const fetchDatabaseHealth = async () => {
    try {
      const response = await fetch('/api/admin/database/health');
      const data = await response.json();
      
      if (data.health) {
        setHealth(data.health);
      }
      if (data.performance) {
        setPerformance(data.performance);
      }
      if (data.recommendations) {
        setRecommendations(data.recommendations);
      }
    } catch (error) {
      console.error('Failed to fetch database health:', error);
    }
  };

  const fetchPerformanceStats = async () => {
    try {
      const response = await fetch('/api/admin/database/performance?format=detailed');
      const data = await response.json();
      
      if (data.queries) {
        setQueryStats(data.queries);
      }
    } catch (error) {
      console.error('Failed to fetch performance stats:', error);
    }
  };

  const refreshData = async () => {
    setRefreshing(true);
    await Promise.all([fetchDatabaseHealth(), fetchPerformanceStats()]);
    setRefreshing(false);
  };

  const clearMetrics = async () => {
    try {
      await fetch('/api/admin/database/performance', { method: 'DELETE' });
      await refreshData();
    } catch (error) {
      console.error('Failed to clear metrics:', error);
    }
  };

  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      await Promise.all([fetchDatabaseHealth(), fetchPerformanceStats()]);
      setLoading(false);
    };

    loadData();

    // Auto-refresh every 30 seconds
    const interval = setInterval(refreshData, 30000);
    return () => clearInterval(interval);
  }, []);

  const getHealthStatusColor = (status: string) => {
    switch (status) {
      case 'healthy': return 'text-green-600';
      case 'unhealthy': return 'text-yellow-600';
      case 'error': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  const getHealthStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy': return <CheckCircle className="h-5 w-5 text-green-600" />;
      case 'unhealthy': return <AlertTriangle className="h-5 w-5 text-yellow-600" />;
      case 'error': return <AlertTriangle className="h-5 w-5 text-red-600" />;
      default: return <Activity className="h-5 w-5 text-gray-600" />;
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <RefreshCw className="h-8 w-8 animate-spin" />
        <span className="ml-2">Loading database performance data...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Action Buttons */}
      <div className="flex justify-end gap-2">
        <Button
          variant="outline"
          onClick={refreshData}
          disabled={refreshing}
        >
          <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
        <Button
          variant="outline"
          onClick={clearMetrics}
        >
          Clear Metrics
        </Button>
      </div>

      {/* Health Status Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Database Status</CardTitle>
            {health && getHealthStatusIcon(health.status)}
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${health ? getHealthStatusColor(health.status) : ''}`}>
              {health?.status || 'Unknown'}
            </div>
            <p className="text-xs text-muted-foreground">
              Response: {health?.responseTime || 0}ms
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Connections</CardTitle>
            <Database className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{health?.activeConnections || 0}</div>
            <p className="text-xs text-muted-foreground">
              Database size: {health?.databaseSize || 'Unknown'}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Response Time</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{performance?.averageResponseTime || 0}ms</div>
            <p className="text-xs text-muted-foreground">
              {performance?.totalQueries || 0} total queries
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Slow Query Rate</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{performance?.slowQueryRate || 0}%</div>
            <p className="text-xs text-muted-foreground">
              {performance?.slowQueries || 0} slow queries
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Performance Details */}
      <Tabs defaultValue="queries" className="space-y-4">
        <TabsList>
          <TabsTrigger value="queries">Query Performance</TabsTrigger>
          <TabsTrigger value="recommendations">Recommendations</TabsTrigger>
        </TabsList>

        <TabsContent value="queries" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5" />
                Query Performance Statistics
              </CardTitle>
              <CardDescription>
                Detailed performance metrics for database queries
              </CardDescription>
            </CardHeader>
            <CardContent>
              {queryStats && Object.keys(queryStats).length > 0 ? (
                <div className="space-y-4">
                  {Object.entries(queryStats).map(([queryName, stats]) => (
                    <div key={queryName} className="border rounded-lg p-4">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-medium">{queryName}</h4>
                        <Badge variant={stats.avg > 1000 ? 'destructive' : stats.avg > 500 ? 'secondary' : 'default'}>
                          {stats.avg}ms avg
                        </Badge>
                      </div>
                      <div className="grid grid-cols-3 gap-4 text-sm">
                        <div>
                          <span className="text-muted-foreground">Count:</span>
                          <span className="ml-2 font-medium">{stats.count}</span>
                        </div>
                        <div>
                          <span className="text-muted-foreground">Max:</span>
                          <span className="ml-2 font-medium">{stats.max}ms</span>
                        </div>
                        <div>
                          <span className="text-muted-foreground">Performance:</span>
                          <Progress 
                            value={Math.max(0, 100 - (stats.avg / 10))} 
                            className="ml-2 w-16 h-2" 
                          />
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  No query statistics available. Run some operations to see performance data.
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="recommendations" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Performance Recommendations</CardTitle>
              <CardDescription>
                Suggestions to improve database performance
              </CardDescription>
            </CardHeader>
            <CardContent>
              {recommendations.length > 0 ? (
                <div className="space-y-3">
                  {recommendations.map((recommendation, index) => (
                    <Alert key={index}>
                      <AlertTriangle className="h-4 w-4" />
                      <AlertDescription>{recommendation}</AlertDescription>
                    </Alert>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  <CheckCircle className="h-12 w-12 mx-auto mb-4 text-green-600" />
                  No performance issues detected. Your database is running optimally!
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Error Display */}
      {health?.error && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            Database Error: {health.error}
          </AlertDescription>
        </Alert>
      )}
    </div>
  );
}
