'use client';

import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { X } from 'lucide-react';
import { FileUploadDropzone, FileUploadItem } from './file-upload-dropzone';
import { createDocumentSchema } from '@/services/document-management';
import { v4 as uuidv4 } from 'uuid';
import { DocumentAnalysisResponse } from '@/services/ai-document-analysis';

const uploadFormSchema = createDocumentSchema.extend({
  files: z.array(z.any()).min(1, 'At least one file is required'),
});

type UploadFormData = z.infer<typeof uploadFormSchema>;

interface DocumentUploadDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  relatedToType: string;
  relatedToId: string;
  onUploadComplete?: (documents: any[]) => void;
  onUploadError?: (error: string) => void;
  opportunityTitle?: string;
  companyName?: string;
}

export function DocumentUploadDialog({
  open,
  onOpenChange,
  relatedToType,
  relatedToId,
  onUploadComplete,
  onUploadError,
  opportunityTitle,
  companyName,
}: DocumentUploadDialogProps) {
  const [uploadItems, setUploadItems] = useState<FileUploadItem[]>([]);
  const [uploading, setUploading] = useState(false);
  const [tags, setTags] = useState<string[]>([]);
  const [tagInput, setTagInput] = useState('');
  const [aiAnalysis, setAiAnalysis] = useState<DocumentAnalysisResponse | null>(null);
  const [analyzing, setAnalyzing] = useState(false);
  const [aiAvailable, setAiAvailable] = useState(false);
  const [enableAiAnalysis, setEnableAiAnalysis] = useState(false);

  const form = useForm<UploadFormData>({
    resolver: zodResolver(uploadFormSchema),
    defaultValues: {
      name: '',
      relatedToType: relatedToType as any,
      relatedToId,
      description: '',
      tags: [],
      isPublic: false,
      files: [],
    },
  });

  const handleFilesAdded = (files: File[]) => {
    const newItems: FileUploadItem[] = files.map(file => ({
      file,
      id: uuidv4(),
      progress: 0,
      status: 'pending',
    }));

    setUploadItems(prev => [...prev, ...newItems]);
    form.setValue('files', [...uploadItems.map(item => item.file), ...files]);

    // Trigger AI analysis for the first file if available and enabled
    if (files.length > 0 && aiAvailable && enableAiAnalysis) {
      analyzeDocument(files[0]);
    }
  };

  const handleFileRemove = (fileId: string) => {
    setUploadItems(prev => {
      const updated = prev.filter(item => item.id !== fileId);
      form.setValue('files', updated.map(item => item.file));
      return updated;
    });
  };

  const handleAddTag = () => {
    if (tagInput.trim() && !tags.includes(tagInput.trim())) {
      const newTags = [...tags, tagInput.trim()];
      setTags(newTags);
      form.setValue('tags', newTags);
      setTagInput('');
    }
  };

  const handleRemoveTag = (tagToRemove: string) => {
    const newTags = tags.filter(tag => tag !== tagToRemove);
    setTags(newTags);
    form.setValue('tags', newTags);
  };

  // Check AI availability on mount
  useEffect(() => {
    const checkAiAvailability = async () => {
      try {
        const response = await fetch('/api/documents/analyze');
        const data = await response.json();
        setAiAvailable(data.success && data.data.available);
      } catch (error) {
        console.error('Failed to check AI availability:', error);
        setAiAvailable(false);
      }
    };

    if (open) {
      checkAiAvailability();
    }
  }, [open]);

  // Trigger analysis when AI is enabled and files are available
  useEffect(() => {
    if (enableAiAnalysis && uploadItems.length > 0 && aiAvailable && !analyzing && !aiAnalysis) {
      analyzeDocument(uploadItems[0].file);
    }
  }, [enableAiAnalysis, uploadItems.length, aiAvailable]);

  // Analyze document with AI
  const analyzeDocument = async (file: File) => {
    if (!aiAvailable || analyzing) return;

    setAnalyzing(true);
    setAiAnalysis(null);

    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('relatedToType', relatedToType);
      formData.append('relatedToId', relatedToId);
      if (opportunityTitle) formData.append('opportunityTitle', opportunityTitle);
      if (companyName) formData.append('companyName', companyName);

      const response = await fetch('/api/documents/analyze', {
        method: 'POST',
        body: formData,
      });

      const data = await response.json();

      if (response.ok) {
        setAiAnalysis(data.data);
        // Auto-populate form fields with AI suggestions
        if (data.data.suggestedTitle && !form.getValues('name')) {
          form.setValue('name', data.data.suggestedTitle);
        }
        if (data.data.suggestedDescription && !form.getValues('description')) {
          form.setValue('description', data.data.suggestedDescription);
        }
        // Add key topics as tags
        if (data.data.keyTopics && data.data.keyTopics.length > 0) {
          const newTags = [...tags, ...data.data.keyTopics.slice(0, 3)];
          const uniqueTags = [...new Set(newTags)];
          setTags(uniqueTags);
          form.setValue('tags', uniqueTags);
        }
      } else {
        console.error('AI analysis failed:', data.error);
      }
    } catch (error) {
      console.error('Error analyzing document:', error);
    } finally {
      setAnalyzing(false);
    }
  };

  // Apply AI suggestions manually
  const applyAiSuggestions = () => {
    if (!aiAnalysis) return;

    form.setValue('name', aiAnalysis.suggestedTitle);
    form.setValue('description', aiAnalysis.suggestedDescription);

    if (aiAnalysis.keyTopics && aiAnalysis.keyTopics.length > 0) {
      const newTags = [...tags, ...aiAnalysis.keyTopics.slice(0, 3)];
      const uniqueTags = [...new Set(newTags)];
      setTags(uniqueTags);
      form.setValue('tags', uniqueTags);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleAddTag();
    }
  };

  const uploadFile = async (item: FileUploadItem, formData: UploadFormData): Promise<any> => {
    const uploadFormData = new FormData();
    uploadFormData.append('file', item.file);
    uploadFormData.append('name', formData.name || item.file.name);
    uploadFormData.append('relatedToType', formData.relatedToType);
    uploadFormData.append('relatedToId', formData.relatedToId);
    uploadFormData.append('description', formData.description || '');
    uploadFormData.append('tags', JSON.stringify(formData.tags));
    uploadFormData.append('isPublic', formData.isPublic.toString());

    // Update status to uploading
    setUploadItems(prev => prev.map(prevItem => 
      prevItem.id === item.id 
        ? { ...prevItem, status: 'uploading', progress: 0 }
        : prevItem
    ));

    try {
      const response = await fetch('/api/documents', {
        method: 'POST',
        body: uploadFormData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Upload failed');
      }

      const result = await response.json();
      
      // Update status to success
      setUploadItems(prev => prev.map(prevItem => 
        prevItem.id === item.id 
          ? { ...prevItem, status: 'success', progress: 100 }
          : prevItem
      ));

      return result.data.document;
    } catch (error) {
      // Update status to error
      setUploadItems(prev => prev.map(prevItem => 
        prevItem.id === item.id 
          ? { 
              ...prevItem, 
              status: 'error', 
              error: error instanceof Error ? error.message : 'Upload failed'
            }
          : prevItem
      ));
      throw error;
    }
  };

  const onSubmit = async (data: UploadFormData) => {
    if (uploadItems.length === 0) {
      onUploadError?.('Please select at least one file to upload');
      return;
    }

    setUploading(true);

    try {
      const uploadPromises = uploadItems.map(item => uploadFile(item, data));
      const results = await Promise.allSettled(uploadPromises);
      
      const successful = results
        .filter((result): result is PromiseFulfilledResult<any> => result.status === 'fulfilled')
        .map(result => result.value);
      
      const failed = results
        .filter((result): result is PromiseRejectedResult => result.status === 'rejected')
        .map(result => result.reason);

      if (successful.length > 0) {
        onUploadComplete?.(successful);
      }

      if (failed.length > 0) {
        onUploadError?.(
          `${failed.length} file(s) failed to upload: ${failed.map(f => f.message).join(', ')}`
        );
      }

      if (successful.length === uploadItems.length) {
        // All uploads successful, close dialog
        handleClose();
      }
    } catch (error) {
      onUploadError?.(
        error instanceof Error ? error.message : 'Upload failed'
      );
    } finally {
      setUploading(false);
    }
  };

  const handleClose = () => {
    if (!uploading) {
      setUploadItems([]);
      setTags([]);
      setTagInput('');
      form.reset();
      onOpenChange(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Upload Documents</DialogTitle>
          <DialogDescription>
            Upload documents and associate them with this {relatedToType}.
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* File Upload */}
            <FileUploadDropzone
              onFilesAdded={handleFilesAdded}
              onFileRemove={handleFileRemove}
              uploadItems={uploadItems}
              disabled={uploading}
            />

            {/* AI Analysis Section */}
            {aiAvailable && uploadItems.length > 0 && (
              <div className="space-y-4 p-4 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg border">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                    <h3 className="font-medium text-gray-900">AI Document Analysis</h3>
                    <div className="flex items-center space-x-2">
                      <Switch
                        checked={enableAiAnalysis}
                        onCheckedChange={setEnableAiAnalysis}
                        disabled={uploading}
                      />
                      <span className="text-sm text-gray-600">
                        {enableAiAnalysis ? 'Enabled' : 'Disabled'}
                      </span>
                    </div>
                  </div>
                  {enableAiAnalysis && !analyzing && !aiAnalysis && (
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => analyzeDocument(uploadItems[0].file)}
                      disabled={uploading}
                    >
                      Analyze Document
                    </Button>
                  )}
                </div>

                {!enableAiAnalysis && (
                  <div className="text-sm text-gray-600">
                    Enable AI analysis to get automatic suggestions for document title, description, and tags.
                  </div>
                )}

                {enableAiAnalysis && analyzing && (
                  <div className="flex items-center space-x-2 text-sm text-gray-600">
                    <div className="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
                    <span>Analyzing document content...</span>
                  </div>
                )}

                {enableAiAnalysis && aiAnalysis && (
                  <div className="space-y-3">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <label className="text-xs font-medium text-gray-700">Suggested Title</label>
                        <div className="p-2 bg-white rounded border text-sm">
                          {aiAnalysis.suggestedTitle}
                        </div>
                      </div>
                      <div className="space-y-2">
                        <label className="text-xs font-medium text-gray-700">Document Type</label>
                        <div className="p-2 bg-white rounded border text-sm">
                          {aiAnalysis.documentType}
                        </div>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <label className="text-xs font-medium text-gray-700">Suggested Description</label>
                      <div className="p-2 bg-white rounded border text-sm">
                        {aiAnalysis.suggestedDescription}
                      </div>
                    </div>

                    {aiAnalysis.keyTopics && aiAnalysis.keyTopics.length > 0 && (
                      <div className="space-y-2">
                        <label className="text-xs font-medium text-gray-700">Key Topics</label>
                        <div className="flex flex-wrap gap-1">
                          {aiAnalysis.keyTopics.map((topic, index) => (
                            <Badge key={index} variant="outline" className="text-xs">
                              {topic}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    )}

                    <div className="flex items-center justify-between pt-2">
                      <div className="text-xs text-gray-500">
                        Confidence: {aiAnalysis.confidence}% •
                        Processed in {Math.round(aiAnalysis.processingTime / 1000)}s
                      </div>
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={applyAiSuggestions}
                        disabled={uploading}
                      >
                        Apply Suggestions
                      </Button>
                    </div>
                  </div>
                )}
              </div>
            )}

            {/* Document Name */}
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Document Name (Optional)</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Leave empty to use file name"
                      {...field}
                      disabled={uploading}
                    />
                  </FormControl>
                  <FormDescription>
                    If empty, the original file name will be used
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Description */}
            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description (Optional)</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Add a description for these documents..."
                      {...field}
                      disabled={uploading}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Tags */}
            <div className="space-y-2">
              <label className="text-sm font-medium">Tags (Optional)</label>
              <div className="flex space-x-2">
                <Input
                  placeholder="Add a tag..."
                  value={tagInput}
                  onChange={(e) => setTagInput(e.target.value)}
                  onKeyPress={handleKeyPress}
                  disabled={uploading}
                />
                <Button
                  type="button"
                  variant="outline"
                  onClick={handleAddTag}
                  disabled={!tagInput.trim() || uploading}
                >
                  Add
                </Button>
              </div>
              {tags.length > 0 && (
                <div className="flex flex-wrap gap-2">
                  {tags.map((tag) => (
                    <Badge key={tag} variant="secondary" className="flex items-center gap-1">
                      {tag}
                      <X
                        className="w-3 h-3 cursor-pointer"
                        onClick={() => handleRemoveTag(tag)}
                      />
                    </Badge>
                  ))}
                </div>
              )}
            </div>

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={handleClose}
                disabled={uploading}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={uploadItems.length === 0 || uploading}
              >
                {uploading ? 'Uploading...' : `Upload ${uploadItems.length} File(s)`}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
