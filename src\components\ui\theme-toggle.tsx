'use client';

import React from 'react';
import {
  <PERSON>,
  <PERSON>,
  Monitor,
  Check
} from 'lucide-react';
import { useTheme } from '@/components/providers/theme-provider';
import { useTranslation } from '@/components/providers/locale-provider';
import { cn } from '@/lib/utils';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Button } from '@/components/ui/button';

const themeOptions = [
  {
    value: 'light' as const,
    icon: Sun,
    labelKey: 'theme.light',
  },
  {
    value: 'dark' as const,
    icon: Moon,
    labelKey: 'theme.dark',
  },
  {
    value: 'system' as const,
    icon: Monitor,
    labelKey: 'theme.system',
  },
];

interface ThemeToggleProps {
  variant?: 'dropdown' | 'button' | 'sidebar';
  showLabel?: boolean;
  className?: string;
  size?: 'sm' | 'default' | 'lg';
}

export function ThemeToggle({
  variant = 'dropdown',
  showLabel = false,
  className,
  size = 'default'
}: ThemeToggleProps) {
  const { theme, setTheme } = useTheme();
  const { t } = useTranslation();

  const currentOption = themeOptions.find(option => option.value === theme) || themeOptions[0];
  const CurrentIcon = currentOption.icon;

  if (variant === 'button') {
    const nextTheme = theme === 'light' ? 'dark' : theme === 'dark' ? 'system' : 'light';

    return (
      <Button
        variant="ghost"
        size={size === 'sm' ? 'sm' : 'icon'}
        onClick={() => setTheme(nextTheme)}
        className={cn(className)}
        title={t('theme.toggle')}
      >
        <CurrentIcon className={cn(
          size === 'sm' ? 'h-4 w-4' : 'h-5 w-5'
        )} />
        {showLabel && (
          <span className="ml-2 text-sm font-medium">
            {t(currentOption.labelKey)}
          </span>
        )}
      </Button>
    );
  }

  if (variant === 'sidebar') {
    return (
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="ghost"
            size="sm"
            className={cn(
              "h-8 w-8 px-0 data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground",
              className
            )}
            title={t('theme.toggle')}
          >
            <CurrentIcon className="h-4 w-4" />
            <span className="sr-only">{t('theme.toggle')}</span>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-48">
          {themeOptions.map((option) => {
            const Icon = option.icon;
            const isSelected = theme === option.value;

            return (
              <DropdownMenuItem
                key={option.value}
                onClick={() => setTheme(option.value)}
                className="gap-2"
              >
                <Icon className="h-4 w-4" />
                <span className="flex-1">{t(option.labelKey)}</span>
                {isSelected && <Check className="h-4 w-4" />}
              </DropdownMenuItem>
            );
          })}
        </DropdownMenuContent>
      </DropdownMenu>
    );
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          size={size}
          className={cn(
            "gap-2",
            !showLabel && "px-2",
            className
          )}
          title={t('theme.toggle')}
        >
          <CurrentIcon className={cn(
            size === 'sm' ? 'h-4 w-4' : 'h-5 w-5'
          )} />
          {showLabel && (
            <span className="text-sm font-medium">
              {t(currentOption.labelKey)}
            </span>
          )}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-48">
        {themeOptions.map((option) => {
          const Icon = option.icon;
          const isSelected = theme === option.value;

          return (
            <DropdownMenuItem
              key={option.value}
              onClick={() => setTheme(option.value)}
              className="gap-2"
            >
              <Icon className="h-4 w-4" />
              <span className="flex-1">{t(option.labelKey)}</span>
              {isSelected && <Check className="h-4 w-4" />}
            </DropdownMenuItem>
          );
        })}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
