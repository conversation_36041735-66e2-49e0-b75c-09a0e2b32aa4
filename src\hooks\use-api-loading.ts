"use client"

import { useCallback } from 'react'
import { useUIStore } from '@/stores/ui-store'

/**
 * Hook for managing API loading states separately from navigation loading
 * This prevents API calls from triggering page refresh-like behavior
 */
export function useApiLoading() {
  const { setApiLoading } = useUIStore()

  const withApiLoading = useCallback(async <T>(
    apiCall: () => Promise<T>,
    options?: {
      showGlobalLoading?: boolean // Whether to show global API loading indicator
    }
  ): Promise<T> => {
    try {
      if (options?.showGlobalLoading) {
        setApiLoading(true)
      }
      
      const result = await apiCall()
      return result
    } finally {
      if (options?.showGlobalLoading) {
        setApiLoading(false)
      }
    }
  }, [setApiLoading])

  const startApiLoading = useCallback(() => {
    setApiLoading(true)
  }, [setApiLoading])

  const stopApiLoading = useCallback(() => {
    setApiLoading(false)
  }, [setApiLoading])

  return {
    withApiLoading,
    startApiLoading,
    stopApiLoading
  }
}
