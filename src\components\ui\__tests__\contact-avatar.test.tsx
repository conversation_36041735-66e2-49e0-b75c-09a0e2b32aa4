import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { ContactAvatar, UserAvatar, AvatarGroup } from '../contact-avatar';
import '@testing-library/jest-dom';

// Mock the tooltip provider
jest.mock('../tooltip', () => ({
  Tooltip: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
  TooltipTrigger: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
  TooltipContent: ({ children }: { children: React.ReactNode }) => <div data-testid="tooltip-content">{children}</div>,
}));

describe('ContactAvatar', () => {
  const defaultProps = {
    firstName: 'John',
    lastName: 'Doe',
  };

  it('renders with initials when no avatar URL is provided', () => {
    render(<ContactAvatar {...defaultProps} />);
    expect(screen.getByText('JD')).toBeInTheDocument();
  });

  it('renders with single initial when only first name is provided', () => {
    render(<ContactAvatar firstName="John" />);
    expect(screen.getByText('J')).toBeInTheDocument();
  });

  it('displays loading skeleton when loading prop is true', () => {
    render(<ContactAvatar {...defaultProps} loading />);
    expect(screen.getByLabelText('Loading avatar')).toBeInTheDocument();
  });

  it('shows status indicator when showStatus is true', () => {
    render(<ContactAvatar {...defaultProps} status="online" showStatus />);
    expect(screen.getByLabelText('Status: online')).toBeInTheDocument();
  });

  it('handles click events', () => {
    const handleClick = jest.fn();
    render(<ContactAvatar {...defaultProps} onClick={handleClick} />);
    
    const avatar = screen.getByRole('button');
    fireEvent.click(avatar);
    expect(handleClick).toHaveBeenCalledTimes(1);
  });

  it('handles keyboard navigation', () => {
    const handleClick = jest.fn();
    render(<ContactAvatar {...defaultProps} onClick={handleClick} />);
    
    const avatar = screen.getByRole('button');
    fireEvent.keyDown(avatar, { key: 'Enter' });
    expect(handleClick).toHaveBeenCalledTimes(1);
    
    fireEvent.keyDown(avatar, { key: ' ' });
    expect(handleClick).toHaveBeenCalledTimes(2);
  });

  it('shows tooltip when showTooltip is true', () => {
    render(<ContactAvatar {...defaultProps} showTooltip />);
    expect(screen.getByTestId('tooltip-content')).toBeInTheDocument();
  });

  it('uses custom tooltip content when provided', () => {
    const customTooltip = <div>Custom tooltip content</div>;
    render(<ContactAvatar {...defaultProps} showTooltip tooltipContent={customTooltip} />);
    expect(screen.getByText('Custom tooltip content')).toBeInTheDocument();
  });

  it('applies correct size classes', () => {
    const { rerender } = render(<ContactAvatar {...defaultProps} size="sm" />);
    expect(screen.getByRole('img', { hidden: true })).toHaveClass('h-6', 'w-6');
    
    rerender(<ContactAvatar {...defaultProps} size="lg" />);
    expect(screen.getByRole('img', { hidden: true })).toHaveClass('h-10', 'w-10');
  });

  it('handles image load and error states', async () => {
    const { container } = render(
      <ContactAvatar {...defaultProps} avatarUrl="https://example.com/avatar.jpg" />
    );
    
    const img = container.querySelector('img');
    expect(img).toBeInTheDocument();
    
    // Simulate image error
    if (img) {
      fireEvent.error(img);
      await waitFor(() => {
        expect(screen.getByText('JD')).toBeInTheDocument();
      });
    }
  });
});

describe('UserAvatar', () => {
  const defaultUser = {
    firstName: 'John',
    lastName: 'Doe',
    email: '<EMAIL>',
    jobTitle: 'Software Engineer',
    status: 'online' as const,
  };

  it('renders basic user avatar', () => {
    render(<UserAvatar user={defaultUser} />);
    expect(screen.getByText('JD')).toBeInTheDocument();
  });

  it('displays email when showEmail is true', () => {
    render(<UserAvatar user={defaultUser} showEmail />);
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
  });

  it('displays job title when showJobTitle is true', () => {
    render(<UserAvatar user={defaultUser} showJobTitle />);
    expect(screen.getByText('Software Engineer')).toBeInTheDocument();
  });

  it('handles vertical layout', () => {
    render(<UserAvatar user={defaultUser} showEmail layout="vertical" />);
    const container = screen.getByText('<EMAIL>').closest('div');
    expect(container).toHaveClass('text-center');
  });

  it('truncates long names when truncateText is true', () => {
    const longNameUser = {
      ...defaultUser,
      firstName: 'VeryLongFirstName',
      lastName: 'VeryLongLastName',
    };
    
    render(<UserAvatar user={longNameUser} showEmail maxNameLength={10} />);
    expect(screen.getByText('VeryLongFi...')).toBeInTheDocument();
  });

  it('displays loading skeleton when loading', () => {
    render(<UserAvatar user={defaultUser} showEmail loading />);
    expect(screen.getAllByTestId(/skeleton/i)).toHaveLength(2); // Avatar + text skeletons
  });

  it('handles missing user data gracefully', () => {
    const incompleteUser = {
      firstName: null,
      lastName: null,
      email: undefined,
    };
    
    render(<UserAvatar user={incompleteUser} showEmail />);
    expect(screen.getByText('U')).toBeInTheDocument(); // 'Unknown' initial
  });
});

describe('AvatarGroup', () => {
  const defaultUsers = [
    { id: '1', firstName: 'John', lastName: 'Doe', email: '<EMAIL>' },
    { id: '2', firstName: 'Jane', lastName: 'Smith', email: '<EMAIL>' },
    { id: '3', firstName: 'Bob', lastName: 'Johnson', email: '<EMAIL>' },
    { id: '4', firstName: 'Alice', lastName: 'Brown', email: '<EMAIL>' },
    { id: '5', firstName: 'Charlie', lastName: 'Wilson', email: '<EMAIL>' },
  ];

  it('renders correct number of avatars based on max prop', () => {
    render(<AvatarGroup users={defaultUsers} max={3} />);
    
    // Should show 3 avatars + overflow indicator
    expect(screen.getByText('JD')).toBeInTheDocument();
    expect(screen.getByText('JS')).toBeInTheDocument();
    expect(screen.getByText('BJ')).toBeInTheDocument();
    expect(screen.getByText('+2')).toBeInTheDocument();
  });

  it('handles empty user list', () => {
    render(<AvatarGroup users={[]} />);
    expect(screen.queryByText('+')).not.toBeInTheDocument();
  });

  it('does not show overflow when users count is less than max', () => {
    render(<AvatarGroup users={defaultUsers.slice(0, 2)} max={3} />);
    expect(screen.queryByText('+')).not.toBeInTheDocument();
  });

  it('handles user click events', () => {
    const handleUserClick = jest.fn();
    render(<AvatarGroup users={defaultUsers} max={3} onUserClick={handleUserClick} />);
    
    const firstAvatar = screen.getByText('JD').closest('[role="button"]');
    if (firstAvatar) {
      fireEvent.click(firstAvatar);
      expect(handleUserClick).toHaveBeenCalledWith(defaultUsers[0], 0);
    }
  });

  it('displays loading skeletons when loading', () => {
    render(<AvatarGroup users={defaultUsers} max={3} loading />);
    expect(screen.getByLabelText('Loading avatars')).toBeInTheDocument();
  });

  it('applies correct spacing classes', () => {
    const { rerender } = render(<AvatarGroup users={defaultUsers} spacing="tight" />);
    expect(screen.getByRole('group')).toHaveClass('-space-x-1');
    
    rerender(<AvatarGroup users={defaultUsers} spacing="loose" />);
    expect(screen.getByRole('group')).toHaveClass('-space-x-1.5');
  });

  it('shows status indicators when showStatus is true', () => {
    const usersWithStatus = defaultUsers.map(user => ({
      ...user,
      status: 'online' as const,
    }));
    
    render(<AvatarGroup users={usersWithStatus} showStatus max={2} />);
    expect(screen.getAllByLabelText('Status: online')).toHaveLength(2);
  });

  it('provides proper accessibility attributes', () => {
    render(<AvatarGroup users={defaultUsers} aria-label="Team members" />);
    expect(screen.getByLabelText('Team members')).toBeInTheDocument();
  });
});

describe('Avatar System Integration', () => {
  it('maintains consistent styling across components', () => {
    const user = {
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
    };

    const { container: contactContainer } = render(
      <ContactAvatar firstName="John" lastName="Doe" size="md" />
    );
    
    const { container: userContainer } = render(
      <UserAvatar user={user} size="md" />
    );

    const contactAvatar = contactContainer.querySelector('[data-slot="avatar"]');
    const userAvatar = userContainer.querySelector('[data-slot="avatar"]');

    expect(contactAvatar).toHaveClass('h-8', 'w-8');
    expect(userAvatar).toHaveClass('h-8', 'w-8');
  });

  it('handles theme changes properly', () => {
    // This would require a theme provider mock, but demonstrates the concept
    render(<ContactAvatar firstName="John" lastName="Doe" />);
    
    const fallback = screen.getByText('JD');
    expect(fallback).toHaveClass('text-white');
  });
});
