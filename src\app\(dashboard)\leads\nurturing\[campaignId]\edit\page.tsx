'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { NurturingCampaignForm } from '@/components/leads/nurturing-campaign-form';
import { PageLayout } from '@/components/layout/page-layout';
import { PermissionGate } from '@/components/ui/permission-gate';
import { PermissionResource, PermissionAction } from '@/types';
import { Card, CardContent } from '@/components/ui/card';
import { EmptyState } from '@/components/ui/empty-state';
import { Skeleton } from '@/components/ui/skeleton';
import { useToast } from '@/hooks/use-toast';
import { NurturingCampaign } from '@/services/lead-nurturing';
import { ExclamationTriangleIcon } from '@heroicons/react/24/outline';

interface EditCampaignPageProps {
  params: {
    campaignId: string;
  };
}

export default function EditCampaignPage({ params }: EditCampaignPageProps) {
  const [campaign, setCampaign] = useState<NurturingCampaign | null>(null);
  const [loading, setLoading] = useState(true);
  const { data: session } = useSession();
  const router = useRouter();
  const { toast } = useToast();
  const tenantId = session?.user?.tenants?.[0]?.id;

  useEffect(() => {
    if (tenantId) {
      fetchCampaign();
    }
  }, [tenantId, params.campaignId]);

  const fetchCampaign = async () => {
    try {
      const response = await fetch(`/api/leads/nurturing/campaigns/${params.campaignId}`, {
        headers: {
          'x-tenant-id': tenantId!
        }
      });

      if (!response.ok) {
        if (response.status === 404) {
          router.push('/leads/nurturing');
          return;
        }
        throw new Error('Failed to fetch campaign');
      }

      const data = await response.json();
      setCampaign(data.data);
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to fetch campaign details',
        variant: 'destructive'
      });
      router.push('/leads/nurturing');
    } finally {
      setLoading(false);
    }
  };

  const handleSave = (updatedCampaign: NurturingCampaign) => {
    router.push(`/leads/nurturing/${updatedCampaign.id}`);
  };

  const handleCancel = () => {
    router.push(`/leads/nurturing/${params.campaignId}`);
  };

  if (!tenantId) {
    return (
      <PageLayout>
        <div className="flex items-center justify-center h-64">
          <p className="text-muted-foreground">Loading...</p>
        </div>
      </PageLayout>
    );
  }

  if (loading) {
    return (
      <PageLayout>
        <div className="space-y-6">
          <div className="flex items-center space-x-3">
            <Skeleton className="h-10 w-20" />
            <div>
              <Skeleton className="h-8 w-48" />
              <Skeleton className="h-4 w-64 mt-2" />
            </div>
          </div>
          
          <Card>
            <CardContent className="p-6 space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Skeleton className="h-4 w-24" />
                  <Skeleton className="h-10 w-full" />
                </div>
                <div className="space-y-2">
                  <Skeleton className="h-4 w-32" />
                  <Skeleton className="h-10 w-full" />
                </div>
              </div>
              <div className="space-y-2">
                <Skeleton className="h-4 w-20" />
                <Skeleton className="h-20 w-full" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6 space-y-4">
              <div className="space-y-2">
                <Skeleton className="h-4 w-32" />
                <div className="flex gap-2">
                  {[1, 2, 3, 4].map((i) => (
                    <Skeleton key={i} className="h-6 w-16" />
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </PageLayout>
    );
  }

  if (!campaign) {
    return (
      <PageLayout>
        <Card>
          <CardContent className="p-8">
            <EmptyState
              icon={<ExclamationTriangleIcon className="w-12 h-12 text-muted-foreground" />}
              title="Campaign Not Found"
              description="The campaign you're trying to edit doesn't exist or has been deleted."
              action={{
                label: 'Back to Campaigns',
                onClick: () => router.push('/leads/nurturing'),
                variant: 'primary'
              }}
            />
          </CardContent>
        </Card>
      </PageLayout>
    );
  }

  return (
    <PermissionGate
      resource={PermissionResource.LEADS}
      action={PermissionAction.UPDATE}
      fallback={
        <PageLayout>
          <Card>
            <CardContent className="p-8">
              <EmptyState
                icon={<ExclamationTriangleIcon className="w-12 h-12 text-muted-foreground" />}
                title="Access Denied"
                description="You don't have permission to edit this campaign. Please contact your administrator for access."
                action={{
                  label: 'Back to Campaign',
                  onClick: () => router.push(`/leads/nurturing/${params.campaignId}`),
                  variant: 'primary'
                }}
              />
            </CardContent>
          </Card>
        </PageLayout>
      }
    >
      <PageLayout>
        <NurturingCampaignForm 
          campaign={campaign}
          tenantId={tenantId}
          onSave={handleSave}
          onCancel={handleCancel}
        />
      </PageLayout>
    </PermissionGate>
  );
}
