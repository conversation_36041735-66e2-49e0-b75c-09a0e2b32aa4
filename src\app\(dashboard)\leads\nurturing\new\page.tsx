'use client';

import { useSession } from 'next-auth/react';
import { NurturingCampaignForm } from '@/components/leads/nurturing-campaign-form';
import { PageLayout } from '@/components/layout/page-layout';
import { PermissionGate } from '@/components/ui/permission-gate';
import { PermissionResource, PermissionAction } from '@/types';
import { Card, CardContent } from '@/components/ui/card';
import { EmptyState } from '@/components/ui/empty-state';
import { ExclamationTriangleIcon } from '@heroicons/react/24/outline';
import { useSmoothNavigation } from '@/hooks/use-smooth-navigation';

export default function NewNurturingCampaignPage() {
  const { data: session } = useSession();
  const { navigate } = useSmoothNavigation();
  const tenantId = session?.user?.tenants?.[0]?.id;

  if (!tenantId) {
    return (
      <PageLayout>
        <div className="flex items-center justify-center h-64">
          <p className="text-muted-foreground">Loading...</p>
        </div>
      </PageLayout>
    );
  }

  return (
    <PermissionGate
      resource={PermissionResource.LEADS}
      action={PermissionAction.CREATE}
      fallback={
        <PageLayout>
          <Card>
            <CardContent className="p-8">
              <EmptyState
                icon={<ExclamationTriangleIcon className="w-12 h-12 text-muted-foreground" />}
                title="Access Denied"
                description="You don't have permission to create lead nurturing campaigns. Please contact your administrator for access."
                action={{
                  label: 'Back to Campaigns',
                  onClick: () => navigate('/leads/nurturing'),
                  variant: 'primary'
                }}
              />
            </CardContent>
          </Card>
        </PageLayout>
      }
    >
      <PageLayout>
        <NurturingCampaignForm tenantId={tenantId} />
      </PageLayout>
    </PermissionGate>
  );
}
