'use client';

import React, { useState, useEffect, use } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { ArrowLeftIcon, Download, Eye, Edit3 } from 'lucide-react';
import { PageLayout } from '@/components/layout/page-layout';
import { ProposalSectionsView } from '@/components/proposals/proposal-sections-view';
import { EmptyState } from '@/components/ui/empty-state';
import { PermissionGate, MultiPermissionGate } from '@/components/ui/permission-gate';
import { usePermissions } from '@/components/providers/permission-provider';
import { PermissionResource, PermissionAction } from '@/types';
import { toast } from 'sonner';

interface ProposalDetailPageProps {
  params: Promise<{
    id: string;
  }>;
}

export default function ProposalDetailPage({ params }: ProposalDetailPageProps) {
  const router = useRouter();
  const resolvedParams = use(params);
  const [proposal, setProposal] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const { hasPermission } = usePermissions();

  // Load proposal data
  useEffect(() => {
    const loadProposal = async () => {
      try {
        setLoading(true);
        const response = await fetch(`/api/proposals/${resolvedParams.id}/sections`);

        if (response.ok) {
          const result = await response.json();
          setProposal(result.data);
        } else if (response.status === 404) {
          toast.error('Proposal not found');
          router.push('/proposals');
        } else {
          toast.error('Failed to load proposal');
        }
      } catch (error) {
        console.error('Error loading proposal:', error);
        toast.error('Failed to load proposal');
      } finally {
        setLoading(false);
      }
    };

    loadProposal();
  }, [resolvedParams.id, router]);

  const handleDocumentGenerate = async () => {
    if (!proposal) return;

    try {
      const response = await fetch(`/api/proposals/${proposal.proposalId}/generate-document`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          format: 'docx',
          includeCharts: true,
          includeBranding: true
        }),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to generate document');
      }

      // Download the document
      window.open(`/api/proposals/${proposal.proposalId}/download`, '_blank');
      toast.success('Document generated and download started');

    } catch (error) {
      console.error('Error generating document:', error);
      toast.error('Failed to generate document');
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'generating':
        return 'bg-blue-100 text-blue-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <PageLayout>
        <div className="flex justify-center items-center h-96">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </PageLayout>
    );
  }

  if (!proposal) {
    return (
      <PageLayout>
        <Card>
          <CardContent className="p-8">
            <EmptyState
              icon={<Edit3 className="w-12 h-12 text-muted-foreground" />}
              title="Proposal Not Found"
              description="The proposal you're looking for doesn't exist or you don't have permission to view it."
              action={{
                label: 'Back to Proposals',
                onClick: () => router.push('/proposals'),
                variant: 'primary'
              }}
            />
          </CardContent>
        </Card>
      </PageLayout>
    );
  }

  const actions = (
    <div className="flex items-center gap-2">
      <Button variant="outline" size="sm" onClick={() => router.back()}>
        <ArrowLeftIcon className="h-4 w-4 mr-2" />
        Back
      </Button>
      <Button variant="outline" size="sm" onClick={handleDocumentGenerate}>
        <Download className="h-4 w-4 mr-2" />
        Download Document
      </Button>
      {hasPermission(PermissionResource.PROPOSALS, PermissionAction.UPDATE) && (
        <Button variant="outline" size="sm" onClick={() => router.push(`/proposals/${proposal.proposalId}/edit`)}>
          <Edit3 className="h-4 w-4 mr-2" />
          Edit
        </Button>
      )}
    </div>
  );

  return (
    <MultiPermissionGate
      permissions={[
        { resource: PermissionResource.PROPOSALS, action: PermissionAction.READ },
        { resource: PermissionResource.PROPOSALS, action: PermissionAction.UPDATE }
      ]}
      requireAll={false}
      fallback={
        <PageLayout>
          <Card>
            <CardContent className="p-8">
              <EmptyState
                icon={<Edit3 className="w-12 h-12 text-muted-foreground" />}
                title="Access Denied"
                description="You don't have permission to view this proposal."
                action={{
                  label: 'Back to Proposals',
                  onClick: () => router.push('/proposals'),
                  variant: 'primary'
                }}
              />
            </CardContent>
          </Card>
        </PageLayout>
      }
    >
      <PageLayout
        title={proposal.proposalTitle}
        description="AI-generated proposal details and management"
        actions={actions}
      >
        <div className="space-y-6">
          {/* Proposal Overview */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>Proposal Overview</CardTitle>
                <Badge className={getStatusColor(proposal.generationStatus)} variant="secondary">
                  {proposal.generationStatus}
                </Badge>
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div>
                  <div className="font-medium">Sections</div>
                  <div className="text-gray-500">
                    {proposal.statistics.completedSections} / {proposal.statistics.totalSections}
                  </div>
                </div>
                <div>
                  <div className="font-medium">Charts</div>
                  <div className="text-gray-500">
                    {proposal.statistics.renderedCharts} / {proposal.statistics.totalCharts}
                  </div>
                </div>
                <div>
                  <div className="font-medium">Word Count</div>
                  <div className="text-gray-500">{proposal.statistics.totalWordCount.toLocaleString()}</div>
                </div>
                <div>
                  <div className="font-medium">Completion</div>
                  <div className="text-gray-500">{proposal.statistics.completionPercentage}%</div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* AI Generation Notice */}
          {proposal.generationStatus === 'completed' && (
            <Alert>
              <Eye className="h-4 w-4" />
              <AlertDescription>
                This proposal was generated using AI technology based on your RFP documents. 
                You can edit individual sections or regenerate them as needed.
              </AlertDescription>
            </Alert>
          )}

          {/* Proposal Sections */}
          <ProposalSectionsView
            proposalId={proposal.proposalId}
            onDocumentGenerate={handleDocumentGenerate}
          />
        </div>
      </PageLayout>
    </MultiPermissionGate>
  );
}
