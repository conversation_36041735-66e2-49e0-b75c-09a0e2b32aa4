'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Tabs, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Activity, 
  Settings, 
  Play, 
  Pause, 
  BarChart3, 
  Zap, 
  CheckCircle, 
  XCircle,
  Clock,
  Users,
  FileText,
  Bell
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface AutomationTrigger {
  id: string;
  name: string;
  description?: string;
  triggerType: string;
  isActive: boolean;
  priority: number;
  actions: Array<{
    type: string;
    config: Record<string, unknown>;
  }>;
  createdAt: string;
  _count: {
    executions: number;
  };
}

interface ProcessMetrics {
  totalExecutions: number;
  successfulExecutions: number;
  failedExecutions: number;
  averageExecutionTime: number;
  handoversCreated: number;
  projectsCreated: number;
  notificationsSent: number;
}

export function AutomationDashboard() {
  const [triggers, setTriggers] = useState<AutomationTrigger[]>([]);
  const [metrics, setMetrics] = useState<ProcessMetrics | null>(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('overview');
  const { toast } = useToast();

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      setLoading(true);
      const [triggersRes, metricsRes] = await Promise.all([
        fetch('/api/automation/triggers'),
        fetch('/api/automation/triggers/metrics')
      ]);

      if (triggersRes.ok) {
        const triggersData = await triggersRes.json();
        setTriggers(triggersData.data.triggers || []);
      }

      if (metricsRes.ok) {
        const metricsData = await metricsRes.json();
        setMetrics(metricsData.data.metrics);
      }
    } catch (error) {
      console.error('Error fetching automation data:', error);
      toast({
        title: 'Error',
        description: 'Failed to load automation data',
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  };

  const toggleTrigger = async (triggerId: string, isActive: boolean) => {
    try {
      const response = await fetch(`/api/automation/triggers/${triggerId}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ isActive })
      });

      if (response.ok) {
        setTriggers(prev => prev.map(trigger => 
          trigger.id === triggerId ? { ...trigger, isActive } : trigger
        ));
        toast({
          title: 'Success',
          description: `Trigger ${isActive ? 'enabled' : 'disabled'} successfully`
        });
      } else {
        throw new Error('Failed to update trigger');
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to update trigger status',
        variant: 'destructive'
      });
    }
  };

  const testTrigger = async (triggerId: string) => {
    try {
      const response = await fetch('/api/automation/triggers/test', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          triggerId,
          testData: { test: true }
        })
      });

      if (response.ok) {
        toast({
          title: 'Test Completed',
          description: 'Trigger test executed successfully'
        });
      } else {
        throw new Error('Test failed');
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to test trigger',
        variant: 'destructive'
      });
    }
  };

  const getTriggerTypeIcon = (type: string) => {
    switch (type) {
      case 'opportunity_status_change':
        return <Activity className="w-4 h-4" />;
      case 'handover_completion':
        return <CheckCircle className="w-4 h-4" />;
      case 'project_milestone':
        return <FileText className="w-4 h-4" />;
      case 'time_based':
        return <Clock className="w-4 h-4" />;
      default:
        return <Zap className="w-4 h-4" />;
    }
  };

  const getActionTypeIcon = (type: string) => {
    switch (type) {
      case 'create_handover':
        return <FileText className="w-4 h-4" />;
      case 'send_notification':
        return <Bell className="w-4 h-4" />;
      case 'create_project':
        return <Settings className="w-4 h-4" />;
      case 'assign_team':
        return <Users className="w-4 h-4" />;
      default:
        return <Zap className="w-4 h-4" />;
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Process Automation</h1>
          <p className="text-muted-foreground">
            Manage automated workflows and triggers
          </p>
        </div>
        <Button>
          <Settings className="w-4 h-4 mr-2" />
          Configure Triggers
        </Button>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="triggers">Triggers</TabsTrigger>
          <TabsTrigger value="metrics">Metrics</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {/* Metrics Overview */}
          {metrics && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Total Executions</CardTitle>
                  <Activity className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{metrics.totalExecutions}</div>
                  <p className="text-xs text-muted-foreground">
                    {metrics.successfulExecutions} successful
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Handovers Created</CardTitle>
                  <FileText className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{metrics.handoversCreated}</div>
                  <p className="text-xs text-muted-foreground">
                    Automated handovers
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Projects Created</CardTitle>
                  <Settings className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{metrics.projectsCreated}</div>
                  <p className="text-xs text-muted-foreground">
                    From closed opportunities
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Success Rate</CardTitle>
                  <CheckCircle className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {metrics.totalExecutions > 0 
                      ? Math.round((metrics.successfulExecutions / metrics.totalExecutions) * 100)
                      : 0}%
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Execution success rate
                  </p>
                </CardContent>
              </Card>
            </div>
          )}

          {/* Active Triggers Summary */}
          <Card>
            <CardHeader>
              <CardTitle>Active Triggers</CardTitle>
              <CardDescription>
                Currently active automation triggers
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {triggers.filter(t => t.isActive).map(trigger => (
                  <div key={trigger.id} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center space-x-3">
                      {getTriggerTypeIcon(trigger.triggerType)}
                      <div>
                        <p className="font-medium">{trigger.name}</p>
                        <p className="text-sm text-muted-foreground">{trigger.description}</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Badge variant="secondary">
                        {trigger._count.executions} executions
                      </Badge>
                      <Badge variant="outline">
                        Priority {trigger.priority}
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="triggers" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Automation Triggers</CardTitle>
              <CardDescription>
                Manage your automation triggers and their configurations
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {triggers.map(trigger => (
                  <div key={trigger.id} className="border rounded-lg p-4">
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center space-x-3">
                        {getTriggerTypeIcon(trigger.triggerType)}
                        <div>
                          <h3 className="font-medium">{trigger.name}</h3>
                          <p className="text-sm text-muted-foreground">{trigger.description}</p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Switch
                          checked={trigger.isActive}
                          onCheckedChange={(checked) => toggleTrigger(trigger.id, checked)}
                        />
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => testTrigger(trigger.id)}
                        >
                          <Play className="w-4 h-4 mr-1" />
                          Test
                        </Button>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                      <span>Type: {trigger.triggerType.replace('_', ' ')}</span>
                      <span>Priority: {trigger.priority}</span>
                      <span>Executions: {trigger._count.executions}</span>
                      <span>Actions: {trigger.actions.length}</span>
                    </div>

                    <div className="mt-3">
                      <p className="text-sm font-medium mb-2">Actions:</p>
                      <div className="flex flex-wrap gap-2">
                        {trigger.actions.map((action, index) => (
                          <Badge key={index} variant="outline" className="flex items-center space-x-1">
                            {getActionTypeIcon(action.type)}
                            <span>{action.type.replace('_', ' ')}</span>
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="metrics" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Automation Metrics</CardTitle>
              <CardDescription>
                Detailed analytics and performance metrics
              </CardDescription>
            </CardHeader>
            <CardContent>
              {metrics ? (
                <div className="space-y-6">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <p className="text-sm font-medium">Execution Statistics</p>
                      <div className="space-y-1 text-sm">
                        <div className="flex justify-between">
                          <span>Total Executions:</span>
                          <span>{metrics.totalExecutions}</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Successful:</span>
                          <span className="text-green-600">{metrics.successfulExecutions}</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Failed:</span>
                          <span className="text-red-600">{metrics.failedExecutions}</span>
                        </div>
                      </div>
                    </div>
                    
                    <div className="space-y-2">
                      <p className="text-sm font-medium">Automation Results</p>
                      <div className="space-y-1 text-sm">
                        <div className="flex justify-between">
                          <span>Handovers Created:</span>
                          <span>{metrics.handoversCreated}</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Projects Created:</span>
                          <span>{metrics.projectsCreated}</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Notifications Sent:</span>
                          <span>{metrics.notificationsSent}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ) : (
                <p className="text-muted-foreground">No metrics available</p>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
