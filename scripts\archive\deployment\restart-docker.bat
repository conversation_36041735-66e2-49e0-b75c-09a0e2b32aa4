@echo off
REM Production-ready Docker startup script for CRM Platform (Windows)
REM Usage: restart-docker.bat [environment] [force-rebuild]
REM Environments: development (default), staging, production

setlocal enabledelayedexpansion

REM Set default values
set ENVIRONMENT=%1
if "%ENVIRONMENT%"=="" set ENVIRONMENT=development

set FORCE_REBUILD=%2
if "%FORCE_REBUILD%"=="" set FORCE_REBUILD=false

echo 🚀 Starting CRM Platform in %ENVIRONMENT% mode...

REM Set compose files based on environment
if "%ENVIRONMENT%"=="development" (
    set COMPOSE_FILES=-f docker-compose.yml -f docker-compose.dev.yml
    echo ✅ Using development configuration
) else if "%ENVIRONMENT%"=="dev" (
    set COMPOSE_FILES=-f docker-compose.yml -f docker-compose.dev.yml
    echo ✅ Using development configuration
) else if "%ENVIRONMENT%"=="staging" (
    set COMPOSE_FILES=-f docker-compose.yml -f docker-compose.staging.yml
    echo ✅ Using staging configuration
) else if "%ENVIRONMENT%"=="production" (
    set COMPOSE_FILES=-f docker-compose.yml -f docker-compose.production.yml
    echo ✅ Using production configuration
) else if "%ENVIRONMENT%"=="prod" (
    set COMPOSE_FILES=-f docker-compose.yml -f docker-compose.production.yml
    echo ✅ Using production configuration
) else (
    echo ❌ Unknown environment: %ENVIRONMENT%
    echo Supported environments: development, staging, production
    pause
    exit /b 1
)

REM Stop existing containers
echo 🛑 Stopping existing containers...
docker compose %COMPOSE_FILES% down --remove-orphans

REM Build and start containers
echo 🔨 Building and starting containers...
if "%FORCE_REBUILD%"=="true" (
    docker compose %COMPOSE_FILES% up -d --build --force-recreate
) else (
    docker compose %COMPOSE_FILES% up -d --build
)

REM Wait for containers to be ready
echo ⏳ Waiting for containers to be ready...
timeout /t 30 /nobreak > nul

REM Check container status
echo 📊 Container status:
docker compose %COMPOSE_FILES% ps

REM Wait for application to be healthy
echo 🔍 Checking application health...
set /a attempts=0
set /a max_attempts=30

:health_check
curl -s http://localhost:3010/api/health >nul 2>&1
if %errorlevel%==0 (
    echo ✅ Application is responding
    goto :health_check_done
)

set /a attempts+=1
if %attempts% geq %max_attempts% (
    echo ⚠️ Application may not be responding yet. Check logs if needed.
    goto :health_check_done
)

echo ⏳ Waiting for application... (%attempts%/%max_attempts%)
timeout /t 2 /nobreak > nul
goto :health_check

:health_check_done

echo.
echo 🎉 CRM Platform startup complete!
echo.
echo 🔗 Service URLs:
echo   Application: http://localhost:3010
echo   pgAdmin:     http://localhost:8082
if "%ENVIRONMENT%"=="development" (
    echo   PostgreSQL:  localhost:5434
    echo   Redis:       localhost:6380
)
echo.
echo 🔑 Default Login Credentials:
echo   CRM App:
echo     Email:    <EMAIL>
echo     Password: Admin123!
echo   pgAdmin:
echo     Email:    <EMAIL>
echo     Password: admin
echo.
echo 💡 Useful commands:
echo   View logs:    docker compose %COMPOSE_FILES% logs -f
echo   Stop:         docker compose %COMPOSE_FILES% down
echo   Restart app:  docker compose %COMPOSE_FILES% restart app
echo   Shell access: docker compose %COMPOSE_FILES% exec app sh
echo   Check auth:   docker compose %COMPOSE_FILES% exec app node scripts/fix-auth-issues.js
echo.

pause
