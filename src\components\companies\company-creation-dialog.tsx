'use client';

import React, { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { motion } from 'framer-motion';
import {
  Building,
  Save,
  X,
  Loader2,
  Globe,
  MapPin,
  Sparkles,
  Users
} from 'lucide-react';
import {
  Di<PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { SearchableSelect, SearchableSelectOption } from '@/components/ui/searchable-select';
import { cn } from '@/lib/utils';

interface CompanyCreationDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onCompanyCreated: (company: any) => void;
  initialData?: {
    name?: string;
    website?: string;
    address?: string;
    jobTitle?: string; // For context about the contact's role
  };
}

interface CompanyFormData {
  name: string;
  website: string;
  industry: string;
  size: string;
  ownerId: string;
}

interface User {
  id: string;
  firstName: string;
  lastName: string;
  email?: string;
}

export function CompanyCreationDialog({
  open,
  onOpenChange,
  onCompanyCreated,
  initialData
}: CompanyCreationDialogProps) {
  const { data: session } = useSession();

  // Smart industry detection based on job title or company name
  const detectIndustry = (companyName?: string, jobTitle?: string): string => {
    const text = `${companyName || ''} ${jobTitle || ''}`.toLowerCase();

    if (text.includes('real estate') || text.includes('realty') || text.includes('property')) return 'Real Estate';
    if (text.includes('tech') || text.includes('software') || text.includes('developer') || text.includes('engineer')) return 'Technology';
    if (text.includes('health') || text.includes('medical') || text.includes('doctor') || text.includes('clinic')) return 'Healthcare';
    if (text.includes('finance') || text.includes('bank') || text.includes('investment') || text.includes('accounting')) return 'Finance';
    if (text.includes('education') || text.includes('school') || text.includes('university') || text.includes('teacher')) return 'Education';
    if (text.includes('retail') || text.includes('store') || text.includes('shop') || text.includes('sales')) return 'Retail';
    if (text.includes('restaurant') || text.includes('food') || text.includes('catering') || text.includes('chef')) return 'Food & Beverage';
    if (text.includes('construction') || text.includes('contractor') || text.includes('builder')) return 'Construction';
    if (text.includes('marketing') || text.includes('advertising') || text.includes('agency')) return 'Marketing & Advertising';
    if (text.includes('legal') || text.includes('law') || text.includes('attorney') || text.includes('lawyer')) return 'Legal';

    return '';
  };

  const [formData, setFormData] = useState<CompanyFormData>({
    name: initialData?.name || '',
    website: initialData?.website || '',
    industry: detectIndustry(initialData?.name, initialData?.jobTitle),
    size: '',
    ownerId: session?.user?.id || ''
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [users, setUsers] = useState<User[]>([]);

  // Fetch users for owner selection
  useEffect(() => {
    const fetchUsers = async () => {
      try {
        const usersRes = await fetch('/api/users?limit=100');
        const usersData = await usersRes.json();

        if (usersData.success) {
          setUsers(usersData.data || []);
        }
      } catch (err) {
        console.error('Failed to fetch users:', err);
      }
    };

    if (open) {
      fetchUsers();
    }
  }, [open]);

  const handleInputChange = (field: keyof CompanyFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Company name is required';
    }

    if (formData.website && formData.website.trim() && !/^https?:\/\/.+\..+/.test(formData.website.trim())) {
      newErrors.website = 'Please enter a valid website URL (e.g., https://example.com)';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    try {
      const response = await fetch('/api/companies', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...formData,
          ownerId: formData.ownerId || undefined,
        }),
      });

      const data = await response.json();

      if (data.success) {
        // Fix: Pass the actual company object, not the wrapper
        onCompanyCreated(data.data.company);
        onOpenChange(false);
        // Reset form
        setFormData({
          name: '',
          website: '',
          industry: '',
          size: '',
          ownerId: session?.user?.id || ''
        });
        setErrors({});
      } else {
        throw new Error(data.error || 'Failed to create company');
      }
    } catch (error) {
      console.error('Company creation error:', error);
      setErrors({ submit: error instanceof Error ? error.message : 'Failed to create company' });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    onOpenChange(false);
    // Reset form with smart defaults
    setFormData({
      name: initialData?.name || '',
      website: initialData?.website || '',
      industry: detectIndustry(initialData?.name, initialData?.jobTitle),
      size: '',
      ownerId: session?.user?.id || ''
    });
    setErrors({});
  };

  // Helper functions for dropdown options
  const industryOptions = [
    { value: '', label: 'Select an industry (optional)' },
    { value: 'Technology', label: 'Technology' },
    { value: 'Healthcare', label: 'Healthcare' },
    { value: 'Finance', label: 'Finance' },
    { value: 'Manufacturing', label: 'Manufacturing' },
    { value: 'Retail', label: 'Retail' },
    { value: 'Education', label: 'Education' },
    { value: 'Real Estate', label: 'Real Estate' },
    { value: 'Government', label: 'Government' },
    { value: 'Other', label: 'Other' }
  ];

  const sizeOptions = [
    { value: '', label: 'Select company size (optional)' },
    { value: '1-10 employees', label: '1-10 employees' },
    { value: '11-50 employees', label: '11-50 employees' },
    { value: '51-200 employees', label: '51-200 employees' },
    { value: '201-500 employees', label: '201-500 employees' },
    { value: '501-1000 employees', label: '501-1000 employees' },
    { value: '1000+ employees', label: '1000+ employees' }
  ];

  const getIndustryOptions = (): SearchableSelectOption[] => {
    return industryOptions.map(industry => ({
      value: industry.value,
      label: industry.label,
      description: industry.value ? `${industry.label} industry sector` : 'Choose the primary industry'
    }));
  };

  const getSizeOptions = (): SearchableSelectOption[] => {
    return sizeOptions.map(size => ({
      value: size.value,
      label: size.label,
      description: size.value ? `Company with ${size.label.toLowerCase()}` : 'Choose the company size range'
    }));
  };

  const getUserOptions = (): SearchableSelectOption[] => {
    const options: SearchableSelectOption[] = [
      { value: '', label: 'Select an owner (optional)', description: 'Assign a team member to manage this company' }
    ];

    users.forEach(user => {
      options.push({
        value: user.id,
        label: `${user.firstName} ${user.lastName}`,
        description: user.email || undefined
      });
    });

    return options;
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Building className="w-5 h-5" />
            Create New Company
          </DialogTitle>
          <DialogDescription>
            Add a new company to your CRM. All fields except company name are optional.
          </DialogDescription>
        </DialogHeader>

        <motion.form
          onSubmit={handleSubmit}
          className="space-y-6"
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.2 }}
        >
          {/* Basic Information */}
          <div className="space-y-4">
            <h3 className="text-sm font-medium text-muted-foreground">Basic Information</h3>

            {/* Company Name */}
            <div className="space-y-2">
              <Label htmlFor="name" className="text-sm font-medium">
                Company Name *
              </Label>
              <Input
                id="name"
                name="name"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                placeholder="Enter company name..."
                required
                disabled={isSubmitting}
                className="w-full"
              />
              {errors.name && (
                <p className="text-sm text-destructive">{errors.name}</p>
              )}
            </div>

            {/* Website */}
            <div className="space-y-2">
              <Label htmlFor="website" className="text-sm font-medium">
                Website
              </Label>
              <Input
                id="website"
                name="website"
                type="url"
                value={formData.website}
                onChange={(e) => handleInputChange('website', e.target.value)}
                placeholder="https://example.com"
                disabled={isSubmitting}
                className="w-full"
              />
              {errors.website && (
                <p className="text-sm text-destructive">{errors.website}</p>
              )}
              <p className="text-xs text-muted-foreground">
                Include the full URL with https://
              </p>
            </div>
          </div>

          {/* Company Details */}
          <div className="space-y-4">
            <h3 className="text-sm font-medium text-muted-foreground">Company Details</h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Industry */}
              <div className="space-y-2">
                <Label htmlFor="industry" className="text-sm font-medium flex items-center gap-2">
                  Industry
                  {formData.industry && detectIndustry(initialData?.name, initialData?.jobTitle) === formData.industry && (
                    <span className="inline-flex items-center gap-1 px-2 py-1 text-xs bg-green-100 text-green-700 rounded-full">
                      <Sparkles className="w-3 h-3" />
                      Auto-detected
                    </span>
                  )}
                </Label>
                <SearchableSelect
                  options={getIndustryOptions()}
                  value={formData.industry}
                  onValueChange={(value) => handleInputChange('industry', value)}
                  placeholder="Select industry (optional)"
                  searchPlaceholder="Search industries..."
                  emptyMessage="No industries found"
                  allowClear={true}
                  clearLabel="Clear industry"
                  disabled={isSubmitting}
                />
                {formData.industry && detectIndustry(initialData?.name, initialData?.jobTitle) === formData.industry && (
                  <p className="text-xs text-green-600">Auto-detected from business card</p>
                )}
              </div>

              {/* Company Size */}
              <div className="space-y-2">
                <Label htmlFor="size" className="text-sm font-medium">
                  Company Size
                </Label>
                <SearchableSelect
                  options={getSizeOptions()}
                  value={formData.size}
                  onValueChange={(value) => handleInputChange('size', value)}
                  placeholder="Select company size (optional)"
                  searchPlaceholder="Search company sizes..."
                  emptyMessage="No size options found"
                  allowClear={true}
                  clearLabel="Clear company size"
                  disabled={isSubmitting}
                />
              </div>
            </div>
          </div>

          {/* Organization & Assignment */}
          <div className="space-y-4">
            <h3 className="text-sm font-medium text-muted-foreground flex items-center gap-2">
              <Users className="w-4 h-4" />
              Organization & Assignment
            </h3>

            {/* Owner */}
            <div className="space-y-2">
              <Label htmlFor="ownerId" className="text-sm font-medium">
                Owner
              </Label>
              <SearchableSelect
                options={getUserOptions()}
                value={formData.ownerId}
                onValueChange={(value) => handleInputChange('ownerId', value)}
                placeholder="Select an owner (optional)"
                searchPlaceholder="Search users..."
                emptyMessage="No users found"
                allowClear={true}
                clearLabel="Clear owner"
                disabled={isSubmitting}
              />
              <p className="text-xs text-muted-foreground">
                Assign a team member to manage this company
              </p>
            </div>
          </div>

          {/* Additional Info from Business Card */}
          {(initialData?.address || initialData?.jobTitle) && (
            <div className="p-3 bg-blue-50 rounded-lg border border-blue-200">
              <div className="flex items-start gap-2">
                <MapPin className="w-4 h-4 text-blue-600 mt-0.5" />
                <div className="space-y-2">
                  <p className="text-sm font-medium text-blue-900">
                    Additional Information from Business Card:
                  </p>

                  {initialData?.address && (
                    <div>
                      <p className="text-xs font-medium text-blue-800">Address:</p>
                      <p className="text-sm text-blue-700">{initialData.address}</p>
                    </div>
                  )}

                  {initialData?.jobTitle && (
                    <div>
                      <p className="text-xs font-medium text-blue-800">Contact's Role:</p>
                      <p className="text-sm text-blue-700">{initialData.jobTitle}</p>
                    </div>
                  )}

                  <p className="text-xs text-blue-600 mt-2">
                    Note: Address field will be added to companies in a future update.
                    The contact's role helps provide context about your relationship with this company.
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Submit Error */}
          {errors.submit && (
            <div className="p-3 bg-red-50 rounded-lg border border-red-200">
              <p className="text-sm text-red-700">{errors.submit}</p>
            </div>
          )}

          {/* Form Actions */}
          <div className="flex gap-3 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={handleCancel}
              disabled={isSubmitting}
              className="flex-1"
            >
              <X className="w-4 h-4 mr-2" />
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting}
              className="flex-1"
            >
              {isSubmitting && <Loader2 className="w-4 h-4 mr-2 animate-spin" />}
              {!isSubmitting && <Save className="w-4 h-4 mr-2" />}
              Create Company
            </Button>
          </div>
        </motion.form>
      </DialogContent>
    </Dialog>
  );
}
