'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { SearchableSelect, SearchableSelectOption } from '@/components/ui/searchable-select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, AlertTriangle, ExternalLink } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { prepareProjectDataForAPI } from '@/lib/date-conversion';
import Link from 'next/link';

const projectFormSchema = z.object({
  name: z.string().min(1, 'Project name is required').max(255),
  description: z.string().optional(),
  status: z.enum(['planning', 'in_progress', 'on_hold', 'completed', 'cancelled']).default('planning'),
  priority: z.enum(['low', 'medium', 'high', 'urgent']).default('medium'),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  budget: z.number().positive().optional(),
  managerId: z.string().optional(),
  estimatedHours: z.number().positive().optional(),
  clientPortalEnabled: z.boolean().default(false),
  integrationProvider: z.string().optional(),
  opportunityId: z.string().optional(),
});

type ProjectFormData = z.infer<typeof projectFormSchema>;

interface ProjectFormProps {
  initialData?: Partial<ProjectFormData>;
  onSubmit: (data: ProjectFormData) => Promise<void>;
  onCancel?: () => void;
  isLoading?: boolean;
  submitLabel?: string;
}

interface User {
  id: string;
  firstName?: string;
  lastName?: string;
  email: string;
  avatarUrl?: string;
  status: string;
  tenantUser?: {
    id: string;
    role: string;
    status: string;
    joinedAt: Date;
    teamId?: string;
    department?: string;
    jobTitle?: string;
    team?: {
      id: string;
      name: string;
      color?: string;
    };
  };
}

interface Opportunity {
  id: string;
  title: string;
  value?: number;
  stage: string;
  contact?: {
    firstName: string;
    lastName: string;
    email?: string;
  };
  company?: {
    name: string;
  };
}

interface ExistingProject {
  id: string;
  name: string;
  status: string;
  createdAt: string;
}

export function ProjectForm({
  initialData,
  onSubmit,
  onCancel,
  isLoading = false,
  submitLabel = 'Create Project'
}: ProjectFormProps) {
  const [users, setUsers] = useState<User[]>([]);
  const [opportunities, setOpportunities] = useState<Opportunity[]>([]);
  const [loadingUsers, setLoadingUsers] = useState(false);
  const [loadingOpportunities, setLoadingOpportunities] = useState(false);
  const [existingProject, setExistingProject] = useState<ExistingProject | null>(null);
  const [checkingExistingProject, setCheckingExistingProject] = useState(false);
  const { toast } = useToast();

  const form = useForm<ProjectFormData>({
    resolver: zodResolver(projectFormSchema),
    defaultValues: {
      name: '',
      description: '',
      status: 'planning',
      priority: 'medium',
      clientPortalEnabled: false,
      ...initialData,
    },
  });

  useEffect(() => {
    fetchUsers();
    fetchOpportunities();
  }, []);

  // Watch for opportunity changes and check for existing projects
  useEffect(() => {
    const opportunityId = form.watch('opportunityId');
    if (opportunityId) {
      checkExistingProject(opportunityId);
    } else {
      setExistingProject(null);
    }
  }, [form.watch('opportunityId')]);

  const fetchUsers = async () => {
    try {
      setLoadingUsers(true);
      const response = await fetch('/api/users');
      if (response.ok) {
        const data = await response.json();
        setUsers(data.data || []);
      }
    } catch (error) {
      console.error('Error fetching users:', error);
      toast({
        title: 'Error',
        description: 'Failed to load users',
        variant: 'destructive',
      });
    } finally {
      setLoadingUsers(false);
    }
  };

  const fetchOpportunities = async () => {
    try {
      setLoadingOpportunities(true);
      const response = await fetch('/api/opportunities');
      if (response.ok) {
        const data = await response.json();
        setOpportunities(data.opportunities || []);
      }
    } catch (error) {
      console.error('Error fetching opportunities:', error);
      toast({
        title: 'Error',
        description: 'Failed to load opportunities',
        variant: 'destructive',
      });
    } finally {
      setLoadingOpportunities(false);
    }
  };

  const checkExistingProject = async (opportunityId: string) => {
    if (!opportunityId) {
      setExistingProject(null);
      return;
    }

    try {
      setCheckingExistingProject(true);
      const response = await fetch(`/api/projects?opportunityId=${opportunityId}&limit=1`);
      if (response.ok) {
        const data = await response.json();
        if (data.success && data.data.projects && data.data.projects.length > 0) {
          setExistingProject(data.data.projects[0]);
        } else {
          setExistingProject(null);
        }
      }
    } catch (error) {
      console.error('Error checking existing project:', error);
      // Don't show error toast for this check as it's not critical
    } finally {
      setCheckingExistingProject(false);
    }
  };

  // Helper functions for searchable select options
  const getOpportunityOptions = (): SearchableSelectOption[] => {
    return opportunities.map(opportunity => ({
      value: opportunity.id,
      label: opportunity.title,
      description: `${opportunity.stage}${opportunity.value ? ` • $${opportunity.value.toLocaleString()}` : ''}${opportunity.company?.name ? ` • ${opportunity.company.name}` : ''}`,
    }));
  };

  const getUserOptions = (): SearchableSelectOption[] => {
    return users.map(user => ({
      value: user.id,
      label: `${user.firstName || ''} ${user.lastName || ''}`.trim() || user.email,
      description: `${user.email}${user.tenantUser?.role ? ` • ${user.tenantUser.role}` : ''}${user.tenantUser?.department ? ` • ${user.tenantUser.department}` : ''}`,
    }));
  };

  const handleSubmit = async (data: ProjectFormData) => {
    try {
      // Ensure dates are properly formatted as strings for the API
      const apiData = prepareProjectDataForAPI(data);
      await onSubmit(apiData);
      toast({
        title: 'Success',
        description: `Project ${initialData ? 'updated' : 'created'} successfully`,
      });
    } catch (error) {
      console.error('Error submitting project:', error);
      toast({
        title: 'Error',
        description: `Failed to ${initialData ? 'update' : 'create'} project`,
        variant: 'destructive',
      });
    }
  };

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader className="pb-8">
        <CardTitle className="text-2xl font-semibold">{initialData ? 'Edit Project' : 'Create New Project'}</CardTitle>
        <CardDescription className="text-base mt-2">
          {initialData ? 'Update project details' : 'Fill in the details to create a new project'}
        </CardDescription>
      </CardHeader>
      <CardContent className="px-8 pb-8">
        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-8">
          {/* Basic Information */}
          <div className="space-y-6">
            <div className="space-y-2">
              <h3 className="text-lg font-semibold text-gray-900">Basic Information</h3>
              <p className="text-sm text-gray-600">Enter the core details for your project</p>
            </div>

            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="name" className="text-sm font-medium">Project Name *</Label>
                <Input
                  id="name"
                  {...form.register('name')}
                  placeholder="Enter project name"
                  className={`h-10 ${form.formState.errors.name ? 'border-red-500' : ''}`}
                />
                {form.formState.errors.name && (
                  <p className="text-sm text-red-500 mt-1">{form.formState.errors.name.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="description" className="text-sm font-medium">Description</Label>
                <Textarea
                  id="description"
                  {...form.register('description')}
                  placeholder="Enter project description"
                  rows={3}
                  className="resize-none"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="opportunityId" className="text-sm font-medium">Related Opportunity</Label>
                <SearchableSelect
                  options={getOpportunityOptions()}
                  value={form.watch('opportunityId') || ''}
                  onValueChange={(value) => form.setValue('opportunityId', value || undefined)}
                  placeholder={loadingOpportunities ? 'Loading opportunities...' : 'Select an opportunity (optional)'}
                  searchPlaceholder="Search opportunities..."
                  emptyMessage={opportunities.length === 0 ? 'No opportunities available' : 'No opportunities found'}
                  allowClear={true}
                  clearLabel="Clear opportunity"
                  disabled={loadingOpportunities}
                />
                {opportunities.length === 0 && !loadingOpportunities && (
                  <p className="text-xs text-muted-foreground mt-1">
                    No opportunities found. You can create the project without linking to an opportunity.
                  </p>
                )}

                {/* Show existing project warning */}
                {checkingExistingProject && (
                  <Alert>
                    <Loader2 className="h-4 w-4 animate-spin" />
                    <AlertDescription>
                      Checking for existing projects...
                    </AlertDescription>
                  </Alert>
                )}

                {existingProject && !checkingExistingProject && (
                  <Alert variant="destructive">
                    <AlertTriangle className="h-4 w-4" />
                    <AlertDescription>
                      <div className="space-y-2">
                        <p>
                          <strong>Project already exists for this opportunity!</strong>
                        </p>
                        <p className="text-sm">
                          Project "{existingProject.name}" (Status: {existingProject.status}) was created on{' '}
                          {new Date(existingProject.createdAt).toLocaleDateString()}.
                        </p>
                        <div className="flex items-center gap-2 mt-2">
                          <Link
                            href={`/projects/${existingProject.id}`}
                            className="inline-flex items-center gap-1 text-sm font-medium text-blue-600 hover:text-blue-800 underline"
                          >
                            View existing project
                            <ExternalLink className="h-3 w-3" />
                          </Link>
                        </div>
                        <p className="text-xs text-muted-foreground mt-2">
                          You can still create a new project, but consider if it's necessary.
                        </p>
                      </div>
                    </AlertDescription>
                  </Alert>
                )}
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="status" className="text-sm font-medium">Status</Label>
                <Select
                  value={form.watch('status')}
                  onValueChange={(value) => form.setValue('status', value as any)}
                >
                  <SelectTrigger className="h-10">
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="planning">Planning</SelectItem>
                    <SelectItem value="in_progress">In Progress</SelectItem>
                    <SelectItem value="on_hold">On Hold</SelectItem>
                    <SelectItem value="completed">Completed</SelectItem>
                    <SelectItem value="cancelled">Cancelled</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="priority" className="text-sm font-medium">Priority</Label>
                <Select
                  value={form.watch('priority')}
                  onValueChange={(value) => form.setValue('priority', value as any)}
                >
                  <SelectTrigger className="h-10">
                    <SelectValue placeholder="Select priority" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="low">Low</SelectItem>
                    <SelectItem value="medium">Medium</SelectItem>
                    <SelectItem value="high">High</SelectItem>
                    <SelectItem value="urgent">Urgent</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>

          {/* Timeline */}
          <div className="space-y-6">
            <div className="space-y-2">
              <h3 className="text-lg font-semibold text-gray-900">Timeline</h3>
              <p className="text-sm text-gray-600">Set the project start and end dates</p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="startDate" className="text-sm font-medium">Start Date</Label>
                <Input
                  id="startDate"
                  type="date"
                  {...form.register('startDate')}
                  placeholder="Select start date"
                  className="h-10"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="endDate" className="text-sm font-medium">End Date</Label>
                <Input
                  id="endDate"
                  type="date"
                  {...form.register('endDate')}
                  placeholder="Select end date"
                  className="h-10"
                />
              </div>
            </div>
          </div>

          {/* Budget and Resources */}
          <div className="space-y-6">
            <div className="space-y-2">
              <h3 className="text-lg font-semibold text-gray-900">Budget & Resources</h3>
              <p className="text-sm text-gray-600">Define budget constraints and assign team members</p>
            </div>

            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="budget" className="text-sm font-medium">Budget ($)</Label>
                  <Input
                    id="budget"
                    type="number"
                    step="0.01"
                    {...form.register('budget', { valueAsNumber: true })}
                    placeholder="0.00"
                    className="h-10"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="estimatedHours" className="text-sm font-medium">Estimated Hours</Label>
                  <Input
                    id="estimatedHours"
                    type="number"
                    {...form.register('estimatedHours', { valueAsNumber: true })}
                    placeholder="0"
                    className="h-10"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="managerId" className="text-sm font-medium">Project Manager</Label>
                <SearchableSelect
                  options={getUserOptions()}
                  value={form.watch('managerId') || ''}
                  onValueChange={(value) => form.setValue('managerId', value || undefined)}
                  placeholder={loadingUsers ? 'Loading users...' : 'Select project manager (optional)'}
                  searchPlaceholder="Search users..."
                  emptyMessage={users.length === 0 ? 'No users available' : 'No users found'}
                  allowClear={true}
                  clearLabel="Clear manager"
                  disabled={loadingUsers}
                />
                {users.length === 0 && !loadingUsers && (
                  <p className="text-xs text-muted-foreground mt-1">
                    No users found. You can create the project without assigning a manager.
                  </p>
                )}
              </div>
            </div>
          </div>

          {/* Settings */}
          <div className="space-y-6">
            <div className="space-y-2">
              <h3 className="text-lg font-semibold text-gray-900">Settings</h3>
              <p className="text-sm text-gray-600">Configure project settings and integrations</p>
            </div>

            <div className="space-y-6">
              <div className="flex items-center space-x-3">
                <Switch
                  id="clientPortalEnabled"
                  checked={form.watch('clientPortalEnabled')}
                  onCheckedChange={(checked) => form.setValue('clientPortalEnabled', checked)}
                />
                <div className="space-y-1">
                  <Label htmlFor="clientPortalEnabled" className="text-sm font-medium">Enable Client Portal</Label>
                  <p className="text-xs text-gray-500">Allow clients to view project progress and updates</p>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="integrationProvider" className="text-sm font-medium">Integration Provider</Label>
                <Select
                  value={form.watch('integrationProvider') || 'none'}
                  onValueChange={(value) => form.setValue('integrationProvider', value === 'none' ? undefined : value)}
                >
                  <SelectTrigger className="h-10">
                    <SelectValue placeholder="Select integration provider" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="none">No integration</SelectItem>
                    <SelectItem value="jira">Jira</SelectItem>
                    <SelectItem value="asana">Asana</SelectItem>
                    <SelectItem value="trello">Trello</SelectItem>
                    <SelectItem value="monday">Monday.com</SelectItem>
                    <SelectItem value="clickup">ClickUp</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>

          {/* Actions */}
          <div className="flex justify-end space-x-4 pt-8 border-t border-gray-200">
            {onCancel && (
              <Button type="button" variant="outline" onClick={onCancel} className="px-6 py-2">
                Cancel
              </Button>
            )}
            <Button type="submit" disabled={isLoading} className="px-6 py-2">
              {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              {submitLabel}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}
