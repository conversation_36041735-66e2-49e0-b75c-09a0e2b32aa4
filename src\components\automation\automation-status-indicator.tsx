'use client';

import React, { useState, useEffect } from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
  BoltIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  ClockIcon,
  EyeIcon,
  PlayIcon
} from '@heroicons/react/24/outline';
import { useToast } from '@/hooks/use-toast';
import Link from 'next/link';

interface AutomationExecution {
  id: string;
  triggerId: string;
  entityType: string;
  entityId: string;
  status: 'pending' | 'completed' | 'failed';
  executedActions: Array<{
    actionType: string;
    status: string;
    result?: Record<string, unknown>;
    error?: string;
  }>;
  startedAt: string;
  completedAt?: string;
  trigger: {
    name: string;
    description?: string;
  };
}

interface AutomationStatusIndicatorProps {
  opportunityId?: string;
  handoverId?: string;
  projectId?: string;
  className?: string;
}

export function AutomationStatusIndicator({
  opportunityId,
  handoverId,
  projectId,
  className = ''
}: AutomationStatusIndicatorProps) {
  const [executions, setExecutions] = useState<AutomationExecution[]>([]);
  const [loading, setLoading] = useState(false);
  const [showDetails, setShowDetails] = useState(false);
  const [setupRequired, setSetupRequired] = useState(false);
  const [testing, setTesting] = useState(false);
  const { toast } = useToast();

  useEffect(() => {
    if (opportunityId || handoverId || projectId) {
      fetchAutomationStatus();
    }
  }, [opportunityId, handoverId, projectId]);

  const fetchAutomationStatus = async () => {
    try {
      setLoading(true);

      const entityType = opportunityId ? 'opportunity' : handoverId ? 'handover' : 'project';
      const entityId = opportunityId || handoverId || projectId;

      const response = await fetch(`/api/automation/executions?entityType=${entityType}&entityId=${entityId}`);

      if (response.ok) {
        const data = await response.json();
        setExecutions(data.data.executions || []);
      } else if (response.status === 503) {
        // Automation tables don't exist yet
        const errorData = await response.json();
        setSetupRequired(true);
        toast({
          title: "Automation Setup Required",
          description: errorData.message || "Automation tables need to be created",
          variant: "destructive",
        });
      } else {
        console.error('Error fetching automation status:', response.statusText);
      }
    } catch (error) {
      console.error('Error fetching automation status:', error);
    } finally {
      setLoading(false);
    }
  };

  const testAutomation = async () => {
    if (!opportunityId) {
      toast({
        title: "Cannot Test Automation",
        description: "This feature is only available for opportunities",
        variant: "destructive",
      });
      return;
    }

    try {
      setTesting(true);

      const response = await fetch('/api/automation/test-trigger', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          opportunityId,
          newStatus: 'Closed Won',
          oldStatus: 'Negotiation'
        })
      });

      const data = await response.json();

      if (response.ok) {
        toast({
          title: "Automation Test Successful",
          description: `Executed ${data.data.triggers.executed} triggers, created ${data.data.executions.length} executions`,
        });

        // Refresh the automation status
        await fetchAutomationStatus();
      } else {
        toast({
          title: "Automation Test Failed",
          description: data.message || "Failed to test automation",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error('Error testing automation:', error);
      toast({
        title: "Test Error",
        description: "Failed to test automation system",
        variant: "destructive",
      });
    } finally {
      setTesting(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircleIcon className="w-4 h-4 text-green-600" />;
      case 'failed':
        return <ExclamationTriangleIcon className="w-4 h-4 text-red-600" />;
      case 'pending':
        return <ClockIcon className="w-4 h-4 text-yellow-600" />;
      default:
        return <BoltIcon className="w-4 h-4 text-gray-600" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'failed':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  if (loading) {
    return (
      <div className={`flex items-center space-x-2 ${className}`}>
        <BoltIcon className="w-4 h-4 text-gray-400 animate-pulse" />
        <span className="text-sm text-gray-500">Checking automation...</span>
      </div>
    );
  }

  if (setupRequired) {
    return (
      <div className={`space-y-2 ${className}`}>
        <div className="flex items-center space-x-2">
          <ExclamationTriangleIcon className="w-4 h-4 text-yellow-600" />
          <span className="text-sm font-medium text-gray-700">Automation Setup Required</span>
        </div>
        <div className="text-xs text-gray-600">
          <p>Process automation is not configured yet.</p>
          <Link href="/admin/automation" className="text-blue-600 hover:text-blue-800 underline">
            Configure automation →
          </Link>
        </div>
      </div>
    );
  }

  if (executions.length === 0) {
    return (
      <div className={`space-y-2 ${className}`}>
        <div className="flex items-center space-x-2">
          <BoltIcon className="w-4 h-4 text-gray-400" />
          <span className="text-sm text-gray-500">No automation activity</span>
        </div>
        <div className="text-xs text-gray-500">
          Automation will appear here when triggered
        </div>
      </div>
    );
  }

  const recentExecution = executions[0];
  const totalExecutions = executions.length;
  const completedExecutions = executions.filter(e => e.status === 'completed').length;
  const failedExecutions = executions.filter(e => e.status === 'failed').length;

  return (
    <div className={`space-y-2 ${className}`}>
      {/* Status Summary */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <BoltIcon className="w-4 h-4 text-blue-600" />
          <span className="text-sm font-medium text-gray-700">Automation</span>
          <Badge variant="outline" className={getStatusColor(recentExecution.status)}>
            {getStatusIcon(recentExecution.status)}
            <span className="ml-1 capitalize">{recentExecution.status}</span>
          </Badge>
        </div>
        
        <div className="flex items-center gap-1">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowDetails(!showDetails)}
            className="text-xs"
          >
            <EyeIcon className="w-3 h-3 mr-1" />
            {showDetails ? 'Hide' : 'Show'} Details
          </Button>

          {opportunityId && (
            <Button
              variant="ghost"
              size="sm"
              onClick={testAutomation}
              disabled={testing}
              className="text-xs"
              title="Test automation with 'Closed Won' status"
            >
              <PlayIcon className="w-3 h-3 mr-1" />
              {testing ? 'Testing...' : 'Test'}
            </Button>
          )}
        </div>
      </div>

      {/* Execution Summary */}
      <div className="flex items-center space-x-4 text-xs text-gray-600">
        <span>{totalExecutions} total</span>
        {completedExecutions > 0 && (
          <span className="text-green-600">{completedExecutions} completed</span>
        )}
        {failedExecutions > 0 && (
          <span className="text-red-600">{failedExecutions} failed</span>
        )}
      </div>

      {/* Detailed View */}
      {showDetails && (
        <Card className="mt-2">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm">Recent Automation Activity</CardTitle>
            <CardDescription className="text-xs">
              Latest automation executions for this record
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-2">
            {executions.slice(0, 3).map((execution) => (
              <div key={execution.id} className="flex items-start justify-between p-2 bg-gray-50 rounded-md">
                <div className="flex-1">
                  <div className="flex items-center space-x-2">
                    {getStatusIcon(execution.status)}
                    <span className="text-sm font-medium">{execution.trigger.name}</span>
                  </div>
                  <p className="text-xs text-gray-600 mt-1">
                    {execution.trigger.description}
                  </p>
                  <div className="flex items-center space-x-2 mt-1">
                    <span className="text-xs text-gray-500">
                      {new Date(execution.startedAt).toLocaleString()}
                    </span>
                    {execution.executedActions.length > 0 && (
                      <Badge variant="outline" className="text-xs">
                        {execution.executedActions.length} actions
                      </Badge>
                    )}
                  </div>
                </div>
              </div>
            ))}
            
            {executions.length > 3 && (
              <div className="text-center pt-2">
                <Link href="/admin/automation">
                  <Button variant="outline" size="sm" className="text-xs">
                    View All Executions
                  </Button>
                </Link>
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
}

export default AutomationStatusIndicator;
