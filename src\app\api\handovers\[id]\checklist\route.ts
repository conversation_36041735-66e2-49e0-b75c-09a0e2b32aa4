import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { handoverChecklistService } from '@/services/handover-checklist';
import { z } from 'zod';

// Request validation schemas
const createChecklistSchema = z.object({
  templateId: z.string().optional(),
});

const createChecklistItemSchema = z.object({
  title: z.string().min(1, 'Title is required').max(255),
  description: z.string().optional(),
  category: z.enum(['general', 'technical', 'legal', 'financial', 'client']).default('general'),
  orderIndex: z.number().int().min(0),
  isRequired: z.boolean().default(true),
  dependsOnIds: z.array(z.string()).default([]),
});

interface RouteParams {
  params: {
    id: string;
  };
}

/**
 * GET /api/handovers/[id]/checklist - Get checklist items for a handover
 */
export async function GET(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get tenantId from user's tenants array (users have one tenant in this system)
    let tenantId: string | null = null;
    if (session.user.tenants && session.user.tenants.length > 0) {
      tenantId = session.user.tenants[0].id;
    }

    if (!tenantId) {
      return NextResponse.json({ error: 'Tenant not found' }, { status: 400 });
    }

    const resolvedParams = await params;
    const items = await handoverChecklistService.getChecklistItems(
      tenantId,
      resolvedParams.id
    );

    return NextResponse.json({ items });
  } catch (error) {
    console.error('Error fetching checklist items:', error);
    return NextResponse.json(
      { error: 'Failed to fetch checklist items' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/handovers/[id]/checklist - Create checklist from template or add individual item
 */
export async function POST(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get tenantId from user's tenants array (users have one tenant in this system)
    let tenantId: string | null = null;
    if (session.user.tenants && session.user.tenants.length > 0) {
      tenantId = session.user.tenants[0].id;
    }

    if (!tenantId) {
      return NextResponse.json({ error: 'Tenant not found' }, { status: 400 });
    }

    const body = await request.json();
    
    // Check if this is a template-based creation or individual item creation
    if ('templateId' in body || Object.keys(body).length === 0) {
      // Create checklist from template
      const validatedData = createChecklistSchema.parse(body);
      
      const resolvedParams = await params;
      const items = await handoverChecklistService.createChecklistFromTemplate(
        tenantId,
        resolvedParams.id,
        validatedData.templateId
      );

      return NextResponse.json({ items }, { status: 201 });
    } else {
      // Create individual checklist item
      const validatedData = createChecklistItemSchema.parse(body);
      
      // This would require adding a method to create individual items
      // For now, return an error suggesting to use template creation
      return NextResponse.json(
        { error: 'Individual item creation not yet implemented. Please use template-based creation.' },
        { status: 501 }
      );
    }
  } catch (error) {
    console.error('Error creating checklist:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request data', details: error.errors },
        { status: 400 }
      );
    }

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to create checklist' },
      { status: 500 }
    );
  }
}
