'use client';

import { useState } from 'react';
import { useSession } from 'next-auth/react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/hooks/use-toast';
import { ChatBubbleLeftRightIcon, PlusIcon } from '@heroicons/react/24/outline';
import { z } from 'zod';

const socialCaptureSchema = z.object({
  platform: z.enum(['twitter', 'linkedin', 'facebook', 'instagram', 'youtube', 'tiktok'], {
    required_error: 'Platform is required'
  }),
  postId: z.string().min(1, 'Post ID is required'),
  authorName: z.string().min(1, 'Author name is required'),
  authorHandle: z.string().optional(),
  content: z.string().min(10, 'Content must be at least 10 characters'),
  url: z.string().url('Valid URL is required').optional().or(z.literal('')),
  likes: z.number().min(0).optional(),
  shares: z.number().min(0).optional(),
  comments: z.number().min(0).optional(),
  views: z.number().min(0).optional()
});

type SocialCaptureFormData = z.infer<typeof socialCaptureSchema>;

interface SocialCaptureFormProps {
  onSuccess?: () => void;
  onCancel?: () => void;
}

const platformOptions = [
  { value: 'twitter', label: 'Twitter/X' },
  { value: 'linkedin', label: 'LinkedIn' },
  { value: 'facebook', label: 'Facebook' },
  { value: 'instagram', label: 'Instagram' },
  { value: 'youtube', label: 'YouTube' },
  { value: 'tiktok', label: 'TikTok' }
];

export function SocialCaptureForm({ onSuccess, onCancel }: SocialCaptureFormProps) {
  const { data: session } = useSession();
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState<SocialCaptureFormData>({
    platform: 'twitter' as const,
    postId: '',
    authorName: '',
    authorHandle: '',
    content: '',
    url: '',
    likes: 0,
    shares: 0,
    comments: 0,
    views: 0
  });
  const [errors, setErrors] = useState<Record<string, string>>({});

  const tenantId = session?.user?.tenants?.[0]?.id;

  const handleInputChange = (field: keyof SocialCaptureFormData, value: string | number) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const validateForm = () => {
    try {
      socialCaptureSchema.parse(formData);
      setErrors({});
      return true;
    } catch (error) {
      if (error instanceof z.ZodError) {
        const newErrors: Record<string, string> = {};
        error.errors.forEach(err => {
          if (err.path[0]) {
            newErrors[err.path[0] as string] = err.message;
          }
        });
        setErrors(newErrors);
      }
      return false;
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    if (!tenantId) {
      toast({
        title: 'Error',
        description: 'No tenant found. Please refresh and try again.',
        variant: 'destructive'
      });
      return;
    }

    setLoading(true);

    try {
      // Prepare the data for the API
      const apiData = {
        platform: formData.platform,
        postId: formData.postId,
        authorName: formData.authorName,
        authorHandle: formData.authorHandle || undefined,
        content: formData.content,
        url: formData.url || undefined,
        createdAt: new Date().toISOString(),
        engagement: {
          likes: formData.likes || 0,
          shares: formData.shares || 0,
          comments: formData.comments || 0,
          views: formData.views || 0
        }
      };

      const response = await fetch('/api/leads/social-capture', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-tenant-id': tenantId
        },
        body: JSON.stringify(apiData)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to capture social media post');
      }

      const result = await response.json();

      toast({
        title: 'Success',
        description: `Social media post captured successfully! ${result.data.lead ? 'Lead created.' : 'Processing in progress.'}`,
        variant: 'default'
      });

      // Reset form
      setFormData({
        platform: 'twitter' as const,
        postId: '',
        authorName: '',
        authorHandle: '',
        content: '',
        url: '',
        likes: 0,
        shares: 0,
        comments: 0,
        views: 0
      });

      onSuccess?.();

    } catch (error) {
      console.error('Error capturing social media post:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to capture social media post',
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <div className="flex items-center space-x-2">
          <ChatBubbleLeftRightIcon className="h-6 w-6 text-primary" />
          <div>
            <CardTitle>Capture Social Media Post</CardTitle>
            <CardDescription>
              Manually add a social media post to the lead capture system for processing
            </CardDescription>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="platform">Platform *</Label>
              <Select
                value={formData.platform}
                onValueChange={(value) => handleInputChange('platform', value)}
              >
                <SelectTrigger className={errors.platform ? 'border-red-500' : ''}>
                  <SelectValue placeholder="Select platform" />
                </SelectTrigger>
                <SelectContent>
                  {platformOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.platform && (
                <p className="text-sm text-red-500">{errors.platform}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="postId">Post ID *</Label>
              <Input
                id="postId"
                placeholder="post_123456"
                value={formData.postId}
                onChange={(e) => handleInputChange('postId', e.target.value)}
                className={errors.postId ? 'border-red-500' : ''}
              />
              {errors.postId && (
                <p className="text-sm text-red-500">{errors.postId}</p>
              )}
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="authorName">Author Name *</Label>
              <Input
                id="authorName"
                placeholder="John Doe"
                value={formData.authorName}
                onChange={(e) => handleInputChange('authorName', e.target.value)}
                className={errors.authorName ? 'border-red-500' : ''}
              />
              {errors.authorName && (
                <p className="text-sm text-red-500">{errors.authorName}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="authorHandle">Author Handle (Optional)</Label>
              <Input
                id="authorHandle"
                placeholder="@johndoe"
                value={formData.authorHandle}
                onChange={(e) => handleInputChange('authorHandle', e.target.value)}
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="content">Post Content *</Label>
            <Textarea
              id="content"
              placeholder="Enter the social media post content here..."
              rows={4}
              value={formData.content}
              onChange={(e) => handleInputChange('content', e.target.value)}
              className={errors.content ? 'border-red-500' : ''}
            />
            {errors.content && (
              <p className="text-sm text-red-500">{errors.content}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="url">Post URL (Optional)</Label>
            <Input
              id="url"
              type="url"
              placeholder="https://twitter.com/user/status/123456"
              value={formData.url}
              onChange={(e) => handleInputChange('url', e.target.value)}
              className={errors.url ? 'border-red-500' : ''}
            />
            {errors.url && (
              <p className="text-sm text-red-500">{errors.url}</p>
            )}
          </div>

          <div className="space-y-3">
            <Label>Engagement Metrics (Optional)</Label>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="space-y-2">
                <Label htmlFor="likes" className="text-sm">Likes</Label>
                <Input
                  id="likes"
                  type="number"
                  min="0"
                  placeholder="0"
                  value={formData.likes || ''}
                  onChange={(e) => handleInputChange('likes', parseInt(e.target.value) || 0)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="shares" className="text-sm">Shares</Label>
                <Input
                  id="shares"
                  type="number"
                  min="0"
                  placeholder="0"
                  value={formData.shares || ''}
                  onChange={(e) => handleInputChange('shares', parseInt(e.target.value) || 0)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="comments" className="text-sm">Comments</Label>
                <Input
                  id="comments"
                  type="number"
                  min="0"
                  placeholder="0"
                  value={formData.comments || ''}
                  onChange={(e) => handleInputChange('comments', parseInt(e.target.value) || 0)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="views" className="text-sm">Views</Label>
                <Input
                  id="views"
                  type="number"
                  min="0"
                  placeholder="0"
                  value={formData.views || ''}
                  onChange={(e) => handleInputChange('views', parseInt(e.target.value) || 0)}
                />
              </div>
            </div>
          </div>

          <div className="flex justify-end space-x-3">
            {onCancel && (
              <Button
                type="button"
                variant="outline"
                onClick={onCancel}
                disabled={loading}
              >
                Cancel
              </Button>
            )}
            <Button
              type="submit"
              disabled={loading}
              className="min-w-[120px]"
            >
              {loading ? (
                <div className="flex items-center space-x-2">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  <span>Processing...</span>
                </div>
              ) : (
                <div className="flex items-center space-x-2">
                  <PlusIcon className="h-4 w-4" />
                  <span>Capture Post</span>
                </div>
              )}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}
