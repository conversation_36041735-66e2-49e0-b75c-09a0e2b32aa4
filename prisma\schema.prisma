generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Account {
  id                String  @id @default(cuid())
  userId            String  @map("user_id")
  type              String
  provider          String
  providerAccountId String  @map("provider_account_id")
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?
  session_state     String?
  user              User    @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@map("accounts")
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique @map("session_token")
  userId       String   @map("user_id")
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
  @@map("verificationtokens")
}

model Tenant {
  id                      String                      @id @default(cuid())
  name                    String
  slug                    String                      @unique
  status                  String                      @default("active")
  createdAt               DateTime                    @default(now()) @map("created_at")
  updatedAt               DateTime                    @updatedAt @map("updated_at")
  settings                Json                        @default("{}")
  branding                Json                        @default("{}")
  activities              Activity[]
  aiInsights              AiInsight[]
  auditLogs               AuditLog[]
  automationExecutions    AutomationExecution[]
  automationTriggers      AutomationTrigger[]
  billingEvents           BillingEvent[]
  calendarEventAttendees  CalendarEventAttendee[]
  calendarEvents          CalendarEvent[]
  chatChannelMembers      ChatChannelMember[]
  chatChannels            ChatChannel[]
  chatMessages            ChatMessage[]
  companies               Company[]
  contacts                Contact[]
  crmIntegrations         CrmIntegration[]
  crmMappings             CrmMapping[]
  crmSyncLogs             CrmSyncLog[]
  documents               Document[]
  documentTemplates       DocumentTemplate[]
  emailLeadCaptures       EmailLeadCapture[]
  emailLogs               EmailLog[]
  emailTemplates          EmailTemplate[]
  handoverChecklistItems  HandoverChecklistItem[]
  handoverTemplates       HandoverChecklistTemplate[]
  handoverDocuments       HandoverDocument[]
  handoverNotifications   HandoverNotification[]
  handoverQAMessages      HandoverQAMessage[]
  handoverQAThreads       HandoverQAThread[]
  impersonationSessions   ImpersonationSession[]      @relation("ImpersonationTenant")
  leadConversionHistory   LeadConversionHistory[]
  leadNotifications       LeadNotification[]
  leadSources             LeadSource[]
  leads                   Lead[]
  notifications           Notification[]
  nurturingCampaigns      NurturingCampaign[]
  opportunities           Opportunity[]
  opportunityStageHistory OpportunityStageHistory[]
  permissions             Permission[]
  projectClientAccess     ProjectClientAccess[]
  projectHandovers        ProjectHandover[]
  projectMilestones       ProjectMilestone[]
  projectResources        ProjectResource[]
  projectTasks            ProjectTask[]
  projects                Project[]
  proposalCharts          ProposalChart[]
  proposalContentSections ProposalContentSection[]
  proposalSections        ProposalSection[]
  proposals               Proposal[]
  rolePermissions         RolePermission[]
  roles                   Role[]
  salesStages             SalesStage[]
  scheduledMeetings       ScheduledMeeting[]
  socialMediaCaptures     SocialMediaCapture[]
  subscriptions           Subscription[]
  teams                   Team[]
  tenantSettings          TenantSetting[]
  tenantUsers             TenantUser[]
  usageMetrics            UsageMetric[]
  userRoles               UserRole[]

  @@index([status, createdAt], map: "idx_tenants_status_created")
  @@map("tenants")
}

model User {
  id                           String                       @id @default(cuid())
  email                        String                       @unique
  passwordHash                 String?                      @map("password_hash")
  firstName                    String?                      @map("first_name")
  lastName                     String?                      @map("last_name")
  phone                        String?
  avatarUrl                    String?                      @map("avatar_url")
  locale                       String                       @default("en")
  timezone                     String                       @default("UTC")
  emailVerified                Boolean                      @default(false) @map("email_verified")
  isPlatformAdmin              Boolean                      @default(false) @map("is_platform_admin")
  status                       String                       @default("active")
  lastLogin                    DateTime?                    @map("last_login")
  resetToken                   String?                      @map("reset_token")
  resetTokenExpiry             DateTime?                    @map("reset_token_expiry")
  createdAt                    DateTime                     @default(now()) @map("created_at")
  updatedAt                    DateTime                     @updatedAt @map("updated_at")
  accounts                     Account[]
  createdActivities            Activity[]                   @relation("ActivityCreatedBy")
  ownedActivities              Activity[]                   @relation("ActivityOwner")
  auditLogs                    AuditLog[]
  createdAutomationTriggers    AutomationTrigger[]          @relation("AutomationTriggerCreatedBy")
  calendarEventAttendances     CalendarEventAttendee[]
  createdCalendarEvents        CalendarEvent[]              @relation("CalendarEventCreatedBy")
  chatChannelMemberships       ChatChannelMember[]
  createdChatChannels          ChatChannel[]                @relation("ChatChannelCreatedBy")
  chatMessages                 ChatMessage[]
  createdCompanies             Company[]                    @relation("CompanyCreatedBy")
  ownedCompanies               Company[]                    @relation("CompanyOwner")
  createdContacts              Contact[]                    @relation("ContactCreatedBy")
  ownedContacts                Contact[]                    @relation("ContactOwner")
  createdCrmIntegrations       CrmIntegration[]             @relation("CrmIntegrationCreatedBy")
  updatedCrmIntegrations       CrmIntegration[]             @relation("CrmIntegrationUpdatedBy")
  uploadedDocuments            Document[]                   @relation("DocumentUploadedBy")
  createdDocumentTemplates     DocumentTemplate[]           @relation("DocumentTemplateCreatedBy")
  sentEmails                   EmailLog[]                   @relation("EmailSentBy")
  createdEmailTemplates        EmailTemplate[]              @relation("EmailTemplateCreatedBy")
  completedChecklistItems      HandoverChecklistItem[]      @relation("ChecklistItemCompletedBy")
  createdHandoverTemplates     HandoverChecklistTemplate[]  @relation("HandoverTemplateCreatedBy")
  approvedHandoverDocs         HandoverDocument[]           @relation("HandoverDocumentApprovedBy")
  handoverNotifications        HandoverNotification[]       @relation("HandoverNotificationUser")
  authoredQAMessages           HandoverQAMessage[]          @relation("QAMessageAuthor")
  assignedQAThreads            HandoverQAThread[]           @relation("QAThreadAssignedTo")
  createdQAThreads             HandoverQAThread[]           @relation("QAThreadCreatedBy")
  impersonationSessionsAsAdmin ImpersonationSession[]       @relation("ImpersonationSuperAdmin")
  impersonationSessionsAsUser  ImpersonationSession[]       @relation("ImpersonationUser")
  leadConversions              LeadConversionHistory[]      @relation("ConversionPerformedBy")
  leadNotifications            LeadNotification[]           @relation("LeadNotificationUser")
  createdLeadSources           LeadSource[]                 @relation("LeadSourceCreatedBy")
  convertedLeads               Lead[]                       @relation("LeadConvertedBy")
  createdLeads                 Lead[]                       @relation("LeadCreatedBy")
  ownedLeads                   Lead[]                       @relation("LeadOwner")
  notifications                Notification[]
  createdNurturingCampaigns    NurturingCampaign[]          @relation("NurturingCampaignCreatedBy")
  createdOpportunities         Opportunity[]                @relation("OpportunityCreatedBy")
  ownedOpportunities           Opportunity[]                @relation("OpportunityOwner")
  opportunityStageChanges      OpportunityStageHistory[]    @relation("OpportunityStageChangedBy")
  createdClientAccess          ProjectClientAccess[]        @relation("ClientAccessCreatedBy")
  createdHandovers             ProjectHandover[]            @relation("HandoverCreatedBy")
  deliveryManagerHandovers     ProjectHandover[]            @relation("HandoverDeliveryManager")
  salesRepHandovers            ProjectHandover[]            @relation("HandoverSalesRep")
  createdMilestones            ProjectMilestone[]           @relation("MilestoneCreatedBy")
  createdProjectResources      ProjectResource[]            @relation("ProjectResourceCreatedBy")
  projectResources             ProjectResource[]            @relation("ProjectResourceUser")
  assignedTasks                ProjectTask[]                @relation("TaskAssignee")
  createdTasks                 ProjectTask[]                @relation("TaskCreatedBy")
  createdProjects              Project[]                    @relation("ProjectCreatedBy")
  managedProjects              Project[]                    @relation("ProjectManager")
  createdProposalCharts        ProposalChart[]              @relation("ProposalChartCreatedBy")
  createdProposalContentSections ProposalContentSection[]   @relation("ProposalContentSectionCreatedBy")
  createdProposalSections      ProposalSection[]            @relation("ProposalSectionCreatedBy")
  createdProposals             Proposal[]                   @relation("ProposalCreatedBy")
  grantedPermissions           RolePermission[]             @relation("PermissionGrantedBy")
  createdRoles                 Role[]                       @relation("RoleCreatedBy")
  scheduledMeetingsAssigned    ScheduledMeeting[]           @relation("ScheduledMeetingAssignedTo")
  scheduledMeetingsCreated     ScheduledMeeting[]           @relation("ScheduledMeetingCreatedBy")
  sessions                     Session[]
  createdTeams                 Team[]                       @relation("TeamCreatedBy")
  managedTeams                 Team[]                       @relation("TeamManager")
  updatedSettings              TenantSetting[]              @relation("SettingUpdatedBy")
  tenantUsers                  TenantUser[]
  notificationPreferences      UserNotificationPreferences? @relation("UserNotificationPreferences")
  assignedRoles                UserRole[]                   @relation("RoleAssignedBy")
  userRoles                    UserRole[]

  @@map("users")
}

model TenantUser {
  id            String    @id @default(cuid())
  tenantId      String    @map("tenant_id")
  userId        String    @map("user_id")
  isTenantAdmin Boolean   @default(false) @map("is_tenant_admin")
  role          String    @default("user")
  status        String    @default("active")
  joinedAt      DateTime  @default(now()) @map("joined_at")
  teamId        String?   @map("team_id")
  department    String?
  jobTitle      String?   @map("job_title")
  manager       String?   @map("manager_id")
  lastActiveAt  DateTime? @map("last_active_at")
  invitedBy     String?   @map("invited_by")
  invitedAt     DateTime? @map("invited_at")
  team          Team?     @relation(fields: [teamId], references: [id])
  tenant        Tenant    @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  user          User      @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([tenantId, userId])
  @@index([tenantId, role], map: "idx_tenant_users_tenant_role")
  @@index([userId, status], map: "idx_tenant_users_user_status")
  @@map("tenant_users")
}

model Team {
  id           String       @id @default(cuid())
  tenantId     String       @map("tenant_id")
  name         String
  description  String?
  color        String?      @default("#6B7280")
  managerId    String?      @map("manager_id")
  parentTeamId String?      @map("parent_team_id")
  isActive     Boolean      @default(true) @map("is_active")
  createdById  String?      @map("created_by")
  createdAt    DateTime     @default(now()) @map("created_at")
  updatedAt    DateTime     @updatedAt @map("updated_at")
  settings     Json         @default("{}")
  metadata     Json         @default("{}")
  createdBy    User?        @relation("TeamCreatedBy", fields: [createdById], references: [id])
  manager      User?        @relation("TeamManager", fields: [managerId], references: [id])
  parentTeam   Team?        @relation("TeamHierarchy", fields: [parentTeamId], references: [id])
  childTeams   Team[]       @relation("TeamHierarchy")
  tenant       Tenant       @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  members      TenantUser[]

  @@unique([tenantId, name])
  @@map("teams")
}

model SubscriptionPlan {
  id            String         @id @default(cuid())
  name          String
  description   String?
  priceMonthly  Decimal        @map("price_monthly")
  priceYearly   Decimal        @map("price_yearly")
  features      Json           @default("{}")
  limits        Json           @default("{}")
  isActive      Boolean        @default(true) @map("is_active")
  createdAt     DateTime       @default(now()) @map("created_at")
  subscriptions Subscription[]

  @@map("subscription_plans")
}

model Subscription {
  id                   String           @id @default(cuid())
  tenantId             String           @map("tenant_id")
  planId               String           @map("plan_id")
  status               String           @default("active")
  currentPeriodStart   DateTime         @map("current_period_start")
  currentPeriodEnd     DateTime         @map("current_period_end")
  stripeSubscriptionId String?          @map("stripe_subscription_id")
  stripeCustomerId     String?          @map("stripe_customer_id")
  createdAt            DateTime         @default(now()) @map("created_at")
  updatedAt            DateTime         @updatedAt @map("updated_at")
  billingEvents        BillingEvent[]
  plan                 SubscriptionPlan @relation(fields: [planId], references: [id])
  tenant               Tenant           @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  @@map("subscriptions")
}

model FeatureFlag {
  id                String   @id @default(cuid())
  name              String   @unique
  description       String?
  isEnabled         Boolean  @default(true) @map("is_enabled")
  requiredPlanLevel Int      @default(1) @map("required_plan_level")
  createdAt         DateTime @default(now()) @map("created_at")

  @@map("feature_flags")
}

model UsageMetric {
  id          String   @id @default(cuid())
  tenantId    String   @map("tenant_id")
  metricName  String   @map("metric_name")
  metricValue Int      @map("metric_value")
  periodStart DateTime @map("period_start")
  periodEnd   DateTime @map("period_end")
  createdAt   DateTime @default(now()) @map("created_at")
  tenant      Tenant   @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  @@unique([tenantId, metricName, periodStart])
  @@index([tenantId, metricName, periodStart], map: "idx_usage_metrics_tenant_period")
  @@map("usage_metrics")
}

model BillingEvent {
  id             String        @id @default(cuid())
  tenantId       String        @map("tenant_id")
  subscriptionId String?       @map("subscription_id")
  eventType      String        @map("event_type")
  amount         Decimal?
  currency       String        @default("USD")
  stripeEventId  String?       @map("stripe_event_id")
  eventData      Json?         @map("event_data")
  createdAt      DateTime      @default(now()) @map("created_at")
  subscription   Subscription? @relation(fields: [subscriptionId], references: [id])
  tenant         Tenant        @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  @@index([tenantId, eventType, createdAt], map: "idx_billing_events_tenant_type_date")
  @@map("billing_events")
}

model Role {
  id              String           @id @default(cuid())
  tenantId        String           @map("tenant_id")
  name            String
  description     String?
  isSystemRole    Boolean          @default(false) @map("is_system_role")
  parentRoleId    String?          @map("parent_role_id")
  createdAt       DateTime         @default(now()) @map("created_at")
  updatedAt       DateTime         @updatedAt @map("updated_at")
  createdById     String?          @map("created_by")
  rolePermissions RolePermission[]
  createdBy       User?            @relation("RoleCreatedBy", fields: [createdById], references: [id])
  parentRole      Role?            @relation("RoleHierarchy", fields: [parentRoleId], references: [id])
  childRoles      Role[]           @relation("RoleHierarchy")
  tenant          Tenant           @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  userRoles       UserRole[]

  @@unique([tenantId, name])
  @@map("roles")
}

model Permission {
  id              String           @id @default(cuid())
  tenantId        String           @map("tenant_id")
  name            String
  resource        String
  action          String
  description     String?
  category        String
  createdAt       DateTime         @default(now()) @map("created_at")
  tenant          Tenant           @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  rolePermissions RolePermission[]

  @@unique([tenantId, name])
  @@map("permissions")
}

model UserRole {
  id             String    @id @default(cuid())
  tenantId       String    @map("tenant_id")
  userId         String    @map("user_id")
  roleId         String    @map("role_id")
  assignedBy     String?   @map("assigned_by")
  assignedAt     DateTime  @default(now()) @map("assigned_at")
  expiresAt      DateTime? @map("expires_at")
  assignedByUser User?     @relation("RoleAssignedBy", fields: [assignedBy], references: [id])
  role           Role      @relation(fields: [roleId], references: [id], onDelete: Cascade)
  tenant         Tenant    @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  user           User      @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([tenantId, userId, roleId])
  @@index([tenantId, roleId], map: "idx_user_roles_tenant_role")
  @@index([tenantId, userId], map: "idx_user_roles_tenant_user")
  @@map("user_roles")
}

model RolePermission {
  id            String     @id @default(cuid())
  tenantId      String     @map("tenant_id")
  roleId        String     @map("role_id")
  permissionId  String     @map("permission_id")
  grantedBy     String?    @map("granted_by")
  grantedAt     DateTime   @default(now()) @map("granted_at")
  grantedByUser User?      @relation("PermissionGrantedBy", fields: [grantedBy], references: [id])
  permission    Permission @relation(fields: [permissionId], references: [id], onDelete: Cascade)
  role          Role       @relation(fields: [roleId], references: [id], onDelete: Cascade)
  tenant        Tenant     @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  @@unique([tenantId, roleId, permissionId])
  @@index([tenantId, permissionId], map: "idx_role_permissions_tenant_permission")
  @@index([tenantId, roleId], map: "idx_role_permissions_tenant_role")
  @@map("role_permissions")
}

/// This model contains an expression index which requires additional setup for migrations. Visit https://pris.ly/d/expression-indexes for more info.
model Company {
  id            String        @id @default(cuid())
  tenantId      String        @map("tenant_id")
  name          String
  website       String?
  industry      String?
  size          String?
  ownerId       String?       @map("owner_id")
  createdById   String?       @map("created_by")
  createdAt     DateTime      @default(now()) @map("created_at")
  updatedAt     DateTime      @updatedAt @map("updated_at")
  createdBy     User?         @relation("CompanyCreatedBy", fields: [createdById], references: [id])
  owner         User?         @relation("CompanyOwner", fields: [ownerId], references: [id])
  tenant        Tenant        @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  contacts      Contact[]
  leads         Lead[]
  opportunities Opportunity[]

  @@index([tenantId, createdAt], map: "idx_companies_tenant_created_date")
  @@index([tenantId, name], map: "idx_companies_tenant_name")
  @@map("companies")
}

/// This model contains an expression index which requires additional setup for migrations. Visit https://pris.ly/d/expression-indexes for more info.
model Contact {
  id                  String                @id @default(cuid())
  tenantId            String                @map("tenant_id")
  firstName           String                @map("first_name")
  lastName            String                @map("last_name")
  email               String?
  phone               String?
  jobTitle            String?               @map("job_title")
  website             String?
  address             String?
  companyId           String?               @map("company_id")
  ownerId             String?               @map("owner_id")
  createdById         String?               @map("created_by")
  lastContactDate     DateTime?             @map("last_contact_date")
  createdAt           DateTime              @default(now()) @map("created_at")
  updatedAt           DateTime              @updatedAt @map("updated_at")
  company             Company?              @relation(fields: [companyId], references: [id])
  createdBy           User?                 @relation("ContactCreatedBy", fields: [createdById], references: [id])
  owner               User?                 @relation("ContactOwner", fields: [ownerId], references: [id])
  tenant              Tenant                @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  emailCaptures       EmailLeadCapture[]
  emailLogs           EmailLog[]
  leads               Lead[]
  opportunities       Opportunity[]
  projectClientAccess ProjectClientAccess[]
  scheduledMeetings   ScheduledMeeting[]
  socialCaptures      SocialMediaCapture[]

  @@index([tenantId, createdAt], map: "idx_contacts_tenant_created")
  @@index([tenantId, createdAt], map: "idx_contacts_tenant_created_date")
  @@map("contacts")
}

model Lead {
  id                  String                  @id @default(cuid())
  tenantId            String                  @map("tenant_id")
  contactId           String?                 @map("contact_id")
  companyId           String?                 @map("company_id")
  title               String
  description         String?
  status              String                  @default("new")
  leadSourceId        String?                 @map("lead_source_id")
  score               Int                     @default(0)
  value               Decimal?
  expectedCloseDate   DateTime?               @map("expected_close_date")
  priority            String                  @default("medium")
  tags                Json                    @default("[]")
  customFields        Json                    @default("{}") @map("custom_fields")
  ownerId             String?                 @map("owner_id")
  createdById         String?                 @map("created_by")
  lastContactDate     DateTime?               @map("last_contact_date")
  deletedAt           DateTime?               @map("deleted_at")
  isConverted         Boolean                 @default(false) @map("is_converted")
  convertedAt         DateTime?               @map("converted_at")
  convertedById       String?                 @map("converted_by")
  conversionNotes     String?                 @map("conversion_notes")
  createdAt           DateTime                @default(now()) @map("created_at")
  updatedAt           DateTime                @updatedAt @map("updated_at")
  emailCaptures       EmailLeadCapture[]
  emailLogs           EmailLog[]
  conversionHistory   LeadConversionHistory[]
  notifications       LeadNotification[]
  company             Company?                @relation(fields: [companyId], references: [id])
  contact             Contact?                @relation(fields: [contactId], references: [id])
  convertedBy         User?                   @relation("LeadConvertedBy", fields: [convertedById], references: [id])
  createdBy           User?                   @relation("LeadCreatedBy", fields: [createdById], references: [id])
  leadSource          LeadSource?             @relation(fields: [leadSourceId], references: [id])
  owner               User?                   @relation("LeadOwner", fields: [ownerId], references: [id])
  tenant              Tenant                  @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  nurturingExecutions NurturingExecution[]
  opportunities       Opportunity[]
  scheduledMeetings   ScheduledMeeting[]
  socialCaptures      SocialMediaCapture[]

  @@map("leads")
}

model LeadConversionHistory {
  id             String      @id @default(cuid())
  tenantId       String      @map("tenant_id")
  leadId         String      @map("lead_id")
  opportunityId  String      @map("opportunity_id")
  convertedById  String      @map("converted_by")
  conversionType String      @default("manual") @map("conversion_type")
  originalData   Json        @map("original_data")
  conversionData Json        @map("conversion_data")
  notes          String?
  convertedAt    DateTime    @default(now()) @map("converted_at")
  convertedBy    User        @relation("ConversionPerformedBy", fields: [convertedById], references: [id])
  lead           Lead        @relation(fields: [leadId], references: [id], onDelete: Cascade)
  opportunity    Opportunity @relation(fields: [opportunityId], references: [id], onDelete: Cascade)
  tenant         Tenant      @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  @@index([tenantId, convertedAt], map: "idx_lead_conversion_history_tenant_date")
  @@map("lead_conversion_history")
}

model LeadSource {
  id          String   @id @default(cuid())
  tenantId    String   @map("tenant_id")
  name        String
  description String?
  category    String   @default("custom")
  icon        String?
  color       String?
  isActive    Boolean  @default(true) @map("is_active")
  sortOrder   Int      @default(0) @map("sort_order")
  createdById String?  @map("created_by")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")
  createdBy   User?    @relation("LeadSourceCreatedBy", fields: [createdById], references: [id])
  tenant      Tenant   @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  leads       Lead[]

  @@unique([tenantId, name])
  @@map("lead_sources")
}

model NurturingCampaign {
  id             String               @id @default(cuid())
  tenantId       String               @map("tenant_id")
  name           String
  description    String?
  targetStatus   Json                 @default("[]") @map("target_status")
  targetPriority Json                 @default("[]") @map("target_priority")
  emailSequence  Json                 @default("[]") @map("email_sequence")
  isActive       Boolean              @default(true) @map("is_active")
  maxLeadsPerDay Int                  @default(10) @map("max_leads_per_day")
  createdById    String?              @map("created_by")
  deletedAt      DateTime?            @map("deleted_at")
  createdAt      DateTime             @default(now()) @map("created_at")
  updatedAt      DateTime             @updatedAt @map("updated_at")
  createdBy      User?                @relation("NurturingCampaignCreatedBy", fields: [createdById], references: [id])
  tenant         Tenant               @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  executions     NurturingExecution[]

  @@map("nurturing_campaigns")
}

model NurturingExecution {
  id                String              @id @default(cuid())
  campaignId        String              @map("campaign_id")
  leadId            String              @map("lead_id")
  currentStep       Int                 @default(0) @map("current_step")
  nextExecutionDate DateTime            @map("next_execution_date")
  status            String              @default("active")
  emailsSent        Int                 @default(0) @map("emails_sent")
  lastEmailSentAt   DateTime?           @map("last_email_sent_at")
  createdAt         DateTime            @default(now()) @map("created_at")
  updatedAt         DateTime            @updatedAt @map("updated_at")
  emailLogs         NurturingEmailLog[]
  campaign          NurturingCampaign   @relation(fields: [campaignId], references: [id], onDelete: Cascade)
  lead              Lead                @relation(fields: [leadId], references: [id], onDelete: Cascade)

  @@unique([campaignId, leadId])
  @@index([campaignId, status], map: "idx_nurturing_executions_campaign_status")
  @@map("nurturing_executions")
}

model NurturingEmailLog {
  id           String             @id @default(cuid())
  executionId  String             @map("execution_id")
  leadId       String             @map("lead_id")
  stepNumber   Int                @map("step_number")
  emailType    String             @map("email_type")
  subject      String
  content      String
  sentAt       DateTime           @map("sent_at")
  status       String             @default("sent")
  errorMessage String?            @map("error_message")
  execution    NurturingExecution @relation(fields: [executionId], references: [id], onDelete: Cascade)

  @@map("nurturing_email_logs")
}

model LeadNotification {
  id        String   @id @default(cuid())
  tenantId  String   @map("tenant_id")
  userId    String   @map("user_id")
  leadId    String   @map("lead_id")
  type      String
  title     String
  message   String
  data      Json?    @default("{}")
  isRead    Boolean  @default(false) @map("is_read")
  createdAt DateTime @default(now()) @map("created_at")
  lead      Lead     @relation(fields: [leadId], references: [id], onDelete: Cascade)
  tenant    Tenant   @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  user      User     @relation("LeadNotificationUser", fields: [userId], references: [id], onDelete: Cascade)

  @@map("lead_notifications")
}

model Notification {
  id        String    @id @default(cuid())
  tenantId  String    @map("tenant_id")
  userId    String    @map("user_id")
  type      String
  title     String
  message   String
  data      Json?     @default("{}")
  isRead    Boolean   @default(false) @map("is_read")
  readAt    DateTime? @map("read_at")
  createdAt DateTime  @default(now()) @map("created_at")
  tenant    Tenant    @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  user      User      @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([tenantId, userId])
  @@index([tenantId, userId, isRead])
  @@map("notifications")
}

model UserNotificationPreferences {
  id          String   @id @default(cuid())
  userId      String   @unique @map("user_id")
  preferences Json     @default("{}")
  updatedAt   DateTime @updatedAt @map("updated_at")
  user        User     @relation("UserNotificationPreferences", fields: [userId], references: [id], onDelete: Cascade)

  @@map("user_notification_preferences")
}

model EmailLeadCapture {
  id                String   @id @default(cuid())
  tenantId          String   @map("tenant_id")
  fromEmail         String   @map("from_email")
  fromName          String?  @map("from_name")
  subject           String
  content           String
  receivedAt        DateTime @map("received_at")
  processed         Boolean  @default(false)
  leadId            String?  @map("lead_id")
  contactId         String?  @map("contact_id")
  sentimentAnalysis Json?    @map("sentiment_analysis")
  extractedData     Json?    @map("extracted_data")
  processingError   String?  @map("processing_error")
  emailProvider     String?  @map("email_provider")
  messageId         String?  @map("message_id")
  createdAt         DateTime @default(now()) @map("created_at")
  contact           Contact? @relation(fields: [contactId], references: [id])
  lead              Lead?    @relation(fields: [leadId], references: [id])
  tenant            Tenant   @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  @@index([tenantId, processed, receivedAt], map: "idx_email_lead_captures_tenant_processed")
  @@map("email_lead_captures")
}

model SocialMediaCapture {
  id              String   @id @default(cuid())
  tenantId        String   @map("tenant_id")
  platform        String
  postId          String   @map("post_id")
  authorId        String   @map("author_id")
  authorName      String   @map("author_name")
  authorHandle    String?  @map("author_handle")
  content         String
  url             String?
  createdAt       DateTime @map("created_at")
  processed       Boolean  @default(false)
  leadId          String?  @map("lead_id")
  contactId       String?  @map("contact_id")
  analysis        Json?
  engagement      Json?
  metadata        Json?
  processingError String?  @map("processing_error")
  capturedAt      DateTime @default(now()) @map("captured_at")
  contact         Contact? @relation(fields: [contactId], references: [id])
  lead            Lead?    @relation(fields: [leadId], references: [id])
  tenant          Tenant   @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  @@unique([platform, postId])
  @@index([tenantId, platform], map: "idx_social_media_captures_tenant_platform")
  @@index([tenantId, processed, capturedAt], map: "idx_social_media_captures_tenant_processed")
  @@map("social_media_captures")
}

model CrmIntegration {
  id           String    @id @default(cuid())
  tenantId     String    @map("tenant_id")
  provider     String
  config       Json
  lastSyncAt   DateTime? @map("last_sync_at")
  syncStatus   String    @default("active")
  errorMessage String?   @map("error_message")
  createdById  String?   @map("created_by")
  updatedById  String?   @map("updated_by")
  createdAt    DateTime  @default(now()) @map("created_at")
  updatedAt    DateTime  @updatedAt @map("updated_at")
  createdBy    User?     @relation("CrmIntegrationCreatedBy", fields: [createdById], references: [id])
  tenant       Tenant    @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  updatedBy    User?     @relation("CrmIntegrationUpdatedBy", fields: [updatedById], references: [id])

  @@unique([tenantId, provider])
  @@index([tenantId, provider], map: "idx_crm_integrations_tenant_provider")
  @@index([tenantId, syncStatus, lastSyncAt], map: "idx_crm_integrations_tenant_sync_status")
  @@map("crm_integrations")
}

model CrmMapping {
  id         String   @id @default(cuid())
  tenantId   String   @map("tenant_id")
  entityType String   @map("entity_type")
  entityId   String   @map("entity_id")
  provider   String
  externalId String   @map("external_id")
  createdAt  DateTime @default(now()) @map("created_at")
  tenant     Tenant   @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  @@unique([tenantId, provider, externalId])
  @@map("crm_mappings")
}

model CrmSyncLog {
  id           String   @id @default(cuid())
  tenantId     String   @map("tenant_id")
  operation    String
  entityType   String   @map("entity_type")
  entityId     String   @map("entity_id")
  externalId   String?  @map("external_id")
  status       String
  errorMessage String?  @map("error_message")
  data         Json?
  syncedAt     DateTime @map("synced_at")
  tenant       Tenant   @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  @@index([tenantId, entityType, entityId], map: "idx_crm_sync_logs_tenant_entity")
  @@index([tenantId, operation, syncedAt], map: "idx_crm_sync_logs_tenant_operation_date")
  @@map("crm_sync_logs")
}

model SalesStage {
  id             String   @id @default(cuid())
  tenantId       String   @map("tenant_id")
  name           String
  description    String?
  orderIndex     Int      @map("order_index")
  color          String   @default("#6B7280")
  probabilityMin Int      @default(0) @map("probability_min")
  probabilityMax Int      @default(100) @map("probability_max")
  isClosed       Boolean  @default(false) @map("is_closed")
  isWon          Boolean  @default(false) @map("is_won")
  createdAt      DateTime @default(now()) @map("created_at")
  updatedAt      DateTime @updatedAt @map("updated_at")
  tenant         Tenant   @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  @@unique([tenantId, name])
  @@unique([tenantId, orderIndex])
  @@map("sales_stages")
}

/// This model contains an expression index which requires additional setup for migrations. Visit https://pris.ly/d/expression-indexes for more info.
model Opportunity {
  id                String                    @id @default(cuid())
  tenantId          String                    @map("tenant_id")
  leadId            String?                   @map("lead_id")
  contactId         String?                   @map("contact_id")
  companyId         String?                   @map("company_id")
  title             String
  description       String?
  value             Decimal?
  stage             String                    @default("qualification")
  probability       Int                       @default(0)
  expectedCloseDate DateTime?                 @map("expected_close_date")
  tags              Json                      @default("[]")
  source            String?
  priority          String                    @default("medium")
  closeReason       String?                   @map("close_reason")
  competitorInfo    Json                      @default("{}") @map("competitor_info")
  lastActivityDate  DateTime?                 @map("last_activity_date")
  nextFollowupDate  DateTime?                 @map("next_followup_date")
  customFields      Json                      @default("{}") @map("custom_fields")
  ownerId           String?                   @map("owner_id")
  createdById       String?                   @map("created_by")
  createdAt         DateTime                  @default(now()) @map("created_at")
  updatedAt         DateTime                  @updatedAt @map("updated_at")
  conversionHistory LeadConversionHistory[]
  company           Company?                  @relation(fields: [companyId], references: [id])
  contact           Contact?                  @relation(fields: [contactId], references: [id])
  createdBy         User?                     @relation("OpportunityCreatedBy", fields: [createdById], references: [id])
  lead              Lead?                     @relation(fields: [leadId], references: [id])
  owner             User?                     @relation("OpportunityOwner", fields: [ownerId], references: [id])
  tenant            Tenant                    @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  stageHistory      OpportunityStageHistory[]
  handovers         ProjectHandover[]
  projects          Project[]
  proposals         Proposal[]

  @@index([tenantId, createdAt], map: "idx_opportunities_tenant_created_date")
  @@index([tenantId, createdAt(sort: Desc)], map: "idx_opportunities_tenant_created_desc")
  @@index([tenantId, priority, createdAt], map: "idx_opportunities_tenant_priority")
  @@index([tenantId, stage, createdAt], map: "idx_opportunities_tenant_stage_created")
  @@map("opportunities")
}

model OpportunityStageHistory {
  id            String      @id @default(cuid())
  tenantId      String      @map("tenant_id")
  opportunityId String      @map("opportunity_id")
  fromStage     String?     @map("from_stage")
  toStage       String      @map("to_stage")
  changedById   String?     @map("changed_by")
  changedAt     DateTime    @default(now()) @map("changed_at")
  notes         String?
  changedBy     User?       @relation("OpportunityStageChangedBy", fields: [changedById], references: [id])
  opportunity   Opportunity @relation(fields: [opportunityId], references: [id], onDelete: Cascade)
  tenant        Tenant      @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  @@index([tenantId, opportunityId, changedAt], map: "idx_opportunity_stage_history_progression")
  @@map("opportunity_stage_history")
}

model Proposal {
  id                String            @id @default(cuid())
  tenantId          String            @map("tenant_id")
  opportunityId     String?           @map("opportunity_id")
  title             String
  content           String?
  status            String            @default("draft")
  version           Int               @default(1)
  totalAmount       Decimal?          @map("total_amount")
  isAiGenerated     Boolean           @default(false) @map("is_ai_generated")
  aiProvider        String?           @map("ai_provider")
  aiModel           String?           @map("ai_model")
  generationStatus  String            @default("pending") @map("generation_status")
  generationError   String?           @map("generation_error")
  selectedFileIds   Json              @default("[]") @map("selected_file_ids")
  geminiFileUris    Json              @default("{}") @map("gemini_file_uris")
  locale            String?           @default("en") @map("locale")
  documentPath      String?           @map("document_path")
  documentGenerated Boolean           @default(false) @map("document_generated")
  parentProposalId  String?           @map("parent_proposal_id")
  createdById       String?           @map("created_by")
  createdAt         DateTime          @default(now()) @map("created_at")
  updatedAt         DateTime          @updatedAt @map("updated_at")
  charts            ProposalChart[]
  sections          ProposalSection[]
  createdBy         User?             @relation("ProposalCreatedBy", fields: [createdById], references: [id])
  opportunity       Opportunity?      @relation(fields: [opportunityId], references: [id])
  parentProposal    Proposal?         @relation("ProposalVersions", fields: [parentProposalId], references: [id])
  childVersions     Proposal[]        @relation("ProposalVersions")
  tenant            Tenant            @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  @@index([tenantId, status], map: "idx_proposals_tenant_status")
  @@map("proposals")
}

model ProposalSection {
  id            String    @id @default(cuid())
  tenantId      String    @map("tenant_id")
  proposalId    String    @map("proposal_id")
  sectionType   String    @map("section_type")
  title         String
  content       String?
  orderIndex    Int       @map("order_index")
  isAiGenerated Boolean   @default(false) @map("is_ai_generated")
  aiPrompt      String?   @map("ai_prompt")
  aiProvider    String?   @map("ai_provider")
  aiModel       String?   @map("ai_model")
  tokensUsed    Int?      @map("tokens_used")
  generatedAt   DateTime? @map("generated_at")
  status        String    @default("draft") @map("status")
  wordCount     Int?      @map("word_count")
  confidence    Decimal?  @map("confidence")
  createdById   String?   @map("created_by")
  createdAt     DateTime  @default(now()) @map("created_at")
  updatedAt     DateTime  @updatedAt @map("updated_at")
  createdBy     User?     @relation("ProposalSectionCreatedBy", fields: [createdById], references: [id])
  proposal      Proposal  @relation(fields: [proposalId], references: [id], onDelete: Cascade)
  tenant        Tenant    @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  @@unique([proposalId, sectionType])
  @@index([tenantId, proposalId], map: "idx_proposal_sections_tenant_proposal")
  @@index([tenantId, sectionType], map: "idx_proposal_sections_tenant_type")
  @@map("proposal_sections")
}

model ProposalChart {
  id                String    @id @default(cuid())
  tenantId          String    @map("tenant_id")
  proposalId        String    @map("proposal_id")
  chartType         String    @map("chart_type")
  title             String
  description       String?
  mermaidCode       String    @map("mermaid_code")
  renderedImagePath String?   @map("rendered_image_path")
  isAiGenerated     Boolean   @default(false) @map("is_ai_generated")
  aiPrompt          String?   @map("ai_prompt")
  aiProvider        String?   @map("ai_provider")
  aiModel           String?   @map("ai_model")
  tokensUsed        Int?      @map("tokens_used")
  generatedAt       DateTime? @map("generated_at")
  status            String    @default("draft") @map("status")
  orderIndex        Int       @map("order_index")
  confidence        Decimal?  @map("confidence")
  errorContext      String?   @map("error_context")
  createdById       String?   @map("created_by")
  createdAt         DateTime  @default(now()) @map("created_at")
  updatedAt         DateTime  @updatedAt @map("updated_at")
  createdBy         User?     @relation("ProposalChartCreatedBy", fields: [createdById], references: [id])
  proposal          Proposal  @relation(fields: [proposalId], references: [id], onDelete: Cascade)
  tenant            Tenant    @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  @@index([tenantId, proposalId], map: "idx_proposal_charts_tenant_proposal")
  @@index([tenantId, chartType], map: "idx_proposal_charts_tenant_type")
  @@map("proposal_charts")
}

model ProposalContentSection {
  id                String    @id @default(cuid())
  tenantId          String    @map("tenant_id")
  sectionType       String    @map("section_type")
  title             String
  description       String?
  promptEn          String    @map("prompt_en")
  promptAr          String?   @map("prompt_ar")
  systemPromptEn    String    @map("system_prompt_en")
  systemPromptAr    String?   @map("system_prompt_ar")
  orderIndex        Int       @map("order_index")
  isActive          Boolean   @default(true) @map("is_active")
  isDefault         Boolean   @default(false) @map("is_default")
  createdById       String?   @map("created_by")
  createdAt         DateTime  @default(now()) @map("created_at")
  updatedAt         DateTime  @updatedAt @map("updated_at")
  createdBy         User?     @relation("ProposalContentSectionCreatedBy", fields: [createdById], references: [id])
  tenant            Tenant    @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  @@unique([tenantId, sectionType])
  @@index([tenantId, isActive], map: "idx_proposal_content_sections_tenant_active")
  @@index([tenantId, orderIndex], map: "idx_proposal_content_sections_tenant_order")
  @@map("proposal_content_sections")
}

model Project {
  id                  String                @id @default(cuid())
  tenantId            String                @map("tenant_id")
  opportunityId       String?               @map("opportunity_id")
  handoverId          String?               @map("handover_id")
  name                String
  description         String?
  status              String                @default("planning")
  startDate           DateTime?             @map("start_date")
  endDate             DateTime?             @map("end_date")
  budget              Decimal?
  managerId           String?               @map("manager_id")
  isFromHandover      Boolean               @default(false) @map("is_from_handover")
  handoverCompletedAt DateTime?             @map("handover_completed_at")
  clientPortalEnabled Boolean               @default(false) @map("client_portal_enabled")
  priority            String                @default("medium")
  progress            Decimal               @default(0) @map("progress")
  estimatedHours      Int?                  @map("estimated_hours")
  actualHours         Int?                  @map("actual_hours")
  clientPortalUrl     String?               @map("client_portal_url")
  clientAccessToken   String?               @map("client_access_token")
  externalProjectId   String?               @map("external_project_id")
  integrationProvider String?               @map("integration_provider")
  integrationConfig   Json?                 @map("integration_config")
  lastSyncAt          DateTime?             @map("last_sync_at")
  createdById         String?               @map("created_by")
  createdAt           DateTime              @default(now()) @map("created_at")
  updatedAt           DateTime              @updatedAt @map("updated_at")
  clientAccess        ProjectClientAccess[]
  milestones          ProjectMilestone[]
  resources           ProjectResource[]
  tasks               ProjectTask[]
  createdBy           User?                 @relation("ProjectCreatedBy", fields: [createdById], references: [id])
  handover            ProjectHandover?      @relation(fields: [handoverId], references: [id])
  manager             User?                 @relation("ProjectManager", fields: [managerId], references: [id])
  opportunity         Opportunity?          @relation(fields: [opportunityId], references: [id])
  tenant              Tenant                @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  @@unique([tenantId, opportunityId], name: "unique_project_per_opportunity")
  @@index([tenantId, status, priority], map: "idx_projects_tenant_status_priority")
  @@map("projects")
}

model ProjectTask {
  id             String            @id @default(cuid())
  tenantId       String            @map("tenant_id")
  projectId      String            @map("project_id")
  milestoneId    String?           @map("milestone_id")
  title          String
  description    String?
  status         String            @default("todo")
  priority       String            @default("medium")
  assigneeId     String?           @map("assignee_id")
  startDate      DateTime?         @map("start_date")
  dueDate        DateTime?         @map("due_date")
  completedAt    DateTime?         @map("completed_at")
  estimatedHours Decimal?          @map("estimated_hours")
  actualHours    Decimal?          @map("actual_hours")
  parentTaskId   String?           @map("parent_task_id")
  dependsOnTasks Json              @default("[]") @map("depends_on_tasks")
  externalTaskId String?           @map("external_task_id")
  createdById    String?           @map("created_by")
  createdAt      DateTime          @default(now()) @map("created_at")
  updatedAt      DateTime          @updatedAt @map("updated_at")
  assignee       User?             @relation("TaskAssignee", fields: [assigneeId], references: [id])
  createdBy      User?             @relation("TaskCreatedBy", fields: [createdById], references: [id])
  milestone      ProjectMilestone? @relation(fields: [milestoneId], references: [id])
  parentTask     ProjectTask?      @relation("TaskDependency", fields: [parentTaskId], references: [id])
  subTasks       ProjectTask[]     @relation("TaskDependency")
  project        Project           @relation(fields: [projectId], references: [id], onDelete: Cascade)
  tenant         Tenant            @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  @@index([tenantId, projectId, status], map: "idx_project_tasks_tenant_project_status")
  @@map("project_tasks")
}

model ProjectMilestone {
  id                  String        @id @default(cuid())
  tenantId            String        @map("tenant_id")
  projectId           String        @map("project_id")
  title               String
  description         String?
  status              String        @default("pending")
  dueDate             DateTime      @map("due_date")
  completedAt         DateTime?     @map("completed_at")
  progress            Decimal       @default(0)
  dependsOnTasks      Json          @default("[]") @map("depends_on_tasks")
  externalMilestoneId String?       @map("external_milestone_id")
  createdById         String?       @map("created_by")
  createdAt           DateTime      @default(now()) @map("created_at")
  updatedAt           DateTime      @updatedAt @map("updated_at")
  createdBy           User?         @relation("MilestoneCreatedBy", fields: [createdById], references: [id])
  project             Project       @relation(fields: [projectId], references: [id], onDelete: Cascade)
  tenant              Tenant        @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  tasks               ProjectTask[]

  @@index([tenantId, dueDate], map: "idx_project_milestones_tenant_due_date")
  @@index([tenantId, projectId, status], map: "idx_project_milestones_tenant_project_status")
  @@map("project_milestones")
}

model ProjectResource {
  id             String    @id @default(cuid())
  tenantId       String    @map("tenant_id")
  projectId      String    @map("project_id")
  userId         String    @map("user_id")
  role           String
  allocation     Decimal   @default(100)
  hourlyRate     Decimal?  @map("hourly_rate")
  startDate      DateTime  @map("start_date")
  endDate        DateTime? @map("end_date")
  hoursAllocated Decimal?  @map("hours_allocated")
  hoursLogged    Decimal   @default(0) @map("hours_logged")
  isActive       Boolean   @default(true) @map("is_active")
  createdById    String?   @map("created_by")
  createdAt      DateTime  @default(now()) @map("created_at")
  updatedAt      DateTime  @updatedAt @map("updated_at")
  createdBy      User?     @relation("ProjectResourceCreatedBy", fields: [createdById], references: [id])
  project        Project   @relation(fields: [projectId], references: [id], onDelete: Cascade)
  tenant         Tenant    @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  user           User      @relation("ProjectResourceUser", fields: [userId], references: [id])

  @@unique([tenantId, projectId, userId, role])
  @@map("project_resources")
}

model ProjectClientAccess {
  id                String    @id @default(cuid())
  tenantId          String    @map("tenant_id")
  projectId         String    @map("project_id")
  contactId         String    @map("contact_id")
  accessLevel       String    @default("view")
  canViewTasks      Boolean   @default(true) @map("can_view_tasks")
  canViewMilestones Boolean   @default(true) @map("can_view_milestones")
  canViewDocuments  Boolean   @default(true) @map("can_view_documents")
  canComment        Boolean   @default(false) @map("can_comment")
  accessToken       String    @unique @map("access_token")
  tokenExpiresAt    DateTime? @map("token_expires_at")
  lastAccessAt      DateTime? @map("last_access_at")
  accessCount       Int       @default(0) @map("access_count")
  isActive          Boolean   @default(true) @map("is_active")
  createdById       String?   @map("created_by")
  createdAt         DateTime  @default(now()) @map("created_at")
  updatedAt         DateTime  @updatedAt @map("updated_at")
  contact           Contact   @relation(fields: [contactId], references: [id])
  createdBy         User?     @relation("ClientAccessCreatedBy", fields: [createdById], references: [id])
  project           Project   @relation(fields: [projectId], references: [id], onDelete: Cascade)
  tenant            Tenant    @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  @@unique([tenantId, projectId, contactId])
  @@map("project_client_access")
}

model HandoverChecklistTemplate {
  id                String            @id @default(cuid())
  tenantId          String            @map("tenant_id")
  name              String
  description       String?
  isDefault         Boolean           @default(false) @map("is_default")
  category          String            @default("general")
  items             Json              @default("[]")
  estimatedDuration Int?              @map("estimated_duration")
  createdById       String?           @map("created_by")
  createdAt         DateTime          @default(now()) @map("created_at")
  updatedAt         DateTime          @updatedAt @map("updated_at")
  createdBy         User?             @relation("HandoverTemplateCreatedBy", fields: [createdById], references: [id])
  tenant            Tenant            @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  handovers         ProjectHandover[]

  @@map("handover_checklist_templates")
}

model ProjectHandover {
  id                 String                     @id @default(cuid())
  tenantId           String                     @map("tenant_id")
  opportunityId      String                     @map("opportunity_id")
  templateId         String?                    @map("template_id")
  title              String
  description        String?
  status             String                     @default("pending")
  priority           String                     @default("medium")
  salesRepId         String?                    @map("sales_rep_id")
  deliveryManagerId  String?                    @map("delivery_manager_id")
  assignedTeamIds    Json                       @default("[]") @map("assigned_team_ids")
  checklistProgress  Decimal                    @default(0) @map("checklist_progress")
  documentsCount     Int                        @default(0) @map("documents_count")
  qaThreadsCount     Int                        @default(0) @map("qa_threads_count")
  scheduledDate      DateTime?                  @map("scheduled_date")
  startedAt          DateTime?                  @map("started_at")
  completedAt        DateTime?                  @map("completed_at")
  estimatedDuration  Int?                       @map("estimated_duration")
  actualDuration     Int?                       @map("actual_duration")
  qualityScore       Decimal?                   @map("quality_score")
  clientSatisfaction Decimal?                   @map("client_satisfaction")
  deliveryFeedback   String?                    @map("delivery_feedback")
  createdById        String?                    @map("created_by")
  createdAt          DateTime                   @default(now()) @map("created_at")
  updatedAt          DateTime                   @updatedAt @map("updated_at")
  checklistItems     HandoverChecklistItem[]
  documents          HandoverDocument[]
  notifications      HandoverNotification[]
  qaThreads          HandoverQAThread[]
  createdBy          User?                      @relation("HandoverCreatedBy", fields: [createdById], references: [id])
  deliveryManager    User?                      @relation("HandoverDeliveryManager", fields: [deliveryManagerId], references: [id])
  opportunity        Opportunity                @relation(fields: [opportunityId], references: [id])
  salesRep           User?                      @relation("HandoverSalesRep", fields: [salesRepId], references: [id])
  template           HandoverChecklistTemplate? @relation(fields: [templateId], references: [id])
  tenant             Tenant                     @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  projects           Project[]

  @@unique([tenantId, opportunityId], name: "unique_handover_per_opportunity")
  @@index([tenantId, status], map: "idx_project_handovers_tenant_status")
  @@map("project_handovers")
}

model HandoverChecklistItem {
  id            String          @id @default(cuid())
  tenantId      String          @map("tenant_id")
  handoverId    String          @map("handover_id")
  title         String
  description   String?
  category      String          @default("general")
  orderIndex    Int             @map("order_index")
  isRequired    Boolean         @default(true) @map("is_required")
  isCompleted   Boolean         @default(false) @map("is_completed")
  completedAt   DateTime?       @map("completed_at")
  completedById String?         @map("completed_by")
  notes         String?
  dependsOnIds  Json            @default("[]") @map("depends_on_ids")
  createdAt     DateTime        @default(now()) @map("created_at")
  updatedAt     DateTime        @updatedAt @map("updated_at")
  completedBy   User?           @relation("ChecklistItemCompletedBy", fields: [completedById], references: [id])
  handover      ProjectHandover @relation(fields: [handoverId], references: [id], onDelete: Cascade)
  tenant        Tenant          @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  @@index([tenantId, isCompleted, completedAt], map: "idx_handover_checklist_items_tenant_completed")
  @@index([tenantId, handoverId], map: "idx_handover_checklist_items_tenant_handover")
  @@map("handover_checklist_items")
}

model HandoverDocument {
  id              String          @id @default(cuid())
  tenantId        String          @map("tenant_id")
  handoverId      String          @map("handover_id")
  documentId      String          @map("document_id")
  category        String          @default("general")
  isRequired      Boolean         @default(false) @map("is_required")
  isApproved      Boolean         @default(false) @map("is_approved")
  approvedAt      DateTime?       @map("approved_at")
  approvedById    String?         @map("approved_by")
  isClientVisible Boolean         @default(false) @map("is_client_visible")
  accessLevel     String          @default("internal")
  orderIndex      Int             @map("order_index")
  notes           String?
  createdAt       DateTime        @default(now()) @map("created_at")
  updatedAt       DateTime        @updatedAt @map("updated_at")
  approvedBy      User?           @relation("HandoverDocumentApprovedBy", fields: [approvedById], references: [id])
  document        Document        @relation(fields: [documentId], references: [id], onDelete: Cascade)
  handover        ProjectHandover @relation(fields: [handoverId], references: [id], onDelete: Cascade)
  tenant          Tenant          @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  @@map("handover_documents")
}

model HandoverQAThread {
  id           String              @id @default(cuid())
  tenantId     String              @map("tenant_id")
  handoverId   String              @map("handover_id")
  title        String
  category     String              @default("general")
  priority     String              @default("medium")
  status       String              @default("open")
  createdById  String              @map("created_by")
  assignedToId String?             @map("assigned_to")
  responseTime Int?                @map("response_time")
  isEscalated  Boolean             @default(false) @map("is_escalated")
  escalatedAt  DateTime?           @map("escalated_at")
  resolvedAt   DateTime?           @map("resolved_at")
  createdAt    DateTime            @default(now()) @map("created_at")
  updatedAt    DateTime            @updatedAt @map("updated_at")
  messages     HandoverQAMessage[]
  assignedTo   User?               @relation("QAThreadAssignedTo", fields: [assignedToId], references: [id])
  createdBy    User                @relation("QAThreadCreatedBy", fields: [createdById], references: [id])
  handover     ProjectHandover     @relation(fields: [handoverId], references: [id], onDelete: Cascade)
  tenant       Tenant              @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  @@map("handover_qa_threads")
}

model HandoverQAMessage {
  id          String           @id @default(cuid())
  tenantId    String           @map("tenant_id")
  threadId    String           @map("thread_id")
  content     String
  messageType String           @default("message") @map("message_type")
  attachments Json             @default("[]")
  isRead      Boolean          @default(false) @map("is_read")
  readAt      DateTime?        @map("read_at")
  authorId    String           @map("author_id")
  createdAt   DateTime         @default(now()) @map("created_at")
  updatedAt   DateTime         @updatedAt @map("updated_at")
  author      User             @relation("QAMessageAuthor", fields: [authorId], references: [id])
  tenant      Tenant           @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  thread      HandoverQAThread @relation(fields: [threadId], references: [id], onDelete: Cascade)

  @@map("handover_qa_messages")
}

model HandoverNotification {
  id          String          @id @default(cuid())
  tenantId    String          @map("tenant_id")
  handoverId  String          @map("handover_id")
  userId      String          @map("user_id")
  type        String
  title       String
  message     String
  data        Json            @default("{}")
  isRead      Boolean         @default(false) @map("is_read")
  readAt      DateTime?       @map("read_at")
  actionUrl   String?         @map("action_url")
  actionLabel String?         @map("action_label")
  createdAt   DateTime        @default(now()) @map("created_at")
  handover    ProjectHandover @relation(fields: [handoverId], references: [id], onDelete: Cascade)
  tenant      Tenant          @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  user        User            @relation("HandoverNotificationUser", fields: [userId], references: [id])

  @@map("handover_notifications")
}

model Activity {
  id            String    @id @default(cuid())
  tenantId      String    @map("tenant_id")
  relatedToType String?   @map("related_to_type")
  relatedToId   String?   @map("related_to_id")
  type          String
  subject       String?
  description   String?
  scheduledAt   DateTime? @map("scheduled_at")
  completedAt   DateTime? @map("completed_at")
  ownerId       String?   @map("owner_id")
  createdById   String?   @map("created_by")
  createdAt     DateTime  @default(now()) @map("created_at")
  updatedAt     DateTime  @updatedAt @map("updated_at")
  createdBy     User?     @relation("ActivityCreatedBy", fields: [createdById], references: [id])
  owner         User?     @relation("ActivityOwner", fields: [ownerId], references: [id])
  tenant        Tenant    @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  @@index([tenantId, createdAt(sort: Desc)], map: "idx_activities_recent_tenant")
  @@index([tenantId, type, createdAt], map: "idx_activities_tenant_type_date")
  @@map("activities")
}

model ScheduledMeeting {
  id               String    @id @default(cuid())
  tenantId         String    @map("tenant_id")
  leadId           String?   @map("lead_id")
  contactId        String?   @map("contact_id")
  scheduledBy      String    @map("scheduled_by")
  assignedTo       String    @map("assigned_to")
  title            String
  description      String?
  meetingType      String    @default("phone") @map("meeting_type")
  meetingLink      String?   @map("meeting_link")
  location         String?
  scheduledAt      DateTime  @map("scheduled_at")
  duration         Int       @default(30)
  timezone         String    @default("UTC")
  status           String    @default("scheduled")
  completedAt      DateTime? @map("completed_at")
  meetingNotes     String?   @map("meeting_notes")
  followUpRequired Boolean   @default(false) @map("follow_up_required")
  nextMeetingDate  DateTime? @map("next_meeting_date")
  reminderSent     Boolean   @default(false) @map("reminder_sent")
  invitationSent   Boolean   @default(false) @map("invitation_sent")
  createdAt        DateTime  @default(now()) @map("created_at")
  updatedAt        DateTime  @updatedAt @map("updated_at")
  assignedToUser   User      @relation("ScheduledMeetingAssignedTo", fields: [assignedTo], references: [id])
  contact          Contact?  @relation(fields: [contactId], references: [id])
  lead             Lead?     @relation(fields: [leadId], references: [id])
  scheduledByUser  User      @relation("ScheduledMeetingCreatedBy", fields: [scheduledBy], references: [id])
  tenant           Tenant    @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  @@index([tenantId, scheduledAt], map: "idx_scheduled_meetings_tenant_date")
  @@map("scheduled_meetings")
}

model Document {
  id                String             @id @default(cuid())
  tenantId          String             @map("tenant_id")
  relatedToType     String?            @map("related_to_type")
  relatedToId       String?            @map("related_to_id")
  name              String
  filePath          String             @map("file_path")
  fileSize          Int?               @map("file_size")
  mimeType          String?            @map("mime_type")
  description       String?
  tags              Json               @default("[]")
  isPublic          Boolean            @default(false) @map("is_public")
  uploadedById      String?            @map("uploaded_by")
  createdAt         DateTime           @default(now()) @map("created_at")
  updatedAt         DateTime           @updatedAt @map("updated_at")
  tenant            Tenant             @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  uploadedBy        User?              @relation("DocumentUploadedBy", fields: [uploadedById], references: [id])
  handoverDocuments HandoverDocument[]

  @@index([tenantId, isPublic], map: "idx_documents_tenant_public")
  @@map("documents")
}

model DocumentTemplate {
  id                  String    @id @default(cuid())
  tenantId            String    @map("tenant_id")
  name                String
  description         String?
  category            String    @default("proposal")
  templateType        String    @map("template_type")
  filePath            String?   @map("file_path")
  htmlTemplate        String?   @map("html_template") @db.Text
  extractedStructure  Json      @default("{}") @map("extracted_structure")
  placeholderMappings Json      @default("{}") @map("placeholder_mappings")
  templateMetadata    Json      @default("{}") @map("template_metadata")
  isDefault           Boolean   @default(false) @map("is_default")
  isActive            Boolean   @default(true) @map("is_active")
  styling             Json      @default("{}")
  branding            Json      @default("{}")
  variables           Json      @default("[]")
  usageCount          Int       @default(0) @map("usage_count")
  lastUsedAt          DateTime? @map("last_used_at")
  createdById         String?   @map("created_by")
  createdAt           DateTime  @default(now()) @map("created_at")
  updatedAt           DateTime  @updatedAt @map("updated_at")
  createdBy           User?     @relation("DocumentTemplateCreatedBy", fields: [createdById], references: [id])
  tenant              Tenant    @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  @@index([tenantId, category])
  @@index([tenantId, isActive])
  @@map("document_templates")
}

model AiInsight {
  id              String   @id @default(cuid())
  tenantId        String   @map("tenant_id")
  relatedToType   String?  @map("related_to_type")
  relatedToId     String?  @map("related_to_id")
  insightType     String   @map("insight_type")
  data            Json
  confidenceScore Decimal? @map("confidence_score")
  generatedBy     String?  @map("generated_by")
  createdAt       DateTime @default(now()) @map("created_at")
  tenant          Tenant   @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  @@index([tenantId, insightType, createdAt], map: "idx_ai_insights_tenant_type_date")
  @@map("ai_insights")
}

model TenantSetting {
  id           String   @id @default(cuid())
  tenantId     String   @map("tenant_id")
  settingKey   String   @map("setting_key")
  settingValue Json?    @map("setting_value")
  description  String?
  updatedById  String?  @map("updated_by")
  updatedAt    DateTime @default(now()) @map("updated_at")
  tenant       Tenant   @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  updatedBy    User?    @relation("SettingUpdatedBy", fields: [updatedById], references: [id])

  @@unique([tenantId, settingKey])
  @@map("tenant_settings")
}

model AuditLog {
  id           String   @id @default(cuid())
  tenantId     String?  @map("tenant_id")
  userId       String?  @map("user_id")
  action       String
  resourceType String?  @map("resource_type")
  resourceId   String?  @map("resource_id")
  oldValues    Json?    @map("old_values")
  newValues    Json?    @map("new_values")
  ipAddress    String?  @map("ip_address")
  userAgent    String?  @map("user_agent")
  createdAt    DateTime @default(now()) @map("created_at")
  tenant       Tenant?  @relation(fields: [tenantId], references: [id])
  user         User?    @relation(fields: [userId], references: [id])

  @@map("audit_logs")
}

model EmailLog {
  id           String    @id @default(cuid())
  tenantId     String    @map("tenant_id")
  sentBy       String    @map("sent_by")
  leadId       String?   @map("lead_id")
  contactId    String?   @map("contact_id")
  campaignId   String?   @map("campaign_id")
  templateId   String?   @map("template_id")
  recipients   Json      @default("[]")
  subject      String
  content      String
  messageId    String?   @map("message_id")
  provider     String    @default("smtp")
  status       String    @default("sent")
  sentAt       DateTime  @map("sent_at")
  deliveredAt  DateTime? @map("delivered_at")
  openedAt     DateTime? @map("opened_at")
  clickedAt    DateTime? @map("clicked_at")
  bouncedAt    DateTime? @map("bounced_at")
  trackOpens   Boolean   @default(true) @map("track_opens")
  trackClicks  Boolean   @default(true) @map("track_clicks")
  tags         Json      @default("[]")
  metadata     Json      @default("{}")
  errorMessage String?   @map("error_message")
  createdAt    DateTime  @default(now()) @map("created_at")
  contact      Contact?  @relation(fields: [contactId], references: [id])
  lead         Lead?     @relation(fields: [leadId], references: [id])
  sentByUser   User      @relation("EmailSentBy", fields: [sentBy], references: [id])
  tenant       Tenant    @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  @@index([tenantId, status, sentAt], map: "idx_email_logs_tenant_status_date")
  @@map("email_logs")
}

model ImpersonationSession {
  id           String    @id @default(cuid())
  superAdminId String    @map("super_admin_id")
  tenantId     String    @map("tenant_id")
  userId       String    @map("user_id")
  startedAt    DateTime  @default(now()) @map("started_at")
  endedAt      DateTime? @map("ended_at")
  reason       String?
  ipAddress    String?   @map("ip_address")
  userAgent    String?   @map("user_agent")
  superAdmin   User      @relation("ImpersonationSuperAdmin", fields: [superAdminId], references: [id])
  tenant       Tenant    @relation("ImpersonationTenant", fields: [tenantId], references: [id])
  user         User      @relation("ImpersonationUser", fields: [userId], references: [id])

  @@map("impersonation_sessions")
}

model ChatChannel {
  id               String              @id @default(cuid())
  tenantId         String              @map("tenant_id")
  name             String
  description      String?
  type             String              @default("general")
  isPrivate        Boolean             @default(false) @map("is_private")
  relatedToType    String?             @map("related_to_type")
  relatedToId      String?             @map("related_to_id")
  allowFileSharing Boolean             @default(true) @map("allow_file_sharing")
  allowMentions    Boolean             @default(true) @map("allow_mentions")
  createdById      String              @map("created_by")
  createdAt        DateTime            @default(now()) @map("created_at")
  updatedAt        DateTime            @updatedAt @map("updated_at")
  members          ChatChannelMember[]
  createdBy        User                @relation("ChatChannelCreatedBy", fields: [createdById], references: [id])
  tenant           Tenant              @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  messages         ChatMessage[]

  @@index([tenantId, type])
  @@index([tenantId, relatedToType, relatedToId])
  @@index([tenantId, type], map: "idx_chat_channels_tenant_type")
  @@map("chat_channels")
}

model ChatChannelMember {
  id          String      @id @default(cuid())
  tenantId    String      @map("tenant_id")
  channelId   String      @map("channel_id")
  userId      String      @map("user_id")
  role        String      @default("member")
  canPost     Boolean     @default(true) @map("can_post")
  canInvite   Boolean     @default(false) @map("can_invite")
  canModerate Boolean     @default(false) @map("can_moderate")
  isActive    Boolean     @default(true) @map("is_active")
  lastReadAt  DateTime?   @map("last_read_at")
  joinedAt    DateTime    @default(now()) @map("joined_at")
  leftAt      DateTime?   @map("left_at")
  channel     ChatChannel @relation(fields: [channelId], references: [id], onDelete: Cascade)
  tenant      Tenant      @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  user        User        @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([channelId, userId])
  @@index([tenantId, userId])
  @@map("chat_channel_members")
}

model ChatMessage {
  id              String        @id @default(cuid())
  tenantId        String        @map("tenant_id")
  channelId       String        @map("channel_id")
  userId          String        @map("user_id")
  content         String
  messageType     String        @default("text") @map("message_type")
  attachments     Json?         @default("[]")
  parentMessageId String?       @map("parent_message_id")
  threadCount     Int           @default(0) @map("thread_count")
  isEdited        Boolean       @default(false) @map("is_edited")
  editedAt        DateTime?     @map("edited_at")
  isDeleted       Boolean       @default(false) @map("is_deleted")
  deletedAt       DateTime?     @map("deleted_at")
  mentions        Json?         @default("[]")
  reactions       Json?         @default("{}")
  createdAt       DateTime      @default(now()) @map("created_at")
  updatedAt       DateTime      @updatedAt @map("updated_at")
  channel         ChatChannel   @relation(fields: [channelId], references: [id], onDelete: Cascade)
  parentMessage   ChatMessage?  @relation("MessageThread", fields: [parentMessageId], references: [id])
  replies         ChatMessage[] @relation("MessageThread")
  tenant          Tenant        @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  user            User          @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([tenantId, channelId, createdAt])
  @@index([tenantId, userId])
  @@index([parentMessageId])
  @@index([tenantId, channelId, createdAt], map: "idx_chat_messages_tenant_channel_date")
  @@index([tenantId, userId], map: "idx_chat_messages_tenant_user")
  @@map("chat_messages")
}

model CalendarEvent {
  id              String                  @id @default(cuid())
  tenantId        String                  @map("tenant_id")
  title           String
  description     String?
  startTime       DateTime                @map("start_time")
  endTime         DateTime                @map("end_time")
  timezone        String                  @default("UTC")
  isAllDay        Boolean                 @default(false) @map("is_all_day")
  location        String?
  meetingLink     String?                 @map("meeting_link")
  eventType       String                  @default("meeting")
  status          String                  @default("confirmed")
  visibility      String                  @default("public")
  relatedToType   String?                 @map("related_to_type")
  relatedToId     String?                 @map("related_to_id")
  isRecurring     Boolean                 @default(false) @map("is_recurring")
  recurrenceRule  String?                 @map("recurrence_rule")
  reminderMinutes Int?                    @map("reminder_minutes")
  createdById     String                  @map("created_by")
  createdAt       DateTime                @default(now()) @map("created_at")
  updatedAt       DateTime                @updatedAt @map("updated_at")
  attendees       CalendarEventAttendee[]
  createdBy       User                    @relation("CalendarEventCreatedBy", fields: [createdById], references: [id])
  tenant          Tenant                  @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  @@index([tenantId, startTime])
  @@index([tenantId, eventType])
  @@index([tenantId, relatedToType, relatedToId])
  @@index([tenantId, startTime], map: "idx_calendar_events_tenant_start_time")
  @@index([tenantId, eventType], map: "idx_calendar_events_tenant_type")
  @@map("calendar_events")
}

model CalendarEventAttendee {
  id          String        @id @default(cuid())
  tenantId    String        @map("tenant_id")
  eventId     String        @map("event_id")
  userId      String        @map("user_id")
  status      String        @default("pending")
  response    String?
  canEdit     Boolean       @default(false) @map("can_edit")
  respondedAt DateTime?     @map("responded_at")
  createdAt   DateTime      @default(now()) @map("created_at")
  event       CalendarEvent @relation(fields: [eventId], references: [id], onDelete: Cascade)
  tenant      Tenant        @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  user        User          @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([eventId, userId])
  @@index([tenantId, userId])
  @@map("calendar_event_attendees")
}

model EmailTemplate {
  id                String    @id @default(cuid())
  tenantId          String    @map("tenant_id")
  name              String
  description       String?
  subject           String
  content           String
  variables         Json      @default("[]")
  category          String    @default("general")
  type              String
  locale            String    @default("en")
  useCustomBranding Boolean   @default(false) @map("use_custom_branding")
  brandingConfig    Json?     @map("branding_config")
  isActive          Boolean   @default(true) @map("is_active")
  isDefault         Boolean   @default(false) @map("is_default")
  usageCount        Int       @default(0) @map("usage_count")
  lastUsedAt        DateTime? @map("last_used_at")
  createdById       String    @map("created_by")
  createdAt         DateTime  @default(now()) @map("created_at")
  updatedAt         DateTime  @updatedAt @map("updated_at")
  createdBy         User      @relation("EmailTemplateCreatedBy", fields: [createdById], references: [id])
  tenant            Tenant    @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  @@index([tenantId, category])
  @@index([tenantId, type])
  @@index([tenantId, locale])
  @@index([tenantId, category], map: "idx_email_templates_tenant_category")
  @@index([tenantId, type], map: "idx_email_templates_tenant_type")
  @@map("email_templates")
}

model AutomationTrigger {
  id                String                @id @default(cuid())
  tenantId          String                @map("tenant_id")
  name              String
  description       String?
  triggerType       String                @map("trigger_type")
  triggerConditions Json                  @map("trigger_conditions")
  actions           Json
  isActive          Boolean               @default(true) @map("is_active")
  priority          Int                   @default(5)
  createdById       String?               @map("created_by")
  createdAt         DateTime              @default(now()) @map("created_at")
  updatedAt         DateTime              @updatedAt @map("updated_at")
  executions        AutomationExecution[]
  createdBy         User?                 @relation("AutomationTriggerCreatedBy", fields: [createdById], references: [id])
  tenant            Tenant                @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  @@index([tenantId, triggerType], map: "idx_automation_triggers_tenant_trigger_type")
  @@map("automation_triggers")
}

model AutomationExecution {
  id              String            @id @default(cuid())
  tenantId        String            @map("tenant_id")
  triggerId       String            @map("trigger_id")
  entityType      String            @map("entity_type")
  entityId        String            @map("entity_id")
  triggerData     Json              @map("trigger_data")
  executedActions Json              @default("[]") @map("executed_actions")
  status          String            @default("pending")
  error           String?
  startedAt       DateTime          @default(now()) @map("started_at")
  completedAt     DateTime?         @map("completed_at")
  tenant          Tenant            @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  trigger         AutomationTrigger @relation(fields: [triggerId], references: [id], onDelete: Cascade)

  @@index([tenantId, status], map: "idx_automation_executions_tenant_status")
  @@index([tenantId, triggerId, startedAt], map: "idx_automation_executions_tenant_trigger_date")
  @@map("automation_executions")
}
