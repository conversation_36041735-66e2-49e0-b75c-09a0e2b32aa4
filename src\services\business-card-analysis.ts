import { GoogleGenAI } from '@google/genai';
import { aiService } from '@/services/ai-service';
import { AIRequest } from '@/services/ai-service';
import fs from 'fs/promises';
import path from 'path';
import os from 'os';

export interface BusinessCardAnalysisRequest {
  file: File;
  tenantId: string;
  userId: string;
}

export interface BusinessCardAnalysisResponse {
  firstName: string;
  lastName: string;
  email: string | null;
  phone: string | null;
  company: string | null;
  jobTitle: string | null;
  website: string | null;
  address: string | null;
  confidence: number;
  processingTime: number;
  rawText?: string;
}

export class BusinessCardAnalysisService {
  private genAI: GoogleGenAI;

  constructor() {
    const apiKey = process.env.GOOGLE_AI_API_KEY;
    if (!apiKey) {
      throw new Error('Google AI API key not configured');
    }
    this.genAI = new GoogleGenAI({ apiKey });
  }

  /**
   * Analyze a business card image using Gemini AI to extract contact information
   */
  async analyzeBusinessCard(request: BusinessCardAnalysisRequest): Promise<BusinessCardAnalysisResponse> {
    const startTime = Date.now();

    try {
      console.log('🔍 Starting business card analysis...');

      // Validate file type (should be an image)
      if (!this.isValidImageFile(request.file)) {
        throw new Error('Invalid file type. Please upload an image file (JPG, PNG, WebP, etc.)');
      }

      console.log('✅ File validation passed');

      // Upload file to Gemini for analysis
      console.log('📤 Uploading file to Gemini...');
      const uploadResponse = await this.uploadFileToGemini(request.file);
      console.log('✅ File uploaded, waiting for processing...');

      // Wait for file processing
      let file = uploadResponse;
      let attempts = 0;
      const maxAttempts = 30; // 60 seconds max wait time

      while (file.state === 'PROCESSING' && attempts < maxAttempts) {
        await new Promise(resolve => setTimeout(resolve, 2000));
        file = await this.genAI.files.get({ name: file.name! });
        attempts++;
        console.log(`⏳ Processing attempt ${attempts}/${maxAttempts}, state: ${file.state}`);
      }

      if (file.state === 'FAILED') {
        throw new Error('Gemini file processing failed');
      }

      if (file.state === 'PROCESSING') {
        throw new Error('File processing timeout - please try again');
      }

      console.log('✅ File processed successfully');

      // Call Gemini directly since the AI service has MIME type issues with images
      console.log('🤖 Calling Gemini directly for analysis...');

      const analysisPrompt = this.buildBusinessCardAnalysisPrompt();
      const systemPrompt = this.getSystemPrompt();

      // Call Gemini directly with proper image handling using the correct API structure
      const result = await this.genAI.models.generateContent({
        model: process.env.GOOGLE_AI_MODEL || 'gemini-1.5-flash',
        contents: [{
          role: 'user',
          parts: [
            {
              fileData: {
                fileUri: file.uri!,
                mimeType: request.file.type || 'image/jpeg'
              }
            },
            {
              text: `${systemPrompt}\n\n${analysisPrompt}`
            }
          ]
        }],
        config: {
          maxOutputTokens: 1000,
          temperature: 0.1,
        }
      });

      const response = {
        content: result.candidates?.[0]?.content?.parts?.[0]?.text || ''
      };

      console.log('✅ AI analysis completed');

      // Parse the AI response
      console.log('📝 Raw AI response content:', response.content);
      const analysis = this.parseBusinessCardResponse(response.content);

      console.log('📊 Final analysis result:', {
        firstName: analysis.firstName,
        lastName: analysis.lastName,
        email: analysis.email,
        phone: analysis.phone,
        company: analysis.company,
        confidence: analysis.confidence
      });

      // Clean up the uploaded file from Gemini
      try {
        await this.genAI.files.delete({ name: file.name! });
        console.log('✅ Temporary file cleaned up');
      } catch (error) {
        console.warn('⚠️ Failed to clean up Gemini file:', error);
      }

      const processingTime = Date.now() - startTime;
      console.log(`✅ Business card analysis completed in ${processingTime}ms`);

      return {
        ...analysis,
        processingTime
      };

    } catch (error) {
      console.error('❌ Business card analysis error:', error);

      // Return fallback analysis
      return this.getFallbackAnalysis(request.file, Date.now() - startTime);
    }
  }

  /**
   * Upload file to Gemini API
   */
  private async uploadFileToGemini(file: File) {
    const buffer = Buffer.from(await file.arrayBuffer());
    const tempPath = path.join(os.tmpdir(), `business_card_${Date.now()}_${file.name.replace(/[^a-zA-Z0-9.-]/g, '_')}`);

    try {
      console.log(`📝 Writing file to temp path: ${tempPath}`);
      await fs.writeFile(tempPath, buffer);

      console.log(`📤 Uploading to Gemini API...`);
      // Upload to Gemini using file path - match the existing pattern
      const uploadResponse = await this.genAI.files.upload({
        file: tempPath,
        config: {
          mimeType: file.type || 'image/jpeg',
          displayName: `business_card_${Date.now()}_${file.name}`
        }
      });

      console.log(`✅ Upload response received: ${uploadResponse.name}`);

      // Clean up temporary file
      try {
        await fs.unlink(tempPath);
        console.log('✅ Temporary file cleaned up');
      } catch (error) {
        console.warn('⚠️ Failed to clean up temporary file:', error);
      }

      return uploadResponse;
    } catch (error) {
      console.error('❌ Upload error:', error);
      // Clean up temporary file on error
      try {
        await fs.unlink(tempPath);
      } catch (unlinkError) {
        console.warn('⚠️ Failed to clean up temporary file after error:', unlinkError);
      }
      throw new Error(`Failed to upload file to Gemini: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Check if file is a valid image type
   */
  private isValidImageFile(file: File): boolean {
    const validTypes = [
      'image/jpeg',
      'image/jpg', 
      'image/png',
      'image/webp',
      'image/gif',
      'image/bmp',
      'image/tiff'
    ];
    return validTypes.includes(file.type.toLowerCase());
  }

  /**
   * Build the analysis prompt for business card extraction
   */
  private buildBusinessCardAnalysisPrompt(): string {
    return `
Analyze this business card image and extract contact information. Focus especially on identifying the person's name.

Return ONLY a JSON object with these exact fields:

{
  "firstName": "string",
  "lastName": "string",
  "email": "string or null",
  "phone": "string or null",
  "company": "string or null",
  "jobTitle": "string or null",
  "website": "string or null",
  "address": "string or null",
  "confidence": number (0-100),
  "rawText": "string - all text found on the card"
}

CRITICAL INSTRUCTIONS FOR NAME EXTRACTION:
1. The person's name is usually the LARGEST or most prominent text on the card
2. Names are typically at the TOP of the card or in the center
3. Look for patterns like "John Smith", "Mary Johnson", "Dr. Sarah Wilson"
4. The name is usually NOT the company name - distinguish between person and organization
5. Split full names into firstName and lastName (e.g., "John Smith" → firstName: "John", lastName: "Smith")
6. Handle titles like "Dr.", "Mr.", "Ms." by removing them from the name
7. For names like "John A. Smith", use "John A." as firstName and "Smith" as lastName

DETAILED EXTRACTION RULES:
- Email: Look for @ symbols and <NAME_EMAIL>
- Phone: Extract ALL phone numbers (office, mobile, fax) - use the primary/first one found
- Company: Look for organization names, often with "Inc", "LLC", "Corp", "Ltd", "Company"
- Job Title: Extract position/role like "Manager", "Director", "CEO", "Engineer", "Sales Rep", "VP"
- Website: Look for www.company.com, company.com, or https://company.com patterns (NOT email addresses)
- Address: Extract full address including street, city, state, zip code if present

PRIORITY ORDER FOR EXTRACTION:
1. PERSON'S NAME (most important - usually largest text)
2. JOB TITLE (usually under the name)
3. COMPANY NAME (organization, not person)
4. CONTACT INFO (email, phone)
5. WEBSITE (company website)
6. ADDRESS (physical location)

FORMATTING RULES:
- Return ONLY valid JSON, no markdown or extra text
- Use null (not empty strings) for missing information
- Confidence should reflect how clearly you can read the text (0-100)
- Include ALL readable text in rawText field for debugging

EXAMPLES:
Card with "JOHN SMITH" (large text), "Marketing Manager" (smaller), "Acme Corp" (company):
→ firstName: "John", lastName: "Smith", jobTitle: "Marketing Manager", company: "Acme Corp"

Card with "Dr. Sarah Wilson", "Chief Technology Officer", "TechStart Inc", "<EMAIL>":
→ firstName: "Sarah", lastName: "Wilson", jobTitle: "Chief Technology Officer", company: "TechStart Inc", email: "<EMAIL>"

Card with "Mike Johnson", "Sales Director", "Global Solutions LLC", "www.globalsolutions.com", "************":
→ firstName: "Mike", lastName: "Johnson", jobTitle: "Sales Director", company: "Global Solutions LLC", website: "www.globalsolutions.com", phone: "************"

IMPORTANT DISTINCTIONS:
- "Marketing Manager" = jobTitle (describes the person's role)
- "Acme Corporation" = company (the organization)
- "John Smith" = person's name (firstName + lastName)
- "<EMAIL>" = email (has @ symbol)
- "www.acme.com" = website (company's web address)
- "123 Main St, City, State 12345" = address (physical location)
`;
  }

  /**
   * Get system prompt for business card analysis
   */
  private getSystemPrompt(): string {
    return `You are an expert at analyzing business card images and extracting contact information with high accuracy.
You specialize in OCR (Optical Character Recognition) and understanding business card layouts.
Always return valid JSON responses and be precise with data extraction.`;
  }

  /**
   * Parse the AI response and extract business card information
   */
  private parseBusinessCardResponse(content: string): Omit<BusinessCardAnalysisResponse, 'processingTime'> {
    try {
      console.log('🔍 Raw AI response:', content);

      // Clean the content - sometimes AI returns markdown or extra text
      let cleanContent = content.trim();

      // Remove markdown code blocks if present
      if (cleanContent.startsWith('```json')) {
        cleanContent = cleanContent.replace(/```json\s*/, '').replace(/```\s*$/, '');
      } else if (cleanContent.startsWith('```')) {
        cleanContent = cleanContent.replace(/```\s*/, '').replace(/```\s*$/, '');
      }

      // Find JSON object in the response
      const jsonMatch = cleanContent.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        cleanContent = jsonMatch[0];
      }

      const parsed = JSON.parse(cleanContent);
      console.log('✅ Parsed JSON:', parsed);

      // Extract and clean the data
      const result = {
        firstName: this.cleanString(parsed.firstName) || '',
        lastName: this.cleanString(parsed.lastName) || '',
        email: this.validateEmail(parsed.email),
        phone: this.cleanPhone(parsed.phone),
        company: this.cleanString(parsed.company),
        jobTitle: this.cleanString(parsed.jobTitle),
        website: this.validateWebsite(parsed.website),
        address: this.cleanString(parsed.address),
        confidence: Math.min(Math.max(parsed.confidence || 0, 0), 100),
        rawText: parsed.rawText || content
      };

      console.log('📊 Extracted data:', result);

      // If no names were extracted from JSON, try to extract from rawText
      if (!result.firstName && !result.lastName && result.rawText) {
        console.log('⚠️ No names in JSON, trying text extraction...');
        const textExtraction = this.extractFromText(result.rawText);
        if (textExtraction.firstName || textExtraction.lastName) {
          result.firstName = textExtraction.firstName;
          result.lastName = textExtraction.lastName;
          console.log(`✅ Extracted names from text: ${result.firstName} ${result.lastName}`);
        }
      }

      return result;
    } catch (error) {
      console.warn('❌ Failed to parse JSON response, attempting text extraction:', error);
      console.log('📄 Content that failed to parse:', content);

      // Fallback: try to extract information from text
      return this.extractFromText(content);
    }
  }

  /**
   * Extract information from text when JSON parsing fails
   */
  private extractFromText(text: string): Omit<BusinessCardAnalysisResponse, 'processingTime'> {
    const lines = text.split('\n').map(line => line.trim()).filter(Boolean);

    // Try to find patterns
    const emailPattern = /[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/;
    const phonePattern = /[\+]?[1-9][\d\s\-\(\)\.]{7,15}/;
    const websitePattern = /(https?:\/\/)?(www\.)?[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/;

    // Enhanced name pattern - look for capitalized words that could be names
    const namePattern = /^[A-Z][a-z]+ [A-Z][a-z]+/; // Basic "FirstName LastName" pattern
    const titlePattern = /^(Dr|Mr|Ms|Mrs|Prof|Professor)\.?\s+/i;

    let email = null;
    let phone = null;
    let website = null;
    let firstName = '';
    let lastName = '';
    let company = null;
    let jobTitle = null;

    // Look for name in the first few lines (names are usually at the top)
    for (let i = 0; i < Math.min(5, lines.length); i++) {
      const line = lines[i];

      // Skip lines that are clearly not names
      if (line.includes('@') || line.includes('www') || line.includes('.com') ||
          /\d{3}/.test(line) || line.toLowerCase().includes('inc') ||
          line.toLowerCase().includes('llc') || line.toLowerCase().includes('corp')) {
        continue;
      }

      // Remove titles and check for name pattern
      const cleanLine = line.replace(titlePattern, '').trim();

      if (namePattern.test(cleanLine) && !firstName && !lastName) {
        const nameParts = cleanLine.split(/\s+/);
        if (nameParts.length >= 2) {
          firstName = nameParts[0];
          lastName = nameParts.slice(1).join(' '); // Handle middle names
          console.log(`📝 Extracted name from text: ${firstName} ${lastName}`);
          break;
        }
      }
    }

    // Extract other information
    for (const line of lines) {
      if (!email && emailPattern.test(line)) {
        const match = line.match(emailPattern);
        email = match ? match[0] : null;
      }
      if (!phone && phonePattern.test(line)) {
        const match = line.match(phonePattern);
        phone = match ? match[0] : null;
      }
      if (!website && websitePattern.test(line)) {
        const match = line.match(websitePattern);
        website = match ? match[0] : null;
      }

      // Look for job titles (more comprehensive patterns)
      if (!jobTitle && /\b(manager|director|ceo|cto|cfo|president|vice|senior|junior|lead|head|chief|engineer|developer|analyst|coordinator|specialist|consultant|supervisor|administrator|executive|officer|representative|rep|sales|marketing|operations|finance|hr|human resources)\b/i.test(line)) {
        jobTitle = line;
      }

      // Look for company names (lines with Inc, LLC, Corp, etc.)
      if (!company && /\b(inc|llc|corp|ltd|company|co\.|corporation|group|associates|partners|solutions|systems|technologies|tech|consulting|services)\b/i.test(line)) {
        company = line;
      }
    }

    // Look for address (try to find lines with address patterns)
    let address = null;
    for (const line of lines) {
      // Address patterns: street numbers, zip codes, state abbreviations
      if (/\b\d+\s+[A-Za-z\s]+(street|st|avenue|ave|road|rd|drive|dr|lane|ln|boulevard|blvd|way|place|pl)\b/i.test(line) ||
          /\b[A-Z]{2}\s+\d{5}(-\d{4})?\b/.test(line) ||
          /\b\d{5}(-\d{4})?\b/.test(line)) {
        address = line;
        break;
      }
    }

    return {
      firstName: firstName || '',
      lastName: lastName || '',
      email: this.validateEmail(email),
      phone: this.cleanPhone(phone),
      company: this.cleanString(company),
      jobTitle: this.cleanString(jobTitle),
      website: this.validateWebsite(website),
      address: this.cleanString(address),
      confidence: firstName && lastName ? 50 : 30, // Higher confidence if we found names
      rawText: text
    };
  }

  /**
   * Clean and validate string fields
   */
  private cleanString(value: any): string | null {
    if (!value || typeof value !== 'string') return null;
    const cleaned = value.trim();
    return cleaned.length > 0 ? cleaned : null;
  }

  /**
   * Validate and clean email addresses
   */
  private validateEmail(email: any): string | null {
    if (!email || typeof email !== 'string') return null;
    const cleaned = email.trim().toLowerCase();
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(cleaned) ? cleaned : null;
  }

  /**
   * Clean and format phone numbers
   */
  private cleanPhone(phone: any): string | null {
    if (!phone || typeof phone !== 'string') return null;
    // Remove all non-digit characters except + at the beginning
    const cleaned = phone.replace(/[^\d+]/g, '');
    if (cleaned.length < 7) return null; // Too short to be a valid phone
    return cleaned;
  }

  /**
   * Validate and clean website URLs
   */
  private validateWebsite(website: any): string | null {
    if (!website || typeof website !== 'string') return null;
    let cleaned = website.trim().toLowerCase();

    // Add protocol if missing
    if (!cleaned.startsWith('http://') && !cleaned.startsWith('https://')) {
      cleaned = 'https://' + cleaned;
    }

    try {
      new URL(cleaned);
      return cleaned;
    } catch {
      return null;
    }
  }

  /**
   * Get fallback analysis when AI processing fails
   */
  private getFallbackAnalysis(file: File, processingTime: number): BusinessCardAnalysisResponse {
    return {
      firstName: '',
      lastName: '',
      email: null,
      phone: null,
      company: null,
      jobTitle: null,
      website: null,
      address: null,
      confidence: 0,
      processingTime,
      rawText: `Failed to analyze business card: ${file.name}`
    };
  }
}

// Export singleton instance
export const businessCardAnalysisService = new BusinessCardAnalysisService();
