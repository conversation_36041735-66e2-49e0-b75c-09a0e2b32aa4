import { NextResponse } from 'next/server';
import { z } from 'zod';
import { withPermission, AuthenticatedRequest } from '@/lib/permission-middleware';
import { TeamManagementService } from '@/services/team-management';
import { PermissionAction, PermissionResource } from '@/types';

const createTeamSchema = z.object({
  name: z.string().min(1, 'Team name is required').max(100, 'Team name too long'),
  description: z.string().optional(),
  color: z.string().optional(),
  managerId: z.string().optional(),
  parentTeamId: z.string().optional(),
  settings: z.record(z.any()).optional()
});

const updateTeamSchema = z.object({
  name: z.string().min(1, 'Team name is required').max(100, 'Team name too long').optional(),
  description: z.string().optional(),
  color: z.string().optional(),
  managerId: z.string().optional(),
  parentTeamId: z.string().optional(),
  isActive: z.boolean().optional(),
  settings: z.record(z.any()).optional()
});

/**
 * GET /api/teams
 * Get all teams for the current tenant
 */
export const GET = withPermission({
  resource: PermissionResource.USERS,
  action: PermissionAction.READ,
  allowSuperAdmin: true
})(async (req) => {
  try {
    const { searchParams } = new URL(req.url);
    const includeInactive = searchParams.get('includeInactive') === 'true';
    const includeMembers = searchParams.get('includeMembers') === 'true';

    const teamService = new TeamManagementService();
    const teams = await teamService.getTeams(req.tenantId, {
      includeInactive,
      includeMembers
    });

    return NextResponse.json({
      success: true,
      data: {
        teams,
        count: teams.length
      }
    });
  } catch (error) {
    console.error('Get teams error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch teams' },
      { status: 500 }
    );
  }
});

/**
 * POST /api/teams
 * Create a new team
 */
export const POST = withPermission({
  resource: PermissionResource.USERS,
  action: PermissionAction.MANAGE,
  allowSuperAdmin: true
})(async (req) => {
  try {
    const body = await req.json();
    const validatedData = createTeamSchema.parse(body);

    const teamService = new TeamManagementService();
    const team = await teamService.createTeam(
      req.tenantId,
      validatedData,
      req.userId
    );

    return NextResponse.json({
      success: true,
      data: { team },
      message: 'Team created successfully'
    }, { status: 201 });

  } catch (error) {
    console.error('Create team error:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          error: 'Validation failed',
          details: error.errors.map(e => ({
            field: e.path.join('.'),
            message: e.message
          }))
        },
        { status: 400 }
      );
    }

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to create team' },
      { status: 500 }
    );
  }
});

/**
 * PUT /api/teams
 * Update an existing team
 */
export const PUT = withPermission({
  resource: PermissionResource.USERS,
  action: PermissionAction.MANAGE,
  allowSuperAdmin: true
})(async (req) => {
  try {
    const body = await req.json();
    const { teamId, ...updateData } = body;

    if (!teamId) {
      return NextResponse.json(
        { error: 'Team ID is required' },
        { status: 400 }
      );
    }

    const validatedData = updateTeamSchema.parse(updateData);

    const teamService = new TeamManagementService();
    const team = await teamService.updateTeam(
      teamId,
      req.tenantId,
      validatedData,
      req.userId
    );

    return NextResponse.json({
      success: true,
      data: { team },
      message: 'Team updated successfully'
    });

  } catch (error) {
    console.error('Update team error:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          error: 'Validation failed',
          details: error.errors.map(e => ({
            field: e.path.join('.'),
            message: e.message
          }))
        },
        { status: 400 }
      );
    }

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to update team' },
      { status: 500 }
    );
  }
});

/**
 * DELETE /api/teams
 * Delete a team
 */
export const DELETE = withPermission({
  resource: PermissionResource.USERS,
  action: PermissionAction.MANAGE,
  allowSuperAdmin: true
})(async (req) => {
  try {
    const { searchParams } = new URL(req.url);
    const teamId = searchParams.get('teamId');

    if (!teamId) {
      return NextResponse.json(
        { error: 'Team ID is required' },
        { status: 400 }
      );
    }

    const teamService = new TeamManagementService();
    await teamService.deleteTeam(teamId, req.tenantId, req.userId);

    return NextResponse.json({
      success: true,
      message: 'Team deleted successfully'
    });

  } catch (error) {
    console.error('Delete team error:', error);

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to delete team' },
      { status: 500 }
    );
  }
});
