import { prisma } from '@/lib/prisma';
import { UnifiedNotificationService } from './unified-notification-service';

export interface CreateTeamData {
  name: string;
  description?: string;
  color?: string;
  managerId?: string;
  parentTeamId?: string;
  settings?: Record<string, any>;
}

export interface UpdateTeamData {
  name?: string;
  description?: string;
  color?: string;
  managerId?: string;
  parentTeamId?: string;
  isActive?: boolean;
  settings?: Record<string, any>;
}

export interface TeamWithMembers {
  id: string;
  name: string;
  description?: string;
  color?: string;
  managerId?: string;
  parentTeamId?: string;
  isActive: boolean;
  memberCount: number;
  manager?: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
  };
  members?: Array<{
    id: string;
    firstName: string;
    lastName: string;
    email: string;
    role: string;
    joinedAt: Date;
  }>;
  childTeams?: TeamWithMembers[];
}

export interface BulkOperationResult {
  successful: string[];
  failed: Array<{
    id: string;
    error: string;
  }>;
}

export class TeamManagementService {
  private notificationService = new UnifiedNotificationService();

  /**
   * Create a new team
   */
  async createTeam(
    tenantId: string,
    teamData: CreateTeamData,
    createdBy: string
  ): Promise<TeamWithMembers> {
    return await prisma.$transaction(async (tx) => {
      // Validate parent team exists if specified
      if (teamData.parentTeamId) {
        const parentTeam = await tx.team.findUnique({
          where: {
            id: teamData.parentTeamId,
            tenantId
          }
        });

        if (!parentTeam) {
          throw new Error('Parent team not found');
        }
      }

      // Validate manager exists if specified
      if (teamData.managerId) {
        const manager = await tx.tenantUser.findUnique({
          where: {
            tenantId_userId: {
              tenantId,
              userId: teamData.managerId
            }
          }
        });

        if (!manager) {
          throw new Error('Manager not found in tenant');
        }
      }

      // Create the team
      const team = await tx.team.create({
        data: {
          tenantId,
          name: teamData.name,
          description: teamData.description,
          color: teamData.color || '#6B7280',
          managerId: teamData.managerId,
          parentTeamId: teamData.parentTeamId,
          settings: teamData.settings || {},
          createdById: createdBy
        },
        include: {
          manager: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true
            }
          },
          _count: {
            select: { members: true }
          }
        }
      });

      // Log team creation
      await tx.auditLog.create({
        data: {
          tenantId,
          userId: createdBy,
          action: 'TEAM_CREATED',
          resourceType: 'team',
          resourceId: team.id,
          newValues: {
            name: team.name,
            description: team.description,
            managerId: team.managerId,
            parentTeamId: team.parentTeamId
          }
        }
      });

      const result = {
        id: team.id,
        name: team.name,
        description: team.description || undefined,
        color: team.color || undefined,
        managerId: team.managerId || undefined,
        parentTeamId: team.parentTeamId || undefined,
        isActive: team.isActive,
        memberCount: team._count.members,
        manager: team.manager || undefined
      };

      // Trigger team creation notification
      await this.triggerTeamCreationNotification(result, tenantId, createdBy);

      return result;
    });
  }

  /**
   * Update an existing team
   */
  async updateTeam(
    teamId: string,
    tenantId: string,
    updateData: UpdateTeamData,
    updatedBy: string
  ): Promise<TeamWithMembers> {
    return await prisma.$transaction(async (tx) => {
      // Get current team
      const currentTeam = await tx.team.findUnique({
        where: { id: teamId, tenantId }
      });

      if (!currentTeam) {
        throw new Error('Team not found');
      }

      // Validate parent team if specified
      if (updateData.parentTeamId) {
        const parentTeam = await tx.team.findUnique({
          where: {
            id: updateData.parentTeamId,
            tenantId
          }
        });

        if (!parentTeam) {
          throw new Error('Parent team not found');
        }

        // Prevent circular hierarchy
        if (await this.wouldCreateCircularHierarchy(teamId, updateData.parentTeamId, tenantId, tx)) {
          throw new Error('Cannot create circular team hierarchy');
        }
      }

      // Validate manager if specified
      if (updateData.managerId) {
        const manager = await tx.tenantUser.findUnique({
          where: {
            tenantId_userId: {
              tenantId,
              userId: updateData.managerId
            }
          }
        });

        if (!manager) {
          throw new Error('Manager not found in tenant');
        }
      }

      // Update team
      const updatedTeam = await tx.team.update({
        where: { id: teamId },
        data: {
          name: updateData.name,
          description: updateData.description,
          color: updateData.color,
          managerId: updateData.managerId,
          parentTeamId: updateData.parentTeamId,
          isActive: updateData.isActive,
          settings: updateData.settings
        },
        include: {
          manager: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true
            }
          },
          _count: {
            select: { members: true }
          }
        }
      });

      // Log team update
      await tx.auditLog.create({
        data: {
          tenantId,
          userId: updatedBy,
          action: 'TEAM_UPDATED',
          resourceType: 'team',
          resourceId: teamId,
          oldValues: {
            name: currentTeam.name,
            description: currentTeam.description,
            managerId: currentTeam.managerId,
            parentTeamId: currentTeam.parentTeamId,
            isActive: currentTeam.isActive
          },
          newValues: {
            name: updatedTeam.name,
            description: updatedTeam.description,
            managerId: updatedTeam.managerId,
            parentTeamId: updatedTeam.parentTeamId,
            isActive: updatedTeam.isActive
          }
        }
      });

      return {
        id: updatedTeam.id,
        name: updatedTeam.name,
        description: updatedTeam.description || undefined,
        color: updatedTeam.color || undefined,
        managerId: updatedTeam.managerId || undefined,
        parentTeamId: updatedTeam.parentTeamId || undefined,
        isActive: updatedTeam.isActive,
        memberCount: updatedTeam._count.members,
        manager: updatedTeam.manager || undefined
      };
    });
  }

  /**
   * Delete a team
   */
  async deleteTeam(teamId: string, tenantId: string, deletedBy: string): Promise<void> {
    await prisma.$transaction(async (tx) => {
      const team = await tx.team.findUnique({
        where: { id: teamId, tenantId },
        include: {
          _count: {
            select: { 
              members: true,
              childTeams: true
            }
          }
        }
      });

      if (!team) {
        throw new Error('Team not found');
      }

      // Check if team has members
      if (team._count.members > 0) {
        throw new Error('Cannot delete team with members. Please move or remove all members first.');
      }

      // Check if team has child teams
      if (team._count.childTeams > 0) {
        throw new Error('Cannot delete team with child teams. Please move or delete child teams first.');
      }

      // Delete the team
      await tx.team.delete({
        where: { id: teamId }
      });

      // Log team deletion
      await tx.auditLog.create({
        data: {
          tenantId,
          userId: deletedBy,
          action: 'TEAM_DELETED',
          resourceType: 'team',
          resourceId: teamId,
          oldValues: {
            name: team.name,
            description: team.description
          }
        }
      });
    });
  }

  /**
   * Get all teams for a tenant
   */
  async getTeams(
    tenantId: string,
    options: {
      includeInactive?: boolean;
      includeMembers?: boolean;
    } = {}
  ): Promise<TeamWithMembers[]> {
    const teams = await prisma.team.findMany({
      where: {
        tenantId,
        ...(options.includeInactive ? {} : { isActive: true })
      },
      include: {
        manager: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true
          }
        },
        members: options.includeMembers ? {
          where: {
            user: {
              // Exclude platform admins from team member lists
              isPlatformAdmin: false
            }
          },
          include: {
            user: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true
              }
            }
          }
        } : false,
        _count: {
          select: { members: true }
        }
      },
      orderBy: { name: 'asc' }
    });

    return teams.map(team => ({
      id: team.id,
      name: team.name,
      description: team.description || undefined,
      color: team.color || undefined,
      managerId: team.managerId || undefined,
      parentTeamId: team.parentTeamId || undefined,
      isActive: team.isActive,
      memberCount: team._count.members,
      manager: team.manager || undefined,
      members: options.includeMembers && team.members ?
        team.members.map(member => ({
          id: member.user.id,
          firstName: member.user.firstName || '',
          lastName: member.user.lastName || '',
          email: member.user.email,
          role: member.role,
          joinedAt: member.joinedAt
        })) : undefined
    }));
  }

  /**
   * Get a specific team by ID
   */
  async getTeamById(teamId: string, tenantId: string): Promise<TeamWithMembers | null> {
    const team = await prisma.team.findUnique({
      where: { id: teamId, tenantId },
      include: {
        manager: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true
          }
        },
        parentTeam: {
          select: {
            id: true,
            name: true
          }
        },
        members: {
          where: {
            user: {
              // Exclude platform admins from team member lists
              isPlatformAdmin: false
            }
          },
          include: {
            user: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
                avatarUrl: true
              }
            }
          }
        },
        _count: {
          select: { members: true }
        }
      }
    });

    if (!team) {
      return null;
    }

    return {
      id: team.id,
      name: team.name,
      description: team.description || undefined,
      color: team.color || undefined,
      managerId: team.managerId || undefined,
      parentTeamId: team.parentTeamId || undefined,
      isActive: team.isActive,
      memberCount: team._count.members,
      manager: team.manager || undefined,
      parentTeam: team.parentTeam || undefined,
      members: team.members.map(member => ({
        id: member.user.id,
        firstName: member.user.firstName || '',
        lastName: member.user.lastName || '',
        email: member.user.email,
        avatarUrl: member.user.avatarUrl,
        role: member.role,
        joinedAt: member.joinedAt
      })),
      createdAt: team.createdAt,
      updatedAt: team.updatedAt
    };
  }

  /**
   * Get team members
   */
  async getTeamMembers(teamId: string, tenantId: string) {
    const team = await prisma.team.findUnique({
      where: { id: teamId, tenantId }
    });

    if (!team) {
      throw new Error('Team not found');
    }

    const members = await prisma.tenantUser.findMany({
      where: {
        tenantId,
        teamId,
        user: {
          // Exclude platform admins from team member lists
          isPlatformAdmin: false
        }
      },
      include: {
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            avatarUrl: true,
            lastLogin: true
          }
        }
      },
      orderBy: { joinedAt: 'desc' }
    });

    return members.map(member => ({
      id: member.user.id,
      firstName: member.user.firstName || '',
      lastName: member.user.lastName || '',
      email: member.user.email,
      avatarUrl: member.user.avatarUrl,
      role: member.role,
      department: member.department,
      jobTitle: member.jobTitle,
      joinedAt: member.joinedAt,
      lastLogin: member.user.lastLogin,
      status: member.status
    }));
  }

  /**
   * Add member to team
   */
  async addMemberToTeam(
    teamId: string,
    tenantId: string,
    userId: string,
    addedBy: string,
    role?: string
  ): Promise<void> {
    await prisma.$transaction(async (tx) => {
      // Validate team exists
      const team = await tx.team.findUnique({
        where: { id: teamId, tenantId }
      });

      if (!team) {
        throw new Error('Team not found');
      }

      // Validate user exists in tenant
      const tenantUser = await tx.tenantUser.findUnique({
        where: {
          tenantId_userId: {
            tenantId,
            userId
          }
        }
      });

      if (!tenantUser) {
        throw new Error('User not found in tenant');
      }

      // Check if user is already in a team
      if (tenantUser.teamId) {
        throw new Error('User is already assigned to a team');
      }

      // Add user to team
      await tx.tenantUser.update({
        where: {
          tenantId_userId: {
            tenantId,
            userId
          }
        },
        data: {
          teamId,
          role: role || tenantUser.role
        }
      });

      // Log team member addition
      await tx.auditLog.create({
        data: {
          tenantId,
          userId: addedBy,
          action: 'TEAM_MEMBER_ADDED',
          resourceType: 'team',
          resourceId: teamId,
          newValues: {
            userId,
            teamName: team.name,
            role: role || tenantUser.role
          }
        }
      });
    });

    // Trigger team member added notification
    await this.triggerTeamMemberAddedNotification(teamId, tenantId, userId, addedBy, role);
  }

  // Private helper method
  private async wouldCreateCircularHierarchy(
    teamId: string,
    parentTeamId: string,
    tenantId: string,
    tx: any
  ): Promise<boolean> {
    // Check if parentTeamId is a descendant of teamId
    let currentParentId = parentTeamId;
    const visited = new Set<string>();

    while (currentParentId && !visited.has(currentParentId)) {
      if (currentParentId === teamId) {
        return true; // Circular hierarchy detected
      }

      visited.add(currentParentId);

      const parentTeam = await tx.team.findUnique({
        where: { id: currentParentId, tenantId }
      });

      currentParentId = parentTeam?.parentTeamId || null;
    }

    return false;
  }

  /**
   * Remove member from team
   */
  async removeMemberFromTeam(
    teamId: string,
    tenantId: string,
    userId: string,
    removedBy: string
  ): Promise<void> {
    await prisma.$transaction(async (tx) => {
      // Validate team exists
      const team = await tx.team.findUnique({
        where: { id: teamId, tenantId }
      });

      if (!team) {
        throw new Error('Team not found');
      }

      // Validate user is in the team
      const tenantUser = await tx.tenantUser.findUnique({
        where: {
          tenantId_userId: {
            tenantId,
            userId
          }
        }
      });

      if (!tenantUser || tenantUser.teamId !== teamId) {
        throw new Error('User is not a member of this team');
      }

      // Remove user from team
      await tx.tenantUser.update({
        where: {
          tenantId_userId: {
            tenantId,
            userId
          }
        },
        data: {
          teamId: null
        }
      });

      // Log team member removal
      await tx.auditLog.create({
        data: {
          tenantId,
          userId: removedBy,
          action: 'TEAM_MEMBER_REMOVED',
          resourceType: 'team',
          resourceId: teamId,
          oldValues: {
            userId,
            teamName: team.name
          }
        }
      });
    });
  }

  /**
   * Add multiple members to team
   */
  async addMembersToTeam(
    teamId: string,
    tenantId: string,
    userIds: string[],
    addedBy: string,
    role?: string
  ): Promise<BulkOperationResult> {
    const results: BulkOperationResult = {
      successful: [],
      failed: []
    };

    for (const userId of userIds) {
      try {
        await this.addMemberToTeam(teamId, tenantId, userId, addedBy, role);
        results.successful.push(userId);
      } catch (error) {
        results.failed.push({
          id: userId,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }

    return results;
  }

  /**
   * Trigger team creation notification
   */
  private async triggerTeamCreationNotification(
    team: TeamWithMembers,
    tenantId: string,
    createdBy: string
  ): Promise<void> {
    try {
      const targetUsers = [];

      // Notify team manager if assigned
      if (team.managerId) {
        targetUsers.push(team.managerId);
      }

      // Notify administrators and managers
      const admins = await prisma.tenantUser.findMany({
        where: {
          tenantId,
          OR: [
            { role: { in: ['Admin', 'Manager', 'Team Lead'] } },
            { isTenantAdmin: true }
          ],
          status: 'active'
        },
        select: { userId: true }
      });

      targetUsers.push(...admins.map(admin => admin.userId));

      // Notify the creator if different from manager
      if (createdBy !== team.managerId) {
        targetUsers.push(createdBy);
      }

      for (const userId of [...new Set(targetUsers)]) {
        await this.notificationService.createNotification({
          tenantId,
          userId,
          type: 'team_created',
          category: 'system',
          title: 'New Team Created',
          message: `Team "${team.name}" has been created`,
          data: {
            teamId: team.id,
            teamName: team.name,
            teamDescription: team.description,
            teamManagerId: team.managerId,
            teamManagerName: team.manager ?
              `${team.manager.firstName} ${team.manager.lastName}` : null,
            parentTeamId: team.parentTeamId,
            createdBy
          },
          actionUrl: `/admin/teams/${team.id}`,
          actionLabel: 'View Team'
        });
      }

      // Notify team manager specifically if assigned
      if (team.managerId && team.managerId !== createdBy) {
        await this.notificationService.createNotification({
          tenantId,
          userId: team.managerId,
          type: 'team_manager_assigned',
          category: 'system',
          title: 'You\'ve Been Assigned as Team Manager',
          message: `You have been assigned as the manager of team "${team.name}"`,
          data: {
            teamId: team.id,
            teamName: team.name,
            teamDescription: team.description,
            assignedBy: createdBy
          },
          actionUrl: `/teams/${team.id}`,
          actionLabel: 'View Team'
        });
      }

    } catch (error) {
      console.error('Failed to send team creation notification:', error);
      // Don't throw error to avoid breaking the main operation
    }
  }

  /**
   * Trigger team member added notification
   */
  async triggerTeamMemberAddedNotification(
    teamId: string,
    tenantId: string,
    userId: string,
    addedBy: string,
    role?: string
  ): Promise<void> {
    try {
      // Get team and user information
      const [team, user] = await Promise.all([
        prisma.team.findUnique({
          where: { id: teamId, tenantId },
          include: {
            manager: {
              select: {
                id: true,
                firstName: true,
                lastName: true
              }
            }
          }
        }),
        prisma.user.findUnique({
          where: { id: userId },
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true
          }
        })
      ]);

      if (!team || !user) return;

      const targetUsers = [];

      // Notify the user who was added
      targetUsers.push(userId);

      // Notify team manager
      if (team.managerId && team.managerId !== userId) {
        targetUsers.push(team.managerId);
      }

      // Notify administrators
      const admins = await prisma.tenantUser.findMany({
        where: {
          tenantId,
          OR: [
            { role: { in: ['Admin', 'Manager'] } },
            { isTenantAdmin: true }
          ],
          status: 'active',
          userId: { notIn: [userId, team.managerId].filter(Boolean) }
        },
        select: { userId: true }
      });

      targetUsers.push(...admins.map(admin => admin.userId));

      for (const targetUserId of [...new Set(targetUsers)]) {
        const isTargetUser = targetUserId === userId;

        await this.notificationService.createNotification({
          tenantId,
          userId: targetUserId,
          type: 'team_member_added',
          category: 'system',
          title: isTargetUser ? 'Added to Team' : 'New Team Member',
          message: isTargetUser
            ? `You have been added to team "${team.name}"`
            : `${user.firstName} ${user.lastName} has been added to team "${team.name}"`,
          data: {
            teamId: team.id,
            teamName: team.name,
            addedUserId: userId,
            addedUserName: `${user.firstName} ${user.lastName}`,
            addedUserEmail: user.email,
            userRole: role,
            addedBy,
            teamManagerId: team.managerId,
            teamManagerName: team.manager ?
              `${team.manager.firstName} ${team.manager.lastName}` : null
          },
          actionUrl: isTargetUser ? `/teams/${team.id}` : `/admin/teams/${team.id}`,
          actionLabel: 'View Team'
        });
      }

    } catch (error) {
      console.error('Failed to send team member added notification:', error);
      // Don't throw error to avoid breaking the main operation
    }
  }
}
