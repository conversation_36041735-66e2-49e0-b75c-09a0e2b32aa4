'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  Activity, 
  Database, 
  Clock, 
  Zap, 
  AlertTriangle, 
  CheckCircle,
  RefreshCw,
  TrendingUp,
  TrendingDown
} from 'lucide-react';

interface PerformanceMetrics {
  database: {
    responseTime: number;
    slowQueries: number;
    activeConnections: number;
    cacheHitRate: number;
  };
  api: {
    averageResponseTime: number;
    p95ResponseTime: number;
    errorRate: number;
    requestsPerMinute: number;
  };
  cache: {
    hitRate: number;
    size: number;
    evictions: number;
  };
  memory: {
    heapUsed: number;
    heapTotal: number;
    external: number;
  };
}

export function PerformanceDashboard() {
  const [metrics, setMetrics] = useState<PerformanceMetrics | null>(null);
  const [loading, setLoading] = useState(true);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  const fetchMetrics = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/admin/performance');
      const data = await response.json();
      
      if (data.success) {
        setMetrics(data.data);
        setLastUpdated(new Date());
      }
    } catch (error) {
      console.error('Failed to fetch performance metrics:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchMetrics();
    
    // Auto-refresh every 30 seconds
    const interval = setInterval(fetchMetrics, 30000);
    return () => clearInterval(interval);
  }, []);

  const getHealthStatus = (value: number, thresholds: { good: number; warning: number }) => {
    if (value <= thresholds.good) return { status: 'good', color: 'green' };
    if (value <= thresholds.warning) return { status: 'warning', color: 'yellow' };
    return { status: 'critical', color: 'red' };
  };

  const formatBytes = (bytes: number) => {
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    if (bytes === 0) return '0 Bytes';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  };

  if (loading && !metrics) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {Array.from({ length: 8 }).map((_, i) => (
          <Card key={i}>
            <CardContent className="p-6">
              <div className="animate-pulse">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-8 bg-gray-200 rounded w-1/2"></div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (!metrics) {
    return (
      <Card>
        <CardContent className="p-6 text-center">
          <AlertTriangle className="h-12 w-12 text-yellow-500 mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">Failed to Load Metrics</h3>
          <p className="text-muted-foreground mb-4">Unable to fetch performance data</p>
          <Button onClick={fetchMetrics}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Retry
          </Button>
        </CardContent>
      </Card>
    );
  }

  const dbHealth = getHealthStatus(metrics.database.responseTime, { good: 100, warning: 500 });
  const apiHealth = getHealthStatus(metrics.api.averageResponseTime, { good: 200, warning: 1000 });
  const memoryUsage = (metrics.memory.heapUsed / metrics.memory.heapTotal) * 100;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Performance Dashboard</h2>
          <p className="text-muted-foreground">
            Real-time system performance metrics
            {lastUpdated && (
              <span className="ml-2">
                • Last updated: {lastUpdated.toLocaleTimeString()}
              </span>
            )}
          </p>
        </div>
        <Button onClick={fetchMetrics} disabled={loading}>
          <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {/* Database Response Time */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Database Response</CardTitle>
            <Database className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.database.responseTime}ms</div>
            <Badge variant={dbHealth.status === 'good' ? 'default' : dbHealth.status === 'warning' ? 'secondary' : 'destructive'}>
              {dbHealth.status.toUpperCase()}
            </Badge>
          </CardContent>
        </Card>

        {/* API Response Time */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">API Response</CardTitle>
            <Zap className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.api.averageResponseTime}ms</div>
            <Badge variant={apiHealth.status === 'good' ? 'default' : apiHealth.status === 'warning' ? 'secondary' : 'destructive'}>
              {apiHealth.status.toUpperCase()}
            </Badge>
          </CardContent>
        </Card>

        {/* Cache Hit Rate */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Cache Hit Rate</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.cache.hitRate.toFixed(1)}%</div>
            <Progress value={metrics.cache.hitRate} className="mt-2" />
          </CardContent>
        </Card>

        {/* Memory Usage */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Memory Usage</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{memoryUsage.toFixed(1)}%</div>
            <Progress value={memoryUsage} className="mt-2" />
            <p className="text-xs text-muted-foreground mt-1">
              {formatBytes(metrics.memory.heapUsed)} / {formatBytes(metrics.memory.heapTotal)}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Metrics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Database Metrics */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Database className="h-5 w-5" />
              Database Performance
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-sm">Slow Queries</span>
              <Badge variant={metrics.database.slowQueries > 5 ? 'destructive' : 'default'}>
                {metrics.database.slowQueries}
              </Badge>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm">Active Connections</span>
              <span className="font-medium">{metrics.database.activeConnections}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm">Cache Hit Rate</span>
              <span className="font-medium">{metrics.database.cacheHitRate.toFixed(1)}%</span>
            </div>
          </CardContent>
        </Card>

        {/* API Metrics */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Zap className="h-5 w-5" />
              API Performance
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-sm">P95 Response Time</span>
              <span className="font-medium">{metrics.api.p95ResponseTime}ms</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm">Error Rate</span>
              <Badge variant={metrics.api.errorRate > 1 ? 'destructive' : 'default'}>
                {metrics.api.errorRate.toFixed(2)}%
              </Badge>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm">Requests/min</span>
              <span className="font-medium">{metrics.api.requestsPerMinute}</span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Performance Recommendations */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            Performance Recommendations
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            {metrics.database.responseTime > 500 && (
              <div className="flex items-center gap-2 text-sm text-yellow-600">
                <AlertTriangle className="h-4 w-4" />
                Database response time is high. Consider optimizing queries or adding indexes.
              </div>
            )}
            {metrics.cache.hitRate < 80 && (
              <div className="flex items-center gap-2 text-sm text-yellow-600">
                <AlertTriangle className="h-4 w-4" />
                Cache hit rate is low. Review caching strategy and TTL settings.
              </div>
            )}
            {memoryUsage > 80 && (
              <div className="flex items-center gap-2 text-sm text-red-600">
                <AlertTriangle className="h-4 w-4" />
                Memory usage is high. Consider increasing memory or optimizing memory usage.
              </div>
            )}
            {metrics.api.errorRate > 1 && (
              <div className="flex items-center gap-2 text-sm text-red-600">
                <AlertTriangle className="h-4 w-4" />
                API error rate is elevated. Check logs for recurring issues.
              </div>
            )}
            {metrics.database.responseTime <= 100 && metrics.cache.hitRate >= 90 && memoryUsage <= 70 && metrics.api.errorRate <= 0.5 && (
              <div className="flex items-center gap-2 text-sm text-green-600">
                <CheckCircle className="h-4 w-4" />
                All systems performing optimally!
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
