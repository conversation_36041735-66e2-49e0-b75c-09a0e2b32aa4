'use client';

import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  FileText, 
  File, 
  CheckCircle, 
  AlertCircle, 
  Upload,
  Search,
  Filter,
  Wand2,
  Loader2,
  X
} from 'lucide-react';
import { Input } from '@/components/ui/input';
import { FileSelectionData } from '@/types/proposal';
import { toast } from 'sonner';

interface CustomEnhancementDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  proposalId: string;
  sectionId: string;
  sectionContent: string;
  onEnhance: (customPrompt: string, selectedFileIds: string[]) => void;
}

export function CustomEnhancementDialog({
  open,
  onOpenChange,
  proposalId,
  sectionId,
  sectionContent,
  onEnhance
}: CustomEnhancementDialogProps) {
  const [customPrompt, setCustomPrompt] = useState('');
  const [selectedFileIds, setSelectedFileIds] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);
  const [files, setFiles] = useState<{
    supportedFiles: FileSelectionData[];
    unsupportedFiles: FileSelectionData[];
    selectedCount: number;
    totalFiles: number;
  }>({
    supportedFiles: [],
    unsupportedFiles: [],
    selectedCount: 0,
    totalFiles: 0
  });
  const [searchQuery, setSearchQuery] = useState('');
  const [showUnsupported, setShowUnsupported] = useState(false);

  // Filter files based on search query
  const filteredSupportedFiles = (files.supportedFiles || []).filter(file =>
    file.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    file.mimeType.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const filteredUnsupportedFiles = (files.unsupportedFiles || []).filter(file =>
    file.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    file.mimeType.toLowerCase().includes(searchQuery.toLowerCase())
  );

  useEffect(() => {
    if (open) {
      loadFiles();
    }
  }, [open, proposalId]);

  const loadFiles = async () => {
    setLoading(true);
    try {
      const response = await fetch(`/api/proposals/${proposalId}/files`);
      if (!response.ok) {
        throw new Error('Failed to load files');
      }

      const result = await response.json();

      // The API returns data wrapped in a 'data' object
      if (result.success && result.data) {
        setFiles({
          supportedFiles: result.data.supportedFiles || [],
          unsupportedFiles: result.data.unsupportedFiles || [],
          selectedCount: result.data.selectedCount || 0,
          totalFiles: result.data.totalFiles || 0
        });
      } else {
        throw new Error(result.error || 'Failed to load files');
      }
    } catch (error) {
      console.error('Error loading files:', error);
      toast.error('Failed to load files');
      // Set empty state on error
      setFiles({
        supportedFiles: [],
        unsupportedFiles: [],
        selectedCount: 0,
        totalFiles: 0
      });
    } finally {
      setLoading(false);
    }
  };

  const handleFileToggle = (fileId: string, checked: boolean) => {
    setSelectedFileIds(prev => {
      if (checked) {
        return [...prev, fileId];
      } else {
        return prev.filter(id => id !== fileId);
      }
    });
  };

  const handleSelectAll = () => {
    const allSupportedIds = filteredSupportedFiles.map(file => file.documentId);
    setSelectedFileIds(allSupportedIds);
  };

  const handleDeselectAll = () => {
    setSelectedFileIds([]);
  };

  const handleEnhance = () => {
    if (!customPrompt.trim()) {
      toast.error('Please enter enhancement instructions');
      return;
    }

    onEnhance(customPrompt.trim(), selectedFileIds);
    onOpenChange(false);
    
    // Reset form
    setCustomPrompt('');
    setSelectedFileIds([]);
    setSearchQuery('');
  };

  const handleCancel = () => {
    onOpenChange(false);
    // Reset form
    setCustomPrompt('');
    setSelectedFileIds([]);
    setSearchQuery('');
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden flex flex-col">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <Wand2 className="h-5 w-5 mr-2" />
            Custom Enhancement
          </DialogTitle>
          <DialogDescription>
            Add custom instructions and select reference files to enhance the content with AI.
          </DialogDescription>
        </DialogHeader>

        <div className="flex-1 overflow-hidden flex flex-col space-y-6">
          {/* Current Content Preview */}
          <div className="flex-shrink-0">
            <Label className="text-sm font-medium">Current Content</Label>
            <div className="mt-2 p-3 bg-muted rounded-md max-h-32 overflow-y-auto">
              <div className="text-sm whitespace-pre-wrap text-muted-foreground">
                {sectionContent.length > 200 
                  ? `${sectionContent.substring(0, 200)}...` 
                  : sectionContent}
              </div>
            </div>
          </div>

          {/* Custom Instructions */}
          <div className="flex-shrink-0 space-y-2">
            <Label htmlFor="customPrompt" className="text-sm font-medium">
              Enhancement Instructions
            </Label>
            <Textarea
              id="customPrompt"
              value={customPrompt}
              onChange={(e) => setCustomPrompt(e.target.value)}
              placeholder="Describe how you want to enhance this content. For example: 'Add more technical details about implementation', 'Include cost breakdown', 'Make it more persuasive for C-level executives', etc."
              rows={4}
              className="resize-none"
            />
          </div>

          {/* File Selection */}
          <div className="flex-1 overflow-hidden flex flex-col space-y-4">
            <div className="flex-shrink-0">
              <Label className="text-sm font-medium">Reference Files (Optional)</Label>
              <p className="text-xs text-muted-foreground mt-1">
                Select files to provide additional context for the enhancement
              </p>
            </div>

            {/* Search and Filter Controls */}
            <div className="flex-shrink-0 flex items-center space-x-4">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search files..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10 h-9"
                />
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowUnsupported(!showUnsupported)}
                className="flex-shrink-0"
              >
                <Filter className="h-4 w-4 mr-2" />
                {showUnsupported ? 'Hide' : 'Show'} Unsupported
              </Button>
            </div>

            {/* Selection Controls */}
            <div className="flex-shrink-0 flex items-center justify-between p-3 bg-muted/50 rounded-lg">
              <div className="flex items-center space-x-2">
                <Badge variant="secondary">
                  {selectedFileIds.length} of {filteredSupportedFiles.length} selected
                </Badge>
                {(files.totalFiles || 0) > (files.supportedFiles?.length || 0) && (
                  <Badge variant="outline">
                    {files.unsupportedFiles?.length || 0} unsupported
                  </Badge>
                )}
              </div>
              <div className="flex space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleSelectAll}
                  disabled={filteredSupportedFiles.length === 0}
                  className="h-8"
                >
                  Select All
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleDeselectAll}
                  disabled={selectedFileIds.length === 0}
                  className="h-8"
                >
                  Clear
                </Button>
              </div>
            </div>

            {loading ? (
              <div className="flex-1 flex items-center justify-center">
                <div className="text-center space-y-3">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
                  <p className="text-sm text-gray-500">Loading files...</p>
                </div>
              </div>
            ) : (
              <ScrollArea className="flex-1">
                <div className="space-y-4 pb-4">
                  {/* Supported Files */}
                  {filteredSupportedFiles.length > 0 && (
                    <div>
                      <h4 className="font-medium text-sm text-foreground mb-3 flex items-center">
                        <CheckCircle className="h-4 w-4 mr-2 text-green-600" />
                        Supported Files ({filteredSupportedFiles.length})
                      </h4>
                      <div className="grid gap-2">
                        {filteredSupportedFiles.map((file) => (
                          <Card
                            key={file.documentId}
                            className={`p-3 transition-all duration-200 hover:shadow-sm cursor-pointer border ${
                              selectedFileIds.includes(file.documentId)
                                ? 'ring-2 ring-primary bg-primary/5 border-primary/20'
                                : 'hover:bg-muted/50'
                            }`}
                            onClick={() => handleFileToggle(file.documentId, !selectedFileIds.includes(file.documentId))}
                          >
                            <div className="flex items-center space-x-3">
                              <Checkbox
                                checked={selectedFileIds.includes(file.documentId)}
                                onCheckedChange={(checked) =>
                                  handleFileToggle(file.documentId, checked as boolean)
                                }
                                onClick={(e) => e.stopPropagation()}
                              />
                              <FileText className="h-4 w-4 text-blue-600 flex-shrink-0" />
                              <div className="flex-1 min-w-0">
                                <div className="font-medium text-sm truncate">{file.name}</div>
                                <div className="text-xs text-muted-foreground truncate">
                                  {Math.round((file.fileSize || 0) / 1024)} KB
                                </div>
                              </div>
                              <div className="flex items-center space-x-2 flex-shrink-0">
                                <Badge variant="outline" className="text-xs">
                                  {file.mimeType.split('/')[1]?.toUpperCase() || 'FILE'}
                                </Badge>
                                {file.geminiFileUri && (
                                  <Badge variant="secondary" className="text-xs">
                                    AI Ready
                                  </Badge>
                                )}
                              </div>
                            </div>
                          </Card>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* No Files Message */}
                  {!loading && filteredSupportedFiles.length === 0 && !showUnsupported && (
                    <Card className="p-6 text-center border-dashed border-2 border-gray-200">
                      <File className="h-12 w-12 mx-auto mb-3 text-gray-300" />
                      <h4 className="font-medium text-gray-900 mb-2">No supported files found</h4>
                      <p className="text-sm text-gray-500 mb-3">
                        Upload some documents to the proposal first, or check if your files are in supported formats.
                      </p>
                      <div className="text-xs text-gray-400">
                        Supported formats: PDF, Word, Excel, Text files
                      </div>
                    </Card>
                  )}
                </div>
              </ScrollArea>
            )}
          </div>
        </div>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={handleCancel}
          >
            <X className="h-4 w-4 mr-2" />
            Cancel
          </Button>
          <Button
            onClick={handleEnhance}
            disabled={!customPrompt.trim()}
          >
            <Wand2 className="h-4 w-4 mr-2" />
            Enhance Content
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
