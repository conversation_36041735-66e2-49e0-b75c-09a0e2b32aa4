'use client';

import React, { useState, memo } from 'react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';
import { Skeleton } from '@/components/ui/skeleton';
import { getAvatarProps, getAvatarSizeClasses } from '@/lib/avatar-utils';
import { cn } from '@/lib/utils';

export interface ContactAvatarProps {
  firstName: string;
  lastName?: string;
  avatarUrl?: string;
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl';
  className?: string;
  showTooltip?: boolean;
  tooltipContent?: React.ReactNode;
  onClick?: () => void;
  loading?: boolean;
  status?: 'online' | 'offline' | 'away' | 'busy';
  showStatus?: boolean;
  lazy?: boolean;
  'aria-label'?: string;
}

const ContactAvatar = memo(function ContactAvatar({
  firstName,
  lastName,
  avatarUrl,
  size = 'md',
  className,
  showTooltip = false,
  tooltipContent,
  onClick,
  loading = false,
  status,
  showStatus = false,
  lazy = true,
  'aria-label': ariaLabel
}: ContactAvatarProps) {
  const [imageError, setImageError] = useState(false);
  const [imageLoading, setImageLoading] = useState(true);

  const avatarProps = getAvatarProps(firstName, lastName, avatarUrl, size);
  const sizeClasses = getAvatarSizeClasses(size);

  // Handle image load events
  const handleImageLoad = () => {
    setImageLoading(false);
    setImageError(false);
  };

  const handleImageError = () => {
    setImageLoading(false);
    setImageError(true);
  };

  // Status indicator styles
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'online': return 'bg-green-500';
      case 'away': return 'bg-yellow-500';
      case 'busy': return 'bg-red-500';
      case 'offline':
      default: return 'bg-gray-400';
    }
  };

  // Status indicator size based on avatar size
  const getStatusSize = (size: string) => {
    switch (size) {
      case 'xs': return 'w-2 h-2';
      case 'sm': return 'w-2.5 h-2.5';
      case 'md': return 'w-3 h-3';
      case 'lg': return 'w-3.5 h-3.5';
      case 'xl': return 'w-4 h-4';
      case '2xl': return 'w-5 h-5';
      default: return 'w-3 h-3';
    }
  };

  // Loading skeleton
  if (loading) {
    return (
      <Skeleton
        className={cn(sizeClasses, 'rounded-full', className)}
        aria-label="Loading avatar"
      />
    );
  }

  const avatarElement = (
    <div className="relative inline-block">
      <Avatar
        className={cn(
          sizeClasses,
          onClick && 'cursor-pointer hover:ring-2 hover:ring-primary hover:ring-offset-2 transition-all duration-200',
          'focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2',
          className
        )}
        onClick={onClick}
        role={onClick ? 'button' : undefined}
        tabIndex={onClick ? 0 : undefined}
        aria-label={ariaLabel || avatarProps.alt}
        onKeyDown={onClick ? (e) => {
          if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            onClick();
          }
        } : undefined}
      >
        {avatarUrl && !imageError ? (
          <AvatarImage
            src={avatarProps.src}
            alt={avatarProps.alt}
            className="object-cover"
            loading={lazy ? 'lazy' : 'eager'}
            onLoad={handleImageLoad}
            onError={handleImageError}
          />
        ) : null}
        <AvatarFallback
          className={cn(
            avatarProps.gradientClass,
            'text-white font-semibold select-none flex items-center justify-center',
            imageLoading && avatarUrl && !imageError && 'opacity-0',
            className?.includes('rounded-lg') && 'rounded-lg'
          )}
        >
          {avatarProps.fallbackText}
        </AvatarFallback>
      </Avatar>

      {/* Status indicator */}
      {showStatus && status && (
        <div
          className={cn(
            'absolute bottom-0 right-0 rounded-full border-2 border-white dark:border-gray-800',
            getStatusSize(size),
            getStatusColor(status)
          )}
          aria-label={`Status: ${status}`}
        />
      )}
    </div>
  );

  // Enhanced tooltip with Radix UI
  if (showTooltip) {
    return (
      <Tooltip>
        <TooltipTrigger asChild>
          {avatarElement}
        </TooltipTrigger>
        <TooltipContent side="top" className="max-w-xs">
          {tooltipContent || (
            <div className="text-center">
              <p className="font-medium">{avatarProps.alt}</p>
              {status && showStatus && (
                <p className="text-xs opacity-75 mt-1">
                  Status: {status.charAt(0).toUpperCase() + status.slice(1)}
                </p>
              )}
            </div>
          )}
        </TooltipContent>
      </Tooltip>
    );
  }

  return avatarElement;
});

export { ContactAvatar };

export interface UserAvatarProps {
  user: {
    firstName: string | null;
    lastName?: string | null;
    avatar?: string | null;
    avatarUrl?: string | null; // Alternative field name
    email?: string;
    jobTitle?: string;
    status?: 'online' | 'offline' | 'away' | 'busy';
  };
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl';
  className?: string;
  showTooltip?: boolean;
  showEmail?: boolean;
  showJobTitle?: boolean;
  showStatus?: boolean;
  onClick?: () => void;
  loading?: boolean;
  layout?: 'horizontal' | 'vertical';
  truncateText?: boolean;
  maxNameLength?: number;
  'aria-label'?: string;
}

const UserAvatar = memo(function UserAvatar({
  user,
  size = 'md',
  className,
  showTooltip = false,
  showEmail = false,
  showJobTitle = false,
  showStatus = false,
  onClick,
  loading = false,
  layout = 'horizontal',
  truncateText = true,
  maxNameLength = 30,
  'aria-label': ariaLabel
}: UserAvatarProps) {
  // Helper function to truncate text
  const truncateString = (str: string, maxLength: number) => {
    if (!truncateText || str.length <= maxLength) return str;
    return str.substring(0, maxLength) + '...';
  };

  // Get avatar URL from either field
  const avatarUrl = user.avatar || user.avatarUrl;
  const fullName = `${user.firstName || 'Unknown'} ${user.lastName || ''}`.trim();
  const displayName = truncateString(fullName, maxNameLength);

  // Enhanced tooltip content for user
  const tooltipContent = showTooltip ? (
    <div className="text-center space-y-1">
      <p className="font-medium">{fullName}</p>
      {user.email && (
        <p className="text-xs opacity-75">{user.email}</p>
      )}
      {user.jobTitle && (
        <p className="text-xs opacity-75">{user.jobTitle}</p>
      )}
      {showStatus && user.status && (
        <p className="text-xs opacity-75">
          Status: {user.status.charAt(0).toUpperCase() + user.status.slice(1)}
        </p>
      )}
    </div>
  ) : undefined;

  // Loading state
  if (loading) {
    if (showEmail || showJobTitle) {
      return (
        <div className={cn(
          'flex gap-3',
          layout === 'vertical' ? 'flex-col items-center text-center' : 'items-center'
        )}>
          <Skeleton className={cn(getAvatarSizeClasses(size), 'rounded-full')} />
          <div className="min-w-0 space-y-1">
            <Skeleton className="h-4 w-24" />
            <Skeleton className="h-3 w-32" />
          </div>
        </div>
      );
    }
    return <Skeleton className={cn(getAvatarSizeClasses(size), 'rounded-full', className)} />;
  }

  // With additional user information
  if (showEmail || showJobTitle) {
    return (
      <div
        className={cn(
          'flex gap-3',
          layout === 'vertical' ? 'flex-col items-center text-center' : 'items-center',
          onClick && 'cursor-pointer group',
          className
        )}
        onClick={onClick}
        role={onClick ? 'button' : undefined}
        tabIndex={onClick ? 0 : undefined}
        aria-label={ariaLabel || `User profile for ${fullName}`}
        onKeyDown={onClick ? (e) => {
          if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            onClick();
          }
        } : undefined}
      >
        <ContactAvatar
          firstName={user.firstName || 'Unknown'}
          lastName={user.lastName || undefined}
          avatarUrl={avatarUrl || undefined}
          size={size}
          showTooltip={showTooltip}
          tooltipContent={tooltipContent}
          status={user.status}
          showStatus={showStatus}
          onClick={undefined} // Handle click on parent container
        />
        <div className={cn(
          'min-w-0',
          layout === 'vertical' ? 'text-center' : ''
        )}>
          <p className={cn(
            'text-sm font-medium text-foreground',
            truncateText && 'truncate',
            onClick && 'group-hover:text-primary transition-colors'
          )}>
            {displayName}
          </p>
          {showEmail && user.email && (
            <p className={cn(
              'text-xs text-muted-foreground',
              truncateText && 'truncate'
            )}>
              {user.email}
            </p>
          )}
          {showJobTitle && user.jobTitle && (
            <p className={cn(
              'text-xs text-muted-foreground',
              truncateText && 'truncate'
            )}>
              {user.jobTitle}
            </p>
          )}
        </div>
      </div>
    );
  }

  // Avatar only
  return (
    <ContactAvatar
      firstName={user.firstName || 'Unknown'}
      lastName={user.lastName || undefined}
      avatarUrl={avatarUrl || undefined}
      size={size}
      className={className}
      showTooltip={showTooltip}
      tooltipContent={tooltipContent}
      onClick={onClick}
      status={user.status}
      showStatus={showStatus}
      aria-label={ariaLabel}
    />
  );
});

export { UserAvatar };

// Avatar group component for showing multiple avatars
export interface AvatarGroupProps {
  users: Array<{
    id?: string;
    firstName: string;
    lastName?: string;
    avatar?: string;
    avatarUrl?: string;
    email?: string;
    status?: 'online' | 'offline' | 'away' | 'busy';
  }>;
  max?: number;
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl';
  className?: string;
  showTooltips?: boolean;
  showStatus?: boolean;
  onUserClick?: (user: AvatarGroupProps['users'][0], index: number) => void;
  loading?: boolean;
  spacing?: 'tight' | 'normal' | 'loose';
  'aria-label'?: string;
}

const AvatarGroup = memo(function AvatarGroup({
  users,
  max = 3,
  size = 'md',
  className,
  showTooltips = true,
  showStatus = false,
  onUserClick,
  loading = false,
  spacing = 'normal',
  'aria-label': ariaLabel
}: AvatarGroupProps) {
  const displayUsers = users.slice(0, max);
  const remainingCount = users.length - max;

  // Spacing classes
  const spacingClasses = {
    tight: '-space-x-1',
    normal: '-space-x-2',
    loose: '-space-x-1.5'
  };

  // Loading state
  if (loading) {
    return (
      <div
        className={cn('flex', spacingClasses[spacing], className)}
        aria-label="Loading avatars"
      >
        {Array.from({ length: Math.min(max, 3) }).map((_, index) => (
          <Skeleton
            key={index}
            className={cn(
              getAvatarSizeClasses(size),
              'rounded-full ring-2 ring-white dark:ring-gray-800'
            )}
          />
        ))}
      </div>
    );
  }

  return (
    <div
      className={cn('flex', spacingClasses[spacing], className)}
      role="group"
      aria-label={ariaLabel || `Group of ${users.length} users`}
    >
      {displayUsers.map((user, index) => {
        const avatarUrl = user.avatar || user.avatarUrl;
        const fullName = `${user.firstName} ${user.lastName || ''}`.trim();

        return (
          <ContactAvatar
            key={user.id || index}
            firstName={user.firstName}
            lastName={user.lastName}
            avatarUrl={avatarUrl}
            size={size}
            className={cn(
              'ring-2 ring-white dark:ring-gray-800',
              onUserClick && 'cursor-pointer hover:z-10 hover:scale-110 transition-transform duration-200'
            )}
            showTooltip={showTooltips}
            tooltipContent={showTooltips ? (
              <div className="text-center">
                <p className="font-medium">{fullName}</p>
                {user.email && (
                  <p className="text-xs opacity-75">{user.email}</p>
                )}
              </div>
            ) : undefined}
            status={user.status}
            showStatus={showStatus}
            onClick={onUserClick ? () => onUserClick(user, index) : undefined}
            aria-label={`${fullName} avatar`}
          />
        );
      })}

      {remainingCount > 0 && (
        <div
          className={cn(
            getAvatarSizeClasses(size),
            'bg-muted hover:bg-muted/80 rounded-full flex items-center justify-center ring-2 ring-white dark:ring-gray-800 transition-colors',
            onUserClick && 'cursor-pointer'
          )}
          onClick={onUserClick ? () => {
            // Could trigger a callback to show all users
            console.log(`${remainingCount} more users`);
          } : undefined}
          role={onUserClick ? 'button' : undefined}
          tabIndex={onUserClick ? 0 : undefined}
          aria-label={`${remainingCount} more users`}
        >
          <span className="text-xs font-medium text-muted-foreground">
            +{remainingCount}
          </span>
        </div>
      )}
    </div>
  );
});

export { AvatarGroup };
