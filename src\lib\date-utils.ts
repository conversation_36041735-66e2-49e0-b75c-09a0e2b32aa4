import { 
  format, 
  isToday, 
  isTomorrow, 
  isYesterday, 
  startOfDay, 
  endOfDay, 
  startOfWeek, 
  endOfWeek, 
  startOfMonth, 
  endOfMonth,
  addDays,
  subDays
} from 'date-fns';

export type DatePeriod = 'day' | 'week' | 'month';

/**
 * Get date range for a specific period
 */
export function getDateRangeForPeriod(period: DatePeriod, referenceDate: Date = new Date()) {
  const today = startOfDay(referenceDate);
  
  switch (period) {
    case 'day':
      return {
        from: today,
        to: endOfDay(today)
      };
    case 'week':
      return {
        from: startOfWeek(today, { weekStartsOn: 1 }), // Monday
        to: endOfWeek(today, { weekStartsOn: 1 })
      };
    case 'month':
      return {
        from: startOfMonth(today),
        to: endOfMonth(today)
      };
    default:
      return {
        from: startOfWeek(today, { weekStartsOn: 1 }),
        to: endOfWeek(today, { weekStartsOn: 1 })
      };
  }
}

/**
 * Get extended date range that includes past and future for overview
 */
export function getExtendedDateRange(period: DatePeriod, referenceDate: Date = new Date()) {
  const today = startOfDay(referenceDate);
  
  switch (period) {
    case 'day':
      return {
        from: subDays(today, 1), // Yesterday
        to: addDays(endOfDay(today), 1) // Tomorrow
      };
    case 'week':
      return {
        from: subDays(startOfWeek(today, { weekStartsOn: 1 }), 7), // Previous week
        to: addDays(endOfWeek(today, { weekStartsOn: 1 }), 7) // Next week
      };
    case 'month':
      return {
        from: subDays(startOfMonth(today), 30), // ~Previous month
        to: addDays(endOfMonth(today), 30) // ~Next month
      };
    default:
      return {
        from: subDays(startOfWeek(today, { weekStartsOn: 1 }), 7),
        to: addDays(endOfWeek(today, { weekStartsOn: 1 }), 7)
      };
  }
}

/**
 * Format date for display in meeting cards
 */
export function formatMeetingDate(date: Date): string {
  if (isToday(date)) return 'Today';
  if (isTomorrow(date)) return 'Tomorrow';
  if (isYesterday(date)) return 'Yesterday';
  return format(date, 'MMM d');
}

/**
 * Format time for display in meeting cards
 */
export function formatMeetingTime(date: Date): string {
  return format(date, 'h:mm a');
}

/**
 * Format full date and time for meeting details
 */
export function formatMeetingDateTime(date: Date): string {
  const dateStr = formatMeetingDate(date);
  const timeStr = formatMeetingTime(date);
  
  if (dateStr === 'Today' || dateStr === 'Tomorrow' || dateStr === 'Yesterday') {
    return `${dateStr} at ${timeStr}`;
  }
  
  return `${dateStr} at ${timeStr}`;
}

/**
 * Get period label for display
 */
export function getPeriodLabel(period: DatePeriod): string {
  switch (period) {
    case 'day':
      return 'Today';
    case 'week':
      return 'This Week';
    case 'month':
      return 'This Month';
    default:
      return 'This Week';
  }
}

/**
 * Check if a date is within a specific period
 */
export function isDateInPeriod(date: Date, period: DatePeriod, referenceDate: Date = new Date()): boolean {
  const { from, to } = getDateRangeForPeriod(period, referenceDate);
  return date >= from && date <= to;
}

/**
 * Get relative time description
 */
export function getRelativeTimeDescription(date: Date): string {
  const now = new Date();
  const diffInMinutes = Math.floor((date.getTime() - now.getTime()) / (1000 * 60));
  
  if (diffInMinutes < 0) {
    // Past
    const absDiff = Math.abs(diffInMinutes);
    if (absDiff < 60) return `${absDiff} minutes ago`;
    if (absDiff < 1440) return `${Math.floor(absDiff / 60)} hours ago`;
    return formatMeetingDate(date);
  } else {
    // Future
    if (diffInMinutes < 60) return `in ${diffInMinutes} minutes`;
    if (diffInMinutes < 1440) return `in ${Math.floor(diffInMinutes / 60)} hours`;
    return formatMeetingDate(date);
  }
}
