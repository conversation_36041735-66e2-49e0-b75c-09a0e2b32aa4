'use client';

import { Metada<PERSON> } from 'next';
import { notFound } from 'next/navigation';
import { Suspense } from 'react';
import ProjectCreationWizard from '@/components/handovers/ProjectCreationWizard';
import { PermissionGate } from '@/components/ui/permission-gate';
import { PermissionResource, PermissionAction } from '@/types';
import { Card, CardContent } from '@/components/ui/card';
import { EmptyState } from '@/components/ui/empty-state';
import { ExclamationTriangleIcon } from '@heroicons/react/24/outline';
import { PageLayout } from '@/components/layout/page-layout';
import { FormPageSkeleton } from '@/components/ui/skeleton-loaders';

interface PageProps {
  params: {
    id: string;
  };
}

export default function CreateProjectFromHandoverPage({ params }: PageProps) {
  if (!params.id) {
    notFound();
  }

  return (
    <PermissionGate
      resource={PermissionResource.PROJECTS}
      action={PermissionAction.CREATE}
      fallback={
        <PageLayout>
          <Card>
            <CardContent className="p-8">
              <EmptyState
                icon={<ExclamationTriangleIcon className="w-12 h-12 text-muted-foreground" />}
                title="Access Denied"
                description="You don't have permission to create projects. Please contact your administrator for access."
                action={{
                  label: 'Back to Handovers',
                  onClick: () => window.location.href = '/handovers',
                  variant: 'primary'
                }}
              />
            </CardContent>
          </Card>
        </PageLayout>
      }
    >
      <PageLayout
        title="Create Project from Handover"
        description="Create a project from completed handover"
      >
        <Suspense fallback={<FormPageSkeleton />}>
          <ProjectCreationWizard
            handoverId={params.id}
          />
        </Suspense>
      </PageLayout>
    </PermissionGate>
  );
}
