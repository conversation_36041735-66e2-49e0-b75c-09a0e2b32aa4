'use client';

import React from 'react';
import { useRouter } from 'next/navigation';
import { TeamWithDetails } from '@/types';
import { ContactAvatar } from '@/components/ui/contact-avatar';
import { PermissionGate } from '@/components/ui/permission-gate';
import { PermissionAction, PermissionResource } from '@/types';
import {
  UserGroupIcon,
  EllipsisVerticalIcon,
  PencilIcon,
  TrashIcon,
  UserIcon,
  ChartBarIcon
} from '@heroicons/react/24/outline';

interface TeamsGridProps {
  teams: TeamWithDetails[];
  onTeamSelect: (team: TeamWithDetails) => void;
  onTeamDelete: (teamId: string) => void;
}

export function TeamsGrid({ teams, onTeamSelect, onTeamDelete }: TeamsGridProps) {
  if (teams.length === 0) {
    return (
      <div className="text-center py-12">
        <UserGroupIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">No teams found</h3>
        <p className="text-gray-500">Create your first team to get started with team management.</p>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {teams.map((team) => (
        <TeamCard
          key={team.id}
          team={team}
          onSelect={() => onTeamSelect(team)}
          onDelete={() => onTeamDelete(team.id)}
        />
      ))}
    </div>
  );
}

interface TeamCardProps {
  team: TeamWithDetails;
  onSelect: () => void;
  onDelete: () => void;
}

function TeamCard({ team, onSelect, onDelete }: TeamCardProps) {
  const router = useRouter();
  const [showMenu, setShowMenu] = React.useState(false);

  const handleEditTeam = () => {
    router.push(`/settings/team/${team.id}/edit`);
    setShowMenu(false);
  };

  return (
    <div className="bg-white rounded-lg border border-gray-200 hover:shadow-md transition-shadow">
      {/* Team Header */}
      <div className="p-6 border-b border-gray-100">
        <div className="flex items-start justify-between">
          <div className="flex items-center space-x-3">
            <div 
              className="w-12 h-12 rounded-lg flex items-center justify-center"
              style={{ backgroundColor: team.color || '#6B7280' }}
            >
              <UserGroupIcon className="w-6 h-6 text-white" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900">{team.name}</h3>
              {team.description && (
                <p className="text-sm text-gray-500 mt-1 line-clamp-2">{team.description}</p>
              )}
            </div>
          </div>

          <div className="relative">
            <button
              onClick={() => setShowMenu(!showMenu)}
              className="p-1 text-gray-400 hover:text-gray-600 rounded-full hover:bg-gray-100"
            >
              <EllipsisVerticalIcon className="w-5 h-5" />
            </button>

            {showMenu && (
              <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg border border-gray-200 z-10">
                <div className="py-1">
                  <button
                    onClick={() => {
                      onSelect();
                      setShowMenu(false);
                    }}
                    className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 w-full text-left"
                  >
                    <ChartBarIcon className="w-4 h-4 mr-3" />
                    View Details
                  </button>
                  
                  <PermissionGate resource={PermissionResource.USERS} action={PermissionAction.MANAGE}>
                    <button
                      onClick={handleEditTeam}
                      className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 w-full text-left"
                    >
                      <PencilIcon className="w-4 h-4 mr-3" />
                      Edit Team
                    </button>
                    
                    <button
                      onClick={() => {
                        onDelete();
                        setShowMenu(false);
                      }}
                      className="flex items-center px-4 py-2 text-sm text-red-600 hover:bg-red-50 w-full text-left"
                    >
                      <TrashIcon className="w-4 h-4 mr-3" />
                      Delete Team
                    </button>
                  </PermissionGate>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Team Status */}
        <div className="mt-4 flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-1">
              <UserIcon className="w-4 h-4 text-gray-400" />
              <span className="text-sm text-gray-600">{team.memberCount} members</span>
            </div>
            
            <div className={`px-2 py-1 rounded-full text-xs font-medium ${
              team.isActive 
                ? 'bg-green-100 text-green-800' 
                : 'bg-gray-100 text-gray-800'
            }`}>
              {team.isActive ? 'Active' : 'Inactive'}
            </div>
          </div>
        </div>
      </div>

      {/* Team Manager */}
      {team.manager && (
        <div className="p-4 bg-gray-50">
          <div className="flex items-center space-x-3">
            <ContactAvatar
              firstName={team.manager.firstName}
              lastName={team.manager.lastName}
              size="sm"
            />
            <div>
              <p className="text-sm font-medium text-gray-900">
                {team.manager.firstName} {team.manager.lastName}
              </p>
              <p className="text-xs text-gray-500">Team Manager</p>
            </div>
          </div>
        </div>
      )}

      {/* Recent Members Preview */}
      {team.members && team.members.length > 0 && (
        <div className="p-4">
          <div className="flex items-center justify-between mb-3">
            <h4 className="text-sm font-medium text-gray-900">Recent Members</h4>
            <button
              onClick={onSelect}
              className="text-xs text-blue-600 hover:text-blue-800"
            >
              View All
            </button>
          </div>
          
          <div className="flex -space-x-2">
            {team.members.slice(0, 5).map((member) => (
              <ContactAvatar
                key={member.id}
                firstName={member.firstName}
                lastName={member.lastName}
                avatarUrl={member.avatarUrl}
                size="sm"
                className="border-2 border-white"
              />
            ))}
            
            {team.members.length > 5 && (
              <div className="w-8 h-8 rounded-full bg-gray-100 border-2 border-white flex items-center justify-center">
                <span className="text-xs font-medium text-gray-600">
                  +{team.members.length - 5}
                </span>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Quick Actions */}
      <div className="p-4 border-t border-gray-100">
        <button
          onClick={onSelect}
          className="w-full bg-blue-50 text-blue-600 py-2 px-4 rounded-md hover:bg-blue-100 transition-colors text-sm font-medium"
        >
          Manage Team
        </button>
      </div>
    </div>
  );
}
