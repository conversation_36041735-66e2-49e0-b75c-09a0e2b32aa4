'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { ArrowRight, FileText, AlertCircle, ExternalLink, CheckCircle, Clock, Users } from 'lucide-react';
import Link from 'next/link';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';

interface PageProps {
  params: {
    id: string;
  };
}

interface ExistingHandover {
  id: string;
  title: string;
  status: string;
  priority: string;
  createdAt: string;
  salesRep?: {
    firstName?: string;
    lastName?: string;
  };
  deliveryManager?: {
    firstName?: string;
    lastName?: string;
  };
}

interface ExistingProject {
  id: string;
  name: string;
  status: string;
  createdAt: string;
  manager?: {
    firstName?: string;
    lastName?: string;
  };
}

interface Opportunity {
  id: string;
  title: string;
  stage: string;
}

export default function CreateProjectPage({ params }: PageProps) {
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [existingHandover, setExistingHandover] = useState<ExistingHandover | null>(null);
  const [existingProject, setExistingProject] = useState<ExistingProject | null>(null);
  const [opportunity, setOpportunity] = useState<Opportunity | null>(null);
  const [error, setError] = useState<string | null>(null);

  const opportunityId = params.id;

  useEffect(() => {
    const fetchExistingData = async () => {
      try {
        setLoading(true);
        setError(null);

        // Fetch opportunity, existing handovers and projects in parallel
        const [opportunityResponse, handoversResponse, projectsResponse] = await Promise.all([
          fetch(`/api/opportunities/${opportunityId}`),
          fetch(`/api/handovers?opportunityId=${opportunityId}&limit=1`),
          fetch(`/api/projects?opportunityId=${opportunityId}&limit=1`)
        ]);

        // Check opportunity and stage
        if (opportunityResponse.ok) {
          const opportunityData = await opportunityResponse.json();
          setOpportunity(opportunityData);

          // If opportunity is not in "Closed Won" stage, show error and redirect
          if (opportunityData.stage !== 'Closed Won') {
            setError('Projects can only be created from opportunities in "Closed Won" stage.');
            setTimeout(() => {
              router.push(`/opportunities/${opportunityId}`);
            }, 3000);
            return;
          }
        } else {
          setError('Opportunity not found or access denied.');
          setTimeout(() => {
            router.push('/opportunities');
          }, 3000);
          return;
        }

        // Check handovers
        if (handoversResponse.ok) {
          const handoversData = await handoversResponse.json();
          if (handoversData.handovers && handoversData.handovers.length > 0) {
            setExistingHandover(handoversData.handovers[0]);
          }
        }

        // Check projects
        if (projectsResponse.ok) {
          const projectsData = await projectsResponse.json();
          if (projectsData.success && projectsData.data.projects && projectsData.data.projects.length > 0) {
            setExistingProject(projectsData.data.projects[0]);
          }
        }
      } catch (err) {
        console.error('Error fetching existing data:', err);
        setError('Failed to load opportunity and existing data');
      } finally {
        setLoading(false);
      }
    };

    if (opportunityId) {
      fetchExistingData();
    }
  }, [opportunityId, router]);

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'in_progress':
        return 'bg-blue-100 text-blue-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority.toLowerCase()) {
      case 'urgent':
        return 'bg-red-100 text-red-800';
      case 'high':
        return 'bg-orange-100 text-orange-800';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800';
      case 'low':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto py-6 max-w-4xl">
        <div className="space-y-6">
          <div>
            <Skeleton className="h-8 w-96 mb-2" />
            <Skeleton className="h-4 w-64" />
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Skeleton className="h-64" />
            <Skeleton className="h-64" />
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6 max-w-4xl">
      <div className="space-y-6">
        {/* Header */}
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Create Project from Opportunity</h1>
          <p className="text-gray-600">
            {opportunity ? (
              <>
                Creating project for: <span className="font-medium">{opportunity.title}</span>
                <Badge className="ml-2 bg-green-100 text-green-800">
                  {opportunity.stage}
                </Badge>
              </>
            ) : (
              'Choose how you want to create your project'
            )}
          </p>
        </div>

        {/* Error Alert */}
        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Options */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Option 1: Direct Project Creation or Existing Project */}
          {existingProject ? (
            <Card className="border-green-200 bg-green-50">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <CheckCircle className="w-5 h-5 text-green-600" />
                  Project Already Exists
                </CardTitle>
                <CardDescription>
                  A project has already been created for this opportunity
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="font-medium">{existingProject.name}</span>
                      <Badge className={getStatusColor(existingProject.status)}>
                        {existingProject.status.replace('_', ' ')}
                      </Badge>
                    </div>

                    {existingProject.manager && (
                      <div className="flex items-center gap-2 text-sm text-gray-600">
                        <Users className="w-4 h-4" />
                        <span>
                          Manager: {existingProject.manager.firstName} {existingProject.manager.lastName}
                        </span>
                      </div>
                    )}

                    <div className="flex items-center gap-2 text-sm text-gray-600">
                      <Clock className="w-4 h-4" />
                      <span>
                        Created: {new Date(existingProject.createdAt).toLocaleDateString()}
                      </span>
                    </div>
                  </div>

                  <Alert className="border-green-200 bg-green-50">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                    <AlertDescription className="text-green-800">
                      Project is ready! You can view and manage it using the link below.
                    </AlertDescription>
                  </Alert>

                  <Button className="w-full" asChild>
                    <Link href={`/projects/${existingProject.id}`}>
                      <ExternalLink className="w-4 h-4 mr-2" />
                      View Existing Project
                    </Link>
                  </Button>
                </div>
              </CardContent>
            </Card>
          ) : (
            <Card className="cursor-pointer hover:shadow-lg transition-shadow">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="w-5 h-5 text-blue-600" />
                  Direct Project Creation
                </CardTitle>
                <CardDescription>
                  Create a project immediately without a handover process
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="text-sm text-gray-600">
                    <p className="mb-2"><strong>Best for:</strong></p>
                    <ul className="list-disc list-inside space-y-1">
                      <li>Simple projects with clear requirements</li>
                      <li>Internal projects</li>
                      <li>When delivery team is already involved</li>
                    </ul>
                  </div>

                  <Alert>
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>
                      This will create a project directly without the handover process.
                    </AlertDescription>
                  </Alert>

                  <Button className="w-full" asChild>
                    <Link href={`/projects/new?opportunityId=${opportunityId}`}>
                      <FileText className="w-4 h-4 mr-2" />
                      Create Project Now
                    </Link>
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Option 2: Handover Process or Existing Handover */}
          {existingHandover ? (
            <Card className="border-blue-200 bg-blue-50">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <CheckCircle className="w-5 h-5 text-blue-600" />
                  Handover Process Active
                </CardTitle>
                <CardDescription>
                  A handover process is already in progress for this opportunity
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="font-medium">{existingHandover.title}</span>
                      <div className="flex gap-2">
                        <Badge className={getStatusColor(existingHandover.status)}>
                          {existingHandover.status.replace('_', ' ')}
                        </Badge>
                        <Badge className={getPriorityColor(existingHandover.priority)}>
                          {existingHandover.priority}
                        </Badge>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 gap-2 text-sm text-gray-600">
                      {existingHandover.salesRep && (
                        <div className="flex items-center gap-2">
                          <Users className="w-4 h-4" />
                          <span>
                            Sales Rep: {existingHandover.salesRep.firstName} {existingHandover.salesRep.lastName}
                          </span>
                        </div>
                      )}

                      {existingHandover.deliveryManager && (
                        <div className="flex items-center gap-2">
                          <Users className="w-4 h-4" />
                          <span>
                            Delivery Manager: {existingHandover.deliveryManager.firstName} {existingHandover.deliveryManager.lastName}
                          </span>
                        </div>
                      )}

                      <div className="flex items-center gap-2">
                        <Clock className="w-4 h-4" />
                        <span>
                          Started: {new Date(existingHandover.createdAt).toLocaleDateString()}
                        </span>
                      </div>
                    </div>
                  </div>

                  <Alert className="border-blue-200 bg-blue-50">
                    <CheckCircle className="h-4 w-4 text-blue-600" />
                    <AlertDescription className="text-blue-800">
                      {existingHandover.status === 'completed'
                        ? 'Handover completed! You can now create a project from this handover.'
                        : 'Handover is in progress. You can view and manage it using the link below.'
                      }
                    </AlertDescription>
                  </Alert>

                  <div className="flex gap-2">
                    <Button className="flex-1" asChild>
                      <Link href={`/handovers/${existingHandover.id}`}>
                        <ExternalLink className="w-4 h-4 mr-2" />
                        View Handover
                      </Link>
                    </Button>

                    {existingHandover.status === 'completed' && !existingProject && (
                      <Button className="flex-1 bg-green-600 hover:bg-green-700" asChild>
                        <Link href={`/handovers/${existingHandover.id}/create-project`}>
                          <ArrowRight className="w-4 h-4 mr-2" />
                          Create Project
                        </Link>
                      </Button>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          ) : (
            <Card className="cursor-pointer hover:shadow-lg transition-shadow border-blue-200 bg-blue-50">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <ArrowRight className="w-5 h-5 text-blue-600" />
                  Start Handover Process
                  <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">
                    Recommended
                  </span>
                </CardTitle>
                <CardDescription>
                  Follow a structured handover process before project creation
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="text-sm text-gray-600">
                    <p className="mb-2"><strong>Includes:</strong></p>
                    <ul className="list-disc list-inside space-y-1">
                      <li>Structured checklist and requirements review</li>
                      <li>Document organization and approval</li>
                      <li>Q&A communication between teams</li>
                      <li>Quality assurance and validation</li>
                      <li>Guided project creation wizard</li>
                    </ul>
                  </div>

                  <Alert className="border-blue-200 bg-blue-50">
                    <AlertCircle className="h-4 w-4 text-blue-600" />
                    <AlertDescription className="text-blue-800">
                      Recommended for complex projects to ensure smooth delivery transition.
                    </AlertDescription>
                  </Alert>

                  <Button className="w-full bg-blue-600 hover:bg-blue-700" asChild>
                    <Link href={`/handovers/new?opportunityId=${opportunityId}`}>
                      <ArrowRight className="w-4 h-4 mr-2" />
                      Start Handover Process
                    </Link>
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Process Comparison */}
        <Card>
          <CardHeader>
            <CardTitle>Process Comparison</CardTitle>
            <CardDescription>
              Understand the differences between the two approaches
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full text-sm">
                <thead>
                  <tr className="border-b">
                    <th className="text-left py-2 font-medium">Feature</th>
                    <th className="text-center py-2 font-medium">Direct Creation</th>
                    <th className="text-center py-2 font-medium">Handover Process</th>
                  </tr>
                </thead>
                <tbody className="space-y-2">
                  <tr className="border-b">
                    <td className="py-2">Time to Project Start</td>
                    <td className="text-center py-2">Immediate</td>
                    <td className="text-center py-2">1-3 days</td>
                  </tr>
                  <tr className="border-b">
                    <td className="py-2">Requirements Documentation</td>
                    <td className="text-center py-2">Basic</td>
                    <td className="text-center py-2">Comprehensive</td>
                  </tr>
                  <tr className="border-b">
                    <td className="py-2">Team Communication</td>
                    <td className="text-center py-2">Manual</td>
                    <td className="text-center py-2">Structured Q&A</td>
                  </tr>
                  <tr className="border-b">
                    <td className="py-2">Quality Assurance</td>
                    <td className="text-center py-2">Limited</td>
                    <td className="text-center py-2">Built-in Checklist</td>
                  </tr>
                  <tr className="border-b">
                    <td className="py-2">Document Organization</td>
                    <td className="text-center py-2">Manual</td>
                    <td className="text-center py-2">Automated</td>
                  </tr>
                  <tr>
                    <td className="py-2">Project Success Rate</td>
                    <td className="text-center py-2">Good</td>
                    <td className="text-center py-2">Excellent</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>

        {/* Back Button */}
        <div className="flex justify-start">
          <Button variant="outline" asChild>
            <Link href={`/opportunities/${opportunityId}`}>
              ← Back to Opportunity
            </Link>
          </Button>
        </div>
      </div>
    </div>
  );
}
