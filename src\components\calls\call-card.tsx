'use client';

import { useState } from 'react';
import { format } from 'date-fns';
import { Calendar, Clock, Phone, Video, MapPin, User, MessageSquare, Edit, Trash2, CheckCircle } from 'lucide-react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { CallStatusBadge, CallStatus } from './call-status-badge';
import { ScheduledCallWithRelations } from '@/services/scheduled-call-service';
import { cn } from '@/lib/utils';

interface CallCardProps {
  call: ScheduledCallWithRelations;
  onEdit?: (call: ScheduledCallWithRelations) => void;
  onDelete?: (callId: string) => void;
  onComplete?: (call: ScheduledCallWithRelations) => void;
  showActions?: boolean;
  compact?: boolean;
  className?: string;
}

const callTypeIcons = {
  phone: Phone,
  video: Video,
  'in-person': MapPin,
};

const callTypeLabels = {
  phone: 'Phone Call',
  video: 'Video Call',
  'in-person': 'In-Person Meeting',
};

export function CallCard({
  call,
  onEdit,
  onDelete,
  onComplete,
  showActions = true,
  compact = false,
  className,
}: CallCardProps) {
  const [isLoading, setIsLoading] = useState(false);

  const CallTypeIcon = callTypeIcons[call.callType as keyof typeof callTypeIcons];
  const isUpcoming = new Date(call.scheduledAt) > new Date();
  const isPast = new Date(call.scheduledAt) < new Date();
  const canComplete = call.status === 'scheduled' && isPast;

  const handleComplete = async () => {
    if (!onComplete) return;
    setIsLoading(true);
    try {
      await onComplete(call);
    } finally {
      setIsLoading(false);
    }
  };

  const handleDelete = async () => {
    if (!onDelete) return;
    setIsLoading(true);
    try {
      await onDelete(call.id);
    } finally {
      setIsLoading(false);
    }
  };

  const getContactName = () => {
    if (call.contact) {
      return `${call.contact.firstName} ${call.contact.lastName}`;
    }
    if (call.lead?.contact) {
      return `${call.lead.contact.firstName} ${call.lead.contact.lastName}`;
    }
    return 'Unknown Contact';
  };

  const getContactInfo = () => {
    if (call.contact) {
      return {
        name: `${call.contact.firstName} ${call.contact.lastName}`,
        email: call.contact.email,
        phone: call.contact.phone,
      };
    }
    if (call.lead?.contact) {
      return {
        name: `${call.lead.contact.firstName} ${call.lead.contact.lastName}`,
        email: call.lead.contact.email,
        phone: null,
      };
    }
    return null;
  };

  const contactInfo = getContactInfo();

  return (
    <Card className={cn(
      'transition-all duration-200 hover:shadow-lg hover:shadow-blue-100/50 dark:hover:shadow-blue-900/20 border-0 ring-1 ring-gray-200 dark:ring-gray-700 bg-white dark:bg-gray-800',
      className
    )}>
      <CardHeader className={cn('pb-3', compact && 'pb-2')}>
        <div className="flex items-start justify-between">
          <div className="flex items-start space-x-4">
            <div className={cn(
              'flex items-center justify-center rounded-xl shadow-sm',
              call.status === 'completed'
                ? 'bg-gradient-to-br from-green-100 to-emerald-100 dark:from-green-900/30 dark:to-emerald-900/30 text-green-600 dark:text-green-400' :
              call.status === 'cancelled'
                ? 'bg-gradient-to-br from-red-100 to-rose-100 dark:from-red-900/30 dark:to-rose-900/30 text-red-600 dark:text-red-400' :
              isUpcoming
                ? 'bg-gradient-to-br from-blue-100 to-indigo-100 dark:from-blue-900/30 dark:to-indigo-900/30 text-blue-600 dark:text-blue-400'
                : 'bg-gradient-to-br from-gray-100 to-slate-100 dark:from-gray-700 dark:to-slate-700 text-gray-600 dark:text-gray-400',
              compact ? 'h-10 w-10' : 'h-12 w-12'
            )}>
              <CallTypeIcon className={cn(compact ? 'h-5 w-5' : 'h-6 w-6')} />
            </div>
            <div className="flex-1 min-w-0">
              <CardTitle className={cn(
                'font-semibold text-gray-900 dark:text-gray-100',
                compact ? 'text-sm' : 'text-base'
              )}>
                {call.title}
              </CardTitle>
              <div className="flex items-center space-x-2 mt-2">
                <CallStatusBadge status={call.status as CallStatus} />
                <Badge variant="outline" className="text-xs bg-gray-50 dark:bg-gray-700">
                  {callTypeLabels[call.callType as keyof typeof callTypeLabels]}
                </Badge>
                {isUpcoming && (
                  <Badge className="text-xs bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-300">
                    Upcoming
                  </Badge>
                )}
              </div>
            </div>
          </div>
          {showActions && (
            <div className="flex items-center space-x-1">
              {canComplete && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleComplete}
                  disabled={isLoading}
                  className="h-8 w-8 p-0 text-green-600 hover:text-green-700 hover:bg-green-50 dark:hover:bg-green-900/20"
                  title="Mark as completed"
                >
                  <CheckCircle className="h-4 w-4" />
                </Button>
              )}
              {onEdit && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => onEdit(call)}
                  disabled={isLoading}
                  className="h-8 w-8 p-0 text-blue-600 hover:text-blue-700 hover:bg-blue-50 dark:hover:bg-blue-900/20"
                  title="Edit call"
                >
                  <Edit className="h-4 w-4" />
                </Button>
              )}
              {onDelete && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleDelete}
                  disabled={isLoading}
                  className="h-8 w-8 p-0 text-red-600 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-900/20"
                  title="Delete call"
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              )}
            </div>
          )}
        </div>
      </CardHeader>

      <CardContent className={cn('pt-0', compact && 'pb-4')}>
        <div className="space-y-4">
          {/* Date and Time - Enhanced */}
          <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="p-1.5 bg-white dark:bg-gray-600 rounded-lg shadow-sm">
                  <Calendar className="h-4 w-4 text-gray-600 dark:text-gray-300" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                    {format(new Date(call.scheduledAt), 'EEEE, MMM dd, yyyy')}
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    {isUpcoming ? 'Scheduled for' : 'Was scheduled for'}
                  </p>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <div className="p-1.5 bg-white dark:bg-gray-600 rounded-lg shadow-sm">
                  <Clock className="h-4 w-4 text-gray-600 dark:text-gray-300" />
                </div>
                <div className="text-right">
                  <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                    {format(new Date(call.scheduledAt), 'h:mm a')}
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    {call.duration} minutes
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Contact and Lead Information - Enhanced */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            {contactInfo && (
              <div className="bg-indigo-50 dark:bg-indigo-900/20 rounded-lg p-3">
                <div className="flex items-center space-x-2 mb-2">
                  <User className="h-4 w-4 text-indigo-600 dark:text-indigo-400" />
                  <span className="text-xs font-medium text-indigo-900 dark:text-indigo-100 uppercase tracking-wide">Contact</span>
                </div>
                <p className="font-semibold text-indigo-900 dark:text-indigo-100">{contactInfo.name}</p>
                {contactInfo.email && (
                  <p className="text-xs text-indigo-700 dark:text-indigo-300 mt-1">{contactInfo.email}</p>
                )}
              </div>
            )}

            {call.lead && (
              <div className="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-3">
                <div className="flex items-center space-x-2 mb-2">
                  <Calendar className="h-4 w-4 text-purple-600 dark:text-purple-400" />
                  <span className="text-xs font-medium text-purple-900 dark:text-purple-100 uppercase tracking-wide">Lead</span>
                </div>
                <p className="font-semibold text-purple-900 dark:text-purple-100">{call.lead.title}</p>
                <Badge variant="outline" className="mt-1 text-xs bg-purple-100 dark:bg-purple-800 border-purple-200 dark:border-purple-700">
                  {call.lead.status}
                </Badge>
              </div>
            )}
          </div>

          {/* Assigned To - Enhanced */}
          <div className="bg-orange-50 dark:bg-orange-900/20 rounded-lg p-3">
            <div className="flex items-center space-x-2 mb-2">
              <User className="h-4 w-4 text-orange-600 dark:text-orange-400" />
              <span className="text-xs font-medium text-orange-900 dark:text-orange-100 uppercase tracking-wide">Assigned To</span>
            </div>
            <p className="font-semibold text-orange-900 dark:text-orange-100">
              {call.assignedToUser.firstName} {call.assignedToUser.lastName}
            </p>
          </div>

          {/* Description */}
          {call.description && !compact && (
            <>
              <Separator />
              <div className="text-sm text-gray-600">
                <p className="line-clamp-2">{call.description}</p>
              </div>
            </>
          )}

          {/* Call Notes (for completed calls) */}
          {call.status === 'completed' && call.callNotes && !compact && (
            <>
              <Separator />
              <div className="text-sm">
                <div className="flex items-center space-x-1 mb-1">
                  <MessageSquare className="h-4 w-4 text-gray-400" />
                  <span className="font-medium text-gray-700">Call Notes:</span>
                </div>
                <p className="text-gray-600 line-clamp-3">{call.callNotes}</p>
              </div>
            </>
          )}

          {/* Follow-up Required */}
          {call.followUpRequired && (
            <div className="flex items-center space-x-2">
              <Badge variant="secondary" className="text-xs">
                Follow-up Required
              </Badge>
              {call.nextCallDate && (
                <span className="text-xs text-gray-500">
                  Next: {format(new Date(call.nextCallDate), 'MMM dd, yyyy')}
                </span>
              )}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
