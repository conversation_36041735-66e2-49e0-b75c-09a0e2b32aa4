import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { opportunityManagementService, createOpportunitySchema } from '@/services/opportunity-management';
import { hasPermission } from '@/lib/permissions';
import { PermissionResource, PermissionAction } from '@/types';
import { z } from 'zod';

const convertLeadSchema = z.object({
  leadId: z.string().min(1, 'Lead ID is required'),
  opportunityData: createOpportunitySchema.partial().optional(),
  conversionNotes: z.string().optional(),
});

/**
 * POST /api/opportunities/convert-from-lead
 * Convert a lead to an opportunity
 */
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user's current tenant
    const currentTenant = session.user.tenants?.[0];
    if (!currentTenant) {
      return NextResponse.json({ error: 'No tenant access' }, { status: 403 });
    }

    // Check permissions for both leads and opportunities
    const [hasLeadPermission, hasOpportunityPermission] = await Promise.all([
      hasPermission(
        PermissionResource.LEADS,
        PermissionAction.UPDATE,
        currentTenant.id
      ),
      hasPermission(
        PermissionResource.OPPORTUNITIES,
        PermissionAction.CREATE,
        currentTenant.id
      )
    ]);

    if (!hasLeadPermission || !hasOpportunityPermission) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const body = await request.json();
    const { leadId, opportunityData = {}, conversionNotes } = convertLeadSchema.parse(body);

    const opportunity = await opportunityManagementService.convertLeadToOpportunity(
      leadId,
      currentTenant.id,
      opportunityData,
      session.user.id,
      conversionNotes
    );

    return NextResponse.json(opportunity, { status: 201 });
  } catch (error) {
    console.error('Error converting lead to opportunity:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid data', details: error.errors },
        { status: 400 }
      );
    }

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
