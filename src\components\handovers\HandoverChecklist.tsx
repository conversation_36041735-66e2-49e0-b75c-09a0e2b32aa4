'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  CheckCircle, 
  Circle, 
  Clock, 
  User, 
  MessageSquare,
  AlertCircle,
  Plus,
  ChevronDown,
  ChevronRight
} from 'lucide-react';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';

// Types
interface ChecklistItem {
  id: string;
  title: string;
  description?: string;
  category: string;
  orderIndex: number;
  isRequired: boolean;
  isCompleted: boolean;
  completedAt?: string;
  completedBy?: {
    id: string;
    firstName?: string;
    lastName?: string;
  };
  notes?: string;
  dependsOnIds: string[];
}

interface ChecklistItemsResponse {
  items: ChecklistItem[];
}

interface HandoverChecklistProps {
  handoverId: string;
  onProgressUpdate?: () => void;
}

// Category configurations
const categoryConfig = {
  general: { label: 'General', color: 'bg-gray-100 text-gray-800' },
  technical: { label: 'Technical', color: 'bg-blue-100 text-blue-800' },
  legal: { label: 'Legal', color: 'bg-purple-100 text-purple-800' },
  financial: { label: 'Financial', color: 'bg-green-100 text-green-800' },
  client: { label: 'Client', color: 'bg-orange-100 text-orange-800' },
};

export default function HandoverChecklist({ handoverId, onProgressUpdate }: HandoverChecklistProps) {
  const [items, setItems] = useState<ChecklistItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [updating, setUpdating] = useState<string | null>(null);
  const [expandedCategories, setExpandedCategories] = useState<Set<string>>(new Set(['general']));
  const [editingNotes, setEditingNotes] = useState<string | null>(null);
  const [noteText, setNoteText] = useState('');

  // Fetch checklist items
  const fetchItems = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`/api/handovers/${handoverId}/checklist`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch checklist items');
      }

      const data: ChecklistItemsResponse = await response.json();
      setItems(data.items || []);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchItems();
  }, [handoverId]);

  // Create checklist from template
  const createFromTemplate = async (templateId?: string) => {
    try {
      setLoading(true);
      
      const response = await fetch(`/api/handovers/${handoverId}/checklist`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ templateId }),
      });

      if (!response.ok) {
        throw new Error('Failed to create checklist from template');
      }

      await fetchItems();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create checklist');
    } finally {
      setLoading(false);
    }
  };

  // Update checklist item
  const updateItem = async (itemId: string, updates: Partial<ChecklistItem>) => {
    try {
      setUpdating(itemId);
      
      const response = await fetch(`/api/handovers/checklist-items/${itemId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updates),
      });

      if (!response.ok) {
        throw new Error('Failed to update checklist item');
      }

      const updatedItem = await response.json();
      
      setItems(prev => prev.map(item =>
        item.id === itemId ? updatedItem : item
      ));

      // Notify parent component to refresh handover data
      if (onProgressUpdate) {
        onProgressUpdate();
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update item');
    } finally {
      setUpdating(null);
    }
  };

  // Toggle item completion
  const toggleCompletion = async (item: ChecklistItem) => {
    await updateItem(item.id, { isCompleted: !item.isCompleted });
  };

  // Save notes
  const saveNotes = async (itemId: string) => {
    await updateItem(itemId, { notes: noteText });
    setEditingNotes(null);
    setNoteText('');
  };

  // Group items by category
  const groupedItems = items.reduce((groups, item) => {
    const category = item.category || 'general';
    if (!groups[category]) {
      groups[category] = [];
    }
    groups[category].push(item);
    return groups;
  }, {} as Record<string, ChecklistItem[]>);

  // Calculate progress
  const totalItems = items.length;
  const completedItems = items.filter(item => item.isCompleted).length;
  const progress = totalItems > 0 ? Math.round((completedItems / totalItems) * 100) : 0;

  // Check if item can be completed (dependencies)
  const canCompleteItem = (item: ChecklistItem) => {
    if (item.dependsOnIds.length === 0) return true;
    
    return item.dependsOnIds.every(depId => {
      const depItem = items.find(i => i.id === depId);
      return depItem?.isCompleted;
    });
  };

  // Toggle category expansion
  const toggleCategory = (category: string) => {
    setExpandedCategories(prev => {
      const newSet = new Set(prev);
      if (newSet.has(category)) {
        newSet.delete(category);
      } else {
        newSet.add(category);
      }
      return newSet;
    });
  };

  const getUserDisplayName = (user?: { firstName?: string; lastName?: string }) => {
    if (!user) return 'Unknown';
    return user.firstName && user.lastName 
      ? `${user.firstName} ${user.lastName}`
      : 'Unknown';
  };

  const getCategoryBadge = (category: string) => {
    const config = categoryConfig[category as keyof typeof categoryConfig] || categoryConfig.general;
    return (
      <Badge variant="outline" className={config.color}>
        {config.label}
      </Badge>
    );
  };

  if (loading && items.length === 0) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-sm text-gray-600">Loading checklist...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-xl font-semibold">Handover Checklist</h2>
          <p className="text-gray-600">Track completion of handover requirements</p>
        </div>
        {items.length === 0 && (
          <Button onClick={() => createFromTemplate()}>
            <Plus className="w-4 h-4 mr-2" />
            Create from Template
          </Button>
        )}
      </div>

      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Progress Overview */}
      {items.length > 0 && (
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between mb-4">
              <div>
                <div className="text-2xl font-bold">{progress}%</div>
                <div className="text-sm text-gray-600">
                  {completedItems} of {totalItems} items completed
                </div>
              </div>
              <div className="text-right">
                <div className="text-sm text-gray-600">
                  Required: {items.filter(item => item.isRequired).length}
                </div>
                <div className="text-sm text-gray-600">
                  Optional: {items.filter(item => !item.isRequired).length}
                </div>
              </div>
            </div>
            <Progress value={progress} className="h-2" />
          </CardContent>
        </Card>
      )}

      {/* Checklist Items by Category */}
      {Object.entries(groupedItems).map(([category, categoryItems]) => {
        const isExpanded = expandedCategories.has(category);
        const categoryCompleted = categoryItems.filter(item => item.isCompleted).length;
        const categoryTotal = categoryItems.length;
        const categoryProgress = categoryTotal > 0 ? Math.round((categoryCompleted / categoryTotal) * 100) : 0;

        return (
          <Card key={category}>
            <CardHeader 
              className="cursor-pointer"
              onClick={() => toggleCategory(category)}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  {isExpanded ? (
                    <ChevronDown className="w-5 h-5" />
                  ) : (
                    <ChevronRight className="w-5 h-5" />
                  )}
                  <CardTitle className="flex items-center gap-2">
                    {getCategoryBadge(category)}
                    <span className="text-sm font-medium">
                      {categoryCompleted}/{categoryTotal} completed
                    </span>
                  </CardTitle>
                </div>
                <div className="flex items-center gap-2">
                  <span className="text-sm text-gray-600">{categoryProgress}%</span>
                  <Progress value={categoryProgress} className="w-20 h-2" />
                </div>
              </div>
            </CardHeader>
            
            {isExpanded && (
              <CardContent className="space-y-4">
                {categoryItems
                  .sort((a, b) => a.orderIndex - b.orderIndex)
                  .map((item) => {
                    const canComplete = canCompleteItem(item);
                    const isUpdating = updating === item.id;

                    return (
                      <div
                        key={item.id}
                        className={cn(
                          "p-4 border rounded-lg",
                          item.isCompleted
                            ? "bg-green-50 border-green-200"
                            : canComplete
                              ? "bg-white border-gray-200"
                              : "bg-gray-50 border-gray-200"
                        )}
                      >
                        <div className="flex items-start gap-3">
                          <Checkbox
                            checked={item.isCompleted}
                            onCheckedChange={() => toggleCompletion(item)}
                            disabled={!canComplete || isUpdating}
                            className="mt-1"
                          />
                          
                          <div className="flex-1 space-y-2">
                            <div className="flex items-center justify-between">
                              <div className="flex items-center gap-2">
                                <span className={cn(
                                  "font-medium",
                                  item.isCompleted && "line-through text-gray-500"
                                )}>
                                  {item.title}
                                </span>
                                {item.isRequired && (
                                  <Badge variant="outline" className="text-xs">
                                    Required
                                  </Badge>
                                )}
                              </div>
                              
                              {item.isCompleted && item.completedAt && (
                                <div className="flex items-center gap-1 text-xs text-gray-500">
                                  <CheckCircle className="w-3 h-3" />
                                  {format(new Date(item.completedAt), 'MMM d, h:mm a')}
                                  {item.completedBy && (
                                    <span>by {getUserDisplayName(item.completedBy)}</span>
                                  )}
                                </div>
                              )}
                            </div>
                            
                            {item.description && (
                              <p className="text-sm text-gray-600">{item.description}</p>
                            )}
                            
                            {!canComplete && item.dependsOnIds.length > 0 && (
                              <div className="flex items-center gap-1 text-xs text-orange-600">
                                <Clock className="w-3 h-3" />
                                Waiting for dependencies
                              </div>
                            )}
                            
                            {/* Notes Section */}
                            <div className="space-y-2">
                              {item.notes && editingNotes !== item.id && (
                                <div className="p-2 bg-gray-50 rounded text-sm">
                                  <div className="flex items-center gap-1 mb-1">
                                    <MessageSquare className="w-3 h-3" />
                                    <span className="font-medium">Notes:</span>
                                  </div>
                                  <p>{item.notes}</p>
                                </div>
                              )}
                              
                              {editingNotes === item.id ? (
                                <div className="space-y-2">
                                  <Textarea
                                    value={noteText}
                                    onChange={(e) => setNoteText(e.target.value)}
                                    placeholder="Add notes for this item..."
                                    rows={2}
                                  />
                                  <div className="flex gap-2">
                                    <Button
                                      size="sm"
                                      onClick={() => saveNotes(item.id)}
                                      disabled={isUpdating}
                                    >
                                      Save
                                    </Button>
                                    <Button
                                      size="sm"
                                      variant="outline"
                                      onClick={() => {
                                        setEditingNotes(null);
                                        setNoteText('');
                                      }}
                                    >
                                      Cancel
                                    </Button>
                                  </div>
                                </div>
                              ) : (
                                <Button
                                  size="sm"
                                  variant="ghost"
                                  onClick={() => {
                                    setEditingNotes(item.id);
                                    setNoteText(item.notes || '');
                                  }}
                                  className="text-xs"
                                >
                                  <MessageSquare className="w-3 h-3 mr-1" />
                                  {item.notes ? 'Edit Notes' : 'Add Notes'}
                                </Button>
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    );
                  })}
              </CardContent>
            )}
          </Card>
        );
      })}

      {/* Empty State */}
      {items.length === 0 && !loading && (
        <Card>
          <CardContent className="pt-6">
            <div className="text-center py-8">
              <CheckCircle className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No checklist items</h3>
              <p className="text-gray-600 mb-4">
                Create a checklist from a template to get started
              </p>
              <Button onClick={() => createFromTemplate()}>
                <Plus className="w-4 h-4 mr-2" />
                Create from Template
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
