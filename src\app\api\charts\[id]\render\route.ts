import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { hasPermission } from '@/lib/permissions';
import { PermissionResource, PermissionAction } from '@/types';
import { mermaidRenderer } from '@/services/mermaid-renderer';
import { enhancedMermaidRenderer } from '@/services/enhanced-mermaid-renderer';
import { z } from 'zod';

// Request validation schema
const renderChartSchema = z.object({
  format: z.enum(['png', 'svg']).default('png'),
  theme: z.enum(['default', 'dark', 'forest', 'neutral']).default('default'),
  width: z.number().min(200).max(2000).default(800),
  height: z.number().min(200).max(2000).default(600)
});

/**
 * POST /api/charts/[id]/render
 * Render a Mermaid chart to image
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user's current tenant
    const currentTenant = session.user.tenants?.[0];
    if (!currentTenant) {
      return NextResponse.json({ error: 'No tenant access' }, { status: 403 });
    }

    // Check permission for proposal reading (charts are part of proposals)
    const hasAccess = await hasPermission(
      PermissionResource.PROPOSALS,
      PermissionAction.READ,
      currentTenant.id
    );

    if (!hasAccess) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const chartId = params.id;
    const body = await request.json().catch(() => ({}));
    
    // Validate request data
    const validatedData = renderChartSchema.parse(body);

    // Render the chart using the enhanced mermaid renderer
    const result = await enhancedMermaidRenderer.renderChart(
      chartId,
      currentTenant.id,
      validatedData
    );

    return NextResponse.json({
      success: true,
      data: result,
      message: 'Chart rendered successfully'
    });

  } catch (error) {
    console.error('Error rendering chart:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          error: 'Invalid request data', 
          details: error.errors.map(e => ({
            field: e.path.join('.'),
            message: e.message
          }))
        },
        { status: 400 }
      );
    }

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * @swagger
 * /api/charts/{id}/render:
 *   post:
 *     summary: Render Mermaid chart to image
 *     description: Convert a Mermaid chart to PNG or SVG image format
 *     tags: [Charts]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Chart ID
 *     requestBody:
 *       required: false
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               format:
 *                 type: string
 *                 enum: [png, svg]
 *                 default: png
 *                 description: Output image format
 *               theme:
 *                 type: string
 *                 enum: [default, dark, forest, neutral]
 *                 default: default
 *                 description: Mermaid theme
 *               width:
 *                 type: number
 *                 minimum: 200
 *                 maximum: 2000
 *                 default: 800
 *                 description: Image width in pixels
 *               height:
 *                 type: number
 *                 minimum: 200
 *                 maximum: 2000
 *                 default: 600
 *                 description: Image height in pixels
 *     responses:
 *       200:
 *         description: Chart rendered successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     imagePath:
 *                       type: string
 *                       description: Relative path to the rendered image
 *                     imageUrl:
 *                       type: string
 *                       description: URL to access the rendered image
 *                     width:
 *                       type: number
 *                       description: Image width in pixels
 *                     height:
 *                       type: number
 *                       description: Image height in pixels
 *                     format:
 *                       type: string
 *                       description: Image format (png or svg)
 *                 message:
 *                   type: string
 *       400:
 *         description: Invalid request data or chart not found
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - insufficient permissions
 *       500:
 *         description: Internal server error
 */
