/**
 * Permission Helper Utilities
 * 
 * This file provides utility functions to work with permissions,
 * bridging between the old enum-based system and new constant-based system.
 */

import { PermissionResource, PermissionAction } from '@/types';
import { PERMISSIONS, isValidPermission, parsePermission } from '@/constants/permissions';

/**
 * Convert resource and action enums to permission string
 */
export function createPermissionString(resource: PermissionResource, action: PermissionAction): string {
  return `${resource}.${action.toLowerCase()}`;
}

/**
 * Validate that a resource/action combination exists in our constants
 */
export function isValidResourceAction(resource: PermissionResource, action: PermissionAction): boolean {
  const permissionString = createPermissionString(resource, action);
  return isValidPermission(permissionString);
}

/**
 * Get all permissions for a specific resource
 */
export function getPermissionsForResource(resource: PermissionResource): string[] {
  const resourceKey = resource.toUpperCase() as keyof typeof PERMISSIONS;
  if (resourceKey in PERMISSIONS) {
    return Object.values(PERMISSIONS[resourceKey]);
  }
  return [];
}

/**
 * Check if a permission string matches a resource/action combination
 */
export function matchesResourceAction(
  permissionString: string, 
  resource: PermissionResource, 
  action: PermissionAction
): boolean {
  const targetPermission = createPermissionString(resource, action);
  return permissionString === targetPermission;
}

/**
 * Get permission constant from resource and action
 */
export function getPermissionConstant(resource: PermissionResource, action: PermissionAction): string {
  const permissionString = createPermissionString(resource, action);
  
  if (!isValidPermission(permissionString)) {
    console.warn(`Permission ${permissionString} is not defined in constants`);
  }
  
  return permissionString;
}

/**
 * Type-safe permission checker that validates against constants
 */
export function createPermissionChecker(userPermissions: string[]) {
  return {
    hasPermission: (resource: PermissionResource, action: PermissionAction): boolean => {
      const permissionString = getPermissionConstant(resource, action);
      return userPermissions.some(p => 
        p === permissionString || 
        p === `${resource}.*` || 
        p === '*'
      );
    },
    
    hasAnyPermission: function(permissions: Array<{ resource: PermissionResource; action: PermissionAction }>): boolean {
      return permissions.some(({ resource, action }) =>
        this.hasPermission(resource, action)
      );
    },

    hasAllPermissions: function(permissions: Array<{ resource: PermissionResource; action: PermissionAction }>): boolean {
      return permissions.every(({ resource, action }) =>
        this.hasPermission(resource, action)
      );
    }
  };
}

/**
 * Migration helper: Get all permission strings that should exist for a resource
 */
export function getExpectedPermissionsForResource(resource: PermissionResource): string[] {
  const actions = Object.values(PermissionAction);
  return actions.map(action => createPermissionString(resource, action))
    .filter(permission => isValidPermission(permission));
}

/**
 * Debug helper: Validate all enum combinations against constants
 */
export function validatePermissionConstants(): {
  valid: string[];
  invalid: string[];
  missing: string[];
} {
  const valid: string[] = [];
  const invalid: string[] = [];
  const missing: string[] = [];
  
  const allResources = Object.values(PermissionResource);
  const allActions = Object.values(PermissionAction);
  
  // Check all possible combinations
  for (const resource of allResources) {
    for (const action of allActions) {
      const permissionString = createPermissionString(resource, action);
      
      if (isValidPermission(permissionString)) {
        valid.push(permissionString);
      } else {
        invalid.push(permissionString);
      }
    }
  }
  
  // Check for permissions in constants that don't have enum equivalents
  const allConstantPermissions = Object.values(PERMISSIONS)
    .flatMap(resourcePerms => Object.values(resourcePerms));
  
  for (const permission of allConstantPermissions) {
    const parsed = parsePermission(permission);
    if (parsed) {
      const hasEnumResource = Object.values(PermissionResource).includes(parsed.resource as PermissionResource);
      const hasEnumAction = Object.values(PermissionAction).includes(parsed.action as PermissionAction);
      
      if (!hasEnumResource || !hasEnumAction) {
        missing.push(permission);
      }
    }
  }
  
  return { valid, invalid, missing };
}

/**
 * Helper to get permission display name
 */
export function getPermissionDisplayName(resource: PermissionResource, action: PermissionAction): string {
  const actionName = action.charAt(0) + action.slice(1).toLowerCase();
  const resourceName = resource.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase());
  return `${actionName} ${resourceName}`;
}

/**
 * Helper to get all permissions for a role type
 */
export function getPermissionsForRole(roleType: 'TENANT_ADMIN' | 'MANAGER' | 'SALES_REP' | 'VIEWER'): string[] {
  const rolePermissions = {
    TENANT_ADMIN: Object.values(PERMISSIONS).flatMap(resourcePerms => Object.values(resourcePerms)),
    MANAGER: [
      ...Object.values(PERMISSIONS.CONTACTS),
      ...Object.values(PERMISSIONS.COMPANIES),
      ...Object.values(PERMISSIONS.LEADS),
      ...Object.values(PERMISSIONS.OPPORTUNITIES),
      ...Object.values(PERMISSIONS.PROPOSALS),
      ...Object.values(PERMISSIONS.PROJECTS),
      PERMISSIONS.USERS.READ,
      PERMISSIONS.ROLES.READ,
      PERMISSIONS.SETTINGS.READ,
      ...Object.values(PERMISSIONS.AI_FEATURES),
      ...Object.values(PERMISSIONS.REPORTS),
      ...Object.values(PERMISSIONS.MEETINGS),
    ],
    SALES_REP: [
      PERMISSIONS.CONTACTS.CREATE,
      PERMISSIONS.CONTACTS.READ,
      PERMISSIONS.CONTACTS.UPDATE,
      PERMISSIONS.CONTACTS.EXPORT,
      PERMISSIONS.COMPANIES.CREATE,
      PERMISSIONS.COMPANIES.READ,
      PERMISSIONS.COMPANIES.UPDATE,
      PERMISSIONS.COMPANIES.EXPORT,
      PERMISSIONS.LEADS.CREATE,
      PERMISSIONS.LEADS.READ,
      PERMISSIONS.LEADS.UPDATE,
      PERMISSIONS.LEADS.EXPORT,
      PERMISSIONS.OPPORTUNITIES.CREATE,
      PERMISSIONS.OPPORTUNITIES.READ,
      PERMISSIONS.OPPORTUNITIES.UPDATE,
      PERMISSIONS.OPPORTUNITIES.EXPORT,
      PERMISSIONS.AI_FEATURES.READ,
      PERMISSIONS.REPORTS.READ,
      PERMISSIONS.MEETINGS.CREATE,
      PERMISSIONS.MEETINGS.READ,
      PERMISSIONS.MEETINGS.UPDATE,
    ],
    VIEWER: [
      PERMISSIONS.CONTACTS.READ,
      PERMISSIONS.COMPANIES.READ,
      PERMISSIONS.LEADS.READ,
      PERMISSIONS.OPPORTUNITIES.READ,
      PERMISSIONS.PROPOSALS.READ,
      PERMISSIONS.PROJECTS.READ,
      PERMISSIONS.REPORTS.READ,
      PERMISSIONS.MEETINGS.READ,
    ],
  };
  
  return rolePermissions[roleType] || [];
}
