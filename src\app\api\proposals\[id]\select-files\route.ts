import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { hasPermission } from '@/lib/permissions';
import { PermissionResource, PermissionAction } from '@/types';
import { prisma } from '@/lib/prisma';
import { geminiFileService } from '@/services/gemini-file-service';
import { GeminiFileUploadBatchRequest } from '@/types/proposal';
import { z } from 'zod';

// Request validation schema
const selectFilesSchema = z.object({
  selectedFileIds: z.array(z.string()).min(1, 'At least one file must be selected'),
  uploadToGemini: z.boolean().default(true)
});

/**
 * POST /api/proposals/[id]/select-files
 * Select RFP files for proposal generation context
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user's current tenant
    const currentTenant = session.user.tenants?.[0];
    if (!currentTenant) {
      return NextResponse.json({ error: 'No tenant access' }, { status: 403 });
    }

    // Check permission for proposal updating
    const hasAccess = await hasPermission(
      PermissionResource.PROPOSALS,
      PermissionAction.UPDATE,
      currentTenant.id
    );

    if (!hasAccess) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const proposalId = params.id;
    const body = await request.json();
    
    // Validate request data
    const validatedData = selectFilesSchema.parse(body);

    // Get proposal and verify access
    const proposal = await prisma.proposal.findFirst({
      where: {
        id: proposalId,
        tenantId: currentTenant.id
      },
      include: {
        opportunity: true
      }
    });

    if (!proposal) {
      return NextResponse.json(
        { error: 'Proposal not found or access denied' },
        { status: 404 }
      );
    }

    if (!proposal.opportunityId) {
      return NextResponse.json(
        { error: 'Proposal is not associated with an opportunity' },
        { status: 400 }
      );
    }

    // Verify all selected files exist and belong to the opportunity
    const documents = await prisma.document.findMany({
      where: {
        id: { in: validatedData.selectedFileIds },
        tenantId: currentTenant.id,
        relatedToType: 'opportunity',
        relatedToId: proposal.opportunityId
      }
    });

    if (documents.length !== validatedData.selectedFileIds.length) {
      return NextResponse.json(
        { error: 'Some selected files are invalid or not associated with this opportunity' },
        { status: 400 }
      );
    }

    // Check if proposal is in a state that allows file selection changes
    if (proposal.generationStatus === 'generating') {
      return NextResponse.json(
        { error: 'Cannot modify file selection while proposal is being generated' },
        { status: 409 }
      );
    }

    let geminiFileUris = proposal.geminiFileUris as Record<string, string> || {};
    let uploadStatus = 'skipped';
    let uploadErrors: string[] = [];

    // Upload files to Gemini API if requested
    if (validatedData.uploadToGemini) {
      try {
        const uploadRequest: GeminiFileUploadBatchRequest = {
          documentIds: validatedData.selectedFileIds,
          tenantId: currentTenant.id
        };

        const uploadResult = await geminiFileService.uploadFilesBatch(uploadRequest);

        // Update geminiFileUris with successful uploads
        uploadResult.successful.forEach(file => {
          // Find the document ID that corresponds to this file
          const document = documents.find(doc =>
            file.displayName === doc.name || file.name.includes(doc.id)
          );
          if (document) {
            geminiFileUris[document.id] = file.fileUri;
          }
        });

        // Track any upload failures
        if (uploadResult.failed.length > 0) {
          uploadErrors = uploadResult.failed.map(f => f.error);
          uploadStatus = 'partial';
        } else {
          uploadStatus = 'completed';
        }

      } catch (error) {
        console.error('Gemini file upload error:', error);
        uploadStatus = 'failed';
        uploadErrors = [error instanceof Error ? error.message : 'Unknown upload error'];
      }
    }

    // Update proposal with selected files and Gemini URIs
    const updatedProposal = await prisma.proposal.update({
      where: { id: proposalId },
      data: {
        selectedFileIds: validatedData.selectedFileIds,
        geminiFileUris: geminiFileUris,
        updatedAt: new Date()
      }
    });

    // Get file details for response
    const selectedDocuments = documents.map(doc => ({
      documentId: doc.id,
      name: doc.name,
      mimeType: doc.mimeType,
      fileSize: doc.fileSize,
      geminiFileUri: geminiFileUris[doc.id],
      uploadStatus: geminiFileUris[doc.id] ? 'uploaded' : 'pending'
    }));

    return NextResponse.json({
      success: true,
      data: {
        proposalId: updatedProposal.id,
        selectedFileIds: validatedData.selectedFileIds,
        selectedDocuments,
        geminiUploadStatus: uploadStatus,
        uploadErrors: uploadErrors.length > 0 ? uploadErrors : undefined,
        totalSelected: validatedData.selectedFileIds.length,
        successfulUploads: Object.keys(geminiFileUris).length,
        failedUploads: uploadErrors.length
      },
      message: uploadStatus === 'failed'
        ? 'File selection saved but Gemini upload failed'
        : uploadStatus === 'partial'
        ? 'File selection saved with some upload failures'
        : 'File selection updated successfully'
    });

  } catch (error) {
    console.error('Error selecting files for proposal:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          error: 'Invalid request data', 
          details: error.errors.map(e => ({
            field: e.path.join('.'),
            message: e.message
          }))
        },
        { status: 400 }
      );
    }

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * @swagger
 * /api/proposals/{id}/select-files:
 *   post:
 *     summary: Select RFP files for proposal generation
 *     description: Select which opportunity files to use as context for AI proposal generation
 *     tags: [Proposals]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Proposal ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - selectedFileIds
 *             properties:
 *               selectedFileIds:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: Array of document IDs to use as RFP context
 *                 minItems: 1
 *               uploadToGemini:
 *                 type: boolean
 *                 description: Whether to upload files to Gemini API immediately
 *                 default: true
 *     responses:
 *       200:
 *         description: File selection updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     proposalId:
 *                       type: string
 *                     selectedFileIds:
 *                       type: array
 *                       items:
 *                         type: string
 *                     selectedDocuments:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           documentId:
 *                             type: string
 *                           name:
 *                             type: string
 *                           mimeType:
 *                             type: string
 *                           fileSize:
 *                             type: number
 *                           geminiFileUri:
 *                             type: string
 *                     geminiUploadStatus:
 *                       type: string
 *                       enum: [completed, skipped, failed]
 *                     totalSelected:
 *                       type: number
 *                 message:
 *                   type: string
 *       400:
 *         description: Invalid request data
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - insufficient permissions
 *       404:
 *         description: Proposal not found
 *       409:
 *         description: Conflict - proposal is being generated
 *       500:
 *         description: Internal server error
 */
