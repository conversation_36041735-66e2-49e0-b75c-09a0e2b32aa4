import { prisma } from '@/lib/prisma';
import Stripe from 'stripe';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2025-05-28.basil',
});

export interface SubscriptionPlanData {
  name: string;
  description: string;
  priceMonthly: number;
  priceYearly: number;
  features: {
    contacts: number | 'unlimited';
    leads: number | 'unlimited';
    users: number | 'unlimited';
    aiRequests: number | 'unlimited';
    storage: number; // in MB
    customIntegrations: boolean;
    prioritySupport: boolean;
    customBranding: boolean;
  };
  limits: {
    [key: string]: number;
  };
  stripePriceMonthlyId?: string;
  stripePriceYearlyId?: string;
}

export interface CreateSubscriptionData {
  tenantId: string;
  planId: string;
  billingCycle: 'monthly' | 'yearly';
  paymentMethodId?: string;
  trialDays?: number;
}

export interface UpdateSubscriptionData {
  planId?: string;
  billingCycle?: 'monthly' | 'yearly';
  cancelAtPeriodEnd?: boolean;
}

export class SubscriptionManagementService {
  // Create subscription plan
  async createSubscriptionPlan(planData: SubscriptionPlanData) {
    return await prisma.$transaction(async (tx) => {
      // Create Stripe products and prices
      const stripeProduct = await stripe.products.create({
        name: planData.name,
        description: planData.description,
        metadata: {
          type: 'subscription_plan'
        }
      });

      const stripePriceMonthly = await stripe.prices.create({
        product: stripeProduct.id,
        unit_amount: Math.round(planData.priceMonthly * 100), // Convert to cents
        currency: 'usd',
        recurring: {
          interval: 'month'
        },
        metadata: {
          billing_cycle: 'monthly'
        }
      });

      const stripePriceYearly = await stripe.prices.create({
        product: stripeProduct.id,
        unit_amount: Math.round(planData.priceYearly * 100), // Convert to cents
        currency: 'usd',
        recurring: {
          interval: 'year'
        },
        metadata: {
          billing_cycle: 'yearly'
        }
      });

      // Create plan in database
      const plan = await tx.subscriptionPlan.create({
        data: {
          name: planData.name,
          description: planData.description,
          priceMonthly: planData.priceMonthly,
          priceYearly: planData.priceYearly,
          features: planData.features as any,
          limits: planData.limits,
          isActive: true
        }
      });

      return {
        ...plan,
        stripeProductId: stripeProduct.id,
        stripePriceMonthlyId: stripePriceMonthly.id,
        stripePriceYearlyId: stripePriceYearly.id
      };
    });
  }

  // Create subscription for tenant
  async createSubscription(subscriptionData: CreateSubscriptionData) {
    return await prisma.$transaction(async (tx) => {
      // Get tenant and plan details
      const [tenant, plan] = await Promise.all([
        tx.tenant.findUnique({ where: { id: subscriptionData.tenantId } }),
        tx.subscriptionPlan.findUnique({ where: { id: subscriptionData.planId } })
      ]);

      if (!tenant || !plan) {
        throw new Error('Tenant or plan not found');
      }

      // Create or get Stripe customer
      let stripeCustomerId = '';
      const currentSubscription = await tx.subscription.findFirst({
        where: { tenantId: subscriptionData.tenantId }
      });

      if (currentSubscription?.stripeCustomerId) {
        stripeCustomerId = currentSubscription.stripeCustomerId;
      } else {
        const stripeCustomer = await stripe.customers.create({
          name: tenant.name,
          metadata: {
            tenantId: tenant.id,
            tenantSlug: tenant.slug
          }
        });
        stripeCustomerId = stripeCustomer.id;
      }

      // Determine price ID based on billing cycle
      const priceId = subscriptionData.billingCycle === 'yearly' 
        ? process.env[`STRIPE_PRICE_${plan.name.toUpperCase()}_YEARLY`]
        : process.env[`STRIPE_PRICE_${plan.name.toUpperCase()}_MONTHLY`];

      if (!priceId) {
        throw new Error(`Stripe price ID not configured for plan ${plan.name}`);
      }

      // Create Stripe subscription
      const stripeSubscriptionData: any = {
        customer: stripeCustomerId,
        items: [{ price: priceId }],
        metadata: {
          tenantId: tenant.id,
          planId: plan.id
        }
      };

      if (subscriptionData.trialDays) {
        stripeSubscriptionData.trial_period_days = subscriptionData.trialDays;
      }

      if (subscriptionData.paymentMethodId) {
        stripeSubscriptionData.default_payment_method = subscriptionData.paymentMethodId;
      }

      const stripeSubscription = await stripe.subscriptions.create(stripeSubscriptionData);

      // Create or update subscription in database
      const currentDate = new Date();
      const stripeSubData = stripeSubscription as any; // Type assertion for compatibility
      const periodEnd = new Date(stripeSubData.current_period_end * 1000);

      // Find existing subscription first
      const existingSubscription = await tx.subscription.findFirst({
        where: { tenantId: subscriptionData.tenantId }
      });

      let subscription;
      if (existingSubscription) {
        subscription = await tx.subscription.update({
          where: { id: existingSubscription.id },
          data: {
            planId: subscriptionData.planId,
            status: stripeSubData.status,
            currentPeriodStart: new Date(stripeSubData.current_period_start * 1000),
            currentPeriodEnd: periodEnd,
            stripeSubscriptionId: stripeSubData.id,
            stripeCustomerId: stripeCustomerId,
            updatedAt: currentDate
          }
        });
      } else {
        subscription = await tx.subscription.create({
          data: {
            tenantId: subscriptionData.tenantId,
            planId: subscriptionData.planId,
            status: stripeSubData.status,
            currentPeriodStart: new Date(stripeSubData.current_period_start * 1000),
            currentPeriodEnd: periodEnd,
            stripeSubscriptionId: stripeSubData.id,
            stripeCustomerId: stripeCustomerId
          }
        });
      }

      // Log billing event
      await tx.billingEvent.create({
        data: {
          tenantId: subscriptionData.tenantId,
          subscriptionId: subscription.id,
          eventType: 'subscription_created',
          amount: subscriptionData.billingCycle === 'yearly' ? plan.priceYearly : plan.priceMonthly,
          currency: 'USD',
          stripeEventId: stripeSubscription.id,
          eventData: {
            planName: plan.name,
            billingCycle: subscriptionData.billingCycle,
            trialDays: subscriptionData.trialDays
          }
        }
      });

      return subscription;
    });
  }

  // Update subscription
  async updateSubscription(tenantId: string, updateData: UpdateSubscriptionData) {
    return await prisma.$transaction(async (tx) => {
      const subscription = await tx.subscription.findFirst({
        where: { tenantId },
        include: { plan: true }
      });

      if (!subscription || !subscription.stripeSubscriptionId) {
        throw new Error('Subscription not found');
      }

      const updatePayload: any = {};

      // Handle plan change
      if (updateData.planId && updateData.planId !== subscription.planId) {
        const newPlan = await tx.subscriptionPlan.findUnique({
          where: { id: updateData.planId }
        });

        if (!newPlan) {
          throw new Error('New plan not found');
        }

        const newPriceId = updateData.billingCycle === 'yearly'
          ? process.env[`STRIPE_PRICE_${newPlan.name.toUpperCase()}_YEARLY`]
          : process.env[`STRIPE_PRICE_${newPlan.name.toUpperCase()}_MONTHLY`];

        if (!newPriceId) {
          throw new Error(`Stripe price ID not configured for plan ${newPlan.name}`);
        }

        // Update Stripe subscription
        const stripeSubscription = await stripe.subscriptions.update(
          subscription.stripeSubscriptionId,
          {
            items: [{
              id: (await stripe.subscriptions.retrieve(subscription.stripeSubscriptionId)).items.data[0].id,
              price: newPriceId
            }],
            proration_behavior: 'create_prorations'
          }
        );

        const stripeSubData = stripeSubscription as any;
        updatePayload.planId = updateData.planId;
        updatePayload.status = stripeSubData.status;
        updatePayload.currentPeriodStart = new Date(stripeSubData.current_period_start * 1000);
        updatePayload.currentPeriodEnd = new Date(stripeSubData.current_period_end * 1000);
      }

      // Handle cancellation
      if (updateData.cancelAtPeriodEnd !== undefined) {
        await stripe.subscriptions.update(
          subscription.stripeSubscriptionId,
          {
            cancel_at_period_end: updateData.cancelAtPeriodEnd
          }
        );

        if (updateData.cancelAtPeriodEnd) {
          updatePayload.status = 'cancelled';
        }
      }

      // Update database
      const updatedSubscription = await tx.subscription.update({
        where: { id: subscription.id },
        data: {
          ...updatePayload,
          updatedAt: new Date()
        },
        include: { plan: true }
      });

      // Log billing event
      await tx.billingEvent.create({
        data: {
          tenantId,
          subscriptionId: subscription.id,
          eventType: updateData.cancelAtPeriodEnd ? 'subscription_cancelled' : 'subscription_updated',
          eventData: updateData as any
        }
      });

      return updatedSubscription;
    });
  }

  // Get subscription with usage
  async getSubscriptionWithUsage(tenantId: string) {
    const subscription = await prisma.subscription.findFirst({
      where: { tenantId },
      include: {
        plan: true,
        tenant: true
      }
    });

    if (!subscription) {
      return null;
    }

    // Get current usage metrics
    const currentDate = new Date();
    const periodStart = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);
    
    const usageMetrics = await prisma.usageMetric.findMany({
      where: {
        tenantId,
        periodStart: {
          gte: periodStart
        }
      }
    });

    const usage = usageMetrics.reduce((acc, metric) => {
      acc[metric.metricName] = metric.metricValue;
      return acc;
    }, {} as Record<string, number>);

    return {
      ...subscription,
      usage
    };
  }

  // Track usage
  async trackUsage(tenantId: string, metricName: string, increment: number = 1) {
    const currentDate = new Date();
    const periodStart = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);
    const periodEnd = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0);

    await prisma.usageMetric.upsert({
      where: {
        tenantId_metricName_periodStart: {
          tenantId,
          metricName,
          periodStart
        }
      },
      update: {
        metricValue: {
          increment
        }
      },
      create: {
        tenantId,
        metricName,
        metricValue: increment,
        periodStart,
        periodEnd
      }
    });
  }

  // Check usage limits
  async checkUsageLimit(tenantId: string, metricName: string): Promise<{
    current: number;
    limit: number;
    isExceeded: boolean;
    percentage: number;
  }> {
    const subscription = await this.getSubscriptionWithUsage(tenantId);
    
    if (!subscription) {
      throw new Error('No subscription found');
    }

    const current = subscription.usage[metricName] || 0;
    const planLimits = subscription.plan?.limits as any;
    const limit = planLimits?.[metricName] || 0;
    const isExceeded = limit > 0 && current >= limit;
    const percentage = limit > 0 ? (current / limit) * 100 : 0;

    return {
      current,
      limit,
      isExceeded,
      percentage
    };
  }

  // Get all subscription plans
  async getSubscriptionPlans() {
    return await prisma.subscriptionPlan.findMany({
      where: { isActive: true },
      orderBy: { priceMonthly: 'asc' }
    });
  }
}
