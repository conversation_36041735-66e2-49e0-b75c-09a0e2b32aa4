import { getToken } from 'next-auth/jwt';
import { NextRequest, NextResponse } from 'next/server';

// Cache for static route checks to avoid repeated string operations
const staticRouteCache = new Set([
  '/_next',
  '/favicon.ico',
  '/robots.txt',
  '/sitemap.xml',
  '/manifest.json',
  '/sw.js',
  '/workbox-',
  '/api/docs',
  '/api/health'
]);

// Performance optimization: Pre-compile route patterns
const isStaticRoute = (pathname: string): boolean => {
  return Array.from(staticRouteCache).some(route => pathname.startsWith(route));
};

// Define auth routes that should redirect to dashboard if already authenticated
const authRoutes = [
  '/auth/signin',
  '/auth/signup',
];

// Define protected routes that require authentication
const protectedRoutes = [
  '/dashboard',
  '/admin',
  '/contacts',
  '/companies',
  '/leads',
  '/opportunities',
  '/proposals',
  '/projects',
  '/activities',
  '/settings',
  '/profile',
];

// Define Super Admin only routes
const superAdminRoutes = [
  '/admin',
];

// Define tenant-specific routes (Super Admin should be redirected away from these)
const tenantOnlyRoutes = [
  '/dashboard',
  '/contacts',
  '/companies',
  '/leads',
  '/opportunities',
  '/proposals',
  '/projects',
  '/activities',
  '/settings',
  '/profile',
];

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // Performance optimization: Skip middleware for static assets and API docs
  if (isStaticRoute(pathname)) {
    return NextResponse.next();
  }

  // Performance optimization: Cache token validation for a short period
  // const tokenCacheKey = request.headers.get('authorization') || request.cookies.get('next-auth.session-token')?.value;

  // Get the token from the request with optimized settings
  const token = await getToken({
    req: request,
    secret: process.env.NEXTAUTH_SECRET,
    // Optimize token validation
    secureCookie: process.env.NODE_ENV === 'production',
  });

  const isAuthenticated = !!token;
  const isSuperAdmin = !!token?.isPlatformAdmin;
  const isAuthRoute = authRoutes.some(route => pathname.startsWith(route));
  const isProtectedRoute = protectedRoutes.some(route => pathname.startsWith(route));
  const isSuperAdminRoute = superAdminRoutes.some(route => pathname.startsWith(route));
  const isTenantOnlyRoute = tenantOnlyRoutes.some(route => pathname.startsWith(route));
  const isRootRoute = pathname === '/';

  // Handle root route
  if (isRootRoute) {
    if (isAuthenticated) {
      // Redirect Super Admin to admin dashboard, regular users to tenant dashboard
      if (isSuperAdmin) {
        return NextResponse.redirect(new URL('/admin/dashboard', request.url));
      } else {
        return NextResponse.redirect(new URL('/dashboard', request.url));
      }
    } else {
      // Redirect unauthenticated users to signin
      return NextResponse.redirect(new URL('/auth/signin', request.url));
    }
  }

  // Handle auth routes (signin, signup) - redirect based on user type if already authenticated
  if (isAuthRoute && isAuthenticated) {
    if (isSuperAdmin) {
      return NextResponse.redirect(new URL('/admin/dashboard', request.url));
    } else {
      return NextResponse.redirect(new URL('/dashboard', request.url));
    }
  }

  // Handle Super Admin route access
  if (isSuperAdminRoute && isAuthenticated && !isSuperAdmin) {
    // Regular users trying to access admin routes - redirect to their dashboard
    return NextResponse.redirect(new URL('/dashboard', request.url));
  }

  // Handle tenant-only route access for Super Admin
  if (isTenantOnlyRoute && isAuthenticated && isSuperAdmin) {
    // Super Admin trying to access tenant routes - redirect to admin dashboard
    return NextResponse.redirect(new URL('/admin/dashboard', request.url));
  }

  // Handle protected routes - redirect to signin if not authenticated
  if (isProtectedRoute && !isAuthenticated) {
    const signInUrl = new URL('/auth/signin', request.url);
    signInUrl.searchParams.set('callbackUrl', pathname);
    return NextResponse.redirect(signInUrl);
  }

  // Create response with performance headers
  const response = NextResponse.next();

  // Add performance and security headers
  response.headers.set('X-Middleware-Cache', token ? 'HIT' : 'MISS');
  response.headers.set('X-Response-Time', Date.now().toString());

  // Add tenant context headers if needed
  const tenantId = request.headers.get('x-tenant-id');
  if (tenantId) {
    response.headers.set('x-tenant-id', tenantId);
  }

  // Performance monitoring in development
  if (process.env.NODE_ENV === 'development') {
    // const processingTime = Date.now() - parseInt(response.headers.get('X-Response-Time') || '0');
    // Performance monitoring without console output
  }

  // Add user context headers for authenticated requests
  if (token) {
    response.headers.set('x-user-id', token.id as string);
    response.headers.set('x-user-email', token.email as string);
    if (token.tenants && Array.isArray(token.tenants) && token.tenants.length > 0) {
      response.headers.set('x-current-tenant', JSON.stringify(token.tenants[0]));
    }
  }

  // Add security headers
  response.headers.set('X-Frame-Options', 'DENY');
  response.headers.set('X-Content-Type-Options', 'nosniff');
  response.headers.set('Referrer-Policy', 'origin-when-cross-origin');
  response.headers.set('X-XSS-Protection', '1; mode=block');

  return response;
}

export const config = {
  // Match all routes except API, static files, and Next.js internals
  matcher: [
    '/((?!api|_next|_vercel|favicon.ico|.*\\..*).*)'
  ]
};
