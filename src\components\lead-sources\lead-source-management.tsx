'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import {
  Search,
  Filter,
  MoreHorizontal,
  Edit,
  Trash2,
  Eye,
  Users,
  TrendingUp,
  Activity
} from 'lucide-react';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger 
} from '@/components/ui/dropdown-menu';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';
import { EmptyState } from '@/components/ui/empty-state';
import { PermissionAwareTable } from '@/components/ui/permission-aware-table';
import { PermissionResource } from '@/types';
import { LeadSourceWithRelations } from '@/services/lead-source-management';
import { toastFunctions as toast } from '@/hooks/use-toast';

interface LeadSourceStats {
  total: number;
  active: number;
  inactive: number;
  byCategory: Record<string, number>;
  totalLeads: number;
}

export function LeadSourceManagement() {
  const router = useRouter();
  const [leadSources, setLeadSources] = useState<LeadSourceWithRelations[]>([]);
  const [stats, setStats] = useState<LeadSourceStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [categoryFilter, setCategoryFilter] = useState<string>('');
  const [statusFilter, setStatusFilter] = useState<string>('');

  // Load lead sources
  const loadLeadSources = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        page: '1',
        limit: '100',
        ...(searchTerm && { search: searchTerm }),
        ...(categoryFilter && categoryFilter !== 'all' && { category: categoryFilter }),
        ...(statusFilter && statusFilter !== 'all' && { isActive: statusFilter })
      });

      const response = await fetch(`/api/lead-sources?${params}`);
      const data = await response.json();

      if (data.success) {
        setLeadSources(data.data.leadSources);
      } else {
        toast.error('Failed to load lead sources');
      }
    } catch (error) {
      console.error('Error loading lead sources:', error);
      toast.error('Failed to load lead sources');
    } finally {
      setLoading(false);
    }
  };

  // Load stats
  const loadStats = async () => {
    try {
      const response = await fetch('/api/lead-sources/stats');
      const data = await response.json();

      if (data.success) {
        setStats(data.data);
      }
    } catch (error) {
      console.error('Error loading stats:', error);
    }
  };

  // Delete lead source
  const handleDelete = async (leadSourceId: string) => {
    if (!confirm('Are you sure you want to delete this lead source? This action cannot be undone.')) {
      return;
    }

    try {
      const response = await fetch(`/api/lead-sources/${leadSourceId}`, {
        method: 'DELETE'
      });

      const data = await response.json();

      if (data.success) {
        toast.success('Lead source deleted successfully');
        loadLeadSources();
        loadStats();
      } else {
        toast.error(data.error || 'Failed to delete lead source');
      }
    } catch (error) {
      console.error('Error deleting lead source:', error);
      toast.error('Failed to delete lead source');
    }
  };

  // Toggle lead source status
  const handleToggleStatus = async (leadSource: LeadSourceWithRelations) => {
    try {
      const response = await fetch(`/api/lead-sources/${leadSource.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ isActive: !leadSource.isActive })
      });

      const data = await response.json();

      if (data.success) {
        toast.success(`Lead source ${leadSource.isActive ? 'deactivated' : 'activated'} successfully`);
        loadLeadSources();
        loadStats();
      } else {
        toast.error(data.error || 'Failed to update lead source');
      }
    } catch (error) {
      console.error('Error updating lead source:', error);
      toast.error('Failed to update lead source');
    }
  };

  useEffect(() => {
    loadLeadSources();
    loadStats();
  }, [searchTerm, categoryFilter, statusFilter]);

  // Table columns
  const columns = [
    {
      key: 'name',
      label: 'Name',
      render: (leadSource: LeadSourceWithRelations) => (
        <div className="flex items-center space-x-3">
          {leadSource.icon && (
            <div 
              className="w-8 h-8 rounded-lg flex items-center justify-center text-white text-sm font-medium"
              style={{ backgroundColor: leadSource.color || '#6B7280' }}
            >
              {leadSource.icon}
            </div>
          )}
          <div>
            <div className="font-medium">{leadSource.name}</div>
            {leadSource.description && (
              <div className="text-sm text-muted-foreground">{leadSource.description}</div>
            )}
          </div>
        </div>
      )
    },
    {
      key: 'category',
      label: 'Category',
      render: (leadSource: LeadSourceWithRelations) => (
        <Badge variant="secondary">
          {leadSource.category.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
        </Badge>
      )
    },
    {
      key: 'leads',
      label: 'Leads',
      render: (leadSource: LeadSourceWithRelations) => (
        <div className="text-center">
          <div className="font-medium">{leadSource._count?.leads || 0}</div>
          <div className="text-xs text-muted-foreground">leads</div>
        </div>
      )
    },
    {
      key: 'status',
      label: 'Status',
      render: (leadSource: LeadSourceWithRelations) => (
        <Badge variant={leadSource.isActive ? 'default' : 'secondary'}>
          {leadSource.isActive ? 'Active' : 'Inactive'}
        </Badge>
      )
    },
    {
      key: 'actions',
      label: 'Actions',
      render: (leadSource: LeadSourceWithRelations) => (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="sm">
              <MoreHorizontal className="w-4 h-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onClick={() => router.push(`/leads/sources/${leadSource.id}`)}>
              <Eye className="w-4 h-4 mr-2" />
              View Details
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => router.push(`/leads/sources/${leadSource.id}/edit`)}>
              <Edit className="w-4 h-4 mr-2" />
              Edit
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => handleToggleStatus(leadSource)}>
              <Activity className="w-4 h-4 mr-2" />
              {leadSource.isActive ? 'Deactivate' : 'Activate'}
            </DropdownMenuItem>
            <DropdownMenuItem 
              onClick={() => handleDelete(leadSource.id)}
              className="text-destructive"
            >
              <Trash2 className="w-4 h-4 mr-2" />
              Delete
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      )
    }
  ];

  return (
    <div className="space-y-6">
      {/* Stats Cards */}
      {stats && (
        <div className="grid gap-4 md:grid-cols-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <TrendingUp className="w-6 h-6 text-blue-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-muted-foreground">Total Sources</p>
                  <p className="text-2xl font-bold">{stats.total}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-green-100 rounded-lg">
                  <Activity className="w-6 h-6 text-green-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-muted-foreground">Active Sources</p>
                  <p className="text-2xl font-bold">{stats.active}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-purple-100 rounded-lg">
                  <Users className="w-6 h-6 text-purple-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-muted-foreground">Total Leads</p>
                  <p className="text-2xl font-bold">{stats.totalLeads}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-orange-100 rounded-lg">
                  <Filter className="w-6 h-6 text-orange-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-muted-foreground">Categories</p>
                  <p className="text-2xl font-bold">{Object.keys(stats.byCategory).length}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Lead Sources</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col sm:flex-row gap-4 mb-6">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                <Input
                  placeholder="Search lead sources..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={categoryFilter || undefined} onValueChange={(value) => setCategoryFilter(value || '')}>
              <SelectTrigger className="w-full sm:w-48">
                <SelectValue placeholder="All Categories" />
              </SelectTrigger>
              <SelectContent position="popper" sideOffset={4}>
                <SelectItem value="all">All Categories</SelectItem>
                <SelectItem value="digital">Digital</SelectItem>
                <SelectItem value="outbound">Outbound</SelectItem>
                <SelectItem value="paid_advertising">Paid Advertising</SelectItem>
                <SelectItem value="events">Events</SelectItem>
                <SelectItem value="partnerships">Partnerships</SelectItem>
                <SelectItem value="traditional">Traditional</SelectItem>
                <SelectItem value="word_of_mouth">Word of Mouth</SelectItem>
                <SelectItem value="existing_relationships">Existing Relationships</SelectItem>
                <SelectItem value="custom">Custom</SelectItem>
                <SelectItem value="other">Other</SelectItem>
              </SelectContent>
            </Select>
            <Select value={statusFilter || undefined} onValueChange={(value) => setStatusFilter(value || '')}>
              <SelectTrigger className="w-full sm:w-32">
                <SelectValue placeholder="All Status" />
              </SelectTrigger>
              <SelectContent position="popper" sideOffset={4}>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="true">Active</SelectItem>
                <SelectItem value="false">Inactive</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Lead Sources Table */}
          {leadSources.length === 0 && !loading ? (
            <EmptyState
              icon={<TrendingUp className="w-12 h-12" />}
              title="No lead sources found"
              description="Get started by creating your first lead source to track where your leads are coming from."
              action={{
                label: 'Add Lead Source',
                onClick: () => router.push('/leads/sources/new'),
                variant: 'primary'
              }}
            />
          ) : (
            <PermissionAwareTable
              data={leadSources}
              columns={columns}
              loading={loading}
              resource={PermissionResource.LEADS}
              idField="id"
              emptyMessage="No lead sources found"
            />
          )}
        </CardContent>
      </Card>
    </div>
  );
}
