import { NextResponse } from 'next/server';
import { z } from 'zod';
import { withPermission, AuthenticatedRequest } from '@/lib/permission-middleware';
import { scheduledCallService, getScheduledCallsQuerySchema, createScheduledCallSchema } from '@/services/scheduled-call-service';
import { PermissionAction, PermissionResource } from '@/types';

/**
 * GET /api/calls
 * Get scheduled calls with filtering and pagination
 */
export const GET = withPermission({
  resource: PermissionResource.CALLS,
  action: PermissionAction.READ,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest) => {
  try {
    const { searchParams } = new URL(req.url);
    const queryParams = Object.fromEntries(searchParams.entries());
    
    const validatedQuery = getScheduledCallsQuerySchema.parse(queryParams);

    const result = await scheduledCallService.getScheduledCalls(req.tenantId, validatedQuery);

    return NextResponse.json({
      success: true,
      data: result,
      message: 'Scheduled calls retrieved successfully'
    });

  } catch (error) {
    console.error('Get scheduled calls error:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          error: 'Invalid query parameters',
          details: error.errors.map(e => ({
            field: e.path.join('.'),
            message: e.message
          }))
        },
        { status: 400 }
      );
    }

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
});

/**
 * POST /api/calls
 * Create a new scheduled call
 */
export const POST = withPermission({
  resource: PermissionResource.CALLS,
  action: PermissionAction.CREATE,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest) => {
  try {
    const body = await req.json();
    const validatedData = createScheduledCallSchema.parse(body);

    const scheduledCall = await scheduledCallService.createScheduledCall(
      req.tenantId,
      validatedData,
      req.userId
    );

    return NextResponse.json({
      success: true,
      data: { scheduledCall },
      message: 'Scheduled call created successfully'
    }, { status: 201 });

  } catch (error) {
    console.error('Create scheduled call error:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          error: 'Validation failed',
          details: error.errors.map(e => ({
            field: e.path.join('.'),
            message: e.message
          }))
        },
        { status: 400 }
      );
    }

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
});

/**
 * PUT /api/calls
 * Update an existing scheduled call
 */
export const PUT = withPermission({
  resource: PermissionResource.CALLS,
  action: PermissionAction.UPDATE,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest) => {
  try {
    const body = await req.json();
    const { callId, ...updateData } = body;

    if (!callId) {
      return NextResponse.json(
        { error: 'Call ID is required' },
        { status: 400 }
      );
    }

    const scheduledCall = await scheduledCallService.updateScheduledCall(
      callId,
      req.tenantId,
      updateData,
      req.userId
    );

    return NextResponse.json({
      success: true,
      data: { scheduledCall },
      message: 'Scheduled call updated successfully'
    });

  } catch (error) {
    console.error('Update scheduled call error:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          error: 'Validation failed',
          details: error.errors.map(e => ({
            field: e.path.join('.'),
            message: e.message
          }))
        },
        { status: 400 }
      );
    }

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
});

/**
 * DELETE /api/calls
 * Delete a scheduled call
 */
export const DELETE = withPermission({
  resource: PermissionResource.CALLS,
  action: PermissionAction.DELETE,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest) => {
  try {
    const { searchParams } = new URL(req.url);
    const callId = searchParams.get('callId');

    if (!callId) {
      return NextResponse.json(
        { error: 'Call ID is required' },
        { status: 400 }
      );
    }

    await scheduledCallService.deleteScheduledCall(callId, req.tenantId);

    return NextResponse.json({
      success: true,
      message: 'Scheduled call deleted successfully'
    });

  } catch (error) {
    console.error('Delete scheduled call error:', error);

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
});
