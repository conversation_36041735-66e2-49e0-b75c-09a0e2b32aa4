"use client"

import { 
  UsersIcon, 
  BuildingOfficeIcon, 
  ChartBarIcon, 
  CurrencyDollarIcon,
  ArrowUpIcon,
  ArrowDownIcon
} from '@heroicons/react/24/outline'
import { cn, formatCurrency, formatNumber } from '@/lib/utils'

interface StatCardProps {
  title: string
  value: string | number
  change: {
    value: number
    type: 'increase' | 'decrease'
    period: string
  }
  icon: React.ComponentType<{ className?: string }>
  trend: string
}

function StatCard({ title, value, change, icon: Icon, trend }: StatCardProps) {
  const isPositive = change.type === 'increase'
  
  return (
    <div className="rounded-lg border bg-card text-card-foreground shadow-sm p-6">
      <div className="flex items-center justify-between space-y-0 pb-2">
        <h3 className="tracking-tight text-sm font-medium text-muted-foreground">
          {title}
        </h3>
        <Icon className="h-4 w-4 text-muted-foreground" />
      </div>
      <div className="space-y-1">
        <div className="text-2xl font-bold">
          {typeof value === 'number' ? formatNumber(value) : value}
        </div>
        <div className="flex items-center space-x-1 text-xs">
          {isPositive ? (
            <ArrowUpIcon className="h-3 w-3 text-green-600" />
          ) : (
            <ArrowDownIcon className="h-3 w-3 text-red-600" />
          )}
          <span className={cn(
            "font-medium",
            isPositive ? "text-green-600" : "text-red-600"
          )}>
            {isPositive ? '+' : ''}{change.value}%
          </span>
          <span className="text-muted-foreground">{change.period}</span>
        </div>
        <p className="text-xs text-muted-foreground">
          {trend}
        </p>
      </div>
    </div>
  )
}

export function SectionCards() {
  // Mock data - in a real app, this would come from an API
  const stats = [
    {
      title: 'Total Revenue',
      value: '$1,250.00',
      change: {
        value: 12.5,
        type: 'increase' as const,
        period: 'from last month',
      },
      icon: CurrencyDollarIcon,
      trend: 'Trending up this month',
    },
    {
      title: 'New Customers',
      value: 1234,
      change: {
        value: 20,
        type: 'decrease' as const,
        period: 'from last month',
      },
      icon: UsersIcon,
      trend: 'Down 20% this period',
    },
    {
      title: 'Active Accounts',
      value: 45678,
      change: {
        value: 12.5,
        type: 'increase' as const,
        period: 'from last month',
      },
      icon: BuildingOfficeIcon,
      trend: 'Strong user retention',
    },
    {
      title: 'Growth Rate',
      value: '4.5%',
      change: {
        value: 4.5,
        type: 'increase' as const,
        period: 'from last month',
      },
      icon: ChartBarIcon,
      trend: 'Steady performance increase',
    },
  ]

  return (
    <div className="grid gap-4 md:grid-cols-2 md:gap-6 lg:grid-cols-4 px-4 lg:px-6">
      {stats.map((stat, index) => (
        <StatCard
          key={index}
          title={stat.title}
          value={stat.value}
          change={stat.change}
          icon={stat.icon}
          trend={stat.trend}
        />
      ))}
    </div>
  )
}
