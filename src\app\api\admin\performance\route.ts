import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { performanceMonitor } from '@/lib/performance-monitor';
import { DatabasePerformanceMonitor } from '@/lib/prisma';
import { cacheService } from '@/lib/redis';

/**
 * @swagger
 * /api/admin/performance:
 *   get:
 *     summary: Get system performance metrics (Super Admin only)
 *     description: Returns comprehensive performance metrics including database, cache, API, and system health
 *     tags:
 *       - Admin
 *       - Performance
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: timeWindow
 *         schema:
 *           type: string
 *           enum: [5m, 15m, 1h, 6h, 24h]
 *           default: 15m
 *         description: Time window for metrics aggregation
 *       - in: query
 *         name: detailed
 *         schema:
 *           type: boolean
 *           default: false
 *         description: Include detailed metrics breakdown
 *     responses:
 *       200:
 *         description: Performance metrics retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     systemHealth:
 *                       type: object
 *                     performanceSummary:
 *                       type: object
 *                     databaseMetrics:
 *                       type: object
 *                     cacheMetrics:
 *                       type: object
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Super Admin access required
 */
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    // Check authentication
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check Super Admin access
    if (!session.user.isPlatformAdmin) {
      return NextResponse.json(
        { error: 'Super Admin access required' },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const timeWindow = searchParams.get('timeWindow') || '15m';
    const detailed = searchParams.get('detailed') === 'true';

    // Convert time window to milliseconds
    const timeWindowMs = parseTimeWindow(timeWindow);

    // Get system health
    const systemHealth = await performanceMonitor.getSystemHealth();

    // Get performance summary
    const performanceSummary = performanceMonitor.getPerformanceSummary();

    // Get database metrics
    const databaseMetrics = {
      queryStats: DatabasePerformanceMonitor.getQueryStats(),
      averageQueryTime: DatabasePerformanceMonitor.getAverageQueryTime('getTenantInfo'),
      slowQueries: Object.entries(DatabasePerformanceMonitor.getQueryStats())
        .filter(([_, stats]) => stats.avg > 1000)
        .map(([name, stats]) => ({ name, ...stats })),
    };

    // Get cache metrics
    const cacheStats = await cacheService.getStats();
    const cacheMetrics = {
      hitRate: cacheStats.hitRate,
      memory: cacheStats.memory,
      keys: cacheStats.keys,
      hits: cacheStats.hits,
      misses: cacheStats.misses,
    };

    // Get API metrics
    const apiMetrics = {
      averageResponseTime: performanceMonitor.getAverageMetric('api_request', timeWindowMs),
      p95ResponseTime: performanceMonitor.getPercentile('api_request', 95, timeWindowMs),
      p99ResponseTime: performanceMonitor.getPercentile('api_request', 99, timeWindowMs),
      errorRate: performanceMonitor.getPerformanceSummary().errorRate,
      requestsPerMinute: Math.round(performanceMonitor.getAverageMetric('api_request_count', timeWindowMs) || 120),
    };

    // Get memory metrics
    const memoryUsage = process.memoryUsage();
    const memoryMetrics = {
      heapUsed: memoryUsage.heapUsed,
      heapTotal: memoryUsage.heapTotal,
      external: memoryUsage.external,
      rss: memoryUsage.rss,
      heapUsedPercentage: (memoryUsage.heapUsed / memoryUsage.heapTotal) * 100,
    };

    const responseData: any = {
      success: true,
      data: {
        timestamp: Date.now(),
        timeWindow,
        systemHealth,
        performanceSummary,
        databaseMetrics,
        cacheMetrics,
        apiMetrics,
        memoryMetrics,
      },
    };

    // Add detailed metrics if requested
    if (detailed) {
      responseData.data.detailedMetrics = {
        recentApiRequests: performanceMonitor.getMetrics('api_request', 100),
        recentDatabaseQueries: performanceMonitor.getMetrics('database_query', 100),
        recentCacheOperations: performanceMonitor.getMetrics('cache_operation', 100),
      };
    }

    return NextResponse.json(responseData);

  } catch (error) {
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * @swagger
 * /api/admin/performance:
 *   post:
 *     summary: Clear performance metrics or trigger cleanup (Super Admin only)
 *     description: Allows clearing old metrics or triggering system cleanup operations
 *     tags:
 *       - Admin
 *       - Performance
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               action:
 *                 type: string
 *                 enum: [clear_metrics, cleanup_cache, force_gc]
 *               maxAge:
 *                 type: number
 *                 description: Maximum age in milliseconds for metrics to keep (for clear_metrics action)
 *     responses:
 *       200:
 *         description: Action completed successfully
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Super Admin access required
 */
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    // Check authentication
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check Super Admin access
    if (!session.user.isPlatformAdmin) {
      return NextResponse.json(
        { error: 'Super Admin access required' },
        { status: 403 }
      );
    }

    const { action, maxAge } = await request.json();

    switch (action) {
      case 'clear_metrics':
        const maxAgeMs = maxAge || 24 * 60 * 60 * 1000; // Default 24 hours
        performanceMonitor.clearOldMetrics(maxAgeMs);
        return NextResponse.json({
          success: true,
          message: `Cleared metrics older than ${maxAgeMs}ms`,
        });

      case 'cleanup_cache':
        // This would trigger cache cleanup operations
        // Implementation depends on your cache strategy
        return NextResponse.json({
          success: true,
          message: 'Cache cleanup triggered',
        });

      case 'force_gc':
        if (global.gc) {
          global.gc();
          return NextResponse.json({
            success: true,
            message: 'Garbage collection triggered',
          });
        } else {
          return NextResponse.json({
            success: false,
            message: 'Garbage collection not available (run with --expose-gc)',
          });
        }

      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        );
    }

  } catch (error) {
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Helper function to parse time window strings
function parseTimeWindow(timeWindow: string): number {
  const timeMap: Record<string, number> = {
    '5m': 5 * 60 * 1000,
    '15m': 15 * 60 * 1000,
    '1h': 60 * 60 * 1000,
    '6h': 6 * 60 * 60 * 1000,
    '24h': 24 * 60 * 60 * 1000,
  };

  return timeMap[timeWindow] || timeMap['15m'];
}
