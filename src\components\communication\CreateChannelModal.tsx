import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, <PERSON>alogHeader, DialogTitle, DialogTrigger } from '../ui/dialog';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Textarea } from '../ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { Switch } from '../ui/switch';
import { Plus, Hash, Users, Lock, Globe } from 'lucide-react';

interface CreateChannelModalProps {
  onCreateChannel: (channelData: {
    name: string;
    description?: string;
    type?: string;
    isPrivate?: boolean;
    relatedToType?: string;
    relatedToId?: string;
  }) => Promise<void>;
  trigger?: React.ReactNode;
}

export default function CreateChannelModal({ onCreateChannel, trigger }: CreateChannelModalProps) {
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    type: 'general',
    isPrivate: false,
    relatedToType: 'none',
    relatedToId: '',
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.name.trim()) return;

    setLoading(true);
    try {
      await onCreateChannel({
        name: formData.name.trim(),
        description: formData.description.trim() || undefined,
        type: formData.type,
        isPrivate: formData.isPrivate,
        relatedToType: formData.relatedToType === 'none' ? undefined : formData.relatedToType,
        relatedToId: formData.relatedToId || undefined,
      });
      
      // Reset form and close modal
      setFormData({
        name: '',
        description: '',
        type: 'general',
        isPrivate: false,
        relatedToType: 'none',
        relatedToId: '',
      });
      setOpen(false);
    } catch (error) {
      console.error('Failed to create channel:', error);
    } finally {
      setLoading(false);
    }
  };

  const getChannelIcon = (type: string) => {
    switch (type) {
      case 'private':
      case 'direct':
        return <Lock className="h-4 w-4" />;
      case 'project':
      case 'team':
        return <Users className="h-4 w-4" />;
      default:
        return <Hash className="h-4 w-4" />;
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {trigger || (
          <Button>
            <Plus className="h-4 w-4 mr-2" />
            New Channel
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Create New Channel</DialogTitle>
        </DialogHeader>
        
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Channel Name */}
          <div className="space-y-2">
            <Label htmlFor="name">Channel Name *</Label>
            <div className="relative">
              <Hash className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                placeholder="e.g., general, project-alpha, team-marketing"
                className="pl-10"
                required
              />
            </div>
            <p className="text-xs text-muted-foreground">
              Channel names must be lowercase and can contain letters, numbers, and hyphens.
            </p>
          </div>

          {/* Description */}
          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              placeholder="What's this channel about?"
              rows={3}
            />
          </div>

          {/* Channel Type */}
          <div className="space-y-2">
            <Label htmlFor="type">Channel Type</Label>
            <Select
              value={formData.type}
              onValueChange={(value) => setFormData(prev => ({ ...prev, type: value }))}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select channel type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="general">
                  <div className="flex items-center space-x-2">
                    <Hash className="h-4 w-4" />
                    <span>General</span>
                  </div>
                </SelectItem>
                <SelectItem value="project">
                  <div className="flex items-center space-x-2">
                    <Users className="h-4 w-4" />
                    <span>Project</span>
                  </div>
                </SelectItem>
                <SelectItem value="team">
                  <div className="flex items-center space-x-2">
                    <Users className="h-4 w-4" />
                    <span>Team</span>
                  </div>
                </SelectItem>
                <SelectItem value="direct">
                  <div className="flex items-center space-x-2">
                    <Lock className="h-4 w-4" />
                    <span>Direct Message</span>
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Privacy Setting */}
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="private">Private Channel</Label>
              <p className="text-xs text-muted-foreground">
                Only invited members can see and join this channel
              </p>
            </div>
            <div className="flex items-center space-x-2">
              {formData.isPrivate ? (
                <Lock className="h-4 w-4 text-muted-foreground" />
              ) : (
                <Globe className="h-4 w-4 text-muted-foreground" />
              )}
              <Switch
                id="private"
                checked={formData.isPrivate}
                onCheckedChange={(checked) => setFormData(prev => ({ ...prev, isPrivate: checked }))}
              />
            </div>
          </div>

          {/* Related Entity (Optional) */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="relatedToType">Related To (Optional)</Label>
              <Select
                value={formData.relatedToType}
                onValueChange={(value) => setFormData(prev => ({ ...prev, relatedToType: value }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="none">None</SelectItem>
                  <SelectItem value="project">Project</SelectItem>
                  <SelectItem value="opportunity">Opportunity</SelectItem>
                  <SelectItem value="lead">Lead</SelectItem>
                  <SelectItem value="contact">Contact</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            {formData.relatedToType && formData.relatedToType !== 'none' && (
              <div className="space-y-2">
                <Label htmlFor="relatedToId">Entity ID</Label>
                <Input
                  id="relatedToId"
                  value={formData.relatedToId}
                  onChange={(e) => setFormData(prev => ({ ...prev, relatedToId: e.target.value }))}
                  placeholder="Enter ID"
                />
              </div>
            )}
          </div>

          {/* Actions */}
          <div className="flex justify-end space-x-2">
            <Button
              type="button"
              variant="outline"
              onClick={() => setOpen(false)}
              disabled={loading}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={loading || !formData.name.trim()}>
              {loading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Creating...
                </>
              ) : (
                <>
                  {getChannelIcon(formData.type)}
                  <span className="ml-2">Create Channel</span>
                </>
              )}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
