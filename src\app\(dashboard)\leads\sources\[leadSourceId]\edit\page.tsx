'use client';

import React, { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { PageLayout } from '@/components/layout/page-layout';
import { LeadSourceForm } from '@/components/lead-sources/lead-source-form';
import { PermissionGate } from '@/components/ui/permission-gate';
import { PermissionResource, PermissionAction } from '@/types';
import { Card, CardContent } from '@/components/ui/card';
import { EmptyState } from '@/components/ui/empty-state';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { ExclamationTriangleIcon } from '@heroicons/react/24/outline';
import { LeadSourceWithRelations } from '@/services/lead-source-management';
import { toastFunctions as toast } from '@/hooks/use-toast';

export default function EditLeadSourcePage() {
  const params = useParams();
  const router = useRouter();
  const [leadSource, setLeadSource] = useState<LeadSourceWithRelations | null>(null);
  const [loading, setLoading] = useState(true);
  const [notFound, setNotFound] = useState(false);

  const leadSourceId = params.leadSourceId as string;

  useEffect(() => {
    const fetchLeadSource = async () => {
      try {
        setLoading(true);
        const response = await fetch(`/api/lead-sources/${leadSourceId}`);
        const data = await response.json();

        if (data.success) {
          setLeadSource(data.data.leadSource);
        } else {
          setNotFound(true);
          toast.error('Lead source not found');
        }
      } catch (error) {
        console.error('Error fetching lead source:', error);
        setNotFound(true);
        toast.error('Failed to load lead source');
      } finally {
        setLoading(false);
      }
    };

    if (leadSourceId) {
      fetchLeadSource();
    }
  }, [leadSourceId]);

  if (loading) {
    return (
      <PageLayout
        title="Edit Lead Source"
        description="Loading lead source details..."
        breadcrumbs={[
          { label: 'Leads', href: '/leads' },
          { label: 'Lead Sources', href: '/leads/sources' },
          { label: 'Edit Lead Source' }
        ]}
      >
        <Card>
          <CardContent className="p-8">
            <div className="flex items-center justify-center">
              <LoadingSpinner size="lg" />
            </div>
          </CardContent>
        </Card>
      </PageLayout>
    );
  }

  if (notFound || !leadSource) {
    return (
      <PageLayout
        title="Lead Source Not Found"
        description="The requested lead source could not be found"
        breadcrumbs={[
          { label: 'Leads', href: '/leads' },
          { label: 'Lead Sources', href: '/leads/sources' },
          { label: 'Edit Lead Source' }
        ]}
      >
        <Card>
          <CardContent className="p-8">
            <EmptyState
              icon={<ExclamationTriangleIcon className="w-12 h-12 text-muted-foreground" />}
              title="Lead Source Not Found"
              description="The lead source you're looking for doesn't exist or you don't have permission to access it."
              action={{
                label: 'Back to Lead Sources',
                onClick: () => router.push('/leads/sources'),
                variant: 'primary'
              }}
            />
          </CardContent>
        </Card>
      </PageLayout>
    );
  }

  return (
    <PermissionGate
      resource={PermissionResource.LEADS}
      action={PermissionAction.UPDATE}
      fallback={
        <PageLayout>
          <Card>
            <CardContent className="p-8">
              <EmptyState
                icon={<ExclamationTriangleIcon className="w-12 h-12 text-muted-foreground" />}
                title="Access Denied"
                description="You don't have permission to edit lead sources. Please contact your administrator for access."
                action={{
                  label: 'Back to Lead Sources',
                  onClick: () => router.push('/leads/sources'),
                  variant: 'primary'
                }}
              />
            </CardContent>
          </Card>
        </PageLayout>
      }
    >
      <PageLayout
        title={`Edit ${leadSource.name}`}
        description="Update lead source details and settings"
        breadcrumbs={[
          { label: 'Leads', href: '/leads' },
          { label: 'Lead Sources', href: '/leads/sources' },
          { label: leadSource.name, href: `/leads/sources/${leadSource.id}` },
          { label: 'Edit' }
        ]}
      >
        <LeadSourceForm 
          leadSource={leadSource}
          onSuccess={() => router.push('/leads/sources')}
          onCancel={() => router.push('/leads/sources')}
        />
      </PageLayout>
    </PermissionGate>
  );
}
