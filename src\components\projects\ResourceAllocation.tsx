'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { SearchableSelect } from '@/components/ui/searchable-select';
import { Progress } from '@/components/ui/progress';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Plus, Edit, Trash2, Users, Clock, DollarSign, Calendar } from 'lucide-react';
import { DateInput } from '@/components/ui/date-input';
import { useToast } from '@/hooks/use-toast';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { EmptyState } from '@/components/ui/empty-state';

interface User {
  id: string;
  firstName?: string;
  lastName?: string;
  email: string;
}

interface ProjectResource {
  id: string;
  userId: string;
  role: string;
  allocation: number;
  hourlyRate?: number;
  startDate: string;
  endDate?: string;
  hoursAllocated?: number;
  hoursLogged: number;
  isActive: boolean;
  user: User;
}

interface ResourceAllocationProps {
  projectId: string;
  canManage?: boolean;
}

export function ResourceAllocation({ projectId, canManage = false }: ResourceAllocationProps) {
  const [resources, setResources] = useState<ProjectResource[]>([]);
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [showAddDialog, setShowAddDialog] = useState(false);
  const [editingResource, setEditingResource] = useState<ProjectResource | null>(null);
  const { toast } = useToast();

  // Form state
  const [formData, setFormData] = useState({
    userId: 'none',
    role: 'none',
    allocation: 100,
    hourlyRate: '',
    startDate: new Date(),
    endDate: undefined as Date | undefined,
    hoursAllocated: '',
  });

  useEffect(() => {
    fetchData();
  }, [projectId]);

  const fetchData = async () => {
    try {
      setLoading(true);

      const [resourcesRes, usersRes] = await Promise.all([
        fetch(`/api/projects/${projectId}/resources`),
        fetch('/api/users'),
      ]);

      if (resourcesRes.ok) {
        const resourcesData = await resourcesRes.json();
        setResources(resourcesData.data.resources || []);
      } else {
        console.error('Failed to fetch resources:', resourcesRes.status);
        toast({
          title: 'Error',
          description: 'Failed to load project resources',
          variant: 'destructive',
        });
      }

      if (usersRes.ok) {
        const usersData = await usersRes.json();
        // Handle the correct data structure from the users API
        // The API returns {success: true, data: [...], pagination: {...}}
        const userList = usersData.data || [];

        // Transform the user data to match the expected interface
        const transformedUsers = userList.map((user: any) => ({
          id: user.id,
          firstName: user.firstName || '',
          lastName: user.lastName || '',
          email: user.email,
        }));

        setUsers(transformedUsers);
      } else {
        console.error('Failed to fetch users:', usersRes.status);
        toast({
          title: 'Error',
          description: 'Failed to load tenant users',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error fetching data:', error);
      toast({
        title: 'Error',
        description: 'Failed to load resource data',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const resetForm = () => {
    setFormData({
      userId: 'none',
      role: 'none',
      allocation: 100,
      hourlyRate: '',
      startDate: new Date(),
      endDate: undefined,
      hoursAllocated: '',
    });
    setEditingResource(null);
  };

  const handleEdit = (resource: ProjectResource) => {
    setFormData({
      userId: resource.userId,
      role: resource.role,
      allocation: resource.allocation,
      hourlyRate: resource.hourlyRate?.toString() || '',
      startDate: new Date(resource.startDate),
      endDate: resource.endDate ? new Date(resource.endDate) : undefined,
      hoursAllocated: resource.hoursAllocated?.toString() || '',
    });
    setEditingResource(resource);
    setShowAddDialog(true);
  };

  const handleSubmit = async () => {
    try {
      setSaving(true);

      // Validate required fields
      if (!formData.userId || formData.userId === 'none' || !formData.role || formData.role === 'none') {
        throw new Error('Please select both a team member and role');
      }

      const payload = {
        userId: formData.userId,
        role: formData.role,
        allocation: formData.allocation,
        hourlyRate: formData.hourlyRate ? parseFloat(formData.hourlyRate) : undefined,
        startDate: formData.startDate.toISOString(),
        endDate: formData.endDate?.toISOString(),
        hoursAllocated: formData.hoursAllocated ? parseFloat(formData.hoursAllocated) : undefined,
      };

      const url = editingResource 
        ? `/api/projects/${projectId}/resources/${editingResource.id}`
        : `/api/projects/${projectId}/resources`;
      
      const method = editingResource ? 'PUT' : 'POST';

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to save resource');
      }

      toast({
        title: 'Success',
        description: `Resource ${editingResource ? 'updated' : 'added'} successfully`,
      });

      setShowAddDialog(false);
      resetForm();
      fetchData();
    } catch (error) {
      console.error('Error saving resource:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to save resource',
        variant: 'destructive',
      });
    } finally {
      setSaving(false);
    }
  };

  const handleDelete = async (resourceId: string) => {
    if (!confirm('Are you sure you want to remove this resource allocation?')) {
      return;
    }

    try {
      const response = await fetch(`/api/projects/${projectId}/resources/${resourceId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete resource');
      }

      toast({
        title: 'Success',
        description: 'Resource removed successfully',
      });

      fetchData();
    } catch (error) {
      console.error('Error deleting resource:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to remove resource',
        variant: 'destructive',
      });
    }
  };

  const calculateUtilization = (resource: ProjectResource) => {
    if (!resource.hoursAllocated) return 0;
    return Math.min((resource.hoursLogged / resource.hoursAllocated) * 100, 100);
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const getAvailableUsers = () => {
    const allocatedUserIds = resources.map(r => r.userId);
    return users.filter(user => !allocatedUserIds.includes(user.id) || editingResource?.userId === user.id);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Resource Allocation</h2>
          <p className="text-muted-foreground">Manage team member assignments and workload</p>
        </div>
        {canManage && (
          <Dialog open={showAddDialog} onOpenChange={setShowAddDialog}>
            <DialogTrigger asChild>
              <Button onClick={resetForm}>
                <Plus className="h-4 w-4 mr-2" />
                Add Resource
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-md">
              <DialogHeader>
                <DialogTitle>{editingResource ? 'Edit Resource' : 'Add Resource'}</DialogTitle>
                <DialogDescription>
                  {editingResource ? 'Update resource allocation details' : 'Assign a team member to this project'}
                </DialogDescription>
              </DialogHeader>
              
              <div className="space-y-4">
                <div>
                  <Label htmlFor="userId">Team Member</Label>
                  <SearchableSelect
                    options={[
                      { value: "none", label: "Select team member" },
                      ...getAvailableUsers().map((user) => {
                        const displayName = user.firstName && user.lastName
                          ? `${user.firstName} ${user.lastName}`
                          : user.firstName || user.lastName || user.email;
                        return {
                          value: user.id,
                          label: displayName,
                          description: user.email,
                        };
                      })
                    ]}
                    value={formData.userId || "none"}
                    onValueChange={(value) => setFormData({ ...formData, userId: value === 'none' ? '' : value })}
                    placeholder="Select team member"
                    searchPlaceholder="Search team members..."
                    emptyMessage="No team members found"
                    allowClear
                    clearLabel="Clear selection"
                  />
                </div>

                <div>
                  <Label htmlFor="role">Role</Label>
                  <SearchableSelect
                    options={[
                      { value: "none", label: "Select role" },
                      { value: "project_manager", label: "Project Manager", description: "Leads and manages project execution" },
                      { value: "developer", label: "Developer", description: "Writes and maintains code" },
                      { value: "designer", label: "Designer", description: "Creates UI/UX designs" },
                      { value: "qa_engineer", label: "QA Engineer", description: "Tests and ensures quality" },
                      { value: "business_analyst", label: "Business Analyst", description: "Analyzes business requirements" },
                      { value: "devops", label: "DevOps Engineer", description: "Manages infrastructure and deployment" },
                      { value: "consultant", label: "Consultant", description: "Provides expert advice" },
                    ]}
                    value={formData.role || "none"}
                    onValueChange={(value) => setFormData({ ...formData, role: value === 'none' ? '' : value })}
                    placeholder="Select role"
                    searchPlaceholder="Search roles..."
                    emptyMessage="No roles found"
                    allowClear
                    clearLabel="Clear selection"
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="allocation">Allocation (%)</Label>
                    <Input
                      id="allocation"
                      type="number"
                      min="0"
                      max="100"
                      value={formData.allocation}
                      onChange={(e) => setFormData({ ...formData, allocation: parseInt(e.target.value) || 0 })}
                    />
                  </div>

                  <div>
                    <Label htmlFor="hourlyRate">Hourly Rate ($)</Label>
                    <Input
                      id="hourlyRate"
                      type="number"
                      step="0.01"
                      value={formData.hourlyRate}
                      onChange={(e) => setFormData({ ...formData, hourlyRate: e.target.value })}
                      placeholder="Optional"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <DateInput
                    label="Start Date"
                    value={formData.startDate}
                    onChange={(date) => setFormData({ ...formData, startDate: date || new Date() })}
                    placeholder="Select start date"
                    min={new Date().toISOString().split('T')[0]}
                    required
                  />

                  <DateInput
                    label="End Date"
                    value={formData.endDate}
                    onChange={(date) => setFormData({ ...formData, endDate: date })}
                    placeholder="Select end date (optional)"
                    min={formData.startDate?.toISOString().split('T')[0]}
                  />
                </div>

                <div>
                  <Label htmlFor="hoursAllocated">Hours Allocated</Label>
                  <Input
                    id="hoursAllocated"
                    type="number"
                    step="0.5"
                    value={formData.hoursAllocated}
                    onChange={(e) => setFormData({ ...formData, hoursAllocated: e.target.value })}
                    placeholder="Optional"
                  />
                </div>

                <div className="flex justify-end space-x-2 pt-4">
                  <Button variant="outline" onClick={() => setShowAddDialog(false)}>
                    Cancel
                  </Button>
                  <Button onClick={handleSubmit} disabled={saving || !formData.userId || formData.userId === 'none' || !formData.role || formData.role === 'none'}>
                    {saving && <LoadingSpinner size="sm" className="mr-2" />}
                    {editingResource ? 'Update' : 'Add'} Resource
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        )}
      </div>

      {/* Resource List */}
      {resources.length === 0 ? (
        <Card>
          <CardContent className="pt-6">
            <EmptyState
              icon={<Users className="w-12 h-12 text-muted-foreground" />}
              title="No resources allocated"
              description="Add team members to start tracking resource allocation"
              action={canManage ? {
                label: 'Add Resource',
                onClick: () => setShowAddDialog(true),
                variant: 'primary'
              } : undefined}
            />
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-4">
          {resources.map((resource) => (
            <Card key={resource.id} className="overflow-hidden">
              <CardContent className="p-0">
                <div className="p-6">
                  {/* Header with name, role, and status */}
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-semibold">
                        {(resource.user.firstName?.[0] || resource.user.email[0]).toUpperCase()}
                      </div>
                      <div>
                        <h3 className="font-semibold text-lg">
                          {resource.user.firstName && resource.user.lastName
                            ? `${resource.user.firstName} ${resource.user.lastName}`
                            : resource.user.firstName || resource.user.lastName || resource.user.email
                          }
                        </h3>
                        <p className="text-sm text-muted-foreground">{resource.user.email}</p>
                      </div>
                    </div>

                    <div className="flex items-center gap-2">
                      <Badge variant="outline" className="capitalize">
                        {resource.role.replace(/_/g, ' ')}
                      </Badge>
                      <Badge variant={resource.isActive ? 'default' : 'secondary'}>
                        {resource.isActive ? 'Active' : 'Inactive'}
                      </Badge>
                      {canManage && (
                        <div className="flex items-center gap-1 ml-2">
                          <Button variant="outline" size="sm" onClick={() => handleEdit(resource)}>
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleDelete(resource.id)}
                            className="text-red-600 hover:text-red-700"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Resource details grid */}
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    {/* Allocation */}
                    <div className="space-y-2">
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                        <span className="text-sm font-medium text-muted-foreground">Allocation</span>
                      </div>
                      <div className="space-y-1">
                        <div className="flex items-center justify-between">
                          <span className="text-2xl font-bold">{resource.allocation}%</span>
                        </div>
                        <Progress value={resource.allocation} className="h-2" />
                      </div>
                    </div>

                    {/* Timeline */}
                    <div className="space-y-2">
                      <div className="flex items-center gap-2">
                        <Calendar className="w-4 h-4 text-muted-foreground" />
                        <span className="text-sm font-medium text-muted-foreground">Timeline</span>
                      </div>
                      <div className="space-y-1">
                        <div className="text-sm font-medium">
                          {formatDate(resource.startDate)}
                        </div>
                        {resource.endDate && (
                          <div className="text-sm text-muted-foreground">
                            to {formatDate(resource.endDate)}
                          </div>
                        )}
                        {!resource.endDate && (
                          <div className="text-sm text-muted-foreground">Ongoing</div>
                        )}
                      </div>
                    </div>

                    {/* Hours (if allocated) */}
                    {resource.hoursAllocated && (
                      <div className="space-y-2">
                        <div className="flex items-center gap-2">
                          <Clock className="w-4 h-4 text-muted-foreground" />
                          <span className="text-sm font-medium text-muted-foreground">Hours</span>
                        </div>
                        <div className="space-y-1">
                          <div className="text-sm font-medium">
                            {resource.hoursLogged} / {resource.hoursAllocated}h
                          </div>
                          <div className="flex items-center gap-2">
                            <Progress value={calculateUtilization(resource)} className="h-2 flex-1" />
                            <span className="text-xs text-muted-foreground">
                              {Math.round(calculateUtilization(resource))}%
                            </span>
                          </div>
                        </div>
                      </div>
                    )}

                    {/* Rate (if set) */}
                    {resource.hourlyRate && (
                      <div className="space-y-2">
                        <div className="flex items-center gap-2">
                          <DollarSign className="w-4 h-4 text-muted-foreground" />
                          <span className="text-sm font-medium text-muted-foreground">Rate</span>
                        </div>
                        <div className="space-y-1">
                          <div className="text-lg font-bold">
                            {formatCurrency(resource.hourlyRate)}
                          </div>
                          <div className="text-sm text-muted-foreground">per hour</div>
                        </div>
                      </div>
                    )}
                  </div>
                </div>

                {/* Footer with summary (if hours and rate are available) */}
                {resource.hoursAllocated && resource.hourlyRate && (
                  <div className="border-t bg-muted/30 px-6 py-3">
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-muted-foreground">
                        Estimated cost: {formatCurrency(resource.hoursAllocated * resource.hourlyRate)}
                      </span>
                      <span className="text-muted-foreground">
                        Cost to date: {formatCurrency(resource.hoursLogged * resource.hourlyRate)}
                      </span>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}
