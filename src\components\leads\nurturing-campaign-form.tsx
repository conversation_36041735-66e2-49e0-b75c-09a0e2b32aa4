'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { useToast } from '@/hooks/use-toast';
import { NurturingCampaign } from '@/services/lead-nurturing';
import { 
  PlusIcon, 
  TrashIcon, 
  ArrowLeftIcon,
  EnvelopeIcon,
  ClockIcon
} from '@heroicons/react/24/outline';

const emailSequenceStepSchema = z.object({
  delayDays: z.number().int().min(0).max(365),
  emailType: z.enum(['cold_outreach', 'follow_up', 'meeting_request', 'proposal_follow_up', 'thank_you', 'nurturing', 're_engagement', 'introduction']),
  subject: z.string().optional(),
  customPrompt: z.string().optional()
});

const campaignFormSchema = z.object({
  name: z.string().min(1, 'Campaign name is required').max(255, 'Name too long'),
  description: z.string().optional(),
  targetStatus: z.array(z.enum(['new', 'contacted', 'qualified', 'proposal', 'negotiation'])).default(['new', 'contacted']),
  targetPriority: z.array(z.enum(['low', 'medium', 'high', 'urgent'])).optional(),
  emailSequence: z.array(emailSequenceStepSchema).min(1, 'At least one email in sequence required'),
  isActive: z.boolean().default(true),
  maxLeadsPerDay: z.number().int().min(1).max(100).default(10)
});

type CampaignFormData = z.infer<typeof campaignFormSchema>;

interface NurturingCampaignFormProps {
  campaign?: NurturingCampaign;
  tenantId: string;
  onSave?: (campaign: NurturingCampaign) => void;
  onCancel?: () => void;
}

const EMAIL_TYPES = [
  { value: 'cold_outreach', label: 'Cold Outreach' },
  { value: 'follow_up', label: 'Follow Up' },
  { value: 'meeting_request', label: 'Meeting Request' },
  { value: 'proposal_follow_up', label: 'Proposal Follow Up' },
  { value: 'thank_you', label: 'Thank You' },
  { value: 'nurturing', label: 'Nurturing' },
  { value: 're_engagement', label: 'Re-engagement' },
  { value: 'introduction', label: 'Introduction' }
];

const LEAD_STATUSES = [
  { value: 'new', label: 'New' },
  { value: 'contacted', label: 'Contacted' },
  { value: 'qualified', label: 'Qualified' },
  { value: 'proposal', label: 'Proposal' },
  { value: 'negotiation', label: 'Negotiation' }
];

const LEAD_PRIORITIES = [
  { value: 'low', label: 'Low' },
  { value: 'medium', label: 'Medium' },
  { value: 'high', label: 'High' },
  { value: 'urgent', label: 'Urgent' }
];

export function NurturingCampaignForm({ 
  campaign, 
  tenantId, 
  onSave, 
  onCancel 
}: NurturingCampaignFormProps) {
  const [loading, setLoading] = useState(false);
  const { toast } = useToast();
  const router = useRouter();

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors }
  } = useForm<CampaignFormData>({
    resolver: zodResolver(campaignFormSchema),
    defaultValues: campaign ? {
      name: campaign.name,
      description: campaign.description || '',
      targetStatus: campaign.targetStatus as ('new' | 'contacted' | 'qualified' | 'proposal' | 'negotiation')[],
      targetPriority: campaign.targetPriority as ('low' | 'medium' | 'high' | 'urgent')[],
      emailSequence: campaign.emailSequence.map(step => ({
        delayDays: step.delayDays,
        emailType: step.emailType as ('cold_outreach' | 'follow_up' | 'meeting_request' | 'proposal_follow_up' | 'thank_you' | 'nurturing' | 're_engagement' | 'introduction'),
        subject: step.subject || '',
        customPrompt: step.customPrompt || ''
      })),
      isActive: campaign.isActive,
      maxLeadsPerDay: campaign.maxLeadsPerDay
    } : {
      name: '',
      description: '',
      targetStatus: ['new', 'contacted'],
      targetPriority: [],
      emailSequence: [{
        delayDays: 0,
        emailType: 'cold_outreach',
        subject: '',
        customPrompt: ''
      }],
      isActive: true,
      maxLeadsPerDay: 10
    }
  });

  const emailSequence = watch('emailSequence');
  const targetStatus = watch('targetStatus');
  const targetPriority = watch('targetPriority');

  const onSubmit = async (data: CampaignFormData) => {
    setLoading(true);
    try {
      const url = campaign 
        ? `/api/leads/nurturing/campaigns/${campaign.id}`
        : '/api/leads/nurturing/campaigns';
      
      const method = campaign ? 'PUT' : 'POST';

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
          'x-tenant-id': tenantId
        },
        body: JSON.stringify(data)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to save campaign');
      }

      const result = await response.json();
      
      toast({
        title: 'Success',
        description: `Campaign ${campaign ? 'updated' : 'created'} successfully`,
      });

      if (onSave) {
        onSave(result.data);
      } else {
        router.push('/leads/nurturing');
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to save campaign',
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    if (onCancel) {
      onCancel();
    } else {
      router.push('/leads/nurturing');
    }
  };

  const addEmailStep = () => {
    const newStep = {
      delayDays: emailSequence.length === 0 ? 0 : 1,
      emailType: 'follow_up' as const,
      subject: '',
      customPrompt: ''
    };
    setValue('emailSequence', [...emailSequence, newStep]);
  };

  const removeEmailStep = (index: number) => {
    if (emailSequence.length > 1) {
      setValue('emailSequence', emailSequence.filter((_, i) => i !== index));
    }
  };

  const updateEmailStep = (index: number, field: string, value: any) => {
    const updated = [...emailSequence];
    updated[index] = { ...updated[index], [field]: value };
    setValue('emailSequence', updated);
  };

  const toggleStatus = (status: string) => {
    const updated = targetStatus.includes(status as any)
      ? targetStatus.filter(s => s !== status)
      : [...targetStatus, status as any];
    setValue('targetStatus', updated);
  };

  const togglePriority = (priority: string) => {
    const updated = targetPriority?.includes(priority as any)
      ? targetPriority.filter(p => p !== priority)
      : [...(targetPriority || []), priority as any];
    setValue('targetPriority', updated);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <Button variant="ghost" size="sm" onClick={handleCancel}>
            <ArrowLeftIcon className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-2xl font-bold">
              {campaign ? 'Edit Campaign' : 'Create New Campaign'}
            </h1>
            <p className="text-muted-foreground">
              {campaign ? 'Update your nurturing campaign settings' : 'Set up automated email sequences for lead nurturing'}
            </p>
          </div>
        </div>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        {/* Basic Information */}
        <Card>
          <CardHeader>
            <CardTitle>Basic Information</CardTitle>
            <CardDescription>
              Configure the basic settings for your nurturing campaign
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">Campaign Name *</Label>
                <Input
                  id="name"
                  {...register('name')}
                  placeholder="Enter campaign name"
                />
                {errors.name && (
                  <p className="text-sm text-destructive">{errors.name.message}</p>
                )}
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="maxLeadsPerDay">Max Leads Per Day</Label>
                <Input
                  id="maxLeadsPerDay"
                  type="number"
                  min="1"
                  max="100"
                  {...register('maxLeadsPerDay', { valueAsNumber: true })}
                />
                {errors.maxLeadsPerDay && (
                  <p className="text-sm text-destructive">{errors.maxLeadsPerDay.message}</p>
                )}
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                {...register('description')}
                placeholder="Describe the purpose and goals of this campaign"
                rows={3}
              />
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="isActive"
                checked={watch('isActive')}
                onCheckedChange={(checked) => setValue('isActive', checked)}
              />
              <Label htmlFor="isActive">Campaign is active</Label>
            </div>
          </CardContent>
        </Card>

        {/* Target Criteria */}
        <Card>
          <CardHeader>
            <CardTitle>Target Criteria</CardTitle>
            <CardDescription>
              Define which leads should be included in this campaign
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label>Target Lead Status</Label>
              <div className="flex flex-wrap gap-2">
                {LEAD_STATUSES.map((status) => (
                  <Badge
                    key={status.value}
                    variant={targetStatus.includes(status.value as any) ? 'default' : 'outline'}
                    className="cursor-pointer"
                    onClick={() => toggleStatus(status.value)}
                  >
                    {status.label}
                  </Badge>
                ))}
              </div>
            </div>

            <div className="space-y-2">
              <Label>Target Lead Priority (Optional)</Label>
              <div className="flex flex-wrap gap-2">
                {LEAD_PRIORITIES.map((priority) => (
                  <Badge
                    key={priority.value}
                    variant={targetPriority?.includes(priority.value as any) ? 'default' : 'outline'}
                    className="cursor-pointer"
                    onClick={() => togglePriority(priority.value)}
                  >
                    {priority.label}
                  </Badge>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Email Sequence */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              Email Sequence
              <Button type="button" variant="outline" size="sm" onClick={addEmailStep}>
                <PlusIcon className="h-4 w-4 mr-2" />
                Add Email
              </Button>
            </CardTitle>
            <CardDescription>
              Define the sequence of emails that will be sent to leads
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {emailSequence.map((step, index) => (
                <div key={index} className="border rounded-lg p-4 space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <div className="flex items-center justify-center w-8 h-8 bg-primary/10 rounded-full">
                        <span className="text-sm font-medium">{index + 1}</span>
                      </div>
                      <h4 className="font-medium">Email Step {index + 1}</h4>
                    </div>
                    {emailSequence.length > 1 && (
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => removeEmailStep(index)}
                        className="text-destructive hover:text-destructive"
                      >
                        <TrashIcon className="h-4 w-4" />
                      </Button>
                    )}
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label>Email Type</Label>
                      <select
                        value={step.emailType}
                        onChange={(e) => updateEmailStep(index, 'emailType', e.target.value)}
                        className="w-full px-3 py-2 border border-input rounded-md bg-background"
                      >
                        {EMAIL_TYPES.map((type) => (
                          <option key={type.value} value={type.value}>
                            {type.label}
                          </option>
                        ))}
                      </select>
                    </div>

                    <div className="space-y-2">
                      <Label>Delay (Days)</Label>
                      <Input
                        type="number"
                        min="0"
                        max="365"
                        value={step.delayDays}
                        onChange={(e) => updateEmailStep(index, 'delayDays', parseInt(e.target.value) || 0)}
                        placeholder="0"
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label>Custom Subject (Optional)</Label>
                    <Input
                      value={step.subject || ''}
                      onChange={(e) => updateEmailStep(index, 'subject', e.target.value)}
                      placeholder="Leave empty to use AI-generated subject"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label>Custom Prompt (Optional)</Label>
                    <Textarea
                      value={step.customPrompt || ''}
                      onChange={(e) => updateEmailStep(index, 'customPrompt', e.target.value)}
                      placeholder="Provide specific instructions for AI email generation"
                      rows={3}
                    />
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Action Buttons */}
        <div className="flex justify-end space-x-3">
          <Button type="button" variant="outline" onClick={handleCancel}>
            Cancel
          </Button>
          <Button type="submit" disabled={loading}>
            {loading ? 'Saving...' : campaign ? 'Update Campaign' : 'Create Campaign'}
          </Button>
        </div>
      </form>
    </div>
  );
}
