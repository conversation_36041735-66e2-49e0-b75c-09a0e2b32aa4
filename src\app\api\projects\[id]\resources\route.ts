import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { prisma } from '@/lib/prisma';
import { withPermission, AuthenticatedRequest } from '@/lib/permission-middleware';
import { PermissionResource, PermissionAction } from '@/types';

const createResourceSchema = z.object({
  userId: z.string().min(1, 'User ID is required'),
  role: z.string().min(1, 'Role is required'),
  allocation: z.number().min(0).max(100).default(100),
  hourlyRate: z.number().positive().optional(),
  startDate: z.string().datetime(),
  endDate: z.string().datetime().optional(),
  hoursAllocated: z.number().positive().optional(),
});

const getResourcesQuerySchema = z.object({
  includeInactive: z.string().optional().transform(val => val === 'true'),
});

interface RouteParams {
  params: Promise<{
    id: string;
  }>;
}

/**
 * GET /api/projects/[id]/resources
 * Get all resources for a specific project
 */
export const GET = withPermission({
  resource: PermissionResource.PROJECTS,
  action: PermissionAction.READ,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest, context: RouteParams) => {
  try {
    const { id: projectId } = await context.params;
    const { searchParams } = new URL(req.url);
    const queryParams = Object.fromEntries(searchParams.entries());
    
    const { includeInactive } = getResourcesQuerySchema.parse(queryParams);

    // Verify project exists and belongs to tenant
    const project = await prisma.project.findFirst({
      where: {
        id: projectId,
        tenantId: req.tenantId,
      },
    });

    if (!project) {
      return NextResponse.json(
        { error: 'Project not found' },
        { status: 404 }
      );
    }

    // Get project resources
    const resources = await prisma.projectResource.findMany({
      where: {
        projectId,
        tenantId: req.tenantId,
        ...(includeInactive ? {} : { isActive: true }),
      },
      include: {
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
      },
      orderBy: [
        { isActive: 'desc' },
        { startDate: 'asc' },
      ],
    });

    return NextResponse.json({
      success: true,
      data: { resources },
      message: 'Project resources retrieved successfully'
    });
  } catch (error) {
    console.error('Get project resources error:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid query parameters', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to fetch project resources' },
      { status: 500 }
    );
  }
});

/**
 * POST /api/projects/[id]/resources
 * Add a new resource to a project
 */
export const POST = withPermission({
  resource: PermissionResource.PROJECTS,
  action: PermissionAction.UPDATE,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest, context: RouteParams) => {
  try {
    const { id: projectId } = await context.params;
    const body = await req.json();
    const validatedData = createResourceSchema.parse(body);

    // Verify project exists and belongs to tenant
    const project = await prisma.project.findFirst({
      where: {
        id: projectId,
        tenantId: req.tenantId,
      },
    });

    if (!project) {
      return NextResponse.json(
        { error: 'Project not found' },
        { status: 404 }
      );
    }

    // Verify user belongs to tenant
    const user = await prisma.user.findFirst({
      where: {
        id: validatedData.userId,
        tenantUsers: {
          some: { tenantId: req.tenantId }
        }
      }
    });

    if (!user) {
      return NextResponse.json(
        { error: 'User not found or not part of tenant' },
        { status: 400 }
      );
    }

    // Check if user is already allocated to this project with the same role
    const existingResource = await prisma.projectResource.findFirst({
      where: {
        projectId,
        userId: validatedData.userId,
        role: validatedData.role,
        tenantId: req.tenantId,
        isActive: true,
      },
    });

    if (existingResource) {
      return NextResponse.json(
        { error: 'User is already allocated to this project with the same role' },
        { status: 400 }
      );
    }

    // Create the resource allocation
    const resource = await prisma.projectResource.create({
      data: {
        tenantId: req.tenantId,
        projectId,
        userId: validatedData.userId,
        role: validatedData.role,
        allocation: validatedData.allocation,
        hourlyRate: validatedData.hourlyRate,
        startDate: new Date(validatedData.startDate),
        endDate: validatedData.endDate ? new Date(validatedData.endDate) : null,
        hoursAllocated: validatedData.hoursAllocated,
        createdById: req.userId,
      },
      include: {
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
      },
    });

    return NextResponse.json({
      success: true,
      data: { resource },
      message: 'Resource added to project successfully'
    }, { status: 201 });
  } catch (error) {
    console.error('Create project resource error:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid resource data', details: error.errors },
        { status: 400 }
      );
    }

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to add resource to project' },
      { status: 500 }
    );
  }
});
