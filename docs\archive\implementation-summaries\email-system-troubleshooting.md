# 📧 Email System Troubleshooting Guide

## 🔍 Common Issues and Solutions

### 1. **400 Bad Request Error**

**Symptoms:** Getting 400 status when trying to send emails

**Possible Causes:**
- Missing required fields in request payload
- Invalid email addresses
- Schema validation failures

**Solutions:**

1. **Check Request Payload Format:**
```javascript
// Correct format for email API
{
  "to": "<EMAIL>",
  "subject": "Test Subject",
  "html": "<p>HTML content</p>",
  "text": "Plain text content",
  "priority": "normal",
  "trackOpens": true,
  "trackClicks": true,
  "leadId": "optional-lead-uuid",
  "contactId": "optional-contact-uuid",
  "tags": ["email_type", "tone"],
  "metadata": {
    "emailType": "follow_up",
    "tone": "professional"
  }
}
```

2. **Validate Email Addresses:**
   - Ensure all email addresses are valid
   - Check for empty or undefined email fields
   - Verify CC/BCC fields are properly formatted

3. **Check Server Logs:**
   - Look for validation error details in console
   - Check the `details` field in API response

### 2. **503 Service Unavailable Error**

**Symptoms:** Email service not available

**Possible Causes:**
- SMTP service not configured
- SMTP credentials invalid
- Email sending disabled

**Solutions:**

1. **Check Environment Variables:**
```bash
# Required SMTP settings in .env
ENABLE_EMAIL_SENDING=true
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
SMTP_FROM_EMAIL=<EMAIL>
SMTP_FROM_NAME=CRM Platform
```

2. **Test SMTP Connection:**
```bash
# Use the GET endpoint to check service status
curl http://localhost:3000/api/email/send
```

3. **Gmail App Password Setup:**
   - Enable 2FA on Gmail account
   - Generate App Password for SMTP
   - Use App Password instead of regular password

### 3. **Database Errors**

**Symptoms:** Prisma client errors, missing tables

**Solutions:**

1. **Regenerate Prisma Client:**
```bash
npx prisma generate
```

2. **Apply Database Changes:**
```bash
npx prisma db push
```

3. **Check Database Schema:**
```bash
npx prisma studio
```

### 4. **Permission Errors**

**Symptoms:** 403 Forbidden when accessing email features

**Solutions:**

1. **Check User Permissions:**
   - Ensure user has CONTACTS:CREATE permission
   - Verify tenant access for leads/contacts

2. **Update User Roles:**
   - Assign appropriate roles in admin panel
   - Check role-permission mappings

### 5. **AI Generation Failures**

**Symptoms:** AI email generation not working

**Solutions:**

1. **Check AI Service Configuration:**
```bash
# Verify AI provider settings in .env
AI_PROVIDER=google
GOOGLE_AI_API_KEY=your-api-key
```

2. **Test AI Service:**
   - Check AI service logs
   - Verify API key validity
   - Test with simple prompts

## 🧪 Testing the Email System

### 1. **Manual Testing Steps**

1. **Login to CRM:**
   - Navigate to http://localhost:3000
   - Login with valid credentials

2. **Navigate to Lead Detail:**
   - Go to Leads section
   - Click on any lead with contact email

3. **Test Email Composer:**
   - Click "Send Email" in Quick Actions
   - Fill in email details
   - Try AI generation
   - Send test email

4. **Verify Email History:**
   - Check email history section
   - Verify tracking status
   - Check email logs in database

### 2. **API Testing with cURL**

```bash
# Test email service status
curl -X GET http://localhost:3000/api/email/send \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"

# Test email sending
curl -X POST http://localhost:3000/api/email/send \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "to": "<EMAIL>",
    "subject": "Test Email",
    "html": "<p>Test content</p>",
    "text": "Test content",
    "priority": "normal",
    "trackOpens": true,
    "trackClicks": true
  }'
```

### 3. **Database Verification**

```sql
-- Check email logs
SELECT * FROM email_logs ORDER BY created_at DESC LIMIT 10;

-- Check lead last contact dates
SELECT id, title, last_contact_date FROM leads 
WHERE last_contact_date IS NOT NULL;

-- Check email tracking
SELECT id, subject, status, opened_at, clicked_at 
FROM email_logs WHERE track_opens = true;
```

## 🔧 Development Mode Testing

### 1. **Mock Email Mode**

When `ENABLE_EMAIL_SENDING=false`, the system runs in mock mode:
- Emails are logged but not actually sent
- Mock message IDs are generated
- All tracking features work normally

### 2. **Debug Logging**

Enable detailed logging by checking server console for:
- Email validation errors
- SMTP connection status
- Database operation results
- AI generation responses

## 📋 Checklist for Email System Setup

- [ ] Environment variables configured
- [ ] Database schema updated (`npx prisma db push`)
- [ ] Prisma client regenerated (`npx prisma generate`)
- [ ] SMTP credentials tested
- [ ] User permissions verified
- [ ] AI service configured
- [ ] Email composer accessible
- [ ] Email history displaying
- [ ] Tracking pixels working

## 🚨 Emergency Fixes

### Quick Fix for 400 Errors:

1. Check the email composer payload in browser dev tools
2. Compare with expected schema in `/src/app/api/email/send/route.ts`
3. Ensure all required fields are present
4. Verify email address format

### Quick Fix for Database Errors:

1. Restart the development server
2. Clear `.next` cache: `rm -rf .next`
3. Regenerate Prisma client: `npx prisma generate`
4. Apply schema changes: `npx prisma db push`

### Quick Fix for Permission Errors:

1. Check user session in browser dev tools
2. Verify JWT token validity
3. Check user roles in database
4. Ensure tenant access is correct

## 📞 Support

If issues persist:
1. Check server logs for detailed error messages
2. Verify all environment variables are set
3. Test with minimal email payload
4. Check database connectivity
5. Verify SMTP server accessibility
