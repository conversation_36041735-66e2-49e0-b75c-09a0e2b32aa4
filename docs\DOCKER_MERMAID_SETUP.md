# Docker Mermaid PNG Generation Setup

This document explains how the CRM Platform Docker setup supports Mermaid diagram PNG generation.

## Overview

The Docker configuration has been enhanced to support:
- Mermaid CLI for PNG generation
- Chrome/Chromium headless browser
- Puppeteer for fallback PNG generation
- Proper security and performance settings

## Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Next.js App   │───▶│  Mermaid Service │───▶│  Chrome Browser │
│                 │    │                  │    │   (Headless)    │
└─────────────────┘    └──────────────────┘    └─────────────────┘
         │                        │                       │
         ▼                        ▼                       ▼
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   File System   │    │   Temp Storage   │    │   PNG Output    │
│   (/uploads)    │    │ (/tmp/mermaid)   │    │                 │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

## Docker Configuration Changes

### 1. Dockerfile Updates

- **Base Image**: Added Chrome/Chromium and required dependencies
- **Environment Variables**: Set Puppeteer and Chrome configuration
- **User Permissions**: Proper permissions for Chrome execution
- **Chrome Installation**: Puppeteer Chrome headless shell

### 2. Docker Compose Updates

- **Shared Memory**: Increased `shm_size` to 2GB for Chrome
- **Security Options**: Added `seccomp:unconfined` for Chrome
- **Environment Variables**: Mermaid and Puppeteer configuration
- **Volumes**: Added `mermaid-temp` volume for temporary files

### 3. Environment Variables

```bash
# Puppeteer Configuration
PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true
PUPPETEER_EXECUTABLE_PATH=/usr/bin/chromium-browser
PUPPETEER_ARGS=--no-sandbox --disable-setuid-sandbox --disable-dev-shm-usage

# Mermaid Configuration
MERMAID_TEMP_DIR=/tmp/mermaid
MERMAID_PNG_WIDTH=800
MERMAID_PNG_HEIGHT=600
MERMAID_PNG_THEME=default
MERMAID_PNG_BACKGROUND=white
```

## Usage

### Development

```bash
# Start development environment with Mermaid support
docker-compose -f docker-compose.yml -f docker-compose.dev.yml up

# Test Mermaid PNG generation
docker-compose -f docker-compose.yml -f docker-compose.dev.yml --profile testing up mermaid-test
docker exec crm-mermaid-test node test-png-conversion.js
```

### Production

```bash
# Start production environment
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d

# Monitor Mermaid PNG generation
docker logs crm-app | grep -i mermaid
```

### Testing Mermaid PNG Generation

```bash
# Enter the app container
docker exec -it crm-app sh

# Test mermaid-cli directly
echo "graph LR; A-->B" > /tmp/test.mmd
/app/node_modules/.bin/mmdc -i /tmp/test.mmd -o /tmp/test.png
ls -la /tmp/test.png
```

## Troubleshooting

### Common Issues

1. **Chrome Not Found**
   ```bash
   # Check Chrome installation
   docker exec crm-app which chromium-browser
   docker exec crm-app chromium-browser --version
   ```

2. **Permission Denied**
   ```bash
   # Check permissions
   docker exec crm-app ls -la /tmp/mermaid
   docker exec crm-app whoami
   ```

3. **Shared Memory Issues**
   ```bash
   # Check shared memory
   docker exec crm-app df -h /dev/shm
   ```

### Performance Optimization

1. **Memory Usage**: Monitor Chrome memory usage
2. **Temp File Cleanup**: Automatic cleanup every 24 hours
3. **Concurrent Rendering**: Limited by container resources

### Security Considerations

1. **Chrome Sandbox**: Disabled for container compatibility
2. **User Permissions**: Runs as non-root user (nextjs)
3. **Network Isolation**: Chrome has no network access for rendering

## Monitoring

### Health Checks

The application includes health checks for:
- Database connectivity
- Redis connectivity
- Mermaid PNG generation capability

### Logs

Monitor Mermaid-related logs:
```bash
docker logs crm-app 2>&1 | grep -i "mermaid\|chrome\|puppeteer"
```

### Metrics

Key metrics to monitor:
- PNG generation success rate
- Chrome memory usage
- Temp file storage usage
- PNG generation response time

## Maintenance

### Regular Tasks

1. **Temp File Cleanup**: Automatic every 24 hours
2. **Chrome Cache Cleanup**: Handled by container restart
3. **Volume Maintenance**: Monitor `mermaid-temp` volume size

### Updates

When updating Mermaid or Chrome:
1. Update package.json dependencies
2. Rebuild Docker images
3. Test PNG generation functionality
4. Deploy with zero-downtime strategy

## Support

For issues related to Mermaid PNG generation:
1. Check container logs
2. Verify Chrome installation
3. Test with simple Mermaid diagrams
4. Check shared memory and permissions
