import { NextResponse } from 'next/server';
import { CompanyManagementService } from '@/services/company-management';
import { withPermission, AuthenticatedRequest } from '@/lib/permission-middleware';
import { PermissionResource, PermissionAction } from '@/types';

const companyService = new CompanyManagementService();



/**
 * GET /api/companies/[companyId]
 * Get a specific company by ID
 */
export const GET = withPermission({
  resource: PermissionResource.COMPANIES,
  action: PermissionAction.READ,
  requireOwnership: true,
  getResourceOwnerId: async (req) => {
    const url = new URL(req.url);
    const pathSegments = url.pathname.split('/');
    return pathSegments[pathSegments.indexOf('companies') + 1];
  },
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest) => {
  try {
    const url = new URL(req.url);
    const pathSegments = url.pathname.split('/');
    const companyId = pathSegments[pathSegments.indexOf('companies') + 1];

    const company = await companyService.getCompany(companyId, req.tenantId);

    if (!company) {
      return NextResponse.json(
        { error: 'Company not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: { company },
      message: 'Company retrieved successfully'
    });

  } catch (error) {
    console.error('Get company error:', error);

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
});

/**
 * PUT /api/companies/[companyId]
 * Update a specific company
 */
export const PUT = withPermission({
  resource: PermissionResource.COMPANIES,
  action: PermissionAction.UPDATE,
  requireOwnership: true,
  getResourceOwnerId: async (req) => {
    const url = new URL(req.url);
    const pathSegments = url.pathname.split('/');
    return pathSegments[pathSegments.indexOf('companies') + 1];
  },
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest) => {
  try {
    const url = new URL(req.url);
    const pathSegments = url.pathname.split('/');
    const companyId = pathSegments[pathSegments.indexOf('companies') + 1];
    const body = await req.json();

    const company = await companyService.updateCompany(
      companyId,
      req.tenantId,
      body,
      req.userId
    );

    return NextResponse.json({
      success: true,
      data: { company },
      message: 'Company updated successfully'
    });

  } catch (error) {
    console.error('Update company error:', error);

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
});

/**
 * DELETE /api/companies/[companyId]
 * Delete a specific company
 */
export const DELETE = withPermission({
  resource: PermissionResource.COMPANIES,
  action: PermissionAction.DELETE,
  requireOwnership: true,
  getResourceOwnerId: async (req) => {
    const url = new URL(req.url);
    const pathSegments = url.pathname.split('/');
    return pathSegments[pathSegments.indexOf('companies') + 1];
  },
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest) => {
  try {
    const url = new URL(req.url);
    const pathSegments = url.pathname.split('/');
    const companyId = pathSegments[pathSegments.indexOf('companies') + 1];

    await companyService.deleteCompany(companyId, req.tenantId);

    return NextResponse.json({
      success: true,
      message: 'Company deleted successfully'
    });

  } catch (error) {
    console.error('Delete company error:', error);

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
});
