'use client';

import React, { useState, useEffect, use } from 'react';
import { useRouter } from 'next/navigation';
import { OpportunityWithRelations } from '@/services/opportunity-management';
import { OpportunityForm } from '@/components/opportunities/opportunity-form';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ArrowLeftIcon, ExclamationTriangleIcon } from '@heroicons/react/24/outline';
import { toastFunctions as toast } from '@/hooks/use-toast';
import { PageLayout } from '@/components/layout/page-layout';
import { EmptyState } from '@/components/ui/empty-state';
import { PermissionGate } from '@/components/ui/permission-gate';
import { PermissionResource, PermissionAction } from '@/types';
import { LoadingSpinner } from '@/components/ui/loading-spinner';

interface EditOpportunityPageProps {
  params: Promise<{
    id: string;
  }>;
}

export default function EditOpportunityPage({ params }: EditOpportunityPageProps) {
  const router = useRouter();
  const resolvedParams = use(params);
  const opportunityId = resolvedParams.id;
  
  const [opportunity, setOpportunity] = useState<OpportunityWithRelations | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Load opportunity data
  useEffect(() => {
    const loadOpportunity = async () => {
      try {
        setLoading(true);
        setError(null);
        
        const response = await fetch(`/api/opportunities/${opportunityId}`);
        
        if (response.ok) {
          const data = await response.json();
          setOpportunity(data);
        } else if (response.status === 404) {
          setError('Opportunity not found');
        } else {
          const errorData = await response.json();
          setError(errorData.error || 'Failed to load opportunity');
        }
      } catch (error) {
        console.error('Error loading opportunity:', error);
        setError('Failed to load opportunity');
      } finally {
        setLoading(false);
      }
    };

    if (opportunityId) {
      loadOpportunity();
    }
  }, [opportunityId]);

  const handleSuccess = (updatedOpportunityId: string) => {
    toast.success('Opportunity updated successfully');
    router.push(`/opportunities/${updatedOpportunityId}`);
  };

  const handleCancel = () => {
    router.push(`/opportunities/${opportunityId}`);
  };

  const actions = (
    <Button variant="outline" size="sm" onClick={() => router.push(`/opportunities/${opportunityId}`)}>
      <ArrowLeftIcon className="h-4 w-4 mr-2" />
      Back to Opportunity
    </Button>
  );

  if (loading) {
    return (
      <PageLayout
        title="Edit Opportunity"
        description="Update opportunity information and details"
        actions={actions}
      >
        <Card>
          <CardContent className="p-8">
            <div className="flex items-center justify-center">
              <LoadingSpinner size="lg" />
            </div>
          </CardContent>
        </Card>
      </PageLayout>
    );
  }

  if (error || !opportunity) {
    return (
      <PageLayout
        title="Edit Opportunity"
        description="Update opportunity information and details"
        actions={actions}
      >
        <Card>
          <CardContent className="p-8">
            <EmptyState
              icon={<ExclamationTriangleIcon className="w-12 h-12 text-muted-foreground" />}
              title={error === 'Opportunity not found' ? 'Opportunity Not Found' : 'Error Loading Opportunity'}
              description={error || 'The opportunity you are looking for could not be found or loaded.'}
              action={{
                label: 'Back to Opportunities',
                onClick: () => router.push('/opportunities'),
                variant: 'primary'
              }}
            />
          </CardContent>
        </Card>
      </PageLayout>
    );
  }

  return (
    <PermissionGate
      resource={PermissionResource.OPPORTUNITIES}
      action={PermissionAction.UPDATE}
      fallback={
        <PageLayout
          title="Access Denied"
          description="You don't have permission to edit this opportunity"
          actions={actions}
        >
          <Card>
            <CardContent className="p-8">
              <EmptyState
                icon={<ExclamationTriangleIcon className="w-12 h-12 text-muted-foreground" />}
                title="Access Denied"
                description="You don't have permission to edit this opportunity. Please contact your administrator for access."
                action={{
                  label: 'Back to Opportunity',
                  onClick: () => router.push(`/opportunities/${opportunityId}`),
                  variant: 'primary'
                }}
              />
            </CardContent>
          </Card>
        </PageLayout>
      }
    >
      <PageLayout
        title={`Edit ${opportunity.title}`}
        description="Update opportunity information and details"
        actions={actions}
      >
        <OpportunityForm
          opportunity={opportunity}
          onSuccess={handleSuccess}
          onCancel={handleCancel}
        />
      </PageLayout>
    </PermissionGate>
  );
}
