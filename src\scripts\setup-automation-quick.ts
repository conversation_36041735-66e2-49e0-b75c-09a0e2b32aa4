/**
 * Quick setup script for Process Automation
 * This script will set up the automation system for immediate use
 */

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function quickSetupAutomation() {
  console.log('🚀 Quick Setup: Process Automation System...\n');

  try {
    // 1. Check database connection
    console.log('1. Checking database connection...');
    await prisma.$queryRaw`SELECT 1`;
    console.log('   ✅ Database connected');

    // 2. Check if automation tables exist
    console.log('\n2. Checking automation tables...');
    try {
      await prisma.automationTrigger.count();
      await prisma.automationExecution.count();
      console.log('   ✅ Automation tables exist');
    } catch (error) {
      console.log('   ❌ Automation tables missing');
      console.log('   💡 Please run: npm run db:push');
      console.log('   💡 Or run: npx prisma db push');
      return false;
    }

    // 3. Get active tenants
    console.log('\n3. Finding active tenants...');
    const tenants = await prisma.tenant.findMany({
      where: { status: 'active' },
      select: { id: true, name: true }
    });

    if (tenants.length === 0) {
      console.log('   ❌ No active tenants found');
      return false;
    }

    console.log(`   ✅ Found ${tenants.length} active tenants`);

    // 4. Setup triggers for each tenant
    console.log('\n4. Setting up automation triggers...');
    
    for (const tenant of tenants) {
      console.log(`\n   🏢 Setting up for: ${tenant.name}`);
      
      // Check existing triggers
      const existingTriggers = await prisma.automationTrigger.count({
        where: { tenantId: tenant.id }
      });

      if (existingTriggers > 0) {
        console.log(`      ⏭️  Skipping - ${existingTriggers} triggers already exist`);
        continue;
      }

      // Create default triggers
      const defaultTriggers = [
        {
          name: 'Opportunity Closed Won → Create Handover',
          description: 'Automatically create handover when opportunity is closed won',
          triggerType: 'opportunity_status_change',
          triggerConditions: { toStatus: 'Closed Won' },
          actions: [
            {
              type: 'create_handover',
              config: { 
                priority: 'high',
                scheduledDelayHours: 24,
                template: 'default'
              }
            },
            {
              type: 'send_notification',
              config: {
                recipients: ['sales_rep', 'delivery_manager'],
                template: 'handover_created',
                priority: 'high'
              }
            }
          ],
          isActive: true,
          priority: 9
        },
        {
          name: 'Opportunity → Proposal Stage',
          description: 'Send notifications when opportunity reaches proposal stage',
          triggerType: 'opportunity_status_change',
          triggerConditions: { toStatus: 'Proposal' },
          actions: [
            {
              type: 'send_notification',
              config: {
                recipients: ['sales_rep', 'sales_manager'],
                template: 'proposal_stage_reached'
              }
            }
          ],
          isActive: true,
          priority: 5
        },
        {
          name: 'Opportunity → Negotiation Stage',
          description: 'High-priority notifications for negotiation stage',
          triggerType: 'opportunity_status_change',
          triggerConditions: { toStatus: 'Negotiation' },
          actions: [
            {
              type: 'send_notification',
              config: {
                recipients: ['sales_rep', 'sales_manager', 'delivery_manager'],
                template: 'negotiation_stage_reached',
                priority: 'high'
              }
            }
          ],
          isActive: true,
          priority: 8
        }
      ];

      // Create triggers
      for (const triggerData of defaultTriggers) {
        try {
          await prisma.automationTrigger.create({
            data: {
              tenantId: tenant.id,
              ...triggerData,
              createdById: 'setup-script'
            }
          });
          console.log(`      ✅ Created: ${triggerData.name}`);
        } catch (error) {
          console.log(`      ❌ Failed: ${triggerData.name} - ${error.message}`);
        }
      }
    }

    // 5. Verify setup
    console.log('\n5. Verifying setup...');
    const totalTriggers = await prisma.automationTrigger.count({
      where: { isActive: true }
    });
    
    const totalExecutions = await prisma.automationExecution.count();

    console.log(`   ✅ Total active triggers: ${totalTriggers}`);
    console.log(`   📊 Total executions logged: ${totalExecutions}`);

    // 6. Find test opportunity
    console.log('\n6. Finding test opportunities...');
    const testOpportunities = await prisma.opportunity.findMany({
      where: { 
        stage: { not: 'Closed Won' },
        tenantId: { in: tenants.map(t => t.id) }
      },
      take: 3,
      select: { id: true, title: true, stage: true, tenantId: true }
    });

    if (testOpportunities.length > 0) {
      console.log(`   ✅ Found ${testOpportunities.length} opportunities for testing:`);
      testOpportunities.forEach(opp => {
        console.log(`      - ${opp.title} (${opp.stage}) - /opportunities/${opp.id}`);
      });
    } else {
      console.log('   ⚠️  No test opportunities found');
    }

    console.log('\n🎉 Quick setup completed successfully!');
    console.log('\n📋 What was set up:');
    console.log(`   • ${totalTriggers} automation triggers across ${tenants.length} tenants`);
    console.log('   • Opportunity Closed Won → Create Handover');
    console.log('   • Opportunity → Proposal Stage notifications');
    console.log('   • Opportunity → Negotiation Stage notifications');

    console.log('\n🧪 How to test:');
    console.log('   1. Go to any opportunity in the system');
    console.log('   2. Change the stage to "Closed Won"');
    console.log('   3. Check the "Automation Status" section on the opportunity page');
    console.log('   4. Visit /admin/automation to see execution logs');

    console.log('\n🔗 Quick links:');
    console.log('   • Automation Dashboard: /admin/automation');
    console.log('   • Pipeline Board: /opportunities/pipeline');
    if (testOpportunities.length > 0) {
      console.log(`   • Test Opportunity: /opportunities/${testOpportunities[0].id}`);
    }

    return true;

  } catch (error) {
    console.error('❌ Setup failed:', error);
    
    if (error.message?.includes('automation_triggers')) {
      console.log('\n💡 Database tables missing. Please run:');
      console.log('   npm run db:push');
      console.log('   OR');
      console.log('   npx prisma db push');
    }
    
    return false;
  } finally {
    await prisma.$disconnect();
  }
}

// Run setup if called directly
if (require.main === module) {
  quickSetupAutomation().catch(console.error);
}

export { quickSetupAutomation };
