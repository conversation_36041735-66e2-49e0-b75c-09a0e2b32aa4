import { exec } from 'child_process';
import { promisify } from 'util';
import path from 'path';
import fs from 'fs/promises';
import { tmpdir } from 'os';

const execAsync = promisify(exec);

export interface MermaidToPngOptions {
  theme?: 'default' | 'dark' | 'forest' | 'neutral';
  backgroundColor?: string;
  width?: number;
  height?: number;
  scale?: number;
}

export interface MermaidToPngResult {
  pngBuffer: Buffer;
  tempFilePath: string;
}

export class MermaidPngConverter {
  private static instance: MermaidPngConverter;
  private tempDir: string;

  constructor() {
    this.tempDir = path.join(tmpdir(), 'mermaid-png-converter');
  }

  public static getInstance(): MermaidPngConverter {
    if (!MermaidPngConverter.instance) {
      MermaidPngConverter.instance = new MermaidPngConverter();
    }
    return MermaidPngConverter.instance;
  }

  /**
   * Convert Mermaid code to PNG using mermaid-cli
   */
  async convertToPng(
    mermaidCode: string,
    options: MermaidToPngOptions = {}
  ): Promise<MermaidToPngResult> {
    try {
      // Ensure temp directory exists
      await fs.mkdir(this.tempDir, { recursive: true });

      // Generate unique filenames
      const timestamp = Date.now();
      const randomId = Math.random().toString(36).substring(2, 15);
      const inputFile = path.join(this.tempDir, `input_${timestamp}_${randomId}.mmd`);
      const outputFile = path.join(this.tempDir, `output_${timestamp}_${randomId}.png`);

      // Write Mermaid code to temporary file
      await fs.writeFile(inputFile, mermaidCode, 'utf-8');

      // Build mermaid-cli command
      const mmdc = path.join(process.cwd(), 'node_modules', '.bin', 'mmdc');
      let command = `"${mmdc}" -i "${inputFile}" -o "${outputFile}"`;

      // Add options
      if (options.theme) {
        command += ` -t ${options.theme}`;
      }
      if (options.backgroundColor) {
        command += ` -b ${options.backgroundColor}`;
      }
      if (options.width) {
        command += ` -w ${options.width}`;
      }
      if (options.height) {
        command += ` -H ${options.height}`;
      }
      if (options.scale) {
        command += ` -s ${options.scale}`;
      }

      // Execute mermaid-cli command
      console.log('Executing mermaid-cli command:', command);
      const { stdout, stderr } = await execAsync(command, {
        timeout: 30000, // 30 second timeout
        env: {
          ...process.env,
          // Disable sandbox for headless Chrome in Docker/CI environments
          PUPPETEER_ARGS: '--no-sandbox --disable-setuid-sandbox'
        }
      });

      if (stderr && !stderr.includes('info')) {
        console.warn('Mermaid-cli stderr:', stderr);
      }

      // Check if output file was created
      try {
        await fs.access(outputFile);
      } catch (error) {
        throw new Error(`PNG file was not created. Command output: ${stdout}, Error: ${stderr}`);
      }

      // Read the generated PNG file
      const pngBuffer = await fs.readFile(outputFile);

      // Clean up input file (keep output file for potential reuse)
      await fs.unlink(inputFile).catch(console.warn);

      return {
        pngBuffer,
        tempFilePath: outputFile
      };

    } catch (error) {
      console.error('Error converting Mermaid to PNG:', error);
      throw new Error(`Failed to convert Mermaid to PNG: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Convert Mermaid code to PNG with fallback to puppeteer if mermaid-cli fails
   */
  async convertToPngWithFallback(
    mermaidCode: string,
    options: MermaidToPngOptions = {}
  ): Promise<MermaidToPngResult> {
    try {
      // Try mermaid-cli first
      return await this.convertToPng(mermaidCode, options);
    } catch (error) {
      console.warn('Mermaid-cli failed, falling back to puppeteer method:', error);
      
      // Fallback to puppeteer-based conversion
      return await this.convertToPngWithPuppeteer(mermaidCode, options);
    }
  }

  /**
   * Convert Mermaid code to PNG using puppeteer directly
   */
  async convertToPngWithPuppeteer(
    mermaidCode: string,
    options: MermaidToPngOptions = {}
  ): Promise<MermaidToPngResult> {
    const puppeteer = await import('puppeteer');
    
    let browser;
    try {
      // Launch browser
      browser = await puppeteer.default.launch({
        headless: true,
        args: [
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-dev-shm-usage',
          '--disable-accelerated-2d-canvas',
          '--no-first-run',
          '--no-zygote',
          '--disable-gpu'
        ]
      });

      const page = await browser.newPage();

      // Set viewport
      await page.setViewport({
        width: options.width || 800,
        height: options.height || 600,
        deviceScaleFactor: options.scale || 1
      });

      // Create HTML content with Mermaid
      const htmlContent = `
        <!DOCTYPE html>
        <html>
        <head>
          <script src="https://cdn.jsdelivr.net/npm/mermaid@11.6.0/dist/mermaid.min.js"></script>
          <style>
            body {
              margin: 0;
              padding: 20px;
              background-color: ${options.backgroundColor || 'white'};
              font-family: Arial, sans-serif;
            }
            .mermaid {
              display: flex;
              justify-content: center;
              align-items: center;
            }
          </style>
        </head>
        <body>
          <div class="mermaid">
            ${mermaidCode}
          </div>
          <script>
            mermaid.initialize({
              startOnLoad: true,
              theme: '${options.theme || 'default'}',
              securityLevel: 'loose'
            });
          </script>
        </body>
        </html>
      `;

      // Set content and wait for mermaid to render
      await page.setContent(htmlContent);
      await page.waitForSelector('.mermaid svg', { timeout: 10000 });

      // Take screenshot of the mermaid diagram
      const element = await page.$('.mermaid');
      if (!element) {
        throw new Error('Mermaid diagram not found in page');
      }

      const pngBuffer = await element.screenshot({
        type: 'png',
        omitBackground: options.backgroundColor === 'transparent'
      });

      // Save to temp file
      await fs.mkdir(this.tempDir, { recursive: true });
      const timestamp = Date.now();
      const randomId = Math.random().toString(36).substring(2, 15);
      const tempFilePath = path.join(this.tempDir, `puppeteer_${timestamp}_${randomId}.png`);
      await fs.writeFile(tempFilePath, pngBuffer);

      return {
        pngBuffer: Buffer.from(pngBuffer),
        tempFilePath
      };

    } finally {
      if (browser) {
        await browser.close();
      }
    }
  }

  /**
   * Clean up temporary files older than specified age
   */
  async cleanupTempFiles(maxAgeMs: number = 24 * 60 * 60 * 1000): Promise<void> {
    try {
      const files = await fs.readdir(this.tempDir);
      const now = Date.now();

      for (const file of files) {
        const filePath = path.join(this.tempDir, file);
        const stats = await fs.stat(filePath);
        
        if (now - stats.mtime.getTime() > maxAgeMs) {
          await fs.unlink(filePath).catch(console.warn);
        }
      }
    } catch (error) {
      console.warn('Error cleaning up temp files:', error);
    }
  }
}

// Export singleton instance
export const mermaidPngConverter = MermaidPngConverter.getInstance();
