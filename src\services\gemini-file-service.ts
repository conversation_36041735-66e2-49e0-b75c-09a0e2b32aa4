import { GoogleGenAI } from '@google/genai';
import { prisma } from '@/lib/prisma';
import {
  GeminiFileUploadRequest,
  GeminiFileUploadResponse,
  GeminiFileUploadBatchRequest,
  GeminiFileUploadBatchResponse,
  GeminiFileContext
} from '@/types/proposal';
import path from 'path';
import fs from 'fs/promises';

const UPLOAD_DIR = process.env.UPLOAD_DIR || './uploads';

export class GeminiFileService {
  private genAI: GoogleGenAI;

  constructor() {
    if (!process.env.GOOGLE_AI_API_KEY) {
      throw new Error('Google AI API key is required for Gemini file service');
    }
    this.genAI = new GoogleGenAI({
      apiKey: process.env.GOOGLE_AI_API_KEY
    });
  }

  /**
   * Upload a single file to Gemini API
   */
  async uploadFile(request: GeminiFileUploadRequest): Promise<GeminiFileUploadResponse> {
    try {
      // Get document details
      const document = await prisma.document.findFirst({
        where: {
          id: request.documentId,
          tenantId: request.tenantId
        }
      });

      if (!document) {
        throw new Error(`Document not found: ${request.documentId}`);
      }

      // Construct full file path
      const fullFilePath = path.join(UPLOAD_DIR, document.filePath);

      // Check if file exists
      try {
        await fs.access(fullFilePath);
      } catch {
        throw new Error(`File not found on disk: ${document.filePath}`);
      }

      // Upload to Gemini using the new API
      const uploadResponse = await this.genAI.files.upload({
        file: fullFilePath,
        config: {
          mimeType: document.mimeType || 'application/octet-stream',
          displayName: request.displayName || document.name,
        }
      });

      // Wait for file to be processed
      let file = uploadResponse;
      while (file.state === 'PROCESSING') {
        await new Promise(resolve => setTimeout(resolve, 2000)); // Wait 2 seconds
        file = await this.genAI.files.get({ name: file.name! });
      }

      if (file.state === 'FAILED') {
        throw new Error(`Gemini file processing failed for ${document.name}`);
      }

      return {
        fileUri: file.uri || '',
        name: file.name || '',
        displayName: file.displayName || document.name,
        mimeType: file.mimeType || '',
        sizeBytes: file.sizeBytes || '0',
        createTime: file.createTime || '',
        updateTime: file.updateTime || '',
        expirationTime: file.expirationTime || '',
        sha256Hash: file.sha256Hash || '',
        state: file.state as 'PROCESSING' | 'ACTIVE' | 'FAILED'
      };

    } catch (error) {
      console.error(`Gemini file upload error for ${request.documentId}:`, error);
      return {
        fileUri: '',
        name: '',
        displayName: request.displayName,
        mimeType: request.mimeType,
        sizeBytes: '0',
        createTime: '',
        updateTime: '',
        expirationTime: '',
        sha256Hash: '',
        state: 'FAILED',
        error: error instanceof Error ? error.message : 'Unknown upload error'
      };
    }
  }

  /**
   * Upload multiple files to Gemini API in batch
   */
  async uploadFilesBatch(request: GeminiFileUploadBatchRequest): Promise<GeminiFileUploadBatchResponse> {
    const successful: GeminiFileUploadResponse[] = [];
    const failed: Array<{ documentId: string; error: string }> = [];

    // Get all documents
    const documents = await prisma.document.findMany({
      where: {
        id: { in: request.documentIds },
        tenantId: request.tenantId
      }
    });

    // Process each document
    for (const document of documents) {
      try {
        const uploadRequest: GeminiFileUploadRequest = {
          documentId: document.id,
          filePath: document.filePath,
          mimeType: document.mimeType || 'application/octet-stream',
          displayName: document.name,
          tenantId: request.tenantId
        };

        const result = await this.uploadFile(uploadRequest);
        
        if (result.state === 'FAILED') {
          failed.push({
            documentId: document.id,
            error: result.error || 'Upload failed'
          });
        } else {
          successful.push(result);
        }
      } catch (error) {
        failed.push({
          documentId: document.id,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }

    return {
      successful,
      failed,
      totalProcessed: documents.length,
      successCount: successful.length,
      failureCount: failed.length
    };
  }

  /**
   * Get file contexts for proposal generation
   */
  async getFileContexts(
    documentIds: string[],
    tenantId: string,
    geminiFileUris?: Record<string, string>
  ): Promise<GeminiFileContext[]> {
    const documents = await prisma.document.findMany({
      where: {
        id: { in: documentIds },
        tenantId
      }
    });

    return documents.map(doc => {
      const fileUri = geminiFileUris?.[doc.id];
      return {
        fileUri: fileUri || '',
        documentId: doc.id,
        name: doc.name,
        mimeType: doc.mimeType || undefined,
        fileSize: doc.fileSize || undefined,
        uploadStatus: fileUri && fileUri.startsWith('https://') ? 'active' : 'pending',
        error: !fileUri ? 'File not uploaded to Gemini' : undefined
      };
    });
  }

  /**
   * Delete a file from Gemini API
   */
  async deleteFile(fileUri: string): Promise<boolean> {
    try {
      // Extract file name from URI
      const fileName = fileUri.split('/').pop();
      if (!fileName) {
        throw new Error('Invalid file URI');
      }

      await this.genAI.files.delete({ name: fileName });
      return true;
    } catch (error) {
      console.error('Error deleting Gemini file:', error);
      return false;
    }
  }

  /**
   * Check if a file is ready for AI processing
   */
  async checkFileStatus(fileUri: string): Promise<'PROCESSING' | 'ACTIVE' | 'FAILED'> {
    try {
      const fileName = fileUri.split('/').pop();
      if (!fileName) {
        return 'FAILED';
      }

      const file = await this.genAI.files.get({ name: fileName });
      return file.state as 'PROCESSING' | 'ACTIVE' | 'FAILED';
    } catch (error) {
      console.error('Error checking file status:', error);
      return 'FAILED';
    }
  }

  /**
   * Validate file for Gemini upload
   */
  static validateFileForGemini(mimeType: string, fileSize: number): { valid: boolean; error?: string } {
    // Gemini supported file types
    const supportedTypes = [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'text/plain',
      'text/csv',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/vnd.ms-powerpoint',
      'application/vnd.openxmlformats-officedocument.presentationml.presentation',
      'image/jpeg',
      'image/png',
      'image/gif',
      'image/webp'
    ];

    if (!supportedTypes.includes(mimeType)) {
      return {
        valid: false,
        error: `File type ${mimeType} is not supported by Gemini API`
      };
    }

    // Check file size (Gemini has a 20MB limit)
    const maxSize = 20 * 1024 * 1024; // 20MB
    if (fileSize > maxSize) {
      return {
        valid: false,
        error: `File size ${Math.round(fileSize / 1024 / 1024)}MB exceeds Gemini's 20MB limit`
      };
    }

    return { valid: true };
  }
}

export const geminiFileService = new GeminiFileService();
