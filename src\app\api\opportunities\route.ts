import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { opportunityManagementService, opportunityFiltersSchema, paginationSchema } from '@/services/opportunity-management';
import { hasPermission } from '@/lib/permissions';
import { PermissionResource, PermissionAction } from '@/types';
import { z } from 'zod';

/**
 * GET /api/opportunities
 * Get opportunities with filtering and pagination
 */
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user's current tenant
    const currentTenant = session.user.tenants?.[0];
    if (!currentTenant) {
      return NextResponse.json({ error: 'No tenant access' }, { status: 403 });
    }

    // Check permission
    const hasAccess = await hasPermission(
      PermissionResource.OPPORTUNITIES,
      PermissionAction.READ,
      currentTenant.id
    );

    if (!hasAccess) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url);
    
    const filters = {
      search: searchParams.get('search') || undefined,
      stage: searchParams.get('stage') || undefined,
      priority: searchParams.get('priority') || undefined,
      ownerId: searchParams.get('ownerId') || undefined,
      companyId: searchParams.get('companyId') || undefined,
      minValue: searchParams.get('minValue') ? Number(searchParams.get('minValue')) : undefined,
      maxValue: searchParams.get('maxValue') ? Number(searchParams.get('maxValue')) : undefined,
      dateFrom: searchParams.get('dateFrom') || undefined,
      dateTo: searchParams.get('dateTo') || undefined,
      tags: searchParams.get('tags') ? searchParams.get('tags')!.split(',') : undefined,
    };

    const pagination = {
      page: searchParams.get('page') ? Number(searchParams.get('page')) : 1,
      limit: searchParams.get('limit') ? Number(searchParams.get('limit')) : 20,
      sortBy: searchParams.get('sortBy') || 'createdAt',
      sortOrder: searchParams.get('sortOrder') || 'desc',
    };

    // Validate filters and pagination
    const validatedFilters = opportunityFiltersSchema.parse(filters);
    const validatedPagination = paginationSchema.parse(pagination);

    const result = await opportunityManagementService.getOpportunities(
      currentTenant.id,
      validatedFilters,
      validatedPagination
    );

    return NextResponse.json(result);
  } catch (error) {
    console.error('Error fetching opportunities:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid parameters', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/opportunities
 * Create a new opportunity
 */
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user's current tenant
    const currentTenant = session.user.tenants?.[0];
    if (!currentTenant) {
      return NextResponse.json({ error: 'No tenant access' }, { status: 403 });
    }

    // Check permission
    const hasAccess = await hasPermission(
      PermissionResource.OPPORTUNITIES,
      PermissionAction.CREATE,
      currentTenant.id
    );

    if (!hasAccess) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const body = await request.json();

    const opportunity = await opportunityManagementService.createOpportunity(
      currentTenant.id,
      body,
      session.user.id
    );

    return NextResponse.json(opportunity, { status: 201 });
  } catch (error) {
    console.error('Error creating opportunity:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid data', details: error.errors },
        { status: 400 }
      );
    }

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/opportunities
 * Bulk delete opportunities
 */
export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user's current tenant
    const currentTenant = session.user.tenants?.[0];
    if (!currentTenant) {
      return NextResponse.json({ error: 'No tenant access' }, { status: 403 });
    }

    // Check permission
    const hasAccess = await hasPermission(
      PermissionResource.OPPORTUNITIES,
      PermissionAction.DELETE,
      currentTenant.id
    );

    if (!hasAccess) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const body = await request.json();
    const { opportunityIds } = body;

    if (!Array.isArray(opportunityIds) || opportunityIds.length === 0) {
      return NextResponse.json(
        { error: 'Opportunity IDs array is required' },
        { status: 400 }
      );
    }

    // Limit bulk operations to prevent abuse
    if (opportunityIds.length > 100) {
      return NextResponse.json(
        { error: 'Maximum 100 opportunities can be deleted at once' },
        { status: 400 }
      );
    }

    const deletedCount = await opportunityManagementService.bulkDeleteOpportunities(
      opportunityIds,
      currentTenant.id
    );

    return NextResponse.json({
      success: true,
      data: { deletedCount },
      message: `${deletedCount} opportunity(ies) deleted successfully`
    });

  } catch (error) {
    console.error('Bulk delete opportunities API error:', error);

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to delete opportunities' },
      { status: 500 }
    );
  }
}
