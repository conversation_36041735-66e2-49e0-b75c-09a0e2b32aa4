'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { toast } from 'sonner';
import { PageLayout } from '@/components/layout/page-layout';
import { PermissionGate } from '@/components/ui/permission-gate';
import { PermissionResource, PermissionAction } from '@/types';
import { EmptyState } from '@/components/ui/empty-state';
import { ExclamationTriangleIcon } from '@heroicons/react/24/outline';
import {
  Plus,
  Edit,
  Trash2,
  MoreVertical,
  Search,
  Loader2,
  Database,
  CheckCircle,
  XCircle
} from 'lucide-react';

interface ProposalContentSection {
  id: string;
  sectionType: string;
  title: string;
  description?: string;
  promptEn: string;
  promptAr?: string;
  systemPromptEn: string;
  systemPromptAr?: string;
  orderIndex: number;
  isActive: boolean;
  isDefault: boolean;
  createdAt: string;
  updatedAt: string;
  createdBy?: {
    id: string;
    firstName?: string;
    lastName?: string;
    email: string;
  };
}

interface ContentSectionFormData {
  sectionType: string;
  title: string;
  description: string;
  promptEn: string;
  promptAr: string;
  systemPromptEn: string;
  systemPromptAr: string;
  orderIndex: number;
  isActive: boolean;
  isDefault: boolean;
}

const initialFormData: ContentSectionFormData = {
  sectionType: '',
  title: '',
  description: '',
  promptEn: '',
  promptAr: '',
  systemPromptEn: '',
  systemPromptAr: '',
  orderIndex: 1,
  isActive: true,
  isDefault: false
};

export default function ProposalContentSectionsPage() {
  const [contentSections, setContentSections] = useState<ProposalContentSection[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingSection, setEditingSection] = useState<ProposalContentSection | null>(null);
  const [formData, setFormData] = useState<ContentSectionFormData>(initialFormData);
  const [saving, setSaving] = useState(false);
  const [seeding, setSeeding] = useState(false);

  // Load content sections
  const loadContentSections = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/proposal-content-sections');
      
      if (!response.ok) {
        throw new Error('Failed to fetch content sections');
      }

      const result = await response.json();
      setContentSections(result.data || []);
    } catch (error) {
      console.error('Error loading content sections:', error);
      toast.error('Failed to load content sections');
    } finally {
      setLoading(false);
    }
  };

  // Seed default content sections
  const seedDefaultSections = async () => {
    try {
      setSeeding(true);
      const response = await fetch('/api/proposal-content-sections/seed', {
        method: 'POST'
      });
      
      if (!response.ok) {
        throw new Error('Failed to seed default sections');
      }

      const result = await response.json();
      toast.success(result.message);
      loadContentSections(); // Reload the list
    } catch (error) {
      console.error('Error seeding default sections:', error);
      toast.error('Failed to seed default sections');
    } finally {
      setSeeding(false);
    }
  };

  // Save content section (create or update)
  const saveContentSection = async () => {
    try {
      setSaving(true);
      
      const url = editingSection 
        ? `/api/proposal-content-sections/${editingSection.id}`
        : '/api/proposal-content-sections';
      
      const method = editingSection ? 'PUT' : 'POST';
      
      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to save content section');
      }

      toast.success(editingSection ? 'Content section updated' : 'Content section created');
      setIsDialogOpen(false);
      setEditingSection(null);
      setFormData(initialFormData);
      loadContentSections();
    } catch (error) {
      console.error('Error saving content section:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to save content section');
    } finally {
      setSaving(false);
    }
  };

  // Delete content section
  const deleteContentSection = async (section: ProposalContentSection) => {
    if (section.isDefault) {
      toast.error('Cannot delete default content sections');
      return;
    }

    if (!confirm('Are you sure you want to delete this content section?')) {
      return;
    }

    try {
      const response = await fetch(`/api/proposal-content-sections/${section.id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Failed to delete content section');
      }

      toast.success('Content section deleted');
      loadContentSections();
    } catch (error) {
      console.error('Error deleting content section:', error);
      toast.error('Failed to delete content section');
    }
  };

  // Open edit dialog
  const openEditDialog = (section: ProposalContentSection) => {
    setEditingSection(section);
    setFormData({
      sectionType: section.sectionType,
      title: section.title,
      description: section.description || '',
      promptEn: section.promptEn,
      promptAr: section.promptAr || '',
      systemPromptEn: section.systemPromptEn,
      systemPromptAr: section.systemPromptAr || '',
      orderIndex: section.orderIndex,
      isActive: section.isActive,
      isDefault: section.isDefault
    });
    setIsDialogOpen(true);
  };

  // Open create dialog
  const openCreateDialog = () => {
    setEditingSection(null);
    setFormData(initialFormData);
    setIsDialogOpen(true);
  };

  // Filter content sections based on search
  const filteredSections = contentSections.filter(section =>
    section.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    section.sectionType.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (section.description && section.description.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  useEffect(() => {
    loadContentSections();
  }, []);

  // Actions for the page header
  const actions = (
    <>
      <Button
        variant="outline"
        onClick={seedDefaultSections}
        disabled={seeding}
      >
        {seeding ? (
          <Loader2 className="h-4 w-4 animate-spin mr-2" />
        ) : (
          <Database className="h-4 w-4 mr-2" />
        )}
        Seed Defaults
      </Button>
      <Button onClick={openCreateDialog}>
        <Plus className="h-4 w-4 mr-2" />
        Add Section
      </Button>
    </>
  );

  return (
    <PermissionGate
      resource={PermissionResource.PROPOSALS}
      action={PermissionAction.READ}
      fallback={
        <PageLayout>
          <Card>
            <CardContent className="p-8">
              <EmptyState
                icon={<ExclamationTriangleIcon className="w-12 h-12 text-muted-foreground" />}
                title="Access Denied"
                description="You don't have permission to manage proposal content sections. Please contact your administrator for access."
                action={{
                  label: 'Back to Dashboard',
                  onClick: () => window.location.href = '/dashboard',
                  variant: 'primary'
                }}
              />
            </CardContent>
          </Card>
        </PageLayout>
      }
    >
      <PageLayout
        title="Proposal Content Sections"
        description="Manage AI prompts for proposal section generation"
        actions={actions}
      >
        <div className="space-y-6">
          {/* Search and Filters */}
          <Card>
            <CardHeader>
              <CardTitle>Search & Filters</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center space-x-2">
                <div className="relative flex-1 max-w-sm">
                  <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search sections..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-8"
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Content Sections Table */}
          <Card>
            <CardHeader>
              <CardTitle>Content Sections</CardTitle>
              <CardDescription>
                Configure AI prompts for different proposal sections
              </CardDescription>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="flex items-center justify-center py-8">
                  <Loader2 className="h-8 w-8 animate-spin" />
                </div>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Section</TableHead>
                      <TableHead>Type</TableHead>
                      <TableHead>Order</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Default</TableHead>
                      <TableHead className="w-[100px]">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredSections.map((section) => (
                      <TableRow key={section.id}>
                        <TableCell>
                          <div>
                            <div className="font-medium">{section.title}</div>
                            {section.description && (
                              <div className="text-sm text-muted-foreground">
                                {section.description}
                              </div>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge variant="outline">{section.sectionType}</Badge>
                        </TableCell>
                        <TableCell>{section.orderIndex}</TableCell>
                        <TableCell>
                          {section.isActive ? (
                            <Badge variant="default" className="bg-green-100 text-green-800">
                              <CheckCircle className="h-3 w-3 mr-1" />
                              Active
                            </Badge>
                          ) : (
                            <Badge variant="secondary">
                              <XCircle className="h-3 w-3 mr-1" />
                              Inactive
                            </Badge>
                          )}
                        </TableCell>
                        <TableCell>
                          {section.isDefault && (
                            <Badge variant="outline">Default</Badge>
                          )}
                        </TableCell>
                        <TableCell>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" className="h-8 w-8 p-0">
                                <MoreVertical className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem onClick={() => openEditDialog(section)}>
                                <Edit className="h-4 w-4 mr-2" />
                                Edit
                              </DropdownMenuItem>
                              {!section.isDefault && (
                                <DropdownMenuItem
                                  onClick={() => deleteContentSection(section)}
                                  className="text-red-600"
                                >
                                  <Trash2 className="h-4 w-4 mr-2" />
                                  Delete
                                </DropdownMenuItem>
                              )}
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              )}
            </CardContent>
          </Card>
          {/* Create/Edit Dialog */}
          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>
              {editingSection ? 'Edit Content Section' : 'Create Content Section'}
            </DialogTitle>
            <DialogDescription>
              Configure the AI prompts for proposal section generation
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-6">
            {/* Basic Information */}
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="sectionType">Section Type</Label>
                <Input
                  id="sectionType"
                  value={formData.sectionType}
                  onChange={(e) => setFormData({ ...formData, sectionType: e.target.value })}
                  placeholder="e.g., executive_summary"
                  disabled={editingSection?.isDefault}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="title">Title</Label>
                <Input
                  id="title"
                  value={formData.title}
                  onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                  placeholder="e.g., Executive Summary"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Input
                id="description"
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                placeholder="Brief description of this section"
              />
            </div>

            {/* Prompts */}
            <Tabs defaultValue="english" className="w-full">
              <TabsList>
                <TabsTrigger value="english">English Prompts</TabsTrigger>
                <TabsTrigger value="arabic">Arabic Prompts</TabsTrigger>
              </TabsList>

              <TabsContent value="english" className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="promptEn">User Prompt (English)</Label>
                  <Textarea
                    id="promptEn"
                    value={formData.promptEn}
                    onChange={(e) => setFormData({ ...formData, promptEn: e.target.value })}
                    placeholder="The main prompt that will be sent to the AI..."
                    rows={4}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="systemPromptEn">System Prompt (English)</Label>
                  <Textarea
                    id="systemPromptEn"
                    value={formData.systemPromptEn}
                    onChange={(e) => setFormData({ ...formData, systemPromptEn: e.target.value })}
                    placeholder="The system prompt that defines the AI's role..."
                    rows={3}
                  />
                </div>
              </TabsContent>

              <TabsContent value="arabic" className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="promptAr">User Prompt (Arabic)</Label>
                  <Textarea
                    id="promptAr"
                    value={formData.promptAr}
                    onChange={(e) => setFormData({ ...formData, promptAr: e.target.value })}
                    placeholder="النص الرئيسي الذي سيتم إرساله للذكاء الاصطناعي..."
                    rows={4}
                    dir="rtl"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="systemPromptAr">System Prompt (Arabic)</Label>
                  <Textarea
                    id="systemPromptAr"
                    value={formData.systemPromptAr}
                    onChange={(e) => setFormData({ ...formData, systemPromptAr: e.target.value })}
                    placeholder="النص النظام الذي يحدد دور الذكاء الاصطناعي..."
                    rows={3}
                    dir="rtl"
                  />
                </div>
              </TabsContent>
            </Tabs>

            {/* Settings */}
            <div className="grid grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="orderIndex">Order Index</Label>
                <Input
                  id="orderIndex"
                  type="number"
                  value={formData.orderIndex}
                  onChange={(e) => setFormData({ ...formData, orderIndex: parseInt(e.target.value) || 1 })}
                  min="1"
                />
              </div>
              <div className="flex items-center space-x-2">
                <Switch
                  id="isActive"
                  checked={formData.isActive}
                  onCheckedChange={(checked) => setFormData({ ...formData, isActive: checked })}
                />
                <Label htmlFor="isActive">Active</Label>
              </div>
              <div className="flex items-center space-x-2">
                <Switch
                  id="isDefault"
                  checked={formData.isDefault}
                  onCheckedChange={(checked) => setFormData({ ...formData, isDefault: checked })}
                  disabled={editingSection?.isDefault}
                />
                <Label htmlFor="isDefault">Default Section</Label>
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={saveContentSection} disabled={saving}>
              {saving ? (
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
              ) : null}
              {editingSection ? 'Update' : 'Create'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
        </div>
      </PageLayout>
    </PermissionGate>
  );
}
