'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { DocumentWithRelations, DocumentStats } from '@/services/document-management';
import { PageLayout } from '@/components/layout/page-layout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { PermissionGate } from '@/components/ui/permission-gate';
import { PermissionResource, PermissionAction } from '@/types';
import { useEnhancedDeleteDialog, deleteConfigurations } from '@/components/ui/enhanced-delete-dialog';
import { useToast } from '@/hooks/use-toast';
import { EmptyState } from '@/components/ui/empty-state';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { DocumentUploadDialog } from '@/components/documents/document-upload-dialog';
import { DocumentDetailsDialog } from '@/components/documents/document-details-dialog';
import { downloadFile } from '@/lib/download-utils';
import {
  Upload,
  Search,
  Filter,
  Grid,
  List,
  Download,
  FileText,
  BarChart3,
  Eye,
  Edit,
  Trash2,
  Plus,
  FolderOpen,
  HardDrive,
  Calendar,
  Users,
  Tag,
  ArrowUpDown,
  MoreHorizontal,
  Sparkles,
  Wand2,
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from '@/components/ui/dropdown-menu';
import { Checkbox } from '@/components/ui/checkbox';
import { cn } from '@/lib/utils';
import { format } from 'date-fns';

interface DocumentsPageState {
  documents: DocumentWithRelations[];
  stats: DocumentStats | null;
  loading: boolean;
  searchTerm: string;
  relatedToTypeFilter: string;
  mimeTypeFilter: string;
  uploadedByFilter: string;
  tagsFilter: string[];
  sortBy: 'name' | 'createdAt' | 'fileSize' | 'mimeType';
  sortOrder: 'asc' | 'desc';
  viewMode: 'list' | 'grid';
  page: number;
  totalPages: number;
  selectedDocuments: Set<string>;
  uploadDialogOpen: boolean;
  detailsDialogOpen: boolean;
  selectedDocument: DocumentWithRelations | null;
  aiAvailable: boolean;
}

export default function DocumentsPage() {
  const router = useRouter();
  const { toast } = useToast();
  const { showDeleteDialog, DialogComponent } = useEnhancedDeleteDialog();

  const [state, setState] = useState<DocumentsPageState>({
    documents: [],
    stats: null,
    loading: true,
    searchTerm: '',
    relatedToTypeFilter: 'all',
    mimeTypeFilter: 'all',
    uploadedByFilter: 'all',
    tagsFilter: [],
    sortBy: 'createdAt',
    sortOrder: 'desc',
    viewMode: 'list',
    page: 1,
    totalPages: 1,
    selectedDocuments: new Set(),
    uploadDialogOpen: false,
    detailsDialogOpen: false,
    selectedDocument: null,
    aiAvailable: false,
  });

  // Load documents
  const loadDocuments = async () => {
    try {
      setState(prev => ({ ...prev, loading: true }));

      const params = new URLSearchParams({
        page: state.page.toString(),
        limit: '20',
        sortBy: state.sortBy,
        sortOrder: state.sortOrder,
        ...(state.searchTerm && { search: state.searchTerm }),
        ...(state.relatedToTypeFilter !== 'all' && { relatedToType: state.relatedToTypeFilter }),
        ...(state.mimeTypeFilter !== 'all' && { mimeType: state.mimeTypeFilter }),
        ...(state.uploadedByFilter !== 'all' && { uploadedBy: state.uploadedByFilter }),
        ...(state.tagsFilter.length > 0 && { tags: JSON.stringify(state.tagsFilter) }),
      });

      const response = await fetch(`/api/documents?${params}`);
      const data = await response.json();

      if (response.ok && data.success) {
        setState(prev => ({
          ...prev,
          documents: data.data.documents,
          totalPages: data.data.pagination.pages,
          loading: false,
        }));
      } else {
        throw new Error(data.error || 'Failed to load documents');
      }
    } catch (error) {
      console.error('Error loading documents:', error);
      toast({
        title: 'Error',
        description: 'Failed to load documents',
        variant: 'destructive',
      });
      setState(prev => ({ ...prev, loading: false }));
    }
  };

  // Load statistics
  const loadStats = async () => {
    try {
      const response = await fetch('/api/documents/stats');
      const data = await response.json();

      if (response.ok && data.success) {
        setState(prev => ({ ...prev, stats: data.data.stats }));
      }
    } catch (error) {
      console.error('Error loading document stats:', error);
    }
  };

  // Check AI availability
  const checkAiAvailability = async () => {
    try {
      const response = await fetch('/api/documents/analyze');
      const data = await response.json();
      setState(prev => ({ ...prev, aiAvailable: data.success && data.data.available }));
    } catch (error) {
      console.error('Failed to check AI availability:', error);
      setState(prev => ({ ...prev, aiAvailable: false }));
    }
  };

  useEffect(() => {
    loadDocuments();
    loadStats();
    checkAiAvailability();
  }, [state.page, state.searchTerm, state.relatedToTypeFilter, state.mimeTypeFilter, state.uploadedByFilter, state.tagsFilter, state.sortBy, state.sortOrder]);

  // Format file size
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // Get file icon
  const getFileIcon = (mimeType: string) => {
    if (mimeType?.startsWith('image/')) return '🖼️';
    if (mimeType?.includes('pdf')) return '📄';
    if (mimeType?.includes('word') || mimeType?.includes('document')) return '📝';
    if (mimeType?.includes('excel') || mimeType?.includes('spreadsheet')) return '📊';
    if (mimeType?.includes('powerpoint') || mimeType?.includes('presentation')) return '📈';
    if (mimeType?.startsWith('video/')) return '🎥';
    if (mimeType?.startsWith('audio/')) return '🎵';
    return '📁';
  };

  // Handle document actions
  const handleView = (document: DocumentWithRelations) => {
    setState(prev => ({ 
      ...prev, 
      selectedDocument: document, 
      detailsDialogOpen: true 
    }));
  };

  const handleDownload = async (document: DocumentWithRelations) => {
    try {
      await downloadFile(`/api/documents/${document.id}/download`, document.name);
      toast({
        title: 'Success',
        description: 'Document downloaded successfully',
      });
    } catch (error) {
      console.error('Error downloading document:', error);
      toast({
        title: 'Error',
        description: 'Failed to download document',
        variant: 'destructive',
      });
    }
  };



  const handleDelete = async (document: DocumentWithRelations) => {
    try {
      await showDeleteDialog({
        ...deleteConfigurations.document,
        itemName: document.name,
        onConfirm: async () => {
          const response = await fetch(`/api/documents/${document.id}`, {
            method: 'DELETE',
          });

          if (response.ok) {
            toast({
              title: 'Success',
              description: 'Document deleted successfully',
            });
            loadDocuments();
            loadStats();
          } else {
            const errorData = await response.json();
            throw new Error(errorData.error || 'Failed to delete document');
          }
        },
      });
    } catch (error) {
      console.error('Error deleting document:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete document',
        variant: 'destructive',
      });
    }
  };

  // Handle bulk operations
  const handleBulkDelete = async () => {
    if (state.selectedDocuments.size === 0) return;

    try {
      await showDeleteDialog({
        ...deleteConfigurations.bulkDocuments,
        itemName: `${state.selectedDocuments.size} document(s)`,
        onConfirm: async () => {
          const deletePromises = Array.from(state.selectedDocuments).map(id =>
            fetch(`/api/documents/${id}`, { method: 'DELETE' })
          );

          await Promise.all(deletePromises);
          
          toast({
            title: 'Success',
            description: `${state.selectedDocuments.size} document(s) deleted successfully`,
          });
          
          setState(prev => ({ ...prev, selectedDocuments: new Set() }));
          loadDocuments();
          loadStats();
        },
      });
    } catch (error) {
      console.error('Error deleting documents:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete documents',
        variant: 'destructive',
      });
    }
  };

  const actions = (
    <div className="flex items-center gap-2">
      <PermissionGate
        resource={PermissionResource.DOCUMENTS}
        action={PermissionAction.CREATE}
      >
        <Button 
          size="sm" 
          onClick={() => setState(prev => ({ ...prev, uploadDialogOpen: true }))}
        >
          <Plus className="h-4 w-4 mr-2" />
          Upload Documents
        </Button>
      </PermissionGate>
      
      {state.selectedDocuments.size > 0 && (
        <PermissionGate
          resource={PermissionResource.DOCUMENTS}
          action={PermissionAction.DELETE}
        >
          <Button 
            variant="destructive" 
            size="sm" 
            onClick={handleBulkDelete}
          >
            <Trash2 className="h-4 w-4 mr-2" />
            Delete Selected ({state.selectedDocuments.size})
          </Button>
        </PermissionGate>
      )}
    </div>
  );

  return (
    <PageLayout
      title="Document Management"
      description="Manage all uploaded documents in your system"
      actions={actions}
    >
      <div className="space-y-6">
        {/* Statistics Cards */}
        {state.stats && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Total Documents</p>
                    <p className="text-2xl font-bold text-gray-900">{state.stats.totalDocuments}</p>
                  </div>
                  <FileText className="w-8 h-8 text-blue-500" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Total Size</p>
                    <p className="text-2xl font-bold text-gray-900">{formatFileSize(state.stats.totalSize)}</p>
                  </div>
                  <HardDrive className="w-8 h-8 text-green-500" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Recent Uploads</p>
                    <p className="text-2xl font-bold text-gray-900">{state.stats.recentUploads}</p>
                    <p className="text-xs text-gray-500">Last 7 days</p>
                  </div>
                  <Calendar className="w-8 h-8 text-purple-500" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">File Types</p>
                    <p className="text-2xl font-bold text-gray-900">{Object.keys(state.stats.byType).length}</p>
                  </div>
                  <BarChart3 className="w-8 h-8 text-orange-500" />
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Filters and Search */}
        <Card>
          <CardContent className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4 mb-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="Search documents..."
                  value={state.searchTerm}
                  onChange={(e) => setState(prev => ({ ...prev, searchTerm: e.target.value, page: 1 }))}
                  className="pl-10"
                />
              </div>
              
              <Select 
                value={state.relatedToTypeFilter} 
                onValueChange={(value) => setState(prev => ({ ...prev, relatedToTypeFilter: value, page: 1 }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Filter by entity" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Entities</SelectItem>
                  <SelectItem value="opportunity">Opportunities</SelectItem>
                  <SelectItem value="contact">Contacts</SelectItem>
                  <SelectItem value="company">Companies</SelectItem>
                  <SelectItem value="lead">Leads</SelectItem>
                  <SelectItem value="proposal">Proposals</SelectItem>
                  <SelectItem value="project">Projects</SelectItem>
                  <SelectItem value="handover">Handovers</SelectItem>
                </SelectContent>
              </Select>

              <Select 
                value={state.mimeTypeFilter} 
                onValueChange={(value) => setState(prev => ({ ...prev, mimeTypeFilter: value, page: 1 }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Filter by type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  <SelectItem value="application/pdf">PDF</SelectItem>
                  <SelectItem value="image/">Images</SelectItem>
                  <SelectItem value="application/vnd.openxmlformats-officedocument.wordprocessingml.document">Word Documents</SelectItem>
                  <SelectItem value="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet">Excel Files</SelectItem>
                  <SelectItem value="text/">Text Files</SelectItem>
                </SelectContent>
              </Select>

              <Select 
                value={`${state.sortBy}-${state.sortOrder}`} 
                onValueChange={(value) => {
                  const [sortBy, sortOrder] = value.split('-') as [typeof state.sortBy, typeof state.sortOrder];
                  setState(prev => ({ ...prev, sortBy, sortOrder, page: 1 }));
                }}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Sort by" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="createdAt-desc">Newest First</SelectItem>
                  <SelectItem value="createdAt-asc">Oldest First</SelectItem>
                  <SelectItem value="name-asc">Name A-Z</SelectItem>
                  <SelectItem value="name-desc">Name Z-A</SelectItem>
                  <SelectItem value="fileSize-desc">Largest First</SelectItem>
                  <SelectItem value="fileSize-asc">Smallest First</SelectItem>
                </SelectContent>
              </Select>

              <div className="flex items-center gap-2">
                <Button
                  variant={state.viewMode === 'list' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setState(prev => ({ ...prev, viewMode: 'list' }))}
                >
                  <List className="w-4 h-4" />
                </Button>
                <Button
                  variant={state.viewMode === 'grid' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setState(prev => ({ ...prev, viewMode: 'grid' }))}
                >
                  <Grid className="w-4 h-4" />
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Document List/Grid */}
        {state.loading ? (
          <Card>
            <CardContent className="p-8">
              <div className="flex items-center justify-center">
                <LoadingSpinner size="lg" />
              </div>
            </CardContent>
          </Card>
        ) : state.documents.length === 0 ? (
          <Card>
            <CardContent>
              <EmptyState
                icon={<FolderOpen className="w-12 h-12" />}
                title="No documents found"
                description="Upload your first document or adjust your search filters."
                action={{
                  label: 'Upload Documents',
                  onClick: () => setState(prev => ({ ...prev, uploadDialogOpen: true })),
                  variant: 'primary'
                }}
              />
            </CardContent>
          </Card>
        ) : (
          <Card>
            <CardContent className="p-6">
              {state.viewMode === 'grid' ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                  {state.documents.map((document) => (
                    <div
                      key={document.id}
                      className="border rounded-lg p-4 hover:shadow-md transition-shadow"
                    >
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex items-center space-x-2">
                          <Checkbox
                            checked={state.selectedDocuments.has(document.id)}
                            onCheckedChange={(checked) => {
                              setState(prev => {
                                const newSelected = new Set(prev.selectedDocuments);
                                if (checked) {
                                  newSelected.add(document.id);
                                } else {
                                  newSelected.delete(document.id);
                                }
                                return { ...prev, selectedDocuments: newSelected };
                              });
                            }}
                          />
                          <span className="text-2xl">{getFileIcon(document.mimeType || '')}</span>
                        </div>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm">
                              <MoreHorizontal className="w-4 h-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem onClick={() => handleView(document)}>
                              <Eye className="w-4 h-4 mr-2" />
                              View Details
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleDownload(document)}>
                              <Download className="w-4 h-4 mr-2" />
                              Download
                            </DropdownMenuItem>

                            <DropdownMenuSeparator />
                            <PermissionGate
                              resource={PermissionResource.DOCUMENTS}
                              action={PermissionAction.DELETE}
                            >
                              <DropdownMenuItem
                                onClick={() => handleDelete(document)}
                                className="text-red-600"
                              >
                                <Trash2 className="w-4 h-4 mr-2" />
                                Delete
                              </DropdownMenuItem>
                            </PermissionGate>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>

                      <div className="space-y-2">
                        <h4 className="font-medium text-sm text-gray-900 truncate" title={document.name}>
                          {document.name}
                        </h4>
                        <div className="flex items-center justify-between text-xs text-gray-500">
                          <span>{document.fileSize ? formatFileSize(document.fileSize) : 'Unknown'}</span>
                          <span>{format(new Date(document.createdAt), 'MMM dd')}</span>
                        </div>
                        {document.description && (
                          <p className="text-xs text-gray-600 line-clamp-2" title={document.description}>
                            {document.description}
                          </p>
                        )}
                        <div className="flex items-center justify-between">
                          <Badge variant="secondary" className="text-xs">
                            {document.relatedToType}
                          </Badge>
                          {document.uploadedBy && (
                            <span className="text-xs text-gray-500">
                              {document.uploadedBy.firstName} {document.uploadedBy.lastName}
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="space-y-2">
                  {/* Select All Checkbox */}
                  <div className="flex items-center space-x-2 pb-2 border-b">
                    <Checkbox
                      checked={state.selectedDocuments.size === state.documents.length && state.documents.length > 0}
                      onCheckedChange={(checked) => {
                        setState(prev => ({
                          ...prev,
                          selectedDocuments: checked
                            ? new Set(prev.documents.map(d => d.id))
                            : new Set()
                        }));
                      }}
                    />
                    <span className="text-sm font-medium text-gray-700">
                      Select All ({state.documents.length})
                    </span>
                  </div>

                  {state.documents.map((document) => (
                    <div
                      key={document.id}
                      className="flex items-center space-x-4 p-3 hover:bg-gray-50 rounded-lg transition-colors"
                    >
                      <Checkbox
                        checked={state.selectedDocuments.has(document.id)}
                        onCheckedChange={(checked) => {
                          setState(prev => {
                            const newSelected = new Set(prev.selectedDocuments);
                            if (checked) {
                              newSelected.add(document.id);
                            } else {
                              newSelected.delete(document.id);
                            }
                            return { ...prev, selectedDocuments: newSelected };
                          });
                        }}
                      />

                      <div className="flex-shrink-0">
                        <span className="text-2xl">{getFileIcon(document.mimeType || '')}</span>
                      </div>

                      <div className="flex-1 min-w-0">
                        <div className="flex items-start justify-between">
                          <div className="flex-1 min-w-0">
                            <h4 className="font-medium text-sm text-gray-900 truncate">
                              {document.name}
                            </h4>
                            {document.description && (
                              <p className="text-xs text-gray-600 mt-1 line-clamp-1">
                                {document.description}
                              </p>
                            )}
                            <div className="flex items-center space-x-4 mt-2 text-xs text-gray-500">
                              <span>{document.fileSize ? formatFileSize(document.fileSize) : 'Unknown size'}</span>
                              <span>{format(new Date(document.createdAt), 'MMM dd, yyyy')}</span>
                              <Badge variant="secondary" className="text-xs">
                                {document.relatedToType}
                              </Badge>
                              {document.uploadedBy && (
                                <span>
                                  by {document.uploadedBy.firstName} {document.uploadedBy.lastName}
                                </span>
                              )}
                            </div>
                          </div>

                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="sm">
                                <MoreHorizontal className="w-4 h-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem onClick={() => handleView(document)}>
                                <Eye className="w-4 h-4 mr-2" />
                                View Details
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => handleDownload(document)}>
                                <Download className="w-4 h-4 mr-2" />
                                Download
                              </DropdownMenuItem>

                              <DropdownMenuSeparator />
                              <PermissionGate
                                resource={PermissionResource.DOCUMENTS}
                                action={PermissionAction.DELETE}
                              >
                                <DropdownMenuItem
                                  onClick={() => handleDelete(document)}
                                  className="text-red-600"
                                >
                                  <Trash2 className="w-4 h-4 mr-2" />
                                  Delete
                                </DropdownMenuItem>
                              </PermissionGate>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        )}

        {/* Pagination */}
        {state.totalPages > 1 && (
          <div className="flex items-center justify-center space-x-2">
            <Button
              variant="outline"
              onClick={() => setState(prev => ({ ...prev, page: Math.max(1, prev.page - 1) }))}
              disabled={state.page === 1}
            >
              Previous
            </Button>
            <span className="text-sm text-gray-600">
              Page {state.page} of {state.totalPages}
            </span>
            <Button
              variant="outline"
              onClick={() => setState(prev => ({ ...prev, page: Math.min(prev.totalPages, prev.page + 1) }))}
              disabled={state.page === state.totalPages}
            >
              Next
            </Button>
          </div>
        )}
      </div>

      {/* Upload Dialog */}
      <DocumentUploadDialog
        open={state.uploadDialogOpen}
        onOpenChange={(open) => setState(prev => ({ ...prev, uploadDialogOpen: open }))}
        relatedToType="general"
        relatedToId="system"
        onUploadComplete={() => {
          loadDocuments();
          loadStats();
          setState(prev => ({ ...prev, uploadDialogOpen: false }));
        }}
        onUploadError={(error) => {
          toast({
            title: 'Upload Error',
            description: error,
            variant: 'destructive',
          });
        }}
      />

      {/* Details Dialog */}
      <DocumentDetailsDialog
        open={state.detailsDialogOpen}
        onOpenChange={(open) => setState(prev => ({ ...prev, detailsDialogOpen: open }))}
        document={state.selectedDocument}
        onDownload={handleDownload}
        aiAvailable={state.aiAvailable}
      />

      {/* Delete Dialog */}
      <DialogComponent />
    </PageLayout>
  );
}
