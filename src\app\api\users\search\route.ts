import { NextResponse } from 'next/server';
import { withPermission, AuthenticatedRequest } from '@/lib/permission-middleware';
import { PermissionAction, PermissionResource } from '@/types';
import { UserManagementService } from '@/services/user-management';

/**
 * GET /api/users/search
 * Search for users who are not already members of the current tenant
 */
export const GET = withPermission({
  resource: PermissionResource.USERS,
  action: PermissionAction.CREATE,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest) => {
  try {
    const { searchParams } = new URL(req.url);
    const query = searchParams.get('q');

    if (!query || query.length < 3) {
      return NextResponse.json({
        success: false,
        error: 'Search query must be at least 3 characters long'
      }, { status: 400 });
    }

    const userService = new UserManagementService();
    const users = await userService.searchUsersNotInTenant(req.tenantId, query);

    return NextResponse.json({
      success: true,
      data: users,
      count: users.length
    });

  } catch (error) {
    console.error('User search error:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal server error' },
      { status: 500 }
    );
  }
});
