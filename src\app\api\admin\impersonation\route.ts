import { NextRequest, NextResponse } from 'next/server';
import { withSuperAdminAuth } from '@/lib/tenant-context';
import { TenantImpersonationService } from '@/services/tenant-impersonation';
import { z } from 'zod';

const startImpersonationSchema = z.object({
  tenantId: z.string().uuid('Invalid tenant ID'),
  userId: z.string().uuid('Invalid user ID'),
  reason: z.string().optional()
});

const endImpersonationSchema = z.object({
  sessionId: z.string().uuid('Invalid session ID')
});

/**
 * @swagger
 * /api/admin/impersonation:
 *   get:
 *     summary: Get impersonation history (Super Admin only)
 *     description: Retrieve impersonation session history with filtering options
 *     tags:
 *       - Super Admin - Impersonation
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 20
 *         description: Items per page
 *       - in: query
 *         name: superAdminId
 *         schema:
 *           type: string
 *         description: Filter by super admin ID
 *       - in: query
 *         name: tenantId
 *         schema:
 *           type: string
 *         description: Filter by tenant ID
 *       - in: query
 *         name: userId
 *         schema:
 *           type: string
 *         description: Filter by user ID
 *     responses:
 *       200:
 *         description: Impersonation history retrieved successfully
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Super Admin access required
 */
export const GET = withSuperAdminAuth(async (request: NextRequest) => {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const superAdminId = searchParams.get('superAdminId') || undefined;
    const tenantId = searchParams.get('tenantId') || undefined;
    const userId = searchParams.get('userId') || undefined;

    const impersonationService = new TenantImpersonationService();
    const result = await impersonationService.getImpersonationHistory(
      page,
      limit,
      {
        superAdminId,
        tenantId,
        userId
      }
    );

    return NextResponse.json({
      success: true,
      data: result
    });

  } catch (error) {
    console.error('Get impersonation history error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
});

/**
 * @swagger
 * /api/admin/impersonation:
 *   post:
 *     summary: Start impersonation session (Super Admin only)
 *     description: Start impersonating a user in a specific tenant
 *     tags:
 *       - Super Admin - Impersonation
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - tenantId
 *               - userId
 *             properties:
 *               tenantId:
 *                 type: string
 *                 description: ID of the tenant
 *               userId:
 *                 type: string
 *                 description: ID of the user to impersonate
 *               reason:
 *                 type: string
 *                 description: Reason for impersonation
 *     responses:
 *       201:
 *         description: Impersonation session started successfully
 *       400:
 *         description: Invalid input data
 *       404:
 *         description: Tenant or user not found
 */
export const POST = withSuperAdminAuth(async (request: NextRequest, context: any) => {
  try {
    const body = await request.json();
    const validatedData = startImpersonationSchema.parse(body);

    // Get super admin ID from context
    const superAdminId = context.user.id;

    // Get client IP and user agent
    const ipAddress = request.headers.get('x-forwarded-for') || 
                     request.headers.get('x-real-ip') || 
                     'unknown';
    const userAgent = request.headers.get('user-agent') || 'unknown';

    const impersonationService = new TenantImpersonationService();
    const session = await impersonationService.startImpersonation({
      superAdminId,
      tenantId: validatedData.tenantId,
      userId: validatedData.userId,
      reason: validatedData.reason,
      ipAddress,
      userAgent
    });

    return NextResponse.json({
      success: true,
      data: session,
      message: 'Impersonation session started successfully'
    }, { status: 201 });

  } catch (error) {
    console.error('Start impersonation error:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid input data', details: error.errors },
        { status: 400 }
      );
    }

    if (error instanceof Error) {
      if (error.message.includes('not found')) {
        return NextResponse.json(
          { error: error.message },
          { status: 404 }
        );
      }
      
      if (error.message.includes('platform administrators')) {
        return NextResponse.json(
          { error: error.message },
          { status: 403 }
        );
      }
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
});

/**
 * @swagger
 * /api/admin/impersonation:
 *   delete:
 *     summary: End impersonation session (Super Admin only)
 *     description: End an active impersonation session
 *     tags:
 *       - Super Admin - Impersonation
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - sessionId
 *             properties:
 *               sessionId:
 *                 type: string
 *                 description: ID of the impersonation session to end
 *     responses:
 *       200:
 *         description: Impersonation session ended successfully
 *       400:
 *         description: Invalid input data
 *       404:
 *         description: Session not found
 */
export const DELETE = withSuperAdminAuth(async (request: NextRequest, context: any) => {
  try {
    const body = await request.json();
    const validatedData = endImpersonationSchema.parse(body);

    // Get super admin ID from context
    const superAdminId = context.user.id;

    const impersonationService = new TenantImpersonationService();
    await impersonationService.endImpersonation(validatedData.sessionId, superAdminId);

    return NextResponse.json({
      success: true,
      message: 'Impersonation session ended successfully'
    });

  } catch (error) {
    console.error('End impersonation error:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid input data', details: error.errors },
        { status: 400 }
      );
    }

    if (error instanceof Error) {
      if (error.message.includes('not found')) {
        return NextResponse.json(
          { error: error.message },
          { status: 404 }
        );
      }
      
      if (error.message.includes('Unauthorized')) {
        return NextResponse.json(
          { error: error.message },
          { status: 403 }
        );
      }
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
});
