# Permission Setup Guide

## 🚨 **Current Status**
The handover pages are currently accessible without permission checks to allow immediate testing. Permission gates have been temporarily disabled with TODO comments.

## 🎯 **Required Action**
To enable proper permission-based access control for handovers and other features, you need to run the permission seeding script.

## 📋 **Step-by-Step Setup**

### 1. **Run Permission Seeding Script**
```bash
# Navigate to project root
cd /path/to/your/crm/project

# Run the permission seeding script
node scripts/seed-permissions.js
```

### 2. **Verify Permissions Were Created**
The script will output something like:
```
🌱 Starting comprehensive permission seeding using centralized constants...

📋 Seeding permissions for tenant: Your Tenant Name
  Creating core permissions from centralized constants...
    Adding contacts permissions...
    Adding companies permissions...
    Adding leads permissions...
    Adding opportunities permissions...
    Adding proposals permissions...
    Adding projects permissions...
    Adding handovers permissions...
    Adding users permissions...
    Adding roles permissions...
    Adding settings permissions...
    Adding ai_features permissions...
    Adding reports permissions...
    Adding calls permissions...
  Adding additional specialized permissions...
  Creating 85 permissions...

✅ Completed permissions for tenant: Your Tenant Name
    Created/Updated: 85, Skipped: 0

🎉 Permission seeding completed successfully using centralized constants!

📊 Summary:
   Total permissions in database: 85
   Core permissions from constants: 65
   Additional specialized permissions: 20
   Expected per tenant: 85
```

### 3. **Re-enable Permission Gates**

After successful permission seeding, you need to uncomment the permission gates in the handover pages:

#### Files to Update:
1. `src/app/(dashboard)/handovers/page.tsx`
2. `src/app/(dashboard)/handovers/new/page.tsx`
3. `src/app/(dashboard)/handovers/[id]/page.tsx`
4. `src/app/(dashboard)/handovers/[id]/edit/page.tsx`
5. `src/app/(dashboard)/handovers/[id]/create-project/page.tsx`
6. `src/app/(dashboard)/handovers/templates/page.tsx`

#### What to Do:
1. **Comment out** the temporary return statement (the one without PermissionGate)
2. **Uncomment** the full PermissionGate implementation
3. **Remove** the TODO comments

#### Example for `handovers/page.tsx`:
```typescript
// Remove this temporary code:
/*
return (
  <PageLayout
    title="Handovers"
    description="Manage sales-to-delivery handover processes"
    actions={actions}
  >
    <HandoverManagement />
  </PageLayout>
);
*/

// Uncomment this proper implementation:
return (
  <PermissionGate
    resource={PermissionResource.HANDOVERS}
    action={PermissionAction.READ}
    fallback={
      <PageLayout>
        <Card>
          <CardContent className="p-8">
            <EmptyState
              icon={<ExclamationTriangleIcon className="w-12 h-12 text-muted-foreground" />}
              title="Access Denied"
              description="You don't have permission to view handovers. Please contact your administrator for access."
              action={{
                label: 'Back to Dashboard',
                onClick: () => window.location.href = '/dashboard',
                variant: 'primary'
              }}
            />
          </CardContent>
        </Card>
      </PageLayout>
    }
  >
    <PageLayout
      title="Handovers"
      description="Manage sales-to-delivery handover processes"
      actions={actions}
    >
      <HandoverManagement />
    </PageLayout>
  </PermissionGate>
);
```

### 4. **Assign Permissions to Users**

After seeding permissions, you need to assign them to user roles:

#### Option A: Through Admin Interface
1. Go to `/settings/roles`
2. Edit existing roles or create new ones
3. Assign handover permissions as needed:
   - `handovers.read` - View handovers
   - `handovers.create` - Create new handovers
   - `handovers.update` - Edit handovers
   - `handovers.delete` - Delete handovers
   - `handovers.manage` - Full handover management

#### Option B: Database Direct Assignment
```sql
-- Example: Give all handover permissions to tenant admin role
INSERT INTO role_permissions (role_id, permission_id)
SELECT r.id, p.id
FROM roles r, permissions p
WHERE r.name = 'Tenant Admin'
  AND p.resource = 'handovers'
  AND r.tenant_id = p.tenant_id;
```

## 🔍 **Verification Steps**

### 1. **Check Database**
```sql
-- Verify handover permissions exist
SELECT * FROM permissions WHERE resource = 'handovers';

-- Check role assignments
SELECT r.name as role_name, p.name as permission_name
FROM roles r
JOIN role_permissions rp ON r.id = rp.role_id
JOIN permissions p ON rp.permission_id = p.id
WHERE p.resource = 'handovers';
```

### 2. **Test Access**
1. Log in as a user with handover permissions
2. Navigate to `/handovers`
3. Should see the handover management interface
4. Try creating, editing, and viewing handovers

### 3. **Test Permission Denial**
1. Log in as a user without handover permissions
2. Navigate to `/handovers`
3. Should see "Access Denied" message with proper fallback

## 🛠️ **Troubleshooting**

### Permission Seeding Failed
- Check database connection
- Ensure tenants exist in the database
- Check for constraint violations in logs

### Still Getting Access Denied
- Verify user has correct role assignments
- Check that role has handover permissions
- Confirm permission names match exactly

### Permission Gates Not Working
- Ensure you uncommented the PermissionGate code
- Check that imports are correct
- Verify PermissionResource.HANDOVERS is defined

## 📚 **Permission Structure**

### Handover Permissions Created:
- `handovers.create` - Create new handovers
- `handovers.read` - View handovers
- `handovers.update` - Edit handovers
- `handovers.delete` - Delete handovers
- `handovers.manage` - Full management access

### Project Permissions Created:
- `projects.create` - Create new projects
- `projects.read` - View projects
- `projects.update` - Edit projects
- `projects.delete` - Delete projects
- `projects.export` - Export project data
- `projects.manage` - Full management access

## ✅ **Next Steps**

1. **Run the seeding script**: `node scripts/seed-permissions.js`
2. **Verify permissions in database**
3. **Assign permissions to appropriate roles**
4. **Re-enable permission gates in handover pages**
5. **Test access with different user roles**

Once completed, the handover system will have proper permission-based access control integrated with the rest of the CRM system.
