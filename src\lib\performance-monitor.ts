import { cacheService, CACHE_PREFIXES } from './redis';

export interface PerformanceMetric {
  name: string;
  value: number;
  timestamp: number;
  metadata?: Record<string, any>;
}

export interface SystemHealth {
  database: {
    status: 'healthy' | 'degraded' | 'down';
    responseTime: number;
    activeConnections: number;
  };
  cache: {
    status: 'healthy' | 'degraded' | 'down';
    hitRate: number;
    memory: string;
    keys: number;
  };
  api: {
    averageResponseTime: number;
    errorRate: number;
    requestsPerMinute: number;
  };
  memory: {
    used: number;
    total: number;
    percentage: number;
  };
}

export class PerformanceMonitor {
  private static instance: PerformanceMonitor;
  private metrics: Map<string, PerformanceMetric[]> = new Map();
  private readonly maxMetricsPerType = 1000;

  static getInstance(): PerformanceMonitor {
    if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor();
    }
    return PerformanceMonitor.instance;
  }

  // Record a performance metric
  recordMetric(name: string, value: number, metadata?: Record<string, any>): void {
    const metric: PerformanceMetric = {
      name,
      value,
      timestamp: Date.now(),
      metadata,
    };

    const metrics = this.metrics.get(name) || [];
    metrics.push(metric);

    // Keep only the latest metrics to prevent memory leaks
    if (metrics.length > this.maxMetricsPerType) {
      metrics.shift();
    }

    this.metrics.set(name, metrics);

    // Track slow operations in development
    if (process.env.NODE_ENV === 'development' && this.isSlowOperation(name, value)) {
      // Slow operation detected - could be logged to monitoring system
    }
  }

  // Get metrics for a specific type
  getMetrics(name: string, limit?: number): PerformanceMetric[] {
    const metrics = this.metrics.get(name) || [];
    return limit ? metrics.slice(-limit) : metrics;
  }

  // Get average value for a metric over a time period
  getAverageMetric(name: string, timeWindowMs: number = 5 * 60 * 1000): number {
    const metrics = this.metrics.get(name) || [];
    const cutoff = Date.now() - timeWindowMs;
    const recentMetrics = metrics.filter(m => m.timestamp > cutoff);
    
    if (recentMetrics.length === 0) return 0;
    
    const sum = recentMetrics.reduce((acc, m) => acc + m.value, 0);
    return sum / recentMetrics.length;
  }

  // Get percentile for a metric
  getPercentile(name: string, percentile: number, timeWindowMs: number = 5 * 60 * 1000): number {
    const metrics = this.metrics.get(name) || [];
    const cutoff = Date.now() - timeWindowMs;
    const recentMetrics = metrics.filter(m => m.timestamp > cutoff);
    
    if (recentMetrics.length === 0) return 0;
    
    const values = recentMetrics.map(m => m.value).sort((a, b) => a - b);
    const index = Math.ceil((percentile / 100) * values.length) - 1;
    return values[Math.max(0, index)];
  }

  // Check if an operation is considered slow
  private isSlowOperation(name: string, value: number): boolean {
    const slowThresholds: Record<string, number> = {
      'database_query': 1000, // 1 second
      'api_request': 2000, // 2 seconds
      'cache_operation': 100, // 100ms
      'middleware_execution': 100, // 100ms
      'page_render': 3000, // 3 seconds
    };

    const threshold = slowThresholds[name] || 1000;
    return value > threshold;
  }

  // Get system health status
  async getSystemHealth(): Promise<SystemHealth> {
    try {
      // Database health check
      const dbStart = Date.now();
      const dbHealthy = await this.checkDatabaseHealth();
      const dbResponseTime = Date.now() - dbStart;

      // Cache health check
      const cacheStats = await cacheService.getStats();
      const cacheHealthy = await cacheService.healthCheck();

      // API metrics
      const apiResponseTime = this.getAverageMetric('api_request', 5 * 60 * 1000);
      const errorRate = this.calculateErrorRate();
      const requestsPerMinute = this.calculateRequestsPerMinute();

      // Memory usage
      const memoryUsage = process.memoryUsage();

      return {
        database: {
          status: dbHealthy ? 'healthy' : 'down',
          responseTime: dbResponseTime,
          activeConnections: 0, // Would need to be implemented based on your DB setup
        },
        cache: {
          status: cacheHealthy ? 'healthy' : 'down',
          hitRate: cacheStats.hitRate,
          memory: cacheStats.memory,
          keys: cacheStats.keys,
        },
        api: {
          averageResponseTime: apiResponseTime,
          errorRate,
          requestsPerMinute,
        },
        memory: {
          used: memoryUsage.heapUsed,
          total: memoryUsage.heapTotal,
          percentage: (memoryUsage.heapUsed / memoryUsage.heapTotal) * 100,
        },
      };
    } catch (error) {
      throw error;
    }
  }

  // Database health check
  private async checkDatabaseHealth(): Promise<boolean> {
    try {
      // This would be implemented based on your database setup
      // For now, we'll assume it's healthy
      return true;
    } catch (error) {
      return false;
    }
  }

  // Calculate error rate from metrics
  private calculateErrorRate(): number {
    const errorMetrics = this.getMetrics('api_error', 100);
    const successMetrics = this.getMetrics('api_success', 100);
    
    const totalRequests = errorMetrics.length + successMetrics.length;
    if (totalRequests === 0) return 0;
    
    return (errorMetrics.length / totalRequests) * 100;
  }

  // Calculate requests per minute
  private calculateRequestsPerMinute(): number {
    const oneMinuteAgo = Date.now() - 60 * 1000;
    const allMetrics = Array.from(this.metrics.values()).flat();
    const recentRequests = allMetrics.filter(m => 
      m.timestamp > oneMinuteAgo && 
      (m.name === 'api_request' || m.name === 'api_error' || m.name === 'api_success')
    );
    
    return recentRequests.length;
  }

  // Get performance summary
  getPerformanceSummary(): {
    slowQueries: number;
    cacheHitRate: number;
    averageResponseTime: number;
    errorRate: number;
    memoryUsage: number;
  } {
    const slowQueries = this.getMetrics('database_query')
      .filter(m => m.value > 1000).length;
    
    return {
      slowQueries,
      cacheHitRate: this.getAverageMetric('cache_hit_rate'),
      averageResponseTime: this.getAverageMetric('api_request'),
      errorRate: this.calculateErrorRate(),
      memoryUsage: (process.memoryUsage().heapUsed / process.memoryUsage().heapTotal) * 100,
    };
  }

  // Clear old metrics to prevent memory leaks
  clearOldMetrics(maxAgeMs: number = 24 * 60 * 60 * 1000): void {
    const cutoff = Date.now() - maxAgeMs;
    
    for (const [name, metrics] of this.metrics.entries()) {
      const filteredMetrics = metrics.filter(m => m.timestamp > cutoff);
      this.metrics.set(name, filteredMetrics);
    }
  }

  // Export metrics for external monitoring systems
  exportMetrics(): Record<string, PerformanceMetric[]> {
    const exported: Record<string, PerformanceMetric[]> = {};
    
    for (const [name, metrics] of this.metrics.entries()) {
      exported[name] = [...metrics]; // Create a copy
    }
    
    return exported;
  }

  // Start automatic cleanup of old metrics
  startCleanupSchedule(): void {
    // Clean up old metrics every hour
    setInterval(() => {
      this.clearOldMetrics();
    }, 60 * 60 * 1000);
  }
}

// Export singleton instance
export const performanceMonitor = PerformanceMonitor.getInstance();

// Helper function to measure execution time
export function measureExecutionTime<T>(
  name: string,
  fn: () => Promise<T>,
  metadata?: Record<string, any>
): Promise<T> {
  return new Promise(async (resolve, reject) => {
    const start = Date.now();
    try {
      const result = await fn();
      const duration = Date.now() - start;
      performanceMonitor.recordMetric(name, duration, metadata);
      resolve(result);
    } catch (error) {
      const duration = Date.now() - start;
      const errorMessage = error instanceof Error ? error.message : String(error);
      performanceMonitor.recordMetric(`${name}_error`, duration, { error: errorMessage, ...metadata });
      reject(error);
    }
  });
}

// Helper function for synchronous operations
export function measureSyncExecutionTime<T>(
  name: string,
  fn: () => T,
  metadata?: Record<string, any>
): T {
  const start = Date.now();
  try {
    const result = fn();
    const duration = Date.now() - start;
    performanceMonitor.recordMetric(name, duration, metadata);
    return result;
  } catch (error) {
    const duration = Date.now() - start;
    const errorMessage = error instanceof Error ? error.message : String(error);
    performanceMonitor.recordMetric(`${name}_error`, duration, { error: errorMessage, ...metadata });
    throw error;
  }
}
