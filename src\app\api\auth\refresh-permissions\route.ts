import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { permissionService } from '@/services/permission-service';
import { prisma } from '@/lib/prisma';

/**
 * POST /api/auth/refresh-permissions
 * Manually refresh user permissions and clear caches
 */
export async function POST(req: NextRequest) {
  try {
    // Get user session
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const userId = session.user.id;
    const isSuperAdmin = session.user.isPlatformAdmin || false;

    // For super admin, just clear their session cache
    if (isSuperAdmin) {
      await permissionService.invalidateUserSession(userId);
      
      return NextResponse.json({
        success: true,
        message: 'Super admin permissions refreshed',
        data: {
          userId,
          userType: 'super_admin'
        }
      });
    }

    // Get user's tenant context
    const tenantUser = await prisma.tenantUser.findFirst({
      where: {
        userId,
        status: 'active'
      },
      include: {
        tenant: true
      }
    });

    if (!tenantUser) {
      return NextResponse.json(
        { error: 'No active tenant found for user' },
        { status: 403 }
      );
    }

    const tenantId = tenantUser.tenantId;

    // Clear all permission caches for this user
    await permissionService.invalidateUserPermissions(userId, tenantId);

    // Get fresh permissions to verify they're working
    const freshPermissions = await permissionService.getUserEffectivePermissions(userId, tenantId);

    return NextResponse.json({
      success: true,
      message: 'User permissions refreshed successfully',
      data: {
        userId,
        tenantId,
        userType: 'tenant_user',
        permissionCount: freshPermissions.effectivePermissions.length,
        roleCount: freshPermissions.roles.length,
        refreshedAt: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('Refresh permissions error:', error);
    return NextResponse.json({
      error: 'Failed to refresh permissions',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

/**
 * GET /api/auth/refresh-permissions
 * Get current permission status and cache info
 */
export async function GET(req: NextRequest) {
  try {
    // Get user session
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const userId = session.user.id;
    const isSuperAdmin = session.user.isPlatformAdmin || false;

    if (isSuperAdmin) {
      return NextResponse.json({
        success: true,
        data: {
          userId,
          userType: 'super_admin',
          hasAllPermissions: true,
          cacheStatus: 'not_applicable'
        }
      });
    }

    // Get user's tenant context
    const tenantUser = await prisma.tenantUser.findFirst({
      where: {
        userId,
        status: 'active'
      }
    });

    if (!tenantUser) {
      return NextResponse.json(
        { error: 'No active tenant found for user' },
        { status: 403 }
      );
    }

    const tenantId = tenantUser.tenantId;

    // Get current permissions
    const permissions = await permissionService.getUserEffectivePermissions(userId, tenantId);

    return NextResponse.json({
      success: true,
      data: {
        userId,
        tenantId,
        userType: 'tenant_user',
        permissionCount: permissions.effectivePermissions.length,
        roleCount: permissions.roles.length,
        roles: permissions.roles.map(role => ({
          id: role.id,
          name: role.name,
          isSystemRole: role.isSystemRole
        })),
        lastFetched: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('Get permission status error:', error);
    return NextResponse.json({
      error: 'Failed to get permission status',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
