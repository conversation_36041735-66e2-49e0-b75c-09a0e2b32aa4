import { NextApiRequest, NextApiResponse } from 'next';

// In-memory rate limiting store (for development)
// In production, you should use Redis or another persistent store
const rateLimitStore = new Map<string, { count: number; resetTime: number }>();

interface RateLimitOptions {
  windowMs?: number; // Time window in milliseconds (default: 15 minutes)
  maxRequests?: number; // Maximum requests per window (default: 100)
  keyGenerator?: (req: NextApiRequest) => string; // Function to generate rate limit key
  skipSuccessfulRequests?: boolean; // Don't count successful requests
  skipFailedRequests?: boolean; // Don't count failed requests
}

/**
 * Rate limiting middleware for Pages API routes
 */
export function withRateLimit(
  handler: (req: NextApiRequest, res: NextApiResponse) => Promise<void>,
  options: RateLimitOptions = {}
) {
  const {
    windowMs = 15 * 60 * 1000, // 15 minutes
    maxRequests = 100,
    keyGenerator = (req) => {
      // Use IP address and user agent as default key
      const ip = req.headers['x-forwarded-for'] || req.headers['x-real-ip'] || req.socket.remoteAddress || 'unknown';
      const userAgent = req.headers['user-agent'] || 'unknown';
      return `${ip}:${userAgent}`;
    },
    skipSuccessfulRequests = false,
    skipFailedRequests = false
  } = options;

  return async function (req: NextApiRequest, res: NextApiResponse) {
    try {
      const key = keyGenerator(req);
      const now = Date.now();
      
      // Clean up expired entries
      const entries = Array.from(rateLimitStore.entries());
      for (const [storeKey, data] of entries) {
        if (data.resetTime <= now) {
          rateLimitStore.delete(storeKey);
        }
      }

      // Get or create rate limit data for this key
      let rateLimitData = rateLimitStore.get(key);
      if (!rateLimitData || rateLimitData.resetTime <= now) {
        rateLimitData = {
          count: 0,
          resetTime: now + windowMs
        };
        rateLimitStore.set(key, rateLimitData);
      }

      // Check if rate limit exceeded
      if (rateLimitData.count >= maxRequests) {
        const retryAfter = Math.ceil((rateLimitData.resetTime - now) / 1000);
        
        res.setHeader('X-RateLimit-Limit', maxRequests.toString());
        res.setHeader('X-RateLimit-Remaining', '0');
        res.setHeader('X-RateLimit-Reset', rateLimitData.resetTime.toString());
        res.setHeader('Retry-After', retryAfter.toString());
        
        return res.status(429).json({
          error: 'Too many requests',
          message: `Rate limit exceeded. Try again in ${retryAfter} seconds.`,
          retryAfter
        });
      }

      // Increment counter before processing request
      if (!skipSuccessfulRequests && !skipFailedRequests) {
        rateLimitData.count++;
      }

      // Set rate limit headers
      const remaining = Math.max(0, maxRequests - rateLimitData.count);
      res.setHeader('X-RateLimit-Limit', maxRequests.toString());
      res.setHeader('X-RateLimit-Remaining', remaining.toString());
      res.setHeader('X-RateLimit-Reset', rateLimitData.resetTime.toString());

      // Store original res.status and res.json to intercept response
      const originalStatus = res.status.bind(res);
      const originalJson = res.json.bind(res);
      let statusCode = 200;

      res.status = function(code: number) {
        statusCode = code;
        return originalStatus(code);
      };

      res.json = function(body: any) {
        // Handle conditional counting based on response status
        if (skipSuccessfulRequests && statusCode >= 200 && statusCode < 400) {
          rateLimitData!.count--;
        } else if (skipFailedRequests && statusCode >= 400) {
          rateLimitData!.count--;
        }
        
        return originalJson(body);
      };

      // Call the actual handler
      await handler(req, res);
      
    } catch (error) {
      console.error('Rate limit middleware error:', error);
      // Don't block the request if rate limiting fails
      await handler(req, res);
    }
  };
}

/**
 * Predefined rate limit configurations
 */
export const rateLimitConfigs = {
  // Strict rate limiting for sensitive endpoints
  strict: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 10
  },
  
  // Standard rate limiting for API endpoints
  standard: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 100
  },
  
  // Lenient rate limiting for public endpoints
  lenient: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 1000
  },
  
  // Per-minute rate limiting for real-time features
  perMinute: {
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 60
  }
};

/**
 * Helper function to create rate limited handler with predefined config
 */
export function createRateLimitedHandler(
  handler: (req: NextApiRequest, res: NextApiResponse) => Promise<void>,
  config: keyof typeof rateLimitConfigs = 'standard'
) {
  return withRateLimit(handler, rateLimitConfigs[config]);
}
