import { NextResponse } from 'next/server';
import { withPermission } from '@/lib/permission-middleware';
import { leadManagementService } from '@/services/lead-management';
import { PermissionAction, PermissionResource } from '@/types';
import { AuthenticatedRequest } from '@/lib/permission-middleware';

/**
 * GET /api/leads/stats
 * Get lead statistics for the current tenant
 */
export const GET = withPermission({
  resource: PermissionResource.LEADS,
  action: PermissionAction.READ,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest) => {
  try {
    const stats = await leadManagementService.getLeadStats(req.tenantId);

    return NextResponse.json({
      success: true,
      data: { stats },
      message: 'Lead statistics retrieved successfully'
    });

  } catch (error) {
    console.error('Get lead stats API error:', error);
    
    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      { error: 'Failed to fetch lead statistics' },
      { status: 500 }
    );
  }
});
