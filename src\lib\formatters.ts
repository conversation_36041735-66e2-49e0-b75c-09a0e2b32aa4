/**
 * Utility functions for formatting various data types
 */

/**
 * Formats a number as currency with proper decimal places and comma separators
 * @param value - The numeric value to format
 * @param currency - The currency code (default: 'USD')
 * @param locale - The locale for formatting (default: 'en-US')
 * @param minimumFractionDigits - Minimum decimal places (default: 2)
 * @param maximumFractionDigits - Maximum decimal places (default: 2)
 * @returns Formatted currency string
 */
export function formatCurrency(
  value: number | string | null | undefined,
  currency: string = 'USD',
  locale: string = 'en-US',
  minimumFractionDigits: number = 2,
  maximumFractionDigits: number = 2
): string {
  // Handle null, undefined, or empty values
  if (value === null || value === undefined || value === '') {
    return '$0.00';
  }

  // Convert string to number if needed
  const numericValue = typeof value === 'string' ? parseFloat(value) : value;

  // Handle invalid numbers
  if (isNaN(numericValue)) {
    return '$0.00';
  }

  try {
    return new Intl.NumberFormat(locale, {
      style: 'currency',
      currency: currency,
      minimumFractionDigits,
      maximumFractionDigits,
    }).format(numericValue);
  } catch (error) {
    // Fallback formatting if Intl.NumberFormat fails
    console.warn('Currency formatting failed, using fallback:', error);
    return `$${numericValue.toLocaleString(locale, {
      minimumFractionDigits,
      maximumFractionDigits,
    })}`;
  }
}

/**
 * Formats a number with comma separators (no currency symbol)
 * @param value - The numeric value to format
 * @param locale - The locale for formatting (default: 'en-US')
 * @param minimumFractionDigits - Minimum decimal places (default: 2)
 * @param maximumFractionDigits - Maximum decimal places (default: 2)
 * @returns Formatted number string with commas
 */
export function formatNumber(
  value: number | string | null | undefined,
  locale: string = 'en-US',
  minimumFractionDigits: number = 2,
  maximumFractionDigits: number = 2
): string {
  // Handle null, undefined, or empty values
  if (value === null || value === undefined || value === '') {
    return '0.00';
  }

  // Convert string to number if needed
  const numericValue = typeof value === 'string' ? parseFloat(value) : value;

  // Handle invalid numbers
  if (isNaN(numericValue)) {
    return '0.00';
  }

  try {
    return new Intl.NumberFormat(locale, {
      minimumFractionDigits,
      maximumFractionDigits,
    }).format(numericValue);
  } catch (error) {
    // Fallback formatting
    console.warn('Number formatting failed, using fallback:', error);
    return numericValue.toLocaleString(locale, {
      minimumFractionDigits,
      maximumFractionDigits,
    });
  }
}

/**
 * Formats a number as a compact currency (e.g., $1.2K, $1.5M)
 * @param value - The numeric value to format
 * @param currency - The currency code (default: 'USD')
 * @param locale - The locale for formatting (default: 'en-US')
 * @returns Compact formatted currency string
 */
export function formatCompactCurrency(
  value: number | string | null | undefined,
  currency: string = 'USD',
  locale: string = 'en-US'
): string {
  // Handle null, undefined, or empty values
  if (value === null || value === undefined || value === '') {
    return '$0';
  }

  // Convert string to number if needed
  const numericValue = typeof value === 'string' ? parseFloat(value) : value;

  // Handle invalid numbers
  if (isNaN(numericValue)) {
    return '$0';
  }

  try {
    return new Intl.NumberFormat(locale, {
      style: 'currency',
      currency: currency,
      notation: 'compact',
      maximumFractionDigits: 1,
    }).format(numericValue);
  } catch (error) {
    // Fallback for compact notation
    console.warn('Compact currency formatting failed, using fallback:', error);
    
    if (numericValue >= 1000000000) {
      return `$${(numericValue / 1000000000).toFixed(1)}B`;
    } else if (numericValue >= 1000000) {
      return `$${(numericValue / 1000000).toFixed(1)}M`;
    } else if (numericValue >= 1000) {
      return `$${(numericValue / 1000).toFixed(1)}K`;
    } else {
      return formatCurrency(numericValue, currency, locale);
    }
  }
}

/**
 * Formats a percentage value
 * @param value - The numeric value to format as percentage
 * @param locale - The locale for formatting (default: 'en-US')
 * @param minimumFractionDigits - Minimum decimal places (default: 1)
 * @param maximumFractionDigits - Maximum decimal places (default: 2)
 * @returns Formatted percentage string
 */
export function formatPercentage(
  value: number | string | null | undefined,
  locale: string = 'en-US',
  minimumFractionDigits: number = 1,
  maximumFractionDigits: number = 2
): string {
  // Handle null, undefined, or empty values
  if (value === null || value === undefined || value === '') {
    return '0%';
  }

  // Convert string to number if needed
  const numericValue = typeof value === 'string' ? parseFloat(value) : value;

  // Handle invalid numbers
  if (isNaN(numericValue)) {
    return '0%';
  }

  try {
    return new Intl.NumberFormat(locale, {
      style: 'percent',
      minimumFractionDigits,
      maximumFractionDigits,
    }).format(numericValue / 100);
  } catch (error) {
    // Fallback formatting
    console.warn('Percentage formatting failed, using fallback:', error);
    return `${numericValue.toFixed(maximumFractionDigits)}%`;
  }
}

/**
 * Parses a currency string and returns the numeric value
 * @param currencyString - The currency string to parse (e.g., "$1,234.56")
 * @returns Numeric value or null if parsing fails
 */
export function parseCurrency(currencyString: string | null | undefined): number | null {
  if (!currencyString || typeof currencyString !== 'string') {
    return null;
  }

  // Remove currency symbols, commas, and spaces
  const cleanedString = currencyString.replace(/[$,\s]/g, '');
  
  // Parse the cleaned string
  const numericValue = parseFloat(cleanedString);
  
  return isNaN(numericValue) ? null : numericValue;
}

/**
 * Validates if a string represents a valid currency amount
 * @param value - The string to validate
 * @returns True if valid currency format
 */
export function isValidCurrency(value: string): boolean {
  if (!value || typeof value !== 'string') {
    return false;
  }

  // Allow currency symbols, digits, commas, and decimal points
  const currencyRegex = /^[$]?[\d,]+\.?\d{0,2}$/;
  const cleanedValue = value.replace(/\s/g, ''); // Remove spaces
  
  return currencyRegex.test(cleanedValue);
}
