import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { opportunityManagementService } from '@/services/opportunity-management';
import { hasPermission } from '@/lib/permissions';
import { PermissionResource, PermissionAction } from '@/types';

/**
 * GET /api/opportunities/analytics
 * Get opportunity analytics and statistics
 */
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user's current tenant
    const currentTenant = session.user.tenants?.[0];
    if (!currentTenant) {
      return NextResponse.json({ error: 'No tenant access' }, { status: 403 });
    }

    // Check permission
    const hasAccess = await hasPermission(
      PermissionResource.OPPORTUNITIES,
      PermissionAction.READ,
      currentTenant.id
    );

    if (!hasAccess) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Parse query parameters for date range
    const { searchParams } = new URL(request.url);
    
    let dateRange: { from: Date; to: Date } | undefined;
    
    const fromDate = searchParams.get('from');
    const toDate = searchParams.get('to');
    
    if (fromDate && toDate) {
      dateRange = {
        from: new Date(fromDate),
        to: new Date(toDate)
      };
    }

    const analytics = await opportunityManagementService.getOpportunityStats(
      currentTenant.id,
      dateRange
    );

    return NextResponse.json(analytics);
  } catch (error) {
    console.error('Error fetching opportunity analytics:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
