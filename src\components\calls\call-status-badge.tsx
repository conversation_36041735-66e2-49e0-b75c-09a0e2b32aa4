'use client';

import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';

export type CallStatus = 'scheduled' | 'completed' | 'cancelled' | 'rescheduled';

interface CallStatusBadgeProps {
  status: CallStatus;
  className?: string;
}

const statusConfig = {
  scheduled: {
    label: 'Scheduled',
    variant: 'default' as const,
    className: 'bg-blue-100 text-blue-800 hover:bg-blue-100',
  },
  completed: {
    label: 'Completed',
    variant: 'default' as const,
    className: 'bg-green-100 text-green-800 hover:bg-green-100',
  },
  cancelled: {
    label: 'Cancelled',
    variant: 'destructive' as const,
    className: 'bg-red-100 text-red-800 hover:bg-red-100',
  },
  rescheduled: {
    label: 'Rescheduled',
    variant: 'secondary' as const,
    className: 'bg-yellow-100 text-yellow-800 hover:bg-yellow-100',
  },
};

export function CallStatusBadge({ status, className }: CallStatusBadgeProps) {
  const config = statusConfig[status];

  return (
    <Badge
      variant={config.variant}
      className={cn(config.className, className)}
    >
      {config.label}
    </Badge>
  );
}
