'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, <PERSON>Content, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  TrendingUp, 
  ArrowRightLeft, 
  Clock, 
  Target,
  Calendar,
  Users
} from 'lucide-react';

interface ConversionStats {
  totalConverted: number;
  conversionsByMonth: Record<string, number>;
  averageTimeToConversion: number;
}

interface ConversionAnalyticsProps {
  className?: string;
}

export function ConversionAnalytics({ className }: ConversionAnalyticsProps) {
  const [stats, setStats] = useState<ConversionStats | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadConversionStats();
  }, []);

  const loadConversionStats = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/leads/stats');
      const data = await response.json();
      
      if (data.success && data.data.stats.conversionStats) {
        setStats(data.data.stats.conversionStats);
      }
    } catch (error) {
      console.error('Error loading conversion stats:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className={`space-y-4 ${className}`}>
        <Card>
          <CardContent className="p-6">
            <div className="animate-pulse space-y-4">
              <div className="h-4 bg-gray-200 rounded w-1/4"></div>
              <div className="h-8 bg-gray-200 rounded w-1/2"></div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!stats) {
    return null;
  }

  // Get last 6 months for trend display
  const last6Months = Object.entries(stats.conversionsByMonth)
    .sort(([a], [b]) => a.localeCompare(b))
    .slice(-6);

  const maxConversions = Math.max(...last6Months.map(([, count]) => count), 1);

  return (
    <div className={`space-y-6 ${className}`}>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* Total Conversions */}
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Conversions</p>
                <p className="text-2xl font-bold text-gray-900">{stats.totalConverted}</p>
              </div>
              <ArrowRightLeft className="w-8 h-8 text-indigo-500" />
            </div>
          </CardContent>
        </Card>

        {/* Average Time to Convert */}
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Avg. Time to Convert</p>
                <p className="text-2xl font-bold text-gray-900">
                  {stats.averageTimeToConversion.toFixed(0)} days
                </p>
              </div>
              <Clock className="w-8 h-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        {/* This Month's Conversions */}
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">This Month</p>
                <p className="text-2xl font-bold text-gray-900">
                  {stats.conversionsByMonth[new Date().toISOString().substring(0, 7)] || 0}
                </p>
              </div>
              <Calendar className="w-8 h-8 text-green-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Conversion Trend */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="w-5 h-5" />
            Conversion Trend (Last 6 Months)
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {last6Months.map(([month, count]) => {
              const percentage = (count / maxConversions) * 100;
              const monthName = new Date(month + '-01').toLocaleDateString('en-US', { 
                month: 'short', 
                year: 'numeric' 
              });
              
              return (
                <div key={month} className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-gray-700">{monthName}</span>
                    <Badge variant="outline">{count} conversions</Badge>
                  </div>
                  <Progress value={percentage} className="h-2" />
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Conversion Insights */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Target className="w-5 h-5" />
            Conversion Insights
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-start gap-3 p-4 bg-blue-50 rounded-lg">
              <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
              <div>
                <p className="font-medium text-blue-900">Conversion Performance</p>
                <p className="text-sm text-blue-700">
                  {stats.totalConverted > 0 
                    ? `You've successfully converted ${stats.totalConverted} leads to opportunities.`
                    : 'No conversions yet. Start converting qualified leads to opportunities.'
                  }
                </p>
              </div>
            </div>

            <div className="flex items-start gap-3 p-4 bg-green-50 rounded-lg">
              <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
              <div>
                <p className="font-medium text-green-900">Time Efficiency</p>
                <p className="text-sm text-green-700">
                  {stats.averageTimeToConversion > 0
                    ? `On average, it takes ${stats.averageTimeToConversion.toFixed(0)} days to convert a lead.`
                    : 'No conversion time data available yet.'
                  }
                </p>
              </div>
            </div>

            {last6Months.length > 1 && (
              <div className="flex items-start gap-3 p-4 bg-purple-50 rounded-lg">
                <div className="w-2 h-2 bg-purple-500 rounded-full mt-2"></div>
                <div>
                  <p className="font-medium text-purple-900">Monthly Trend</p>
                  <p className="text-sm text-purple-700">
                    {(() => {
                      const currentMonth = last6Months[last6Months.length - 1]?.[1] || 0;
                      const previousMonth = last6Months[last6Months.length - 2]?.[1] || 0;
                      const trend = currentMonth - previousMonth;
                      
                      if (trend > 0) {
                        return `Conversions increased by ${trend} compared to last month.`;
                      } else if (trend < 0) {
                        return `Conversions decreased by ${Math.abs(trend)} compared to last month.`;
                      } else {
                        return 'Conversions remained stable compared to last month.';
                      }
                    })()}
                  </p>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
