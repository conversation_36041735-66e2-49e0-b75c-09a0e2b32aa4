'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useRouter } from 'next/navigation';
import { useSession } from 'next-auth/react';
import { ContactWithRelations } from '@/services/contact-management';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { LegacySelect as Select } from '@/components/ui/select';
import { SearchableSelect, SearchableSelectOption } from '@/components/ui/searchable-select';
import { User, Mail, Building, Save, X, Loader2, Users, CreditCard, Sparkles, Plus } from 'lucide-react';
import { BusinessCardUpload } from '@/components/contacts/business-card-upload';
import { BusinessCardAnalysisResponse } from '@/services/business-card-analysis';
import { CompanyCreationDialog } from '@/components/companies/company-creation-dialog';

interface Company {
  id: string;
  name: string;
}

interface UserOption {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
}

interface ModernContactFormProps {
  contact?: ContactWithRelations | null;
  onSave: (contactData: any) => Promise<void>;
  onCancel: () => void;
  loading?: boolean;
  className?: string;
}

export function ModernContactForm({
  contact,
  onSave,
  onCancel,
  loading = false,
  className
}: ModernContactFormProps) {
  const { data: session } = useSession();
  const [formData, setFormData] = useState(() => {
    // Initialize with current user as owner for new contacts
    if (contact) {
      return {
        firstName: contact.firstName || '',
        lastName: contact.lastName || '',
        email: contact.email || '',
        phone: contact.phone || '',
        jobTitle: contact.jobTitle || '',
        website: contact.website || '',
        address: contact.address || '',
        companyId: contact.companyId || '',
        ownerId: contact.ownerId || '',
      };
    }
    return {
      firstName: '',
      lastName: '',
      email: '',
      phone: '',
      jobTitle: '',
      website: '',
      address: '',
      companyId: '',
      ownerId: session?.user?.id || '',
    };
  });

  const [companies, setCompanies] = useState<Company[]>([]);
  const [users, setUsers] = useState<UserOption[]>([]);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showBusinessCardUpload, setShowBusinessCardUpload] = useState(false);
  const [businessCardAnalysis, setBusinessCardAnalysis] = useState<BusinessCardAnalysisResponse | null>(null);
  const [autoPopulatedFields, setAutoPopulatedFields] = useState<Set<string>>(new Set());
  const [suggestedCompany, setSuggestedCompany] = useState<string | null>(null);
  const [showCompanyDialog, setShowCompanyDialog] = useState(false);
  const [companyDialogData, setCompanyDialogData] = useState<{name?: string; website?: string; address?: string} | undefined>();

  // Update form data when contact or session changes
  useEffect(() => {
    if (contact) {
      setFormData({
        firstName: contact.firstName || '',
        lastName: contact.lastName || '',
        email: contact.email || '',
        phone: contact.phone || '',
        jobTitle: contact.jobTitle || '',
        website: contact.website || '',
        address: contact.address || '',
        companyId: contact.companyId || '',
        ownerId: contact.ownerId || '',
      });
    } else if (session?.user?.id) {
      // For new contacts, auto-populate current user as owner when session is available
      setFormData(prev => ({
        ...prev,
        ownerId: session.user.id,
      }));
    }
  }, [contact, session?.user?.id]);

  // Fetch companies and users
  useEffect(() => {
    const fetchData = async () => {
      try {
        const [companiesRes, usersRes] = await Promise.all([
          fetch('/api/companies?limit=100'),
          fetch('/api/users?limit=100')
        ]);

        const companiesData = await companiesRes.json();
        const usersData = await usersRes.json();

        if (companiesData.success) {
          setCompanies(companiesData.data.companies || []);
        }
        if (usersData.success) {
          setUsers(usersData.data || []);
        }
      } catch (err) {
        console.error('Failed to fetch form data:', err);
      }
    };

    fetchData();
  }, []);

  // Helper functions to convert data to SearchableSelectOption format
  const getCompanyOptions = (): SearchableSelectOption[] => {
    const options: SearchableSelectOption[] = [
      { value: '', label: 'Select a company (optional)', description: 'Choose the company this contact works for' }
    ];

    companies.forEach(company => {
      options.push({
        value: company.id,
        label: company.name,
        description: `Company: ${company.name}`
      });
    });

    return options;
  };

  const getUserOptions = (): SearchableSelectOption[] => {
    const options: SearchableSelectOption[] = [
      { value: '', label: 'Select an owner (optional)', description: 'Assign a team member to manage this contact' }
    ];

    users.forEach(user => {
      options.push({
        value: user.id,
        label: `${user.firstName} ${user.lastName}`,
        description: user.email
      });
    });

    return options;
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.firstName.trim()) {
      newErrors.firstName = 'First name is required';
    }

    if (!formData.lastName.trim()) {
      newErrors.lastName = 'Last name is required';
    }

    if (formData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'Please enter a valid email address';
    }

    if (formData.phone && !/^[\+]?[1-9][\d]{0,15}$/.test(formData.phone.replace(/\s/g, ''))) {
      newErrors.phone = 'Please enter a valid phone number';
    }

    if (formData.website && formData.website.trim() && !/^https?:\/\/.+\..+/.test(formData.website.trim())) {
      newErrors.website = 'Please enter a valid website URL (e.g., https://example.com)';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    try {
      await onSave(formData);
    } catch (error) {
      console.error('Form submission error:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const handleBusinessCardAnalysis = (analysis: BusinessCardAnalysisResponse) => {
    setBusinessCardAnalysis(analysis);

    // Auto-populate form fields with extracted data
    console.log('🔄 Auto-populating form fields with analysis:', analysis);

    // Lower confidence threshold and provide user feedback
    const minConfidence = 20; // Lower threshold for better user experience

    if (analysis.confidence >= minConfidence) {
      const updates: Partial<typeof formData> = {};
      let populatedFields: string[] = [];

      // Always populate if we have data, even if fields are not empty (user can review)
      if (analysis.firstName && analysis.firstName.trim()) {
        updates.firstName = analysis.firstName.trim();
        populatedFields.push('First Name');
      }

      if (analysis.lastName && analysis.lastName.trim()) {
        updates.lastName = analysis.lastName.trim();
        populatedFields.push('Last Name');
      }

      if (analysis.email && analysis.email.trim()) {
        updates.email = analysis.email.trim().toLowerCase();
        populatedFields.push('Email');
      }

      if (analysis.phone && analysis.phone.trim()) {
        // Clean and format phone number
        const cleanPhone = analysis.phone.replace(/[^\d+\-\(\)\s]/g, '');
        updates.phone = cleanPhone;
        populatedFields.push('Phone');
      }

      if (analysis.jobTitle && analysis.jobTitle.trim()) {
        updates.jobTitle = analysis.jobTitle.trim();
        populatedFields.push('Job Title');
      }

      if (analysis.website && analysis.website.trim()) {
        updates.website = analysis.website.trim();
        populatedFields.push('Website');
      }

      if (analysis.address && analysis.address.trim()) {
        updates.address = analysis.address.trim();
        populatedFields.push('Address');
      }

      // Enhanced company matching
      if (analysis.company && analysis.company.trim() && companies.length > 0) {
        const companyName = analysis.company.trim();

        // Try exact match first
        let matchingCompany = companies.find(company =>
          company.name.toLowerCase() === companyName.toLowerCase()
        );

        // Try partial match if no exact match
        if (!matchingCompany) {
          matchingCompany = companies.find(company =>
            company.name.toLowerCase().includes(companyName.toLowerCase()) ||
            companyName.toLowerCase().includes(company.name.toLowerCase())
          );
        }

        // Try fuzzy matching for common variations
        if (!matchingCompany) {
          const normalizeCompanyName = (name: string) =>
            name.toLowerCase()
              .replace(/\b(inc|ltd|llc|corp|corporation|company|co)\b\.?/g, '')
              .replace(/[^\w\s]/g, '')
              .trim();

          const normalizedExtracted = normalizeCompanyName(companyName);
          matchingCompany = companies.find(company =>
            normalizeCompanyName(company.name) === normalizedExtracted
          );
        }

        if (matchingCompany) {
          updates.companyId = matchingCompany.id;
          populatedFields.push(`Company (${matchingCompany.name})`);
        } else {
          // Log for potential new company creation with additional details
          console.log('💡 New company detected:', {
            name: companyName,
            website: analysis.website,
            address: analysis.address
          });
        }
      }

      // Apply updates
      if (Object.keys(updates).length > 0) {
        setFormData(prev => ({ ...prev, ...updates }));

        // Track which fields were auto-populated for visual indicators
        const fieldMapping: Record<string, string> = {
          'First Name': 'firstName',
          'Last Name': 'lastName',
          'Email': 'email',
          'Phone': 'phone',
          'Job Title': 'jobTitle',
          'Website': 'website',
          'Address': 'address'
        };

        const autoPopulatedFieldKeys = populatedFields
          .map(field => fieldMapping[field] || field.toLowerCase())
          .filter(Boolean);

        setAutoPopulatedFields(new Set(autoPopulatedFieldKeys));

        console.log('✅ Auto-populated fields:', populatedFields.join(', '));

        // Show user feedback about what was populated
        if (populatedFields.length > 0) {
          console.log(`📝 Populated ${populatedFields.length} fields: ${populatedFields.join(', ')}`);
        }
      } else {
        console.log('ℹ️ No fields were auto-populated');
      }

      // Handle company suggestion if no match found
      if (analysis.company && analysis.company.trim() && !updates.companyId) {
        const companyInfo = {
          name: analysis.company.trim(),
          website: analysis.website,
          address: analysis.address,
          jobTitle: analysis.jobTitle // For context about the contact's role
        };
        setSuggestedCompany(analysis.company.trim());
        console.log('💡 Suggesting new company creation:', companyInfo);
      }
    } else {
      console.log(`⚠️ Confidence too low (${analysis.confidence}%) - skipping auto-population`);
    }
  };

  const handleBusinessCardError = (error: string) => {
    console.error('Business card analysis error:', error);
    // You could show a toast notification here
  };

  const handleCompanyCreated = (newCompany: any) => {
    // Add the new company to the companies list
    setCompanies(prev => [...prev, newCompany]);
    // Auto-select the new company
    setFormData(prev => ({ ...prev, companyId: newCompany.id }));
    // Clear the suggestion
    setSuggestedCompany(null);
    console.log('✅ Company created and selected:', newCompany);
  };

  const openCompanyDialog = (companyData?: {name?: string; website?: string; address?: string}) => {
    // If we have business card analysis, use that data to enhance the company info
    const enhancedCompanyData = companyData ? {
      ...companyData,
      // Add any additional context from business card analysis
      jobTitle: businessCardAnalysis?.jobTitle, // For context about the contact's role
    } : undefined;

    setCompanyDialogData(enhancedCompanyData);
    setShowCompanyDialog(true);
  };

  return (
    <motion.div
      className={`max-w-4xl mx-auto space-y-6 ${className}`}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <form onSubmit={handleSubmit} className="space-y-8">
        {/* Business Card Upload */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CreditCard className="w-5 h-5" />
              Quick Import from Business Card
              <Sparkles className="w-4 h-4 text-yellow-500" />
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-sm text-muted-foreground">
              Upload a business card image and let AI automatically extract contact information to populate the form below.
            </p>

            {!showBusinessCardUpload ? (
              <Button
                type="button"
                variant="outline"
                onClick={() => setShowBusinessCardUpload(true)}
                disabled={isSubmitting}
                className="w-full sm:w-auto"
              >
                <CreditCard className="w-4 h-4 mr-2" />
                Upload Business Card
              </Button>
            ) : (
              <div className="space-y-4">
                <BusinessCardUpload
                  onAnalysisComplete={handleBusinessCardAnalysis}
                  onError={handleBusinessCardError}
                  disabled={isSubmitting}
                />

                {businessCardAnalysis && businessCardAnalysis.confidence > 0 && (
                  <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
                    <div className="flex items-center justify-between mb-3">
                      <h4 className="text-sm font-medium text-blue-900">
                        AI Analysis Results ({businessCardAnalysis.confidence}% confidence)
                      </h4>
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        className="text-xs h-7"
                        onClick={() => {
                          // Re-apply all extracted data
                          handleBusinessCardAnalysis(businessCardAnalysis);
                        }}
                      >
                        <Sparkles className="w-3 h-3 mr-1" />
                        Re-apply All
                      </Button>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
                      {businessCardAnalysis.firstName && businessCardAnalysis.lastName && (
                        <div className="flex items-center justify-between p-2 bg-white rounded border">
                          <span><strong>Name:</strong> {businessCardAnalysis.firstName} {businessCardAnalysis.lastName}</span>
                          {(!autoPopulatedFields.has('firstName') || !autoPopulatedFields.has('lastName')) && (
                            <Button
                              type="button"
                              variant="ghost"
                              size="sm"
                              className="h-6 text-xs"
                              onClick={() => {
                                setFormData(prev => ({
                                  ...prev,
                                  firstName: businessCardAnalysis.firstName || '',
                                  lastName: businessCardAnalysis.lastName || ''
                                }));
                                setAutoPopulatedFields(prev => new Set([...prev, 'firstName', 'lastName']));
                              }}
                            >
                              Apply
                            </Button>
                          )}
                        </div>
                      )}

                      {businessCardAnalysis.email && (
                        <div className="flex items-center justify-between p-2 bg-white rounded border">
                          <span><strong>Email:</strong> {businessCardAnalysis.email}</span>
                          {!autoPopulatedFields.has('email') && (
                            <Button
                              type="button"
                              variant="ghost"
                              size="sm"
                              className="h-6 text-xs"
                              onClick={() => {
                                setFormData(prev => ({ ...prev, email: businessCardAnalysis.email || '' }));
                                setAutoPopulatedFields(prev => new Set([...prev, 'email']));
                              }}
                            >
                              Apply
                            </Button>
                          )}
                        </div>
                      )}

                      {businessCardAnalysis.phone && (
                        <div className="flex items-center justify-between p-2 bg-white rounded border">
                          <span><strong>Phone:</strong> {businessCardAnalysis.phone}</span>
                          {!autoPopulatedFields.has('phone') && (
                            <Button
                              type="button"
                              variant="ghost"
                              size="sm"
                              className="h-6 text-xs"
                              onClick={() => {
                                setFormData(prev => ({ ...prev, phone: businessCardAnalysis.phone || '' }));
                                setAutoPopulatedFields(prev => new Set([...prev, 'phone']));
                              }}
                            >
                              Apply
                            </Button>
                          )}
                        </div>
                      )}

                      {businessCardAnalysis.company && (
                        <div className="flex items-center justify-between p-2 bg-white rounded border">
                          <span><strong>Company:</strong> {businessCardAnalysis.company}</span>
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            className="h-6 text-xs"
                            onClick={() => {
                              // Try to find and set company
                              const matchingCompany = companies.find(company =>
                                company.name.toLowerCase().includes(businessCardAnalysis.company!.toLowerCase()) ||
                                businessCardAnalysis.company!.toLowerCase().includes(company.name.toLowerCase())
                              );
                              if (matchingCompany) {
                                setFormData(prev => ({ ...prev, companyId: matchingCompany.id }));
                              } else {
                                setSuggestedCompany(businessCardAnalysis.company);
                              }
                            }}
                          >
                            {companies.find(company =>
                              company.name.toLowerCase().includes(businessCardAnalysis.company!.toLowerCase()) ||
                              businessCardAnalysis.company!.toLowerCase().includes(company.name.toLowerCase())
                            ) ? 'Apply' : 'Note'}
                          </Button>
                        </div>
                      )}

                      {businessCardAnalysis.jobTitle && (
                        <div className="flex items-center justify-between p-2 bg-white rounded border">
                          <span><strong>Job Title:</strong> {businessCardAnalysis.jobTitle}</span>
                          {!autoPopulatedFields.has('jobTitle') && (
                            <Button
                              type="button"
                              variant="ghost"
                              size="sm"
                              className="h-6 text-xs"
                              onClick={() => {
                                setFormData(prev => ({ ...prev, jobTitle: businessCardAnalysis.jobTitle || '' }));
                                setAutoPopulatedFields(prev => new Set([...prev, 'jobTitle']));
                              }}
                            >
                              Apply
                            </Button>
                          )}
                        </div>
                      )}

                      {businessCardAnalysis.website && (
                        <div className="flex items-center justify-between p-2 bg-white rounded border">
                          <span><strong>Website:</strong> {businessCardAnalysis.website}</span>
                          {!autoPopulatedFields.has('website') && (
                            <Button
                              type="button"
                              variant="ghost"
                              size="sm"
                              className="h-6 text-xs"
                              onClick={() => {
                                setFormData(prev => ({ ...prev, website: businessCardAnalysis.website || '' }));
                                setAutoPopulatedFields(prev => new Set([...prev, 'website']));
                              }}
                            >
                              Apply
                            </Button>
                          )}
                        </div>
                      )}

                      {businessCardAnalysis.address && (
                        <div className="flex items-center justify-between p-2 bg-white rounded border">
                          <span><strong>Address:</strong> {businessCardAnalysis.address}</span>
                          {!autoPopulatedFields.has('address') && (
                            <Button
                              type="button"
                              variant="ghost"
                              size="sm"
                              className="h-6 text-xs"
                              onClick={() => {
                                setFormData(prev => ({ ...prev, address: businessCardAnalysis.address || '' }));
                                setAutoPopulatedFields(prev => new Set([...prev, 'address']));
                              }}
                            >
                              Apply
                            </Button>
                          )}
                        </div>
                      )}
                    </div>

                    <p className="text-xs text-blue-600 mt-3">
                      Fields marked with <Sparkles className="w-3 h-3 inline mx-1" /> were automatically filled.
                      Review and edit the information in the form below before saving.
                    </p>
                  </div>
                )}

                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={() => {
                    setShowBusinessCardUpload(false);
                    setBusinessCardAnalysis(null);
                  }}
                  disabled={isSubmitting}
                >
                  <X className="w-4 h-4 mr-2" />
                  Hide Business Card Upload
                </Button>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Basic Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="w-5 h-5" />
              Basic Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <label className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 flex items-center gap-2">
                  First Name *
                  {autoPopulatedFields.has('firstName') && (
                    <span className="inline-flex items-center gap-1 px-2 py-1 text-xs bg-green-100 text-green-700 rounded-full">
                      <Sparkles className="w-3 h-3" />
                      AI filled
                    </span>
                  )}
                </label>
                <Input
                  name="firstName"
                  value={formData.firstName}
                  onChange={(e) => {
                    handleInputChange('firstName', e.target.value);
                    // Remove from auto-populated set when user manually edits
                    if (autoPopulatedFields.has('firstName')) {
                      setAutoPopulatedFields(prev => {
                        const newSet = new Set(prev);
                        newSet.delete('firstName');
                        return newSet;
                      });
                    }
                  }}
                  placeholder="Enter first name..."
                  required
                  disabled={isSubmitting}
                  className={autoPopulatedFields.has('firstName') ? 'border-green-300 bg-green-50' : ''}
                />
                {errors.firstName && (
                  <p className="text-sm text-destructive">{errors.firstName}</p>
                )}
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 flex items-center gap-2">
                  Last Name *
                  {autoPopulatedFields.has('lastName') && (
                    <span className="inline-flex items-center gap-1 px-2 py-1 text-xs bg-green-100 text-green-700 rounded-full">
                      <Sparkles className="w-3 h-3" />
                      AI filled
                    </span>
                  )}
                </label>
                <Input
                  name="lastName"
                  value={formData.lastName}
                  onChange={(e) => {
                    handleInputChange('lastName', e.target.value);
                    if (autoPopulatedFields.has('lastName')) {
                      setAutoPopulatedFields(prev => {
                        const newSet = new Set(prev);
                        newSet.delete('lastName');
                        return newSet;
                      });
                    }
                  }}
                  placeholder="Enter last name..."
                  required
                  disabled={isSubmitting}
                  className={autoPopulatedFields.has('lastName') ? 'border-green-300 bg-green-50' : ''}
                />
                {errors.lastName && (
                  <p className="text-sm text-destructive">{errors.lastName}</p>
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Contact Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Mail className="w-5 h-5" />
              Contact Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <label className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 flex items-center gap-2">
                  Email Address
                  {autoPopulatedFields.has('email') && (
                    <span className="inline-flex items-center gap-1 px-2 py-1 text-xs bg-green-100 text-green-700 rounded-full">
                      <Sparkles className="w-3 h-3" />
                      AI filled
                    </span>
                  )}
                </label>
                <Input
                  name="email"
                  type="email"
                  value={formData.email}
                  onChange={(e) => {
                    handleInputChange('email', e.target.value);
                    if (autoPopulatedFields.has('email')) {
                      setAutoPopulatedFields(prev => {
                        const newSet = new Set(prev);
                        newSet.delete('email');
                        return newSet;
                      });
                    }
                  }}
                  placeholder="Enter email address..."
                  disabled={isSubmitting}
                  className={autoPopulatedFields.has('email') ? 'border-green-300 bg-green-50' : ''}
                />
                {errors.email && (
                  <p className="text-sm text-destructive">{errors.email}</p>
                )}
                <p className="text-sm text-muted-foreground">
                  Primary email address for communication
                </p>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 flex items-center gap-2">
                  Phone Number
                  {autoPopulatedFields.has('phone') && (
                    <span className="inline-flex items-center gap-1 px-2 py-1 text-xs bg-green-100 text-green-700 rounded-full">
                      <Sparkles className="w-3 h-3" />
                      AI filled
                    </span>
                  )}
                </label>
                <Input
                  name="phone"
                  type="tel"
                  value={formData.phone}
                  onChange={(e) => {
                    handleInputChange('phone', e.target.value);
                    if (autoPopulatedFields.has('phone')) {
                      setAutoPopulatedFields(prev => {
                        const newSet = new Set(prev);
                        newSet.delete('phone');
                        return newSet;
                      });
                    }
                  }}
                  placeholder="Enter phone number..."
                  disabled={isSubmitting}
                  className={autoPopulatedFields.has('phone') ? 'border-green-300 bg-green-50' : ''}
                />
                {errors.phone && (
                  <p className="text-sm text-destructive">{errors.phone}</p>
                )}
                <p className="text-sm text-muted-foreground">
                  Primary phone number for contact
                </p>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 flex items-center gap-2">
                  Job Title
                  {autoPopulatedFields.has('jobTitle') && (
                    <span className="inline-flex items-center gap-1 px-2 py-1 text-xs bg-green-100 text-green-700 rounded-full">
                      <Sparkles className="w-3 h-3" />
                      AI filled
                    </span>
                  )}
                </label>
                <Input
                  name="jobTitle"
                  value={formData.jobTitle}
                  onChange={(e) => {
                    handleInputChange('jobTitle', e.target.value);
                    if (autoPopulatedFields.has('jobTitle')) {
                      setAutoPopulatedFields(prev => {
                        const newSet = new Set(prev);
                        newSet.delete('jobTitle');
                        return newSet;
                      });
                    }
                  }}
                  placeholder="Enter job title..."
                  disabled={isSubmitting}
                  className={autoPopulatedFields.has('jobTitle') ? 'border-green-300 bg-green-50' : ''}
                />
                <p className="text-sm text-muted-foreground">
                  Contact's position or role
                </p>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 flex items-center gap-2">
                  Website
                  {autoPopulatedFields.has('website') && (
                    <span className="inline-flex items-center gap-1 px-2 py-1 text-xs bg-green-100 text-green-700 rounded-full">
                      <Sparkles className="w-3 h-3" />
                      AI filled
                    </span>
                  )}
                </label>
                <Input
                  name="website"
                  type="url"
                  value={formData.website}
                  onChange={(e) => {
                    handleInputChange('website', e.target.value);
                    if (autoPopulatedFields.has('website')) {
                      setAutoPopulatedFields(prev => {
                        const newSet = new Set(prev);
                        newSet.delete('website');
                        return newSet;
                      });
                    }
                  }}
                  placeholder="https://example.com"
                  disabled={isSubmitting}
                  className={autoPopulatedFields.has('website') ? 'border-green-300 bg-green-50' : ''}
                />
                <p className="text-sm text-muted-foreground">
                  Personal or professional website
                </p>
              </div>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 flex items-center gap-2">
                Address
                {autoPopulatedFields.has('address') && (
                  <span className="inline-flex items-center gap-1 px-2 py-1 text-xs bg-green-100 text-green-700 rounded-full">
                    <Sparkles className="w-3 h-3" />
                    AI filled
                  </span>
                )}
              </label>
              <Input
                name="address"
                value={formData.address}
                onChange={(e) => {
                  handleInputChange('address', e.target.value);
                  if (autoPopulatedFields.has('address')) {
                    setAutoPopulatedFields(prev => {
                      const newSet = new Set(prev);
                      newSet.delete('address');
                      return newSet;
                    });
                  }
                }}
                placeholder="Enter full address..."
                disabled={isSubmitting}
                className={autoPopulatedFields.has('address') ? 'border-green-300 bg-green-50' : ''}
              />
              <p className="text-sm text-muted-foreground">
                Physical address or location
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Organization & Assignment */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="w-5 h-5" />
              Organization & Assignment
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <label className="text-sm font-medium">Company</label>
                  <div className="relative">
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      className={`h-7 w-7 p-0 rounded-full border-dashed border-2 hover:border-solid hover:bg-primary hover:text-primary-foreground transition-all duration-200 ${
                        businessCardAnalysis?.company ? 'border-green-400 bg-green-50 hover:bg-green-500' : ''
                      }`}
                      onClick={() => {
                        // Pass extracted company data if available
                        const extractedData = businessCardAnalysis ? {
                          name: businessCardAnalysis.company || '',
                          website: businessCardAnalysis.website || '',
                          address: businessCardAnalysis.address || ''
                        } : undefined;
                        openCompanyDialog(extractedData);
                      }}
                      disabled={isSubmitting}
                      title={businessCardAnalysis?.company
                        ? `Create new company: ${businessCardAnalysis.company}`
                        : "Create new company"
                      }
                    >
                      <Plus className="w-3.5 h-3.5" />
                    </Button>
                    {businessCardAnalysis?.company && (
                      <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-white">
                        <Sparkles className="w-2 h-2 text-white absolute top-0.5 left-0.5" />
                      </div>
                    )}
                  </div>
                </div>
                <SearchableSelect
                  options={getCompanyOptions()}
                  value={formData.companyId}
                  onValueChange={(value) => handleInputChange('companyId', value)}
                  placeholder="Select a company (optional)"
                  searchPlaceholder="Search companies..."
                  emptyMessage="No companies found"
                  allowClear={true}
                  clearLabel="Clear company"
                  disabled={isSubmitting}
                />
                <p className="text-xs text-muted-foreground">Associate this contact with a company</p>

                {/* Suggested company creation */}
                {suggestedCompany && !formData.companyId && businessCardAnalysis && (
                  <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                    <div className="flex items-start gap-2">
                      <Building className="w-4 h-4 text-yellow-600 mt-0.5" />
                      <div className="flex-1">
                        <p className="text-sm font-medium text-yellow-800">
                          Company not found: "{suggestedCompany}"
                        </p>
                        <div className="text-xs text-yellow-700 mt-1 space-y-1">
                          <p>This company doesn't exist in your system yet.</p>
                          {businessCardAnalysis.website && (
                            <p><strong>Website:</strong> {businessCardAnalysis.website}</p>
                          )}
                          {businessCardAnalysis.address && (
                            <p><strong>Address:</strong> {businessCardAnalysis.address}</p>
                          )}
                          {businessCardAnalysis.jobTitle && (
                            <p><strong>Contact's Role:</strong> {businessCardAnalysis.jobTitle}</p>
                          )}
                        </div>
                        <div className="flex gap-2 mt-2">
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            className="text-xs h-7"
                            onClick={() => {
                              openCompanyDialog({
                                name: suggestedCompany,
                                website: businessCardAnalysis.website,
                                address: businessCardAnalysis.address
                              });
                            }}
                          >
                            <Building className="w-3 h-3 mr-1" />
                            Create Company
                          </Button>
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            className="text-xs h-7"
                            onClick={() => setSuggestedCompany(null)}
                          >
                            Skip for now
                          </Button>
                        </div>
                      </div>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="h-6 w-6 p-0"
                        onClick={() => setSuggestedCompany(null)}
                      >
                        <X className="w-3 h-3" />
                      </Button>
                    </div>
                  </div>
                )}
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Owner</label>
                <SearchableSelect
                  options={getUserOptions()}
                  value={formData.ownerId}
                  onValueChange={(value) => handleInputChange('ownerId', value)}
                  placeholder="Select an owner (optional)"
                  searchPlaceholder="Search users..."
                  emptyMessage="No users found"
                  allowClear={true}
                  clearLabel="Clear owner"
                  disabled={isSubmitting}
                />
                <p className="text-xs text-muted-foreground">Assign a team member to manage this contact</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Form Actions */}
        <div className="flex flex-col sm:flex-row gap-3 sm:justify-end pt-6">
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
            disabled={isSubmitting}
            className="sm:w-auto w-full"
          >
            <X className="w-4 h-4 mr-2" />
            Cancel
          </Button>
          <Button
            type="submit"
            variant="default"
            disabled={isSubmitting}
            className="sm:w-auto w-full"
          >
            {isSubmitting && <Loader2 className="w-4 h-4 mr-2 animate-spin" />}
            {!isSubmitting && <Save className="w-4 h-4 mr-2" />}
            {contact ? 'Update Contact' : 'Create Contact'}
          </Button>
        </div>
      </form>

      {/* Company Creation Dialog */}
      <CompanyCreationDialog
        open={showCompanyDialog}
        onOpenChange={setShowCompanyDialog}
        onCompanyCreated={handleCompanyCreated}
        initialData={companyDialogData}
      />
    </motion.div>
  );
}
