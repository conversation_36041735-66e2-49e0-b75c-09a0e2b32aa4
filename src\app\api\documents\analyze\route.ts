import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { aiDocumentAnalysisService } from '@/services/ai-document-analysis';
import { hasPermission } from '@/lib/permissions';
import { PermissionResource, PermissionAction } from '@/types';
import { z } from 'zod';

const analyzeRequestSchema = z.object({
  relatedToType: z.string().min(1),
  relatedToId: z.string().min(1),
  opportunityTitle: z.string().optional(),
  companyName: z.string().optional(),
  language: z.enum(['english', 'arabic']).default('english'),
});

/**
 * POST /api/documents/analyze
 * Analyze a document using AI to generate title and description suggestions
 */
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user's current tenant
    const currentTenant = session.user.tenants?.[0];
    if (!currentTenant) {
      return NextResponse.json({ error: 'No tenant access' }, { status: 403 });
    }

    // Check permissions
    const hasAccess = await hasPermission(
      PermissionResource.DOCUMENTS,
      PermissionAction.CREATE,
      currentTenant.id
    );

    if (!hasAccess) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Parse form data
    const formData = await request.formData();
    const file = formData.get('file') as File;
    
    if (!file) {
      return NextResponse.json(
        { error: 'No file provided' },
        { status: 400 }
      );
    }

    // Validate file size (max 50MB for analysis)
    const maxSize = 50 * 1024 * 1024; // 50MB
    if (file.size > maxSize) {
      return NextResponse.json(
        { error: 'File too large for analysis. Maximum size is 50MB.' },
        { status: 400 }
      );
    }

    // Validate file type (only allow common document types)
    const allowedTypes = [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/vnd.ms-powerpoint',
      'application/vnd.openxmlformats-officedocument.presentationml.presentation',
      'text/plain',
      'text/csv',
      'image/jpeg',
      'image/png',
      'image/gif'
    ];

    if (!allowedTypes.includes(file.type)) {
      return NextResponse.json(
        { error: 'File type not supported for AI analysis' },
        { status: 400 }
      );
    }

    // Parse context data
    const contextData = {
      relatedToType: formData.get('relatedToType') as string,
      relatedToId: formData.get('relatedToId') as string,
      opportunityTitle: formData.get('opportunityTitle') as string || undefined,
      companyName: formData.get('companyName') as string || undefined,
      language: formData.get('language') as string || 'english',
    };

    // Validate context data
    const validatedContext = analyzeRequestSchema.parse(contextData);

    // Perform AI analysis
    const analysis = await aiDocumentAnalysisService.analyzeDocument({
      file,
      tenantId: currentTenant.id,
      userId: session.user.id,
      language: validatedContext.language,
      context: validatedContext
    });

    return NextResponse.json({
      success: true,
      data: analysis,
      message: 'Document analyzed successfully'
    });

  } catch (error) {
    console.error('Document analysis error:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          error: 'Invalid request data',
          details: error.errors.map(e => ({
            field: e.path.join('.'),
            message: e.message
          }))
        },
        { status: 400 }
      );
    }

    if (error instanceof Error) {
      // Check for specific error types
      if (error.message.includes('API key')) {
        return NextResponse.json(
          { error: 'AI service not configured' },
          { status: 503 }
        );
      }

      if (error.message.includes('file processing failed')) {
        return NextResponse.json(
          { error: 'Failed to process file for analysis' },
          { status: 422 }
        );
      }

      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * GET /api/documents/analyze
 * Get information about document analysis capabilities
 */
export async function GET() {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if AI service is available
    const isAvailable = !!process.env.GOOGLE_AI_API_KEY;

    return NextResponse.json({
      success: true,
      data: {
        available: isAvailable,
        supportedFileTypes: [
          'application/pdf',
          'application/msword',
          'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
          'application/vnd.ms-excel',
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          'application/vnd.ms-powerpoint',
          'application/vnd.openxmlformats-officedocument.presentationml.presentation',
          'text/plain',
          'text/csv',
          'image/jpeg',
          'image/png',
          'image/gif'
        ],
        maxFileSize: 50 * 1024 * 1024, // 50MB
        features: [
          'Automatic title generation',
          'Content-based description',
          'Document type classification',
          'Key topic extraction',
          'Content summarization'
        ]
      }
    });

  } catch (error) {
    console.error('Error getting analysis info:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
