'use client';

import React, { useState } from 'react';
import {
  XMarkIcon,
  UserPlusIcon,
  PencilIcon,
  TrashIcon,
  ArrowDownTrayIcon
} from '@heroicons/react/24/outline';

interface BulkActionsModalProps {
  onClose: () => void;
  onActionComplete: () => void;
}

export function BulkActionsModal({ onClose, onActionComplete }: BulkActionsModalProps) {
  const [selectedAction, setSelectedAction] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);

  const actions = [
    {
      id: 'invite',
      name: 'Bulk Invite Users',
      description: 'Invite multiple users to teams at once',
      icon: UserPlusIcon,
      color: 'text-green-600'
    },
    {
      id: 'update',
      name: 'Bulk Update Users',
      description: 'Update multiple users\' team assignments',
      icon: PencilIcon,
      color: 'text-blue-600'
    },
    {
      id: 'remove',
      name: 'Bulk Remove Users',
      description: 'Remove multiple users from teams',
      icon: TrashIcon,
      color: 'text-red-600'
    },
    {
      id: 'export',
      name: 'Export Team Data',
      description: 'Export team and member data',
      icon: ArrowDownTrayIcon,
      color: 'text-gray-600'
    }
  ];

  const handleActionSelect = (actionId: string) => {
    setSelectedAction(actionId);
    // For now, just close the modal
    // In a full implementation, this would open specific forms for each action
    onClose();
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg w-full max-w-md p-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-semibold text-gray-900">Bulk Actions</h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 p-1"
          >
            <XMarkIcon className="w-6 h-6" />
          </button>
        </div>

        {/* Actions List */}
        <div className="space-y-3">
          {actions.map((action) => (
            <button
              key={action.id}
              onClick={() => handleActionSelect(action.id)}
              className="w-full text-left p-4 border border-gray-200 rounded-lg hover:bg-gray-50 hover:border-gray-300 transition-colors"
            >
              <div className="flex items-start space-x-3">
                <action.icon className={`w-6 h-6 ${action.color} flex-shrink-0 mt-0.5`} />
                <div>
                  <h4 className="font-medium text-gray-900">{action.name}</h4>
                  <p className="text-sm text-gray-500 mt-1">{action.description}</p>
                </div>
              </div>
            </button>
          ))}
        </div>

        {/* Footer */}
        <div className="mt-6 pt-4 border-t border-gray-200">
          <p className="text-xs text-gray-500 text-center">
            Select an action to perform bulk operations on teams and users
          </p>
        </div>
      </div>
    </div>
  );
}
