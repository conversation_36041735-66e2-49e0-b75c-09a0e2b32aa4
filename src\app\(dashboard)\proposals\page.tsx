'use client';

import { ProposalManagement } from '@/components/proposals/proposal-management';
import { PermissionGate } from '@/components/ui/permission-gate';
import { PermissionResource, PermissionAction } from '@/types';
import { Card, CardContent } from '@/components/ui/card';
import { EmptyState } from '@/components/ui/empty-state';
import { ExclamationTriangleIcon, PlusIcon, ArrowDownTrayIcon } from '@heroicons/react/24/outline';
import { PageLayout } from '@/components/layout/page-layout';
import { Button } from '@/components/ui/button';
import { useRouter } from 'next/navigation';
import { useSmoothNavigation } from '@/hooks/use-smooth-navigation';

export default function ProposalsPage() {
  const router = useRouter();
  const { navigate } = useSmoothNavigation();

  // Actions for the page header
  const actions = (
    <>
      <Button
        variant="outline"
        onClick={() => navigate('/opportunities')}
      >
        <ArrowDownTrayIcon className="h-4 w-4 mr-2" />
        View Opportunities
      </Button>
      <Button
        variant="outline"
        onClick={() => navigate('/proposals/templates')}
      >
        <PlusIcon className="h-4 w-4 mr-2" />
        Manage Templates
      </Button>
      <Button onClick={() => navigate('/proposals/content-sections')}>
        <PlusIcon className="h-4 w-4 mr-2" />
        Manage Content Sections
      </Button>
    </>
  );

  return (
    <PermissionGate
      resource={PermissionResource.PROPOSALS}
      action={PermissionAction.READ}
      fallback={
        <PageLayout>
          <Card>
            <CardContent className="p-8">
              <EmptyState
                icon={<ExclamationTriangleIcon className="w-12 h-12 text-muted-foreground" />}
                title="Access Denied"
                description="You don't have permission to view proposals. Please contact your administrator for access."
                action={{
                  label: 'Back to Dashboard',
                  onClick: () => navigate('/dashboard'),
                  variant: 'primary'
                }}
              />
            </CardContent>
          </Card>
        </PageLayout>
      }
    >
      <PageLayout
        title="Proposal Management"
        description="Manage and track your AI-generated proposals and content"
        actions={actions}
      >
        <ProposalManagement />
      </PageLayout>
    </PermissionGate>
  );
}
