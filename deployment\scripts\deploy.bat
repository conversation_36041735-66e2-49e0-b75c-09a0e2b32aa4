@echo off
REM Windows batch version of deploy.sh for CRM Platform

setlocal enabledelayedexpansion

REM Configuration
set "ENVIRONMENT=%~1"
if "%ENVIRONMENT%"=="" set "ENVIRONMENT=dev"

REM Colors for terminal output
set "RED=[91m"
set "GREEN=[92m"
set "YELLOW=[93m"
set "BLUE=[94m"
set "NC=[0m"

REM Function to display usage information
:usage
if "%~1"=="show" (
    echo Usage: %0 [environment] [options]
    echo.
    echo Environments:
    echo   dev         Development environment ^(default^)
    echo   staging     Staging environment
    echo   prod        Production environment
    echo.
    echo Options:
    echo   --build       Force rebuild of Docker images
    echo   --no-build    Skip building Docker images
    echo   --pull        Pull latest Docker images
    echo   --no-pull     Skip pulling Docker images
    echo   --migrate     Run database migrations
    echo   --no-migrate  Skip database migrations
    echo   --help        Display this help message
    echo.
    echo Examples:
    echo   %0 dev          Deploy to development environment
    echo   %0 staging      Deploy to staging environment
    echo   %0 prod --pull  Deploy to production with latest images
    exit /b 0
)

REM Parse command line arguments
set "BUILD=false"
set "PULL=false"
set "MIGRATE=false"

:parse_args
if "%~1"=="" goto :end_parse_args
if "%~1"=="dev" (
    set "ENVIRONMENT=dev"
    shift
    goto :parse_args
)
if "%~1"=="staging" (
    set "ENVIRONMENT=staging"
    shift
    goto :parse_args
)
if "%~1"=="prod" (
    set "ENVIRONMENT=prod"
    shift
    goto :parse_args
)
if "%~1"=="--build" (
    set "BUILD=true"
    shift
    goto :parse_args
)
if "%~1"=="--no-build" (
    set "BUILD=false"
    shift
    goto :parse_args
)
if "%~1"=="--pull" (
    set "PULL=true"
    shift
    goto :parse_args
)
if "%~1"=="--no-pull" (
    set "PULL=false"
    shift
    goto :parse_args
)
if "%~1"=="--migrate" (
    set "MIGRATE=true"
    shift
    goto :parse_args
)
if "%~1"=="--no-migrate" (
    set "MIGRATE=false"
    shift
    goto :parse_args
)
if "%~1"=="--help" (
    goto :usage show
)
echo %RED%Unknown option: %~1%NC%
goto :usage show

:end_parse_args

REM Setup environment-specific configuration
set "COMPOSE_FILE=docker-compose.yml"
set "COMPOSE_OVERRIDE="
set "ENV_FILE=.env.docker"

if "%ENVIRONMENT%"=="dev" (
    echo %GREEN%Using development environment...%NC%
    set "COMPOSE_FILE=docker-compose.yml"
    set "COMPOSE_OVERRIDE=-f docker-compose.override.yml"
    set "ENV_FILE=.env.docker"
) else if "%ENVIRONMENT%"=="staging" (
    echo %GREEN%Using staging environment...%NC%
    set "COMPOSE_FILE=docker-compose.yml"
    set "COMPOSE_OVERRIDE=-f docker-compose.staging.yml"
    set "ENV_FILE=.env.docker"
) else if "%ENVIRONMENT%"=="prod" (
    echo %GREEN%Using production environment...%NC%
    set "COMPOSE_FILE=docker-compose.yml"
    set "COMPOSE_OVERRIDE=-f docker-compose.prod.yml"
    set "ENV_FILE=.env.docker"
) else (
    echo %RED%Error: Unknown environment '%ENVIRONMENT%'%NC%
    goto :usage show
)

REM Check if Docker is installed
docker --version >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo %RED%Error: Docker is not installed or not in PATH%NC%
    exit /b 1
)

REM Check if Docker Compose is installed
docker-compose --version >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo %RED%Error: Docker Compose is not installed or not in PATH%NC%
    exit /b 1
)

REM Check if environment file exists
if not exist "%ENV_FILE%" (
    echo %RED%Error: Environment file '%ENV_FILE%' not found%NC%
    echo %YELLOW%Create the environment file from the template:%NC%
    echo copy .env.example %ENV_FILE%
    exit /b 1
)

REM Generate SSL certificates for staging and production
if "%ENVIRONMENT%"=="staging" (
    echo %YELLOW%Generating SSL certificates for staging...%NC%
    call scripts\generate-ssl-certs.bat
) else if "%ENVIRONMENT%"=="prod" (
    echo %YELLOW%Generating SSL certificates for production...%NC%
    call scripts\generate-ssl-certs.bat
)

REM Pull latest Docker images if requested
if "%PULL%"=="true" (
    echo %YELLOW%Pulling latest Docker images...%NC%
    docker-compose -f %COMPOSE_FILE% %COMPOSE_OVERRIDE% pull
    echo %GREEN%Docker images pulled successfully.%NC%
)

REM Build Docker images if requested or if this is the first deployment
if "%BUILD%"=="true" (
    echo %YELLOW%Building Docker images...%NC%
    docker-compose -f %COMPOSE_FILE% %COMPOSE_OVERRIDE% build
    echo %GREEN%Docker images built successfully.%NC%
)

REM Start Docker containers
echo %YELLOW%Starting Docker containers...%NC%
docker-compose -f %COMPOSE_FILE% %COMPOSE_OVERRIDE% up -d
echo %GREEN%Docker containers started successfully.%NC%

REM Run database migrations if requested
if "%MIGRATE%"=="true" (
    echo %YELLOW%Running database migrations...%NC%
    docker-compose -f %COMPOSE_FILE% %COMPOSE_OVERRIDE% exec app npx prisma migrate deploy
    echo %GREEN%Database migrations completed successfully.%NC%
)

REM Display access URLs
echo.
echo %GREEN%Deployment completed successfully!%NC%
echo.
echo %YELLOW%Access URLs:%NC%
if "%ENVIRONMENT%"=="dev" (
    echo Application: http://localhost:3000
    echo Prisma Studio: http://localhost:5555
) else if "%ENVIRONMENT%"=="staging" (
    echo Application: https://staging.crm-platform.com
) else if "%ENVIRONMENT%"=="prod" (
    echo Application: https://crm-platform.com
)
echo.
echo %YELLOW%Container Status:%NC%
docker-compose -f %COMPOSE_FILE% %COMPOSE_OVERRIDE% ps

endlocal
