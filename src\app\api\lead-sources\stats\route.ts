import { NextResponse } from 'next/server';
import { withPermission } from '@/lib/permission-middleware';
import { leadSourceManagementService } from '@/services/lead-source-management';
import { PermissionAction, PermissionResource } from '@/types';
import { AuthenticatedRequest } from '@/lib/permission-middleware';

/**
 * GET /api/lead-sources/stats
 * Get lead source statistics
 */
export const GET = withPermission({
  resource: PermissionResource.LEADS,
  action: PermissionAction.READ,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest) => {
  try {
    const stats = await leadSourceManagementService.getLeadSourceStats(req.tenantId);

    return NextResponse.json({
      success: true,
      data: stats,
      message: 'Lead source statistics retrieved successfully'
    });
  } catch (error) {
    console.error('Error fetching lead source stats:', error);
    
    if (error instanceof Error) {
      return NextResponse.json(
        { 
          success: false, 
          error: error.message,
          details: 'Failed to fetch lead source statistics'
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error',
        details: 'An unexpected error occurred while fetching lead source statistics'
      },
      { status: 500 }
    );
  }
});
