import { NextRequest, NextResponse } from 'next/server';
import { ProjectManagementService } from '@/services/project-management';
import { withPermission, AuthenticatedRequest } from '@/lib/permission-middleware';
import { PermissionResource, PermissionAction } from '@/types';

const projectService = new ProjectManagementService();

/**
 * GET /api/projects/stats
 * Get project statistics for the current tenant
 */
export const GET = withPermission({
  resource: PermissionResource.PROJECTS,
  action: PermissionAction.READ,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest) => {
  try {
    const stats = await projectService.getProjectStats(req.tenantId);

    return NextResponse.json({
      success: true,
      data: { stats },
      message: 'Project statistics retrieved successfully'
    });
  } catch (error) {
    console.error('Get project stats error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch project statistics' },
      { status: 500 }
    );
  }
});
