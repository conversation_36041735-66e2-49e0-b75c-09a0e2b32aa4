import { z } from 'zod';
import { prisma } from '@/lib/prisma';

// Validation schemas
export const createLeadSourceSchema = z.object({
  name: z.string().min(1, 'Name is required').max(100, 'Name too long'),
  description: z.string().optional(),
  category: z.enum([
    'digital',
    'outbound', 
    'paid_advertising',
    'events',
    'partnerships',
    'traditional',
    'word_of_mouth',
    'existing_relationships',
    'custom',
    'other'
  ]).default('custom'),
  icon: z.string().optional(),
  color: z.string().optional(),
  isActive: z.boolean().default(true),
  sortOrder: z.number().int().min(0).default(0)
});

export const updateLeadSourceSchema = createLeadSourceSchema.partial();

export const getLeadSourcesQuerySchema = z.object({
  page: z.string().optional().transform((val) => val ? parseInt(val, 10) : 1),
  limit: z.string().optional().transform((val) => val ? parseInt(val, 10) : 50),
  search: z.string().optional(),
  category: z.string().optional(),
  isActive: z.string().optional().transform((val) => val === 'true' ? true : val === 'false' ? false : undefined),
  sortBy: z.enum(['name', 'category', 'createdAt', 'updatedAt', 'sortOrder']).default('sortOrder'),
  sortOrder: z.enum(['asc', 'desc']).default('asc')
});

export type CreateLeadSourceData = z.infer<typeof createLeadSourceSchema>;
export type UpdateLeadSourceData = z.infer<typeof updateLeadSourceSchema>;
export type GetLeadSourcesQuery = z.infer<typeof getLeadSourcesQuerySchema>;

type UserSummary = {
  id: string;
  firstName: string | null;
  lastName: string | null;
  email: string;
  avatarUrl: string | null;
};

export interface LeadSourceWithRelations {
  id: string;
  tenantId: string;
  name: string;
  description: string | null;
  category: string;
  icon: string | null;
  color: string | null;
  isActive: boolean;
  sortOrder: number;
  createdById: string | null;
  createdAt: Date;
  updatedAt: Date;
  createdBy?: UserSummary | null;
  _count?: {
    leads: number;
  };
}

export interface LeadSourceStats {
  total: number;
  active: number;
  inactive: number;
  byCategory: Record<string, number>;
  totalLeads: number;
}

class LeadSourceManagementService {
  /**
   * Get lead sources with filtering and pagination
   */
  async getLeadSources(
    tenantId: string,
    query: GetLeadSourcesQuery
  ): Promise<{
    leadSources: LeadSourceWithRelations[];
    totalCount: number;
    totalPages: number;
    currentPage: number;
  }> {
    const { page, limit, search, category, isActive, sortBy, sortOrder } = query;
    const skip = (page - 1) * limit;

    // Build where clause
    const where: any = {
      tenantId,
      ...(search && {
        OR: [
          { name: { contains: search, mode: 'insensitive' } },
          { description: { contains: search, mode: 'insensitive' } }
        ]
      }),
      ...(category && { category }),
      ...(isActive !== undefined && { isActive })
    };

    // Get total count
    const totalCount = await prisma.leadSource.count({ where });

    // Get lead sources
    const leadSources = await prisma.leadSource.findMany({
      where,
      include: {
        createdBy: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            avatarUrl: true
          }
        },
        _count: {
          select: {
            leads: true
          }
        }
      },
      orderBy: { [sortBy]: sortOrder },
      skip,
      take: limit
    });

    return {
      leadSources,
      totalCount,
      totalPages: Math.ceil(totalCount / limit),
      currentPage: page
    };
  }

  /**
   * Get a single lead source by ID
   */
  async getLeadSourceById(
    leadSourceId: string,
    tenantId: string
  ): Promise<LeadSourceWithRelations | null> {
    return await prisma.leadSource.findFirst({
      where: {
        id: leadSourceId,
        tenantId
      },
      include: {
        createdBy: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            avatarUrl: true
          }
        },
        _count: {
          select: {
            leads: true
          }
        }
      }
    });
  }

  /**
   * Create a new lead source
   */
  async createLeadSource(
    tenantId: string,
    data: CreateLeadSourceData,
    createdById: string | null
  ): Promise<LeadSourceWithRelations> {
    // Check if lead source with same name already exists
    const existingLeadSource = await prisma.leadSource.findFirst({
      where: {
        tenantId,
        name: data.name
      }
    });

    if (existingLeadSource) {
      throw new Error('Lead source with this name already exists');
    }

    const leadSource = await prisma.leadSource.create({
      data: {
        ...data,
        tenantId,
        createdById
      },
      include: {
        createdBy: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            avatarUrl: true
          }
        },
        _count: {
          select: {
            leads: true
          }
        }
      }
    });

    return leadSource;
  }

  /**
   * Update a lead source
   */
  async updateLeadSource(
    leadSourceId: string,
    tenantId: string,
    data: UpdateLeadSourceData
  ): Promise<LeadSourceWithRelations> {
    // Check if lead source exists
    const existingLeadSource = await prisma.leadSource.findFirst({
      where: {
        id: leadSourceId,
        tenantId
      }
    });

    if (!existingLeadSource) {
      throw new Error('Lead source not found');
    }

    // Check if name is being changed and if it conflicts
    if (data.name && data.name !== existingLeadSource.name) {
      const nameConflict = await prisma.leadSource.findFirst({
        where: {
          tenantId,
          name: data.name,
          id: { not: leadSourceId }
        }
      });

      if (nameConflict) {
        throw new Error('Lead source with this name already exists');
      }
    }

    const leadSource = await prisma.leadSource.update({
      where: { id: leadSourceId },
      data,
      include: {
        createdBy: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            avatarUrl: true
          }
        },
        _count: {
          select: {
            leads: true
          }
        }
      }
    });

    return leadSource;
  }

  /**
   * Delete a lead source
   */
  async deleteLeadSource(leadSourceId: string, tenantId: string): Promise<void> {
    // Check if lead source exists
    const existingLeadSource = await prisma.leadSource.findFirst({
      where: {
        id: leadSourceId,
        tenantId
      },
      include: {
        _count: {
          select: {
            leads: true
          }
        }
      }
    });

    if (!existingLeadSource) {
      throw new Error('Lead source not found');
    }

    // Check if lead source is being used by any leads
    if (existingLeadSource._count.leads > 0) {
      throw new Error('Cannot delete lead source that is being used by leads. Please reassign the leads first.');
    }

    await prisma.leadSource.delete({
      where: { id: leadSourceId }
    });
  }

  /**
   * Get lead source statistics
   */
  async getLeadSourceStats(tenantId: string): Promise<LeadSourceStats> {
    const [total, active, inactive, byCategory, totalLeads] = await Promise.all([
      prisma.leadSource.count({ where: { tenantId } }),
      prisma.leadSource.count({ where: { tenantId, isActive: true } }),
      prisma.leadSource.count({ where: { tenantId, isActive: false } }),
      prisma.leadSource.groupBy({
        by: ['category'],
        where: { tenantId },
        _count: { id: true }
      }),
      prisma.lead.count({ 
        where: { 
          tenantId,
          leadSourceId: { not: null }
        } 
      })
    ]);

    const categoryStats = byCategory.reduce((acc, item) => {
      acc[item.category] = item._count.id;
      return acc;
    }, {} as Record<string, number>);

    return {
      total,
      active,
      inactive,
      byCategory: categoryStats,
      totalLeads
    };
  }

  /**
   * Bulk update lead source sort order
   */
  async updateSortOrder(
    tenantId: string,
    updates: Array<{ id: string; sortOrder: number }>
  ): Promise<void> {
    await prisma.$transaction(
      updates.map(({ id, sortOrder }) =>
        prisma.leadSource.update({
          where: { id },
          data: { sortOrder }
        })
      )
    );
  }
}

export const leadSourceManagementService = new LeadSourceManagementService();
