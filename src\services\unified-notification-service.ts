import { prisma } from '@/lib/prisma';
import { z } from 'zod';

// Unified notification interface
export interface UnifiedNotification {
  id: string;
  type: string;
  category: 'general' | 'lead' | 'handover' | 'communication' | 'system';
  title: string;
  message: string;
  data?: Record<string, any>;
  isRead: boolean;
  readAt?: Date | null;
  createdAt: Date;
  actionUrl?: string;
  actionLabel?: string;
  user?: {
    id: string;
    name: string;
    avatar?: string;
  };
  relatedEntity?: {
    id: string;
    type: string;
    name: string;
  };
}

// Query options for fetching notifications
export interface NotificationQueryOptions {
  limit?: number;
  offset?: number;
  unreadOnly?: boolean;
  category?: string;
  type?: string;
  startDate?: Date;
  endDate?: Date;
}

// Validation schemas
export const notificationQuerySchema = z.object({
  limit: z.coerce.number().int().positive().max(100).default(20),
  offset: z.coerce.number().int().min(0).default(0),
  unreadOnly: z.coerce.boolean().default(false),
  category: z.enum(['general', 'lead', 'handover', 'communication', 'system']).optional(),
  type: z.string().optional(),
  startDate: z.coerce.date().optional(),
  endDate: z.coerce.date().optional(),
});

export const markAsReadSchema = z.object({
  notificationIds: z.array(z.string()).optional(),
  markAll: z.boolean().default(false),
  category: z.enum(['general', 'lead', 'handover', 'communication', 'system']).optional(),
});

export const createNotificationSchema = z.object({
  tenantId: z.string(),
  userId: z.string(),
  type: z.string(),
  category: z.enum(['general', 'lead', 'handover', 'communication', 'system']),
  title: z.string(),
  message: z.string(),
  data: z.record(z.any()).optional(),
  actionUrl: z.string().optional(),
  actionLabel: z.string().optional(),
});

export class UnifiedNotificationService {
  /**
   * Create a new notification
   */
  async createNotification(data: z.infer<typeof createNotificationSchema>): Promise<UnifiedNotification> {
    const validatedData = createNotificationSchema.parse(data);

    try {
      let notification: any;

      // Route to appropriate notification table based on category
      switch (validatedData.category) {
        case 'general':
          notification = await prisma.notification.create({
            data: {
              tenantId: validatedData.tenantId,
              userId: validatedData.userId,
              type: validatedData.type,
              title: validatedData.title,
              message: validatedData.message,
              data: {
                ...validatedData.data,
                actionUrl: validatedData.actionUrl,
                actionLabel: validatedData.actionLabel,
              },
              isRead: false,
            },
          });
          break;

        case 'lead':
          notification = await prisma.leadNotification.create({
            data: {
              tenantId: validatedData.tenantId,
              userId: validatedData.userId,
              type: validatedData.type,
              title: validatedData.title,
              message: validatedData.message,
              data: {
                ...validatedData.data,
                actionUrl: validatedData.actionUrl,
                actionLabel: validatedData.actionLabel,
              },
              isRead: false,
              leadId: validatedData.data?.leadId || null,
            },
          });
          break;

        case 'handover':
          notification = await prisma.handoverNotification.create({
            data: {
              tenantId: validatedData.tenantId,
              userId: validatedData.userId,
              type: validatedData.type,
              title: validatedData.title,
              message: validatedData.message,
              data: {
                ...validatedData.data,
                actionUrl: validatedData.actionUrl,
                actionLabel: validatedData.actionLabel,
              },
              isRead: false,
              handoverId: validatedData.data?.handoverId || null,
              actionUrl: validatedData.actionUrl,
              actionLabel: validatedData.actionLabel,
            },
          });
          break;

        case 'communication':
        case 'system':
          // For now, route these to general notifications
          notification = await prisma.notification.create({
            data: {
              tenantId: validatedData.tenantId,
              userId: validatedData.userId,
              type: validatedData.type,
              title: validatedData.title,
              message: validatedData.message,
              data: {
                ...validatedData.data,
                actionUrl: validatedData.actionUrl,
                actionLabel: validatedData.actionLabel,
                category: validatedData.category,
              },
              isRead: false,
            },
          });
          break;

        default:
          throw new Error(`Unsupported notification category: ${validatedData.category}`);
      }

      // Convert to unified format
      const unifiedNotification: UnifiedNotification = {
        id: notification.id,
        type: notification.type,
        category: validatedData.category,
        title: notification.title,
        message: notification.message,
        data: notification.data as Record<string, any>,
        isRead: notification.isRead,
        readAt: notification.readAt,
        createdAt: notification.createdAt,
        actionUrl: validatedData.actionUrl,
        actionLabel: validatedData.actionLabel,
      };

      return unifiedNotification;
    } catch (error) {
      console.error('Error creating notification:', error);
      throw new Error('Failed to create notification');
    }
  }

  /**
   * Create multiple notifications in bulk
   */
  async createBulkNotifications(notifications: z.infer<typeof createNotificationSchema>[]): Promise<UnifiedNotification[]> {
    const results: UnifiedNotification[] = [];

    for (const notificationData of notifications) {
      try {
        const notification = await this.createNotification(notificationData);
        results.push(notification);
      } catch (error) {
        console.error('Error creating bulk notification:', error);
        // Continue with other notifications even if one fails
      }
    }

    return results;
  }

  /**
   * Get all notifications for a user across all types
   */
  async getUserNotifications(
    userId: string,
    tenantId: string,
    options: NotificationQueryOptions = {}
  ): Promise<{
    notifications: UnifiedNotification[];
    total: number;
    unreadCount: number;
  }> {
    const { limit = 20, offset = 0, unreadOnly = false, category, type } = options;

    try {
      const notifications: UnifiedNotification[] = [];

      // Fetch general notifications
      if (!category || category === 'general') {
        const generalNotifications = await prisma.notification.findMany({
          where: {
            userId,
            tenantId,
            ...(unreadOnly && { isRead: false }),
            ...(type && { type }),
          },
          orderBy: { createdAt: 'desc' },
          take: limit,
          skip: offset,
        });

        notifications.push(...generalNotifications.map(n => ({
          id: n.id,
          type: n.type,
          category: 'general' as const,
          title: n.title,
          message: n.message,
          data: n.data as Record<string, any>,
          isRead: n.isRead,
          readAt: n.readAt,
          createdAt: n.createdAt,
          actionUrl: (n.data as any)?.actionUrl,
          actionLabel: (n.data as any)?.actionLabel,
        })));
      }

      // Fetch lead notifications
      if (!category || category === 'lead') {
        const leadNotifications = await prisma.leadNotification.findMany({
          where: {
            userId,
            tenantId,
            ...(unreadOnly && { isRead: false }),
            ...(type && { type }),
          },
          include: {
            lead: {
              select: {
                id: true,
                title: true,
                contact: {
                  select: {
                    firstName: true,
                    lastName: true,
                  }
                }
              }
            }
          },
          orderBy: { createdAt: 'desc' },
          take: limit,
          skip: offset,
        });

        notifications.push(...leadNotifications.map(n => ({
          id: n.id,
          type: n.type,
          category: 'lead' as const,
          title: n.title,
          message: n.message,
          data: n.data as Record<string, any>,
          isRead: n.isRead,
          readAt: null,
          createdAt: n.createdAt,
          actionUrl: `/leads/${n.leadId}`,
          actionLabel: 'View Lead',
          relatedEntity: {
            id: n.lead.id,
            type: 'lead',
            name: n.lead.title || `${n.lead.contact?.firstName} ${n.lead.contact?.lastName}`.trim() || 'Unknown Lead'
          }
        })));
      }

      // Fetch handover notifications
      if (!category || category === 'handover') {
        const handoverNotifications = await prisma.handoverNotification.findMany({
          where: {
            userId,
            tenantId,
            ...(unreadOnly && { isRead: false }),
            ...(type && { type }),
          },
          include: {
            handover: {
              select: {
                id: true,
                title: true,
                opportunity: {
                  select: {
                    title: true,
                  }
                }
              }
            }
          },
          orderBy: { createdAt: 'desc' },
          take: limit,
          skip: offset,
        });

        notifications.push(...handoverNotifications.map(n => ({
          id: n.id,
          type: n.type,
          category: 'handover' as const,
          title: n.title,
          message: n.message,
          data: n.data as Record<string, any>,
          isRead: n.isRead,
          readAt: n.readAt,
          createdAt: n.createdAt,
          actionUrl: n.actionUrl || `/handovers/${n.handoverId}`,
          actionLabel: n.actionLabel || 'View Handover',
          relatedEntity: {
            id: n.handover.id,
            type: 'handover',
            name: n.handover.title || n.handover.opportunity?.title || 'Unknown Handover'
          }
        })));
      }

      // Sort all notifications by creation date
      notifications.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());

      // Apply pagination to the combined results
      const paginatedNotifications = notifications.slice(offset, offset + limit);

      // Get total counts
      const totalCount = await this.getTotalNotificationCount(userId, tenantId, { category, type });
      const unreadCount = await this.getUnreadNotificationCount(userId, tenantId, { category, type });

      return {
        notifications: paginatedNotifications,
        total: totalCount,
        unreadCount,
      };
    } catch (error) {
      console.error('Error fetching unified notifications:', error);
      throw new Error('Failed to fetch notifications');
    }
  }

  /**
   * Get total notification count
   */
  private async getTotalNotificationCount(
    userId: string,
    tenantId: string,
    filters: { category?: string; type?: string } = {}
  ): Promise<number> {
    const { category, type } = filters;
    let total = 0;

    if (!category || category === 'general') {
      total += await prisma.notification.count({
        where: {
          userId,
          tenantId,
          ...(type && { type }),
        },
      });
    }

    if (!category || category === 'lead') {
      total += await prisma.leadNotification.count({
        where: {
          userId,
          tenantId,
          ...(type && { type }),
        },
      });
    }

    if (!category || category === 'handover') {
      total += await prisma.handoverNotification.count({
        where: {
          userId,
          tenantId,
          ...(type && { type }),
        },
      });
    }

    return total;
  }

  /**
   * Get unread notification count
   */
  async getUnreadNotificationCount(
    userId: string,
    tenantId: string,
    filters: { category?: string; type?: string } = {}
  ): Promise<number> {
    const { category, type } = filters;
    let unreadCount = 0;

    if (!category || category === 'general') {
      unreadCount += await prisma.notification.count({
        where: {
          userId,
          tenantId,
          isRead: false,
          ...(type && { type }),
        },
      });
    }

    if (!category || category === 'lead') {
      unreadCount += await prisma.leadNotification.count({
        where: {
          userId,
          tenantId,
          isRead: false,
          ...(type && { type }),
        },
      });
    }

    if (!category || category === 'handover') {
      unreadCount += await prisma.handoverNotification.count({
        where: {
          userId,
          tenantId,
          isRead: false,
          ...(type && { type }),
        },
      });
    }

    return unreadCount;
  }

  /**
   * Mark notifications as read
   */
  async markAsRead(
    userId: string,
    tenantId: string,
    options: {
      notificationIds?: string[];
      markAll?: boolean;
      category?: string;
    } = {}
  ): Promise<{ updated: number }> {
    const { notificationIds, markAll = false, category } = options;
    let updated = 0;

    try {
      if (markAll) {
        // Mark all notifications as read
        if (!category || category === 'general') {
          const result = await prisma.notification.updateMany({
            where: {
              userId,
              tenantId,
              isRead: false,
            },
            data: {
              isRead: true,
              readAt: new Date(),
            },
          });
          updated += result.count;
        }

        if (!category || category === 'lead') {
          const result = await prisma.leadNotification.updateMany({
            where: {
              userId,
              tenantId,
              isRead: false,
            },
            data: {
              isRead: true,
            },
          });
          updated += result.count;
        }

        if (!category || category === 'handover') {
          const result = await prisma.handoverNotification.updateMany({
            where: {
              userId,
              tenantId,
              isRead: false,
            },
            data: {
              isRead: true,
              readAt: new Date(),
            },
          });
          updated += result.count;
        }
      } else if (notificationIds && notificationIds.length > 0) {
        // Mark specific notifications as read
        // Note: This is a simplified approach. In a real implementation,
        // you'd need to determine which table each ID belongs to
        for (const id of notificationIds) {
          // Try each table until we find the notification
          try {
            const generalResult = await prisma.notification.updateMany({
              where: { id, userId, tenantId, isRead: false },
              data: { isRead: true, readAt: new Date() },
            });
            if (generalResult.count > 0) {
              updated += generalResult.count;
              continue;
            }

            const leadResult = await prisma.leadNotification.updateMany({
              where: { id, userId, tenantId, isRead: false },
              data: { isRead: true },
            });
            if (leadResult.count > 0) {
              updated += leadResult.count;
              continue;
            }

            const handoverResult = await prisma.handoverNotification.updateMany({
              where: { id, userId, tenantId, isRead: false },
              data: { isRead: true, readAt: new Date() },
            });
            if (handoverResult.count > 0) {
              updated += handoverResult.count;
            }
          } catch (error) {
            console.error(`Error marking notification ${id} as read:`, error);
          }
        }
      }

      return { updated };
    } catch (error) {
      console.error('Error marking notifications as read:', error);
      throw new Error('Failed to mark notifications as read');
    }
  }
}

export const unifiedNotificationService = new UnifiedNotificationService();
