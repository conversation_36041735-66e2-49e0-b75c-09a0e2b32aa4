import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { hasPermission } from '@/lib/permissions';
import { PermissionResource, PermissionAction } from '@/types';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';

// Request validation schema
const bulkDeleteProposalsSchema = z.object({
  proposalIds: z.array(z.string()).min(1, 'At least one proposal ID is required').max(50, 'Maximum 50 proposals can be deleted at once'),
});

/**
 * DELETE /api/proposals/bulk-delete
 * Bulk delete proposals
 */
export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user's current tenant
    const currentTenant = session.user.tenants?.[0];
    if (!currentTenant) {
      return NextResponse.json({ error: 'No tenant access' }, { status: 403 });
    }

    // Check permission for proposal deletion
    const hasAccess = await hasPermission(
      PermissionResource.PROPOSALS,
      PermissionAction.DELETE,
      currentTenant.id
    );

    if (!hasAccess) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const body = await request.json();
    const { proposalIds } = bulkDeleteProposalsSchema.parse(body);

    // Verify all proposals exist and belong to tenant
    const existingProposals = await prisma.proposal.findMany({
      where: {
        id: { in: proposalIds },
        tenantId: currentTenant.id
      },
      select: {
        id: true,
        title: true,
        generationStatus: true
      }
    });

    if (existingProposals.length !== proposalIds.length) {
      const foundIds = existingProposals.map(p => p.id);
      const notFoundIds = proposalIds.filter(id => !foundIds.includes(id));
      return NextResponse.json(
        { 
          error: 'Some proposals not found or access denied',
          details: { notFoundIds }
        },
        { status: 404 }
      );
    }

    // Check if any proposals are currently being generated
    const generatingProposals = existingProposals.filter(p => p.generationStatus === 'generating');
    if (generatingProposals.length > 0) {
      return NextResponse.json(
        { 
          error: 'Cannot delete proposals that are currently being generated',
          details: { 
            generatingProposals: generatingProposals.map(p => ({ id: p.id, title: p.title }))
          }
        },
        { status: 409 }
      );
    }

    // Perform bulk deletion using transaction for data integrity
    const result = await prisma.$transaction(async (tx) => {
      // Delete proposals (cascade will handle sections and charts)
      const deletedProposals = await tx.proposal.deleteMany({
        where: {
          id: { in: proposalIds },
          tenantId: currentTenant.id
        }
      });

      return {
        deletedCount: deletedProposals.count,
        deletedProposals: existingProposals.map(p => ({ id: p.id, title: p.title }))
      };
    });

    return NextResponse.json({
      success: true,
      message: `Successfully deleted ${result.deletedCount} proposal(s)`,
      data: result
    });

  } catch (error) {
    console.error('Error bulk deleting proposals:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          error: 'Invalid request data', 
          details: error.errors.map(e => ({
            field: e.path.join('.'),
            message: e.message
          }))
        },
        { status: 400 }
      );
    }

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
