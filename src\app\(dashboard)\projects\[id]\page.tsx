'use client';

import { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { <PERSON><PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  ArrowLeft, 
  Edit, 
  Settings, 
  Calendar, 
  DollarSign, 
  Users, 
  Clock, 
  ExternalLink,
  AlertTriangle
} from 'lucide-react';
import Link from 'next/link';
import { PermissionGate } from '@/components/ui/permission-gate';
import { PermissionResource, PermissionAction } from '@/types';
import { PageLayout } from '@/components/layout/page-layout';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { EmptyState } from '@/components/ui/empty-state';
import { ResourceAllocation } from '@/components/projects/ResourceAllocation';
import { MilestoneManagement } from '@/components/projects/MilestoneManagement';
import { TaskManagement } from '@/components/projects/TaskManagement';
import { useToast } from '@/hooks/use-toast';

interface Project {
  id: string;
  name: string;
  description?: string;
  status: string;
  priority: string;
  progress: number;
  startDate?: string;
  endDate?: string;
  budget?: number;
  estimatedHours?: number;
  actualHours?: number;
  clientPortalEnabled: boolean;
  clientPortalUrl?: string;
  integrationProvider?: string;
  lastSyncAt?: string;
  createdAt: string;
  updatedAt: string;
  manager?: {
    id: string;
    firstName?: string;
    lastName?: string;
    email: string;
  };
  opportunity?: {
    id: string;
    title: string;
    value?: number;
  };
  tasks?: Array<{
    id: string;
    title: string;
    status: string;
    priority: string;
    assigneeId?: string;
    dueDate?: string;
  }>;
  milestones?: Array<{
    id: string;
    title: string;
    status: string;
    dueDate: string;
    progress: number;
  }>;
  resources?: Array<{
    id: string;
    userId: string;
    role: string;
    allocation: number;
    user: {
      firstName?: string;
      lastName?: string;
      email: string;
    };
  }>;
}

export default function ProjectDetailPage() {
  const params = useParams();
  const router = useRouter();
  const { toast } = useToast();
  const projectId = params.id as string;

  const [project, setProject] = useState<Project | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('overview');

  useEffect(() => {
    if (projectId) {
      fetchProject();
    }
  }, [projectId]);

  const fetchProject = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`/api/projects/${projectId}`);
      
      if (!response.ok) {
        if (response.status === 404) {
          setError('Project not found');
        } else {
          throw new Error('Failed to fetch project');
        }
        return;
      }

      const data = await response.json();
      setProject(data.data.project);
    } catch (err) {
      console.error('Error fetching project:', err);
      setError('Failed to load project data');
    } finally {
      setLoading(false);
    }
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      planning: { label: 'Planning', color: 'bg-blue-100 text-blue-800' },
      in_progress: { label: 'In Progress', color: 'bg-green-100 text-green-800' },
      on_hold: { label: 'On Hold', color: 'bg-yellow-100 text-yellow-800' },
      completed: { label: 'Completed', color: 'bg-gray-100 text-gray-800' },
      cancelled: { label: 'Cancelled', color: 'bg-red-100 text-red-800' },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.planning;
    return (
      <Badge className={config.color}>
        {config.label}
      </Badge>
    );
  };

  const getPriorityBadge = (priority: string) => {
    const priorityConfig = {
      low: { label: 'Low', color: 'bg-gray-100 text-gray-800' },
      medium: { label: 'Medium', color: 'bg-blue-100 text-blue-800' },
      high: { label: 'High', color: 'bg-orange-100 text-orange-800' },
      urgent: { label: 'Urgent', color: 'bg-red-100 text-red-800' },
    };

    const config = priorityConfig[priority as keyof typeof priorityConfig] || priorityConfig.medium;
    return (
      <Badge variant="outline" className={config.color}>
        {config.label}
      </Badge>
    );
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  if (loading) {
    return (
      <PageLayout>
        <div className="flex items-center justify-center h-64">
          <LoadingSpinner size="lg" />
        </div>
      </PageLayout>
    );
  }

  if (error || !project) {
    return (
      <PageLayout>
        <Card>
          <CardContent className="pt-6">
            <EmptyState
              icon={<AlertTriangle className="w-12 h-12 text-red-500" />}
              title="Error Loading Project"
              description={error || 'Project not found'}
              action={{
                label: 'Back to Projects',
                onClick: () => router.push('/projects'),
                variant: 'primary'
              }}
            />
          </CardContent>
        </Card>
      </PageLayout>
    );
  }

  const actions = (
    <div className="flex items-center gap-2">
      <Button variant="outline" onClick={() => router.push('/projects')}>
        <ArrowLeft className="h-4 w-4 mr-2" />
        Back to Projects
      </Button>
      {project.clientPortalEnabled && project.clientPortalUrl && (
        <Button variant="outline" asChild>
          <Link href={project.clientPortalUrl} target="_blank">
            <ExternalLink className="h-4 w-4 mr-2" />
            Client Portal
          </Link>
        </Button>
      )}
      <PermissionGate
        resource={PermissionResource.PROJECTS}
        action={PermissionAction.UPDATE}
      >
        <Button variant="outline" asChild>
          <Link href={`/projects/${project.id}/edit`}>
            <Edit className="h-4 w-4 mr-2" />
            Edit Project
          </Link>
        </Button>
      </PermissionGate>
      <PermissionGate
        resource={PermissionResource.PROJECTS}
        action={PermissionAction.UPDATE}
      >
        <Button variant="outline" asChild>
          <Link href={`/projects/${project.id}/settings`}>
            <Settings className="h-4 w-4 mr-2" />
            Settings
          </Link>
        </Button>
      </PermissionGate>
    </div>
  );

  return (
    <PageLayout
      title={project.name}
      description={project.description}
      actions={actions}
    >
      <div className="space-y-6">
        {/* Project Header */}
        <Card>
          <CardHeader>
            <div className="flex items-start justify-between">
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  {getStatusBadge(project.status)}
                  {getPriorityBadge(project.priority)}
                  {project.integrationProvider && (
                    <Badge variant="outline">
                      {project.integrationProvider} Integration
                    </Badge>
                  )}
                </div>
                <div className="flex items-center gap-4 text-sm text-muted-foreground">
                  <span>Created: {formatDate(project.createdAt)}</span>
                  <span>Updated: {formatDate(project.updatedAt)}</span>
                  {project.lastSyncAt && (
                    <span>Last Sync: {formatDate(project.lastSyncAt)}</span>
                  )}
                </div>
              </div>
              <div className="text-right">
                <div className="text-2xl font-bold">{Math.round(project.progress)}%</div>
                <Progress value={project.progress} className="w-32 mt-1" />
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              {project.manager && (
                <div>
                  <h4 className="font-medium text-sm text-muted-foreground">Project Manager</h4>
                  <div className="flex items-center gap-1 mt-1">
                    <Users className="w-4 h-4" />
                    <span className="text-sm">
                      {project.manager.firstName} {project.manager.lastName}
                    </span>
                  </div>
                </div>
              )}

              {project.startDate && project.endDate && (
                <div>
                  <h4 className="font-medium text-sm text-muted-foreground">Timeline</h4>
                  <div className="flex items-center gap-1 mt-1">
                    <Calendar className="w-4 h-4" />
                    <span className="text-sm">
                      {formatDate(project.startDate)} - {formatDate(project.endDate)}
                    </span>
                  </div>
                </div>
              )}

              {project.budget && (
                <div>
                  <h4 className="font-medium text-sm text-muted-foreground">Budget</h4>
                  <div className="flex items-center gap-1 mt-1">
                    <DollarSign className="w-4 h-4" />
                    <span className="text-sm">{formatCurrency(project.budget)}</span>
                  </div>
                </div>
              )}

              {project.estimatedHours && (
                <div>
                  <h4 className="font-medium text-sm text-muted-foreground">Hours</h4>
                  <div className="flex items-center gap-1 mt-1">
                    <Clock className="w-4 h-4" />
                    <span className="text-sm">
                      {project.actualHours || 0} / {project.estimatedHours}h
                    </span>
                  </div>
                </div>
              )}
            </div>

            {project.opportunity && (
              <div className="mt-4 p-3 bg-blue-50 rounded-lg">
                <h4 className="font-medium text-sm text-blue-900">Related Opportunity</h4>
                <div className="flex items-center justify-between mt-1">
                  <span className="text-sm text-blue-800">{project.opportunity.title}</span>
                  {project.opportunity.value && (
                    <span className="text-sm font-medium text-blue-900">
                      {formatCurrency(project.opportunity.value)}
                    </span>
                  )}
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Content Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
          <TabsList>
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="tasks">
              Tasks
              {project.tasks && project.tasks.length > 0 && (
                <Badge variant="secondary" className="ml-2 h-5 w-5 p-0 text-xs">
                  {project.tasks.length}
                </Badge>
              )}
            </TabsTrigger>
            <TabsTrigger value="milestones">
              Milestones
              {project.milestones && project.milestones.length > 0 && (
                <Badge variant="secondary" className="ml-2 h-5 w-5 p-0 text-xs">
                  {project.milestones.length}
                </Badge>
              )}
            </TabsTrigger>
            <TabsTrigger value="resources">
              Resources
              {project.resources && project.resources.length > 0 && (
                <Badge variant="secondary" className="ml-2 h-5 w-5 p-0 text-xs">
                  {project.resources.length}
                </Badge>
              )}
            </TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-4">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Recent Tasks */}
              <Card>
                <CardHeader>
                  <CardTitle>Recent Tasks</CardTitle>
                  <CardDescription>Latest task updates</CardDescription>
                </CardHeader>
                <CardContent>
                  {project.tasks && project.tasks.length > 0 ? (
                    <div className="space-y-3">
                      {project.tasks.slice(0, 5).map((task) => (
                        <div key={task.id} className="flex items-center justify-between">
                          <div className="flex-1">
                            <h4 className="font-medium text-sm">{task.title}</h4>
                            {task.dueDate && (
                              <p className="text-xs text-muted-foreground">
                                Due: {formatDate(task.dueDate)}
                              </p>
                            )}
                          </div>
                          <Badge variant="outline" className="text-xs">
                            {task.status.replace('_', ' ')}
                          </Badge>
                        </div>
                      ))}
                      <Button variant="outline" size="sm" onClick={() => setActiveTab('tasks')}>
                        View All Tasks
                      </Button>
                    </div>
                  ) : (
                    <EmptyState
                      icon={<Clock className="w-8 h-8 text-muted-foreground" />}
                      title="No tasks yet"
                      description="Tasks will appear here as they are created"
                    />
                  )}
                </CardContent>
              </Card>

              {/* Upcoming Milestones */}
              <Card>
                <CardHeader>
                  <CardTitle>Upcoming Milestones</CardTitle>
                  <CardDescription>Key project deliverables</CardDescription>
                </CardHeader>
                <CardContent>
                  {project.milestones && project.milestones.length > 0 ? (
                    <div className="space-y-3">
                      {project.milestones.slice(0, 3).map((milestone) => (
                        <div key={milestone.id} className="space-y-2">
                          <div className="flex items-center justify-between">
                            <h4 className="font-medium text-sm">{milestone.title}</h4>
                            <Badge variant="outline" className="text-xs">
                              {milestone.status}
                            </Badge>
                          </div>
                          <div className="flex items-center justify-between text-xs">
                            <span className="text-muted-foreground">
                              Due: {formatDate(milestone.dueDate)}
                            </span>
                            <div className="flex items-center gap-2">
                              <Progress value={milestone.progress} className="w-16" />
                              <span>{Math.round(milestone.progress)}%</span>
                            </div>
                          </div>
                        </div>
                      ))}
                      <Button variant="outline" size="sm" onClick={() => setActiveTab('milestones')}>
                        View All Milestones
                      </Button>
                    </div>
                  ) : (
                    <EmptyState
                      icon={<Calendar className="w-8 h-8 text-muted-foreground" />}
                      title="No milestones set"
                      description="Milestones will appear here as they are defined"
                    />
                  )}
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="tasks">
            <PermissionGate
              resource={PermissionResource.PROJECTS}
              action={PermissionAction.READ}
            >
              <TaskManagement
                projectId={project.id}
                canManage={true} // This should be based on actual permissions
              />
            </PermissionGate>
          </TabsContent>

          <TabsContent value="milestones">
            <PermissionGate
              resource={PermissionResource.PROJECTS}
              action={PermissionAction.READ}
            >
              <MilestoneManagement 
                projectId={project.id} 
                canManage={true} // This should be based on actual permissions
              />
            </PermissionGate>
          </TabsContent>

          <TabsContent value="resources">
            <PermissionGate
              resource={PermissionResource.PROJECTS}
              action={PermissionAction.READ}
            >
              <ResourceAllocation 
                projectId={project.id} 
                canManage={true} // This should be based on actual permissions
              />
            </PermissionGate>
          </TabsContent>
        </Tabs>
      </div>
    </PageLayout>
  );
}
