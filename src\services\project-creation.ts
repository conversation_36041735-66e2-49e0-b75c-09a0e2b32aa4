import { prisma } from '@/lib/prisma';
import { z } from 'zod';

// Helper function to parse date values (supports Date objects, strings in YYYY-MM-DD and ISO datetime formats)
const dateSchema = z.union([
  z.string(),
  z.date()
]).optional().transform((val, ctx) => {
  if (!val) return undefined;

  // If it's already a Date object, return it
  if (val instanceof Date) {
    if (isNaN(val.getTime())) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Invalid date object',
      });
      return z.NEVER;
    }
    return val;
  }

  // Handle string values
  if (typeof val === 'string') {
    // Handle YYYY-MM-DD format (from HTML date inputs)
    if (/^\d{4}-\d{2}-\d{2}$/.test(val)) {
      const date = new Date(val + 'T00:00:00.000Z');
      if (isNaN(date.getTime())) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Invalid date',
        });
        return z.NEVER;
      }
      return date;
    }

    // Handle ISO datetime format
    if (/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(\.\d{3})?Z?$/.test(val)) {
      const date = new Date(val);
      if (isNaN(date.getTime())) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Invalid datetime',
        });
        return z.NEVER;
      }
      return date;
    }

    // Try to parse any other string format
    const date = new Date(val);
    if (isNaN(date.getTime())) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Invalid date format. Expected YYYY-MM-DD, ISO datetime, or Date object',
      });
      return z.NEVER;
    }
    return date;
  }

  // Invalid type
  ctx.addIssue({
    code: z.ZodIssueCode.custom,
    message: 'Expected string or Date object',
  });
  return z.NEVER;
});

// Validation schemas
export const createProjectFromOpportunitySchema = z.object({
  name: z.string().min(1, 'Project name is required').max(255),
  description: z.string().optional(),
  startDate: dateSchema,
  endDate: dateSchema,
  budget: z.number().positive().optional(),
  managerId: z.string().optional(),
  clientPortalEnabled: z.boolean().default(false),
  customFields: z.record(z.any()).default({}),
});

export const createProjectFromHandoverSchema = z.object({
  name: z.string().min(1, 'Project name is required').max(255).optional(),
  description: z.string().optional(),
  startDate: dateSchema,
  endDate: dateSchema,
  budget: z.number().positive().optional(),
  managerId: z.string().optional(),
  clientPortalEnabled: z.boolean().default(true),
  customFields: z.record(z.any()).default({}),
});

// Types
export type CreateProjectFromOpportunityData = z.infer<typeof createProjectFromOpportunitySchema>;
export type CreateProjectFromHandoverData = z.infer<typeof createProjectFromHandoverSchema>;

export interface ProjectWithDetails {
  id: string;
  tenantId: string;
  opportunityId?: string;
  handoverId?: string;
  name: string;
  description?: string;
  status: string;
  startDate?: Date;
  endDate?: Date;
  budget?: number;
  managerId?: string;
  isFromHandover: boolean;
  handoverCompletedAt?: Date;
  clientPortalEnabled: boolean;
  createdById?: string;
  createdAt: Date;
  updatedAt: Date;
  
  // Relations
  opportunity?: {
    id: string;
    title: string;
    value?: number;
    contact?: {
      id: string;
      firstName: string;
      lastName: string;
      email?: string;
    };
    company?: {
      id: string;
      name: string;
    };
  };
  handover?: {
    id: string;
    title: string;
    status: string;
    checklistProgress: number;
    salesRep?: {
      id: string;
      firstName?: string;
      lastName?: string;
      email: string;
    };
    deliveryManager?: {
      id: string;
      firstName?: string;
      lastName?: string;
      email: string;
    };
  };
  manager?: {
    id: string;
    firstName?: string;
    lastName?: string;
    email: string;
  };
  createdBy?: {
    id: string;
    firstName?: string;
    lastName?: string;
    email: string;
  };
}

export class ProjectCreationService {
  private includeOptions = {
    opportunity: {
      select: {
        id: true,
        title: true,
        value: true,
        contact: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          }
        },
        company: {
          select: {
            id: true,
            name: true,
          }
        }
      }
    },
    handover: {
      select: {
        id: true,
        title: true,
        status: true,
        checklistProgress: true,
        salesRep: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          }
        },
        deliveryManager: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          }
        }
      }
    },
    manager: {
      select: {
        id: true,
        firstName: true,
        lastName: true,
        email: true,
      }
    },
    createdBy: {
      select: {
        id: true,
        firstName: true,
        lastName: true,
        email: true,
      }
    }
  };

  /**
   * Create a project directly from an opportunity (manual process)
   */
  async createProjectFromOpportunity(
    tenantId: string,
    opportunityId: string,
    projectData: CreateProjectFromOpportunityData,
    createdBy: string
  ): Promise<ProjectWithDetails> {
    const validatedData = createProjectFromOpportunitySchema.parse(projectData);

    return await prisma.$transaction(async (tx) => {
      // Verify opportunity exists and belongs to tenant
      const opportunity = await tx.opportunity.findUnique({
        where: {
          id: opportunityId,
          tenantId
        },
        include: {
          contact: true,
          company: true
        }
      });

      if (!opportunity) {
        throw new Error('Opportunity not found or access denied');
      }

      // Check if project already exists for this opportunity
      const existingProject = await tx.project.findFirst({
        where: {
          opportunityId,
          tenantId
        }
      });

      if (existingProject) {
        throw new Error(`A project "${existingProject.name}" already exists for this opportunity. You can view it at /projects/${existingProject.id}`);
      }

      // Verify manager belongs to tenant if provided
      if (validatedData.managerId) {
        const manager = await tx.user.findFirst({
          where: {
            id: validatedData.managerId,
            tenantUsers: {
              some: { tenantId }
            }
          }
        });

        if (!manager) {
          throw new Error('Project manager not found or not part of tenant');
        }
      }

      // Create the project
      const project = await tx.project.create({
        data: {
          tenantId,
          opportunityId,
          name: validatedData.name,
          description: validatedData.description,
          startDate: validatedData.startDate,
          endDate: validatedData.endDate,
          budget: validatedData.budget,
          managerId: validatedData.managerId,
          clientPortalEnabled: validatedData.clientPortalEnabled,
          isFromHandover: false,
          createdById: createdBy,
        },
        include: this.includeOptions,
      });

      return project as ProjectWithDetails;
    });
  }

  /**
   * Create a project from a completed handover
   */
  async createProjectFromHandover(
    tenantId: string,
    handoverId: string,
    projectData: CreateProjectFromHandoverData,
    createdBy: string
  ): Promise<ProjectWithDetails> {
    const validatedData = createProjectFromHandoverSchema.parse(projectData);

    return await prisma.$transaction(async (tx) => {
      // Verify handover exists and belongs to tenant
      const handover = await tx.projectHandover.findUnique({
        where: {
          id: handoverId,
          tenantId
        },
        include: {
          opportunity: {
            include: {
              contact: true,
              company: true
            }
          }
        }
      });

      if (!handover) {
        throw new Error('Handover not found or access denied');
      }

      // Check if project already exists for this handover
      const existingProjectByHandover = await tx.project.findFirst({
        where: {
          handoverId,
          tenantId
        }
      });

      if (existingProjectByHandover) {
        throw new Error('Project already exists for this handover');
      }

      // Check if project already exists for this opportunity
      const existingProjectByOpportunity = await tx.project.findFirst({
        where: {
          opportunityId: handover.opportunityId,
          tenantId
        },
        include: this.includeOptions
      });

      if (existingProjectByOpportunity) {
        // If a project already exists for this opportunity, update the handover to completed
        // and return the existing project instead of creating a new one
        if (handover.status !== 'completed') {
          await tx.projectHandover.update({
            where: {
              id: handoverId,
              tenantId
            },
            data: {
              status: 'completed',
              completedAt: new Date()
            }
          });
        }

        // Update the existing project to reference this handover if it doesn't already
        if (!existingProjectByOpportunity.handoverId) {
          const updatedProject = await tx.project.update({
            where: {
              id: existingProjectByOpportunity.id,
              tenantId
            },
            data: {
              handoverId,
              isFromHandover: true,
              handoverCompletedAt: new Date()
            },
            include: this.includeOptions
          });
          return updatedProject as ProjectWithDetails;
        }

        return existingProjectByOpportunity as ProjectWithDetails;
      }

      // Verify manager belongs to tenant if provided
      let managerId = validatedData.managerId;
      if (!managerId && handover.deliveryManagerId) {
        // Use delivery manager from handover as default project manager
        managerId = handover.deliveryManagerId;
      }

      if (managerId) {
        const manager = await tx.user.findFirst({
          where: {
            id: managerId,
            tenantUsers: {
              some: { tenantId }
            }
          }
        });

        if (!manager) {
          throw new Error('Project manager not found or not part of tenant');
        }
      }

      // Generate project name if not provided
      const projectName = validatedData.name || 
        `${handover.opportunity?.title || handover.title} - Project`;

      // Create the project
      const project = await tx.project.create({
        data: {
          tenantId,
          opportunityId: handover.opportunityId,
          handoverId,
          name: projectName,
          description: validatedData.description || handover.description,
          startDate: validatedData.startDate,
          endDate: validatedData.endDate,
          budget: validatedData.budget || handover.opportunity?.value,
          managerId,
          clientPortalEnabled: validatedData.clientPortalEnabled,
          isFromHandover: true,
          handoverCompletedAt: new Date(),
          createdById: createdBy,
        },
        include: this.includeOptions,
      });

      // Update handover status to completed if not already
      if (handover.status !== 'completed') {
        await tx.projectHandover.update({
          where: {
            id: handoverId,
            tenantId
          },
          data: {
            status: 'completed',
            completedAt: new Date()
          }
        });
      }

      return project as ProjectWithDetails;
    });
  }

  /**
   * Get project by handover ID (to check if project exists for the opportunity)
   */
  async getProjectByOpportunity(tenantId: string, handoverId: string): Promise<ProjectWithDetails | null> {
    // First get the handover to find the opportunity
    const handover = await prisma.projectHandover.findUnique({
      where: {
        id: handoverId,
        tenantId
      }
    });

    if (!handover) {
      return null;
    }

    // Then find project by opportunity
    const project = await prisma.project.findFirst({
      where: {
        opportunityId: handover.opportunityId,
        tenantId
      },
      include: this.includeOptions
    });

    return project as ProjectWithDetails | null;
  }

  /**
   * Automatically create project when opportunity is marked as "Closed Won"
   */
  async autoCreateProjectFromOpportunity(
    tenantId: string,
    opportunityId: string,
    triggeredBy: string
  ): Promise<ProjectWithDetails | null> {
    return await prisma.$transaction(async (tx) => {
      // Get opportunity details
      const opportunity = await tx.opportunity.findUnique({
        where: {
          id: opportunityId,
          tenantId
        },
        include: {
          contact: true,
          company: true
        }
      });

      if (!opportunity || opportunity.stage !== 'Closed Won') {
        return null; // Only create project for closed won opportunities
      }

      // Check if project already exists
      const existingProject = await tx.project.findFirst({
        where: {
          opportunityId,
          tenantId
        }
      });

      if (existingProject) {
        return existingProject as ProjectWithDetails;
      }

      // Check if there's a handover for this opportunity
      const handover = await tx.projectHandover.findFirst({
        where: {
          opportunityId,
          tenantId
        }
      });

      if (handover) {
        // If handover exists, create project from handover
        return await this.createProjectFromHandover(
          tenantId,
          handover.id,
          {
            name: `${opportunity.title} - Project`,
            description: `Auto-created project from opportunity: ${opportunity.title}`,
            budget: opportunity.value || undefined,
            clientPortalEnabled: true,
          },
          triggeredBy
        );
      } else {
        // Create project directly from opportunity
        return await this.createProjectFromOpportunity(
          tenantId,
          opportunityId,
          {
            name: `${opportunity.title} - Project`,
            description: `Auto-created project from opportunity: ${opportunity.title}`,
            budget: opportunity.value || undefined,
            clientPortalEnabled: false,
          },
          triggeredBy
        );
      }
    });
  }

  /**
   * Get project creation suggestions based on opportunities
   */
  async getProjectCreationSuggestions(
    tenantId: string,
    options: {
      limit?: number;
      includeWithHandovers?: boolean;
    } = {}
  ): Promise<Array<{
    opportunity: any;
    handover?: any;
    suggestedProjectName: string;
    hasHandover: boolean;
    readyForProject: boolean;
  }>> {
    const { limit = 10, includeWithHandovers = true } = options;

    // Get closed won opportunities without projects
    const opportunities = await prisma.opportunity.findMany({
      where: {
        tenantId,
        stage: 'Closed Won',
        projects: {
          none: {}
        }
      },
      include: {
        contact: true,
        company: true,
        handovers: {
          include: {
            salesRep: true,
            deliveryManager: true
          }
        }
      },
      orderBy: {
        updatedAt: 'desc'
      },
      take: limit
    });

    return opportunities.map(opportunity => {
      const handover = opportunity.handovers[0]; // Get first handover if exists
      const hasHandover = !!handover;
      const readyForProject = hasHandover ? 
        handover.status === 'completed' || handover.checklistProgress >= 80 :
        true; // Ready if no handover process needed

      return {
        opportunity: {
          id: opportunity.id,
          title: opportunity.title,
          value: opportunity.value,
          stage: opportunity.stage,
          contact: opportunity.contact,
          company: opportunity.company,
        },
        handover: hasHandover ? {
          id: handover.id,
          title: handover.title,
          status: handover.status,
          checklistProgress: handover.checklistProgress,
          salesRep: handover.salesRep,
          deliveryManager: handover.deliveryManager,
        } : undefined,
        suggestedProjectName: `${opportunity.title} - Project`,
        hasHandover,
        readyForProject
      };
    });
  }
}

// Export singleton instance
export const projectCreationService = new ProjectCreationService();
