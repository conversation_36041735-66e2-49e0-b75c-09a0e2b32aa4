/**
 * RTL/LTR utility functions for handling directional layouts
 */

export type Direction = 'ltr' | 'rtl';

/**
 * Check if a locale is RTL
 */
export function isRTLLocale(locale: string): boolean {
  const rtlLocales = ['ar', 'he', 'fa', 'ur', 'ku', 'dv'];
  return rtlLocales.includes(locale.toLowerCase());
}

/**
 * Get direction for a locale
 */
export function getDirection(locale: string): Direction {
  return isRTLLocale(locale) ? 'rtl' : 'ltr';
}

/**
 * Get opposite direction
 */
export function getOppositeDirection(direction: Direction): Direction {
  return direction === 'rtl' ? 'ltr' : 'rtl';
}

/**
 * RTL-aware margin and padding utilities
 */
export const rtlUtils = {
  /**
   * Get margin-left or margin-right based on direction
   */
  marginStart: (value: string, direction: Direction = 'ltr') => {
    return direction === 'rtl' ? { marginRight: value } : { marginLeft: value };
  },

  /**
   * Get margin-right or margin-left based on direction
   */
  marginEnd: (value: string, direction: Direction = 'ltr') => {
    return direction === 'rtl' ? { marginLeft: value } : { marginRight: value };
  },

  /**
   * Get padding-left or padding-right based on direction
   */
  paddingStart: (value: string, direction: Direction = 'ltr') => {
    return direction === 'rtl' ? { paddingRight: value } : { paddingLeft: value };
  },

  /**
   * Get padding-right or padding-left based on direction
   */
  paddingEnd: (value: string, direction: Direction = 'ltr') => {
    return direction === 'rtl' ? { paddingLeft: value } : { paddingRight: value };
  },

  /**
   * Get left or right based on direction
   */
  start: (value: string, direction: Direction = 'ltr') => {
    return direction === 'rtl' ? { right: value } : { left: value };
  },

  /**
   * Get right or left based on direction
   */
  end: (value: string, direction: Direction = 'ltr') => {
    return direction === 'rtl' ? { left: value } : { right: value };
  },

  /**
   * Get border-left or border-right based on direction
   */
  borderStart: (value: string, direction: Direction = 'ltr') => {
    return direction === 'rtl' ? { borderRight: value } : { borderLeft: value };
  },

  /**
   * Get border-right or border-left based on direction
   */
  borderEnd: (value: string, direction: Direction = 'ltr') => {
    return direction === 'rtl' ? { borderLeft: value } : { borderRight: value };
  }
};

/**
 * Tailwind CSS class utilities for RTL
 */
export const rtlClasses = {
  /**
   * Get margin classes based on direction
   */
  marginStart: (size: string, direction: Direction = 'ltr') => {
    return direction === 'rtl' ? `mr-${size}` : `ml-${size}`;
  },

  marginEnd: (size: string, direction: Direction = 'ltr') => {
    return direction === 'rtl' ? `ml-${size}` : `mr-${size}`;
  },

  /**
   * Get padding classes based on direction
   */
  paddingStart: (size: string, direction: Direction = 'ltr') => {
    return direction === 'rtl' ? `pr-${size}` : `pl-${size}`;
  },

  paddingEnd: (size: string, direction: Direction = 'ltr') => {
    return direction === 'rtl' ? `pl-${size}` : `pr-${size}`;
  },

  /**
   * Get text alignment classes
   */
  textStart: (direction: Direction = 'ltr') => {
    return direction === 'rtl' ? 'text-right' : 'text-left';
  },

  textEnd: (direction: Direction = 'ltr') => {
    return direction === 'rtl' ? 'text-left' : 'text-right';
  },

  /**
   * Get flex direction classes
   */
  flexRow: (direction: Direction = 'ltr') => {
    return direction === 'rtl' ? 'flex-row-reverse' : 'flex-row';
  },

  /**
   * Get border classes based on direction
   */
  borderStart: (size: string, direction: Direction = 'ltr') => {
    return direction === 'rtl' ? `border-r-${size}` : `border-l-${size}`;
  },

  borderEnd: (size: string, direction: Direction = 'ltr') => {
    return direction === 'rtl' ? `border-l-${size}` : `border-r-${size}`;
  },

  /**
   * Get positioning classes
   */
  start: (size: string, direction: Direction = 'ltr') => {
    return direction === 'rtl' ? `right-${size}` : `left-${size}`;
  },

  end: (size: string, direction: Direction = 'ltr') => {
    return direction === 'rtl' ? `left-${size}` : `right-${size}`;
  }
};

/**
 * Transform CSS properties for RTL
 */
export function transformRTLStyles(
  styles: Record<string, any>, 
  direction: Direction = 'ltr'
): Record<string, any> {
  if (direction === 'ltr') return styles;

  const transformed = { ...styles };
  const transformMap: Record<string, string> = {
    marginLeft: 'marginRight',
    marginRight: 'marginLeft',
    paddingLeft: 'paddingRight',
    paddingRight: 'paddingLeft',
    left: 'right',
    right: 'left',
    borderLeft: 'borderRight',
    borderRight: 'borderLeft',
    borderLeftWidth: 'borderRightWidth',
    borderRightWidth: 'borderLeftWidth',
    borderLeftColor: 'borderRightColor',
    borderRightColor: 'borderLeftColor',
    borderLeftStyle: 'borderRightStyle',
    borderRightStyle: 'borderLeftStyle',
    borderTopLeftRadius: 'borderTopRightRadius',
    borderTopRightRadius: 'borderTopLeftRadius',
    borderBottomLeftRadius: 'borderBottomRightRadius',
    borderBottomRightRadius: 'borderBottomLeftRadius'
  };

  Object.keys(transformed).forEach(key => {
    if (transformMap[key]) {
      const value = transformed[key];
      delete transformed[key];
      transformed[transformMap[key]] = value;
    }
  });

  return transformed;
}

/**
 * Get appropriate font family for locale
 */
export function getFontFamily(locale: string): string {
  const fontMap: Record<string, string> = {
    ar: 'font-arabic',
    he: 'font-hebrew',
    fa: 'font-persian',
    ur: 'font-urdu',
    default: 'font-sans'
  };

  return fontMap[locale.toLowerCase()] || fontMap.default;
}

/**
 * Format number for RTL locales
 */
export function formatNumberRTL(
  number: number, 
  locale: string, 
  options?: Intl.NumberFormatOptions
): string {
  try {
    return new Intl.NumberFormat(locale, options).format(number);
  } catch (error) {
    // Fallback to English formatting
    return new Intl.NumberFormat('en-US', options).format(number);
  }
}

/**
 * Format date for RTL locales
 */
export function formatDateRTL(
  date: Date | string, 
  locale: string, 
  options?: Intl.DateTimeFormatOptions
): string {
  try {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return new Intl.DateTimeFormat(locale, options).format(dateObj);
  } catch (error) {
    // Fallback to English formatting
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return new Intl.DateTimeFormat('en-US', options).format(dateObj);
  }
}

/**
 * Get text direction for input elements
 */
export function getInputDirection(locale: string, content?: string): Direction {
  // If content is provided, detect its direction
  if (content) {
    const rtlChars = /[\u0590-\u083F]|[\u08A0-\u08FF]|[\uFB1D-\uFDFF]|[\uFE70-\uFEFF]/;
    if (rtlChars.test(content)) {
      return 'rtl';
    }
  }
  
  // Otherwise, use locale-based direction
  return getDirection(locale);
}

/**
 * Validate RTL text rendering
 */
export function validateRTLText(text: string): {
  hasRTLChars: boolean;
  hasLTRChars: boolean;
  isMixed: boolean;
  suggestedDirection: Direction;
} {
  const rtlChars = /[\u0590-\u083F]|[\u08A0-\u08FF]|[\uFB1D-\uFDFF]|[\uFE70-\uFEFF]/;
  const ltrChars = /[A-Za-z]/;
  
  const hasRTLChars = rtlChars.test(text);
  const hasLTRChars = ltrChars.test(text);
  const isMixed = hasRTLChars && hasLTRChars;
  
  let suggestedDirection: Direction = 'ltr';
  if (hasRTLChars && !hasLTRChars) {
    suggestedDirection = 'rtl';
  } else if (hasRTLChars && hasLTRChars) {
    // For mixed content, suggest based on the first strong directional character
    const firstRTLIndex = text.search(rtlChars);
    const firstLTRIndex = text.search(ltrChars);
    
    if (firstRTLIndex !== -1 && (firstLTRIndex === -1 || firstRTLIndex < firstLTRIndex)) {
      suggestedDirection = 'rtl';
    }
  }
  
  return {
    hasRTLChars,
    hasLTRChars,
    isMixed,
    suggestedDirection
  };
}

/**
 * Create RTL-aware CSS class string
 */
export function createRTLClass(
  baseClasses: string, 
  rtlClasses: string, 
  direction: Direction = 'ltr'
): string {
  const classes = baseClasses.split(' ');
  
  if (direction === 'rtl') {
    const rtlClassArray = rtlClasses.split(' ');
    classes.push(...rtlClassArray);
  }
  
  return classes.join(' ');
}

/**
 * Get icon rotation for RTL
 */
export function getIconRotation(direction: Direction = 'ltr'): string {
  return direction === 'rtl' ? 'transform rotate-180' : '';
}

/**
 * Check if element needs RTL transformation
 */
export function needsRTLTransform(element: HTMLElement): boolean {
  const computedStyle = window.getComputedStyle(element);
  const direction = computedStyle.direction;
  
  return direction === 'rtl';
}

/**
 * Apply RTL transformations to element
 */
export function applyRTLTransform(element: HTMLElement, direction: Direction): void {
  element.dir = direction;
  
  if (direction === 'rtl') {
    element.classList.add('rtl');
  } else {
    element.classList.remove('rtl');
  }
}
