'use client';

import { useState } from 'react';
import { useSession } from 'next-auth/react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { useToast } from '@/hooks/use-toast';
import { EnvelopeIcon, PlusIcon } from '@heroicons/react/24/outline';
import { z } from 'zod';

const emailCaptureSchema = z.object({
  fromEmail: z.string().email('Valid email address is required'),
  fromName: z.string().min(1, 'Name is required'),
  subject: z.string().min(1, 'Subject is required'),
  content: z.string().min(10, 'Content must be at least 10 characters'),
  emailProvider: z.string().optional()
});

type EmailCaptureFormData = z.infer<typeof emailCaptureSchema>;

interface EmailCaptureFormProps {
  onSuccess?: () => void;
  onCancel?: () => void;
}

export function EmailCaptureForm({ onSuccess, onCancel }: EmailCaptureFormProps) {
  const { data: session } = useSession();
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState<EmailCaptureFormData>({
    fromEmail: '',
    fromName: '',
    subject: '',
    content: '',
    emailProvider: 'manual'
  });
  const [errors, setErrors] = useState<Record<string, string>>({});

  const tenantId = session?.user?.tenants?.[0]?.id;

  const handleInputChange = (field: keyof EmailCaptureFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const validateForm = () => {
    try {
      emailCaptureSchema.parse(formData);
      setErrors({});
      return true;
    } catch (error) {
      if (error instanceof z.ZodError) {
        const newErrors: Record<string, string> = {};
        error.errors.forEach(err => {
          if (err.path[0]) {
            newErrors[err.path[0] as string] = err.message;
          }
        });
        setErrors(newErrors);
      }
      return false;
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    if (!tenantId) {
      toast({
        title: 'Error',
        description: 'No tenant found. Please refresh and try again.',
        variant: 'destructive'
      });
      return;
    }

    setLoading(true);

    try {
      const response = await fetch('/api/leads/email-capture', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-tenant-id': tenantId
        },
        body: JSON.stringify({
          ...formData,
          receivedAt: new Date().toISOString()
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to capture email');
      }

      const result = await response.json();

      toast({
        title: 'Success',
        description: `Email captured successfully! ${result.data.lead ? 'Lead created.' : 'Processing in progress.'}`,
        variant: 'default'
      });

      // Reset form
      setFormData({
        fromEmail: '',
        fromName: '',
        subject: '',
        content: '',
        emailProvider: 'manual'
      });

      onSuccess?.();

    } catch (error) {
      console.error('Error capturing email:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to capture email',
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <div className="flex items-center space-x-2">
          <EnvelopeIcon className="h-6 w-6 text-primary" />
          <div>
            <CardTitle>Capture Email</CardTitle>
            <CardDescription>
              Manually add an email to the lead capture system for processing
            </CardDescription>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="fromEmail">From Email *</Label>
              <Input
                id="fromEmail"
                type="email"
                placeholder="<EMAIL>"
                value={formData.fromEmail}
                onChange={(e) => handleInputChange('fromEmail', e.target.value)}
                className={errors.fromEmail ? 'border-red-500' : ''}
              />
              {errors.fromEmail && (
                <p className="text-sm text-red-500">{errors.fromEmail}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="fromName">From Name *</Label>
              <Input
                id="fromName"
                placeholder="John Doe"
                value={formData.fromName}
                onChange={(e) => handleInputChange('fromName', e.target.value)}
                className={errors.fromName ? 'border-red-500' : ''}
              />
              {errors.fromName && (
                <p className="text-sm text-red-500">{errors.fromName}</p>
              )}
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="subject">Subject *</Label>
            <Input
              id="subject"
              placeholder="Email subject line"
              value={formData.subject}
              onChange={(e) => handleInputChange('subject', e.target.value)}
              className={errors.subject ? 'border-red-500' : ''}
            />
            {errors.subject && (
              <p className="text-sm text-red-500">{errors.subject}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="content">Email Content *</Label>
            <Textarea
              id="content"
              placeholder="Enter the email content here..."
              rows={6}
              value={formData.content}
              onChange={(e) => handleInputChange('content', e.target.value)}
              className={errors.content ? 'border-red-500' : ''}
            />
            {errors.content && (
              <p className="text-sm text-red-500">{errors.content}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="emailProvider">Email Provider (Optional)</Label>
            <Input
              id="emailProvider"
              placeholder="gmail, outlook, etc."
              value={formData.emailProvider}
              onChange={(e) => handleInputChange('emailProvider', e.target.value)}
            />
          </div>

          <div className="flex justify-end space-x-3">
            {onCancel && (
              <Button
                type="button"
                variant="outline"
                onClick={onCancel}
                disabled={loading}
              >
                Cancel
              </Button>
            )}
            <Button
              type="submit"
              disabled={loading}
              className="min-w-[120px]"
            >
              {loading ? (
                <div className="flex items-center space-x-2">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  <span>Processing...</span>
                </div>
              ) : (
                <div className="flex items-center space-x-2">
                  <PlusIcon className="h-4 w-4" />
                  <span>Capture Email</span>
                </div>
              )}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}
