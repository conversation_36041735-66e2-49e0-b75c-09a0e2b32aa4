'use client';

import React from 'react';
import { useRouter } from 'next/navigation';
import { PermissionProvider } from '@/components/providers/permission-provider';
import { EnhancedTeamManagement } from '@/components/admin/enhanced-team-management';
import { PermissionGate } from '@/components/ui/permission-gate';
import { PermissionAction, PermissionResource } from '@/types';
import { useTenant } from '@/hooks/use-tenant';
import { PageLayout } from '@/components/layout/page-layout';
import { Button } from '@/components/ui/button';
import {
  RectangleGroupIcon,
  ExclamationTriangleIcon,
  PlusIcon
} from '@heroicons/react/24/outline';

export default function TeamSettingsPage() {
  const { currentTenant } = useTenant();
  const router = useRouter();

  if (!currentTenant) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <ExclamationTriangleIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-500">No tenant context available</p>
        </div>
      </div>
    );
  }

  const handleCreateTeam = () => {
    router.push('/settings/team/new');
  };

  const actions = (
    <PermissionGate
      resource={PermissionResource.USERS}
      action={PermissionAction.MANAGE}
    >
      <Button onClick={handleCreateTeam} className="flex items-center gap-2">
        <PlusIcon className="w-4 h-4" />
        Create Team
      </Button>
    </PermissionGate>
  );

  return (
    <PermissionProvider>
      <PageLayout
        title="Team Management"
        description={`Organize and manage your team structure for ${currentTenant.name}`}
        actions={actions}
      >
        <PermissionGate
          resource={PermissionResource.USERS}
          action={PermissionAction.READ}
          fallback={<AccessDeniedMessage />}
        >
          <EnhancedTeamManagement tenantId={currentTenant.id} />
        </PermissionGate>
      </PageLayout>
    </PermissionProvider>
  );
}

function AccessDeniedMessage({ message }: { message?: string }) {
  return (
    <div className="text-center py-12">
      <ExclamationTriangleIcon className="w-12 h-12 text-red-400 mx-auto mb-4" />
      <h3 className="text-lg font-medium text-gray-900 mb-2">Access Denied</h3>
      <p className="text-gray-500 max-w-md mx-auto">
        {message || "You don't have permission to access team management. Contact your administrator for access."}
      </p>
    </div>
  );
}
