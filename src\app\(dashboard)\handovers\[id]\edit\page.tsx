'use client';

import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import HandoverForm from '@/components/handovers/HandoverForm';
import { PermissionGate } from '@/components/ui/permission-gate';
import { PermissionResource, PermissionAction } from '@/types';
import { Card, CardContent } from '@/components/ui/card';
import { EmptyState } from '@/components/ui/empty-state';
import { ExclamationTriangleIcon } from '@heroicons/react/24/outline';
import { PageLayout } from '@/components/layout/page-layout';
import { FormPageSkeleton } from '@/components/ui/skeleton-loaders';
import { Suspense, use } from 'react';

interface PageProps {
  params: Promise<{
    id: string;
  }>;
}

export default function EditHandoverPage({ params }: PageProps) {
  const resolvedParams = use(params);

  if (!resolvedParams.id) {
    notFound();
  }

  // TODO: Re-enable permission gate after running permission seeding
  // Run: node scripts/seed-permissions.js

  return (
    <PageLayout
      title="Edit Handover"
      description="Edit handover details and configuration"
    >
      <Suspense fallback={<FormPageSkeleton />}>
        <HandoverForm
          handoverId={resolvedParams.id}
        />
      </Suspense>
    </PageLayout>
  );

  // Uncomment this after seeding handover permissions:
  /*
  return (
    <PermissionGate
      resource={PermissionResource.HANDOVERS}
      action={PermissionAction.UPDATE}
      fallback={
        <PageLayout>
          <Card>
            <CardContent className="p-8">
              <EmptyState
                icon={<ExclamationTriangleIcon className="w-12 h-12 text-muted-foreground" />}
                title="Access Denied"
                description="You don't have permission to edit handovers. Please contact your administrator for access."
                action={{
                  label: 'Back to Handovers',
                  onClick: () => window.location.href = '/handovers',
                  variant: 'primary'
                }}
              />
            </CardContent>
          </Card>
        </PageLayout>
      }
    >
      <PageLayout
        title="Edit Handover"
        description="Edit handover details and configuration"
      >
        <Suspense fallback={<FormPageSkeleton />}>
          <HandoverForm
            handoverId={resolvedParams.id}
          />
        </Suspense>
      </PageLayout>
    </PermissionGate>
  );
  */
}
