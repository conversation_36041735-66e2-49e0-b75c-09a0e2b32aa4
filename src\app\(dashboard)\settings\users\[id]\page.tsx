'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { PermissionProvider } from '@/components/providers/permission-provider';
import { PermissionGate } from '@/components/ui/permission-gate';
import { PermissionAction, PermissionResource } from '@/types';
import { useTenant } from '@/hooks/use-tenant';
import { PageLayout } from '@/components/layout/page-layout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ContactAvatar } from '@/components/ui/contact-avatar';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { UserWithTenantInfo } from '@/services/user-management';
import {
  ArrowLeftIcon,
  UserIcon,
  PencilIcon,
  TrashIcon,
  EnvelopeIcon,
  CalendarIcon,
  BuildingOfficeIcon,
  BriefcaseIcon,
  UserGroupIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';

export default function UserDetailsPage() {
  const { currentTenant } = useTenant();
  const router = useRouter();
  const params = useParams();
  const userId = params.id as string;

  const [user, setUser] = useState<UserWithTenantInfo | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (currentTenant && userId) {
      fetchUserDetails();
    }
  }, [currentTenant, userId]);

  const fetchUserDetails = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch(`/api/users/${userId}`);
      
      if (!response.ok) {
        if (response.status === 404) {
          throw new Error('User not found');
        }
        throw new Error('Failed to fetch user details');
      }

      const data = await response.json();
      
      if (data.success) {
        setUser(data.data);
      } else {
        throw new Error('Failed to fetch user details');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch user details');
    } finally {
      setIsLoading(false);
    }
  };

  const handleEditUser = () => {
    router.push(`/settings/users/${userId}/edit`);
  };

  const handleDeleteUser = async () => {
    if (!confirm('Are you sure you want to remove this user from the tenant? This action cannot be undone.')) {
      return;
    }

    try {
      const response = await fetch(`/api/users/${userId}`, {
        method: 'DELETE'
      });

      if (response.ok) {
        router.push('/settings/users');
      } else {
        const errorData = await response.json();
        setError(errorData.error || 'Failed to remove user');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to remove user');
    }
  };

  const handleBackToUsers = () => {
    router.push('/settings/users');
  };

  if (!currentTenant) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <ExclamationTriangleIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-500">No tenant context available</p>
        </div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <PageLayout title="Loading..." description="Loading user details...">
        <div className="flex items-center justify-center h-64">
          <LoadingSpinner />
        </div>
      </PageLayout>
    );
  }

  if (error || !user) {
    return (
      <PageLayout title="Error" description="Failed to load user details">
        <div className="text-center py-12">
          <ExclamationTriangleIcon className="w-12 h-12 text-red-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Error</h3>
          <p className="text-gray-500 mb-4">{error || 'User not found'}</p>
          <Button onClick={handleBackToUsers}>
            <ArrowLeftIcon className="w-4 h-4 mr-2" />
            Back to Users
          </Button>
        </div>
      </PageLayout>
    );
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'inactive':
        return 'bg-gray-100 text-gray-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const actions = (
    <div className="flex items-center gap-2">
      <Button variant="outline" onClick={handleBackToUsers}>
        <ArrowLeftIcon className="w-4 h-4 mr-2" />
        Back to Users
      </Button>
      <PermissionGate
        resource={PermissionResource.USERS}
        action={PermissionAction.UPDATE}
      >
        <Button variant="outline" onClick={handleEditUser}>
          <PencilIcon className="w-4 h-4 mr-2" />
          Edit User
        </Button>
      </PermissionGate>
      <PermissionGate
        resource={PermissionResource.USERS}
        action={PermissionAction.DELETE}
      >
        <Button variant="destructive" onClick={handleDeleteUser}>
          <TrashIcon className="w-4 h-4 mr-2" />
          Remove User
        </Button>
      </PermissionGate>
    </div>
  );

  return (
    <PermissionProvider>
      <PageLayout
        title={`${user.firstName} ${user.lastName}`}
        description="User details and information"
        actions={actions}
      >
        <PermissionGate
          resource={PermissionResource.USERS}
          action={PermissionAction.READ}
          fallback={<AccessDeniedMessage />}
        >
          <div className="space-y-6">
            {error && (
              <div className="bg-red-50 border border-red-200 rounded-md p-4">
                <p className="text-red-600">{error}</p>
                <button
                  onClick={() => setError(null)}
                  className="text-red-600 hover:text-red-800 underline mt-2"
                >
                  Dismiss
                </button>
              </div>
            )}

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* User Profile */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <UserIcon className="w-5 h-5" />
                    User Profile
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="flex items-center space-x-4">
                    <ContactAvatar
                      firstName={user.firstName}
                      lastName={user.lastName}
                      avatarUrl={user.avatarUrl}
                      size="lg"
                    />
                    <div>
                      <h3 className="text-lg font-medium text-gray-900">
                        {user.firstName} {user.lastName}
                      </h3>
                      <div className="flex items-center gap-2 mt-1">
                        <EnvelopeIcon className="w-4 h-4 text-gray-400" />
                        <span className="text-gray-600">{user.email}</span>
                      </div>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 gap-4">
                    <div>
                      <label className="text-sm font-medium text-gray-500">Status</label>
                      <div className="mt-1">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(user.status)}`}>
                          {user.status}
                        </span>
                      </div>
                    </div>

                    {user.lastLogin && (
                      <div>
                        <label className="text-sm font-medium text-gray-500">Last Login</label>
                        <div className="flex items-center gap-2 mt-1">
                          <CalendarIcon className="w-4 h-4 text-gray-400" />
                          <span className="text-gray-900">
                            {new Date(user.lastLogin).toLocaleString()}
                          </span>
                        </div>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>

              {/* Tenant Information */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <BuildingOfficeIcon className="w-5 h-5" />
                    Tenant Information
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <label className="text-sm font-medium text-gray-500">Role</label>
                    <div className="mt-1">
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        {user.tenantUser.role}
                      </span>
                    </div>
                  </div>

                  <div>
                    <label className="text-sm font-medium text-gray-500">Tenant Status</label>
                    <div className="mt-1">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(user.tenantUser.status)}`}>
                        {user.tenantUser.status}
                      </span>
                    </div>
                  </div>

                  <div>
                    <label className="text-sm font-medium text-gray-500">Joined Date</label>
                    <div className="flex items-center gap-2 mt-1">
                      <CalendarIcon className="w-4 h-4 text-gray-400" />
                      <span className="text-gray-900">
                        {new Date(user.tenantUser.joinedAt).toLocaleDateString()}
                      </span>
                    </div>
                  </div>

                  {user.tenantUser.department && (
                    <div>
                      <label className="text-sm font-medium text-gray-500">Department</label>
                      <div className="flex items-center gap-2 mt-1">
                        <BuildingOfficeIcon className="w-4 h-4 text-gray-400" />
                        <span className="text-gray-900">{user.tenantUser.department}</span>
                      </div>
                    </div>
                  )}

                  {user.tenantUser.jobTitle && (
                    <div>
                      <label className="text-sm font-medium text-gray-500">Job Title</label>
                      <div className="flex items-center gap-2 mt-1">
                        <BriefcaseIcon className="w-4 h-4 text-gray-400" />
                        <span className="text-gray-900">{user.tenantUser.jobTitle}</span>
                      </div>
                    </div>
                  )}

                  {user.tenantUser.team && (
                    <div>
                      <label className="text-sm font-medium text-gray-500">Team</label>
                      <div className="flex items-center gap-2 mt-1">
                        <UserGroupIcon className="w-4 h-4 text-gray-400" />
                        <div className="flex items-center gap-2">
                          <div
                            className="w-3 h-3 rounded-full"
                            style={{ backgroundColor: user.tenantUser.team.color || '#6B7280' }}
                          />
                          <span className="text-gray-900">{user.tenantUser.team.name}</span>
                        </div>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          </div>
        </PermissionGate>
      </PageLayout>
    </PermissionProvider>
  );
}

function AccessDeniedMessage() {
  return (
    <div className="text-center py-12">
      <ExclamationTriangleIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
      <h3 className="text-lg font-medium text-gray-900 mb-2">Access Denied</h3>
      <p className="text-gray-500 max-w-md mx-auto">
        You don't have permission to view user details.
      </p>
    </div>
  );
}
