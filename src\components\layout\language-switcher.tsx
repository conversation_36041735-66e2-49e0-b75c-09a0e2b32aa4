'use client';

import React from 'react';
import { Globe, Check } from 'lucide-react';
import { useLocale } from '@/components/providers/locale-provider';
import { cn } from '@/lib/utils';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Button } from '@/components/ui/button';

const languages = [
  {
    code: 'en' as const,
    name: 'English',
    nativeName: 'English',
    flag: '🇺🇸',
    dir: 'ltr',
  },
  {
    code: 'ar' as const,
    name: 'Arabic',
    nativeName: 'العربية',
    flag: '🇸🇦',
    dir: 'rtl',
  },
];

interface LanguageSwitcherProps {
  variant?: 'default' | 'sidebar';
  showLabel?: boolean;
  className?: string;
  size?: 'sm' | 'default' | 'lg';
}

export function LanguageSwitcher({
  variant = 'default',
  showLabel = false,
  className,
  size = 'default'
}: LanguageSwitcherProps) {
  const { locale, setLocale } = useLocale();

  const currentLanguage = languages.find(lang => lang.code === locale) || languages[0];

  const handleLanguageChange = (newLocale: 'en' | 'ar') => {
    // Add smooth transition class before changing locale
    document.body.classList.add('transition-all', 'duration-300');

    setLocale(newLocale);

    // Remove transition class after a delay
    setTimeout(() => {
      document.body.classList.remove('transition-all', 'duration-300');
    }, 300);
  };

  if (variant === 'sidebar') {
    return (
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="ghost"
            size="sm"
            className={cn(
              "h-8 w-8 px-0 data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground",
              className
            )}
            title="Switch language"
          >
            <span className="text-sm">{currentLanguage.flag}</span>
            <span className="sr-only">Switch language</span>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-48">
          {languages.map((language) => {
            const isSelected = locale === language.code;

            return (
              <DropdownMenuItem
                key={language.code}
                onClick={() => handleLanguageChange(language.code)}
                className="gap-3"
              >
                <span className="text-base">{language.flag}</span>
                <div className="flex flex-col items-start flex-1">
                  <span className="font-medium">{language.nativeName}</span>
                  <span className="text-xs text-muted-foreground uppercase">
                    {language.code}
                  </span>
                </div>
                {isSelected && <Check className="h-4 w-4" />}
              </DropdownMenuItem>
            );
          })}
        </DropdownMenuContent>
      </DropdownMenu>
    );
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          size={size}
          className={cn(
            "gap-2",
            !showLabel && "px-2",
            className
          )}
          title="Switch language"
        >
          <Globe className={cn(
            size === 'sm' ? 'h-4 w-4' : 'h-5 w-5'
          )} />
          <span className="text-sm">{currentLanguage.flag}</span>
          {showLabel && (
            <span className="text-sm font-medium">
              {currentLanguage.nativeName}
            </span>
          )}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-48">
        {languages.map((language) => {
          const isSelected = locale === language.code;

          return (
            <DropdownMenuItem
              key={language.code}
              onClick={() => handleLanguageChange(language.code)}
              className="gap-3"
            >
              <span className="text-base">{language.flag}</span>
              <div className="flex flex-col items-start flex-1">
                <span className="font-medium">{language.nativeName}</span>
                <span className="text-xs text-muted-foreground uppercase">
                  {language.code}
                </span>
              </div>
              {isSelected && <Check className="h-4 w-4" />}
            </DropdownMenuItem>
          );
        })}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
