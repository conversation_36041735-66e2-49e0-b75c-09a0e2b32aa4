'use client';

import { useState, useEffect, useCallback } from 'react';
import { useSession } from 'next-auth/react';
import { ScheduledMeetingWithRelations } from '@/services/scheduled-meeting-service';
import { useToast } from '@/hooks/use-toast';
import { getExtendedDateRange } from '@/lib/date-utils';

export type TimePeriod = 'day' | 'week' | 'month';

interface UseMeetingsOverviewResult {
  meetings: ScheduledMeetingWithRelations[];
  upcomingMeetings: ScheduledMeetingWithRelations[];
  pastMeetings: ScheduledMeetingWithRelations[];
  loading: boolean;
  error: string | null;
  timePeriod: TimePeriod;
  setTimePeriod: (period: TimePeriod) => void;
  refetch: () => void;
}

export function useMeetingsOverview(): UseMeetingsOverviewResult {
  const { data: session } = useSession();
  const { toast } = useToast();
  
  const [meetings, setMeetings] = useState<ScheduledMeetingWithRelations[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [timePeriod, setTimePeriod] = useState<TimePeriod>('week');

  const getDateRange = useCallback((period: TimePeriod) => {
    const { from, to } = getExtendedDateRange(period);
    return { dateFrom: from, dateTo: to };
  }, []);

  const fetchMeetings = useCallback(async () => {
    if (!session?.user) return;

    try {
      setLoading(true);
      setError(null);

      const { dateFrom, dateTo } = getDateRange(timePeriod);
      
      const params = new URLSearchParams({
        dateFrom: dateFrom.toISOString(),
        dateTo: dateTo.toISOString(),
        sortBy: 'scheduledAt',
        sortOrder: 'asc',
        limit: '50' // Get more meetings for better overview
      });

      const response = await fetch(`/api/meetings?${params.toString()}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch meetings');
      }

      const result = await response.json();
      
      if (result.success) {
        setMeetings(result.data.meetings || []);
      } else {
        throw new Error(result.message || 'Failed to fetch meetings');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      setError(errorMessage);
      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  }, [session, timePeriod, getDateRange, toast]);

  useEffect(() => {
    fetchMeetings();
  }, [fetchMeetings]);

  // Separate meetings into upcoming and past
  const now = new Date();
  const upcomingMeetings = meetings.filter(meeting => 
    new Date(meeting.scheduledAt) > now && meeting.status === 'scheduled'
  );
  const pastMeetings = meetings.filter(meeting => 
    new Date(meeting.scheduledAt) <= now || meeting.status === 'completed'
  );

  return {
    meetings,
    upcomingMeetings,
    pastMeetings,
    loading,
    error,
    timePeriod,
    setTimePeriod,
    refetch: fetchMeetings,
  };
}
