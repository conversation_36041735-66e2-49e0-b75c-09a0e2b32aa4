import { NextRequest, NextResponse } from 'next/server';
import { withPermission, AuthenticatedRequest } from '@/lib/permission-middleware';
import { DocumentTemplateManagementService } from '@/services/document-template-management';
import { htmlTemplateGeneratorService } from '@/services/html-template-generator';
import { PermissionResource, PermissionAction } from '@/types';
import { z } from 'zod';

interface RouteParams {
  params: Promise<{ id: string }>;
}

// Request validation schema
const generateHtmlRequestSchema = z.object({
  data: z.record(z.any()).optional().default({}),
  options: z.object({
    format: z.enum(['html', 'pdf']).optional().default('html'),
    includeStyles: z.boolean().optional().default(true),
    responsive: z.boolean().optional().default(true)
  }).optional().default({})
});

/**
 * POST /api/document-templates/[id]/generate-html
 * Generate HTML from template with provided data
 */
export const POST = withPermission({
  resource: PermissionResource.PROPOSALS,
  action: PermissionAction.READ,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest, { params }: RouteParams) => {
  try {
    const { id: templateId } = await params;
    const body = await req.json();
    
    // Validate request body
    const validatedData = generateHtmlRequestSchema.parse(body);

    // Get the template
    const templateService = new DocumentTemplateManagementService();
    const template = await templateService.getTemplate(templateId, req.tenantId);

    if (!template) {
      return NextResponse.json(
        { error: 'Template not found' },
        { status: 404 }
      );
    }

    if (!template.htmlTemplate) {
      return NextResponse.json(
        { error: 'Template does not have HTML content. Please analyze the template first.' },
        { status: 400 }
      );
    }

    // Validate template data
    const validation = htmlTemplateGeneratorService.validateTemplateData(
      validatedData.data,
      template.placeholderMappings || {}
    );

    if (!validation.isValid) {
      return NextResponse.json(
        { 
          error: 'Template data validation failed',
          details: {
            missingRequired: validation.missingRequired,
            errors: validation.errors
          }
        },
        { status: 400 }
      );
    }

    // Generate HTML
    const result = await htmlTemplateGeneratorService.generateHTML(
      template,
      validatedData.data,
      validatedData.options
    );

    // Update template usage statistics
    await templateService.incrementUsageCount(templateId, req.tenantId);

    return NextResponse.json({
      success: true,
      data: {
        html: result.html,
        metadata: result.metadata,
        template: {
          id: template.id,
          name: template.name,
          templateType: template.templateType
        }
      },
      message: 'HTML generated successfully'
    });

  } catch (error) {
    console.error('Generate HTML error:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          error: 'Invalid request data',
          details: error.errors
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to generate HTML' },
      { status: 500 }
    );
  }
});

/**
 * GET /api/document-templates/[id]/generate-html
 * Get template preview with sample data
 */
export const GET = withPermission({
  resource: PermissionResource.PROPOSALS,
  action: PermissionAction.READ,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest, { params }: RouteParams) => {
  try {
    const { id: templateId } = await params;

    // Get the template
    const templateService = new DocumentTemplateManagementService();
    const template = await templateService.getTemplate(templateId, req.tenantId);

    if (!template) {
      return NextResponse.json(
        { error: 'Template not found' },
        { status: 404 }
      );
    }

    if (!template.htmlTemplate) {
      return NextResponse.json(
        { error: 'Template does not have HTML content. Please analyze the template first.' },
        { status: 400 }
      );
    }

    // Generate sample data for common Arabic proposal placeholders
    const sampleData = generateDefaultSampleData();

    // Generate HTML with sample data
    const result = await htmlTemplateGeneratorService.generateHTML(
      template,
      sampleData,
      { format: 'html', includeStyles: true, responsive: true }
    );

    return NextResponse.json({
      success: true,
      data: {
        html: result.html,
        sampleData,
        metadata: result.metadata,
        template: {
          id: template.id,
          name: template.name,
          templateType: template.templateType
        }
      },
      message: 'Template preview generated successfully'
    });

  } catch (error) {
    console.error('Generate template preview error:', error);
    
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to generate template preview' },
      { status: 500 }
    );
  }
});

/**
 * Generate default sample data for Arabic proposal templates
 */
function generateDefaultSampleData(): Record<string, any> {
  return {
    Client_Name: 'شركة العميل المحدودة',
    Client_Name_Arabic: 'شركة العميل المحدودة',
    Project_Name: 'مشروع تطوير النظام الإلكتروني',
    Project_Name_Arabic: 'مشروع تطوير النظام الإلكتروني',
    Company_Name: 'شركة التقنية المتقدمة',
    Company_Name_Arabic: 'شركة التقنية المتقدمة',
    Competition_Number: 'RFP-2024-001',
    Approver_Name: 'أحمد محمد السعيد',
    Approver_Title: 'مدير المشاريع',
    Project_Duration_Months: '12',
    Submission_Date_Hijri: '15 جمادى الأولى 1446هـ',
    Submission_Date_Gregorian: '15 ديسمبر 2024م',
    Company_Logo: 'شعار الشركة',
    Contact_Table: `
      <table class="w-full border-collapse border border-gray-300">
        <thead>
          <tr class="bg-gray-100">
            <th class="border border-gray-300 p-2">الاسم</th>
            <th class="border border-gray-300 p-2">المنصب</th>
            <th class="border border-gray-300 p-2">الاتصال</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td class="border border-gray-300 p-2">أحمد محمد</td>
            <td class="border border-gray-300 p-2">مدير المشروع</td>
            <td class="border border-gray-300 p-2"><EMAIL></td>
          </tr>
          <tr>
            <td class="border border-gray-300 p-2">فاطمة علي</td>
            <td class="border border-gray-300 p-2">مطورة أنظمة</td>
            <td class="border border-gray-300 p-2"><EMAIL></td>
          </tr>
        </tbody>
      </table>
    `,
    Team_CVs: `
      <div class="space-y-4">
        <div class="bg-gray-50 p-4 rounded">
          <h4 class="font-bold">أحمد محمد السعيد - مدير المشروع</h4>
          <p>خبرة 10 سنوات في إدارة المشاريع التقنية</p>
        </div>
        <div class="bg-gray-50 p-4 rounded">
          <h4 class="font-bold">فاطمة علي أحمد - مطورة أنظمة</h4>
          <p>خبرة 7 سنوات في تطوير الأنظمة الإلكترونية</p>
        </div>
      </div>
    `,
    Project_Timeline: '12 شهرًا مقسمة على 4 مراحل',
    Technical_Specifications: 'مواصفات تقنية متقدمة باستخدام أحدث التقنيات',
    Financial_Details: 'تفاصيل مالية شاملة مع خطة دفع مرنة'
  };
}
