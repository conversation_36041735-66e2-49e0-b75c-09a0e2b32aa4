'use client';

import { Fragment } from 'react';
import { Menu, Transition } from '@headlessui/react';
import {
  UserCircleIcon,
  ChevronDownIcon,
  UserIcon,
  Cog6ToothIcon,
  ArrowRightOnRectangleIcon
} from '@heroicons/react/24/outline';
import { useSession, signOut } from 'next-auth/react';
import { useTranslation } from '@/components/providers/locale-provider';
import { cn } from '@/lib/utils';
import { ContactAvatar } from '@/components/ui/contact-avatar';
import { getInitialsFromName } from '@/lib/avatar-utils';
import { ThemeToggle } from '@/components/ui/theme-toggle';

// Utility function to parse full name into first and last name
function parseFullName(fullName: string): { firstName: string; lastName?: string } {
  const parts = fullName.trim().split(/\s+/);
  if (parts.length === 1) {
    return { firstName: parts[0] };
  }
  return {
    firstName: parts[0],
    lastName: parts.slice(1).join(' ')
  };
}

export function UserDropdown() {
  const { t } = useTranslation();
  const { data: session } = useSession();

  const handleSignOut = async () => {
    try {
      await signOut({
        callbackUrl: '/auth/signin',
        redirect: true
      });
    } catch (error) {
      console.error('Error signing out:', error);
    }
  };

  const handleProfileClick = () => {
    // TODO: Navigate to profile page
    console.log('Profile clicked');
  };

  const handleSettingsClick = () => {
    // TODO: Navigate to settings page
    console.log('Settings clicked');
  };

  // Don't render if no session
  if (!session?.user) {
    return null;
  }

  const user = session.user;
  const currentTenant = user.tenants?.[0]; // Get the first tenant or current tenant

  // Parse user name for avatar
  const { firstName, lastName } = parseFullName(user.name);

  return (
    <Menu as="div" className="relative">
      <div>
        <Menu.Button className="relative flex max-w-xs items-center rounded-full bg-white dark:bg-gray-800 text-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 lg:p-2 lg:rounded-md lg:hover:bg-gray-50 dark:lg:hover:bg-gray-700 transition-colors">
          <div className="flex items-center space-x-3">
            {/* Avatar */}
            <div className="relative">
              <ContactAvatar
                firstName={firstName}
                lastName={lastName}
                avatarUrl={user.image}
                size="md"
              />
              <div className="absolute -bottom-1 -right-1 h-3 w-3 rounded-full bg-green-400 border-2 border-white dark:border-gray-800"></div>
            </div>

            {/* User Info (hidden on mobile) */}
            <div className="hidden lg:block">
              <div className="flex items-center space-x-2">
                <div className="text-left">
                  <p className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    {user.name}
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    {currentTenant?.role || 'User'} • {currentTenant?.name || 'No Tenant'}
                  </p>
                </div>
                <ChevronDownIcon className="h-4 w-4 text-gray-400" aria-hidden="true" />
              </div>
            </div>
          </div>
        </Menu.Button>
      </div>

      <Transition
        as={Fragment}
        enter="transition ease-out duration-100"
        enterFrom="transform opacity-0 scale-95"
        enterTo="transform opacity-100 scale-100"
        leave="transition ease-in duration-75"
        leaveFrom="transform opacity-100 scale-100"
        leaveTo="transform opacity-0 scale-95"
      >
        <Menu.Items className="absolute right-0 z-10 mt-2 w-64 origin-top-right rounded-md bg-white dark:bg-gray-800 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
          <div className="py-1">
            {/* User Info Header */}
            <div className="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
              <div className="flex items-center space-x-3">
                <div className="relative">
                  <ContactAvatar
                    firstName={firstName}
                    lastName={lastName}
                    avatarUrl={user.image}
                    size="lg"
                  />
                  <div className="absolute -bottom-1 -right-1 h-3 w-3 rounded-full bg-green-400 border-2 border-white dark:border-gray-800"></div>
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                    {user.name}
                  </p>
                  <p className="text-sm text-gray-500 dark:text-gray-400 truncate">
                    {user.email}
                  </p>
                  <p className="text-xs text-gray-400 dark:text-gray-500">
                    {currentTenant?.role || 'User'} • {currentTenant?.name || 'No Tenant'}
                  </p>
                </div>
              </div>
            </div>

            {/* Menu Items */}
            <div className="py-1">
              <Menu.Item>
                {({ active }) => (
                  <button
                    onClick={handleProfileClick}
                    className={cn(
                      active 
                        ? 'bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-white' 
                        : 'text-gray-700 dark:text-gray-300',
                      'group flex w-full items-center px-4 py-2 text-sm transition-colors'
                    )}
                  >
                    <UserIcon className="mr-3 h-5 w-5 text-gray-400 group-hover:text-gray-500" aria-hidden="true" />
                    Your Profile
                  </button>
                )}
              </Menu.Item>

              <Menu.Item>
                {({ active }) => (
                  <button
                    onClick={handleSettingsClick}
                    className={cn(
                      active 
                        ? 'bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-white' 
                        : 'text-gray-700 dark:text-gray-300',
                      'group flex w-full items-center px-4 py-2 text-sm transition-colors'
                    )}
                  >
                    <Cog6ToothIcon className="mr-3 h-5 w-5 text-gray-400 group-hover:text-gray-500" aria-hidden="true" />
                    Settings
                  </button>
                )}
              </Menu.Item>

              <div className="px-4 py-2">
                <ThemeToggle variant="dropdown" showLabel />
              </div>
            </div>

            {/* Sign Out */}
            <div className="border-t border-gray-200 dark:border-gray-700">
              <Menu.Item>
                {({ active }) => (
                  <button
                    onClick={handleSignOut}
                    className={cn(
                      active 
                        ? 'bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-white' 
                        : 'text-gray-700 dark:text-gray-300',
                      'group flex w-full items-center px-4 py-2 text-sm transition-colors'
                    )}
                  >
                    <ArrowRightOnRectangleIcon className="mr-3 h-5 w-5 text-gray-400 group-hover:text-gray-500" aria-hidden="true" />
                    Sign out
                  </button>
                )}
              </Menu.Item>
            </div>
          </div>
        </Menu.Items>
      </Transition>
    </Menu>
  );
}
