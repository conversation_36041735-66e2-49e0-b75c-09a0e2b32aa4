import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { hasPermission } from '@/lib/permissions';
import { PermissionResource, PermissionAction } from '@/types';
import { aiProposalService } from '@/services/ai-proposal-generation';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';

// Request validation schema
const regenerateSectionSchema = z.object({
  sectionId: z.string().min(1, 'Section ID is required'),
  customPrompt: z.string().optional(),
  preserveManualEdits: z.boolean().default(false),
  locale: z.enum(['en', 'ar']).default('en')
});

/**
 * POST /api/proposals/[id]/regenerate-section
 * Regenerate a specific section using AI
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user's current tenant
    const currentTenant = session.user.tenants?.[0];
    if (!currentTenant) {
      return NextResponse.json({ error: 'No tenant access' }, { status: 403 });
    }

    // Check permission for proposal updating
    const hasAccess = await hasPermission(
      PermissionResource.PROPOSALS,
      PermissionAction.UPDATE,
      currentTenant.id
    );

    if (!hasAccess) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const proposalId = params.id;
    const body = await request.json();
    
    // Validate request data
    const validatedData = regenerateSectionSchema.parse(body);

    // Verify proposal exists and belongs to tenant
    const proposal = await prisma.proposal.findFirst({
      where: {
        id: proposalId,
        tenantId: currentTenant.id
      }
    });

    if (!proposal) {
      return NextResponse.json(
        { error: 'Proposal not found or access denied' },
        { status: 404 }
      );
    }

    // Check if proposal is in a state that allows regeneration
    if (proposal.generationStatus === 'generating') {
      return NextResponse.json(
        { error: 'Cannot regenerate section while proposal is being generated' },
        { status: 409 }
      );
    }

    // Verify section exists and belongs to the proposal
    const section = await prisma.proposalSection.findFirst({
      where: {
        id: validatedData.sectionId,
        proposalId,
        tenantId: currentTenant.id
      }
    });

    if (!section) {
      return NextResponse.json(
        { error: 'Section not found or access denied' },
        { status: 404 }
      );
    }

    // Store original content if preserving manual edits
    let originalContent = null;
    if (validatedData.preserveManualEdits && !section.isAiGenerated) {
      originalContent = section.content;
    }

    // Get file contexts for the proposal
    const fileContexts = await getProposalFileContexts(proposalId, currentTenant.id);

    // Mark section as being regenerated
    await prisma.proposalSection.update({
      where: { id: validatedData.sectionId },
      data: {
        status: 'draft'
      }
    });

    // Regenerate the section (use proposal's locale if not provided)
    const localeToUse = validatedData.locale || (proposal.locale as 'en' | 'ar') || 'en';
    const result = await aiProposalService.generateSection(
      {
        proposalId,
        sectionType: section.sectionType as any,
        customPrompt: validatedData.customPrompt,
        regenerate: true
      },
      currentTenant.id,
      session.user.id,
      fileContexts,
      localeToUse
    );

    // If preserving manual edits, append original content
    let finalContent = result.content;
    if (originalContent && validatedData.preserveManualEdits) {
      finalContent = `${result.content}\n\n--- Manual Edits (Preserved) ---\n${originalContent}`;
    }

    // Update the section with new content
    const updatedSection = await prisma.proposalSection.update({
      where: { id: validatedData.sectionId },
      data: {
        content: finalContent,
        wordCount: countWords(finalContent),
        tokensUsed: result.tokensUsed,
        status: 'generated',
        generatedAt: new Date(),
        updatedAt: new Date()
      },
      include: {
        createdBy: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true
          }
        }
      }
    });

    return NextResponse.json({
      success: true,
      data: {
        section: updatedSection,
        regenerationStats: {
          tokensUsed: result.tokensUsed,
          wordCount: result.wordCount,
          confidence: result.confidence,
          preservedManualEdits: validatedData.preserveManualEdits && !!originalContent
        }
      },
      message: 'Section regenerated successfully'
    });

  } catch (error) {
    console.error('Error regenerating section:', error);
    
    // Reset section status on error
    if (error instanceof Error && error.message.includes('sectionId')) {
      try {
        const body = await request.json();
        const { sectionId } = body;
        await prisma.proposalSection.update({
          where: { id: sectionId },
          data: { status: 'generated' }
        });
      } catch (resetError) {
        console.error('Error resetting section status:', resetError);
      }
    }
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          error: 'Invalid request data', 
          details: error.errors.map(e => ({
            field: e.path.join('.'),
            message: e.message
          }))
        },
        { status: 400 }
      );
    }

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * Helper function to get file contexts for a proposal
 */
async function getProposalFileContexts(proposalId: string, tenantId: string) {
  const proposal = await prisma.proposal.findFirst({
    where: { id: proposalId, tenantId }
  });

  if (!proposal || !proposal.geminiFileUris) {
    return [];
  }

  const geminiFileUris = proposal.geminiFileUris as Record<string, string>;
  const selectedFileIds = Array.isArray(proposal.selectedFileIds) 
    ? proposal.selectedFileIds as string[]
    : [];

  const documents = await prisma.document.findMany({
    where: {
      id: { in: selectedFileIds },
      tenantId
    }
  });

  return documents.map(doc => ({
    fileUri: geminiFileUris[doc.id],
    documentId: doc.id,
    name: doc.name
  })).filter(ctx => ctx.fileUri);
}

/**
 * Helper function to count words
 */
function countWords(text: string): number {
  return text.trim().split(/\s+/).length;
}

/**
 * @swagger
 * /api/proposals/{id}/regenerate-section:
 *   post:
 *     summary: Regenerate proposal section
 *     description: Use AI to regenerate a specific section of a proposal with optional custom prompt
 *     tags: [Proposals]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Proposal ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - sectionId
 *             properties:
 *               sectionId:
 *                 type: string
 *                 description: ID of the section to regenerate
 *               customPrompt:
 *                 type: string
 *                 description: Optional custom prompt to guide AI regeneration
 *               preserveManualEdits:
 *                 type: boolean
 *                 description: Whether to preserve any manual edits made to the section
 *                 default: false
 *     responses:
 *       200:
 *         description: Section regenerated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     section:
 *                       type: object
 *                       description: Updated section data
 *                     regenerationStats:
 *                       type: object
 *                       properties:
 *                         tokensUsed:
 *                           type: number
 *                         wordCount:
 *                           type: number
 *                         confidence:
 *                           type: number
 *                         preservedManualEdits:
 *                           type: boolean
 *                 message:
 *                   type: string
 *       400:
 *         description: Invalid request data
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - insufficient permissions
 *       404:
 *         description: Proposal or section not found
 *       409:
 *         description: Conflict - proposal is being generated
 *       500:
 *         description: Internal server error
 */
