import React, { useState, useEffect, useRef } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Avatar, AvatarFallback, AvatarImage } from '../ui/avatar';
import { Badge } from '../ui/badge';
import { ScrollArea } from '../ui/scroll-area';
import { Separator } from '../ui/separator';
import { 
  Send, 
  Paperclip, 
  Smile, 
  MoreVertical, 
  Users, 
  Hash,
  Lock,
  Globe
} from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';

interface User {
  id: string;
  firstName: string | null;
  lastName: string | null;
  email: string;
  avatarUrl: string | null;
}

interface ChatMessage {
  id: string;
  content: string;
  messageType: string;
  createdAt: string;
  isEdited: boolean;
  user: User;
  reactions?: Record<string, string[]>;
  threadCount: number;
  parentMessage?: ChatMessage;
}

interface ChatChannel {
  id: string;
  name: string;
  description?: string;
  type: string;
  isPrivate: boolean;
  createdBy: User;
  members: Array<{
    id: string;
    role: string;
    user: User;
  }>;
  _count: {
    messages: number;
    members: number;
  };
}

interface ChatChannelProps {
  channel: ChatChannel;
  onSendMessage: (content: string, parentMessageId?: string) => void;
  onLoadMessages: (options?: { limit?: number; offset?: number }) => void;
  messages: ChatMessage[];
  loading: boolean;
  currentUserId: string;
}

export default function ChatChannel({
  channel,
  onSendMessage,
  onLoadMessages,
  messages,
  loading,
  currentUserId,
}: ChatChannelProps) {
  const [newMessage, setNewMessage] = useState('');
  const [replyingTo, setReplyingTo] = useState<ChatMessage | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  useEffect(() => {
    onLoadMessages();
  }, [channel.id]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const handleSendMessage = () => {
    if (!newMessage.trim()) return;

    onSendMessage(newMessage, replyingTo?.id);
    setNewMessage('');
    setReplyingTo(null);
    inputRef.current?.focus();
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const getChannelIcon = () => {
    switch (channel.type) {
      case 'private':
      case 'direct':
        return <Lock className="h-4 w-4" />;
      case 'project':
      case 'team':
        return <Users className="h-4 w-4" />;
      default:
        return <Hash className="h-4 w-4" />;
    }
  };

  const getUserDisplayName = (user: User) => {
    return user.firstName && user.lastName 
      ? `${user.firstName} ${user.lastName}`
      : user.email;
  };

  const getUserInitials = (user: User) => {
    if (user.firstName && user.lastName) {
      return `${user.firstName[0]}${user.lastName[0]}`.toUpperCase();
    }
    return user.email[0].toUpperCase();
  };

  const formatMessageTime = (timestamp: string) => {
    return formatDistanceToNow(new Date(timestamp), { addSuffix: true });
  };

  return (
    <div className="flex flex-col h-full">
      {/* Channel Header */}
      <div className="flex items-center justify-between p-4 border-b">
        <div className="flex items-center space-x-3">
          <div className="flex items-center space-x-2">
            {getChannelIcon()}
            <h2 className="text-lg font-semibold">{channel.name}</h2>
            {channel.isPrivate && (
              <Badge variant="secondary" className="text-xs">
                Private
              </Badge>
            )}
          </div>
          {channel.description && (
            <span className="text-sm text-muted-foreground">
              {channel.description}
            </span>
          )}
        </div>
        
        <div className="flex items-center space-x-2">
          <Badge variant="outline" className="text-xs">
            {channel._count.members} members
          </Badge>
          <Button variant="ghost" size="sm">
            <MoreVertical className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Messages Area */}
      <ScrollArea className="flex-1 p-4">
        <div className="space-y-4">
          {loading && messages.length === 0 ? (
            <div className="flex justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            </div>
          ) : (
            messages.map((message) => (
              <div key={message.id} className="flex space-x-3">
                <Avatar className="h-8 w-8">
                  <AvatarImage src={message.user.avatarUrl || undefined} />
                  <AvatarFallback className="text-xs">
                    {getUserInitials(message.user)}
                  </AvatarFallback>
                </Avatar>
                
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-2">
                    <span className="font-medium text-sm">
                      {getUserDisplayName(message.user)}
                    </span>
                    <span className="text-xs text-muted-foreground">
                      {formatMessageTime(message.createdAt)}
                    </span>
                    {message.isEdited && (
                      <Badge variant="outline" className="text-xs">
                        edited
                      </Badge>
                    )}
                  </div>
                  
                  {message.parentMessage && (
                    <div className="mt-1 p-2 bg-muted rounded text-sm border-l-2 border-primary">
                      <div className="font-medium text-xs text-muted-foreground">
                        Replying to {getUserDisplayName(message.parentMessage.user)}
                      </div>
                      <div className="truncate">
                        {message.parentMessage.content}
                      </div>
                    </div>
                  )}
                  
                  <div className="mt-1 text-sm break-words">
                    {message.content}
                  </div>
                  
                  {message.reactions && Object.keys(message.reactions).length > 0 && (
                    <div className="flex flex-wrap gap-1 mt-2">
                      {Object.entries(message.reactions).map(([emoji, userIds]) => (
                        <Badge
                          key={emoji}
                          variant="outline"
                          className="text-xs cursor-pointer hover:bg-muted"
                        >
                          {emoji} {userIds.length}
                        </Badge>
                      ))}
                    </div>
                  )}
                  
                  <div className="flex items-center space-x-2 mt-1">
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-6 px-2 text-xs"
                      onClick={() => setReplyingTo(message)}
                    >
                      Reply
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-6 px-2 text-xs"
                    >
                      <Smile className="h-3 w-3" />
                    </Button>
                    {message.threadCount > 0 && (
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-6 px-2 text-xs"
                      >
                        {message.threadCount} replies
                      </Button>
                    )}
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
        <div ref={messagesEndRef} />
      </ScrollArea>

      {/* Reply Preview */}
      {replyingTo && (
        <div className="px-4 py-2 bg-muted border-t">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <span className="text-xs text-muted-foreground">
                Replying to {getUserDisplayName(replyingTo.user)}
              </span>
              <span className="text-xs truncate max-w-xs">
                {replyingTo.content}
              </span>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setReplyingTo(null)}
              className="h-6 w-6 p-0"
            >
              ×
            </Button>
          </div>
        </div>
      )}

      {/* Message Input */}
      <div className="p-4 border-t">
        <div className="flex items-center space-x-2">
          <div className="flex-1 relative">
            <Input
              ref={inputRef}
              value={newMessage}
              onChange={(e) => setNewMessage(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder={`Message #${channel.name}`}
              className="pr-20"
            />
            <div className="absolute right-2 top-1/2 transform -translate-y-1/2 flex space-x-1">
              <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                <Paperclip className="h-3 w-3" />
              </Button>
              <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                <Smile className="h-3 w-3" />
              </Button>
            </div>
          </div>
          <Button 
            onClick={handleSendMessage}
            disabled={!newMessage.trim()}
            size="sm"
          >
            <Send className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  );
}
