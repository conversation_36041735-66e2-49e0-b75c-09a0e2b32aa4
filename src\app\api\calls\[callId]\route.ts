import { NextResponse } from 'next/server';
import { z } from 'zod';
import { withPermission, AuthenticatedRequest } from '@/lib/permission-middleware';
import { scheduledCallService, updateScheduledCallSchema } from '@/services/scheduled-call-service';
import { PermissionAction, PermissionResource } from '@/types';

/**
 * GET /api/calls/[callId]
 * Get a specific scheduled call by ID
 */
export const GET = withPermission({
  resource: PermissionResource.CALLS,
  action: PermissionAction.READ,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest) => {
  try {
    const url = new URL(req.url);
    const pathSegments = url.pathname.split('/');
    const callId = pathSegments[pathSegments.indexOf('calls') + 1];

    const scheduledCall = await scheduledCallService.getScheduledCall(callId, req.tenantId);

    if (!scheduledCall) {
      return NextResponse.json(
        { error: 'Scheduled call not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: { scheduledCall },
      message: 'Scheduled call retrieved successfully'
    });

  } catch (error) {
    console.error('Get scheduled call error:', error);

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
});

/**
 * PUT /api/calls/[callId]
 * Update a specific scheduled call
 */
export const PUT = withPermission({
  resource: PermissionResource.CALLS,
  action: PermissionAction.UPDATE,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest) => {
  try {
    const url = new URL(req.url);
    const pathSegments = url.pathname.split('/');
    const callId = pathSegments[pathSegments.indexOf('calls') + 1];
    const body = await req.json();

    const scheduledCall = await scheduledCallService.updateScheduledCall(
      callId,
      req.tenantId,
      body,
      req.userId
    );

    return NextResponse.json({
      success: true,
      data: { scheduledCall },
      message: 'Scheduled call updated successfully'
    });

  } catch (error) {
    console.error('Update scheduled call error:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          error: 'Validation failed',
          details: error.errors.map(e => ({
            field: e.path.join('.'),
            message: e.message
          }))
        },
        { status: 400 }
      );
    }

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
});

/**
 * DELETE /api/calls/[callId]
 * Delete a specific scheduled call
 */
export const DELETE = withPermission({
  resource: PermissionResource.CALLS,
  action: PermissionAction.DELETE,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest) => {
  try {
    const url = new URL(req.url);
    const pathSegments = url.pathname.split('/');
    const callId = pathSegments[pathSegments.indexOf('calls') + 1];

    await scheduledCallService.deleteScheduledCall(callId, req.tenantId);

    return NextResponse.json({
      success: true,
      message: 'Scheduled call deleted successfully'
    });

  } catch (error) {
    console.error('Delete scheduled call error:', error);

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
});

/**
 * PATCH /api/calls/[callId]/complete
 * Mark a call as completed
 */
export const PATCH = withPermission({
  resource: PermissionResource.CALLS,
  action: PermissionAction.UPDATE,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest) => {
  try {
    const url = new URL(req.url);
    const pathSegments = url.pathname.split('/');
    const callId = pathSegments[pathSegments.indexOf('calls') + 1];
    
    // Check if this is a complete action
    if (pathSegments.includes('complete')) {
      const body = await req.json();
      const { callNotes, followUpRequired, nextCallDate } = body;

      const scheduledCall = await scheduledCallService.completeCall(
        callId,
        req.tenantId,
        callNotes,
        followUpRequired,
        nextCallDate
      );

      return NextResponse.json({
        success: true,
        data: { scheduledCall },
        message: 'Call marked as completed successfully'
      });
    }

    // Regular update
    const body = await req.json();
    const scheduledCall = await scheduledCallService.updateScheduledCall(
      callId,
      req.tenantId,
      body,
      req.userId
    );

    return NextResponse.json({
      success: true,
      data: { scheduledCall },
      message: 'Scheduled call updated successfully'
    });

  } catch (error) {
    console.error('Update/complete scheduled call error:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          error: 'Validation failed',
          details: error.errors.map(e => ({
            field: e.path.join('.'),
            message: e.message
          }))
        },
        { status: 400 }
      );
    }

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
});
