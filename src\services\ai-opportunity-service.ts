import { aiService } from './ai-service';
import { OpportunityWithRelations } from './opportunity-management';
import { prisma } from '@/lib/prisma';

export interface OpportunityScoring {
  totalScore: number;
  confidence: number;
  factors: {
    dealSize: { score: number; weight: number; reasoning: string };
    timeline: { score: number; weight: number; reasoning: string };
    authority: { score: number; weight: number; reasoning: string };
    need: { score: number; weight: number; reasoning: string };
    competition: { score: number; weight: number; reasoning: string };
  };
  recommendations: string[];
  riskFactors: string[];
  nextActions: string[];
}

export interface DealForecast {
  predictedCloseDate: string;
  confidence: number;
  probabilityAdjustment: number;
  reasoning: string;
  factors: {
    historical: string;
    seasonal: string;
    competitive: string;
    internal: string;
  };
}

export interface PipelineForecast {
  totalPredictedRevenue: number;
  confidence: number;
  timeframe: string;
  breakdown: Array<{
    stage: string;
    predictedRevenue: number;
    probability: number;
    count: number;
  }>;
  trends: {
    velocity: number;
    conversionRates: Record<string, number>;
    seasonality: string;
  };
  recommendations: string[];
}

export interface CompetitiveAnalysis {
  competitors: Array<{
    name: string;
    strength: 'low' | 'medium' | 'high';
    advantages: string[];
    weaknesses: string[];
  }>;
  positioning: {
    strengths: string[];
    weaknesses: string[];
    differentiators: string[];
  };
  strategy: {
    recommendations: string[];
    messaging: string[];
    pricing: string[];
  };
}

export class AIOpportunityService {
  /**
   * Score an opportunity using AI analysis
   */
  async scoreOpportunity(
    opportunity: OpportunityWithRelations,
    tenantId: string,
    locale: 'en' | 'ar' = 'en'
  ): Promise<OpportunityScoring> {
    try {
      // Get historical data for context
      const historicalData = await this.getHistoricalContext(tenantId);
      
      const prompt = this.buildScoringPrompt(opportunity, historicalData, locale);
      
      const response = await aiService.generateResponse({
        prompt,
        tenantId,
        userId: 'system',
        feature: 'opportunity_scoring'
      }, {
        maxTokens: 1000,
        temperature: 0.3
      });

      return this.parseScoringResponse(response.content);
    } catch (error) {
      console.error('AI opportunity scoring error:', error);
      return this.getFallbackScoring(opportunity);
    }
  }

  /**
   * Predict deal close date and probability
   */
  async predictDealOutcome(
    opportunity: OpportunityWithRelations,
    tenantId: string,
    locale: 'en' | 'ar' = 'en'
  ): Promise<DealForecast> {
    try {
      const historicalData = await this.getHistoricalContext(tenantId);
      const stageHistory = await this.getStageHistory(opportunity.id, tenantId);
      
      const prompt = this.buildForecastPrompt(opportunity, historicalData, stageHistory, locale);
      
      const response = await aiService.generateResponse({
        prompt,
        tenantId,
        userId: 'system',
        feature: 'deal_forecast'
      }, {
        maxTokens: 800,
        temperature: 0.2
      });

      return this.parseForecastResponse(response.content);
    } catch (error) {
      console.error('AI deal forecast error:', error);
      return this.getFallbackForecast(opportunity);
    }
  }

  /**
   * Generate pipeline forecast
   */
  async generatePipelineForecast(
    tenantId: string,
    timeframe: '30d' | '60d' | '90d' | '1y' = '90d',
    locale: 'en' | 'ar' = 'en'
  ): Promise<PipelineForecast> {
    try {
      const pipelineData = await this.getPipelineData(tenantId);
      const historicalData = await this.getHistoricalContext(tenantId);
      
      const prompt = this.buildPipelineForecastPrompt(pipelineData, historicalData, timeframe, locale);
      
      const response = await aiService.generateResponse({
        prompt,
        tenantId,
        userId: 'system',
        feature: 'pipeline_forecast'
      }, {
        maxTokens: 1200,
        temperature: 0.2
      });

      return this.parsePipelineForecastResponse(response.content);
    } catch (error) {
      console.error('AI pipeline forecast error:', error);
      return this.getFallbackPipelineForecast(tenantId);
    }
  }

  /**
   * Analyze competition for an opportunity
   */
  async analyzeCompetition(
    opportunity: OpportunityWithRelations,
    tenantId: string,
    locale: 'en' | 'ar' = 'en'
  ): Promise<CompetitiveAnalysis> {
    try {
      const competitorInfo = opportunity.competitorInfo as any || {};
      const industryContext = opportunity.company?.industry || 'general';
      
      const prompt = this.buildCompetitiveAnalysisPrompt(opportunity, competitorInfo, industryContext, locale);
      
      const response = await aiService.generateResponse({
        prompt,
        tenantId,
        userId: 'system',
        feature: 'competitive_analysis'
      }, {
        maxTokens: 1000,
        temperature: 0.4
      });

      return this.parseCompetitiveAnalysisResponse(response.content);
    } catch (error) {
      console.error('AI competitive analysis error:', error);
      return this.getFallbackCompetitiveAnalysis();
    }
  }

  /**
   * Get next best actions for an opportunity
   */
  async getNextBestActions(
    opportunity: OpportunityWithRelations,
    tenantId: string,
    locale: 'en' | 'ar' = 'en'
  ): Promise<string[]> {
    try {
      const recentActivities = await this.getRecentActivities(opportunity.id, tenantId);
      const stageInfo = await this.getCurrentStageInfo(opportunity.stage, tenantId);
      
      const prompt = this.buildNextActionsPrompt(opportunity, recentActivities, stageInfo, locale);
      
      const response = await aiService.generateResponse({
        prompt,
        tenantId,
        userId: 'system',
        feature: 'next_actions'
      }, {
        maxTokens: 600,
        temperature: 0.3
      });

      return this.parseNextActionsResponse(response.content);
    } catch (error) {
      console.error('AI next actions error:', error);
      return this.getFallbackNextActions(opportunity);
    }
  }

  // Private helper methods
  private buildScoringPrompt(opportunity: OpportunityWithRelations, historicalData: any, locale: 'en' | 'ar'): string {
    const isArabic = locale === 'ar';
    
    return `${isArabic ? 'قم بتحليل وتقييم هذه الفرصة التجارية' : 'Analyze and score this sales opportunity'}:

${isArabic ? 'معلومات الفرصة' : 'Opportunity Details'}:
- ${isArabic ? 'العنوان' : 'Title'}: ${opportunity.title}
- ${isArabic ? 'القيمة' : 'Value'}: ${opportunity.value ? `$${opportunity.value}` : isArabic ? 'غير محدد' : 'Not specified'}
- ${isArabic ? 'المرحلة' : 'Stage'}: ${opportunity.stage}
- ${isArabic ? 'الاحتمالية' : 'Probability'}: ${opportunity.probability}%
- ${isArabic ? 'تاريخ الإغلاق المتوقع' : 'Expected Close Date'}: ${opportunity.expectedCloseDate || (isArabic ? 'غير محدد' : 'Not specified')}
- ${isArabic ? 'الأولوية' : 'Priority'}: ${(opportunity as any).priority || 'medium'}
- ${isArabic ? 'الشركة' : 'Company'}: ${opportunity.company?.name || (isArabic ? 'غير محدد' : 'Not specified')}
- ${isArabic ? 'الصناعة' : 'Industry'}: ${opportunity.company?.industry || (isArabic ? 'غير محدد' : 'Not specified')}

${isArabic ? 'قم بتقييم الفرصة على أساس العوامل التالية (0-100 لكل عامل)' : 'Score the opportunity based on these factors (0-100 for each factor)'}:
1. ${isArabic ? 'حجم الصفقة' : 'Deal Size'} (${isArabic ? 'الوزن' : 'Weight'}: 25%)
2. ${isArabic ? 'الجدول الزمني' : 'Timeline'} (${isArabic ? 'الوزن' : 'Weight'}: 20%)
3. ${isArabic ? 'السلطة/صانع القرار' : 'Authority/Decision Maker'} (${isArabic ? 'الوزن' : 'Weight'}: 20%)
4. ${isArabic ? 'الحاجة/الألم' : 'Need/Pain'} (${isArabic ? 'الوزن' : 'Weight'}: 20%)
5. ${isArabic ? 'المنافسة' : 'Competition'} (${isArabic ? 'الوزن' : 'Weight'}: 15%)

${isArabic ? 'قدم النتيجة بتنسيق JSON مع' : 'Provide the result in JSON format with'}:
- totalScore: ${isArabic ? 'النتيجة الإجمالية' : 'overall score'}
- confidence: ${isArabic ? 'مستوى الثقة' : 'confidence level'}
- factors: ${isArabic ? 'تفاصيل كل عامل مع النتيجة والتبرير' : 'each factor with score and reasoning'}
- recommendations: ${isArabic ? 'التوصيات' : 'recommendations array'}
- riskFactors: ${isArabic ? 'عوامل المخاطر' : 'risk factors array'}
- nextActions: ${isArabic ? 'الإجراءات التالية المقترحة' : 'suggested next actions array'}`;
  }

  private buildForecastPrompt(opportunity: OpportunityWithRelations, historicalData: any, stageHistory: any[], locale: 'en' | 'ar'): string {
    const isArabic = locale === 'ar';
    
    return `${isArabic ? 'تنبؤ بنتيجة هذه الصفقة' : 'Predict the outcome of this deal'}:

${isArabic ? 'معلومات الفرصة' : 'Opportunity Details'}:
- ${isArabic ? 'العنوان' : 'Title'}: ${opportunity.title}
- ${isArabic ? 'المرحلة الحالية' : 'Current Stage'}: ${opportunity.stage}
- ${isArabic ? 'القيمة' : 'Value'}: ${opportunity.value || (isArabic ? 'غير محدد' : 'Not specified')}
- ${isArabic ? 'تاريخ الإغلاق المتوقع' : 'Expected Close Date'}: ${opportunity.expectedCloseDate || (isArabic ? 'غير محدد' : 'Not specified')}

${isArabic ? 'تاريخ تطور المراحل' : 'Stage History'}: ${stageHistory.length} ${isArabic ? 'تغييرات' : 'changes'}

${isArabic ? 'قدم تنبؤ بتنسيق JSON مع' : 'Provide forecast in JSON format with'}:
- predictedCloseDate: ${isArabic ? 'تاريخ الإغلاق المتوقع' : 'predicted close date'}
- confidence: ${isArabic ? 'مستوى الثقة (0-100)' : 'confidence level (0-100)'}
- probabilityAdjustment: ${isArabic ? 'تعديل الاحتمالية المقترح' : 'suggested probability adjustment'}
- reasoning: ${isArabic ? 'التبرير' : 'reasoning'}
- factors: ${isArabic ? 'العوامل المؤثرة' : 'influencing factors'}`;
  }

  private buildPipelineForecastPrompt(pipelineData: any, historicalData: any, timeframe: string, locale: 'en' | 'ar'): string {
    const isArabic = locale === 'ar';
    
    return `${isArabic ? 'قم بتوليد توقعات خط الأنابيب للفترة' : 'Generate pipeline forecast for'} ${timeframe}:

${isArabic ? 'بيانات خط الأنابيب الحالي' : 'Current Pipeline Data'}: ${JSON.stringify(pipelineData, null, 2)}

${isArabic ? 'قدم التوقعات بتنسيق JSON مع' : 'Provide forecast in JSON format with'}:
- totalPredictedRevenue: ${isArabic ? 'إجمالي الإيرادات المتوقعة' : 'total predicted revenue'}
- confidence: ${isArabic ? 'مستوى الثقة' : 'confidence level'}
- breakdown: ${isArabic ? 'تفصيل حسب المرحلة' : 'breakdown by stage'}
- trends: ${isArabic ? 'الاتجاهات' : 'trends'}
- recommendations: ${isArabic ? 'التوصيات' : 'recommendations'}`;
  }

  private buildCompetitiveAnalysisPrompt(opportunity: OpportunityWithRelations, competitorInfo: any, industry: string, locale: 'en' | 'ar'): string {
    const isArabic = locale === 'ar';
    
    return `${isArabic ? 'حلل المنافسة لهذه الفرصة' : 'Analyze competition for this opportunity'}:

${isArabic ? 'الصناعة' : 'Industry'}: ${industry}
${isArabic ? 'معلومات المنافسين' : 'Competitor Info'}: ${JSON.stringify(competitorInfo)}

${isArabic ? 'قدم التحليل بتنسيق JSON مع' : 'Provide analysis in JSON format with'}:
- competitors: ${isArabic ? 'قائمة المنافسين مع نقاط القوة والضعف' : 'competitor list with strengths/weaknesses'}
- positioning: ${isArabic ? 'موقعنا التنافسي' : 'our competitive positioning'}
- strategy: ${isArabic ? 'الاستراتيجية المقترحة' : 'recommended strategy'}`;
  }

  private buildNextActionsPrompt(opportunity: OpportunityWithRelations, activities: any[], stageInfo: any, locale: 'en' | 'ar'): string {
    const isArabic = locale === 'ar';

    return `${isArabic ? 'اقترح الإجراءات التالية لهذه الفرصة' : 'Suggest next actions for this opportunity'}:

${isArabic ? 'المرحلة الحالية' : 'Current Stage'}: ${opportunity.stage}
${isArabic ? 'الأنشطة الأخيرة' : 'Recent Activities'}: ${activities.length} ${isArabic ? 'أنشطة' : 'activities'}

${isArabic ? 'قدم قائمة بالإجراءات المقترحة (3-5 إجراءات)' : 'Provide a list of suggested actions (3-5 actions)'}`;
  }

  private buildSuggestionsPrompt(context: any, locale: 'en' | 'ar'): string {
    const isArabic = locale === 'ar';

    return `${isArabic ? 'اقترح قيم وتفاصيل لهذه الفرصة الجديدة' : 'Suggest values and details for this new opportunity'}:

${isArabic ? 'العنوان' : 'Title'}: ${context.title || (isArabic ? 'غير محدد' : 'Not specified')}
${isArabic ? 'الوصف' : 'Description'}: ${context.description || (isArabic ? 'غير محدد' : 'Not specified')}
${isArabic ? 'الصناعة' : 'Industry'}: ${context.industry || (isArabic ? 'غير محدد' : 'Not specified')}
${isArabic ? 'حجم الشركة' : 'Company Size'}: ${context.companySize || (isArabic ? 'غير محدد' : 'Not specified')}

${isArabic ? 'قدم اقتراحات بتنسيق JSON مع' : 'Provide suggestions in JSON format with'}:
- suggestedValue: ${isArabic ? 'القيمة المقترحة بالدولار' : 'suggested value in USD'}
- suggestedCloseDate: ${isArabic ? 'تاريخ الإغلاق المقترح' : 'suggested close date'}
- suggestedTags: ${isArabic ? 'العلامات المقترحة' : 'suggested tags array'}
- suggestedPriority: ${isArabic ? 'الأولوية المقترحة' : 'suggested priority'}
- confidence: ${isArabic ? 'مستوى الثقة (0-100)' : 'confidence level (0-100)'}
- reasoning: ${isArabic ? 'التبرير' : 'reasoning'}`;
  }

  // Response parsing methods
  private parseScoringResponse(response: string): OpportunityScoring {
    try {
      return JSON.parse(response);
    } catch {
      return this.getFallbackScoring();
    }
  }

  private parseForecastResponse(response: string): DealForecast {
    try {
      return JSON.parse(response);
    } catch {
      return this.getFallbackForecast();
    }
  }

  private parsePipelineForecastResponse(response: string): PipelineForecast {
    try {
      return JSON.parse(response);
    } catch {
      return this.getFallbackPipelineForecast();
    }
  }

  private parseCompetitiveAnalysisResponse(response: string): CompetitiveAnalysis {
    try {
      return JSON.parse(response);
    } catch {
      return this.getFallbackCompetitiveAnalysis();
    }
  }

  private parseNextActionsResponse(response: string): string[] {
    try {
      const parsed = JSON.parse(response);
      return Array.isArray(parsed) ? parsed : parsed.actions || [];
    } catch {
      return this.getFallbackNextActions();
    }
  }

  private parseSuggestionsResponse(response: string): {
    suggestedValue?: number;
    suggestedCloseDate?: string;
    suggestedTags?: string[];
    suggestedPriority?: string;
    confidence: number;
    reasoning: string;
  } {
    try {
      const parsed = JSON.parse(response);
      return {
        suggestedValue: parsed.suggestedValue,
        suggestedCloseDate: parsed.suggestedCloseDate,
        suggestedTags: parsed.suggestedTags || [],
        suggestedPriority: parsed.suggestedPriority || 'medium',
        confidence: parsed.confidence || 70,
        reasoning: parsed.reasoning || 'AI-generated suggestions'
      };
    } catch {
      return {
        confidence: 30,
        reasoning: 'Failed to parse AI suggestions'
      };
    }
  }

  // Fallback methods
  private getFallbackScoring(opportunity?: OpportunityWithRelations): OpportunityScoring {
    const baseScore = opportunity?.probability || 50;
    return {
      totalScore: baseScore,
      confidence: 60,
      factors: {
        dealSize: { score: baseScore, weight: 25, reasoning: 'Based on opportunity value' },
        timeline: { score: baseScore, weight: 20, reasoning: 'Based on expected close date' },
        authority: { score: baseScore, weight: 20, reasoning: 'Based on contact information' },
        need: { score: baseScore, weight: 20, reasoning: 'Based on opportunity description' },
        competition: { score: baseScore, weight: 15, reasoning: 'Based on industry analysis' }
      },
      recommendations: ['Follow up with decision maker', 'Clarify timeline', 'Understand competition'],
      riskFactors: ['Timeline uncertainty', 'Competition presence'],
      nextActions: ['Schedule follow-up call', 'Send proposal', 'Identify decision makers']
    };
  }

  private getFallbackForecast(opportunity?: OpportunityWithRelations): DealForecast {
    const closeDate = opportunity?.expectedCloseDate || new Date(Date.now() + 30 * 24 * 60 * 60 * 1000);
    return {
      predictedCloseDate: closeDate.toISOString(),
      confidence: 60,
      probabilityAdjustment: 0,
      reasoning: 'Based on current stage and historical data',
      factors: {
        historical: 'Average deal cycle for this stage',
        seasonal: 'No significant seasonal impact',
        competitive: 'Standard competitive environment',
        internal: 'Normal resource allocation'
      }
    };
  }

  private getFallbackPipelineForecast(tenantId?: string): PipelineForecast {
    return {
      totalPredictedRevenue: 0,
      confidence: 50,
      timeframe: '90d',
      breakdown: [],
      trends: {
        velocity: 0,
        conversionRates: {},
        seasonality: 'No clear pattern'
      },
      recommendations: ['Increase pipeline activity', 'Focus on high-value opportunities']
    };
  }

  private getFallbackCompetitiveAnalysis(): CompetitiveAnalysis {
    return {
      competitors: [],
      positioning: {
        strengths: ['Quality service', 'Competitive pricing'],
        weaknesses: ['Limited market presence'],
        differentiators: ['Unique value proposition']
      },
      strategy: {
        recommendations: ['Highlight unique strengths', 'Address competitive concerns'],
        messaging: ['Focus on value proposition'],
        pricing: ['Maintain competitive pricing']
      }
    };
  }

  private getFallbackNextActions(opportunity?: OpportunityWithRelations): string[] {
    return [
      'Follow up with key stakeholders',
      'Prepare and send proposal',
      'Schedule product demonstration',
      'Identify decision timeline',
      'Address any concerns or objections'
    ];
  }

  // Data fetching helper methods
  private async getHistoricalContext(tenantId: string) {
    // Get historical opportunity data for context
    return await prisma.opportunity.findMany({
      where: { tenantId },
      select: {
        stage: true,
        value: true,
        probability: true,
        createdAt: true,
        updatedAt: true
      },
      take: 100,
      orderBy: { createdAt: 'desc' }
    });
  }

  private async getStageHistory(opportunityId: string, tenantId: string) {
    // For now, return empty array since we don't have stage history table yet
    // TODO: Implement stage history tracking
    return [];
  }

  private async getPipelineData(tenantId: string) {
    return await prisma.opportunity.groupBy({
      by: ['stage'],
      where: { tenantId },
      _count: { _all: true },
      _sum: { value: true }
    });
  }

  private async getRecentActivities(opportunityId: string, tenantId: string) {
    return await prisma.activity.findMany({
      where: {
        tenantId,
        relatedToType: 'opportunity',
        relatedToId: opportunityId
      },
      take: 10,
      orderBy: { createdAt: 'desc' }
    });
  }

  private async getCurrentStageInfo(stageName: string, tenantId: string) {
    // For now, return basic stage info since we don't have sales stage table yet
    // TODO: Implement sales stage management
    return {
      name: stageName,
      tenantId,
      probabilityMin: 0,
      probabilityMax: 100
    };
  }
}

export const aiOpportunityService = new AIOpportunityService();
