# Enhanced Proposal Generation System

## Overview

The enhanced proposal generation system integrates file upload capabilities with the Gemini API to provide context-aware, professional proposal generation with clean AI output and multi-language support.

## Key Features

### 1. File Upload Integration with Gemini API
- **Real File Upload**: Files are actually uploaded to Gemini API and receive proper file URIs
- **Context Grounding**: AI responses are grounded in uploaded documents to prevent hallucination
- **File Validation**: Automatic validation of file compatibility with Gemini API requirements
- **Batch Processing**: Multiple files can be uploaded and processed efficiently

### 2. Enhanced AI Prompts for Clean Output
- **No Conversational Artifacts**: Eliminates phrases like "Here's a draft...", "I'll create...", etc.
- **Direct Content Generation**: AI provides only the actual content for each section
- **Professional Tone**: Maintains consistent professional writing style
- **Context-Aware**: Uses uploaded documents as reference material

### 3. Multi-Language Support
- **English and Arabic**: Full support for both languages
- **Localized Prompts**: Language-specific prompts and instructions
- **RTL Support**: Proper right-to-left text handling for Arabic
- **Clean Output**: No conversational artifacts in either language

## Implementation Details

### Database Schema
The existing proposal schema supports:
- `selectedFileIds`: Array of document IDs used for context
- `geminiFileUris`: Map of document ID to Gemini file URI
- Multi-language content generation

### API Endpoints

#### File Upload and Validation
- `POST /api/proposals/[id]/select-files` - Upload files to Gemini and associate with proposal
- `POST /api/proposals/[id]/validate-files` - Validate files for Gemini compatibility
- `POST /api/proposals/temp/validate-files` - Temporary validation during wizard

#### Enhanced Generation
- `POST /api/proposals/generate` - Generate proposal with file context and language support
- `POST /api/proposals/[id]/regenerate-section` - Regenerate sections with enhanced prompts

### Services

#### GeminiFileService
```typescript
class GeminiFileService {
  // Upload single file to Gemini API
  async uploadFile(request: GeminiFileUploadRequest): Promise<GeminiFileUploadResponse>
  
  // Upload multiple files in batch
  async uploadFilesBatch(request: GeminiFileUploadBatchRequest): Promise<GeminiFileUploadBatchResponse>
  
  // Get file contexts for AI generation
  async getFileContexts(documentIds: string[], tenantId: string, geminiFileUris?: Record<string, string>): Promise<GeminiFileContext[]>
  
  // Validate file compatibility
  static validateFileForGemini(mimeType: string, fileSize: number): { valid: boolean; error?: string }
}
```

#### Enhanced AIProposalGenerationService
- Integrated file upload workflow
- Clean prompt generation without conversational artifacts
- Multi-language support (English/Arabic)
- Context-aware content generation using Gemini file URIs

### Frontend Components

#### Enhanced ProposalGenerationWizard
- Language selection (English/Arabic)
- File validation display
- Real-time compatibility checking
- Enhanced error handling

## Usage Examples

### 1. Generate Proposal with File Context (English)
```typescript
const request = {
  opportunityId: 'opp-123',
  title: 'Software Development Proposal',
  selectedFileIds: ['doc-1', 'doc-2'],
  sections: ['executive_summary', 'scope_of_work'],
  includeCharts: true,
  chartTypes: ['architecture_high_level'],
  locale: 'en'
};

const result = await aiProposalService.generateProposal(request, tenantId, userId);
```

### 2. Generate Arabic Content
```typescript
const request = {
  // ... same as above
  locale: 'ar'
};

const result = await aiProposalService.generateProposal(request, tenantId, userId);
```

### 3. Validate Files Before Upload
```typescript
const validation = GeminiFileService.validateFileForGemini('application/pdf', 1024000);
if (!validation.valid) {
  console.error('File not compatible:', validation.error);
}
```

## File Compatibility

### Supported File Types
- PDF documents (`application/pdf`)
- Word documents (`.doc`, `.docx`)
- Text files (`.txt`)
- Spreadsheets (`.xls`, `.xlsx`)
- Presentations (`.ppt`, `.pptx`)
- Images (`.jpg`, `.png`, `.gif`, `.webp`)

### File Size Limits
- Maximum file size: 20MB (Gemini API limit)
- Automatic validation before upload
- Clear error messages for incompatible files

## Error Handling

### File Upload Errors
- Network failures with retry logic
- File size/type validation
- Gemini API rate limiting
- Graceful degradation to text-only context

### Generation Errors
- Fallback to simplified prompts
- Partial generation recovery
- Clear user feedback
- Audit trail for debugging

## Testing

### Unit Tests
- File validation logic
- Prompt generation without artifacts
- Multi-language support
- Error handling scenarios

### Integration Tests
- End-to-end proposal generation
- File upload workflow
- API endpoint validation
- UI component behavior

## Configuration

### Environment Variables
```bash
GOOGLE_AI_API_KEY=your_gemini_api_key
UPLOAD_DIR=./uploads
AI_PROVIDER=google
```

### Feature Flags
- `ENABLE_GEMINI_FILE_UPLOAD`: Enable/disable file upload to Gemini
- `ENABLE_ARABIC_GENERATION`: Enable/disable Arabic language support
- `ENABLE_FILE_VALIDATION`: Enable/disable file compatibility checking

## Performance Considerations

### File Upload Optimization
- Batch processing for multiple files
- Parallel upload when possible
- Progress tracking for large files
- Caching of file URIs

### AI Generation Optimization
- Efficient prompt construction
- Token usage optimization
- Response caching where appropriate
- Streaming for long content

## Security

### File Security
- Tenant isolation for uploaded files
- Secure file path generation
- Access control validation
- Automatic cleanup of expired files

### API Security
- Authentication required for all endpoints
- Permission-based access control
- Rate limiting on file uploads
- Input validation and sanitization

## Monitoring and Analytics

### Metrics Tracked
- File upload success/failure rates
- Generation quality scores
- Language usage statistics
- Performance metrics

### Logging
- File upload events
- Generation requests and responses
- Error conditions and recovery
- User interaction patterns

## Future Enhancements

### Planned Features
- Additional language support
- Advanced file preprocessing
- Custom prompt templates
- Integration with more AI providers

### Performance Improvements
- Streaming file uploads
- Background processing
- Enhanced caching strategies
- Optimized prompt engineering
