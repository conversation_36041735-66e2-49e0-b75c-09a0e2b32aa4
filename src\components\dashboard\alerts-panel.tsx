'use client';

import React from 'react';
import {
  ExclamationTriangleIcon,
  InformationCircleIcon,
  CheckCircleIcon,
  XCircleIcon,
  ClockIcon,
  ServerIcon,
  CpuChipIcon,
  CircleStackIcon,
} from '@heroicons/react/24/outline';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { SystemHealthMetrics } from '@/services/super-admin';
import { cn } from '@/lib/utils';

interface Alert {
  id: string;
  type: 'critical' | 'warning' | 'info' | 'success';
  title: string;
  message: string;
  timestamp: Date;
  source: string;
  icon: React.ComponentType<{ className?: string }>;
  actionable?: boolean;
}

interface AlertsPanelProps {
  healthStatus: string;
  currentHealth: SystemHealthMetrics | null;
  className?: string;
}

export function AlertsPanel({ 
  healthStatus, 
  currentHealth, 
  className 
}: AlertsPanelProps) {
  const generateAlerts = (): Alert[] => {
    const alerts: Alert[] = [];
    const now = new Date();

    if (!currentHealth) {
      alerts.push({
        id: 'no-data',
        type: 'warning',
        title: 'No Health Data Available',
        message: 'Unable to retrieve current system health metrics. Check system connectivity.',
        timestamp: now,
        source: 'System Monitor',
        icon: ExclamationTriangleIcon,
        actionable: true,
      });
      return alerts;
    }

    // Critical alerts
    if (currentHealth.errorRate > 5) {
      alerts.push({
        id: 'high-error-rate',
        type: 'critical',
        title: 'High Error Rate Detected',
        message: `Current error rate is ${currentHealth.errorRate.toFixed(2)}%, which exceeds the critical threshold of 5%.`,
        timestamp: now,
        source: 'API Monitor',
        icon: XCircleIcon,
        actionable: true,
      });
    }

    if (currentHealth.responseTime > 2000) {
      alerts.push({
        id: 'slow-response',
        type: 'critical',
        title: 'Slow Response Times',
        message: `Average response time is ${Math.round(currentHealth.responseTime)}ms, exceeding the critical threshold of 2000ms.`,
        timestamp: now,
        source: 'Performance Monitor',
        icon: ClockIcon,
        actionable: true,
      });
    }

    if (currentHealth.memoryUsage > 90) {
      alerts.push({
        id: 'high-memory',
        type: 'critical',
        title: 'Critical Memory Usage',
        message: `Memory usage is at ${currentHealth.memoryUsage.toFixed(1)}%, approaching system limits.`,
        timestamp: now,
        source: 'Resource Monitor',
        icon: CpuChipIcon,
        actionable: true,
      });
    }

    // Warning alerts
    if (currentHealth.errorRate > 2 && currentHealth.errorRate <= 5) {
      alerts.push({
        id: 'elevated-errors',
        type: 'warning',
        title: 'Elevated Error Rate',
        message: `Error rate is ${currentHealth.errorRate.toFixed(2)}%, above normal levels but below critical threshold.`,
        timestamp: now,
        source: 'API Monitor',
        icon: ExclamationTriangleIcon,
      });
    }

    if (currentHealth.responseTime > 1000 && currentHealth.responseTime <= 2000) {
      alerts.push({
        id: 'slow-response-warning',
        type: 'warning',
        title: 'Degraded Response Times',
        message: `Response times averaging ${Math.round(currentHealth.responseTime)}ms, above optimal performance.`,
        timestamp: now,
        source: 'Performance Monitor',
        icon: ClockIcon,
      });
    }

    if (currentHealth.memoryUsage > 80 && currentHealth.memoryUsage <= 90) {
      alerts.push({
        id: 'high-memory-warning',
        type: 'warning',
        title: 'High Memory Usage',
        message: `Memory usage at ${currentHealth.memoryUsage.toFixed(1)}%, monitoring for potential issues.`,
        timestamp: now,
        source: 'Resource Monitor',
        icon: CpuChipIcon,
      });
    }

    if (currentHealth.databaseConnections > 15) {
      alerts.push({
        id: 'high-db-connections',
        type: 'warning',
        title: 'High Database Connections',
        message: `Database connection count is ${currentHealth.databaseConnections}, approaching pool limits.`,
        timestamp: now,
        source: 'Database Monitor',
        icon: CircleStackIcon,
      });
    }

    // Info alerts
    if (currentHealth.uptime < 99.9) {
      alerts.push({
        id: 'uptime-info',
        type: 'info',
        title: 'Uptime Below Target',
        message: `System uptime is ${currentHealth.uptime.toFixed(3)}%, below the 99.9% target.`,
        timestamp: now,
        source: 'Uptime Monitor',
        icon: InformationCircleIcon,
      });
    }

    // Success alerts (when everything is good)
    if (alerts.length === 0) {
      alerts.push({
        id: 'all-good',
        type: 'success',
        title: 'All Systems Operational',
        message: 'All monitored systems are operating within normal parameters.',
        timestamp: now,
        source: 'System Monitor',
        icon: CheckCircleIcon,
      });
    }

    return alerts.sort((a, b) => {
      const priority = { critical: 4, warning: 3, info: 2, success: 1 };
      return priority[b.type] - priority[a.type];
    });
  };

  const alerts = generateAlerts();

  const getAlertIcon = (alert: Alert) => {
    const IconComponent = alert.icon;
    const iconClasses = {
      critical: 'h-5 w-5 text-red-500',
      warning: 'h-5 w-5 text-yellow-500',
      info: 'h-5 w-5 text-blue-500',
      success: 'h-5 w-5 text-green-500',
    };

    return <IconComponent className={iconClasses[alert.type]} />;
  };

  const getAlertBadge = (type: Alert['type']) => {
    const variants = {
      critical: 'destructive',
      warning: 'secondary',
      info: 'outline',
      success: 'default',
    } as const;

    return (
      <Badge variant={variants[type]} className="text-xs">
        {type.toUpperCase()}
      </Badge>
    );
  };

  const formatTimestamp = (timestamp: Date) => {
    return timestamp.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
    });
  };

  const handleAlertAction = (alertId: string) => {
    // In a real implementation, this would trigger specific actions
    // based on the alert type (e.g., restart service, clear cache, etc.)
    console.log(`Taking action for alert: ${alertId}`);
  };

  return (
    <div className={cn("space-y-6", className)}>
      {/* Alert Summary */}
      <Card className="bg-white dark:bg-gray-800 rounded-lg shadow-soft">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <ExclamationTriangleIcon className="h-5 w-5" />
            System Alerts
          </CardTitle>
          <CardDescription>
            Current system alerts and notifications based on health metrics
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
            <div className="text-center">
              <div className="text-2xl font-bold text-red-500">
                {alerts.filter(a => a.type === 'critical').length}
              </div>
              <div className="text-xs text-muted-foreground">Critical</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-yellow-500">
                {alerts.filter(a => a.type === 'warning').length}
              </div>
              <div className="text-xs text-muted-foreground">Warning</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-500">
                {alerts.filter(a => a.type === 'info').length}
              </div>
              <div className="text-xs text-muted-foreground">Info</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-500">
                {alerts.filter(a => a.type === 'success').length}
              </div>
              <div className="text-xs text-muted-foreground">Success</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Alert List */}
      <div className="space-y-4">
        {alerts.map((alert) => (
          <Card key={alert.id} className="bg-white dark:bg-gray-800 rounded-lg shadow-soft">
            <CardContent className="pt-6">
              <div className="flex items-start justify-between">
                <div className="flex items-start space-x-3">
                  {getAlertIcon(alert)}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-1">
                      <h4 className="text-sm font-semibold">{alert.title}</h4>
                      {getAlertBadge(alert.type)}
                    </div>
                    <p className="text-sm text-muted-foreground mb-2">
                      {alert.message}
                    </p>
                    <div className="flex items-center gap-4 text-xs text-muted-foreground">
                      <span>Source: {alert.source}</span>
                      <span>Time: {formatTimestamp(alert.timestamp)}</span>
                    </div>
                  </div>
                </div>
                {alert.actionable && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleAlertAction(alert.id)}
                    className="ml-4"
                  >
                    Take Action
                  </Button>
                )}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Alert Actions */}
      <Card className="bg-white dark:bg-gray-800 rounded-lg shadow-soft">
        <CardHeader>
          <CardTitle className="text-sm">Quick Actions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-2">
            <Button variant="outline" size="sm">
              Clear All Alerts
            </Button>
            <Button variant="outline" size="sm">
              Export Alert Log
            </Button>
            <Button variant="outline" size="sm">
              Configure Thresholds
            </Button>
            <Button variant="outline" size="sm">
              Test Notifications
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
