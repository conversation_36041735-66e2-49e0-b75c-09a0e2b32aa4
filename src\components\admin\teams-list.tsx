'use client';

import React from 'react';
import { useRouter } from 'next/navigation';
import { TeamWithDetails } from '@/types';
import { ContactAvatar } from '@/components/ui/contact-avatar';
import { PermissionGate } from '@/components/ui/permission-gate';
import { PermissionAction, PermissionResource } from '@/types';
import {
  UserGroupIcon,
  EllipsisVerticalIcon,
  PencilIcon,
  TrashIcon,
  UserIcon,
  ChartBarIcon
} from '@heroicons/react/24/outline';

interface TeamsListProps {
  teams: TeamWithDetails[];
  onTeamSelect: (team: TeamWithDetails) => void;
  onTeamDelete: (teamId: string) => void;
}

export function TeamsList({ teams, onTeamSelect, onTeamDelete }: TeamsListProps) {
  if (teams.length === 0) {
    return (
      <div className="text-center py-12">
        <UserGroupIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">No teams found</h3>
        <p className="text-gray-500">Create your first team to get started with team management.</p>
      </div>
    );
  }

  return (
    <div className="bg-white shadow-sm rounded-lg border border-gray-200">
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Team
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Manager
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Members
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Status
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Created
              </th>
              <th className="relative px-6 py-3">
                <span className="sr-only">Actions</span>
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {teams.map((team) => (
              <TeamRow
                key={team.id}
                team={team}
                onSelect={() => onTeamSelect(team)}
                onDelete={() => onTeamDelete(team.id)}
              />
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
}

interface TeamRowProps {
  team: TeamWithDetails;
  onSelect: () => void;
  onDelete: () => void;
}

function TeamRow({ team, onSelect, onDelete }: TeamRowProps) {
  const router = useRouter();
  const [showMenu, setShowMenu] = React.useState(false);

  const handleEditTeam = () => {
    router.push(`/settings/team/${team.id}/edit`);
    setShowMenu(false);
  };

  return (
    <tr className="hover:bg-gray-50">
      {/* Team Info */}
      <td className="px-6 py-4 whitespace-nowrap">
        <div className="flex items-center">
          <div 
            className="w-10 h-10 rounded-lg flex items-center justify-center flex-shrink-0"
            style={{ backgroundColor: team.color || '#6B7280' }}
          >
            <UserGroupIcon className="w-5 h-5 text-white" />
          </div>
          <div className="ml-4">
            <div className="text-sm font-medium text-gray-900">{team.name}</div>
            {team.description && (
              <div className="text-sm text-gray-500 max-w-xs truncate">
                {team.description}
              </div>
            )}
          </div>
        </div>
      </td>

      {/* Manager */}
      <td className="px-6 py-4 whitespace-nowrap">
        {team.manager ? (
          <div className="flex items-center">
            <ContactAvatar
              firstName={team.manager.firstName}
              lastName={team.manager.lastName}
              size="sm"
              className="flex-shrink-0"
            />
            <div className="ml-3">
              <div className="text-sm font-medium text-gray-900">
                {team.manager.firstName} {team.manager.lastName}
              </div>
              <div className="text-sm text-gray-500">{team.manager.email}</div>
            </div>
          </div>
        ) : (
          <span className="text-sm text-gray-400">No manager assigned</span>
        )}
      </td>

      {/* Members */}
      <td className="px-6 py-4 whitespace-nowrap">
        <div className="flex items-center">
          <div className="flex -space-x-1 mr-3">
            {team.members?.slice(0, 3).map((member) => (
              <ContactAvatar
                key={member.id}
                firstName={member.firstName}
                lastName={member.lastName}
                avatarUrl={member.avatarUrl}
                size="xs"
                className="border-2 border-white"
              />
            ))}
            {team.memberCount > 3 && (
              <div className="w-6 h-6 rounded-full bg-gray-100 border-2 border-white flex items-center justify-center">
                <span className="text-xs font-medium text-gray-600">
                  +{team.memberCount - 3}
                </span>
              </div>
            )}
          </div>
          <span className="text-sm text-gray-900">{team.memberCount} members</span>
        </div>
      </td>

      {/* Status */}
      <td className="px-6 py-4 whitespace-nowrap">
        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
          team.isActive 
            ? 'bg-green-100 text-green-800' 
            : 'bg-gray-100 text-gray-800'
        }`}>
          {team.isActive ? 'Active' : 'Inactive'}
        </span>
      </td>

      {/* Created Date */}
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
        {new Date(team.createdAt).toLocaleDateString()}
      </td>

      {/* Actions */}
      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
        <div className="relative">
          <button
            onClick={() => setShowMenu(!showMenu)}
            className="p-1 text-gray-400 hover:text-gray-600 rounded-full hover:bg-gray-100"
          >
            <EllipsisVerticalIcon className="w-5 h-5" />
          </button>

          {showMenu && (
            <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg border border-gray-200 z-10">
              <div className="py-1">
                <button
                  onClick={() => {
                    onSelect();
                    setShowMenu(false);
                  }}
                  className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 w-full text-left"
                >
                  <ChartBarIcon className="w-4 h-4 mr-3" />
                  View Details
                </button>
                
                <PermissionGate resource={PermissionResource.USERS} action={PermissionAction.MANAGE}>
                  <button
                    onClick={handleEditTeam}
                    className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 w-full text-left"
                  >
                    <PencilIcon className="w-4 h-4 mr-3" />
                    Edit Team
                  </button>
                  
                  <button
                    onClick={() => {
                      onDelete();
                      setShowMenu(false);
                    }}
                    className="flex items-center px-4 py-2 text-sm text-red-600 hover:bg-red-50 w-full text-left"
                  >
                    <TrashIcon className="w-4 h-4 mr-3" />
                    Delete Team
                  </button>
                </PermissionGate>
              </div>
            </div>
          )}
        </div>
      </td>
    </tr>
  );
}
