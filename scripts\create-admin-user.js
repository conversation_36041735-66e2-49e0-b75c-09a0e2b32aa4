#!/usr/bin/env node

/**
 * Enhanced Admin User Creation Script
 * 
 * This script creates an initial admin user with configurable credentials
 * using the same bcryptjs hashing system as the main application.
 * 
 * Features:
 * - Uses environment variables for configuration
 * - Bcryptjs hashing with salt rounds 12 (same as main app)
 * - Password strength validation
 * - Idempotent operation (safe to run multiple times)
 * - Comprehensive error handling
 * 
 * Environment Variables:
 * - ADMIN_EMAIL: Admin user email (default: <EMAIL>)
 * - ADMIN_PASSWORD: Admin user password (default: Admin123!)
 * - ADMIN_FIRST_NAME: Admin first name (default: Admin)
 * - ADMIN_LAST_NAME: Admin last name (default: User)
 * 
 * Usage:
 *   node scripts/create-admin-user.js [options]
 * 
 * Options:
 *   --force          Force update existing admin user
 *   --generate       Generate a secure random password
 *   --validate-only  Only validate password without creating user
 */

const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');
const crypto = require('crypto');

// Initialize Prisma client
let prisma;
try {
  prisma = new PrismaClient();
  console.log('✅ Prisma client initialized successfully');
} catch (error) {
  console.error('❌ Failed to initialize Prisma client:', error.message);
  console.error('Please ensure you have run "npx prisma generate" before running this script');
  process.exit(1);
}

// Parse command line arguments
const args = process.argv.slice(2);
const options = {
  force: args.includes('--force'),
  generate: args.includes('--generate'),
  validateOnly: args.includes('--validate-only')
};

// Configuration from environment variables with defaults
const config = {
  email: process.env.ADMIN_EMAIL || '<EMAIL>',
  password: process.env.ADMIN_PASSWORD || 'Admin123!',
  firstName: process.env.ADMIN_FIRST_NAME || 'Admin',
  lastName: process.env.ADMIN_LAST_NAME || 'User'
};

/**
 * Password strength validation (same as main app)
 */
function validatePasswordStrength(password, minLength = 8) {
  const errors = [];
  let score = 0;

  // Length check
  if (password.length < minLength) {
    errors.push(`Password must be at least ${minLength} characters long`);
  } else {
    score += 1;
  }

  // Uppercase check
  if (!/[A-Z]/.test(password)) {
    errors.push('Password must contain at least one uppercase letter');
  } else {
    score += 1;
  }

  // Lowercase check
  if (!/[a-z]/.test(password)) {
    errors.push('Password must contain at least one lowercase letter');
  } else {
    score += 1;
  }

  // Number check
  if (!/\d/.test(password)) {
    errors.push('Password must contain at least one number');
  } else {
    score += 1;
  }

  // Special character check
  if (!/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
    errors.push('Password must contain at least one special character');
  } else {
    score += 1;
  }

  // Bonus points for longer passwords
  if (password.length >= 12) score += 1;
  if (password.length >= 16) score += 1;

  return {
    isValid: errors.length === 0,
    errors,
    score: Math.min(score, 5), // Cap at 5
  };
}

/**
 * Generate a secure random password
 */
function generateSecurePassword(length = 16) {
  const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
  const lowercase = 'abcdefghijklmnopqrstuvwxyz';
  const numbers = '0123456789';
  const symbols = '!@#$%^&*()_+-=[]{}|;:,.<>?';
  
  const allChars = uppercase + lowercase + numbers + symbols;
  
  let password = '';
  
  // Ensure at least one character from each type
  password += uppercase[crypto.randomInt(0, uppercase.length)];
  password += lowercase[crypto.randomInt(0, lowercase.length)];
  password += numbers[crypto.randomInt(0, numbers.length)];
  password += symbols[crypto.randomInt(0, symbols.length)];
  
  // Fill the rest randomly
  for (let i = 4; i < length; i++) {
    password += allChars[crypto.randomInt(0, allChars.length)];
  }
  
  // Shuffle the password
  return password.split('').sort(() => crypto.randomInt(-1, 2)).join('');
}

/**
 * Hash password using bcryptjs (same as main app)
 */
async function hashPassword(password) {
  return bcrypt.hash(password, 12);
}

/**
 * Check if admin user already exists
 */
async function checkExistingAdmin(email) {
  try {
    const user = await prisma.user.findUnique({
      where: { email },
      include: {
        tenantUsers: {
          include: {
            tenant: true
          }
        }
      }
    });
    return user;
  } catch (error) {
    console.error('Error checking existing admin:', error.message);
    return null;
  }
}

/**
 * Create or update admin user
 */
async function createAdminUser() {
  console.log('👤 Creating/updating admin user...\n');
  
  // Use generated password if requested
  let password = config.password;
  if (options.generate) {
    password = generateSecurePassword();
    console.log('🔐 Generated secure password:', password);
    console.log('⚠️  Please save this password securely!\n');
  }
  
  // Validate password strength
  console.log('🔍 Validating password strength...');
  const validation = validatePasswordStrength(password);
  
  if (!validation.isValid) {
    console.error('❌ Password validation failed:');
    validation.errors.forEach(error => console.error(`   • ${error}`));
    return false;
  }
  
  console.log(`✅ Password validation passed (strength score: ${validation.score}/5)`);
  
  if (options.validateOnly) {
    console.log('✅ Password validation complete. Exiting (--validate-only flag set).');
    return true;
  }
  
  // Check for existing admin
  const existingAdmin = await checkExistingAdmin(config.email);
  
  if (existingAdmin && !options.force) {
    console.log('⚠️  Admin user already exists:');
    console.log(`   Email: ${existingAdmin.email}`);
    console.log(`   Name: ${existingAdmin.firstName} ${existingAdmin.lastName}`);
    console.log(`   Status: ${existingAdmin.status}`);
    console.log(`   Platform Admin: ${existingAdmin.isPlatformAdmin}`);
    console.log('\n   Use --force flag to update existing admin user.');
    return false;
  }
  
  try {
    // Hash the password
    console.log('🔐 Hashing password...');
    const passwordHash = await hashPassword(password);
    console.log('✅ Password hashed successfully');
    
    // Create or update admin user
    const adminUser = await prisma.user.upsert({
      where: { email: config.email },
      update: {
        passwordHash,
        firstName: config.firstName,
        lastName: config.lastName,
        isPlatformAdmin: true,
        status: 'active',
        emailVerified: true,
        updatedAt: new Date()
      },
      create: {
        email: config.email,
        passwordHash,
        firstName: config.firstName,
        lastName: config.lastName,
        locale: 'en',
        timezone: 'UTC',
        emailVerified: true,
        isPlatformAdmin: true,
        status: 'active'
      }
    });
    
    const action = existingAdmin ? 'updated' : 'created';
    console.log(`✅ Admin user ${action} successfully:`);
    console.log(`   ID: ${adminUser.id}`);
    console.log(`   Email: ${adminUser.email}`);
    console.log(`   Name: ${adminUser.firstName} ${adminUser.lastName}`);
    console.log(`   Platform Admin: ${adminUser.isPlatformAdmin}`);
    console.log(`   Status: ${adminUser.status}`);
    
    return adminUser;
    
  } catch (error) {
    console.error('❌ Failed to create/update admin user:', error.message);
    throw error;
  }
}

/**
 * Main execution function
 */
async function main() {
  console.log('🚀 Enhanced Admin User Creation Script\n');
  console.log('📋 Configuration:');
  console.log(`   Email: ${config.email}`);
  console.log(`   First Name: ${config.firstName}`);
  console.log(`   Last Name: ${config.lastName}`);
  console.log(`   Force update: ${options.force}`);
  console.log(`   Generate password: ${options.generate}`);
  console.log(`   Validate only: ${options.validateOnly}\n`);
  
  try {
    // Test database connection
    await prisma.$connect();
    console.log('✅ Database connection established\n');
    
    // Create admin user
    const result = await createAdminUser();
    
    if (result) {
      console.log('\n🎉 Admin user setup completed successfully!');
      
      if (!options.validateOnly) {
        console.log('\n📝 Login Credentials:');
        console.log(`   Email: ${config.email}`);
        if (options.generate) {
          console.log(`   Password: ${password} (generated)`);
        } else {
          console.log(`   Password: [from ADMIN_PASSWORD env var]`);
        }
        console.log('\n⚠️  Please change the default password after first login!');
      }
    }
    
  } catch (error) {
    console.error('\n❌ Script execution failed:', error);
    console.error('Stack trace:', error.stack);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the script
main();
