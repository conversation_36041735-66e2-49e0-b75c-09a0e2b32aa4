import { NextResponse } from 'next/server';
import { z } from 'zod';
import { withPermission, AuthenticatedRequest } from '@/lib/permission-middleware';
import { roleManagementService } from '@/services/role-management';
import { permissionService } from '@/services/permission-service';
import { PermissionAction, PermissionResource } from '@/types';

const updateRoleSchema = z.object({
  name: z.string().min(1, 'Role name is required').max(100, 'Role name too long').optional(),
  description: z.string().optional(),
  parentRoleId: z.string().optional(),
  permissions: z.array(z.string()).optional()
});

interface RouteParams {
  params: Promise<{
    id: string;
  }>;
}

/**
 * GET /api/roles/[id]
 * Get a specific role by ID
 */
export const GET = withPermission({
  resource: PermissionResource.ROLES,
  action: PermissionAction.READ,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest, { params }: RouteParams) => {
  try {
    const { id: roleId } = await params;

    const role = await roleManagementService.getRoleById(roleId, req.tenantId);

    if (!role) {
      return NextResponse.json(
        { error: 'Role not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: { role }
    });
  } catch (error) {
    console.error('Get role error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch role' },
      { status: 500 }
    );
  }
});

/**
 * PUT /api/roles/[id]
 * Update a specific role
 */
export const PUT = withPermission({
  resource: PermissionResource.ROLES,
  action: PermissionAction.UPDATE,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest, { params }: RouteParams) => {
  try {
    const { id: roleId } = await params;
    const body = await req.json();
    const validatedData = updateRoleSchema.parse(body);

    // Check if this is a system role and if user has permission to edit it
    const existingRole = await roleManagementService.getRoleById(roleId, req.tenantId);
    if (!existingRole) {
      return NextResponse.json(
        { error: 'Role not found' },
        { status: 404 }
      );
    }

    let role;
    if (existingRole.isSystemRole) {
      // Check for system role management permission
      const hasSystemRolePermission = await permissionService.checkUserPermission(
        req.userId,
        req.tenantId,
        PermissionResource.ROLES,
        PermissionAction.MANAGE_SYSTEM_ROLES
      );

      if (!hasSystemRolePermission) {
        return NextResponse.json(
          { error: 'Insufficient permissions to modify system roles' },
          { status: 403 }
        );
      }

      // Use system role update method
      role = await roleManagementService.updateSystemRole(
        roleId,
        req.tenantId,
        validatedData,
        req.userId
      );
    } else {
      // Regular role update
      role = await roleManagementService.updateRole(
        roleId,
        req.tenantId,
        validatedData,
        req.userId
      );
    }

    return NextResponse.json({
      success: true,
      data: { role },
      message: `${existingRole.isSystemRole ? 'System role' : 'Role'} updated successfully`
    });
  } catch (error) {
    console.error('Update role error:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          error: 'Validation failed',
          details: error.errors.map(e => ({
            field: e.path.join('.'),
            message: e.message
          }))
        },
        { status: 400 }
      );
    }

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to update role' },
      { status: 500 }
    );
  }
});

/**
 * DELETE /api/roles/[id]
 * Delete a specific role
 */
export const DELETE = withPermission({
  resource: PermissionResource.ROLES,
  action: PermissionAction.DELETE,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest, { params }: RouteParams) => {
  try {
    const { id: roleId } = await params;

    await roleManagementService.deleteRole(roleId, req.tenantId, req.userId);

    return NextResponse.json({
      success: true,
      message: 'Role deleted successfully'
    });
  } catch (error) {
    console.error('Delete role error:', error);

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to delete role' },
      { status: 500 }
    );
  }
});
