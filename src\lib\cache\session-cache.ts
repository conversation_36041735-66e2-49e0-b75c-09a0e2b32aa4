import { cacheService, CACHE_PREFIXES, CACHE_TTL } from '../redis';
import { Session } from 'next-auth';

export interface CachedSession extends Session {
  tenantId?: string;
  permissions?: string[];
  lastActivity: number;
}

export class SessionCacheService {
  // Cache user session data
  async setSession(userId: string, session: CachedSession): Promise<boolean> {
    const key = `${userId}`;
    return await cacheService.set(key, {
      ...session,
      lastActivity: Date.now(),
    }, {
      ttl: CACHE_TTL.SESSION,
      prefix: CACHE_PREFIXES.SESSION,
    });
  }

  // Get cached session
  async getSession(userId: string): Promise<CachedSession | null> {
    const key = `${userId}`;
    const session = await cacheService.get<CachedSession>(key, {
      prefix: CACHE_PREFIXES.SESSION,
    });

    if (!session) return null;

    // Check if session is still valid (not expired due to inactivity)
    const maxInactivity = 24 * 60 * 60 * 1000; // 24 hours
    if (Date.now() - session.lastActivity > maxInactivity) {
      await this.deleteSession(userId);
      return null;
    }

    return session;
  }

  // Update session activity
  async updateActivity(userId: string): Promise<boolean> {
    const session = await this.getSession(userId);
    if (!session) return false;

    session.lastActivity = Date.now();
    return await this.setSession(userId, session);
  }

  // Delete session
  async deleteSession(userId: string): Promise<boolean> {
    const key = `${userId}`;
    return await cacheService.del(key, CACHE_PREFIXES.SESSION);
  }

  // Cache user permissions
  async setUserPermissions(userId: string, permissions: string[]): Promise<boolean> {
    const key = `${userId}`;
    return await cacheService.set(key, permissions, {
      ttl: CACHE_TTL.USER_PERMISSIONS,
      prefix: CACHE_PREFIXES.USER_PERMISSIONS,
    });
  }

  // Get cached user permissions
  async getUserPermissions(userId: string): Promise<string[] | null> {
    const key = `${userId}`;
    return await cacheService.get<string[]>(key, {
      prefix: CACHE_PREFIXES.USER_PERMISSIONS,
    });
  }

  // Invalidate user permissions cache
  async invalidateUserPermissions(userId: string): Promise<boolean> {
    const key = `${userId}`;
    return await cacheService.del(key, CACHE_PREFIXES.USER_PERMISSIONS);
  }

  // Batch invalidate permissions for multiple users
  async invalidateMultipleUserPermissions(userIds: string[]): Promise<void> {
    const promises = userIds.map(userId => this.invalidateUserPermissions(userId));
    await Promise.all(promises);
  }

  // Get active sessions count
  async getActiveSessionsCount(): Promise<number> {
    try {
      const pattern = `*`;
      const keys = await cacheService['redis'].keys(
        cacheService['buildKey'](pattern, CACHE_PREFIXES.SESSION)
      );
      return keys.length;
    } catch (error) {
      console.error('Error getting active sessions count:', error);
      return 0;
    }
  }

  // Clean up expired sessions
  async cleanupExpiredSessions(): Promise<number> {
    try {
      const pattern = `*`;
      const keys = await cacheService['redis'].keys(
        cacheService['buildKey'](pattern, CACHE_PREFIXES.SESSION)
      );
      
      let cleanedCount = 0;
      const maxInactivity = 24 * 60 * 60 * 1000; // 24 hours
      
      for (const key of keys) {
        const session = await cacheService['redis'].get(key);
        if (session) {
          try {
            const sessionData = JSON.parse(session) as CachedSession;
            if (Date.now() - sessionData.lastActivity > maxInactivity) {
              await cacheService['redis'].del(key);
              cleanedCount++;
            }
          } catch (error) {
            // Invalid session data, remove it
            await cacheService['redis'].del(key);
            cleanedCount++;
          }
        }
      }
      
      return cleanedCount;
    } catch (error) {
      console.error('Error cleaning up expired sessions:', error);
      return 0;
    }
  }
}

export const sessionCache = new SessionCacheService();
