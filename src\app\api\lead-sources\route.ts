import { NextResponse } from 'next/server';
import { withPermission } from '@/lib/permission-middleware';
import { 
  leadSourceManagementService, 
  createLeadSourceSchema, 
  getLeadSourcesQuerySchema,
  CreateLeadSourceData 
} from '@/services/lead-source-management';
import { PermissionAction, PermissionResource } from '@/types';
import { AuthenticatedRequest } from '@/lib/permission-middleware';

/**
 * GET /api/lead-sources
 * Get lead sources with filtering and pagination
 */
export const GET = withPermission({
  resource: PermissionResource.LEADS,
  action: PermissionAction.READ,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest) => {
  try {
    const { searchParams } = new URL(req.url);
    const queryParams = Object.fromEntries(searchParams.entries());
    
    const validatedQuery = getLeadSourcesQuerySchema.parse(queryParams);
    
    const result = await leadSourceManagementService.getLeadSources(
      req.tenantId,
      validatedQuery
    );

    return NextResponse.json({
      success: true,
      data: result,
      message: 'Lead sources retrieved successfully'
    });
  } catch (error) {
    console.error('Error fetching lead sources:', error);
    
    if (error instanceof Error) {
      return NextResponse.json(
        { 
          success: false, 
          error: error.message,
          details: 'Failed to fetch lead sources'
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error',
        details: 'An unexpected error occurred while fetching lead sources'
      },
      { status: 500 }
    );
  }
});

/**
 * POST /api/lead-sources
 * Create a new lead source
 */
export const POST = withPermission({
  resource: PermissionResource.LEADS,
  action: PermissionAction.CREATE,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest) => {
  try {
    const body = await req.json();
    const validatedData = createLeadSourceSchema.parse(body);

    const leadSource = await leadSourceManagementService.createLeadSource(
      req.tenantId,
      validatedData,
      req.userId
    );

    return NextResponse.json({
      success: true,
      data: { leadSource },
      message: 'Lead source created successfully'
    }, { status: 201 });
  } catch (error) {
    console.error('Error creating lead source:', error);
    
    if (error instanceof Error) {
      return NextResponse.json(
        { 
          success: false, 
          error: error.message,
          details: 'Failed to create lead source'
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error',
        details: 'An unexpected error occurred while creating the lead source'
      },
      { status: 500 }
    );
  }
});

/**
 * PUT /api/lead-sources
 * Bulk update lead source sort order
 */
export const PUT = withPermission({
  resource: PermissionResource.LEADS,
  action: PermissionAction.UPDATE,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest) => {
  try {
    const body = await req.json();
    const { updates } = body;

    if (!Array.isArray(updates)) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Updates array is required',
          details: 'Request body must contain an updates array'
        },
        { status: 400 }
      );
    }

    // Validate updates structure
    for (const update of updates) {
      if (!update.id || typeof update.sortOrder !== 'number') {
        return NextResponse.json(
          { 
            success: false, 
            error: 'Invalid update format',
            details: 'Each update must have id and sortOrder'
          },
          { status: 400 }
        );
      }
    }

    await leadSourceManagementService.updateSortOrder(req.tenantId, updates);

    return NextResponse.json({
      success: true,
      message: 'Lead source sort order updated successfully'
    });
  } catch (error) {
    console.error('Error updating lead source sort order:', error);
    
    if (error instanceof Error) {
      return NextResponse.json(
        { 
          success: false, 
          error: error.message,
          details: 'Failed to update lead source sort order'
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error',
        details: 'An unexpected error occurred while updating sort order'
      },
      { status: 500 }
    );
  }
});

/**
 * DELETE /api/lead-sources
 * Bulk delete lead sources
 */
export const DELETE = withPermission({
  resource: PermissionResource.LEADS,
  action: PermissionAction.DELETE,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest) => {
  try {
    const body = await req.json();
    const { leadSourceIds } = body;

    if (!Array.isArray(leadSourceIds) || leadSourceIds.length === 0) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Lead source IDs array is required',
          details: 'Request body must contain a non-empty leadSourceIds array'
        },
        { status: 400 }
      );
    }

    // Limit bulk operations to prevent abuse
    if (leadSourceIds.length > 50) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Too many lead sources',
          details: 'Maximum 50 lead sources can be deleted at once'
        },
        { status: 400 }
      );
    }

    let deletedCount = 0;
    const errors: string[] = [];

    // Delete each lead source individually to handle errors gracefully
    for (const leadSourceId of leadSourceIds) {
      try {
        await leadSourceManagementService.deleteLeadSource(leadSourceId, req.tenantId);
        deletedCount++;
      } catch (error) {
        if (error instanceof Error) {
          errors.push(`${leadSourceId}: ${error.message}`);
        }
      }
    }

    return NextResponse.json({
      success: true,
      data: { 
        deletedCount,
        totalRequested: leadSourceIds.length,
        errors: errors.length > 0 ? errors : undefined
      },
      message: `${deletedCount} lead source(s) deleted successfully${errors.length > 0 ? ` (${errors.length} failed)` : ''}`
    });
  } catch (error) {
    console.error('Error deleting lead sources:', error);
    
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error',
        details: 'An unexpected error occurred while deleting lead sources'
      },
      { status: 500 }
    );
  }
});
