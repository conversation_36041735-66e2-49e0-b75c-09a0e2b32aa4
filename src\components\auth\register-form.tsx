'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import Link from 'next/link';
import { EyeIcon, EyeSlashIcon, CheckIcon, XMarkIcon, ClockIcon } from '@heroicons/react/24/outline';
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { PhoneInput } from "@/components/ui/phone-input";
import { PasswordStrengthIndicator } from "@/components/ui/password-strength-indicator";
import { TermsDialog } from "@/components/ui/terms-dialog";
import { FormError, FormSuccess, FormFieldWrapper } from "@/components/ui/form-error";
import { isValidPhoneNumber } from 'react-phone-number-input';

const signupSchema = z.object({
  tenantName: z.string().min(2, 'Organization name must be at least 2 characters'),
  tenantSlug: z.string()
    .min(3, 'Slug must be at least 3 characters')
    .max(50, 'Slug must be less than 50 characters')
    .regex(/^[a-z0-9-]+$/, 'Slug can only contain lowercase letters, numbers, and hyphens'),
  adminFirstName: z.string().min(1, 'First name is required'),
  adminLastName: z.string().min(1, 'Last name is required'),
  adminEmail: z.string().email('Invalid email address'),
  adminPhone: z.string()
    .optional()
    .refine((phone) => {
      if (!phone) return true; // Phone is optional
      return isValidPhoneNumber(phone);
    }, 'Please enter a valid phone number'),
  adminPassword: z.string()
    .min(8, 'Password must be at least 8 characters')
    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, 'Password must contain at least one uppercase letter, one lowercase letter, and one number'),
  confirmPassword: z.string(),
  acceptTerms: z.boolean().refine(val => val === true, 'You must accept the terms and conditions'),
}).refine((data) => data.adminPassword === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
});

type SignupFormData = z.infer<typeof signupSchema>;

export function RegisterForm({
  className,
  ...props
}: React.ComponentProps<"form">) {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [slugAvailable, setSlugAvailable] = useState<boolean | null>(null);
  const [emailAvailable, setEmailAvailable] = useState<boolean | null>(null);
  const [emailCheckLoading, setEmailCheckLoading] = useState(false);

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors },
  } = useForm<SignupFormData>({
    resolver: zodResolver(signupSchema),
  });

  const tenantName = watch('tenantName');
  const tenantSlug = watch('tenantSlug');
  const adminEmail = watch('adminEmail');
  const adminPassword = watch('adminPassword');

  // Auto-generate slug from tenant name
  useEffect(() => {
    if (tenantName && !tenantSlug) {
      const slug = tenantName
        .toLowerCase()
        .replace(/[^a-z0-9\s-]/g, '')
        .replace(/\s+/g, '-')
        .replace(/-+/g, '-')
        .trim();
      setValue('tenantSlug', slug);
    }
  }, [tenantName, tenantSlug, setValue]);

  // Check slug availability
  useEffect(() => {
    const checkSlugAvailability = async () => {
      if (!tenantSlug || tenantSlug.length < 3) {
        setSlugAvailable(null);
        return;
      }

      try {
        const response = await fetch(`/api/tenants/check-slug?slug=${tenantSlug}`);
        const data = await response.json();
        setSlugAvailable(data.available);
      } catch (error) {
        setSlugAvailable(null);
      }
    };

    const timeoutId = setTimeout(checkSlugAvailability, 500);
    return () => clearTimeout(timeoutId);
  }, [tenantSlug]);

  // Check email availability
  useEffect(() => {
    const checkEmailAvailability = async () => {
      if (!adminEmail || !adminEmail.includes('@')) {
        setEmailAvailable(null);
        setEmailCheckLoading(false);
        return;
      }

      setEmailCheckLoading(true);
      try {
        const response = await fetch(`/api/users/check-email?email=${encodeURIComponent(adminEmail)}`);
        const data = await response.json();
        setEmailAvailable(data.available);
      } catch (error) {
        setEmailAvailable(null);
      } finally {
        setEmailCheckLoading(false);
      }
    };

    const timeoutId = setTimeout(checkEmailAvailability, 500);
    return () => clearTimeout(timeoutId);
  }, [adminEmail]);

  const onSubmit = async (data: SignupFormData) => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/tenants/register', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          tenantName: data.tenantName,
          tenantSlug: data.tenantSlug,
          adminEmail: data.adminEmail,
          adminPassword: data.adminPassword,
          adminFirstName: data.adminFirstName,
          adminLastName: data.adminLastName,
          adminPhone: data.adminPhone,
          acceptTerms: data.acceptTerms,
        }),
      });

      const result = await response.json();

      if (!response.ok) {
        setError(result.error || 'Registration failed');
        return;
      }

      router.push('/auth/signin?message=registration-success');
    } catch (error) {
      setError('An error occurred during registration');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <form className={cn("flex flex-col gap-6", className)} onSubmit={handleSubmit(onSubmit)} {...props}>
      <FormError error={error} type="error" />
      
      <div className="grid gap-6">
        <FormFieldWrapper error={errors.tenantName?.message}>
          <Label htmlFor="tenantName">Organization Name</Label>
          <Input
            id="tenantName"
            type="text"
            placeholder="Acme Inc."
            {...register('tenantName')}
            className={cn(errors.tenantName && 'border-destructive focus-visible:ring-destructive')}
            required
          />
        </FormFieldWrapper>

        <FormFieldWrapper
          error={errors.tenantSlug?.message || (slugAvailable === false ? 'This slug is already taken' : undefined)}
          success={slugAvailable === true && tenantSlug && tenantSlug.length >= 3 ? 'Organization slug is available' : undefined}
        >
          <Label htmlFor="tenantSlug">Organization Slug</Label>
          <div className="relative">
            <Input
              id="tenantSlug"
              type="text"
              placeholder="acme-inc"
              {...register('tenantSlug')}
              className={cn(
                (errors.tenantSlug || slugAvailable === false) && 'border-destructive focus-visible:ring-destructive',
                slugAvailable === true && 'border-green-500 focus-visible:ring-green-500'
              )}
              required
            />
            {tenantSlug && tenantSlug.length >= 3 && (
              <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                {slugAvailable === true && (
                  <CheckIcon className="h-4 w-4 text-green-500" />
                )}
                {slugAvailable === false && (
                  <XMarkIcon className="h-4 w-4 text-red-500" />
                )}
              </div>
            )}
          </div>
        </FormFieldWrapper>

        <div className="grid grid-cols-2 gap-3">
          <FormFieldWrapper error={errors.adminFirstName?.message}>
            <Label htmlFor="adminFirstName">First Name</Label>
            <Input
              id="adminFirstName"
              type="text"
              placeholder="John"
              {...register('adminFirstName')}
              className={cn(errors.adminFirstName && 'border-destructive focus-visible:ring-destructive')}
              required
            />
          </FormFieldWrapper>
          <FormFieldWrapper error={errors.adminLastName?.message}>
            <Label htmlFor="adminLastName">Last Name</Label>
            <Input
              id="adminLastName"
              type="text"
              placeholder="Doe"
              {...register('adminLastName')}
              className={cn(errors.adminLastName && 'border-destructive focus-visible:ring-destructive')}
              required
            />
          </FormFieldWrapper>
        </div>

        <FormFieldWrapper
          error={errors.adminEmail?.message || (emailAvailable === false ? 'This email is already registered or has reached the organization limit' : undefined)}
          success={emailAvailable === true && adminEmail && adminEmail.includes('@') ? 'Email is available' : undefined}
        >
          <Label htmlFor="adminEmail">Email</Label>
          <div className="relative">
            <Input
              id="adminEmail"
              type="email"
              placeholder="<EMAIL>"
              {...register('adminEmail')}
              className={cn(
                (errors.adminEmail || emailAvailable === false) && 'border-destructive focus-visible:ring-destructive',
                emailAvailable === true && 'border-green-500 focus-visible:ring-green-500'
              )}
              required
            />
            {adminEmail && adminEmail.includes('@') && (
              <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                {emailCheckLoading && (
                  <ClockIcon className="h-4 w-4 text-gray-400 animate-spin" />
                )}
                {!emailCheckLoading && emailAvailable === true && (
                  <CheckIcon className="h-4 w-4 text-green-500" />
                )}
                {!emailCheckLoading && emailAvailable === false && (
                  <XMarkIcon className="h-4 w-4 text-red-500" />
                )}
              </div>
            )}
          </div>
        </FormFieldWrapper>

        <FormFieldWrapper error={errors.adminPhone?.message}>
          <Label htmlFor="adminPhone">Phone Number (Optional)</Label>
          <PhoneInput
            id="adminPhone"
            value={watch('adminPhone')}
            onChange={(value) => setValue('adminPhone', value || '')}
            placeholder="Enter your phone number"
            className={cn(errors.adminPhone && 'phone-input-error')}
          />
        </FormFieldWrapper>

        <FormFieldWrapper error={errors.adminPassword?.message}>
          <Label htmlFor="adminPassword">Password</Label>
          <div className="relative">
            <Input
              id="adminPassword"
              type={showPassword ? 'text' : 'password'}
              {...register('adminPassword')}
              className={cn(errors.adminPassword && 'border-destructive focus-visible:ring-destructive')}
              required
            />
            <button
              type="button"
              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground hover:text-foreground"
              onClick={() => setShowPassword(!showPassword)}
            >
              {showPassword ? (
                <EyeSlashIcon className="h-4 w-4" />
              ) : (
                <EyeIcon className="h-4 w-4" />
              )}
            </button>
          </div>
          {adminPassword && (
            <PasswordStrengthIndicator
              password={adminPassword}
              className="mt-2"
              showRequirements={true}
            />
          )}
        </FormFieldWrapper>

        <FormFieldWrapper error={errors.confirmPassword?.message}>
          <Label htmlFor="confirmPassword">Confirm Password</Label>
          <div className="relative">
            <Input
              id="confirmPassword"
              type={showConfirmPassword ? 'text' : 'password'}
              {...register('confirmPassword')}
              className={cn(errors.confirmPassword && 'border-destructive focus-visible:ring-destructive')}
              required
            />
            <button
              type="button"
              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground hover:text-foreground"
              onClick={() => setShowConfirmPassword(!showConfirmPassword)}
            >
              {showConfirmPassword ? (
                <EyeSlashIcon className="h-4 w-4" />
              ) : (
                <EyeIcon className="h-4 w-4" />
              )}
            </button>
          </div>
        </FormFieldWrapper>

        <FormFieldWrapper error={errors.acceptTerms?.message}>
          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id="acceptTerms"
              {...register('acceptTerms')}
              className={cn(
                "h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary",
                errors.acceptTerms && "border-destructive focus:ring-destructive"
              )}
            />
            <Label htmlFor="acceptTerms" className="text-sm">
              I accept the{' '}
              <TermsDialog>
                <button
                  type="button"
                  className="underline underline-offset-4 hover:text-primary text-primary font-medium"
                >
                  Terms and Conditions
                </button>
              </TermsDialog>
            </Label>
          </div>
        </FormFieldWrapper>

        <Button type="submit" className="w-full" loading={isLoading}>
          {isLoading ? 'Creating account...' : 'Create account'}
        </Button>
      </div>
      <div className="text-center text-sm">
        Already have an account?{" "}
        <Link href="/auth/signin" className="underline underline-offset-4">
          Sign in
        </Link>
      </div>
    </form>
  );
}
