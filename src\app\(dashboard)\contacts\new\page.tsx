'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { PermissionResource, PermissionAction } from '@/types';
import { PageLayout } from '@/components/layout/page-layout';
import { ModernContactForm } from '@/components/contacts/modern-contact-form';
import { PermissionGate } from '@/components/ui/permission-gate';
import { Button } from '@/components/ui/button';
import { ArrowLeftIcon } from '@heroicons/react/24/outline';

export default function NewContactPage() {
  const router = useRouter();
  const [loading, setLoading] = useState(false);

  const handleSave = async (contactData: Record<string, unknown>) => {
    setLoading(true);
    try {
      const response = await fetch('/api/contacts', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...contactData,
          companyId: contactData.companyId || null,
          ownerId: contactData.ownerId || null,
        }),
      });

      const data = await response.json();

      if (data.success) {
        router.push('/contacts');
      } else {
        throw new Error(data.error || 'Failed to create contact');
      }
    } catch (err) {
      console.error('Error creating contact:', err);
      alert(err instanceof Error ? err.message : 'Failed to create contact. Please try again.');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    router.push('/contacts');
  };

  const actions = (
    <Button variant="outline" size="sm" onClick={() => router.push('/contacts')}>
      <ArrowLeftIcon className="h-4 w-4 mr-2" />
      Back to Contacts
    </Button>
  );

  return (
    <PermissionGate
      resource={PermissionResource.CONTACTS}
      action={PermissionAction.CREATE}
    >
      <PageLayout
        title="Add New Contact"
        description="Create a new contact in your CRM system"
        actions={actions}
      >
        <ModernContactForm
          onSave={handleSave}
          onCancel={handleCancel}
          loading={loading}
        />
      </PageLayout>
    </PermissionGate>
  );
}
