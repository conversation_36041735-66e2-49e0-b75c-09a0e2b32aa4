'use client';

/**
 * Client-side password utilities that work in the browser
 * These are simpler versions that don't rely on Node.js crypto
 */

// Simple browser-compatible random number generator
function getSecureRandom(): number {
  if (typeof window !== 'undefined' && window.crypto && window.crypto.getRandomValues) {
    const array = new Uint32Array(1);
    window.crypto.getRandomValues(array);
    return array[0] / (0xffffffff + 1);
  }
  // Fallback to Math.random (less secure but works everywhere)
  return Math.random();
}

function getRandomInt(min: number, max: number): number {
  return Math.floor(getSecureRandom() * (max - min)) + min;
}

/**
 * Generate a simple but secure password for client-side use
 */
export function generateSimplePassword(length: number = 12): string {
  const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
  const lowercase = 'abcdefghijklmnopqrstuvwxyz';
  const numbers = '0123456789';
  const symbols = '!@#$%^&*';
  
  const allChars = uppercase + lowercase + numbers + symbols;
  
  let password = '';
  
  // Ensure at least one character from each type
  password += uppercase[getRandomInt(0, uppercase.length)];
  password += lowercase[getRandomInt(0, lowercase.length)];
  password += numbers[getRandomInt(0, numbers.length)];
  password += symbols[getRandomInt(0, symbols.length)];
  
  // Fill the rest randomly
  for (let i = 4; i < length; i++) {
    password += allChars[getRandomInt(0, allChars.length)];
  }
  
  // Shuffle the password
  return shuffleString(password);
}

/**
 * Generate a user-friendly password
 */
export function generateUserFriendlyPassword(): string {
  const adjectives = [
    'Happy', 'Bright', 'Swift', 'Calm', 'Bold', 'Smart', 'Quick', 'Wise',
    'Strong', 'Gentle', 'Brave', 'Clear', 'Fresh', 'Warm', 'Cool', 'Sharp'
  ];
  
  const nouns = [
    'Tiger', 'Eagle', 'River', 'Mountain', 'Ocean', 'Forest', 'Star', 'Moon',
    'Sun', 'Cloud', 'Wind', 'Fire', 'Stone', 'Tree', 'Flower', 'Bird'
  ];

  const adjective = adjectives[getRandomInt(0, adjectives.length)];
  const noun = nouns[getRandomInt(0, nouns.length)];
  const number = getRandomInt(10, 99);
  const specialChar = '!@#$%^&*'[getRandomInt(0, 8)];

  return `${adjective}${noun}${number}${specialChar}`;
}

/**
 * Shuffle a string randomly
 */
function shuffleString(str: string): string {
  const array = str.split('');
  for (let i = array.length - 1; i > 0; i--) {
    const j = getRandomInt(0, i + 1);
    [array[i], array[j]] = [array[j], array[i]];
  }
  return array.join('');
}

/**
 * Calculate password strength (client-side version)
 */
export function calculatePasswordStrength(password: string, minLength: number = 8) {
  const requirements = {
    minLength: password.length >= minLength,
    hasUppercase: /[A-Z]/.test(password),
    hasLowercase: /[a-z]/.test(password),
    hasNumber: /\d/.test(password),
    hasSpecialChar: /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password),
  };

  const metRequirements = Object.values(requirements).filter(Boolean).length;
  let score = 0;
  let level: 'weak' | 'fair' | 'good' | 'strong' = 'weak';
  const feedback: string[] = [];

  // Calculate score based on requirements met
  if (metRequirements >= 5) {
    score = 4;
    level = 'strong';
  } else if (metRequirements >= 4) {
    score = 3;
    level = 'good';
  } else if (metRequirements >= 3) {
    score = 2;
    level = 'fair';
  } else if (metRequirements >= 1) {
    score = 1;
    level = 'weak';
  }

  // Additional length bonus
  if (password.length >= 12) {
    score = Math.min(4, score + 1);
  }

  // Generate feedback
  if (!requirements.minLength) {
    feedback.push(`Password must be at least ${minLength} characters long`);
  }
  if (!requirements.hasUppercase) {
    feedback.push('Add at least one uppercase letter');
  }
  if (!requirements.hasLowercase) {
    feedback.push('Add at least one lowercase letter');
  }
  if (!requirements.hasNumber) {
    feedback.push('Add at least one number');
  }
  if (!requirements.hasSpecialChar) {
    feedback.push('Add at least one special character');
  }

  if (feedback.length === 0) {
    feedback.push('Password meets all requirements');
  }

  return {
    score,
    level,
    feedback,
    requirements,
  };
}
