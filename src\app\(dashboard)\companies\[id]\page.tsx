'use client';

import React, { useState, useEffect, use } from 'react';
import { useRouter } from 'next/navigation';
import { motion } from 'framer-motion';
import { 
  Building, 
  Globe, 
  Users, 
  TrendingUp, 
  Edit, 
  Trash2, 
  ArrowLeft,
  Mail,
  Phone,
  Calendar,
  User
} from 'lucide-react';
import { PermissionGate } from '@/components/ui/permission-gate';
import { PermissionResource, PermissionAction } from '@/types';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { EmptyState } from '@/components/ui/empty-state';
import { FormPageSkeleton } from '@/components/ui/skeleton-loaders';
import { ExclamationTriangleIcon } from '@heroicons/react/24/outline';
import { CompanyWithRelations } from '@/services/company-management';
import { PageLayout } from '@/components/layout/page-layout';
import { useDeleteConfirmationDialog } from '@/components/companies/delete-confirmation-dialog';

interface CompanyDetailPageProps {
  params: Promise<{
    id: string;
  }>;
}

export default function CompanyDetailPage({ params }: CompanyDetailPageProps) {
  const router = useRouter();
  const resolvedParams = use(params);
  const companyId = resolvedParams.id;

  const [company, setCompany] = useState<CompanyWithRelations | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Delete confirmation dialog
  const { showDeleteDialog, DialogComponent } = useDeleteConfirmationDialog();

  // Fetch company data
  useEffect(() => {
    const fetchCompany = async () => {
      try {
        const response = await fetch(`/api/companies/${companyId}`);
        const data = await response.json();

        if (data.success) {
          setCompany(data.data.company);
        } else {
          setError('Company not found');
        }
      } catch (err) {
        console.error('Failed to fetch company:', err);
        setError('Failed to load company data');
      } finally {
        setLoading(false);
      }
    };

    if (companyId) {
      fetchCompany();
    }
  }, [companyId]);

  // Handle delete company
  const handleDeleteCompany = async () => {
    if (!company) return;

    showDeleteDialog(company, async () => {
      try {
        const response = await fetch(`/api/companies?companyId=${company.id}`, {
          method: 'DELETE',
        });

        const data = await response.json();

        if (data.success) {
          router.push('/companies');
        } else {
          throw new Error(data.error || 'Failed to delete company');
        }
      } catch (err) {
        console.error('Delete company error:', err);
        throw err;
      }
    });
  };

  // Page actions
  const actions = [
    <Button
      key="back"
      variant="outline"
      onClick={() => router.push('/companies')}
      className="gap-2"
    >
      <ArrowLeft className="w-4 h-4" />
      Back to Companies
    </Button>,
  ];

  if (loading) {
    return (
      <PageLayout
        title="Company Details"
        description="View company information and related data"
        actions={actions}
      >
        <FormPageSkeleton />
      </PageLayout>
    );
  }

  if (error || !company) {
    return (
      <PageLayout
        title="Company Details"
        description="View company information and related data"
        actions={actions}
      >
        <Card>
          <CardContent className="p-8">
            <EmptyState
              icon={<ExclamationTriangleIcon className="w-12 h-12 text-muted-foreground" />}
              title="Company Not Found"
              description={error || "The company you're looking for doesn't exist or you don't have permission to view it."}
              action={{
                label: 'Back to Companies',
                onClick: () => router.push('/companies'),
                variant: 'primary'
              }}
            />
          </CardContent>
        </Card>
      </PageLayout>
    );
  }

  return (
    <PermissionGate
      resource={PermissionResource.COMPANIES}
      action={PermissionAction.READ}
      fallback={
        <PageLayout
          title="Company Details"
          description="View company information and related data"
          actions={actions}
        >
          <Card>
            <CardContent className="p-8">
              <EmptyState
                icon={<ExclamationTriangleIcon className="w-12 h-12 text-muted-foreground" />}
                title="Access Denied"
                description="You don't have permission to view this company. Please contact your administrator for access."
                action={{
                  label: 'Back to Companies',
                  onClick: () => router.push('/companies'),
                  variant: 'primary'
                }}
              />
            </CardContent>
          </Card>
        </PageLayout>
      }
    >
      <PageLayout
        title={company.name}
        description="Company details and related information"
        actions={[
          ...actions,
          <PermissionGate
            key="edit"
            resource={PermissionResource.COMPANIES}
            action={PermissionAction.UPDATE}
            requireOwnership={true}
            ownerId={company.ownerId}
            fallback={null}
          >
            <Button
              variant="outline"
              onClick={() => router.push(`/companies/${company.id}/edit`)}
              className="gap-2"
            >
              <Edit className="w-4 h-4" />
              Edit
            </Button>
          </PermissionGate>,
          <PermissionGate
            key="delete"
            resource={PermissionResource.COMPANIES}
            action={PermissionAction.DELETE}
            requireOwnership={true}
            ownerId={company.ownerId}
            fallback={null}
          >
            <Button
              variant="destructive"
              onClick={handleDeleteCompany}
              className="gap-2"
            >
              <Trash2 className="w-4 h-4" />
              Delete
            </Button>
          </PermissionGate>,
        ]}
      >
        <motion.div
          className="space-y-6"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          {/* Company Overview */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-3">
                <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                  <Building className="w-5 h-5 text-blue-600" />
                </div>
                Company Overview
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <label className="text-sm font-medium text-gray-500 dark:text-gray-400">
                      Company Name
                    </label>
                    <p className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                      {company.name}
                    </p>
                  </div>

                  {company.website && (
                    <div>
                      <label className="text-sm font-medium text-gray-500 dark:text-gray-400">
                        Website
                      </label>
                      <div className="flex items-center gap-2">
                        <Globe className="w-4 h-4 text-gray-400" />
                        <a
                          href={company.website}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
                        >
                          {company.website.replace(/^https?:\/\//, '')}
                        </a>
                      </div>
                    </div>
                  )}

                  {company.industry && (
                    <div>
                      <label className="text-sm font-medium text-gray-500 dark:text-gray-400">
                        Industry
                      </label>
                      <p className="text-gray-900 dark:text-gray-100">
                        {company.industry}
                      </p>
                    </div>
                  )}

                  {company.size && (
                    <div>
                      <label className="text-sm font-medium text-gray-500 dark:text-gray-400">
                        Company Size
                      </label>
                      <p className="text-gray-900 dark:text-gray-100">
                        {company.size}
                      </p>
                    </div>
                  )}
                </div>

                <div className="space-y-4">
                  {company.owner && (
                    <div>
                      <label className="text-sm font-medium text-gray-500 dark:text-gray-400">
                        Owner
                      </label>
                      <div className="flex items-center gap-2">
                        <User className="w-4 h-4 text-gray-400" />
                        <p className="text-gray-900 dark:text-gray-100">
                          {company.owner.firstName} {company.owner.lastName}
                        </p>
                      </div>
                    </div>
                  )}

                  <div>
                    <label className="text-sm font-medium text-gray-500 dark:text-gray-400">
                      Created
                    </label>
                    <div className="flex items-center gap-2">
                      <Calendar className="w-4 h-4 text-gray-400" />
                      <p className="text-gray-900 dark:text-gray-100">
                        {new Date(company.createdAt).toLocaleDateString()}
                      </p>
                    </div>
                  </div>

                  <div>
                    <label className="text-sm font-medium text-gray-500 dark:text-gray-400">
                      Last Updated
                    </label>
                    <div className="flex items-center gap-2">
                      <Calendar className="w-4 h-4 text-gray-400" />
                      <p className="text-gray-900 dark:text-gray-100">
                        {new Date(company.updatedAt).toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Statistics */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                    <Users className="w-5 h-5 text-green-600" />
                  </div>
                  <div>
                    <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                      {company._count?.contacts || 0}
                    </p>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      Contacts
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                    <TrendingUp className="w-5 h-5 text-blue-600" />
                  </div>
                  <div>
                    <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                      {company._count?.leads || 0}
                    </p>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      Leads
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center">
                    <TrendingUp className="w-5 h-5 text-purple-600" />
                  </div>
                  <div>
                    <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                      {company._count?.opportunities || 0}
                    </p>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      Opportunities
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Related Contacts */}
          {company.contacts && company.contacts.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                    <Users className="w-5 h-5 text-green-600" />
                  </div>
                  Related Contacts ({company.contacts.length})
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {company.contacts.map((contact) => (
                    <div
                      key={contact.id}
                      className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors cursor-pointer"
                      onClick={() => router.push(`/contacts/${contact.id}`)}
                    >
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                          <span className="text-sm font-semibold text-blue-600">
                            {contact.firstName?.charAt(0)}{contact.lastName?.charAt(0)}
                          </span>
                        </div>
                        <div className="flex-1 min-w-0">
                          <p className="font-medium text-gray-900 dark:text-gray-100 truncate">
                            {contact.firstName} {contact.lastName}
                          </p>
                          {contact.email && (
                            <div className="flex items-center gap-1 text-sm text-gray-500 dark:text-gray-400">
                              <Mail className="w-3 h-3" />
                              <span className="truncate">{contact.email}</span>
                            </div>
                          )}
                          {contact.phone && (
                            <div className="flex items-center gap-1 text-sm text-gray-500 dark:text-gray-400">
                              <Phone className="w-3 h-3" />
                              <span>{contact.phone}</span>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </motion.div>

        {/* Delete Confirmation Dialog */}
        <DialogComponent />
      </PageLayout>
    </PermissionGate>
  );
}
