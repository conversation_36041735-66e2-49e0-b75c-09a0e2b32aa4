import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { handoverManagementService } from '@/services/handover-management';
import { z } from 'zod';

// Request validation schemas
const updateHandoverSchema = z.object({
  title: z.string().min(1, 'Title is required').max(255).optional(),
  description: z.string().optional(),
  status: z.enum(['pending', 'in_progress', 'completed', 'cancelled']).optional(),
  priority: z.enum(['low', 'medium', 'high', 'urgent']).optional(),
  salesRepId: z.string().optional(),
  deliveryManagerId: z.string().optional(),
  assignedTeamIds: z.array(z.string()).optional(),
  scheduledDate: z.string().datetime().optional().transform(val => val ? new Date(val) : undefined),
  estimatedDuration: z.number().int().positive().optional(),
  checklistProgress: z.number().min(0).max(100).optional(),
  qualityScore: z.number().min(1).max(5).optional(),
  clientSatisfaction: z.number().min(1).max(5).optional(),
  deliveryFeedback: z.string().optional(),
});

interface RouteParams {
  params: Promise<{
    id: string;
  }>;
}

/**
 * GET /api/handovers/[id] - Get a specific handover
 */
export async function GET(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get tenantId from user's tenants array (users have one tenant in this system)
    let tenantId: string | null = null;
    if (session.user.tenants && session.user.tenants.length > 0) {
      tenantId = session.user.tenants[0].id;
    }

    if (!tenantId) {
      return NextResponse.json({ error: 'Tenant not found' }, { status: 400 });
    }

    const resolvedParams = await params;
    const handover = await handoverManagementService.getHandoverById(
      tenantId,
      resolvedParams.id
    );

    if (!handover) {
      return NextResponse.json({ error: 'Handover not found' }, { status: 404 });
    }

    return NextResponse.json(handover);
  } catch (error) {
    console.error('Error fetching handover:', error);
    return NextResponse.json(
      { error: 'Failed to fetch handover' },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/handovers/[id] - Update a handover
 */
export async function PUT(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get tenantId from user's tenants array (users have one tenant in this system)
    let tenantId: string | null = null;
    if (session.user.tenants && session.user.tenants.length > 0) {
      tenantId = session.user.tenants[0].id;
    }

    if (!tenantId) {
      return NextResponse.json({ error: 'Tenant not found' }, { status: 400 });
    }

    const body = await request.json();
    const validatedData = updateHandoverSchema.parse(body);

    const resolvedParams = await params;
    const handover = await handoverManagementService.updateHandover(
      tenantId,
      resolvedParams.id,
      validatedData
    );

    return NextResponse.json(handover);
  } catch (error) {
    console.error('Error updating handover:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request data', details: error.errors },
        { status: 400 }
      );
    }

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to update handover' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/handovers/[id] - Delete a handover
 */
export async function DELETE(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get tenantId from user's tenants array (users have one tenant in this system)
    let tenantId: string | null = null;
    if (session.user.tenants && session.user.tenants.length > 0) {
      tenantId = session.user.tenants[0].id;
    }

    if (!tenantId) {
      return NextResponse.json({ error: 'Tenant not found' }, { status: 400 });
    }

    const resolvedParams = await params;
    await handoverManagementService.deleteHandover(tenantId, resolvedParams.id);

    return NextResponse.json({ message: 'Handover deleted successfully' });
  } catch (error) {
    console.error('Error deleting handover:', error);
    
    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to delete handover' },
      { status: 500 }
    );
  }
}
