import { NextRequest, NextResponse } from 'next/server';
import { withPermission, AuthenticatedRequest } from '@/lib/permission-middleware';
import { DocumentTemplateManagementService } from '@/services/document-template-management';
import { PermissionResource, PermissionAction } from '@/types';
import { z } from 'zod';

interface RouteParams {
  params: Promise<{ id: string }>;
}

/**
 * GET /api/document-templates/[id]
 * Get a single document template
 */
export const GET = withPermission({
  resource: PermissionResource.PROPOSALS,
  action: PermissionAction.READ,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest, { params }: RouteParams) => {
  try {
    const { id: templateId } = await params;

    const templateService = new DocumentTemplateManagementService();
    const template = await templateService.getTemplate(templateId, req.tenantId);

    if (!template) {
      return NextResponse.json(
        { error: 'Template not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: { template }
    });

  } catch (error) {
    console.error('Get template error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch template' },
      { status: 500 }
    );
  }
});

/**
 * PUT /api/document-templates/[id]
 * Update a document template
 */
export const PUT = withPermission({
  resource: PermissionResource.PROPOSALS,
  action: PermissionAction.UPDATE,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest, { params }: RouteParams) => {
  try {
    const { id: templateId } = await params;
    const formData = await req.formData();
    
    // Extract form fields
    const updateData: any = {};
    
    if (formData.get('name')) updateData.name = formData.get('name') as string;
    if (formData.get('description')) updateData.description = formData.get('description') as string;
    if (formData.get('category')) updateData.category = formData.get('category') as string;
    if (formData.get('templateType')) updateData.templateType = formData.get('templateType') as string;
    if (formData.get('isDefault') !== null) updateData.isDefault = formData.get('isDefault') === 'true';
    if (formData.get('isActive') !== null) updateData.isActive = formData.get('isActive') === 'true';
    
    if (formData.get('styling')) {
      updateData.styling = JSON.parse(formData.get('styling') as string);
    }
    if (formData.get('branding')) {
      updateData.branding = JSON.parse(formData.get('branding') as string);
    }
    if (formData.get('variables')) {
      updateData.variables = JSON.parse(formData.get('variables') as string);
    }
    if (formData.get('htmlTemplate')) {
      updateData.htmlTemplate = formData.get('htmlTemplate') as string;
    }


    // Handle file upload if present
    const file = formData.get('file') as File | null;
    if (file) {
      // TODO: Implement file upload logic
      // For now, we'll just store the filename
      updateData.filePath = file.name;
    }

    const templateService = new DocumentTemplateManagementService();
    const template = await templateService.updateTemplate(
      templateId,
      req.tenantId,
      updateData,
      req.userId
    );

    return NextResponse.json({
      success: true,
      data: { template },
      message: 'Template updated successfully'
    });

  } catch (error) {
    console.error('Update template error:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          error: 'Validation failed',
          details: error.errors
        },
        { status: 400 }
      );
    }

    if (error instanceof Error && error.message === 'Template not found or access denied') {
      return NextResponse.json(
        { error: 'Template not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to update template' },
      { status: 500 }
    );
  }
});

/**
 * DELETE /api/document-templates/[id]
 * Delete a document template
 */
export const DELETE = withPermission({
  resource: PermissionResource.PROPOSALS,
  action: PermissionAction.DELETE,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest, { params }: RouteParams) => {
  try {
    const { id: templateId } = await params;

    const templateService = new DocumentTemplateManagementService();
    await templateService.deleteTemplate(templateId, req.tenantId);

    return NextResponse.json({
      success: true,
      message: 'Template deleted successfully'
    });

  } catch (error) {
    console.error('Delete template error:', error);
    
    if (error instanceof Error && error.message === 'Template not found or access denied') {
      return NextResponse.json(
        { error: 'Template not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to delete template' },
      { status: 500 }
    );
  }
});
