'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { useToast } from '@/hooks/use-toast';
import { PermissionGate } from '@/components/ui/permission-gate';
import { PermissionAction, PermissionResource } from '@/types';
import {
  UserIcon,
  MagnifyingGlassIcon,
  TrashIcon,
  CalendarIcon,
  ClockIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';

interface RoleUser {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  status: string;
  assignedAt: Date;
  expiresAt: Date | null;
  assignedBy: string | null;
  lastLoginAt: Date | null;
}

interface RoleUsersTabProps {
  roleId: string;
  roleName: string;
  isSystemRole: boolean;
}

export function RoleUsersTab({ roleId, roleName, isSystemRole }: RoleUsersTabProps) {
  const [users, setUsers] = useState<RoleUser[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalUsers, setTotalUsers] = useState(0);
  const [isRemoving, setIsRemoving] = useState<string | null>(null);
  const [includeExpired, setIncludeExpired] = useState(false);

  const { toast } = useToast();

  useEffect(() => {
    fetchUsers();
  }, [roleId, currentPage, searchTerm, includeExpired]);

  const fetchUsers = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: '20',
        ...(searchTerm && { search: searchTerm }),
        ...(includeExpired && { includeExpired: 'true' })
      });

      const response = await fetch(`/api/roles/${roleId}/users?${params}`);
      if (response.ok) {
        const data = await response.json();
        setUsers(data.data.users || []);
        setTotalPages(data.data.pagination.totalPages);
        setTotalUsers(data.data.pagination.total);
      } else {
        const errorData = await response.json();
        setError(errorData.error || 'Failed to fetch users');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch users');
    } finally {
      setIsLoading(false);
    }
  };

  const handleRemoveUser = async (userId: string, userEmail: string) => {
    if (!confirm(`Are you sure you want to remove ${userEmail} from the ${roleName} role?`)) {
      return;
    }

    try {
      setIsRemoving(userId);
      const response = await fetch(`/api/roles/${roleId}/users`, {
        method: 'DELETE',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ userId })
      });

      if (response.ok) {
        toast({
          title: 'Success',
          description: `User removed from ${roleName} role successfully`,
        });
        fetchUsers();
      } else {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to remove user');
      }
    } catch (err) {
      toast({
        title: 'Error',
        description: err instanceof Error ? err.message : 'Failed to remove user',
        variant: 'destructive',
      });
    } finally {
      setIsRemoving(null);
    }
  };

  const formatDate = (date: Date | string | null) => {
    if (!date) return 'Never';
    const d = new Date(date);
    return d.toLocaleDateString() + ' ' + d.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const isExpired = (expiresAt: Date | null) => {
    if (!expiresAt) return false;
    return new Date(expiresAt) < new Date();
  };

  const getUserDisplayName = (user: RoleUser) => {
    const fullName = `${user.firstName} ${user.lastName}`.trim();
    return fullName || user.email;
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'active': return 'text-green-600 bg-green-100';
      case 'inactive': return 'text-red-600 bg-red-100';
      case 'pending': return 'text-yellow-600 bg-yellow-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  if (error) {
    return (
      <Card className="border-red-200 bg-red-50">
        <CardContent className="p-12 text-center">
          <ExclamationTriangleIcon className="w-12 h-12 text-red-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Error Loading Users</h3>
          <p className="text-gray-500 mb-4">{error}</p>
          <Button onClick={fetchUsers}>Try Again</Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with Search and Filters */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Assigned Users</CardTitle>
              <p className="text-sm text-gray-600 mt-1">
                {totalUsers} user{totalUsers !== 1 ? 's' : ''} assigned to this role
              </p>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-4 mb-4">
            <div className="relative flex-1">
              <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="Search users..."
                value={searchTerm}
                onChange={(e) => {
                  setSearchTerm(e.target.value);
                  setCurrentPage(1);
                }}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            <label className="flex items-center gap-2">
              <input
                type="checkbox"
                checked={includeExpired}
                onChange={(e) => {
                  setIncludeExpired(e.target.checked);
                  setCurrentPage(1);
                }}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <span className="text-sm text-gray-600">Include expired assignments</span>
            </label>
          </div>
        </CardContent>
      </Card>

      {/* Users List */}
      {isLoading ? (
        <div className="flex items-center justify-center py-12">
          <LoadingSpinner />
        </div>
      ) : users.length === 0 ? (
        <Card>
          <CardContent className="p-12 text-center">
            <UserIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No Users Assigned</h3>
            <p className="text-gray-500">
              {searchTerm ? 'No users match your search criteria.' : 'No users are currently assigned to this role.'}
            </p>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-4">
          {users.map((user) => (
            <Card key={user.id} className={`${isExpired(user.expiresAt) ? 'border-red-200 bg-red-50' : ''}`}>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    <div className="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center">
                      <UserIcon className="w-5 h-5 text-gray-600" />
                    </div>
                    <div>
                      <h4 className="font-medium text-gray-900">{getUserDisplayName(user)}</h4>
                      <p className="text-sm text-gray-500">{user.email}</p>
                      <div className="flex items-center gap-4 mt-1">
                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(user.status)}`}>
                          {user.status}
                        </span>
                        {isExpired(user.expiresAt) && (
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium text-red-600 bg-red-100">
                            Expired
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-4">
                    <div className="text-right text-sm text-gray-500">
                      <div className="flex items-center gap-1 mb-1">
                        <CalendarIcon className="w-4 h-4" />
                        <span>Assigned: {formatDate(user.assignedAt)}</span>
                      </div>
                      {user.expiresAt && (
                        <div className="flex items-center gap-1 mb-1">
                          <ClockIcon className="w-4 h-4" />
                          <span>Expires: {formatDate(user.expiresAt)}</span>
                        </div>
                      )}
                      {user.assignedBy && (
                        <div className="text-xs">
                          Assigned by: {user.assignedBy}
                        </div>
                      )}
                      {user.lastLoginAt && (
                        <div className="text-xs">
                          Last login: {formatDate(user.lastLoginAt)}
                        </div>
                      )}
                    </div>
                    
                    <PermissionGate
                      resource={PermissionResource.USERS}
                      action={PermissionAction.MANAGE}
                    >
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleRemoveUser(user.id, user.email)}
                        disabled={isRemoving === user.id || (isSystemRole && !user.firstName)}
                        className="text-red-600 hover:text-red-700 hover:bg-red-50"
                      >
                        {isRemoving === user.id ? (
                          <LoadingSpinner className="w-4 h-4" />
                        ) : (
                          <TrashIcon className="w-4 h-4" />
                        )}
                      </Button>
                    </PermissionGate>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Pagination */}
      {totalPages > 1 && (
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <p className="text-sm text-gray-600">
                Page {currentPage} of {totalPages}
              </p>
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                  disabled={currentPage === 1}
                >
                  Previous
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                  disabled={currentPage === totalPages}
                >
                  Next
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
