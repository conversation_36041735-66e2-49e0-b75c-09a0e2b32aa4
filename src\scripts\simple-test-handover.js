/**
 * Simple test to create a basic handover
 */

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function createSimpleHandover() {
  console.log('🚀 Creating Simple Test Handover...\n');

  try {
    // 1. Find tenant and opportunity
    const tenant = await prisma.tenant.findFirst({
      where: { status: 'active' }
    });

    if (!tenant) {
      console.log('❌ No tenant found');
      return;
    }

    console.log(`✅ Found tenant: ${tenant.name}`);

    const opportunity = await prisma.opportunity.findFirst({
      where: { tenantId: tenant.id }
    });

    if (!opportunity) {
      console.log('❌ No opportunity found');
      return;
    }

    console.log(`✅ Found opportunity: ${opportunity.title}`);

    const user = await prisma.user.findFirst();

    if (!user) {
      console.log('❌ No user found');
      return;
    }

    console.log(`✅ Found user: ${user.email}`);

    // 2. Create minimal handover
    console.log('\n📝 Creating handover...');
    
    const handover = await prisma.projectHandover.create({
      data: {
        tenantId: tenant.id,
        opportunityId: opportunity.id,
        title: `Test Handover - ${new Date().toISOString().split('T')[0]}`,
        description: 'Test handover for delete functionality testing',
        status: 'pending',
        priority: 'medium',
        checklistProgress: 0,
        documentsCount: 0,
        qaThreadsCount: 0,
        assignedTeamIds: [],
        createdById: user.id
      }
    });

    console.log(`✅ Created handover: ${handover.title}`);
    console.log(`📋 ID: ${handover.id}`);
    console.log(`🔗 URL: /handovers/${handover.id}`);

    return handover;

  } catch (error) {
    console.error('❌ Error:', error.message);
    console.error('Full error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createSimpleHandover();
