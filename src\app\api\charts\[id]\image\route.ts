import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { hasPermission } from '@/lib/permissions';
import { PermissionResource, PermissionAction } from '@/types';
import { mermaidRenderer } from '@/services/mermaid-renderer';
import { prisma } from '@/lib/prisma';

/**
 * GET /api/charts/[id]/image
 * Serve rendered chart image
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user's current tenant
    const currentTenant = session.user.tenants?.[0];
    if (!currentTenant) {
      return NextResponse.json({ error: 'No tenant access' }, { status: 403 });
    }

    // Check permission for proposal reading (charts are part of proposals)
    const hasAccess = await hasPermission(
      PermissionResource.PROPOSALS,
      PermissionAction.READ,
      currentTenant.id
    );

    if (!hasAccess) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const chartId = params.id;

    // Get chart details to determine content type
    const chart = await prisma.proposalChart.findFirst({
      where: {
        id: chartId,
        tenantId: currentTenant.id
      }
    });

    if (!chart) {
      return NextResponse.json(
        { error: 'Chart not found or access denied' },
        { status: 404 }
      );
    }

    if (!chart.renderedImagePath) {
      return NextResponse.json(
        { error: 'Chart image not available. Please render the chart first.' },
        { status: 404 }
      );
    }

    // Get the image data
    const imageData = await mermaidRenderer.getChartImage(chartId, currentTenant.id);

    if (!imageData) {
      return NextResponse.json(
        { error: 'Chart image file not found' },
        { status: 404 }
      );
    }

    // Determine content type based on file extension
    const fileExtension = chart.renderedImagePath.split('.').pop()?.toLowerCase();
    let contentType = 'application/octet-stream';
    
    switch (fileExtension) {
      case 'png':
        contentType = 'image/png';
        break;
      case 'svg':
        contentType = 'image/svg+xml';
        break;
      case 'jpg':
      case 'jpeg':
        contentType = 'image/jpeg';
        break;
    }

    // Set cache headers for better performance
    const headers = new Headers({
      'Content-Type': contentType,
      'Content-Length': imageData.length.toString(),
      'Cache-Control': 'public, max-age=3600', // Cache for 1 hour
      'ETag': `"${chartId}-${chart.updatedAt.getTime()}"`,
      'Content-Disposition': `inline; filename="chart_${chartId}.${fileExtension}"`
    });

    // Check if client has cached version
    const ifNoneMatch = request.headers.get('if-none-match');
    const etag = headers.get('ETag');
    
    if (ifNoneMatch === etag) {
      return new NextResponse(null, { status: 304, headers });
    }

    return new NextResponse(imageData, { headers });

  } catch (error) {
    console.error('Error serving chart image:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * @swagger
 * /api/charts/{id}/image:
 *   get:
 *     summary: Get rendered chart image
 *     description: Serve the rendered image file for a chart
 *     tags: [Charts]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Chart ID
 *     responses:
 *       200:
 *         description: Chart image file
 *         content:
 *           image/png:
 *             schema:
 *               type: string
 *               format: binary
 *           image/svg+xml:
 *             schema:
 *               type: string
 *               format: binary
 *         headers:
 *           Content-Type:
 *             description: MIME type of the image
 *             schema:
 *               type: string
 *           Content-Length:
 *             description: Size of the image in bytes
 *             schema:
 *               type: integer
 *           Cache-Control:
 *             description: Cache control header
 *             schema:
 *               type: string
 *           ETag:
 *             description: Entity tag for caching
 *             schema:
 *               type: string
 *       304:
 *         description: Not Modified - client has cached version
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - insufficient permissions
 *       404:
 *         description: Chart not found or image not available
 *       500:
 *         description: Internal server error
 */
