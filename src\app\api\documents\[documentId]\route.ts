import { NextResponse } from 'next/server';
import { DocumentManagementService } from '@/services/document-management';
import { FileStorageService } from '@/lib/file-storage';
import { withPermission, AuthenticatedRequest } from '@/lib/permission-middleware';
import { PermissionResource, PermissionAction } from '@/types';
import { z } from 'zod';

const documentService = new DocumentManagementService();

/**
 * GET /api/documents/[documentId]
 * Get a specific document by ID
 */
export const GET = withPermission({
  resource: PermissionResource.DOCUMENTS,
  action: PermissionAction.READ,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest) => {
  try {
    const url = new URL(req.url);
    const pathSegments = url.pathname.split('/');
    const documentId = pathSegments[pathSegments.indexOf('documents') + 1];

    const document = await documentService.getDocument(documentId, req.tenantId);

    if (!document) {
      return NextResponse.json(
        { error: 'Document not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: { document },
      message: 'Document retrieved successfully'
    });

  } catch (error) {
    console.error('Get document error:', error);

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
});

/**
 * PUT /api/documents/[documentId]
 * Update document metadata
 */
export const PUT = withPermission({
  resource: PermissionResource.DOCUMENTS,
  action: PermissionAction.UPDATE,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest) => {
  try {
    const url = new URL(req.url);
    const pathSegments = url.pathname.split('/');
    const documentId = pathSegments[pathSegments.indexOf('documents') + 1];
    const body = await req.json();

    const document = await documentService.updateDocument(
      documentId,
      req.tenantId,
      body,
      req.userId
    );

    return NextResponse.json({
      success: true,
      data: { document },
      message: 'Document updated successfully'
    });

  } catch (error) {
    console.error('Update document error:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          error: 'Validation failed',
          details: error.errors.map(e => ({
            field: e.path.join('.'),
            message: e.message
          }))
        },
        { status: 400 }
      );
    }

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
});

/**
 * DELETE /api/documents/[documentId]
 * Delete a document
 */
export const DELETE = withPermission({
  resource: PermissionResource.DOCUMENTS,
  action: PermissionAction.DELETE,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest) => {
  try {
    const url = new URL(req.url);
    const pathSegments = url.pathname.split('/');
    const documentId = pathSegments[pathSegments.indexOf('documents') + 1];

    const document = await documentService.deleteDocument(documentId, req.tenantId);

    // Delete file from disk
    try {
      await FileStorageService.deleteFile(document.filePath);
    } catch (error) {
      console.error('Failed to delete file from disk:', error);
      // Don't throw error as database deletion was successful
    }

    return NextResponse.json({
      success: true,
      message: 'Document deleted successfully'
    });

  } catch (error) {
    console.error('Delete document error:', error);

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
});
