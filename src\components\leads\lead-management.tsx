'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import {
  Plus,
  Search,
  Filter,
  MoreHorizontal,
  Edit,
  Trash2,
  ClipboardList,
  Users,
  TrendingUp,
  Download,
  Upload,
  Eye,
  Star,
  Clock,
  Target,
  DollarSign,
  MessageSquare,
  ArrowRightLeft,
  CheckCircle
} from 'lucide-react';
import { LeadWithRelations } from '@/services/lead-management';
import { usePermissions } from '@/components/providers/permission-provider';
import { PermissionResource, PermissionAction } from '@/types';
import { PermissionGate } from '@/components/ui/permission-gate';
import { PermissionAwareTable } from '@/components/ui/permission-aware-table';
import { EmptyState } from '@/components/ui/empty-state';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger 
} from '@/components/ui/dropdown-menu';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';
import { useEnhancedDeleteDialog, deleteConfigurations } from '@/components/ui/enhanced-delete-dialog';
import { toastFunctions as toast } from '@/hooks/use-toast';
import { useFormatters } from '@/hooks/use-formatters';

import { ConvertLeadDialog } from './convert-lead-dialog';
import { BulkConvertLeadsDialog } from './bulk-convert-leads-dialog';

interface LeadManagementProps {
  className?: string;
}

export function LeadManagement({ className }: LeadManagementProps) {
  const router = useRouter();
  const { hasPermission } = usePermissions();
  const { formatMoney } = useFormatters();
  const [leads, setLeads] = useState<LeadWithRelations[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [sourceFilter, setSourceFilter] = useState<string>('');
  const [priorityFilter, setPriorityFilter] = useState<string>('all');
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [stats, setStats] = useState<any>(null);

  const [convertDialogOpen, setConvertDialogOpen] = useState(false);
  const [selectedLeadForConversion, setSelectedLeadForConversion] = useState<LeadWithRelations | null>(null);
  const [bulkConvertDialogOpen, setBulkConvertDialogOpen] = useState(false);
  const [selectedLeadsForBulkConversion, setSelectedLeadsForBulkConversion] = useState<Array<{ id: string; title: string }>>([]);

  const { showDeleteDialog, DialogComponent } = useEnhancedDeleteDialog();

  // Load leads
  const loadLeads = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        page: page.toString(),
        limit: '10',
        ...(searchTerm && { search: searchTerm }),
        ...(statusFilter && statusFilter !== 'all' && { status: statusFilter }),
        ...(sourceFilter && { leadSourceId: sourceFilter }),
        ...(priorityFilter && priorityFilter !== 'all' && { priority: priorityFilter })
      });

      console.log('Loading leads with params:', params.toString());
      const response = await fetch(`/api/leads?${params}`);
      const data = await response.json();

      console.log('Leads API response:', data);

      if (data.success) {
        setLeads(data.data.leads);
        setTotalPages(data.data.totalPages);
        console.log('Loaded leads:', data.data.leads.length);
      } else {
        console.error('API returned error:', data.error);
        toast.error(data.error || 'Failed to load leads');
      }
    } catch (error) {
      console.error('Error loading leads:', error);
      toast.error('Failed to load leads');
    } finally {
      setLoading(false);
    }
  };

  // Handle export
  const handleExport = async (format: 'csv' | 'json') => {
    try {
      console.log('Starting export...', format);
      const params = new URLSearchParams({
        format,
        ...(searchTerm && { search: searchTerm }),
        ...(statusFilter && statusFilter !== 'all' && { status: statusFilter }),
        ...(sourceFilter && { leadSourceId: sourceFilter }),
        ...(priorityFilter && priorityFilter !== 'all' && { priority: priorityFilter }),
      });

      console.log('Export params:', params.toString());
      const response = await fetch(`/api/leads/export?${params}`);
      console.log('Export response status:', response.status);

      if (response.ok) {
        if (format === 'json') {
          const data = await response.json();
          const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
          const filename = `leads-export-${new Date().toISOString().split('T')[0]}.json`;

          if (typeof window !== 'undefined') {
            const url = window.URL.createObjectURL(blob);
            const a = window.document.createElement('a');
            a.href = url;
            a.download = filename;
            window.document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            window.document.body.removeChild(a);
          }
        } else {
          const blob = await response.blob();
          const filename = `leads-export-${new Date().toISOString().split('T')[0]}.csv`;

          if (typeof window !== 'undefined') {
            const url = window.URL.createObjectURL(blob);
            const a = window.document.createElement('a');
            a.href = url;
            a.download = filename;
            window.document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            window.document.body.removeChild(a);
          }
        }
        toast.success(`Successfully exported ${format.toUpperCase()} file`);
      } else {
        const errorData = await response.json();
        toast.error(errorData.error || 'Failed to export leads');
      }
    } catch (err) {
      console.error('Export error:', err);
      toast.error('Failed to export leads');
    }
  };

  // Load statistics
  const loadStats = async () => {
    try {
      const response = await fetch('/api/leads/stats');
      const data = await response.json();
      if (data.success) {
        setStats(data.data.stats);
      }
    } catch (error) {
      console.error('Error loading lead stats:', error);
    }
  };

  useEffect(() => {
    loadLeads();
    loadStats();
  }, [page, searchTerm, statusFilter, sourceFilter, priorityFilter]);

  // Handle delete lead
  const handleDeleteLead = async (leadId: string, leadTitle: string) => {
    console.log('Attempting to delete lead:', { leadId, leadTitle });

    try {
      await showDeleteDialog({
        ...deleteConfigurations.lead,
        itemName: leadTitle,
        onConfirm: async () => {
          console.log('Delete confirmed, making API call...');
          const response = await fetch(`/api/leads/${leadId}`, {
            method: 'DELETE'
          });

          console.log('Delete API response:', response.status, response.statusText);

          if (response.ok) {
            const data = await response.json();
            console.log('Delete successful:', data);
            toast.success('Lead deleted successfully');
            loadLeads();
            loadStats();
          } else {
            const errorData = await response.json();
            console.error('Delete failed:', errorData);
            throw new Error(errorData.error || 'Failed to delete lead');
          }
        }
      });
    } catch (error) {
      console.error('Error deleting lead:', error);
      toast.error('Failed to delete lead');
    }
  };

  // Handle bulk delete
  const handleBulkDelete = async (selectedIds: string[]) => {
    console.log('Attempting to bulk delete leads:', selectedIds);

    try {
      await showDeleteDialog({
        ...deleteConfigurations.bulkLeads,
        itemName: `${selectedIds.length} lead(s)`,
        onConfirm: async () => {
          console.log('Bulk delete confirmed, making API call...');
          const response = await fetch('/api/leads', {
            method: 'DELETE',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ leadIds: selectedIds })
          });

          console.log('Bulk delete API response:', response.status, response.statusText);

          if (response.ok) {
            const data = await response.json();
            console.log('Bulk delete successful:', data);
            toast.success(`${data.data.deletedCount} lead(s) deleted successfully`);
            loadLeads();
            loadStats();
          } else {
            const errorData = await response.json();
            console.error('Bulk delete failed:', errorData);
            throw new Error(errorData.error || 'Failed to delete leads');
          }
        }
      });
    } catch (error) {
      console.error('Error deleting leads:', error);
      toast.error('Failed to delete leads');
    }
  };



  const handleConvertLead = (lead: LeadWithRelations) => {
    setSelectedLeadForConversion(lead);
    setConvertDialogOpen(true);
  };

  const handleConversionSuccess = () => {
    setConvertDialogOpen(false);
    setSelectedLeadForConversion(null);
    loadLeads();
    loadStats();
  };

  const handleBulkConvert = (selectedIds: string[]) => {
    const selectedLeadsData = leads
      .filter(lead => selectedIds.includes(lead.id))
      .map(lead => ({ id: lead.id, title: lead.title }));

    setSelectedLeadsForBulkConversion(selectedLeadsData);
    setBulkConvertDialogOpen(true);
  };

  const handleBulkConversionSuccess = () => {
    setBulkConvertDialogOpen(false);
    setSelectedLeadsForBulkConversion([]);
    loadLeads();
    loadStats();
  };

  // Handle bulk export
  const handleBulkExport = async (selectedIds: string[]) => {
    try {
      console.log('Starting bulk export for selected leads:', selectedIds);

      // For bulk export, we'll export all leads matching current filters
      // but could be enhanced to export only selected leads
      const params = new URLSearchParams({
        format: 'csv',
        ...(searchTerm && { search: searchTerm }),
        ...(statusFilter && statusFilter !== 'all' && { status: statusFilter }),
        ...(sourceFilter && { leadSourceId: sourceFilter }),
        ...(priorityFilter && priorityFilter !== 'all' && { priority: priorityFilter }),
      });

      const response = await fetch(`/api/leads/export?${params}`);

      if (response.ok) {
        const blob = await response.blob();
        const filename = `leads-export-${new Date().toISOString().split('T')[0]}.csv`;

        if (typeof window !== 'undefined') {
          const url = window.URL.createObjectURL(blob);
          const a = window.document.createElement('a');
          a.href = url;
          a.download = filename;
          window.document.body.appendChild(a);
          a.click();
          window.URL.revokeObjectURL(url);
          window.document.body.removeChild(a);
        }
        toast.success(`Successfully exported ${selectedIds.length} selected leads`);
      } else {
        const errorData = await response.json();
        toast.error(errorData.error || 'Failed to export leads');
      }
    } catch (err) {
      console.error('Bulk export error:', err);
      toast.error('Failed to export selected leads');
    }
  };

  // Status badge component
  const StatusBadge = ({ status, isConverted }: { status: string; isConverted?: boolean }) => {
    const statusConfig = {
      new: { color: 'bg-blue-100 text-blue-800', label: 'New' },
      contacted: { color: 'bg-yellow-100 text-yellow-800', label: 'Contacted' },
      qualified: { color: 'bg-green-100 text-green-800', label: 'Qualified' },
      proposal: { color: 'bg-purple-100 text-purple-800', label: 'Proposal' },
      negotiation: { color: 'bg-orange-100 text-orange-800', label: 'Negotiation' },
      closed_won: { color: 'bg-emerald-100 text-emerald-800', label: 'Won' },
      closed_lost: { color: 'bg-red-100 text-red-800', label: 'Lost' },
      converted: { color: 'bg-indigo-100 text-indigo-800', label: 'Converted' }
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.new;

    return (
      <div className="flex items-center gap-2">
        <Badge className={config.color}>
          {config.label}
        </Badge>
        {isConverted && (
          <Badge className="bg-indigo-100 text-indigo-800">
            <ArrowRightLeft className="w-3 h-3 mr-1" />
            Converted
          </Badge>
        )}
      </div>
    );
  };

  // Priority badge component
  const PriorityBadge = ({ priority }: { priority: string }) => {
    const priorityConfig = {
      low: { color: 'bg-gray-100 text-gray-800', label: 'Low' },
      medium: { color: 'bg-blue-100 text-blue-800', label: 'Medium' },
      high: { color: 'bg-orange-100 text-orange-800', label: 'High' },
      urgent: { color: 'bg-red-100 text-red-800', label: 'Urgent' }
    };

    const config = priorityConfig[priority as keyof typeof priorityConfig] || priorityConfig.medium;
    
    return (
      <Badge className={config.color}>
        {config.label}
      </Badge>
    );
  };

  // Table columns
  const columns = [
    {
      key: 'title',
      label: 'Lead Title',
      render: (lead: LeadWithRelations) => (
        <div className="flex flex-col">
          <button
            onClick={() => router.push(`/leads/${lead.id}`)}
            className="font-medium text-gray-900 hover:text-primary-600 dark:hover:text-primary-400 transition-colors hover:underline focus:outline-none focus:underline text-left"
          >
            {lead.title}
          </button>
          {(lead as any).description && (
            <span className="text-sm text-gray-500 truncate max-w-xs">
              {(lead as any).description}
            </span>
          )}
        </div>
      )
    },
    {
      key: 'contact',
      label: 'Contact',
      render: (lead: LeadWithRelations) => (
        <div className="flex flex-col">
          {lead.contact ? (
            <>
              <span className="font-medium text-gray-900">
                {lead.contact.firstName} {lead.contact.lastName}
              </span>
              <span className="text-sm text-gray-500">{lead.contact.email}</span>
            </>
          ) : (
            <span className="text-gray-400">No contact</span>
          )}
        </div>
      )
    },
    {
      key: 'company',
      label: 'Company',
      render: (lead: LeadWithRelations) => (
        lead.company ? (
          <span className="text-gray-900">{lead.company.name}</span>
        ) : (
          <span className="text-gray-400">No company</span>
        )
      )
    },
    {
      key: 'status',
      label: 'Status',
      render: (lead: LeadWithRelations) => (
        <StatusBadge
          status={lead.status}
          isConverted={(lead as any).isConverted}
        />
      )
    },
    {
      key: 'priority',
      label: 'Priority',
      render: (lead: LeadWithRelations) => <PriorityBadge priority={(lead as any).priority || 'medium'} />
    },
    {
      key: 'score',
      label: 'Score',
      render: (lead: LeadWithRelations) => (
        <div className="flex items-center space-x-1">
          <Star className="w-4 h-4 text-yellow-400" />
          <span className="font-medium">{lead.score}</span>
        </div>
      )
    },
    {
      key: 'value',
      label: 'Value',
      render: (lead: LeadWithRelations) => (
        (lead as any).value ? (
          <div className="flex items-center space-x-1">
            <DollarSign className="w-4 h-4 text-green-500" />
            <span className="font-medium">{formatMoney((lead as any).value)}</span>
          </div>
        ) : (
          <span className="text-gray-400">-</span>
        )
      )
    },
    {
      key: 'owner',
      label: 'Owner',
      render: (lead: LeadWithRelations) => (
        lead.owner ? (
          <span className="text-gray-900">
            {lead.owner.firstName} {lead.owner.lastName}
          </span>
        ) : (
          <span className="text-gray-400">Unassigned</span>
        )
      )
    }
  ];

  return (
    <div className={`space-y-6 ${className}`}>


      {/* Statistics Cards */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Leads</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.total}</p>
                </div>
                <ClipboardList className="w-8 h-8 text-blue-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Average Score</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.averageScore.toFixed(1)}</p>
                </div>
                <Star className="w-8 h-8 text-yellow-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Conversion Rate</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.conversionRate.toFixed(1)}%</p>
                </div>
                <Target className="w-8 h-8 text-green-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Value</p>
                  <p className="text-2xl font-bold text-gray-900">{formatMoney(stats.totalValue)}</p>
                </div>
                <DollarSign className="w-8 h-8 text-emerald-500" />
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Filters */}
      <Card>
        <CardContent className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div>
              <Input
                placeholder="Search leads..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full"
              />
            </div>
            
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger>
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Statuses</SelectItem>
                <SelectItem value="new">New</SelectItem>
                <SelectItem value="contacted">Contacted</SelectItem>
                <SelectItem value="qualified">Qualified</SelectItem>
                <SelectItem value="proposal">Proposal</SelectItem>
                <SelectItem value="negotiation">Negotiation</SelectItem>
                <SelectItem value="closed_won">Won</SelectItem>
                <SelectItem value="closed_lost">Lost</SelectItem>
              </SelectContent>
            </Select>

            <Select value={priorityFilter} onValueChange={setPriorityFilter}>
              <SelectTrigger>
                <SelectValue placeholder="Filter by priority" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Priorities</SelectItem>
                <SelectItem value="low">Low</SelectItem>
                <SelectItem value="medium">Medium</SelectItem>
                <SelectItem value="high">High</SelectItem>
                <SelectItem value="urgent">Urgent</SelectItem>
              </SelectContent>
            </Select>

            <Input
              placeholder="Filter by source..."
              value={sourceFilter}
              onChange={(e) => setSourceFilter(e.target.value)}
            />
          </div>
        </CardContent>
      </Card>

      {/* Leads Table */}
      {leads.length === 0 && !loading ? (
        <Card>
          <CardContent>
            <EmptyState
              icon={<ClipboardList className="w-12 h-12" />}
              title="No leads found"
              description="Get started by creating your first lead to track potential sales opportunities."
              action={{
                label: 'Add Lead',
                onClick: () => router.push('/leads/new'),
                variant: 'primary'
              }}
            />
          </CardContent>
        </Card>
      ) : (
        <Card>
          <PermissionAwareTable
            data={leads}
            columns={columns}
            actions={[
              {
                label: 'View',
                icon: Eye,
                onClick: (lead) => router.push(`/leads/${lead.id}`),
                permission: { resource: PermissionResource.LEADS, action: PermissionAction.READ },
                tooltip: 'View lead details and activity history',
                color: 'blue'
              },
              {
                label: 'Convert to Opportunity',
                icon: ArrowRightLeft,
                onClick: (lead) => handleConvertLead(lead),
                permission: { resource: PermissionResource.OPPORTUNITIES, action: PermissionAction.CREATE },
                disabled: (lead) => (lead as any).isConverted || lead.status === 'converted',
                tooltip: 'Convert this lead to an opportunity',
                color: 'green'
              },
              {
                label: 'Edit',
                icon: Edit,
                onClick: (lead) => router.push(`/leads/${lead.id}/edit`),
                permission: { resource: PermissionResource.LEADS, action: PermissionAction.UPDATE },
                tooltip: 'Edit lead information and details',
                color: 'blue'
              },
              {
                label: 'Delete',
                icon: Trash2,
                onClick: (lead) => handleDeleteLead(lead.id, lead.title),
                permission: { resource: PermissionResource.LEADS, action: PermissionAction.DELETE },
                variant: 'destructive',
                tooltip: 'Permanently delete this lead',
                color: 'red'
              }
            ]}
            bulkActions={[
              {
                label: 'Export Selected',
                icon: Download,
                onClick: (leads) => handleBulkExport(leads.map(l => l.id)),
                permission: { resource: PermissionResource.LEADS, action: PermissionAction.READ },
                tooltip: 'Export selected leads to CSV file',
                color: 'green'
              },
              {
                label: 'Convert to Opportunities',
                icon: CheckCircle,
                onClick: (leads) => handleBulkConvert(leads.map(l => l.id)),
                permission: { resource: PermissionResource.OPPORTUNITIES, action: PermissionAction.CREATE },
                tooltip: 'Convert selected leads to opportunities',
                color: 'blue'
              },
              {
                label: 'Delete Selected',
                icon: Trash2,
                onClick: (leads) => handleBulkDelete(leads.map(l => l.id)),
                permission: { resource: PermissionResource.LEADS, action: PermissionAction.DELETE },
                variant: 'destructive',
                tooltip: 'Permanently delete selected leads',
                color: 'red',
                confirmMessage: 'Are you sure you want to delete {count} lead(s)? This action cannot be undone.'
              }
            ]}
            resource={PermissionResource.LEADS}
            idField="id"
            ownershipField="ownerId"
            loading={loading}
            emptyMessage="No leads found"
          />
        </Card>
      )}



      {/* Convert Lead Dialog */}
      {selectedLeadForConversion && (
        <ConvertLeadDialog
          open={convertDialogOpen}
          onOpenChange={setConvertDialogOpen}
          leadId={selectedLeadForConversion.id}
          leadTitle={selectedLeadForConversion.title}
          leadOwnerId={selectedLeadForConversion.ownerId || undefined}
          onSuccess={handleConversionSuccess}
        />
      )}

      {/* Bulk Convert Leads Dialog */}
      <BulkConvertLeadsDialog
        open={bulkConvertDialogOpen}
        onOpenChange={setBulkConvertDialogOpen}
        selectedLeads={selectedLeadsForBulkConversion}
        onSuccess={handleBulkConversionSuccess}
      />

      {/* Enhanced Delete Dialog */}
      <DialogComponent />
    </div>
  );
}
