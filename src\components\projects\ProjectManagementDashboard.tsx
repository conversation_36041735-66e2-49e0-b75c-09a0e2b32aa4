'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { 
  Plus, 
  FolderOpen, 
  Calendar, 
  Users, 
  DollarSign, 
  AlertTriangle,
  Clock,
  TrendingUp,
  CheckCircle,
  XCircle,
  Pause
} from 'lucide-react';
import Link from 'next/link';
import { PermissionGate } from '@/components/ui/permission-gate';
import { PermissionResource, PermissionAction } from '@/types';
import { EmptyState } from '@/components/ui/empty-state';
import { LoadingSpinner } from '@/components/ui/loading-spinner';

interface ProjectStats {
  total: number;
  byStatus: Record<string, number>;
  totalBudget: number;
  totalEstimatedHours: number;
  totalActualHours: number;
  averageProgress: number;
}

interface Project {
  id: string;
  name: string;
  description?: string;
  status: string;
  priority: string;
  progress: number;
  startDate?: string;
  endDate?: string;
  budget?: number;
  estimatedHours?: number;
  actualHours?: number;
  clientPortalEnabled: boolean;
  manager?: {
    id: string;
    firstName?: string;
    lastName?: string;
    email: string;
  };
  opportunity?: {
    id: string;
    title: string;
    value?: number;
  };
}

interface ProjectsNeedingAttention {
  overdue: Project[];
  atRisk: Project[];
  noActivity: Project[];
}

export function ProjectManagementDashboard() {
  const [projects, setProjects] = useState<Project[]>([]);
  const [stats, setStats] = useState<ProjectStats | null>(null);
  const [attention, setAttention] = useState<ProjectsNeedingAttention | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('overview');

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Fetch projects, stats, and attention data in parallel
      const [projectsRes, statsRes, attentionRes] = await Promise.all([
        fetch('/api/projects?limit=6&includeDetails=true'),
        fetch('/api/projects/stats'),
        fetch('/api/projects/attention'),
      ]);

      if (!projectsRes.ok || !statsRes.ok || !attentionRes.ok) {
        throw new Error('Failed to fetch project data');
      }

      const [projectsData, statsData, attentionData] = await Promise.all([
        projectsRes.json(),
        statsRes.json(),
        attentionRes.json(),
      ]);

      setProjects(projectsData.data.projects);
      setStats(statsData.data.stats);
      setAttention(attentionData.data);
    } catch (err) {
      console.error('Error fetching project data:', err);
      setError('Failed to load project data');
    } finally {
      setLoading(false);
    }
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      planning: { label: 'Planning', color: 'bg-blue-100 text-blue-800', icon: Clock },
      in_progress: { label: 'In Progress', color: 'bg-green-100 text-green-800', icon: TrendingUp },
      on_hold: { label: 'On Hold', color: 'bg-yellow-100 text-yellow-800', icon: Pause },
      completed: { label: 'Completed', color: 'bg-gray-100 text-gray-800', icon: CheckCircle },
      cancelled: { label: 'Cancelled', color: 'bg-red-100 text-red-800', icon: XCircle },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.planning;
    const Icon = config.icon;
    
    return (
      <Badge className={config.color}>
        <Icon className="w-3 h-3 mr-1" />
        {config.label}
      </Badge>
    );
  };

  const getPriorityBadge = (priority: string) => {
    const priorityConfig = {
      low: { label: 'Low', color: 'bg-gray-100 text-gray-800' },
      medium: { label: 'Medium', color: 'bg-blue-100 text-blue-800' },
      high: { label: 'High', color: 'bg-orange-100 text-orange-800' },
      urgent: { label: 'Urgent', color: 'bg-red-100 text-red-800' },
    };

    const config = priorityConfig[priority as keyof typeof priorityConfig] || priorityConfig.medium;
    
    return (
      <Badge variant="outline" className={config.color}>
        {config.label}
      </Badge>
    );
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent className="pt-6">
          <EmptyState
            icon={<AlertTriangle className="w-12 h-12 text-red-500" />}
            title="Error Loading Projects"
            description={error}
            action={{
              label: 'Retry',
              onClick: fetchData,
              variant: 'primary'
            }}
          />
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Project Management</h1>
          <p className="text-muted-foreground">
            Manage and track your project portfolio
          </p>
        </div>
        <PermissionGate
          resource={PermissionResource.PROJECTS}
          action={PermissionAction.CREATE}
        >
          <Button asChild>
            <Link href="/projects/new">
              <Plus className="h-4 w-4 mr-2" />
              New Project
            </Link>
          </Button>
        </PermissionGate>
      </div>

      {/* Stats Cards */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center">
                <FolderOpen className="h-4 w-4 text-muted-foreground" />
                <div className="ml-2">
                  <p className="text-sm font-medium text-muted-foreground">Total Projects</p>
                  <p className="text-2xl font-bold">{stats.total}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center">
                <DollarSign className="h-4 w-4 text-muted-foreground" />
                <div className="ml-2">
                  <p className="text-sm font-medium text-muted-foreground">Total Budget</p>
                  <p className="text-2xl font-bold">{formatCurrency(stats.totalBudget)}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center">
                <Clock className="h-4 w-4 text-muted-foreground" />
                <div className="ml-2">
                  <p className="text-sm font-medium text-muted-foreground">Estimated Hours</p>
                  <p className="text-2xl font-bold">{stats.totalEstimatedHours.toLocaleString()}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center">
                <TrendingUp className="h-4 w-4 text-muted-foreground" />
                <div className="ml-2">
                  <p className="text-sm font-medium text-muted-foreground">Avg Progress</p>
                  <p className="text-2xl font-bold">{Math.round(stats.averageProgress)}%</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Main Content Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="attention">
            Needs Attention
            {attention && (attention.overdue.length + attention.atRisk.length + attention.noActivity.length) > 0 && (
              <Badge variant="destructive" className="ml-2 h-5 w-5 p-0 text-xs">
                {attention.overdue.length + attention.atRisk.length + attention.noActivity.length}
              </Badge>
            )}
          </TabsTrigger>
          <TabsTrigger value="all">All Projects</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          {/* Recent Projects */}
          <Card>
            <CardHeader>
              <CardTitle>Recent Projects</CardTitle>
              <CardDescription>Your most recently updated projects</CardDescription>
            </CardHeader>
            <CardContent>
              {projects.length === 0 ? (
                <EmptyState
                  icon={<FolderOpen className="w-12 h-12 text-muted-foreground" />}
                  title="No projects found"
                  description="Create your first project to get started"
                  action={{
                    label: 'Create Project',
                    onClick: () => window.location.href = '/projects/new',
                    variant: 'primary'
                  }}
                />
              ) : (
                <div className="space-y-4">
                  {projects.map((project) => (
                    <div key={project.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          <h3 className="font-semibold">{project.name}</h3>
                          {getStatusBadge(project.status)}
                          {getPriorityBadge(project.priority)}
                        </div>
                        {project.description && (
                          <p className="text-sm text-muted-foreground mb-2">{project.description}</p>
                        )}
                        <div className="flex items-center gap-4 text-sm text-muted-foreground">
                          {project.manager && (
                            <span className="flex items-center gap-1">
                              <Users className="w-4 h-4" />
                              {project.manager.firstName} {project.manager.lastName}
                            </span>
                          )}
                          {project.endDate && (
                            <span className="flex items-center gap-1">
                              <Calendar className="w-4 h-4" />
                              Due {formatDate(project.endDate)}
                            </span>
                          )}
                          {project.budget && (
                            <span className="flex items-center gap-1">
                              <DollarSign className="w-4 h-4" />
                              {formatCurrency(project.budget)}
                            </span>
                          )}
                        </div>
                      </div>
                      <div className="flex items-center gap-4">
                        <div className="text-right">
                          <div className="text-sm font-medium">{Math.round(project.progress)}%</div>
                          <Progress value={project.progress} className="w-20" />
                        </div>
                        <Button variant="outline" size="sm" asChild>
                          <Link href={`/projects/${project.id}`}>
                            View
                          </Link>
                        </Button>
                      </div>
                    </div>
                  ))}
                  <div className="text-center pt-4">
                    <Button variant="outline" asChild>
                      <Link href="/projects">View All Projects</Link>
                    </Button>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="attention" className="space-y-4">
          {attention && (
            <>
              {/* Overdue Projects */}
              {attention.overdue.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2 text-red-600">
                      <AlertTriangle className="w-5 h-5" />
                      Overdue Projects ({attention.overdue.length})
                    </CardTitle>
                    <CardDescription>Projects that have passed their due date</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {attention.overdue.map((project) => (
                        <div key={project.id} className="flex items-center justify-between p-3 border border-red-200 rounded-lg bg-red-50">
                          <div>
                            <h4 className="font-medium">{project.name}</h4>
                            <p className="text-sm text-muted-foreground">
                              Due: {project.endDate && formatDate(project.endDate)}
                            </p>
                          </div>
                          <Button variant="outline" size="sm" asChild>
                            <Link href={`/projects/${project.id}`}>Review</Link>
                          </Button>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* At Risk Projects */}
              {attention.atRisk.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2 text-orange-600">
                      <Clock className="w-5 h-5" />
                      At Risk Projects ({attention.atRisk.length})
                    </CardTitle>
                    <CardDescription>Projects due soon with low progress</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {attention.atRisk.map((project) => (
                        <div key={project.id} className="flex items-center justify-between p-3 border border-orange-200 rounded-lg bg-orange-50">
                          <div>
                            <h4 className="font-medium">{project.name}</h4>
                            <p className="text-sm text-muted-foreground">
                              Progress: {Math.round(project.progress)}% | Due: {project.endDate && formatDate(project.endDate)}
                            </p>
                          </div>
                          <Button variant="outline" size="sm" asChild>
                            <Link href={`/projects/${project.id}`}>Review</Link>
                          </Button>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* No Activity Projects */}
              {attention.noActivity.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2 text-yellow-600">
                      <Pause className="w-5 h-5" />
                      Inactive Projects ({attention.noActivity.length})
                    </CardTitle>
                    <CardDescription>Projects with no recent activity</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {attention.noActivity.map((project) => (
                        <div key={project.id} className="flex items-center justify-between p-3 border border-yellow-200 rounded-lg bg-yellow-50">
                          <div>
                            <h4 className="font-medium">{project.name}</h4>
                            <p className="text-sm text-muted-foreground">
                              Status: {project.status} | Progress: {Math.round(project.progress)}%
                            </p>
                          </div>
                          <Button variant="outline" size="sm" asChild>
                            <Link href={`/projects/${project.id}`}>Review</Link>
                          </Button>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}

              {attention.overdue.length === 0 && attention.atRisk.length === 0 && attention.noActivity.length === 0 && (
                <Card>
                  <CardContent className="pt-6">
                    <EmptyState
                      icon={<CheckCircle className="w-12 h-12 text-green-500" />}
                      title="All projects are on track"
                      description="No projects need immediate attention"
                    />
                  </CardContent>
                </Card>
              )}
            </>
          )}
        </TabsContent>

        <TabsContent value="all">
          <Card>
            <CardContent className="pt-6">
              <div className="text-center">
                <Button asChild>
                  <Link href="/projects">View All Projects</Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
