import { NextResponse } from 'next/server';
import { ContactManagementService, ContactFilters } from '@/services/contact-management';
import { withPermission, AuthenticatedRequest } from '@/lib/permission-middleware';
import { PermissionResource, PermissionAction } from '@/types';
import { z } from 'zod';

const contactService = new ContactManagementService();

// Validation schema for export parameters
const exportQuerySchema = z.object({
  format: z.enum(['csv', 'json']).default('csv'),
  search: z.string().optional(),
  companyId: z.string().optional(),
  ownerId: z.string().optional(),
  hasEmail: z.string().transform(val => val === 'true').optional(),
  hasPhone: z.string().transform(val => val === 'true').optional(),
});

/**
 * GET /api/contacts/export
 * Export contacts to CSV or JSON format
 */
export const GET = withPermission({
  resource: PermissionResource.CONTACTS,
  action: PermissionAction.READ,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest) => {
  try {
    const { searchParams } = new URL(req.url);
    const queryParams = Object.fromEntries(searchParams.entries());
    
    const validatedQuery = exportQuerySchema.parse(queryParams);
    
    const filters: ContactFilters = {
      search: validatedQuery.search,
      companyId: validatedQuery.companyId,
      ownerId: validatedQuery.ownerId,
      hasEmail: validatedQuery.hasEmail,
      hasPhone: validatedQuery.hasPhone,
      limit: 10000, // Large limit for export
      sortBy: 'firstName',
      sortOrder: 'asc',
    };

    // Get all contacts matching the filters
    const result = await contactService.getContacts(req.tenantId, filters);
    const contacts = result.contacts;

    if (contacts.length === 0) {
      return NextResponse.json(
        { error: 'No contacts found to export' },
        { status: 404 }
      );
    }

    const timestamp = new Date().toISOString().split('T')[0];
    let filename = `contacts-export-${timestamp}`;
    let data: string | Buffer;
    let contentType: string;

    if (validatedQuery.format === 'json') {
      // JSON Export
      const exportData = contacts.map(contact => ({
        id: contact.id,
        firstName: contact.firstName,
        lastName: contact.lastName,
        email: contact.email,
        phone: contact.phone,
        company: contact.company?.name || '',
        owner: contact.owner ? `${contact.owner.firstName} ${contact.owner.lastName}` : '',
        createdAt: contact.createdAt,
        updatedAt: contact.updatedAt,
      }));

      data = JSON.stringify(exportData, null, 2);
      contentType = 'application/json';
      filename += '.json';
    } else {
      // CSV Export
      const headers = [
        'ID',
        'First Name',
        'Last Name',
        'Email',
        'Phone',
        'Company',
        'Owner',
        'Created At',
        'Updated At'
      ];

      const csvRows = contacts.map(contact => [
        contact.id,
        contact.firstName,
        contact.lastName,
        contact.email || '',
        contact.phone || '',
        contact.company?.name || '',
        contact.owner ? `${contact.owner.firstName} ${contact.owner.lastName}` : '',
        new Date(contact.createdAt).toLocaleDateString(),
        new Date(contact.updatedAt).toLocaleDateString(),
      ]);

      // Escape CSV values that contain commas or quotes
      const escapeCsvValue = (value: string) => {
        if (value.includes(',') || value.includes('"') || value.includes('\n')) {
          return `"${value.replace(/"/g, '""')}"`;
        }
        return value;
      };

      // Add UTF-8 BOM for proper Arabic character support
      const BOM = '\uFEFF';
      const csvContent = BOM + [
        headers.join(','),
        ...csvRows.map(row => row.map(cell => escapeCsvValue(String(cell))).join(','))
      ].join('\n');

      data = csvContent;
      contentType = 'text/csv; charset=utf-8';
      filename += '.csv';
    }

    const headers: Record<string, string> = {
      'Content-Type': contentType,
      'Content-Disposition': `attachment; filename="${filename}"`,
      'Cache-Control': 'no-cache',
    };

    return new NextResponse(data, { headers });

  } catch (error) {
    console.error('Export contacts error:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          error: 'Invalid export parameters',
          details: error.errors.map(e => ({
            field: e.path.join('.'),
            message: e.message
          }))
        },
        { status: 400 }
      );
    }

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
});
