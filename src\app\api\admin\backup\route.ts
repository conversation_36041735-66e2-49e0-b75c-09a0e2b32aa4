import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { DatabaseBackupService } from '@/lib/database-backup';
import { z } from 'zod';

/**
 * @swagger
 * /api/admin/backup:
 *   post:
 *     summary: Create database backup
 *     description: Create full database backup or tenant-specific backup (Admin only)
 *     tags:
 *       - Admin - Backup
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               type:
 *                 type: string
 *                 enum: [full, tenant]
 *                 description: Type of backup to create
 *               tenantId:
 *                 type: string
 *                 description: Tenant ID (required for tenant backup)
 *               includeData:
 *                 type: boolean
 *                 default: true
 *                 description: Whether to include data in backup
 *               compress:
 *                 type: boolean
 *                 default: true
 *                 description: Whether to compress the backup file
 *     responses:
 *       200:
 *         description: Backup created successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/ApiResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         backupPath:
 *                           type: string
 *                         size:
 *                           type: number
 *                         type:
 *                           type: string
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - Admin access required
 *       400:
 *         description: Validation error
 */

const createBackupSchema = z.object({
  type: z.enum(['full', 'tenant']),
  tenantId: z.string().optional(),
  includeData: z.boolean().default(true),
  compress: z.boolean().default(true)
}).refine((data) => {
  if (data.type === 'tenant' && !data.tenantId) {
    return false;
  }
  return true;
}, {
  message: "tenantId is required for tenant backup",
  path: ["tenantId"]
});

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user is platform admin
    if (!session.user.isPlatformAdmin) {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      );
    }

    const body = await request.json();
    const validatedData = createBackupSchema.parse(body);

    const backupService = new DatabaseBackupService();
    let backupPath: string;

    if (validatedData.type === 'full') {
      backupPath = await backupService.createFullBackup({
        includeData: validatedData.includeData,
        compress: validatedData.compress
      });
    } else {
      backupPath = await backupService.createTenantBackup(
        validatedData.tenantId!,
        {
          includeData: validatedData.includeData,
          compress: validatedData.compress
        }
      );
    }

    // Get file size
    const fs = require('fs/promises');
    const stats = await fs.stat(backupPath);

    return NextResponse.json({
      success: true,
      data: {
        backupPath,
        size: stats.size,
        type: validatedData.type,
        compressed: validatedData.compress,
        includeData: validatedData.includeData
      },
      message: 'Backup created successfully'
    });

  } catch (error) {
    console.error('Backup creation error:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          error: 'Validation failed',
          details: error.errors.map(e => ({
            field: e.path.join('.'),
            message: e.message
          }))
        },
        { status: 400 }
      );
    }

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * @swagger
 * /api/admin/backup:
 *   put:
 *     summary: Restore database from backup
 *     description: Restore database from backup file (Admin only)
 *     tags:
 *       - Admin - Backup
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - backupFile
 *             properties:
 *               backupFile:
 *                 type: string
 *                 description: Path to backup file
 *               targetDatabase:
 *                 type: string
 *                 description: Target database name (optional)
 *               dropExisting:
 *                 type: boolean
 *                 default: false
 *                 description: Whether to drop existing database
 *               tenantMapping:
 *                 type: object
 *                 description: Map old tenant IDs to new ones
 *     responses:
 *       200:
 *         description: Database restored successfully
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - Admin access required
 *       400:
 *         description: Validation error
 */

const restoreBackupSchema = z.object({
  backupFile: z.string().min(1, 'Backup file path is required'),
  targetDatabase: z.string().optional(),
  dropExisting: z.boolean().default(false),
  tenantMapping: z.record(z.string()).optional()
});

export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user is platform admin
    if (!session.user.isPlatformAdmin) {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      );
    }

    const body = await request.json();
    const validatedData = restoreBackupSchema.parse(body);

    const backupService = new DatabaseBackupService();
    
    await backupService.restoreFromBackup({
      backupFile: validatedData.backupFile,
      targetDatabase: validatedData.targetDatabase,
      dropExisting: validatedData.dropExisting,
      tenantMapping: validatedData.tenantMapping
    });

    return NextResponse.json({
      success: true,
      message: 'Database restored successfully'
    });

  } catch (error) {
    console.error('Backup restore error:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          error: 'Validation failed',
          details: error.errors.map(e => ({
            field: e.path.join('.'),
            message: e.message
          }))
        },
        { status: 400 }
      );
    }

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
