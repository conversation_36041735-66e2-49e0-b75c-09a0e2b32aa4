@echo off
REM Database initialization script for CRM Platform
REM This script completely initializes the database from scratch

setlocal enabledelayedexpansion

REM Colors for terminal output
set "RED=[91m"
set "GREEN=[92m"
set "YELLOW=[93m"
set "BLUE=[94m"
set "CYAN=[96m"
set "NC=[0m"

REM Configuration
set "ENVIRONMENT=%~1"
if "%ENVIRONMENT%"=="" set "ENVIRONMENT=dev"

echo %CYAN%=======================================================%NC%
echo %CYAN%             CRM Platform Database Initializer         %NC%
echo %CYAN%=======================================================%NC%
echo.

echo %YELLOW%Initializing database for %ENVIRONMENT% environment...%NC%

REM Setup environment-specific configuration
set "COMPOSE_FILE=docker-compose.yml"
set "COMPOSE_OVERRIDE="

if "%ENVIRONMENT%"=="dev" (
    set "COMPOSE_OVERRIDE=-f docker-compose.override.yml"
) else if "%ENVIRONMENT%"=="staging" (
    set "COMPOSE_OVERRIDE=-f docker-compose.staging.yml"
) else if "%ENVIRONMENT%"=="prod" (
    set "COMPOSE_OVERRIDE=-f docker-compose.prod.yml"
) else (
    echo %RED%Error: Unknown environment '%ENVIRONMENT%'%NC%
    exit /b 1
)

REM Confirm before proceeding in production
if "%ENVIRONMENT%"=="prod" (
    echo %RED%WARNING: You are about to initialize the PRODUCTION database!%NC%
    echo %RED%This will DELETE ALL DATA and cannot be undone!%NC%
    echo.
    set /p CONFIRM="Are you absolutely sure? Type 'YES' to confirm: "
    if /i not "!CONFIRM!"=="YES" (
        echo %YELLOW%Database initialization cancelled.%NC%
        exit /b 0
    )
)

REM Step 1: Drop the database if it exists
echo %YELLOW%Dropping existing database...%NC%
docker-compose -f %COMPOSE_FILE% %COMPOSE_OVERRIDE% exec -T postgres psql -U admin -d postgres -c "DROP DATABASE IF EXISTS crm_platform WITH (FORCE);"

REM Step 2: Create a fresh database
echo %YELLOW%Creating new database...%NC%
docker-compose -f %COMPOSE_FILE% %COMPOSE_OVERRIDE% exec -T postgres psql -U admin -d postgres -c "CREATE DATABASE crm_platform;"

REM Step 3: Generate Prisma client
echo %YELLOW%Generating Prisma client...%NC%
docker-compose -f %COMPOSE_FILE% %COMPOSE_OVERRIDE% exec -T app npx prisma generate

REM Step 4: Push the Prisma schema to the database
echo %YELLOW%Pushing Prisma schema to database...%NC%
docker-compose -f %COMPOSE_FILE% %COMPOSE_OVERRIDE% exec -T app npx prisma db push --force-reset

REM Step 5: Run SQL initialization scripts directly (if needed)
echo %YELLOW%Running SQL initialization scripts...%NC%
docker-compose -f %COMPOSE_FILE% %COMPOSE_OVERRIDE% exec -T postgres psql -U admin -d crm_platform -f /app/scripts/init-db/02-seed-data.sql

REM Step 6: Mark migrations as applied in the _prisma_migrations table
echo %YELLOW%Setting up Prisma migrations tracking...%NC%
docker-compose -f %COMPOSE_FILE% %COMPOSE_OVERRIDE% exec -T postgres psql -U admin -d crm_platform -c "CREATE TABLE IF NOT EXISTS _prisma_migrations (id character varying(36) NOT NULL, checksum character varying(64) NOT NULL, finished_at timestamp with time zone, migration_name character varying(255) NOT NULL, logs text, rolled_back_at timestamp with time zone, started_at timestamp with time zone NOT NULL DEFAULT now(), applied_steps_count integer NOT NULL DEFAULT 0, CONSTRAINT _prisma_migrations_pkey PRIMARY KEY (id));"

REM Mark all migrations as applied
for /f "delims=" %%m in ('dir /b /ad "prisma\migrations"') do (
    echo %YELLOW%Marking migration %%m as applied...%NC%
    docker-compose -f %COMPOSE_FILE% %COMPOSE_OVERRIDE% exec -T postgres psql -U admin -d crm_platform -c "INSERT INTO _prisma_migrations (id, checksum, migration_name, finished_at, applied_steps_count) VALUES (gen_random_uuid(), 'manually-applied', '%%m', now(), 1);"
)

REM Step 7: Run Prisma migrate deploy to ensure schema is up to date
echo %YELLOW%Running Prisma migrate deploy...%NC%
docker-compose -f %COMPOSE_FILE% %COMPOSE_OVERRIDE% exec -T app npx prisma migrate deploy

if %ERRORLEVEL% equ 0 (
    echo %GREEN%Database initialization completed successfully!%NC%
    
    REM Ask if user wants to seed the database
    set /p SEED_DB="Do you want to seed the database with initial data? (y/n): "
    if /i "%SEED_DB%"=="y" (
        echo %YELLOW%Seeding database...%NC%
        call scripts\seed-db.bat %ENVIRONMENT%
    )
) else (
    echo %RED%Failed to initialize database.%NC%
    exit /b 1
)

exit /b 0
