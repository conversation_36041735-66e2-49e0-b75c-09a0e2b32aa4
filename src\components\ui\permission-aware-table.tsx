'use client';

import React, { useState, useMemo } from 'react';
import { usePermissions } from '@/components/providers/permission-provider';
import { PermissionAction, PermissionResource } from '@/types';
import {
  ChevronUpIcon,
  ChevronDownIcon,
  TrashIcon,
  PencilIcon,
  EyeIcon
} from '@heroicons/react/24/outline';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

// Simple tooltip component without animations
const SimpleTooltip = ({ children, content, side = "top" }: {
  children: React.ReactNode;
  content: React.ReactNode;
  side?: "top" | "bottom" | "left" | "right";
}) => {
  const [isVisible, setIsVisible] = useState(false);

  return (
    <div
      className="relative inline-block"
      onMouseEnter={() => setIsVisible(true)}
      onMouseLeave={() => setIsVisible(false)}
    >
      {children}
      {isVisible && (
        <div
          className={cn(
            "absolute z-50 px-3 py-2 text-sm font-medium rounded-md shadow-lg border-0 pointer-events-none",
            "bg-gradient-to-r from-gray-900 to-gray-800 text-white",
            "dark:from-gray-100 dark:to-gray-50 dark:text-gray-900",
            "whitespace-nowrap",
            side === "top" && "bottom-full left-1/2 transform -translate-x-1/2 mb-2",
            side === "bottom" && "top-full left-1/2 transform -translate-x-1/2 mt-2",
            side === "left" && "right-full top-1/2 transform -translate-y-1/2 mr-2",
            side === "right" && "left-full top-1/2 transform -translate-y-1/2 ml-2"
          )}
        >
          {content}
        </div>
      )}
    </div>
  );
};
import { cn } from "@/lib/utils";

export interface TableColumn<T> {
  key: string;
  label: string;
  sortable?: boolean;
  render?: (item: T, index: number) => React.ReactNode;
  permission?: {
    resource: PermissionResource;
    action: PermissionAction;
  };
  width?: string;
  align?: 'left' | 'center' | 'right';
}

export interface TableAction<T> {
  label: string;
  icon?: React.ComponentType<{ className?: string }>;
  onClick: (item: T) => void;
  permission?: {
    resource: PermissionResource;
    action: PermissionAction;
  };
  requireOwnership?: boolean;
  ownershipField?: string;
  color?: 'blue' | 'green' | 'red' | 'gray';
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';
  disabled?: (item: T) => boolean;
  tooltip?: string; // Custom tooltip text
}

export interface BulkAction<T> {
  label: string;
  icon?: React.ComponentType<{ className?: string }>;
  onClick: (items: T[]) => void;
  permission?: {
    resource: PermissionResource;
    action: PermissionAction;
  };
  requireOwnership?: boolean;
  ownershipField?: string;
  color?: 'blue' | 'green' | 'red' | 'gray';
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';
  confirmMessage?: string;
  tooltip?: string; // Custom tooltip text
}

export interface PermissionAwareTableProps<T> {
  data: T[];
  columns: TableColumn<T>[];
  actions?: TableAction<T>[];
  bulkActions?: BulkAction<T>[];
  resource: PermissionResource;
  idField?: string;
  ownershipField?: string;
  onSort?: (key: string, direction: 'asc' | 'desc') => void;
  loading?: boolean;
  emptyMessage?: string;
  className?: string;
}

export function PermissionAwareTable<T extends Record<string, any>>({
  data,
  columns,
  actions = [],
  bulkActions = [],
  resource,
  idField = 'id',
  ownershipField = 'userId',
  onSort,
  loading = false,
  emptyMessage = 'No data available',
  className = ''
}: PermissionAwareTableProps<T>) {
  const [selectedItems, setSelectedItems] = useState<Set<string>>(new Set());
  const [sortKey, setSortKey] = useState<string>('');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');

  const { hasPermission, canAccessResource, canPerformBulkAction } = usePermissions();

  // Filter columns based on permissions
  const visibleColumns = useMemo(() => {
    return columns.filter(column => {
      if (!column.permission) return true;
      return hasPermission(column.permission.resource, column.permission.action);
    });
  }, [columns, hasPermission]);

  // Filter actions based on permissions
  const getVisibleActions = (item: T) => {
    return actions.filter(action => {
      if (!action.permission) return true;
      
      const hasBasicPermission = hasPermission(action.permission.resource, action.permission.action);
      if (!hasBasicPermission) return false;

      if (action.requireOwnership && action.ownershipField) {
        return canAccessResource(
          action.permission.resource, 
          action.permission.action, 
          item[action.ownershipField]
        );
      }

      return true;
    });
  };

  // Filter bulk actions based on permissions
  const visibleBulkActions = useMemo(() => {
    return bulkActions.filter(action => {
      if (!action.permission) return true;
      return hasPermission(action.permission.resource, action.permission.action);
    });
  }, [bulkActions, hasPermission]);

  const handleSort = (key: string) => {
    if (!onSort) return;

    const newDirection = sortKey === key && sortDirection === 'asc' ? 'desc' : 'asc';
    setSortKey(key);
    setSortDirection(newDirection);
    onSort(key, newDirection);
  };

  const handleSelectAll = () => {
    if (selectedItems.size === data.length) {
      setSelectedItems(new Set());
    } else {
      setSelectedItems(new Set(data.map(item => item[idField])));
    }
  };

  const handleSelectItem = (itemId: string) => {
    const newSelected = new Set(selectedItems);
    if (newSelected.has(itemId)) {
      newSelected.delete(itemId);
    } else {
      newSelected.add(itemId);
    }
    setSelectedItems(newSelected);
  };

  const handleBulkAction = (action: BulkAction<T>) => {
    const selectedData = data.filter(item => selectedItems.has(item[idField]));

    // Check bulk operation permissions
    if (action.permission) {
      const hasBasicPermission = hasPermission(action.permission.resource, action.permission.action);
      if (!hasBasicPermission) {
        alert('You do not have permission to perform this action.');
        return;
      }

      const canPerformBulk = canPerformBulkAction(
        action.permission.resource,
        action.permission.action,
        selectedData.length
      );

      if (!canPerformBulk) {
        alert('You do not have permission to perform bulk operations on this many items.');
        return;
      }

      // Check ownership if required
      if (action.requireOwnership && action.ownershipField) {
        const unauthorizedItems = selectedData.filter(item => {
          return !canAccessResource(
            action.permission!.resource,
            action.permission!.action,
            item[action.ownershipField!]
          );
        });

        if (unauthorizedItems.length > 0) {
          alert(`You can only perform this action on items you own. ${unauthorizedItems.length} items are not owned by you.`);
          return;
        }
      }
    }

    if (action.confirmMessage) {
      if (!confirm(action.confirmMessage.replace('{count}', selectedData.length.toString()))) {
        return;
      }
    }

    action.onClick(selectedData);
    setSelectedItems(new Set());
  };

  if (loading) {
    return (
      <div className="animate-pulse">
        <div className="h-10 bg-gray-200 rounded mb-4"></div>
        {[...Array(5)].map((_, i) => (
          <div key={i} className="h-16 bg-gray-100 rounded mb-2"></div>
        ))}
      </div>
    );
  }

  return (
    <div className={`rounded-md border ${className}`}>
      {/* Bulk Actions Bar */}
      {selectedItems.size > 0 && visibleBulkActions.length > 0 && (
        <div className="bg-muted/50 px-4 py-3 border-b">
          <div className="flex items-center justify-between">
            <span className="text-sm text-muted-foreground">
              {selectedItems.size} item{selectedItems.size !== 1 ? 's' : ''} selected
            </span>
            <div className="flex space-x-2">
              {visibleBulkActions.map((action, index) => (
                <SimpleTooltip
                  key={index}
                  content={
                    <div>
                      <div className="flex items-center space-x-2">
                        {action.icon && <action.icon className="h-3 w-3" />}
                        <span>{action.tooltip || `Apply to ${selectedItems.size} selected item${selectedItems.size !== 1 ? 's' : ''}`}</span>
                      </div>
                      {action.confirmMessage && (
                        <div className="text-xs opacity-75 mt-1">
                          ⚠️ Requires confirmation
                        </div>
                      )}
                      {action.variant === 'destructive' && (
                        <div className="text-xs opacity-75 mt-1">
                          ⚠️ This action cannot be undone
                        </div>
                      )}
                    </div>
                  }
                >
                  <Button
                    onClick={() => handleBulkAction(action)}
                    variant={action.variant || (action.color === 'red' ? 'destructive' : 'default')}
                    size="sm"
                    className="transition-all duration-200 hover:scale-105"
                  >
                    {action.icon && <action.icon className="h-4 w-4 mr-1" />}
                    {action.label}
                  </Button>
                </SimpleTooltip>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Table */}
      <Table>
        <TableHeader>
          <TableRow>
            {/* Bulk selection checkbox */}
            {visibleBulkActions.length > 0 && (
              <TableHead className="w-12">
                <Checkbox
                  checked={selectedItems.size === data.length && data.length > 0}
                  onCheckedChange={handleSelectAll}
                />
              </TableHead>
            )}

            {/* Column headers */}
            {visibleColumns.map((column) => (
              <TableHead
                key={column.key}
                className={`${
                  column.align === 'center' ? 'text-center' :
                  column.align === 'right' ? 'text-right' : 'text-left'
                } ${column.sortable ? 'cursor-pointer hover:bg-muted/50' : ''}`}
                style={{ width: column.width }}
                onClick={() => column.sortable && handleSort(column.key)}
              >
                <div className="flex items-center space-x-1">
                  <span>{column.label}</span>
                  {column.sortable && (
                    <div className="flex flex-col">
                      <ChevronUpIcon
                        className={`h-3 w-3 ${
                          sortKey === column.key && sortDirection === 'asc'
                            ? 'text-primary' : 'text-muted-foreground'
                        }`}
                      />
                      <ChevronDownIcon
                        className={`h-3 w-3 -mt-1 ${
                          sortKey === column.key && sortDirection === 'desc'
                            ? 'text-primary' : 'text-muted-foreground'
                        }`}
                      />
                    </div>
                  )}
                </div>
              </TableHead>
            ))}

            {/* Actions column */}
            {actions.length > 0 && (
              <TableHead className="text-right">
                Actions
              </TableHead>
            )}
          </TableRow>
        </TableHeader>

        <TableBody>
          {data.length === 0 ? (
            <TableRow>
              <TableCell
                colSpan={visibleColumns.length + (visibleBulkActions.length > 0 ? 1 : 0) + (actions.length > 0 ? 1 : 0)}
                className="h-24 text-center"
              >
                {emptyMessage}
              </TableCell>
            </TableRow>
          ) : (
            data.map((item, index) => {
              const itemId = item[idField];
              const visibleItemActions = getVisibleActions(item);

              return (
                <TableRow key={itemId}>
                  {/* Bulk selection checkbox */}
                  {visibleBulkActions.length > 0 && (
                    <TableCell>
                      <Checkbox
                        checked={selectedItems.has(itemId)}
                        onCheckedChange={() => handleSelectItem(itemId)}
                      />
                    </TableCell>
                  )}

                  {/* Data columns */}
                  {visibleColumns.map((column) => (
                    <TableCell
                      key={column.key}
                      className={`${
                        column.align === 'center' ? 'text-center' :
                        column.align === 'right' ? 'text-right' : 'text-left'
                      }`}
                    >
                      {column.render ? column.render(item, index) : item[column.key]}
                    </TableCell>
                  ))}

                  {/* Actions */}
                  {actions.length > 0 && (
                    <TableCell className="text-right">
                      <div className="flex justify-end space-x-1">
                        {visibleItemActions.map((action, actionIndex) => (
                          <SimpleTooltip
                            key={actionIndex}
                            content={
                              <div>
                                <div className="flex items-center space-x-2">
                                  {action.icon && <action.icon className="h-3 w-3" />}
                                  <span>{action.tooltip || action.label}</span>
                                </div>
                                {action.disabled?.(item) && (
                                  <div className="text-xs opacity-75 mt-1">
                                    Action not available
                                  </div>
                                )}
                                {action.variant === 'destructive' && !action.disabled?.(item) && (
                                  <div className="text-xs opacity-75 mt-1">
                                    ⚠️ This action cannot be undone
                                  </div>
                                )}
                              </div>
                            }
                          >
                            <Button
                              onClick={() => action.onClick(item)}
                              disabled={action.disabled?.(item)}
                              variant={action.variant || "ghost"}
                              size="sm"
                              className={cn(
                                "h-8 w-8 p-0 transition-all duration-200 hover:scale-110",
                                !action.variant && action.color === 'red' && "hover:bg-red-50 hover:text-red-600 dark:hover:bg-red-950/20",
                                !action.variant && action.color === 'blue' && "hover:bg-blue-50 hover:text-blue-600 dark:hover:bg-blue-950/20",
                                !action.variant && action.color === 'green' && "hover:bg-green-50 hover:text-green-600 dark:hover:bg-green-950/20",
                                !action.variant && action.color === 'gray' && "hover:bg-gray-50 hover:text-gray-600 dark:hover:bg-gray-950/20",
                                action.disabled?.(item) && "opacity-50 cursor-not-allowed hover:scale-100"
                              )}
                            >
                              {action.icon ? (
                                <action.icon className="h-4 w-4" />
                              ) : (
                                <span className="sr-only">{action.label}</span>
                              )}
                            </Button>
                          </SimpleTooltip>
                        ))}
                      </div>
                    </TableCell>
                  )}
                </TableRow>
              );
            })
          )}
        </TableBody>
      </Table>
    </div>
  );
}

// Predefined common actions
export const commonTableActions = {
  view: (onClick: (item: any) => void): TableAction<any> => ({
    label: 'View',
    icon: EyeIcon,
    onClick,
    color: 'blue'
  }),

  edit: (onClick: (item: any) => void, resource: PermissionResource): TableAction<any> => ({
    label: 'Edit',
    icon: PencilIcon,
    onClick,
    permission: { resource, action: PermissionAction.UPDATE },
    requireOwnership: true,
    color: 'blue'
  }),

  delete: (onClick: (item: any) => void, resource: PermissionResource): TableAction<any> => ({
    label: 'Delete',
    icon: TrashIcon,
    onClick,
    permission: { resource, action: PermissionAction.DELETE },
    requireOwnership: true,
    color: 'red'
  })
};

// Predefined common bulk actions
export const commonBulkActions = {
  delete: (onClick: (items: any[]) => void, resource: PermissionResource): BulkAction<any> => ({
    label: 'Delete Selected',
    icon: TrashIcon,
    onClick,
    permission: { resource, action: PermissionAction.DELETE },
    requireOwnership: true,
    color: 'red',
    confirmMessage: 'Are you sure you want to delete {count} items?'
  }),

  export: (onClick: (items: any[]) => void): BulkAction<any> => ({
    label: 'Export Selected',
    onClick,
    color: 'green'
  })
};
