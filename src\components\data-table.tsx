"use client"

import { useState } from 'react'
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { formatCurrency, formatRelativeTime } from '@/lib/utils'
import { EyeIcon, PencilIcon, TrashIcon, PlusIcon } from '@heroicons/react/24/outline'

interface DataItem {
  id: string
  name: string
  email: string
  company: string
  status: string
  lastActivity: string
  value: number
  type: string
}

interface DataTableProps {
  data: DataItem[]
}

const statusColors: Record<string, string> = {
  'Active': 'bg-blue-100 text-blue-800',
  'Qualified': 'bg-green-100 text-green-800',
  'Proposal': 'bg-yellow-100 text-yellow-800',
  'Negotiation': 'bg-orange-100 text-orange-800',
  'Closed Won': 'bg-emerald-100 text-emerald-800',
  'Closed Lost': 'bg-red-100 text-red-800',
}

const typeColors: Record<string, string> = {
  'Lead': 'bg-purple-100 text-purple-800',
  'Opportunity': 'bg-indigo-100 text-indigo-800',
  'Deal': 'bg-pink-100 text-pink-800',
  'Customer': 'bg-green-100 text-green-800',
}

export function DataTable({ data }: DataTableProps) {
  const [selectedTab, setSelectedTab] = useState("Outline")
  
  const tabs = [
    { name: "Outline", count: data.length },
    { name: "Past Performance", count: 3 },
    { name: "Key Personnel", count: 2 },
    { name: "Focus Documents", count: 0 },
  ]

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Recent Activities</CardTitle>
            <CardDescription>
              Latest customer interactions and pipeline updates
            </CardDescription>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm">
              Customize Columns
            </Button>
            <Button size="sm">
              <PlusIcon className="h-4 w-4 mr-1" />
              Add Section
            </Button>
          </div>
        </div>
        
        {/* Tabs */}
        <div className="flex space-x-1 border-b">
          {tabs.map((tab) => (
            <button
              key={tab.name}
              onClick={() => setSelectedTab(tab.name)}
              className={`px-3 py-2 text-sm font-medium border-b-2 transition-colors ${
                selectedTab === tab.name
                  ? 'border-primary text-primary'
                  : 'border-transparent text-muted-foreground hover:text-foreground'
              }`}
            >
              {tab.name}
              {tab.count > 0 && (
                <span className="ml-2 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white bg-primary rounded-full">
                  {tab.count}
                </span>
              )}
            </button>
          ))}
        </div>
      </CardHeader>
      
      <CardContent>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b text-left">
                <th className="pb-3 text-sm font-medium text-muted-foreground">Name</th>
                <th className="pb-3 text-sm font-medium text-muted-foreground">Company</th>
                <th className="pb-3 text-sm font-medium text-muted-foreground">Status</th>
                <th className="pb-3 text-sm font-medium text-muted-foreground">Type</th>
                <th className="pb-3 text-sm font-medium text-muted-foreground">Value</th>
                <th className="pb-3 text-sm font-medium text-muted-foreground">Last Activity</th>
                <th className="pb-3 text-sm font-medium text-muted-foreground">Actions</th>
              </tr>
            </thead>
            <tbody>
              {data.map((item) => (
                <tr key={item.id} className="border-b last:border-0">
                  <td className="py-4">
                    <div>
                      <div className="font-medium">{item.name}</div>
                      <div className="text-sm text-muted-foreground">{item.email}</div>
                    </div>
                  </td>
                  <td className="py-4 text-sm">{item.company}</td>
                  <td className="py-4">
                    <Badge 
                      variant="secondary" 
                      className={statusColors[item.status] || 'bg-gray-100 text-gray-800'}
                    >
                      {item.status}
                    </Badge>
                  </td>
                  <td className="py-4">
                    <Badge 
                      variant="outline"
                      className={typeColors[item.type] || 'bg-gray-100 text-gray-800'}
                    >
                      {item.type}
                    </Badge>
                  </td>
                  <td className="py-4 font-medium">{formatCurrency(item.value)}</td>
                  <td className="py-4 text-sm text-muted-foreground">
                    {formatRelativeTime(new Date(item.lastActivity))}
                  </td>
                  <td className="py-4">
                    <div className="flex items-center gap-2">
                      <Button variant="ghost" size="sm">
                        <EyeIcon className="h-4 w-4" />
                      </Button>
                      <Button variant="ghost" size="sm">
                        <PencilIcon className="h-4 w-4" />
                      </Button>
                      <Button variant="ghost" size="sm">
                        <TrashIcon className="h-4 w-4" />
                      </Button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </CardContent>
    </Card>
  )
}
