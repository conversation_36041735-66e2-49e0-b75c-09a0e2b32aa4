import { NextResponse } from 'next/server';
import { withPermission } from '@/lib/permission-middleware';
import { 
  leadSourceManagementService, 
  updateLeadSourceSchema,
  UpdateLeadSourceData 
} from '@/services/lead-source-management';
import { PermissionAction, PermissionResource } from '@/types';
import { AuthenticatedRequest } from '@/lib/permission-middleware';

/**
 * GET /api/lead-sources/[leadSourceId]
 * Get a specific lead source
 */
export const GET = withPermission({
  resource: PermissionResource.LEADS,
  action: PermissionAction.READ,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest) => {
  try {
    const url = new URL(req.url);
    const pathSegments = url.pathname.split('/');
    const leadSourceId = pathSegments[pathSegments.indexOf('lead-sources') + 1];

    if (!leadSourceId) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Lead source ID is required',
          details: 'Lead source ID must be provided in the URL path'
        },
        { status: 400 }
      );
    }

    const leadSource = await leadSourceManagementService.getLeadSourceById(
      leadSourceId,
      req.tenantId
    );

    if (!leadSource) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Lead source not found',
          details: 'The requested lead source does not exist or you do not have permission to access it'
        },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: { leadSource },
      message: 'Lead source retrieved successfully'
    });
  } catch (error) {
    console.error('Error fetching lead source:', error);
    
    if (error instanceof Error) {
      return NextResponse.json(
        { 
          success: false, 
          error: error.message,
          details: 'Failed to fetch lead source'
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error',
        details: 'An unexpected error occurred while fetching the lead source'
      },
      { status: 500 }
    );
  }
});

/**
 * PUT /api/lead-sources/[leadSourceId]
 * Update a specific lead source
 */
export const PUT = withPermission({
  resource: PermissionResource.LEADS,
  action: PermissionAction.UPDATE,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest) => {
  try {
    const url = new URL(req.url);
    const pathSegments = url.pathname.split('/');
    const leadSourceId = pathSegments[pathSegments.indexOf('lead-sources') + 1];

    if (!leadSourceId) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Lead source ID is required',
          details: 'Lead source ID must be provided in the URL path'
        },
        { status: 400 }
      );
    }

    const body = await req.json();
    const validatedData = updateLeadSourceSchema.parse(body);

    const leadSource = await leadSourceManagementService.updateLeadSource(
      leadSourceId,
      req.tenantId,
      validatedData
    );

    return NextResponse.json({
      success: true,
      data: { leadSource },
      message: 'Lead source updated successfully'
    });
  } catch (error) {
    console.error('Error updating lead source:', error);
    
    if (error instanceof Error) {
      return NextResponse.json(
        { 
          success: false, 
          error: error.message,
          details: 'Failed to update lead source'
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error',
        details: 'An unexpected error occurred while updating the lead source'
      },
      { status: 500 }
    );
  }
});

/**
 * DELETE /api/lead-sources/[leadSourceId]
 * Delete a specific lead source
 */
export const DELETE = withPermission({
  resource: PermissionResource.LEADS,
  action: PermissionAction.DELETE,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest) => {
  try {
    const url = new URL(req.url);
    const pathSegments = url.pathname.split('/');
    const leadSourceId = pathSegments[pathSegments.indexOf('lead-sources') + 1];

    if (!leadSourceId) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Lead source ID is required',
          details: 'Lead source ID must be provided in the URL path'
        },
        { status: 400 }
      );
    }

    await leadSourceManagementService.deleteLeadSource(leadSourceId, req.tenantId);

    return NextResponse.json({
      success: true,
      message: 'Lead source deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting lead source:', error);
    
    if (error instanceof Error) {
      return NextResponse.json(
        { 
          success: false, 
          error: error.message,
          details: 'Failed to delete lead source'
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error',
        details: 'An unexpected error occurred while deleting the lead source'
      },
      { status: 500 }
    );
  }
});
