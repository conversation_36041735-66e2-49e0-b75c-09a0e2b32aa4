import { NextResponse } from 'next/server';
import { z } from 'zod';
import { withPermission, AuthenticatedRequest } from '@/lib/permission-middleware';
import { TeamManagementService } from '@/services/team-management';
import { PermissionAction, PermissionResource } from '@/types';

const updateTeamSchema = z.object({
  name: z.string().min(1, 'Team name is required').max(100, 'Team name too long').optional(),
  description: z.string().optional(),
  color: z.string().optional(),
  managerId: z.string().optional(),
  parentTeamId: z.string().optional(),
  isActive: z.boolean().optional(),
  settings: z.record(z.any()).optional()
});

interface RouteParams {
  params: Promise<{
    id: string;
  }>;
}

/**
 * GET /api/teams/[id]
 * Get a specific team by ID
 */
export const GET = withPermission({
  resource: PermissionResource.USERS,
  action: PermissionAction.READ,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest, context: { params: Promise<{ id: string }> }) => {
  try {
    const { id: teamId } = await context.params;

    const teamService = new TeamManagementService();
    const team = await teamService.getTeamById(teamId, req.tenantId);

    if (!team) {
      return NextResponse.json(
        { error: 'Team not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: { team }
    });
  } catch (error) {
    console.error('Get team error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch team' },
      { status: 500 }
    );
  }
});

/**
 * PUT /api/teams/[id]
 * Update a specific team
 */
export const PUT = withPermission({
  resource: PermissionResource.USERS,
  action: PermissionAction.MANAGE,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest, context: { params: Promise<{ id: string }> }) => {
  try {
    const { id: teamId } = await context.params;
    const body = await req.json();
    const validatedData = updateTeamSchema.parse(body);

    const teamService = new TeamManagementService();
    const team = await teamService.updateTeam(
      teamId,
      req.tenantId,
      validatedData,
      req.userId
    );

    return NextResponse.json({
      success: true,
      data: { team },
      message: 'Team updated successfully'
    });
  } catch (error) {
    console.error('Update team error:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid input data', details: error.errors },
        { status: 400 }
      );
    }

    if (error instanceof Error) {
      if (error.message.includes('not found')) {
        return NextResponse.json(
          { error: 'Team not found' },
          { status: 404 }
        );
      }
      
      if (error.message.includes('already exists')) {
        return NextResponse.json(
          { error: 'A team with this name already exists' },
          { status: 409 }
        );
      }
    }

    return NextResponse.json(
      { error: 'Failed to update team' },
      { status: 500 }
    );
  }
});

/**
 * DELETE /api/teams/[id]
 * Delete a specific team
 */
export const DELETE = withPermission({
  resource: PermissionResource.USERS,
  action: PermissionAction.MANAGE,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest, context: { params: Promise<{ id: string }> }) => {
  try {
    const { id: teamId } = await context.params;

    const teamService = new TeamManagementService();
    await teamService.deleteTeam(teamId, req.tenantId, req.userId);

    return NextResponse.json({
      success: true,
      message: 'Team deleted successfully'
    });
  } catch (error) {
    console.error('Delete team error:', error);
    
    if (error instanceof Error) {
      if (error.message.includes('not found')) {
        return NextResponse.json(
          { error: 'Team not found' },
          { status: 404 }
        );
      }
      
      if (error.message.includes('has members')) {
        return NextResponse.json(
          { error: 'Cannot delete team with members. Remove all members first.' },
          { status: 409 }
        );
      }
    }

    return NextResponse.json(
      { error: 'Failed to delete team' },
      { status: 500 }
    );
  }
});
