import Redis from 'ioredis';

// Redis client configuration with connection pooling
const redis = new Redis(process.env.REDIS_URL || 'redis://localhost:6379', {
  // Connection pool settings
  maxRetriesPerRequest: 3,
  // Connection settings
  connectTimeout: 10000,
  commandTimeout: 5000, // Keep original 5 second timeout for normal operations
  // Performance settings
  lazyConnect: true,
  keepAlive: 30000,
  // Retry settings
  retryDelayOnFailover: 100,
  enableReadyCheck: false,
});

// Redis connection event handlers
redis.on('connect', () => {
  console.log('✅ Redis connected successfully');
});

redis.on('error', (error) => {
  console.error('❌ Redis connection error:', error);
});

redis.on('ready', () => {
  console.log('🚀 Redis is ready to accept commands');
});

redis.on('reconnecting', () => {
  console.log('🔄 Redis reconnecting...');
});

// Cache key prefixes for different data types
export const CACHE_PREFIXES = {
  SESSION: 'session:',
  USER_PERMISSIONS: 'permissions:user:',
  TENANT_DATA: 'tenant:',
  API_RESPONSE: 'api:',
  AI_RESPONSE: 'ai:',
  USAGE_METRICS: 'usage:',
  SUBSCRIPTION: 'subscription:',
} as const;

// Cache TTL constants (in seconds)
export const CACHE_TTL = {
  SESSION: 24 * 60 * 60, // 24 hours
  USER_PERMISSIONS: 60 * 60, // 1 hour
  TENANT_DATA: 30 * 60, // 30 minutes
  API_RESPONSE: 5 * 60, // 5 minutes
  AI_RESPONSE: 60 * 60, // 1 hour
  USAGE_METRICS: 10 * 60, // 10 minutes
  SUBSCRIPTION: 60 * 60, // 1 hour
} as const;

export interface CacheOptions {
  ttl?: number;
  prefix?: string;
  compress?: boolean;
}

export class CacheService {
  private redis: Redis;

  constructor() {
    this.redis = redis;
  }

  // Generic cache operations
  async get<T>(key: string, options?: CacheOptions): Promise<T | null> {
    try {
      const fullKey = this.buildKey(key, options?.prefix);
      const value = await this.redis.get(fullKey);
      
      if (!value) return null;
      
      // Handle compressed data
      const data = options?.compress ? this.decompress(value) : value;
      return JSON.parse(data);
    } catch (error) {
      console.error('Cache get error:', error);
      return null;
    }
  }

  async set<T>(key: string, value: T, options?: CacheOptions): Promise<boolean> {
    try {
      const fullKey = this.buildKey(key, options?.prefix);
      const ttl = options?.ttl || CACHE_TTL.API_RESPONSE;
      
      // Serialize data
      let data = JSON.stringify(value);
      
      // Handle compression
      if (options?.compress) {
        data = this.compress(data);
      }
      
      await this.redis.setex(fullKey, ttl, data);
      return true;
    } catch (error) {
      console.error('Cache set error:', error);
      return false;
    }
  }

  async del(key: string, prefix?: string): Promise<boolean> {
    try {
      const fullKey = this.buildKey(key, prefix);
      await this.redis.del(fullKey);
      return true;
    } catch (error) {
      console.error('Cache delete error:', error);
      return false;
    }
  }

  async exists(key: string, prefix?: string): Promise<boolean> {
    try {
      const fullKey = this.buildKey(key, prefix);
      const result = await this.redis.exists(fullKey);
      return result === 1;
    } catch (error) {
      console.error('Cache exists error:', error);
      return false;
    }
  }

  // Batch operations
  async mget<T>(keys: string[], prefix?: string): Promise<(T | null)[]> {
    try {
      const fullKeys = keys.map(key => this.buildKey(key, prefix));
      const values = await this.redis.mget(...fullKeys);
      
      return values.map(value => {
        if (!value) return null;
        try {
          return JSON.parse(value);
        } catch {
          return null;
        }
      });
    } catch (error) {
      console.error('Cache mget error:', error);
      return keys.map(() => null);
    }
  }

  async mset(keyValuePairs: Array<{ key: string; value: any; ttl?: number }>, prefix?: string): Promise<boolean> {
    try {
      const pipeline = this.redis.pipeline();
      
      for (const { key, value, ttl } of keyValuePairs) {
        const fullKey = this.buildKey(key, prefix);
        const data = JSON.stringify(value);
        const cacheTtl = ttl || CACHE_TTL.API_RESPONSE;
        
        pipeline.setex(fullKey, cacheTtl, data);
      }
      
      await pipeline.exec();
      return true;
    } catch (error) {
      console.error('Cache mset error:', error);
      return false;
    }
  }

  // Pattern-based operations
  async deletePattern(pattern: string, prefix?: string): Promise<number> {
    try {
      const fullPattern = this.buildKey(pattern, prefix);
      const keys = await this.redis.keys(fullPattern);
      
      if (keys.length === 0) return 0;
      
      await this.redis.del(...keys);
      return keys.length;
    } catch (error) {
      console.error('Cache delete pattern error:', error);
      return 0;
    }
  }

  // Cache statistics
  async getStats(): Promise<{
    memory: string;
    keys: number;
    hits: number;
    misses: number;
    hitRate: number;
  }> {
    try {
      const info = await this.redis.info('memory');
      const stats = await this.redis.info('stats');
      
      const memory = this.extractInfoValue(info, 'used_memory_human');
      const keys = await this.redis.dbsize();
      const hits = parseInt(this.extractInfoValue(stats, 'keyspace_hits') || '0');
      const misses = parseInt(this.extractInfoValue(stats, 'keyspace_misses') || '0');
      const hitRate = hits + misses > 0 ? (hits / (hits + misses)) * 100 : 0;
      
      return { memory, keys, hits, misses, hitRate };
    } catch (error) {
      console.error('Cache stats error:', error);
      return { memory: '0B', keys: 0, hits: 0, misses: 0, hitRate: 0 };
    }
  }

  // Health check
  async healthCheck(): Promise<boolean> {
    try {
      const result = await this.redis.ping();
      return result === 'PONG';
    } catch (error) {
      console.error('Cache health check error:', error);
      return false;
    }
  }

  // Utility methods
  private buildKey(key: string, prefix?: string): string {
    const env = process.env.NODE_ENV || 'development';
    const basePrefix = `crm:${env}:`;
    return prefix ? `${basePrefix}${prefix}${key}` : `${basePrefix}${key}`;
  }

  private compress(data: string): string {
    // Simple compression - in production, consider using a proper compression library
    return Buffer.from(data).toString('base64');
  }

  private decompress(data: string): string {
    return Buffer.from(data, 'base64').toString('utf-8');
  }

  private extractInfoValue(info: string, key: string): string {
    const lines = info.split('\r\n');
    const line = lines.find(l => l.startsWith(`${key}:`));
    return line ? line.split(':')[1] : '';
  }

  // Graceful shutdown
  async disconnect(): Promise<void> {
    try {
      await this.redis.quit();
      console.log('✅ Redis connection closed gracefully');
    } catch (error) {
      console.error('❌ Error closing Redis connection:', error);
    }
  }
}

// Export singleton instance
export const cacheService = new CacheService();

// Export Redis client for direct access if needed
export { redis };
