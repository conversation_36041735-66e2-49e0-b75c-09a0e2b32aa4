import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { handoverManagementService } from '@/services/handover-management';
import { z } from 'zod';

// Request validation schemas
const getHandoversQuerySchema = z.object({
  status: z.string().optional(),
  priority: z.string().optional(),
  salesRepId: z.string().optional(),
  deliveryManagerId: z.string().optional(),
  opportunityId: z.string().optional(),
  search: z.string().optional(),
  page: z.coerce.number().int().positive().default(1),
  limit: z.coerce.number().int().positive().max(100).default(20),
  sortBy: z.enum(['createdAt', 'updatedAt', 'scheduledDate', 'priority']).default('createdAt'),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
});

const createHandoverSchema = z.object({
  opportunityId: z.string().min(1, 'Opportunity ID is required'),
  templateId: z.string().optional(),
  title: z.string().min(1, 'Title is required').max(255),
  description: z.string().optional(),
  priority: z.enum(['low', 'medium', 'high', 'urgent']).default('medium'),
  salesRepId: z.string().optional(),
  deliveryManagerId: z.string().optional(),
  assignedTeamIds: z.array(z.string()).default([]),
  scheduledDate: z.string().datetime().optional().transform(val => val ? new Date(val) : undefined),
  estimatedDuration: z.number().int().positive().optional(),
});

/**
 * GET /api/handovers - Get handovers with filtering and pagination
 */
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get tenantId from user's tenants array (users have one tenant in this system)
    let tenantId: string | null = null;
    if (session.user.tenants && session.user.tenants.length > 0) {
      tenantId = session.user.tenants[0].id;
    }

    if (!tenantId) {
      return NextResponse.json({ error: 'Tenant not found' }, { status: 400 });
    }

    // Parse and validate query parameters
    const url = new URL(request.url);
    const queryParams = Object.fromEntries(url.searchParams.entries());
    
    const validatedQuery = getHandoversQuerySchema.parse(queryParams);

    const result = await handoverManagementService.getHandovers(tenantId, validatedQuery);

    return NextResponse.json(result);
  } catch (error) {
    console.error('Error fetching handovers:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid query parameters', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to fetch handovers' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/handovers - Create a new handover
 */
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get tenantId from user's tenants array (users have one tenant in this system)
    let tenantId: string | null = null;
    if (session.user.tenants && session.user.tenants.length > 0) {
      tenantId = session.user.tenants[0].id;
    }

    if (!tenantId) {
      return NextResponse.json({ error: 'Tenant not found' }, { status: 400 });
    }

    const body = await request.json();
    const validatedData = createHandoverSchema.parse(body);

    const handover = await handoverManagementService.createHandover(
      tenantId,
      validatedData,
      session.user.id
    );

    return NextResponse.json(handover, { status: 201 });
  } catch (error) {
    console.error('Error creating handover:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request data', details: error.errors },
        { status: 400 }
      );
    }

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to create handover' },
      { status: 500 }
    );
  }
}
