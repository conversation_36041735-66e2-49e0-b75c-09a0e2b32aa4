import nodemailer from 'nodemailer';
import { z } from 'zod';
import { emailDeliveryNotificationService } from './email-delivery-notifications';

// Email validation schemas
export const emailAddressSchema = z.string().email('Invalid email address');

export const emailAttachmentSchema = z.object({
  filename: z.string(),
  content: z.union([z.string(), z.instanceof(Buffer)]),
  contentType: z.string().optional(),
  encoding: z.string().optional(),
  cid: z.string().optional() // For inline attachments
});

export const emailDataSchema = z.object({
  to: z.union([emailAddressSchema, z.array(emailAddressSchema)]),
  cc: z.union([emailAddressSchema, z.array(emailAddressSchema)]).optional(),
  bcc: z.union([emailAddressSchema, z.array(emailAddressSchema)]).optional(),
  subject: z.string().min(1, 'Subject is required'),
  text: z.string().optional(),
  html: z.string().optional(),
  attachments: z.array(emailAttachmentSchema).optional(),
  replyTo: z.string().email().optional(),
  priority: z.enum(['high', 'normal', 'low']).default('normal'),
  headers: z.record(z.string()).optional()
});

export type EmailData = z.infer<typeof emailDataSchema>;
export type EmailAttachment = z.infer<typeof emailAttachmentSchema>;

export interface EmailResult {
  success: boolean;
  messageId?: string;
  error?: string;
  provider: 'smtp' | 'mock';
  timestamp: Date;
  recipients: string[];
}

export interface SMTPConfig {
  host: string;
  port: number;
  secure: boolean;
  auth: {
    user: string;
    pass: string;
  };
  from: {
    email: string;
    name: string;
  };
  replyTo?: string;
}

export class SMTPService {
  private transporter: nodemailer.Transporter | null = null;
  private config: SMTPConfig;
  private isEnabled: boolean;

  constructor() {
    this.isEnabled = process.env.ENABLE_EMAIL_SENDING === 'true';

    console.log('🔧 SMTP Service Initialization:');
    console.log('- Email sending enabled:', this.isEnabled);
    console.log('- SMTP Host:', process.env.SMTP_HOST);
    console.log('- SMTP Port:', process.env.SMTP_PORT);
    console.log('- SMTP User:', process.env.SMTP_USER ? '***configured***' : 'NOT SET');
    console.log('- SMTP Pass:', process.env.SMTP_PASS ? '***configured***' : 'NOT SET');

    this.config = {
      host: process.env.SMTP_HOST || 'localhost',
      port: parseInt(process.env.SMTP_PORT || '587'),
      secure: process.env.SMTP_SECURE === 'true',
      auth: {
        user: process.env.SMTP_USER || '',
        pass: process.env.SMTP_PASS || ''
      },
      from: {
        email: process.env.SMTP_FROM_EMAIL || 'noreply@localhost',
        name: process.env.SMTP_FROM_NAME || 'CRM Platform'
      },
      replyTo: process.env.SMTP_REPLY_TO
    };

    const configValid = this.isConfigValid();
    console.log('- Config valid:', configValid);

    if (this.isEnabled && configValid) {
      console.log('✅ Initializing SMTP transporter...');
      this.initializeTransporter();
    } else {
      console.log('❌ SMTP service not initialized:', {
        enabled: this.isEnabled,
        configValid
      });
    }
  }

  private isConfigValid(): boolean {
    return !!(
      this.config.host &&
      this.config.auth.user &&
      this.config.auth.pass &&
      this.config.from.email
    );
  }

  private initializeTransporter(): void {
    try {
      this.transporter = nodemailer.createTransport({
        host: this.config.host,
        port: this.config.port,
        secure: this.config.secure,
        auth: this.config.auth,
        pool: true, // Use connection pooling
        maxConnections: 5,
        maxMessages: 100,
        rateLimit: parseInt(process.env.EMAIL_RATE_LIMIT_PER_HOUR || '100'),
        rateDelta: 3600000, // 1 hour in milliseconds
      });

      // Verify connection
      if (this.transporter) {
        this.transporter.verify((error) => {
          if (error) {
            console.error('❌ SMTP connection verification failed:', error);
            this.transporter = null;
          } else {
            console.log('✅ SMTP server connection verified successfully');
          }
        });
      }
    } catch (error) {
      console.error('Failed to initialize SMTP transporter:', error);
      this.transporter = null;
    }
  }

  async sendEmail(emailData: EmailData): Promise<EmailResult> {
    const startTime = new Date();
    
    try {
      // Validate email data
      const validatedData = emailDataSchema.parse(emailData);
      
      // Extract recipients for result
      const recipients = this.extractRecipients(validatedData);
      
      // If email sending is disabled or no transporter, return mock result
      if (!this.isEnabled || !this.transporter) {
        console.log('Email sending disabled or SMTP not configured. Mock sending:', {
          to: recipients,
          subject: validatedData.subject
        });
        
        return {
          success: true,
          messageId: `mock-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
          provider: 'mock',
          timestamp: startTime,
          recipients
        };
      }

      // Prepare email options
      const mailOptions = {
        from: `${this.config.from.name} <${this.config.from.email}>`,
        to: validatedData.to,
        cc: validatedData.cc,
        bcc: validatedData.bcc,
        subject: validatedData.subject,
        text: validatedData.text,
        html: validatedData.html,
        attachments: validatedData.attachments,
        replyTo: validatedData.replyTo || this.config.replyTo,
        priority: validatedData.priority,
        headers: validatedData.headers
      };

      // Send email
      const result = await this.transporter.sendMail(mailOptions);

      const emailResult = {
        success: true,
        messageId: result.messageId,
        provider: 'smtp',
        timestamp: startTime,
        recipients
      };

      // Trigger email sent notification for tracking
      if (result.messageId) {
        await this.triggerEmailSentNotification(result.messageId, recipients, startTime);
      }

      return emailResult;

    } catch (error) {
      console.error('Email sending failed:', error);
      
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
        provider: this.transporter ? 'smtp' : 'mock',
        timestamp: startTime,
        recipients: this.extractRecipients(emailData)
      };
    }
  }

  private extractRecipients(emailData: EmailData): string[] {
    const recipients: string[] = [];
    
    if (Array.isArray(emailData.to)) {
      recipients.push(...emailData.to);
    } else {
      recipients.push(emailData.to);
    }
    
    if (emailData.cc) {
      if (Array.isArray(emailData.cc)) {
        recipients.push(...emailData.cc);
      } else {
        recipients.push(emailData.cc);
      }
    }
    
    return recipients;
  }

  async testConnection(): Promise<{ success: boolean; error?: string }> {
    if (!this.transporter) {
      return {
        success: false,
        error: 'SMTP transporter not initialized'
      };
    }

    try {
      await this.transporter.verify();
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Connection test failed'
      };
    }
  }

  getConfig(): Omit<SMTPConfig, 'auth'> {
    return {
      host: this.config.host,
      port: this.config.port,
      secure: this.config.secure,
      from: this.config.from,
      replyTo: this.config.replyTo
    };
  }

  isReady(): boolean {
    return this.isEnabled && !!this.transporter;
  }

  /**
   * Trigger email sent notification for delivery tracking
   */
  private async triggerEmailSentNotification(
    messageId: string,
    recipients: string[],
    timestamp: Date
  ): Promise<void> {
    try {
      // For each recipient, create a sent event
      // Note: We don't have tenantId here, so this would need to be passed from the calling context
      // For now, we'll skip this automatic notification and rely on the webhook for delivery status
      console.log(`📧 Email sent with message ID: ${messageId} to ${recipients.length} recipients`);

      // TODO: If we need automatic sent notifications, we would need to:
      // 1. Pass tenantId to sendEmail method
      // 2. Create sent events for each recipient
      // 3. Store them for later correlation with delivery webhooks

    } catch (error) {
      console.error('Failed to trigger email sent notification:', error);
      // Don't throw error to avoid breaking email sending
    }
  }
}

// Export singleton instance
export const smtpService = new SMTPService();
