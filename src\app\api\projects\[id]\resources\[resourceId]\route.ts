import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { prisma } from '@/lib/prisma';
import { withPermission, AuthenticatedRequest } from '@/lib/permission-middleware';
import { PermissionResource, PermissionAction } from '@/types';

const updateResourceSchema = z.object({
  role: z.string().min(1, 'Role is required').optional(),
  allocation: z.number().min(0).max(100).optional(),
  hourlyRate: z.number().positive().optional(),
  startDate: z.string().datetime().optional(),
  endDate: z.string().datetime().optional(),
  hoursAllocated: z.number().positive().optional(),
  isActive: z.boolean().optional(),
});

interface RouteParams {
  params: Promise<{
    id: string;
    resourceId: string;
  }>;
}

/**
 * GET /api/projects/[id]/resources/[resourceId]
 * Get a specific project resource
 */
export const GET = withPermission({
  resource: PermissionResource.PROJECTS,
  action: PermissionAction.READ,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest, context: RouteParams) => {
  try {
    const { id: projectId, resourceId } = await context.params;

    const resource = await prisma.projectResource.findFirst({
      where: {
        id: resourceId,
        projectId,
        tenantId: req.tenantId,
      },
      include: {
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
        project: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    if (!resource) {
      return NextResponse.json(
        { error: 'Resource not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: { resource },
      message: 'Resource retrieved successfully'
    });
  } catch (error) {
    console.error('Get resource error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch resource' },
      { status: 500 }
    );
  }
});

/**
 * PUT /api/projects/[id]/resources/[resourceId]
 * Update a specific project resource
 */
export const PUT = withPermission({
  resource: PermissionResource.PROJECTS,
  action: PermissionAction.UPDATE,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest, context: RouteParams) => {
  try {
    const { id: projectId, resourceId } = await context.params;
    const body = await req.json();
    const validatedData = updateResourceSchema.parse(body);

    // Verify resource exists and belongs to tenant
    const existingResource = await prisma.projectResource.findFirst({
      where: {
        id: resourceId,
        projectId,
        tenantId: req.tenantId,
      },
    });

    if (!existingResource) {
      return NextResponse.json(
        { error: 'Resource not found' },
        { status: 404 }
      );
    }

    // Check for role conflicts if role is being changed
    if (validatedData.role && validatedData.role !== existingResource.role) {
      const conflictingResource = await prisma.projectResource.findFirst({
        where: {
          projectId,
          userId: existingResource.userId,
          role: validatedData.role,
          tenantId: req.tenantId,
          isActive: true,
          id: { not: resourceId },
        },
      });

      if (conflictingResource) {
        return NextResponse.json(
          { error: 'User is already allocated to this project with the specified role' },
          { status: 400 }
        );
      }
    }

    // Prepare update data
    const updateData: any = {
      ...validatedData,
      updatedAt: new Date(),
    };

    if (validatedData.startDate) {
      updateData.startDate = new Date(validatedData.startDate);
    }

    if (validatedData.endDate) {
      updateData.endDate = new Date(validatedData.endDate);
    }

    // Update the resource
    const resource = await prisma.projectResource.update({
      where: {
        id: resourceId,
      },
      data: updateData,
      include: {
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
        project: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    return NextResponse.json({
      success: true,
      data: { resource },
      message: 'Resource updated successfully'
    });
  } catch (error) {
    console.error('Update resource error:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid resource data', details: error.errors },
        { status: 400 }
      );
    }

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to update resource' },
      { status: 500 }
    );
  }
});

/**
 * DELETE /api/projects/[id]/resources/[resourceId]
 * Remove a resource from a project
 */
export const DELETE = withPermission({
  resource: PermissionResource.PROJECTS,
  action: PermissionAction.UPDATE,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest, context: RouteParams) => {
  try {
    const { id: projectId, resourceId } = await context.params;

    // Verify resource exists and belongs to tenant
    const resource = await prisma.projectResource.findFirst({
      where: {
        id: resourceId,
        projectId,
        tenantId: req.tenantId,
      },
    });

    if (!resource) {
      return NextResponse.json(
        { error: 'Resource not found' },
        { status: 404 }
      );
    }

    // Check if resource has logged hours
    if (resource.hoursLogged > 0) {
      // Instead of deleting, mark as inactive
      await prisma.projectResource.update({
        where: {
          id: resourceId,
        },
        data: {
          isActive: false,
          endDate: new Date(),
          updatedAt: new Date(),
        },
      });

      return NextResponse.json({
        success: true,
        message: 'Resource marked as inactive (has logged hours)'
      });
    } else {
      // Safe to delete if no hours logged
      await prisma.projectResource.delete({
        where: {
          id: resourceId,
        },
      });

      return NextResponse.json({
        success: true,
        message: 'Resource removed from project successfully'
      });
    }
  } catch (error) {
    console.error('Delete resource error:', error);
    
    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to remove resource from project' },
      { status: 500 }
    );
  }
});
