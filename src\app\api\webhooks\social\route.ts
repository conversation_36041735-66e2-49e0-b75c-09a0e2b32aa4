import { NextRequest, NextResponse } from 'next/server';
import { socialMediaIntegrationService } from '@/services/social-media-integration';
import { z } from 'zod';

// Schema for webhook social media data (flexible to support different platforms)
const webhookSocialSchema = z.object({
  // Common fields
  platform: z.string().min(1),
  postId: z.string().min(1),
  authorId: z.string().optional(),
  authorName: z.string().min(1),
  authorHandle: z.string().optional(),
  content: z.string().min(1),
  url: z.string().url().optional(),
  createdAt: z.string().optional(),
  timestamp: z.string().optional(),
  
  // Engagement metrics
  likes: z.number().optional(),
  shares: z.number().optional(),
  retweets: z.number().optional(),
  comments: z.number().optional(),
  views: z.number().optional(),
  
  // Platform-specific fields
  engagement: z.object({
    likes: z.number().optional(),
    shares: z.number().optional(),
    comments: z.number().optional(),
    views: z.number().optional(),
    retweets: z.number().optional()
  }).optional(),
  
  // Metadata
  metadata: z.record(z.any()).optional(),
  tenantId: z.string().optional()
});

/**
 * Webhook endpoint for receiving social media posts from external platforms
 * Supports: Twitter API, LinkedIn API, Facebook Graph API, Instagram API, etc.
 */
export async function POST(request: NextRequest) {
  try {
    const provider = request.headers.get('x-social-provider') || 
                    request.headers.get('x-platform') || 'unknown';
    const tenantId = request.headers.get('x-tenant-id') || 
                    request.headers.get('tenant-id');
    
    if (!tenantId) {
      return NextResponse.json({ 
        error: 'Tenant ID required in headers (x-tenant-id or tenant-id)' 
      }, { status: 400 });
    }

    const body = await request.json();
    console.log(`📱 Received social media webhook from ${provider}:`, body);

    // Validate and normalize the webhook data
    const validationResult = webhookSocialSchema.safeParse(body);
    if (!validationResult.success) {
      console.error('Social webhook validation failed:', validationResult.error);
      return NextResponse.json(
        { 
          error: 'Invalid webhook data',
          details: validationResult.error.errors
        },
        { status: 400 }
      );
    }

    const webhookData = validationResult.data;

    // Normalize social media data based on platform
    const normalizedPost = normalizeSocialData(webhookData, provider);

    // Process the social media post through the integration service
    const result = await socialMediaIntegrationService.processSocialMediaPost(
      tenantId,
      normalizedPost
    );

    console.log(`✅ Social media post processed successfully:`, {
      captureId: result.capture.id,
      leadCreated: !!result.lead,
      contactCreated: !!result.contact,
      platform: normalizedPost.platform
    });

    return NextResponse.json({
      success: true,
      message: 'Social media post processed successfully',
      data: {
        captureId: result.capture.id,
        leadCreated: !!result.lead,
        contactCreated: !!result.contact,
        processed: result.capture.processed,
        platform: normalizedPost.platform,
        leadPotential: result.analysis.leadPotential
      }
    }, { status: 200 });

  } catch (error) {
    console.error('Error processing social media webhook:', error);
    return NextResponse.json(
      { 
        error: 'Failed to process social media webhook',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

/**
 * Normalize social media data from different platforms into a standard format
 */
function normalizeSocialData(webhookData: any, provider: string) {
  const normalized: any = {
    platform: webhookData.platform || provider.toLowerCase(),
    postId: webhookData.postId,
    authorId: webhookData.authorId,
    authorName: webhookData.authorName,
    authorHandle: webhookData.authorHandle,
    content: webhookData.content,
    url: webhookData.url,
    createdAt: webhookData.createdAt || webhookData.timestamp || new Date().toISOString(),
    metadata: webhookData.metadata || {}
  };

  // Normalize engagement metrics based on platform
  const engagement: any = {};
  
  switch (provider.toLowerCase()) {
    case 'twitter':
    case 'x':
      engagement.likes = webhookData.likes || webhookData.engagement?.likes || 0;
      engagement.shares = webhookData.retweets || webhookData.engagement?.retweets || 0;
      engagement.comments = webhookData.comments || webhookData.engagement?.comments || 0;
      engagement.views = webhookData.views || webhookData.engagement?.views || 0;
      break;
      
    case 'linkedin':
      engagement.likes = webhookData.likes || webhookData.engagement?.likes || 0;
      engagement.shares = webhookData.shares || webhookData.engagement?.shares || 0;
      engagement.comments = webhookData.comments || webhookData.engagement?.comments || 0;
      engagement.views = webhookData.views || webhookData.engagement?.views || 0;
      break;
      
    case 'facebook':
    case 'instagram':
      engagement.likes = webhookData.likes || webhookData.engagement?.likes || 0;
      engagement.shares = webhookData.shares || webhookData.engagement?.shares || 0;
      engagement.comments = webhookData.comments || webhookData.engagement?.comments || 0;
      engagement.views = webhookData.views || webhookData.engagement?.views || 0;
      break;
      
    default:
      // Generic platform
      engagement.likes = webhookData.likes || webhookData.engagement?.likes || 0;
      engagement.shares = webhookData.shares || webhookData.engagement?.shares || 0;
      engagement.comments = webhookData.comments || webhookData.engagement?.comments || 0;
      engagement.views = webhookData.views || webhookData.engagement?.views || 0;
  }

  normalized.engagement = engagement;

  return normalized;
}

/**
 * GET endpoint for webhook verification (some platforms require this)
 */
export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const challenge = searchParams.get('challenge');
  const hub_challenge = searchParams.get('hub.challenge'); // Facebook format
  
  if (challenge || hub_challenge) {
    // Return challenge for webhook verification
    return NextResponse.json({ 
      challenge: challenge || hub_challenge 
    });
  }
  
  return NextResponse.json({ 
    message: 'Social media webhook endpoint is active',
    timestamp: new Date().toISOString(),
    supportedPlatforms: ['twitter', 'linkedin', 'facebook', 'instagram', 'youtube', 'tiktok']
  });
}
