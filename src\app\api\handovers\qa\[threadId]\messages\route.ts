import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { handoverQAService } from '@/services/handover-qa';
import { z } from 'zod';

// Request validation schemas
const createQAMessageSchema = z.object({
  content: z.string().min(1, 'Content is required'),
  messageType: z.enum(['message', 'answer', 'clarification', 'escalation']).default('message'),
  attachments: z.array(z.string()).default([]),
});

interface RouteParams {
  params: {
    threadId: string;
  };
}

/**
 * POST /api/handovers/qa/[threadId]/messages - Add a message to a Q&A thread
 */
export async function POST(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get tenantId from user's tenants array (users have one tenant in this system)
    let tenantId: string | null = null;
    if (session.user.tenants && session.user.tenants.length > 0) {
      tenantId = session.user.tenants[0].id;
    }

    if (!tenantId) {
      return NextResponse.json({ error: 'Tenant not found' }, { status: 400 });
    }

    const body = await request.json();
    const validatedData = createQAMessageSchema.parse(body);

    const resolvedParams = await params;
    const message = await handoverQAService.addMessageToThread(
      tenantId,
      resolvedParams.threadId,
      validatedData,
      session.user.id
    );

    return NextResponse.json(message, { status: 201 });
  } catch (error) {
    console.error('Error adding message to Q&A thread:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request data', details: error.errors },
        { status: 400 }
      );
    }

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to add message to Q&A thread' },
      { status: 500 }
    );
  }
}
