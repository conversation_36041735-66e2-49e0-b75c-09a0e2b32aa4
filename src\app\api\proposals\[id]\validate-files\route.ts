import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { hasPermission } from '@/lib/permissions';
import { PermissionResource, PermissionAction } from '@/types';
import { prisma } from '@/lib/prisma';
import { GeminiFileService } from '@/services/gemini-file-service';
import { z } from 'zod';

// Request validation schema
const validateFilesSchema = z.object({
  documentIds: z.array(z.string()).min(1, 'At least one document ID is required')
});

/**
 * POST /api/proposals/[id]/validate-files
 * Validate files for Gemini upload compatibility
 */
export async function POST(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const proposalId = params.id;
    const body = await req.json();
    const validatedData = validateFilesSchema.parse(body);

    // Check permissions
    const currentTenant = session.user.currentTenant;
    if (!currentTenant) {
      return NextResponse.json(
        { error: 'No active tenant found' },
        { status: 400 }
      );
    }

    const hasProposalPermission = await hasPermission(
      session.user.id,
      currentTenant.id,
      PermissionResource.PROPOSALS,
      PermissionAction.UPDATE
    );

    if (!hasProposalPermission) {
      return NextResponse.json(
        { error: 'Insufficient permissions to validate proposal files' },
        { status: 403 }
      );
    }

    // Verify proposal exists and belongs to tenant
    const proposal = await prisma.proposal.findFirst({
      where: {
        id: proposalId,
        tenantId: currentTenant.id
      }
    });

    if (!proposal) {
      return NextResponse.json(
        { error: 'Proposal not found' },
        { status: 404 }
      );
    }

    // Get documents to validate
    const documents = await prisma.document.findMany({
      where: {
        id: { in: validatedData.documentIds },
        tenantId: currentTenant.id
      }
    });

    if (documents.length !== validatedData.documentIds.length) {
      return NextResponse.json(
        { error: 'Some documents not found or not accessible' },
        { status: 400 }
      );
    }

    // Validate each document for Gemini compatibility
    const validationResults = documents.map(doc => {
      const validation = GeminiFileService.validateFileForGemini(
        doc.mimeType || 'application/octet-stream',
        doc.fileSize || 0
      );

      return {
        documentId: doc.id,
        name: doc.name,
        mimeType: doc.mimeType,
        fileSize: doc.fileSize,
        isValid: validation.valid,
        error: validation.error,
        canUpload: validation.valid
      };
    });

    const validFiles = validationResults.filter(result => result.isValid);
    const invalidFiles = validationResults.filter(result => !result.isValid);

    return NextResponse.json({
      success: true,
      data: {
        proposalId,
        totalFiles: documents.length,
        validFiles: validFiles.length,
        invalidFiles: invalidFiles.length,
        validationResults,
        canProceedWithUpload: validFiles.length > 0,
        summary: {
          allValid: invalidFiles.length === 0,
          hasValidFiles: validFiles.length > 0,
          hasInvalidFiles: invalidFiles.length > 0
        }
      },
      message: invalidFiles.length === 0 
        ? 'All files are valid for Gemini upload'
        : validFiles.length === 0
        ? 'No files are valid for Gemini upload'
        : `${validFiles.length} of ${documents.length} files are valid for Gemini upload`
    });

  } catch (error) {
    console.error('File validation error:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          error: 'Invalid request data',
          details: error.errors.map(e => `${e.path.join('.')}: ${e.message}`)
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error during file validation' },
      { status: 500 }
    );
  }
}

/**
 * @swagger
 * /api/proposals/{id}/validate-files:
 *   post:
 *     summary: Validate files for Gemini upload compatibility
 *     description: Check if selected files are compatible with Gemini API upload requirements
 *     tags: [Proposals]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Proposal ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - documentIds
 *             properties:
 *               documentIds:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: Array of document IDs to validate
 *                 minItems: 1
 *     responses:
 *       200:
 *         description: File validation completed
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     proposalId:
 *                       type: string
 *                     totalFiles:
 *                       type: number
 *                     validFiles:
 *                       type: number
 *                     invalidFiles:
 *                       type: number
 *                     validationResults:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           documentId:
 *                             type: string
 *                           name:
 *                             type: string
 *                           mimeType:
 *                             type: string
 *                           fileSize:
 *                             type: number
 *                           isValid:
 *                             type: boolean
 *                           error:
 *                             type: string
 *                           canUpload:
 *                             type: boolean
 *                     canProceedWithUpload:
 *                       type: boolean
 *                     summary:
 *                       type: object
 *                       properties:
 *                         allValid:
 *                           type: boolean
 *                         hasValidFiles:
 *                           type: boolean
 *                         hasInvalidFiles:
 *                           type: boolean
 *                 message:
 *                   type: string
 *       400:
 *         description: Bad request - Invalid input or documents not found
 *       401:
 *         description: Authentication required
 *       403:
 *         description: Insufficient permissions
 *       404:
 *         description: Proposal not found
 *       500:
 *         description: Internal server error
 */
