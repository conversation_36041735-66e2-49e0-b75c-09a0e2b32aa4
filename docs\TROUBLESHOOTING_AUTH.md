# Authentication Troubleshooting Guide

This guide helps you diagnose and fix common authentication issues in the CRM platform.

## Common Error: NextAuth Session Error

### Error Message
```
[next-auth][error][CLIENT_FETCH_ERROR] 
https://next-auth.js.org/errors#client_fetch_error JSON.parse: unexpected character at line 1 column 1 of the JSON data
```

### Root Causes & Solutions

#### 1. Wrong NEXTAUTH_URL Configuration

**Problem**: NEXTAUTH_URL doesn't match the actual application URL.

**Solution**: Update `.env.docker`:
```bash
# Wrong (if using Docker)
NEXTAUTH_URL="http://localhost:3000"

# Correct (for Docker setup)
NEXTAUTH_URL="http://localhost:3010"
```

#### 2. Database Not Initialized

**Problem**: Database tables don't exist or are empty.

**Diagnosis**:
```bash
# Run the diagnostic script
docker-compose exec app node scripts/fix-auth-issues.js
```

**Solution**:
```bash
# Initialize database
docker-compose exec app npx prisma migrate deploy
docker-compose exec app npx prisma generate
docker-compose exec app npm run db:seed
```

#### 3. Missing Initial Users

**Problem**: No users exist in the database for authentication.

**Solution**: The diagnostic script will create a default admin user:
- Email: `<EMAIL>`
- Password: `Admin123!`

#### 4. Database Connection Issues

**Problem**: Application can't connect to PostgreSQL.

**Diagnosis**:
```bash
# Check container status
docker-compose ps

# Check database logs
docker-compose logs postgres

# Test database connection
docker-compose exec app npx prisma db ping
```

**Solution**:
```bash
# Restart containers
docker-compose down
docker-compose up -d

# Wait for database to be ready
docker-compose exec postgres pg_isready -U admin -d crm_platform
```

## Step-by-Step Fix Process

### Step 1: Update Configuration
1. Ensure `NEXTAUTH_URL="http://localhost:3010"` in `.env.docker`
2. Restart containers: `docker-compose down && docker-compose up -d`

### Step 2: Initialize Database
```bash
# Run migrations
docker-compose exec app npx prisma migrate deploy

# Generate Prisma client
docker-compose exec app npx prisma generate

# Run diagnostic script
docker-compose exec app node scripts/fix-auth-issues.js
```

### Step 3: Verify Setup
1. Open http://localhost:3010
2. Try logging in with:
   - Email: `<EMAIL>`
   - Password: `Admin123!`

### Step 4: Check Database (Optional)
1. Open pgAdmin at http://localhost:8082
2. Login with:
   - Email: `<EMAIL>`
   - Password: `admin`
3. Connect to database:
   - Host: `postgres`
   - Port: `5432`
   - Username: `admin`
   - Password: `admin`
   - Database: `crm_platform`

## Quick Fix Script

Use the provided restart script:
```bash
# Windows
scripts\restart-docker.bat

# Linux/Mac
chmod +x scripts/restart-docker.sh
./scripts/restart-docker.sh
```

## Environment Variables Checklist

Ensure these variables are set in `.env.docker`:

```bash
# Database
DATABASE_URL="**************************************/crm_platform"

# Authentication
NEXTAUTH_SECRET="your-secret-here"
NEXTAUTH_URL="http://localhost:3010"
JWT_SECRET="your-jwt-secret-here"

# Redis
REDIS_URL="redis://redis:6379"
```

## Container Health Check

```bash
# Check all containers
docker-compose ps

# Check specific container logs
docker-compose logs app
docker-compose logs postgres
docker-compose logs redis
docker-compose logs pgadmin

# Check container resource usage
docker stats
```

## Database Verification Commands

```bash
# Connect to database
docker-compose exec postgres psql -U admin -d crm_platform

# Check if tables exist
\dt

# Check users table
SELECT id, email, "first_name", "last_name", "is_platform_admin" FROM users;

# Check tenants table
SELECT id, name, slug, status FROM tenants;

# Exit psql
\q
```

## Common Issues & Solutions

### Issue: "Database connection failed"
- Check if PostgreSQL container is running
- Verify DATABASE_URL format
- Ensure database exists

### Issue: "Prisma client not generated"
- Run: `docker-compose exec app npx prisma generate`

### Issue: "Migration failed"
- Check database permissions
- Ensure no conflicting migrations
- Try: `docker-compose exec app npx prisma migrate reset`

### Issue: "No users found"
- Run the diagnostic script to create initial user
- Or manually seed: `docker-compose exec app npm run db:seed`

### Issue: "Session token invalid"
- Clear browser cookies
- Restart application container
- Check NEXTAUTH_SECRET consistency

## Support

If issues persist:
1. Check container logs for specific errors
2. Verify all environment variables
3. Ensure Docker has sufficient resources
4. Try a clean restart with volume cleanup

For additional help, check the main documentation or create an issue with:
- Error messages
- Container logs
- Environment configuration (without secrets)
