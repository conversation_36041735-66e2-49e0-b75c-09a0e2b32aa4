import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { hasPermission } from '@/lib/permissions';
import { PermissionResource, PermissionAction } from '@/types';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';

// Request validation schema
const duplicateProposalSchema = z.object({
  title: z.string().min(1, 'Title is required').max(255),
  versionType: z.enum(['minor', 'major', 'copy']).default('minor'),
  includeContent: z.boolean().default(true),
  includeCharts: z.boolean().default(true)
});

/**
 * POST /api/proposals/[id]/duplicate
 * Create a duplicate/new version of an existing proposal
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user's current tenant
    const currentTenant = session.user.tenants?.[0];
    if (!currentTenant) {
      return NextResponse.json({ error: 'No tenant access' }, { status: 403 });
    }

    // Check permission for proposal creation
    const hasAccess = await hasPermission(
      PermissionResource.PROPOSALS,
      PermissionAction.CREATE,
      currentTenant.id
    );

    if (!hasAccess) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const proposalId = params.id;
    const body = await request.json();
    const validatedData = duplicateProposalSchema.parse(body);

    // Get the original proposal with all related data
    const originalProposal = await prisma.proposal.findFirst({
      where: {
        id: proposalId,
        tenantId: currentTenant.id
      },
      include: {
        sections: true,
        charts: true
      }
    });

    if (!originalProposal) {
      return NextResponse.json(
        { error: 'Proposal not found or access denied' },
        { status: 404 }
      );
    }

    // Calculate new version number
    let newVersion = 1;
    
    if (validatedData.versionType !== 'copy') {
      // Find the highest version for proposals with the same base title
      const baseTitle = validatedData.title;
      const existingVersions = await prisma.proposal.findMany({
        where: {
          tenantId: currentTenant.id,
          opportunityId: originalProposal.opportunityId,
          title: baseTitle
        },
        select: {
          version: true
        },
        orderBy: {
          version: 'desc'
        }
      });

      if (existingVersions.length > 0) {
        const highestVersion = existingVersions[0].version;
        
        if (validatedData.versionType === 'major') {
          // Increment major version (e.g., 1.3 -> 2.0)
          newVersion = Math.floor(highestVersion) + 1;
        } else {
          // Increment minor version (e.g., 1.3 -> 1.4)
          const majorVersion = Math.floor(highestVersion);
          const minorVersion = Math.round((highestVersion - majorVersion) * 10);
          newVersion = majorVersion + (minorVersion + 1) / 10;
        }
      }
    } else {
      // For copies, find a unique version number
      const existingVersions = await prisma.proposal.findMany({
        where: {
          tenantId: currentTenant.id,
          opportunityId: originalProposal.opportunityId,
          title: validatedData.title
        },
        select: {
          version: true
        },
        orderBy: {
          version: 'desc'
        }
      });

      if (existingVersions.length > 0) {
        newVersion = Math.floor(existingVersions[0].version) + 1;
      }
    }

    // Create the new proposal
    const newProposal = await prisma.proposal.create({
      data: {
        tenantId: currentTenant.id,
        opportunityId: originalProposal.opportunityId,
        title: validatedData.title,
        content: validatedData.includeContent ? originalProposal.content : null,
        status: 'draft',
        version: newVersion,
        totalAmount: originalProposal.totalAmount,
        isAiGenerated: originalProposal.isAiGenerated,
        aiProvider: originalProposal.aiProvider,
        aiModel: originalProposal.aiModel,
        generationStatus: 'completed',
        selectedFileIds: originalProposal.selectedFileIds,
        createdById: session.user.id,
        parentProposalId: originalProposal.id
      }
    });

    // Duplicate sections if requested
    if (validatedData.includeContent && originalProposal.sections.length > 0) {
      const sectionsData = originalProposal.sections.map(section => ({
        tenantId: currentTenant.id,
        proposalId: newProposal.id,
        sectionType: section.sectionType,
        title: section.title,
        content: section.content,
        status: 'generated',
        orderIndex: section.orderIndex,
        wordCount: section.wordCount,
        tokensUsed: section.tokensUsed,
        confidence: section.confidence,
        createdById: session.user.id
      }));

      await prisma.proposalSection.createMany({
        data: sectionsData
      });
    }

    // Duplicate charts if requested
    if (validatedData.includeCharts && originalProposal.charts.length > 0) {
      const chartsData = originalProposal.charts.map(chart => ({
        tenantId: currentTenant.id,
        proposalId: newProposal.id,
        chartType: chart.chartType,
        title: chart.title,
        description: chart.description,
        mermaidCode: chart.mermaidCode,
        renderedImagePath: null, // Will need to re-render
        status: 'generated',
        orderIndex: chart.orderIndex,
        tokensUsed: chart.tokensUsed,
        confidence: chart.confidence,
        createdById: session.user.id
      }));

      await prisma.proposalChart.createMany({
        data: chartsData
      });
    }

    // Get the created proposal with relations
    const createdProposal = await prisma.proposal.findUnique({
      where: { id: newProposal.id },
      include: {
        createdBy: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true
          }
        },
        sections: {
          select: {
            id: true,
            status: true
          }
        },
        charts: {
          select: {
            id: true,
            status: true
          }
        }
      }
    });

    return NextResponse.json({
      success: true,
      data: {
        proposal: createdProposal,
        originalProposalId: originalProposal.id,
        versionType: validatedData.versionType,
        newVersion
      },
      message: `Proposal ${validatedData.versionType === 'copy' ? 'copied' : 'version created'} successfully`
    }, { status: 201 });

  } catch (error) {
    console.error('Error duplicating proposal:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
