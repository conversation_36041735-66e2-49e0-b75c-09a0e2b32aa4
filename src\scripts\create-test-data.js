/**
 * Create test data for handover testing
 */

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function createTestData() {
  console.log('🚀 Creating Test Data for Handovers...\n');

  try {
    // 1. Find or create tenant
    let tenant = await prisma.tenant.findFirst({
      where: { status: 'active' }
    });

    if (!tenant) {
      console.log('Creating test tenant...');
      tenant = await prisma.tenant.create({
        data: {
          name: 'Test Company',
          status: 'active',
          settings: {}
        }
      });
    }

    console.log(`✅ Using tenant: ${tenant.name} (${tenant.id})`);

    // 2. Find or create user
    let user = await prisma.user.findFirst();

    if (!user) {
      console.log('Creating test user...');
      user = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          firstName: 'Test',
          lastName: 'User',
          tenants: {
            create: {
              tenantId: tenant.id,
              role: 'admin'
            }
          }
        }
      });
    }

    console.log(`✅ Using user: ${user.email} (${user.id})`);

    // 3. Find or create contact
    let contact = await prisma.contact.findFirst({
      where: { tenantId: tenant.id }
    });

    if (!contact) {
      console.log('Creating test contact...');
      contact = await prisma.contact.create({
        data: {
          tenantId: tenant.id,
          firstName: 'John',
          lastName: 'Doe',
          email: '<EMAIL>',
          phone: '+1234567890',
          ownerId: user.id
        }
      });
    }

    console.log(`✅ Using contact: ${contact.firstName} ${contact.lastName} (${contact.id})`);

    // 4. Find or create company
    let company = await prisma.company.findFirst({
      where: { tenantId: tenant.id }
    });

    if (!company) {
      console.log('Creating test company...');
      company = await prisma.company.create({
        data: {
          tenantId: tenant.id,
          name: 'Test Company Inc.',
          industry: 'Technology',
          website: 'https://testcompany.com',
          ownerId: user.id
        }
      });
    }

    console.log(`✅ Using company: ${company.name} (${company.id})`);

    // 5. Find or create opportunity
    let opportunity = await prisma.opportunity.findFirst({
      where: { tenantId: tenant.id }
    });

    if (!opportunity) {
      console.log('Creating test opportunity...');
      opportunity = await prisma.opportunity.create({
        data: {
          tenantId: tenant.id,
          title: 'Test Opportunity - Website Development',
          description: 'A test opportunity for website development project',
          value: 50000,
          stage: 'Closed Won',
          probability: 100,
          expectedCloseDate: new Date(),
          contactId: contact.id,
          companyId: company.id,
          ownerId: user.id,
          source: 'Website',
          priority: 'high'
        }
      });
    }

    console.log(`✅ Using opportunity: ${opportunity.title} (${opportunity.id})`);

    // 6. Create test handovers
    console.log('\n📝 Creating test handovers...');

    const handovers = [
      {
        title: 'Website Development Handover',
        description: 'Handover for the website development project',
        status: 'pending',
        priority: 'high',
        checklistProgress: 0
      },
      {
        title: 'Mobile App Development Handover',
        description: 'Handover for the mobile app development project',
        status: 'in_progress',
        priority: 'medium',
        checklistProgress: 45
      },
      {
        title: 'Database Migration Handover',
        description: 'Handover for the database migration project',
        status: 'completed',
        priority: 'low',
        checklistProgress: 100
      }
    ];

    for (const handoverData of handovers) {
      const existingHandover = await prisma.projectHandover.findFirst({
        where: {
          tenantId: tenant.id,
          title: handoverData.title
        }
      });

      if (!existingHandover) {
        const handover = await prisma.projectHandover.create({
          data: {
            tenantId: tenant.id,
            opportunityId: opportunity.id,
            ...handoverData,
            salesRepId: user.id,
            deliveryManagerId: user.id,
            assignedTeamIds: [user.id],
            documentsCount: 0,
            qaThreadsCount: 0,
            scheduledDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days from now
            estimatedDuration: 480, // 8 hours
            createdById: user.id
          }
        });

        console.log(`   ✅ Created: ${handover.title} (${handover.id})`);
      } else {
        console.log(`   ⏭️  Exists: ${handoverData.title}`);
      }
    }

    // 7. Verify handovers exist
    console.log('\n🔍 Verifying created handovers...');
    const allHandovers = await prisma.projectHandover.findMany({
      where: { tenantId: tenant.id },
      include: {
        opportunity: { select: { title: true } },
        salesRep: { select: { firstName: true, lastName: true } },
        deliveryManager: { select: { firstName: true, lastName: true } }
      }
    });

    console.log(`✅ Found ${allHandovers.length} handovers:`);
    allHandovers.forEach(handover => {
      console.log(`   - ${handover.title} (${handover.status}) - ID: ${handover.id}`);
    });

    console.log('\n🎉 Test data creation completed!');
    console.log('\n🧪 Now you can test the handover buttons:');
    console.log('   1. Go to http://localhost:3000/handovers');
    console.log('   2. You should see the handovers listed');
    console.log('   3. Try clicking the Edit and Delete buttons');
    console.log('   4. Check browser console for click logs');

    console.log('\n🔗 Quick links:');
    console.log('   • Handover List: http://localhost:3000/handovers');
    allHandovers.forEach(handover => {
      console.log(`   • ${handover.title}: http://localhost:3000/handovers/${handover.id}`);
    });

    return true;

  } catch (error) {
    console.error('❌ Error creating test data:', error);
    return false;
  } finally {
    await prisma.$disconnect();
  }
}

// Run if called directly
if (require.main === module) {
  createTestData().catch(console.error);
}

module.exports = { createTestData };
