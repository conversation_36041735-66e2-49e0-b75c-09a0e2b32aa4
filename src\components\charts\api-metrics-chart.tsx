'use client';

import React from 'react';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  BarChart,
  Bar,
  ReferenceLine,
} from 'recharts';
import { BaseChart, BaseChartProps } from './base-chart';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

interface ApiMetrics {
  averageResponseTime: number;
  p95ResponseTime: number;
  p99ResponseTime: number;
  errorRate: number;
  requestsPerMinute: number;
}

interface ApiMetricsChartProps extends Omit<BaseChartProps, 'children'> {
  data: ApiMetrics;
  timeWindow: string;
}

export function ApiMetricsChart({
  data,
  timeWindow,
  ...baseProps
}: ApiMetricsChartProps) {
  // Generate mock historical data for API metrics
  const generateApiTrendData = () => {
    const points = 20;
    const now = Date.now();
    const intervalMs = getIntervalMs(timeWindow, points);
    
    return Array.from({ length: points }, (_, i) => {
      const timestamp = now - (points - 1 - i) * intervalMs;
      const variance = 0.15; // 15% variance
      
      return {
        timestamp,
        time: formatTime(new Date(timestamp)),
        avgResponseTime: data.averageResponseTime * (1 + (Math.random() - 0.5) * variance),
        p95ResponseTime: data.p95ResponseTime * (1 + (Math.random() - 0.5) * variance),
        p99ResponseTime: data.p99ResponseTime * (1 + (Math.random() - 0.5) * variance),
        errorRate: Math.max(0, data.errorRate * (1 + (Math.random() - 0.5) * variance)),
        requestsPerMinute: data.requestsPerMinute * (1 + (Math.random() - 0.5) * variance),
      };
    });
  };

  // Generate mock endpoint performance data
  const endpointData = [
    { endpoint: '/api/auth/signin', avgTime: 120, requests: 450, errors: 2 },
    { endpoint: '/api/dashboard/analytics', avgTime: 340, requests: 280, errors: 1 },
    { endpoint: '/api/contacts', avgTime: 180, requests: 320, errors: 0 },
    { endpoint: '/api/leads', avgTime: 220, requests: 190, errors: 1 },
    { endpoint: '/api/companies', avgTime: 160, requests: 150, errors: 0 },
    { endpoint: '/api/admin/tenants', avgTime: 890, requests: 45, errors: 3 },
    { endpoint: '/api/admin/users', avgTime: 420, requests: 78, errors: 1 },
  ].sort((a, b) => b.avgTime - a.avgTime);

  // Generate mock status code distribution
  const statusCodeData = [
    { code: '200', count: 1450, percentage: 92.3 },
    { code: '201', count: 85, percentage: 5.4 },
    { code: '400', count: 18, percentage: 1.1 },
    { code: '401', count: 12, percentage: 0.8 },
    { code: '404', count: 4, percentage: 0.3 },
    { code: '500', count: 2, percentage: 0.1 },
  ];

  const getIntervalMs = (timeWindow: string, points: number) => {
    const timeMap: Record<string, number> = {
      '5m': 5 * 60 * 1000,
      '15m': 15 * 60 * 1000,
      '1h': 60 * 60 * 1000,
      '6h': 6 * 60 * 60 * 1000,
      '24h': 24 * 60 * 60 * 1000,
    };
    return (timeMap[timeWindow] || timeMap['15m']) / points;
  };

  const formatTime = (date: Date) => {
    if (timeWindow === '5m' || timeWindow === '15m') {
      return date.toLocaleTimeString('en-US', { 
        hour: '2-digit', 
        minute: '2-digit',
        second: '2-digit',
        hour12: false 
      });
    } else if (timeWindow === '1h' || timeWindow === '6h') {
      return date.toLocaleTimeString('en-US', { 
        hour: '2-digit', 
        minute: '2-digit',
        hour12: false 
      });
    } else {
      return date.toLocaleDateString('en-US', { 
        month: 'short', 
        day: 'numeric',
        hour: '2-digit'
      });
    }
  };

  const formatTooltipValue = (value: number, name: string) => {
    switch (name) {
      case 'avgResponseTime':
      case 'p95ResponseTime':
      case 'p99ResponseTime':
      case 'avgTime':
        return [`${Math.round(value)}ms`, name.replace('ResponseTime', ' Response Time').replace('avgTime', 'Avg Time')];
      case 'errorRate':
        return [`${value.toFixed(2)}%`, 'Error Rate'];
      case 'requestsPerMinute':
      case 'requests':
        return [`${Math.round(value)}`, name === 'requestsPerMinute' ? 'Requests/min' : 'Requests'];
      case 'errors':
        return [`${Math.round(value)}`, 'Errors'];
      case 'count':
        return [`${Math.round(value)}`, 'Count'];
      default:
        return [value.toString(), name];
    }
  };

  const trendData = generateApiTrendData();

  return (
    <BaseChart {...baseProps}>
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Response Time Percentiles */}
        <Card className="bg-white dark:bg-gray-800 rounded-lg shadow-soft">
          <CardHeader>
            <CardTitle className="text-sm">Response Time Percentiles</CardTitle>
            <CardDescription>API response time distribution over time</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={trendData}>
                <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
                <XAxis 
                  dataKey="time" 
                  className="text-xs"
                  interval="preserveStartEnd"
                />
                <YAxis 
                  className="text-xs"
                  label={{ value: 'Response Time (ms)', angle: -90, position: 'insideLeft' }}
                />
                <Tooltip
                  formatter={formatTooltipValue}
                  contentStyle={{
                    backgroundColor: 'hsl(var(--card))',
                    border: '1px solid hsl(var(--border))',
                    borderRadius: '6px',
                    fontSize: '12px'
                  }}
                />
                <Legend />
                
                <Line
                  type="monotone"
                  dataKey="avgResponseTime"
                  stroke="hsl(var(--primary))"
                  strokeWidth={3}
                  dot={false}
                  activeDot={{ r: 6, fill: 'hsl(var(--primary))', strokeWidth: 2 }}
                  name="Average"
                  connectNulls={true}
                />

                <Line
                  type="monotone"
                  dataKey="p95ResponseTime"
                  stroke="hsl(var(--chart-2))"
                  strokeWidth={3}
                  dot={false}
                  activeDot={{ r: 6, fill: 'hsl(var(--chart-2))', strokeWidth: 2 }}
                  name="95th Percentile"
                  connectNulls={true}
                />

                <Line
                  type="monotone"
                  dataKey="p99ResponseTime"
                  stroke="hsl(var(--chart-3))"
                  strokeWidth={3}
                  dot={false}
                  activeDot={{ r: 6, fill: 'hsl(var(--chart-3))', strokeWidth: 2 }}
                  name="99th Percentile"
                  connectNulls={true}
                />
                
                {/* Reference lines */}
                <ReferenceLine y={1000} stroke="orange" strokeDasharray="5 5" />
                <ReferenceLine y={2000} stroke="red" strokeDasharray="5 5" />
              </LineChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Request Volume & Error Rate */}
        <Card className="bg-white dark:bg-gray-800 rounded-lg shadow-soft">
          <CardHeader>
            <CardTitle className="text-sm">Request Volume & Error Rate</CardTitle>
            <CardDescription>API traffic and error trends</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={trendData}>
                <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
                <XAxis 
                  dataKey="time" 
                  className="text-xs"
                  interval="preserveStartEnd"
                />
                <YAxis 
                  yAxisId="requests"
                  orientation="left"
                  className="text-xs"
                  label={{ value: 'Requests/min', angle: -90, position: 'insideLeft' }}
                />
                <YAxis 
                  yAxisId="errorRate"
                  orientation="right"
                  className="text-xs"
                  label={{ value: 'Error Rate (%)', angle: 90, position: 'insideRight' }}
                />
                <Tooltip
                  formatter={formatTooltipValue}
                  contentStyle={{
                    backgroundColor: 'hsl(var(--card))',
                    border: '1px solid hsl(var(--border))',
                    borderRadius: '6px',
                    fontSize: '12px'
                  }}
                />
                <Legend />
                
                <Line
                  yAxisId="requests"
                  type="monotone"
                  dataKey="requestsPerMinute"
                  stroke="hsl(var(--chart-4))"
                  strokeWidth={3}
                  dot={false}
                  activeDot={{ r: 6, fill: 'hsl(var(--chart-4))', strokeWidth: 2 }}
                  name="Requests/min"
                  connectNulls={true}
                />

                <Line
                  yAxisId="errorRate"
                  type="monotone"
                  dataKey="errorRate"
                  stroke="hsl(var(--destructive))"
                  strokeWidth={3}
                  dot={false}
                  activeDot={{ r: 6, fill: 'hsl(var(--destructive))', strokeWidth: 2 }}
                  name="Error Rate"
                  connectNulls={true}
                />
                
                {/* Reference line for error rate */}
                <ReferenceLine yAxisId="errorRate" y={2} stroke="red" strokeDasharray="5 5" />
              </LineChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Endpoint Performance */}
        <Card className="bg-white dark:bg-gray-800 rounded-lg shadow-soft">
          <CardHeader>
            <CardTitle className="text-sm">Endpoint Performance</CardTitle>
            <CardDescription>Response times by API endpoint</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={endpointData} layout="horizontal">
                <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
                <XAxis 
                  type="number"
                  className="text-xs"
                  label={{ value: 'Response Time (ms)', position: 'insideBottom', offset: -5 }}
                />
                <YAxis 
                  type="category"
                  dataKey="endpoint"
                  className="text-xs"
                  width={120}
                  tickFormatter={(value) => value.replace('/api/', '')}
                />
                <Tooltip
                  formatter={formatTooltipValue}
                  contentStyle={{
                    backgroundColor: 'hsl(var(--card))',
                    border: '1px solid hsl(var(--border))',
                    borderRadius: '6px',
                    fontSize: '12px'
                  }}
                />
                <Legend />
                
                <Bar
                  dataKey="avgTime"
                  fill="hsl(var(--primary))"
                  name="Avg Response Time"
                  radius={[0, 4, 4, 0]}
                />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Status Code Distribution */}
        <Card className="bg-white dark:bg-gray-800 rounded-lg shadow-soft">
          <CardHeader>
            <CardTitle className="text-sm">HTTP Status Code Distribution</CardTitle>
            <CardDescription>Response status breakdown</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {statusCodeData.map((status, index) => (
                <div key={status.code} className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div 
                      className="w-3 h-3 rounded-full"
                      style={{ 
                        backgroundColor: status.code.startsWith('2') ? 'hsl(var(--chart-1))' :
                                        status.code.startsWith('3') ? 'hsl(var(--chart-2))' :
                                        status.code.startsWith('4') ? 'hsl(var(--chart-3))' :
                                        'hsl(var(--destructive))'
                      }}
                    />
                    <span className="text-sm font-medium">{status.code}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="text-sm text-muted-foreground">{status.count}</span>
                    <span className="text-sm font-medium">{status.percentage}%</span>
                  </div>
                </div>
              ))}
            </div>
            
            <div className="mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
              <div className="grid grid-cols-3 gap-4 text-center">
                <div>
                  <div className="text-lg font-bold text-green-500">
                    {statusCodeData.filter(s => s.code.startsWith('2')).reduce((sum, s) => sum + s.count, 0)}
                  </div>
                  <div className="text-xs text-muted-foreground">Success</div>
                </div>
                <div>
                  <div className="text-lg font-bold text-yellow-500">
                    {statusCodeData.filter(s => s.code.startsWith('4')).reduce((sum, s) => sum + s.count, 0)}
                  </div>
                  <div className="text-xs text-muted-foreground">Client Errors</div>
                </div>
                <div>
                  <div className="text-lg font-bold text-red-500">
                    {statusCodeData.filter(s => s.code.startsWith('5')).reduce((sum, s) => sum + s.count, 0)}
                  </div>
                  <div className="text-xs text-muted-foreground">Server Errors</div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </BaseChart>
  );
}
