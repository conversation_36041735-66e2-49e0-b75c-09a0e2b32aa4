'use client';

import { useEffect, useState } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import {
  UsersIcon,
  BuildingOfficeIcon,
  ArrowTrendingUpIcon,
  ChartBarIcon,
  ClockIcon,
  CurrencyDollarIcon,
  ServerIcon,
  DocumentChartBarIcon,
  CalendarIcon
} from '@heroicons/react/24/outline';
import {
  PlatformAnalytics,
  TrendData,
  RevenueAnalytics,
  SystemHealthMetrics
} from '@/services/super-admin';
import { EnhancedMetricCard } from '@/components/dashboard/enhanced-metric-card';
import { TrendLineChart } from '@/components/charts/trend-line-chart';
import { SubscriptionPieChart } from '@/components/charts/subscription-pie-chart';
import { RevenueBarChart } from '@/components/charts/revenue-bar-chart';
import { ActivityFeed } from '@/components/dashboard/activity-feed';
import { SystemHealthMonitor } from '@/components/dashboard/system-health-monitor';
import { DateRangeSelector, DateRange } from '@/components/dashboard/date-range-selector';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { <PERSON><PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';

interface DashboardState {
  analytics: PlatformAnalytics | null;
  trends: TrendData[];
  revenue: RevenueAnalytics | null;
  systemHealth: SystemHealthMetrics | null;
  loading: boolean;
  error: string | null;
  dateRange: DateRange;
  activeTab: string;
}

export default function SuperAdminDashboard() {
  const { data: session, status } = useSession();
  const router = useRouter();

  const [state, setState] = useState<DashboardState>({
    analytics: null,
    trends: [],
    revenue: null,
    systemHealth: null,
    loading: true,
    error: null,
    dateRange: {
      from: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // Last 30 days
      to: new Date()
    },
    activeTab: 'overview'
  });

  useEffect(() => {
    if (status === 'loading') return;

    if (!session?.user?.isPlatformAdmin) {
      router.push('/dashboard');
      return;
    }

    fetchAllData();
  }, [session, status, router]);

  useEffect(() => {
    // Fetch trends when date range changes
    fetchTrends();
  }, [state.dateRange]);

  const fetchAllData = async () => {
    setState(prev => ({ ...prev, loading: true, error: null }));

    try {
      const [analyticsRes, trendsRes, revenueRes] = await Promise.all([
        fetch('/api/admin/platform/analytics'),
        fetch(`/api/admin/platform/analytics/trends?days=30`),
        fetch('/api/admin/platform/analytics/revenue')
      ]);

      if (!analyticsRes.ok || !trendsRes.ok || !revenueRes.ok) {
        throw new Error('Failed to fetch dashboard data');
      }

      const [analyticsData, trendsData, revenueData] = await Promise.all([
        analyticsRes.json(),
        trendsRes.json(),
        revenueRes.json()
      ]);

      setState(prev => ({
        ...prev,
        analytics: analyticsData.data,
        trends: trendsData.data,
        revenue: revenueData.data,
        loading: false
      }));
    } catch (err) {
      setState(prev => ({
        ...prev,
        error: err instanceof Error ? err.message : 'An error occurred',
        loading: false
      }));
    }
  };

  const fetchTrends = async () => {
    try {
      const days = Math.ceil((state.dateRange.to.getTime() - state.dateRange.from.getTime()) / (1000 * 60 * 60 * 24));
      const response = await fetch(`/api/admin/platform/analytics/trends?days=${days}`);

      if (response.ok) {
        const data = await response.json();
        setState(prev => ({ ...prev, trends: data.data }));
      }
    } catch (err) {
      console.error('Failed to fetch trends:', err);
    }
  };

  const handleDateRangeChange = (newRange: DateRange) => {
    setState(prev => ({ ...prev, dateRange: newRange }));
  };

  const handleTabChange = (tab: string) => {
    setState(prev => ({ ...prev, activeTab: tab }));
  };

  if (status === 'loading' || state.loading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  if (state.error) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">Error Loading Dashboard</h2>
          <p className="text-gray-600 dark:text-gray-400 mb-4">{state.error}</p>
          <Button onClick={fetchAllData} className="mt-4">
            Retry
          </Button>
        </div>
      </div>
    );
  }

  if (!state.analytics) {
    return null;
  }

  const { analytics, trends, revenue } = state;

  return (
    <div className="px-4 sm:px-6 lg:px-8 py-8 space-y-8">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            Super Admin Dashboard
          </h1>
          <p className="mt-1 text-gray-600 dark:text-gray-400">
            Comprehensive platform analytics and real-time monitoring
          </p>
        </div>
        <div className="mt-4 sm:mt-0 flex items-center space-x-4">
          <DateRangeSelector
            value={state.dateRange}
            onChange={handleDateRangeChange}
          />
          <Button
            variant="outline"
            onClick={fetchAllData}
            disabled={state.loading}
          >
            <ArrowTrendingUpIcon className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Enhanced Metrics Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <EnhancedMetricCard
          title="Total Tenants"
          value={analytics.totalTenants}
          change={analytics.monthlyGrowth}
          changeLabel="vs last month"
          trend={analytics.monthlyGrowth > 0 ? 'up' : analytics.monthlyGrowth < 0 ? 'down' : 'neutral'}
          icon={BuildingOfficeIcon}
          format="number"
          onClick={() => router.push('/admin/tenants')}
        />
        <EnhancedMetricCard
          title="Active Users"
          value={analytics.activeUsers}
          subtitle={`${analytics.totalUsers} total users`}
          icon={UsersIcon}
          format="number"
          color="success"
          onClick={() => router.push('/admin/users')}
        />
        <EnhancedMetricCard
          title="Monthly Revenue"
          value={revenue?.monthlyRecurringRevenue || 0}
          change={revenue?.revenueGrowthRate}
          changeLabel="growth rate"
          trend={revenue && revenue.revenueGrowthRate > 0 ? 'up' : 'neutral'}
          icon={CurrencyDollarIcon}
          format="currency"
          color="success"
        />
        <EnhancedMetricCard
          title="System Health"
          value="99.9"
          subtitle="Uptime percentage"
          icon={ServerIcon}
          format="percentage"
          color="success"
        />
      </div>

      {/* Tabbed Analytics Interface */}
      <Tabs value={state.activeTab} onValueChange={handleTabChange} className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
          <TabsTrigger value="revenue">Revenue</TabsTrigger>
          <TabsTrigger value="system">System Health</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Growth Trends Chart */}
            <TrendLineChart
              title="Platform Growth Trends"
              description="Tenant and user growth over time"
              data={trends}
              showTenants={true}
              showUsers={true}
              loading={state.loading}
            />

            {/* Activity Feed */}
            <ActivityFeed />
          </div>

          {/* Top Tenants Table */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-6">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
              Top Tenants by Users
            </h3>
            <div className="space-y-4">
              {analytics.topTenantsByUsers.map((tenant) => (
                <div key={tenant.id} className="flex items-center justify-between p-3 rounded-lg hover:bg-muted/50 transition-colors">
                  <div>
                    <p className="font-medium text-gray-900 dark:text-white">
                      {tenant.name}
                    </p>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      Status: {tenant.status}
                    </p>
                  </div>
                  <div className="text-right">
                    <p className="font-semibold text-gray-900 dark:text-white">
                      {tenant.userCount} users
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* User Growth Chart */}
            <TrendLineChart
              title="User Growth Analytics"
              description="Detailed user acquisition trends"
              data={trends}
              showTenants={false}
              showUsers={true}
              loading={state.loading}
            />

            {/* Tenant Growth Chart */}
            <TrendLineChart
              title="Tenant Growth Analytics"
              description="New tenant acquisition over time"
              data={trends}
              showTenants={true}
              showUsers={false}
              loading={state.loading}
            />
          </div>
        </TabsContent>

        <TabsContent value="revenue" className="space-y-6">
          {revenue && (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Revenue by Month */}
              <RevenueBarChart
                title="Monthly Revenue Trends"
                description="Revenue and subscription growth"
                data={revenue.revenueByMonth}
                loading={state.loading}
              />

              {/* Subscription Breakdown */}
              <SubscriptionPieChart
                title="Subscription Plan Distribution"
                description="Revenue breakdown by plan type"
                data={revenue.subscriptionBreakdown}
                showRevenue={true}
                loading={state.loading}
              />
            </div>
          )}
        </TabsContent>

        <TabsContent value="system" className="space-y-6">
          <SystemHealthMonitor />
        </TabsContent>
      </Tabs>

      {/* Quick Actions */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-6">
          Quick Actions
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Button
            variant="outline"
            onClick={() => router.push('/admin/tenants')}
            className="h-auto p-4 flex flex-col items-start space-y-2 hover:bg-muted/50"
          >
            <BuildingOfficeIcon className="w-6 h-6 text-primary-600 dark:text-primary-400" />
            <div className="text-left">
              <h4 className="font-semibold text-sm">Manage Tenants</h4>
              <p className="text-xs text-muted-foreground">
                View and manage all tenants
              </p>
            </div>
          </Button>

          <Button
            variant="outline"
            onClick={() => router.push('/admin/users')}
            className="h-auto p-4 flex flex-col items-start space-y-2 hover:bg-muted/50"
          >
            <UsersIcon className="w-6 h-6 text-primary-600 dark:text-primary-400" />
            <div className="text-left">
              <h4 className="font-semibold text-sm">Manage Users</h4>
              <p className="text-xs text-muted-foreground">
                View and manage all users
              </p>
            </div>
          </Button>

          <Button
            variant="outline"
            onClick={() => router.push('/admin/analytics')}
            className="h-auto p-4 flex flex-col items-start space-y-2 hover:bg-muted/50"
          >
            <DocumentChartBarIcon className="w-6 h-6 text-primary-600 dark:text-primary-400" />
            <div className="text-left">
              <h4 className="font-semibold text-sm">Detailed Analytics</h4>
              <p className="text-xs text-muted-foreground">
                Advanced platform insights
              </p>
            </div>
          </Button>

          <Button
            variant="outline"
            onClick={() => router.push('/admin/billing')}
            className="h-auto p-4 flex flex-col items-start space-y-2 hover:bg-muted/50"
          >
            <CurrencyDollarIcon className="w-6 h-6 text-primary-600 dark:text-primary-400" />
            <div className="text-left">
              <h4 className="font-semibold text-sm">Billing Overview</h4>
              <p className="text-xs text-muted-foreground">
                Revenue and subscriptions
              </p>
            </div>
          </Button>
        </div>
      </div>
    </div>
  );
}
