import { NextResponse } from 'next/server';
import { smtpService } from '@/services/smtp-service';

export async function GET() {
  try {
    console.log('🧪 Testing SMTP service...');
    
    const isReady = smtpService.isReady();
    const config = smtpService.getConfig();
    
    let connectionTest = null;
    if (isReady) {
      connectionTest = await smtpService.testConnection();
    }

    const result = {
      isReady,
      config: {
        host: config.host,
        port: config.port,
        secure: config.secure,
        fromEmail: config.from.email,
        fromName: config.from.name
      },
      connectionTest,
      environment: {
        ENABLE_EMAIL_SENDING: process.env.ENABLE_EMAIL_SENDING,
        SMTP_HOST: process.env.SMTP_HOST,
        SMTP_PORT: process.env.SMTP_PORT,
        SMTP_USER: process.env.SMTP_USER ? '***configured***' : 'NOT SET',
        SMTP_PASS: process.env.SMTP_PASS ? '***configured***' : 'NOT SET',
        SMTP_FROM_EMAIL: process.env.SMTP_FROM_EMAIL
      }
    };

    console.log('📊 SMTP Test Result:', result);

    return NextResponse.json({
      success: true,
      data: result
    });

  } catch (error) {
    console.error('❌ SMTP test error:', error);
    return NextResponse.json(
      { 
        error: 'SMTP test failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
