"use client"

import * as React from "react"
import { useSession, signOut } from "next-auth/react"
import { usePathname } from "next/navigation"
import {
  HomeIcon,
  BuildingOfficeIcon,
  UsersIcon,
  ChartBarIcon,
  CurrencyDollarIcon,
  Cog6ToothIcon,
  ShieldCheckIcon,
  ArrowRightOnRectangleIcon,
} from "@heroicons/react/24/outline"

import { NavUser } from "@/components/nav-user"
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarRail,
  SidebarGroup,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
} from "@/components/ui/sidebar"
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible"
import { ChevronRight } from "lucide-react"
import { SmoothLink } from "@/components/ui/smooth-link"
import { useRTL } from "@/hooks/use-rtl"
import { memo, useState } from "react"

const SuperAdminNavMain = memo(function SuperAdminNavMain({
  items,
}: {
  items: {
    title: string
    url: string
    icon?: any
    isActive?: boolean
    items?: {
      title: string
      url: string
    }[]
  }[]
}) {
  const { isRTL } = useRTL()
  const [openItems, setOpenItems] = useState<Record<string, boolean>>(() => {
    // Initialize with active items open
    const initial: Record<string, boolean> = {}
    items.forEach(item => {
      if (item.isActive && item.items && item.items.length > 0) {
        initial[item.title] = true
      }
    })
    return initial
  })

  const toggleItem = (itemTitle: string) => {
    setOpenItems(prev => ({
      ...prev,
      [itemTitle]: !prev[itemTitle]
    }))
  }

  return (
    <SidebarGroup>
      <SidebarGroupLabel>Super Admin</SidebarGroupLabel>
      <SidebarMenu>
        {items.map((item) => (
          <Collapsible
            key={item.title}
            open={openItems[item.title] || false}
            onOpenChange={(open) => setOpenItems(prev => ({ ...prev, [item.title]: open }))}
            className="group/collapsible"
          >
            <SidebarMenuItem>
              {/* Main navigation item - different behavior for parent vs leaf items */}
              {item.items && item.items.length > 0 ? (
                // Parent item - only toggle, don't navigate
                <SidebarMenuButton
                  tooltip={item.title}
                  className="w-full cursor-pointer"
                  onClick={() => toggleItem(item.title)}
                >
                  {item.icon && <item.icon className="w-4 h-4" />}
                  <span className="flex-1">{item.title}</span>
                  <ChevronRight className={`w-4 h-4 transition-transform duration-200 ${openItems[item.title] ? 'rotate-90' : ''} ${isRTL ? 'rotate-180' : ''}`} />
                </SidebarMenuButton>
              ) : (
                // Leaf item - navigate normally
                <SidebarMenuButton asChild tooltip={item.title}>
                  <SmoothLink href={item.url} className="flex items-center w-full">
                    {item.icon && <item.icon className="w-4 h-4" />}
                    <span className="flex-1">{item.title}</span>
                  </SmoothLink>
                </SidebarMenuButton>
              )}
              {item.items && item.items.length > 0 && (
                <CollapsibleContent>
                  <SidebarMenuSub>
                    {item.items.map((subItem) => (
                      <SidebarMenuSubItem key={subItem.title}>
                        <SidebarMenuSubButton asChild>
                          <SmoothLink href={subItem.url}>
                            <span>{subItem.title}</span>
                          </SmoothLink>
                        </SidebarMenuSubButton>
                      </SidebarMenuSubItem>
                    ))}
                  </SidebarMenuSub>
                </CollapsibleContent>
              )}
            </SidebarMenuItem>
          </Collapsible>
        ))}
      </SidebarMenu>
    </SidebarGroup>
  )
})

export function SuperAdminSidebar({ side = "left", ...props }: React.ComponentProps<typeof Sidebar>) {
  const { data: session } = useSession()
  const pathname = usePathname()

  // Get user data from session
  const user = session?.user

  // Super Admin user data
  const userData = {
    name: user?.name || "Super Admin",
    email: user?.email || "<EMAIL>",
    avatar: user?.avatarUrl || user?.image || "/avatars/admin.jpg",
  }

  // Super Admin navigation data
  const navMainData = [
    {
      title: "Dashboard",
      url: "/admin/dashboard",
      icon: HomeIcon,
      isActive: pathname === "/admin/dashboard" || pathname.startsWith("/admin/database-performance"),
      items: [
        {
          title: "Overview",
          url: "/admin/dashboard",
        },
        {
          title: "System Health",
          url: "/admin/dashboard/health",
        },
        {
          title: "Performance",
          url: "/admin/dashboard/performance",
        },
        {
          title: "Database Performance",
          url: "/admin/database-performance",
        },
      ],
    },
    {
      title: "Tenant Management",
      url: "/admin/tenants",
      icon: BuildingOfficeIcon,
      isActive: pathname.startsWith("/admin/tenants"),
      items: [
        {
          title: "All Tenants",
          url: "/admin/tenants",
        },
        {
          title: "Add Tenant",
          url: "/admin/tenants/new",
        },
        {
          title: "Tenant Analytics",
          url: "/admin/tenants/analytics",
        },
      ],
    },
    {
      title: "User Management",
      url: "/admin/users",
      icon: UsersIcon,
      isActive: pathname.startsWith("/admin/users"),
      items: [
        {
          title: "All Users",
          url: "/admin/users",
        },
        {
          title: "Platform Admins",
          url: "/admin/users/admins",
        },
        {
          title: "User Activity",
          url: "/admin/users/activity",
        },
      ],
    },
    {
      title: "Analytics",
      url: "/admin/analytics",
      icon: ChartBarIcon,
      isActive: pathname.startsWith("/admin/analytics"),
      items: [
        {
          title: "Platform Overview",
          url: "/admin/analytics",
        },
        {
          title: "Usage Metrics",
          url: "/admin/analytics/usage",
        },
        {
          title: "Performance",
          url: "/admin/analytics/performance",
        },
      ],
    },
    {
      title: "Billing & Revenue",
      url: "/admin/billing",
      icon: CurrencyDollarIcon,
      isActive: pathname.startsWith("/admin/billing"),
      items: [
        {
          title: "Revenue Overview",
          url: "/admin/billing",
        },
        {
          title: "Subscription Plans",
          url: "/admin/billing/plans",
        },
        {
          title: "Payment Issues",
          url: "/admin/billing/issues",
        },
      ],
    },
    {
      title: "System Settings",
      url: "/admin/settings",
      icon: Cog6ToothIcon,
      isActive: pathname.startsWith("/admin/settings"),
      items: [
        {
          title: "Feature Flags",
          url: "/admin/settings/features",
        },
        {
          title: "System Config",
          url: "/admin/settings/config",
        },
        {
          title: "Maintenance",
          url: "/admin/settings/maintenance",
        },
      ],
    },
  ]

  const handleSignOut = async () => {
    await signOut({ callbackUrl: '/auth/signin' });
  };

  return (
    <Sidebar collapsible="icon" side={side} {...props}>
      <SidebarHeader>
        <div className="flex items-center gap-2 px-4 py-2">
          <div className="flex aspect-square size-8 items-center justify-center rounded-lg bg-primary text-primary-foreground">
            <ShieldCheckIcon className="size-4" />
          </div>
          <div className="grid flex-1 text-left text-sm leading-tight">
            <span className="truncate font-semibold">Super Admin</span>
            <span className="truncate text-xs text-muted-foreground">Platform Control</span>
          </div>
        </div>
      </SidebarHeader>
      <SidebarContent>
        <SuperAdminNavMain items={navMainData} />
      </SidebarContent>
      <SidebarFooter>
        <NavUser user={userData} />
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  )
}
