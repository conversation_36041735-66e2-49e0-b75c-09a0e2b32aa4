import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { z } from 'zod';
import { validateMermaidSyntax, sanitizeMermaidCode, normalizeMermaidTheme } from '@/lib/mermaid-fallback';

// Request validation schema
const renderMermaidSchema = z.object({
  code: z.string().min(1, 'Diagram code is required'),
  theme: z.enum(['default', 'dark', 'forest', 'neutral']).default('default'),
  backgroundColor: z.string().optional(),
  width: z.number().min(200).max(2000).default(800),
  height: z.number().min(200).max(2000).default(600),
});

/**
 * POST /api/mermaid/render
 * Server-side Mermaid diagram rendering fallback
 */
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json().catch(() => ({}));
    
    // Validate request data
    const validatedData = renderMermaidSchema.parse(body);
    const { code, theme, backgroundColor, width, height } = validatedData;

    // Sanitize the Mermaid code
    const sanitizedCode = sanitizeMermaidCode(code);
    
    // Validate syntax
    const validation = validateMermaidSyntax(sanitizedCode);
    if (!validation.valid) {
      return NextResponse.json(
        { error: validation.error },
        { status: 400 }
      );
    }

    // Server-side Mermaid rendering is not supported due to DOM dependencies
    // Return an informative error that suggests client-side rendering
    return NextResponse.json({
      success: false,
      error: 'Server-side rendering not available',
      details: 'Mermaid requires a browser environment. Please use client-side rendering.',
      suggestion: 'Use the MermaidDiagram component for client-side rendering',
      fallback: true
    }, { status: 501 }); // 501 Not Implemented

  } catch (error) {
    console.error('Error in Mermaid render API:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          error: 'Invalid request data', 
          details: error.errors.map(e => ({
            field: e.path.join('.'),
            message: e.message
          }))
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * GET /api/mermaid/render
 * Get information about the Mermaid rendering service
 */
export async function GET() {
  return NextResponse.json({
    service: 'Mermaid Server-side Rendering',
    version: '1.0.0',
    supportedThemes: ['default', 'dark', 'forest', 'neutral'],
    maxWidth: 2000,
    maxHeight: 2000,
    features: [
      'Server-side rendering fallback',
      'Syntax validation',
      'Code sanitization',
      'Multiple themes support'
    ]
  });
}
