import { NextApiRequest, NextApiResponse } from 'next';
import { CalendarEventService } from '../../../../services/communication';
import { withTenant, TenantApiRequest } from '../../../../lib/middleware/withTenant';
import { withRateLimit } from '../../../../lib/middleware/withRateLimit';

async function handler(req: TenantApiRequest, res: NextApiResponse) {
  // The withTenant middleware already handles authentication and tenant context
  const tenantId = req.tenantId;
  const userId = req.userId;

  try {
    switch (req.method) {
      case 'GET':
        const { startDate, endDate, eventType, relatedToType, relatedToId } = req.query;
        const events = await CalendarEventService.getEvents(
          tenantId,
          userId,
          {
            startDate: startDate ? new Date(startDate as string) : undefined,
            endDate: endDate ? new Date(endDate as string) : undefined,
            eventType: eventType as string,
            relatedToType: relatedToType as string,
            relatedToId: relatedToId as string,
          }
        );
        return res.status(200).json(events);

      case 'POST':
        const newEvent = await CalendarEventService.createEvent(
          tenantId,
          userId,
          req.body
        );
        return res.status(201).json(newEvent);

      default:
        res.setHeader('Allow', ['GET', 'POST']);
        return res.status(405).json({ error: `Method ${req.method} not allowed` });
    }
  } catch (error) {
    console.error('Calendar events API error:', error);
    return res.status(500).json({ 
      error: error instanceof Error ? error.message : 'Internal server error' 
    });
  }
}

export default withRateLimit(withTenant(handler));
