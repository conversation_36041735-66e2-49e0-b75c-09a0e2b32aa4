import { z } from 'zod';
import { prisma } from '@/lib/prisma';
import { Decimal } from '@prisma/client/runtime/library';

// Helper function to parse date values (supports Date objects, strings in YYYY-MM-DD and ISO datetime formats)
const dateSchema = z.union([
  z.string(),
  z.date(),
  z.undefined(),
  z.null()
]).transform((val, ctx) => {
  if (!val || val === null) return undefined;

  // If it's already a Date object, return it
  if (val instanceof Date) {
    if (isNaN(val.getTime())) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Invalid date object',
      });
      return z.NEVER;
    }
    return val;
  }

  // If it's a string, try to parse it
  if (typeof val === 'string') {
    // Handle ISO datetime strings (e.g., "2024-01-15T10:30:00.000Z")
    if (val.includes('T') || val.includes('Z')) {
      const date = new Date(val);
      if (isNaN(date.getTime())) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Invalid ISO datetime string',
        });
        return z.NEVER;
      }
      return date;
    }

    // Handle YYYY-MM-DD format
    if (/^\d{4}-\d{2}-\d{2}$/.test(val)) {
      const date = new Date(`${val}T00:00:00.000Z`);
      if (isNaN(date.getTime())) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Invalid date string format',
        });
        return z.NEVER;
      }
      return date;
    }

    // Try parsing as a general date string
    const date = new Date(val);
    if (isNaN(date.getTime())) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Invalid date string',
      });
      return z.NEVER;
    }
    return date;
  }

  ctx.addIssue({
    code: z.ZodIssueCode.custom,
    message: 'Expected date or date string',
  });
  return z.NEVER;
}).optional();

// Required date schema for milestone creation
const requiredDateSchema = z.union([
  z.string(),
  z.date()
]).transform((val, ctx) => {
  if (!val) {
    ctx.addIssue({
      code: z.ZodIssueCode.custom,
      message: 'Date is required',
    });
    return z.NEVER;
  }

  // If it's already a Date object, return it
  if (val instanceof Date) {
    if (isNaN(val.getTime())) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Invalid date object',
      });
      return z.NEVER;
    }
    return val;
  }

  // If it's a string, try to parse it
  if (typeof val === 'string') {
    // Handle ISO datetime strings (e.g., "2024-01-15T10:30:00.000Z")
    if (val.includes('T') || val.includes('Z')) {
      const date = new Date(val);
      if (isNaN(date.getTime())) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Invalid ISO datetime string',
        });
        return z.NEVER;
      }
      return date;
    }

    // Handle YYYY-MM-DD format
    if (/^\d{4}-\d{2}-\d{2}$/.test(val)) {
      const date = new Date(`${val}T00:00:00.000Z`);
      if (isNaN(date.getTime())) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Invalid date string format',
        });
        return z.NEVER;
      }
      return date;
    }

    // Try parsing as a general date string
    const date = new Date(val);
    if (isNaN(date.getTime())) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Invalid date string',
      });
      return z.NEVER;
    }
    return date;
  }

  ctx.addIssue({
    code: z.ZodIssueCode.custom,
    message: 'Expected date or date string',
  });
  return z.NEVER;
});

// Validation schemas
export const createMilestoneSchema = z.object({
  title: z.string().min(1, 'Milestone title is required').max(255),
  description: z.string().optional(),
  dueDate: requiredDateSchema,
  status: z.enum(['pending', 'in_progress', 'completed', 'overdue']).default('pending'),
  dependsOnTasks: z.array(z.string()).default([]),
});

export const updateMilestoneSchema = z.object({
  title: z.string().min(1, 'Milestone title is required').max(255).optional(),
  description: z.string().optional(),
  dueDate: dateSchema,
  status: z.enum(['pending', 'in_progress', 'completed', 'overdue']).optional(),
  dependsOnTasks: z.array(z.string()).optional(),
});

// Types
export type CreateMilestoneData = z.infer<typeof createMilestoneSchema>;
export type UpdateMilestoneData = z.infer<typeof updateMilestoneSchema>;

export interface MilestoneWithDetails {
  id: string;
  title: string;
  description?: string;
  dueDate: Date;
  status: string;
  progress: Decimal;
  dependsOnTasks: any; // JSON array of task IDs
  completedAt?: Date;
  createdAt: Date;
  updatedAt: Date;
  project?: {
    id: string;
    name: string;
    status: string;
  };
  createdBy?: {
    id: string;
    firstName?: string;
    lastName?: string;
    email: string;
  };
  tasks?: Array<{
    id: string;
    title: string;
    status: string;
    priority: string;
  }>;
}

export class MilestoneService {
  private readonly includeOptions = {
    project: {
      select: {
        id: true,
        name: true,
        status: true,
      },
    },
    createdBy: {
      select: {
        id: true,
        firstName: true,
        lastName: true,
        email: true,
      },
    },
    tasks: {
      select: {
        id: true,
        title: true,
        status: true,
        priority: true,
      },
      orderBy: {
        createdAt: 'asc' as const,
      },
    },
  };

  /**
   * Get all milestones for a project with pagination and filtering
   */
  async getProjectMilestones(
    projectId: string,
    tenantId: string,
    options: {
      page?: number;
      limit?: number;
      status?: string;
      search?: string;
    } = {}
  ): Promise<{
    milestones: MilestoneWithDetails[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  }> {
    const {
      page = 1,
      limit = 20,
      status,
      search,
    } = options;

    const skip = (page - 1) * limit;

    // Build where clause
    const where: any = {
      projectId,
      tenantId,
    };

    if (status) {
      where.status = status;
    }

    if (search) {
      where.OR = [
        { title: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } },
      ];
    }

    // Get total count
    const total = await prisma.projectMilestone.count({ where });

    // Get milestones
    const milestones = await prisma.projectMilestone.findMany({
      where,
      include: this.includeOptions,
      orderBy: [
        { dueDate: 'asc' },
        { createdAt: 'desc' },
      ],
      skip,
      take: limit,
    });

    const totalPages = Math.ceil(total / limit);

    return {
      milestones: milestones as MilestoneWithDetails[],
      total,
      page,
      limit,
      totalPages,
    };
  }

  /**
   * Get a single milestone by ID
   */
  async getMilestoneById(
    milestoneId: string,
    tenantId: string
  ): Promise<MilestoneWithDetails | null> {
    const milestone = await prisma.projectMilestone.findFirst({
      where: {
        id: milestoneId,
        tenantId,
      },
      include: this.includeOptions,
    });

    return milestone as MilestoneWithDetails | null;
  }

  /**
   * Create a new milestone
   */
  async createMilestone(
    projectId: string,
    tenantId: string,
    data: CreateMilestoneData,
    createdBy: string
  ): Promise<MilestoneWithDetails> {
    const validatedData = createMilestoneSchema.parse(data);

    return await prisma.$transaction(async (tx) => {
      // Verify project exists and belongs to tenant
      const project = await tx.project.findFirst({
        where: {
          id: projectId,
          tenantId,
        },
      });

      if (!project) {
        throw new Error('Project not found');
      }

      // Create the milestone
      const milestone = await tx.projectMilestone.create({
        data: {
          tenantId,
          projectId,
          title: validatedData.title,
          description: validatedData.description,
          dueDate: validatedData.dueDate,
          status: validatedData.status,
          dependsOnTasks: validatedData.dependsOnTasks,
          progress: new Decimal(0),
          createdById: createdBy,
        },
        include: this.includeOptions,
      });

      return milestone as MilestoneWithDetails;
    });
  }

  /**
   * Update a milestone
   */
  async updateMilestone(
    milestoneId: string,
    tenantId: string,
    data: UpdateMilestoneData,
    updatedBy: string
  ): Promise<MilestoneWithDetails> {
    const validatedData = updateMilestoneSchema.parse(data);

    return await prisma.$transaction(async (tx) => {
      // Verify milestone exists and belongs to tenant
      const existingMilestone = await tx.projectMilestone.findFirst({
        where: {
          id: milestoneId,
          tenantId,
        },
      });

      if (!existingMilestone) {
        throw new Error('Milestone not found');
      }

      // Update the milestone
      const milestone = await tx.projectMilestone.update({
        where: {
          id: milestoneId,
        },
        data: {
          ...validatedData,
          updatedAt: new Date(),
        },
        include: this.includeOptions,
      });

      return milestone as MilestoneWithDetails;
    });
  }

  /**
   * Delete a milestone
   */
  async deleteMilestone(milestoneId: string, tenantId: string): Promise<void> {
    await prisma.$transaction(async (tx) => {
      // Verify milestone exists and belongs to tenant
      const milestone = await tx.projectMilestone.findFirst({
        where: {
          id: milestoneId,
          tenantId,
        },
      });

      if (!milestone) {
        throw new Error('Milestone not found');
      }

      // Check if milestone has tasks
      const tasks = await tx.projectTask.findMany({
        where: {
          milestoneId: milestoneId,
          tenantId,
        },
      });

      if (tasks.length > 0) {
        // Move tasks to no milestone instead of preventing deletion
        await tx.projectTask.updateMany({
          where: {
            milestoneId: milestoneId,
            tenantId,
          },
          data: {
            milestoneId: null,
          },
        });
      }

      // Delete the milestone
      await tx.projectMilestone.delete({
        where: {
          id: milestoneId,
        },
      });
    });
  }

  /**
   * Update milestone progress based on task completion
   */
  async updateMilestoneProgress(
    milestoneId: string,
    tenantId: string
  ): Promise<void> {
    await prisma.$transaction(async (tx) => {
      // Get all tasks for the milestone
      const tasks = await tx.projectTask.findMany({
        where: {
          milestoneId,
          tenantId,
        },
        select: {
          status: true,
        },
      });

      if (tasks.length === 0) {
        return; // No tasks, keep current progress
      }

      // Calculate progress based on completed tasks
      const completedTasks = tasks.filter(task => task.status === 'done').length;
      const progress = (completedTasks / tasks.length) * 100;

      // Update milestone progress
      await tx.projectMilestone.update({
        where: {
          id: milestoneId,
        },
        data: {
          progress: new Decimal(progress),
        },
      });
    });
  }
}
