'use client';

import React, { useEffect, useRef, useState, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import {
  Download,
  Copy,
  RefreshCw,
  AlertCircle,
  Wrench,
  RotateCcw,
  ZoomIn,
  ZoomOut,
  Maximize2,
  Move,
  RotateCcw as ResetIcon
} from 'lucide-react';
import { toast } from 'sonner';
import {
  validateMermaidSyntax,
  sanitizeMermaidCode,
  parseMermaidError,
  applyAggressiveSanitization,
  type MermaidErrorInfo
} from '@/lib/mermaid-fallback';

interface MermaidDiagramProps {
  code: string;
  title?: string;
  theme?: 'default' | 'dark' | 'forest' | 'neutral';
  className?: string;
  showControls?: boolean;
  onError?: (error: string) => void;
  onSuccess?: () => void;
  // Chart regeneration props
  chartId?: string;
  proposalId?: string;
  onChartRegenerated?: (newCode: string) => void;
  showFixButton?: boolean;
  showRegenerateButton?: boolean;
  // Auto-rendering and interaction props
  autoRender?: boolean;
  showZoomControls?: boolean;
  showFullscreenButton?: boolean;
  enablePanZoom?: boolean;
  maxZoom?: number;
  minZoom?: number;
}

// Global Mermaid instance management
let mermaidInstance: any = null;
let mermaidInitialized = false;
let initializationPromise: Promise<any> | null = null;

const initializeMermaid = async (theme: string = 'default') => {
  if (mermaidInitialized && mermaidInstance) {
    return mermaidInstance;
  }

  if (initializationPromise) {
    return initializationPromise;
  }

  initializationPromise = (async () => {
    try {
      // Dynamic import of mermaid for client-side usage
      const mermaid = (await import('mermaid')).default;

      mermaid.initialize({
        startOnLoad: false,
        theme: theme as any,
        securityLevel: 'loose',
        fontFamily: 'Arial, sans-serif',
        fontSize: 14,
        // Enhanced configuration to prevent edge clipping
        flowchart: {
          useMaxWidth: false, // Allow natural width to prevent clipping
          htmlLabels: true,
          curve: 'basis',
          padding: 20, // Add padding around the diagram
          nodeSpacing: 50,
          rankSpacing: 50
        },
        sequence: {
          useMaxWidth: false,
          wrap: true
        },
        gantt: {
          useMaxWidth: false,
          leftPadding: 100,
          gridLineStartPadding: 350
        },
        journey: {
          useMaxWidth: false
        },
        timeline: {
          useMaxWidth: false
        }
      });

      mermaidInstance = mermaid;
      mermaidInitialized = true;
      return mermaid;
    } catch (err) {
      initializationPromise = null;
      throw err;
    }
  })();

  return initializationPromise;
};

export function MermaidDiagram({
  code,
  title,
  theme = 'default',
  className = '',
  showControls = true,
  onError,
  onSuccess,
  chartId,
  proposalId,
  onChartRegenerated,
  showFixButton = true,
  showRegenerateButton = true,
  autoRender = true,
  showZoomControls = true,
  showFullscreenButton = true,
  enablePanZoom = true,
  maxZoom = 3,
  minZoom = 0.1
}: MermaidDiagramProps) {
  const containerRef = useRef<HTMLDivElement>(null);
  const svgRef = useRef<SVGSVGElement | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [errorInfo, setErrorInfo] = useState<MermaidErrorInfo | null>(null);
  const [renderKey, setRenderKey] = useState(0);
  const [isRegenerating, setIsRegenerating] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const renderTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Zoom and pan state
  const [zoom, setZoom] = useState(1);
  const [panX, setPanX] = useState(0);
  const [panY, setPanY] = useState(0);
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
  const [lastPan, setLastPan] = useState({ x: 0, y: 0 });

  // Zoom and pan control functions
  const handleZoomIn = useCallback(() => {
    setZoom(prev => Math.min(prev * 1.2, maxZoom));
  }, [maxZoom]);

  const handleZoomOut = useCallback(() => {
    setZoom(prev => Math.max(prev / 1.2, minZoom));
  }, [minZoom]);

  const handleResetZoom = useCallback(() => {
    setZoom(1);
    setPanX(0);
    setPanY(0);
  }, []);

  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    if (!enablePanZoom) return;
    setIsDragging(true);
    setDragStart({ x: e.clientX, y: e.clientY });
    setLastPan({ x: panX, y: panY });
  }, [enablePanZoom, panX, panY]);

  const handleMouseMove = useCallback((e: React.MouseEvent) => {
    if (!isDragging || !enablePanZoom) return;
    const deltaX = e.clientX - dragStart.x;
    const deltaY = e.clientY - dragStart.y;
    setPanX(lastPan.x + deltaX);
    setPanY(lastPan.y + deltaY);
  }, [isDragging, enablePanZoom, dragStart, lastPan]);

  const handleMouseUp = useCallback(() => {
    setIsDragging(false);
  }, []);

  const handleWheel = useCallback((e: React.WheelEvent) => {
    if (!enablePanZoom) return;
    e.preventDefault();
    const delta = e.deltaY > 0 ? 0.9 : 1.1;
    setZoom(prev => Math.min(Math.max(prev * delta, minZoom), maxZoom));
  }, [enablePanZoom, minZoom, maxZoom]);

  // Utility function to fix SVG edge clipping
  const fixSvgClipping = useCallback((svgElement: SVGSVGElement) => {
    try {
      // Get the bounding box of all SVG content
      const bbox = svgElement.getBBox();

      // Add generous padding to prevent any clipping
      const padding = 30;
      const newX = bbox.x - padding;
      const newY = bbox.y - padding;
      const newWidth = bbox.width + (padding * 2);
      const newHeight = bbox.height + (padding * 2);

      // Update viewBox with the new dimensions
      const newViewBox = `${newX} ${newY} ${newWidth} ${newHeight}`;
      svgElement.setAttribute('viewBox', newViewBox);

      // Ensure preserveAspectRatio is set correctly
      svgElement.setAttribute('preserveAspectRatio', 'xMidYMid meet');

      // Set overflow to visible to prevent any clipping
      svgElement.style.overflow = 'visible';

      console.log('Fixed SVG clipping:', {
        originalBBox: bbox,
        newViewBox,
        padding
      });

    } catch (error) {
      console.warn('Could not fix SVG clipping:', error);
      // Fallback: just ensure overflow is visible
      svgElement.style.overflow = 'visible';
    }
  }, []);

  // Update SVG transform when zoom/pan changes
  useEffect(() => {
    if (svgRef.current && enablePanZoom) {
      svgRef.current.style.transform = `translate(${panX}px, ${panY}px) scale(${zoom})`;
      svgRef.current.style.transformOrigin = 'center center';
      svgRef.current.style.transition = isDragging ? 'none' : 'transform 0.1s ease-out';
    }
  }, [zoom, panX, panY, isDragging, enablePanZoom]);

  // Render diagram with improved error handling and timeout
  const renderDiagram = useCallback(async () => {
    if (!code || !containerRef.current) {
      setIsLoading(false);
      return;
    }

    setIsLoading(true);
    setError(null);

    // Clear any existing timeout
    if (renderTimeoutRef.current) {
      clearTimeout(renderTimeoutRef.current);
    }

    // Set a timeout to prevent infinite loading
    renderTimeoutRef.current = setTimeout(() => {
      setError('Diagram rendering timed out. Please try refreshing.');
      setIsLoading(false);
      onError?.('Diagram rendering timed out');
    }, 15000); // 15 second timeout (increased for complex diagrams)

    try {
      // Initialize Mermaid if not already done
      const mermaid = await initializeMermaid(theme);

      // Clear previous content
      if (containerRef.current) {
        containerRef.current.innerHTML = '';
      }

      // Sanitize the code first to fix common issues
      const sanitizedCode = sanitizeMermaidCode(code);

      // Validate the sanitized code
      const validation = validateMermaidSyntax(sanitizedCode);

      // Only throw errors for critical structural issues that can't be fixed
      if (!validation.valid && validation.errorType === 'structure') {
        const errorInfo = parseMermaidError(new Error(validation.error || 'Invalid diagram syntax'));
        setErrorInfo(errorInfo);
        setError(errorInfo.message);
        setIsLoading(false);
        onError?.(errorInfo.message);
        return;
      }

      // For fixable issues, show a warning but continue
      if (!validation.valid && validation.fixable) {
        console.warn('Mermaid syntax issues detected, attempting to fix:', validation.error);
        // Don't show toast for every render attempt, just log it
      }

      // Generate unique ID for this diagram
      const elementId = `mermaid-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;

      // Render the diagram with progressive fallback
      let renderResult: any;
      let codeToRender = sanitizedCode;

      try {
        renderResult = await Promise.race([
          mermaid.render(elementId, codeToRender),
          new Promise((_, reject) =>
            setTimeout(() => reject(new Error('Render operation timed out')), 12000)
          )
        ]) as any;
      } catch (renderError) {
        // If rendering fails, try with more aggressive sanitization
        console.warn('Initial render failed, trying with aggressive sanitization:', renderError);

        // Apply more aggressive sanitization
        codeToRender = applyAggressiveSanitization(sanitizedCode);

        try {
          renderResult = await Promise.race([
            mermaid.render(`${elementId}-fallback`, codeToRender),
            new Promise((_, reject) =>
              setTimeout(() => reject(new Error('Fallback render operation timed out')), 8000)
            )
          ]) as any;

          // Show info that fallback was used
          toast.info('Diagram rendered with simplified syntax');
        } catch (fallbackError) {
          // If fallback also fails, throw the original error
          throw renderError;
        }
      }

      if (!renderResult || !renderResult.svg) {
        throw new Error('Failed to generate SVG from diagram code');
      }

      // Insert the SVG into the container
      if (containerRef.current) {
        containerRef.current.innerHTML = renderResult.svg;

        // Add responsive styling to the SVG and fix edge clipping
        const svgElement = containerRef.current.querySelector('svg') as SVGSVGElement;
        if (svgElement) {
          svgRef.current = svgElement;

          // Use a timeout to ensure the SVG is fully rendered before fixing clipping
          setTimeout(() => {
            fixSvgClipping(svgElement);
          }, 100);

          // Ensure SVG has proper dimensions
          if (!svgElement.getAttribute('width') || !svgElement.getAttribute('height')) {
            svgElement.setAttribute('width', '100%');
            svgElement.setAttribute('height', 'auto');
          }

          // Apply responsive styling with enhanced clipping prevention
          svgElement.style.maxWidth = '100%';
          svgElement.style.height = 'auto';
          svgElement.style.display = 'block';
          svgElement.style.margin = '0 auto';
          svgElement.style.cursor = enablePanZoom ? 'grab' : 'default';
          svgElement.style.overflow = 'visible'; // Ensure content is not clipped

          // Additional styles to prevent edge clipping
          svgElement.style.shapeRendering = 'geometricPrecision';
          svgElement.style.textRendering = 'geometricPrecision';

          // Ensure all child elements are also visible
          const allElements = svgElement.querySelectorAll('*');
          allElements.forEach((element: any) => {
            if (element.style) {
              element.style.overflow = 'visible';
            }
          });

          // Apply zoom and pan transformations
          if (enablePanZoom) {
            svgElement.style.transform = `translate(${panX}px, ${panY}px) scale(${zoom})`;
            svgElement.style.transformOrigin = 'center center';
            svgElement.style.transition = isDragging ? 'none' : 'transform 0.1s ease-out';
          }
        }

        // Bind any interactive functions if available
        if (renderResult.bindFunctions) {
          renderResult.bindFunctions(containerRef.current);
        }
      }

      // Clear timeout on success
      if (renderTimeoutRef.current) {
        clearTimeout(renderTimeoutRef.current);
        renderTimeoutRef.current = null;
      }

      setIsLoading(false);
      onSuccess?.();

    } catch (err) {
      console.error('Client-side Mermaid rendering failed:', err);

      // Clear timeout on error
      if (renderTimeoutRef.current) {
        clearTimeout(renderTimeoutRef.current);
        renderTimeoutRef.current = null;
      }

      // Parse error using enhanced error analysis
      const parsedError = parseMermaidError(err as Error);
      setErrorInfo(parsedError);
      setError(parsedError.message);
      setIsLoading(false);
      onError?.(parsedError.message);
    }
  }, [code, theme, onError, onSuccess]);

  // Effect to trigger rendering when code or theme changes (if autoRender is enabled)
  useEffect(() => {
    if (autoRender) {
      renderDiagram();
    }
  }, [renderDiagram, renderKey, autoRender]);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (renderTimeoutRef.current) {
        clearTimeout(renderTimeoutRef.current);
      }
    };
  }, []);

  const handleCopyCode = async () => {
    try {
      await navigator.clipboard.writeText(code);
      toast.success('Mermaid code copied to clipboard');
    } catch (err) {
      toast.error('Failed to copy code');
    }
  };

  const handleDownloadPng = () => {
    if (!containerRef.current) return;

    const svgElement = containerRef.current.querySelector('svg');
    if (!svgElement) {
      toast.error('No diagram to download');
      return;
    }

    try {
      // Get SVG dimensions
      const svgRect = svgElement.getBoundingClientRect();
      const svgWidth = svgElement.viewBox?.baseVal?.width || svgRect.width || 800;
      const svgHeight = svgElement.viewBox?.baseVal?.height || svgRect.height || 600;

      // Create canvas
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      if (!ctx) {
        throw new Error('Could not get canvas context');
      }

      // Set canvas size with higher resolution for better quality
      const scale = 2;
      canvas.width = svgWidth * scale;
      canvas.height = svgHeight * scale;
      ctx.scale(scale, scale);

      // Set white background
      ctx.fillStyle = 'white';
      ctx.fillRect(0, 0, svgWidth, svgHeight);

      // Convert SVG to data URL
      const svgData = new XMLSerializer().serializeToString(svgElement);
      const svgBlob = new Blob([svgData], { type: 'image/svg+xml;charset=utf-8' });
      const svgUrl = URL.createObjectURL(svgBlob);

      // Create image and draw to canvas
      const img = new Image();
      img.onload = () => {
        ctx.drawImage(img, 0, 0, svgWidth, svgHeight);

        // Convert canvas to PNG blob
        canvas.toBlob((blob) => {
          if (!blob) {
            toast.error('Failed to create PNG image');
            return;
          }

          // Create download link
          const url = URL.createObjectURL(blob);
          const link = document.createElement('a');
          link.href = url;
          link.download = `${title || 'mermaid-diagram'}.png`;
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          URL.revokeObjectURL(url);
          URL.revokeObjectURL(svgUrl);

          toast.success('Diagram downloaded successfully');
        }, 'image/png', 0.95);
      };

      img.onerror = () => {
        URL.revokeObjectURL(svgUrl);
        toast.error('Failed to convert diagram to PNG');
      };

      img.src = svgUrl;
    } catch (err) {
      console.error('Error downloading PNG:', err);
      toast.error('Failed to download diagram');
    }
  };

  const handleRefresh = useCallback(() => {
    if (containerRef.current) {
      containerRef.current.innerHTML = '';
    }
    setError(null);
    setErrorInfo(null);
    setIsLoading(true);

    // Clear any existing timeout
    if (renderTimeoutRef.current) {
      clearTimeout(renderTimeoutRef.current);
      renderTimeoutRef.current = null;
    }

    // Trigger re-render by updating the render key
    setRenderKey(prev => prev + 1);
  }, []);

  const handleFixChart = useCallback(async () => {
    if (!chartId || !proposalId || !errorInfo) {
      toast.error('Cannot regenerate chart: missing required information');
      return;
    }

    setIsRegenerating(true);
    try {
      const response = await fetch(`/api/proposals/${proposalId}/regenerate-chart`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          chartId,
          errorContext: {
            error: errorInfo.originalError,
            errorType: errorInfo.type,
            suggestions: errorInfo.suggestions,
            line: errorInfo.line,
            column: errorInfo.column
          }
        }),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to regenerate chart');
      }

      // Update the chart with new code
      const newCode = result.data.chart.mermaidCode;
      onChartRegenerated?.(newCode);

      toast.success('Chart regenerated successfully with error fixes');

      // Clear error state and trigger re-render
      setError(null);
      setErrorInfo(null);
      setRenderKey(prev => prev + 1);

    } catch (error) {
      console.error('Error regenerating chart:', error);
      toast.error('Failed to regenerate chart');
    } finally {
      setIsRegenerating(false);
    }
  }, [chartId, proposalId, errorInfo, onChartRegenerated]);

  const handleRegenerateChart = useCallback(async () => {
    if (!chartId || !proposalId) {
      toast.error('Cannot regenerate chart: missing required information');
      return;
    }

    setIsRegenerating(true);
    try {
      const response = await fetch(`/api/proposals/${proposalId}/regenerate-chart`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          chartId,
          customPrompt: 'Regenerate this chart with improved layout and Word document optimization. Focus on better alignment, spacing, and professional presentation suitable for business documents.'
        }),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to regenerate chart');
      }

      // Update the chart with new code
      const newCode = result.data.chart.mermaidCode;
      onChartRegenerated?.(newCode);

      toast.success('Chart regenerated successfully with improved layout');

      // Clear any existing errors and trigger re-render
      setError(null);
      setErrorInfo(null);
      setRenderKey(prev => prev + 1);

    } catch (error) {
      console.error('Error regenerating chart:', error);
      toast.error('Failed to regenerate chart');
    } finally {
      setIsRegenerating(false);
    }
  }, [chartId, proposalId, onChartRegenerated]);

  return (
    <Card className={className}>
      {title && (
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg">{title}</CardTitle>
            {showControls && (
              <div className="flex items-center space-x-2">
                {/* Zoom Controls */}
                {showZoomControls && enablePanZoom && (
                  <>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleZoomIn}
                      disabled={isLoading || !!error}
                      title="Zoom In"
                    >
                      <ZoomIn className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleZoomOut}
                      disabled={isLoading || !!error}
                      title="Zoom Out"
                    >
                      <ZoomOut className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleResetZoom}
                      disabled={isLoading || !!error}
                      title="Reset Zoom"
                    >
                      <ResetIcon className="h-4 w-4" />
                    </Button>
                  </>
                )}

                {/* Fullscreen Button */}
                {showFullscreenButton && (
                  <Dialog open={isFullscreen} onOpenChange={setIsFullscreen}>
                    <DialogTrigger asChild>
                      <Button
                        variant="outline"
                        size="sm"
                        disabled={isLoading || !!error}
                        title="View Fullscreen"
                      >
                        <Maximize2 className="h-4 w-4" />
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="max-w-[95vw] max-h-[95vh] w-full h-full">
                      <DialogHeader>
                        <DialogTitle>{title || 'Mermaid Diagram'}</DialogTitle>
                      </DialogHeader>
                      <div className="flex-1 overflow-hidden">
                        <div
                          className="w-full h-full overflow-auto border rounded-lg bg-white"
                          style={{ minHeight: '60vh' }}
                          onWheel={handleWheel}
                          onMouseDown={handleMouseDown}
                          onMouseMove={handleMouseMove}
                          onMouseUp={handleMouseUp}
                          onMouseLeave={handleMouseUp}
                        >
                          <div
                            className="w-full h-full flex items-center justify-center p-4"
                            dangerouslySetInnerHTML={{
                              __html: containerRef.current?.innerHTML || ''
                            }}
                          />
                        </div>
                      </div>
                    </DialogContent>
                  </Dialog>
                )}

                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleCopyCode}
                  title="Copy Mermaid code"
                >
                  <Copy className="h-4 w-4" />
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleDownloadPng}
                  disabled={isLoading || !!error}
                  title="Download as PNG"
                >
                  <Download className="h-4 w-4" />
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleRefresh}
                  title="Refresh diagram"
                >
                  <RefreshCw className="h-4 w-4" />
                </Button>
                {showRegenerateButton && chartId && proposalId && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleRegenerateChart}
                    disabled={isRegenerating}
                    title="Regenerate chart with improved layout"
                    className="text-blue-600 border-blue-300 hover:bg-blue-50"
                  >
                    {isRegenerating ? (
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                    ) : (
                      <RotateCcw className="h-4 w-4" />
                    )}
                  </Button>
                )}
              </div>
            )}
          </div>
        </CardHeader>
      )}
      <CardContent>
        {error ? (
          <Alert className="border-red-200 bg-red-50">
            <AlertCircle className="h-4 w-4 text-red-600" />
            <AlertDescription className="text-red-800">
              <div className="space-y-3">
                <div>
                  <strong>Rendering Error:</strong> {error}
                </div>

                {errorInfo && (
                  <div className="space-y-2">
                    <div className="text-sm">
                      <strong>Error Type:</strong> {errorInfo.type}
                      {errorInfo.line && <span> (Line {errorInfo.line})</span>}
                      <span className={`ml-2 px-2 py-1 rounded text-xs ${
                        errorInfo.severity === 'high' ? 'bg-red-100 text-red-700' :
                        errorInfo.severity === 'medium' ? 'bg-yellow-100 text-yellow-700' :
                        'bg-blue-100 text-blue-700'
                      }`}>
                        {errorInfo.severity} severity
                      </span>
                    </div>

                    {errorInfo.suggestions && errorInfo.suggestions.length > 0 && (
                      <div className="text-sm">
                        <strong>Suggestions:</strong>
                        <ul className="list-disc list-inside mt-1 space-y-1">
                          {errorInfo.suggestions.map((suggestion, index) => (
                            <li key={index}>{suggestion}</li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>
                )}

                <div className="flex gap-2 flex-wrap">
                  <Button
                    onClick={handleRefresh}
                    size="sm"
                    variant="outline"
                    className="text-red-700 border-red-300 hover:bg-red-100"
                  >
                    <RefreshCw className="h-3 w-3 mr-1" />
                    Retry Rendering
                  </Button>

                  {showFixButton && chartId && proposalId && errorInfo?.fixable && (
                    <Button
                      onClick={handleFixChart}
                      size="sm"
                      variant="outline"
                      disabled={isRegenerating}
                      className="text-blue-700 border-blue-300 hover:bg-blue-100"
                    >
                      {isRegenerating ? (
                        <>
                          <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-blue-600 mr-1"></div>
                          Fixing...
                        </>
                      ) : (
                        <>
                          <Wrench className="h-3 w-3 mr-1" />
                          Fix Chart with AI
                        </>
                      )}
                    </Button>
                  )}
                </div>
              </div>
            </AlertDescription>
          </Alert>
        ) : isLoading ? (
          <div className="flex items-center justify-center py-8">
            <div className="text-center space-y-3">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
              <p className="text-sm text-gray-500">Rendering diagram...</p>
              <p className="text-xs text-gray-400">This may take a few seconds</p>
            </div>
          </div>
        ) : (
          <div className="relative">
            <div
              ref={containerRef}
              className={`mermaid-container ${enablePanZoom ? 'overflow-hidden' : 'overflow-visible'}`}
              style={{
                minHeight: '200px',
                padding: '30px', // Generous padding to prevent edge clipping
                cursor: enablePanZoom ? (isDragging ? 'grabbing' : 'grab') : 'default',
                // Ensure the container doesn't clip content
                boxSizing: 'content-box',
                // Additional CSS to prevent clipping
                position: 'relative',
                width: '100%',
                // Ensure content is not clipped even if SVG is larger
                contain: 'none'
              }}
              onWheel={enablePanZoom ? handleWheel : undefined}
              onMouseDown={enablePanZoom ? handleMouseDown : undefined}
              onMouseMove={enablePanZoom ? handleMouseMove : undefined}
              onMouseUp={enablePanZoom ? handleMouseUp : undefined}
              onMouseLeave={enablePanZoom ? handleMouseUp : undefined}
            />

            {/* Zoom Level Indicator */}
            {enablePanZoom && showZoomControls && zoom !== 1 && (
              <div className="absolute top-2 right-2 bg-black/70 text-white px-2 py-1 rounded text-xs">
                {Math.round(zoom * 100)}%
              </div>
            )}

            {/* Pan/Zoom Instructions */}
            {enablePanZoom && !isLoading && !error && (
              <div className="absolute bottom-2 left-2 bg-black/70 text-white px-2 py-1 rounded text-xs">
                <Move className="h-3 w-3 inline mr-1" />
                Drag to pan • Scroll to zoom
              </div>
            )}
          </div>
        )}
        
        {/* Code preview (optional) */}
        {showControls && !error && (
          <details className="mt-4">
            <summary className="cursor-pointer text-sm text-gray-600 hover:text-gray-800">
              View Mermaid Code
            </summary>
            <pre className="mt-2 p-3 bg-gray-50 rounded-md text-xs overflow-x-auto">
              <code>{code}</code>
            </pre>
          </details>
        )}
      </CardContent>
    </Card>
  );
}

// Hook for using Mermaid in other components
export function useMermaid() {
  const [isLoaded, setIsLoaded] = useState(false);

  useEffect(() => {
    const loadMermaid = async () => {
      try {
        await import('mermaid');
        setIsLoaded(true);
      } catch (error) {
        console.error('Failed to load Mermaid:', error);
      }
    };

    loadMermaid();
  }, []);

  const renderToString = async (code: string, theme: string = 'default'): Promise<string> => {
    if (!isLoaded) {
      throw new Error('Mermaid not loaded');
    }

    const mermaid = (await import('mermaid')).default;
    
    mermaid.initialize({
      startOnLoad: false,
      theme: theme as any,
      securityLevel: 'loose'
    });

    const elementId = `mermaid-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
    const { svg } = await mermaid.render(elementId, code);
    
    return svg;
  };

  return {
    isLoaded,
    renderToString
  };
}
