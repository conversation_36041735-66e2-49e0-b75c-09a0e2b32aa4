import { NextResponse } from 'next/server';
import { withPermission, AuthenticatedRequest } from '@/lib/permission-middleware';
import { scheduledMeetingService, createScheduledMeetingSchema, getScheduledMeetingsQuerySchema } from '@/services/scheduled-meeting-service';
import { PermissionAction, PermissionResource } from '@/types';

/**
 * GET /api/meetings
 * Get scheduled meetings with filtering and pagination
 */
export const GET = withPermission({
  resource: PermissionResource.MEETINGS,
  action: PermissionAction.READ,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest) => {
  try {
    const { searchParams } = new URL(req.url);
    const queryParams = Object.fromEntries(searchParams.entries());
    
    const validatedQuery = getScheduledMeetingsQuerySchema.parse(queryParams);

    const result = await scheduledMeetingService.getScheduledMeetings(req.tenantId, validatedQuery);

    return NextResponse.json({
      success: true,
      data: result,
      message: 'Scheduled meetings retrieved successfully'
    });

  } catch (error) {
    console.error('Get scheduled meetings error:', error);

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
});

/**
 * POST /api/meetings
 * Create a new scheduled meeting
 */
export const POST = withPermission({
  resource: PermissionResource.MEETINGS,
  action: PermissionAction.CREATE,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest) => {
  try {
    const body = await req.json();
    const validatedData = createScheduledMeetingSchema.parse(body);

    const scheduledMeeting = await scheduledMeetingService.createScheduledMeeting(
      req.tenantId,
      validatedData,
      req.userId
    );

    return NextResponse.json({
      success: true,
      data: { scheduledMeeting },
      message: 'Scheduled meeting created successfully'
    }, { status: 201 });

  } catch (error) {
    console.error('Create scheduled meeting error:', error);

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
});

/**
 * PUT /api/meetings
 * Update a scheduled meeting
 */
export const PUT = withPermission({
  resource: PermissionResource.MEETINGS,
  action: PermissionAction.UPDATE,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest) => {
  try {
    const { searchParams } = new URL(req.url);
    const meetingId = searchParams.get('meetingId');

    if (!meetingId) {
      return NextResponse.json(
        { error: 'Meeting ID is required' },
        { status: 400 }
      );
    }

    const body = await req.json();
    
    const scheduledMeeting = await scheduledMeetingService.updateScheduledMeeting(
      meetingId,
      req.tenantId,
      body
    );

    return NextResponse.json({
      success: true,
      data: { scheduledMeeting },
      message: 'Scheduled meeting updated successfully'
    });

  } catch (error) {
    console.error('Update scheduled meeting error:', error);

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
});

/**
 * DELETE /api/meetings
 * Delete a scheduled meeting
 */
export const DELETE = withPermission({
  resource: PermissionResource.MEETINGS,
  action: PermissionAction.DELETE,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest) => {
  try {
    const { searchParams } = new URL(req.url);
    const meetingId = searchParams.get('meetingId');

    if (!meetingId) {
      return NextResponse.json(
        { error: 'Meeting ID is required' },
        { status: 400 }
      );
    }

    await scheduledMeetingService.deleteScheduledMeeting(meetingId, req.tenantId);

    return NextResponse.json({
      success: true,
      message: 'Scheduled meeting deleted successfully'
    });

  } catch (error) {
    console.error('Delete scheduled meeting error:', error);

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
});
