# Database Seeding Guide

This document explains the comprehensive database seeding system for the CRM Platform, including dependency management and Docker integration.

## Overview

The seeding system has been redesigned to handle all database initialization in the correct dependency order, ensuring data consistency and avoiding conflicts.

## Seeding Architecture

### Master Seeding Script

The main orchestrator is `scripts/master-seed.js`, which coordinates all seeding operations:

```bash
node scripts/master-seed.js [options]
```

**Options:**
- `--force` - Force re-seeding even if data exists
- `--sample-data` - Include sample data creation
- `--skip-existing` - Skip seeding if any data already exists

### Dependency Order

The seeding process follows this strict dependency order:

1. **Subscription Plans & Feature Flags** - Foundation for tenant subscriptions
2. **Tenants & Subscriptions** - Create demo tenant with subscription
3. **Users & Tenant-User relationships** - Admin user and tenant connections
4. **Permissions** - Comprehensive permission system
5. **Roles & Role-Permission assignments** - RBAC with permission mappings
6. **Lead Sources & Sales Stages** - CRM-specific data structures
7. **Sample Data** - Optional demo contacts and leads

## Individual Seeding Scripts

### Core Scripts

| Script | Purpose | Dependencies |
|--------|---------|--------------|
| `master-seed.js` | Main orchestrator | None |
| `seed-db.js` | Legacy script (now comprehensive) | None |
| `seed-database.js` | Subscription plans and feature flags | None |
| `seed-permissions.js` | Comprehensive permissions | Tenants |
| `seed-rbac.js` | Roles and permission assignments | Tenants, Permissions |
| `seed-lead-sources.js` | Default lead sources | Tenants |
| `seed-sales-stages.js` | Default sales pipeline stages | Tenants |

### Specialized Scripts

| Script | Purpose | When to Use |
|--------|---------|-------------|
| `create-super-admin.js` | Create platform admin | Manual admin creation |
| `assign-user-role.js` | Assign roles to users | User management |
| `seed-lead-sources.ts` | TypeScript version | Development |
| `migrate-lead-sources.ts` | Data migration | Schema updates |

## Docker Integration

### Automatic Seeding

The Docker setup automatically runs seeding during container startup:

```bash
# Start containers with automatic seeding prompt
scripts/docker-manager.bat start dev

# Manual seeding
scripts/seed-db.bat dev
```

### Docker Compose Integration

The seeding is integrated into the Docker Compose workflow:

```yaml
# docker-compose.yml includes seeding scripts
volumes:
  - ./scripts:/app/scripts:ro
```

### Environment-Specific Seeding

Different environments use different configurations:

- **Development** (`dev`) - Full seeding with sample data
- **Staging** (`staging`) - Production-like data without samples
- **Production** (`prod`) - Minimal required data only

## Usage Examples

### Basic Seeding

```bash
# Run complete seeding process
node scripts/master-seed.js

# Force re-seed with sample data
node scripts/master-seed.js --force --sample-data

# Skip if data exists
node scripts/master-seed.js --skip-existing
```

### Docker Seeding

```bash
# Seed development environment
scripts/seed-db.bat dev

# Seed production environment
scripts/seed-db.bat prod
```

### Individual Module Seeding

```bash
# Seed only permissions
node scripts/seed-permissions.js

# Seed only roles
node scripts/seed-rbac.js

# Seed only lead sources
node scripts/seed-lead-sources.js
```

## Data Structure

### Subscription Plans

Three default plans are created:
- **Starter** ($29/month) - Basic features for small teams
- **Professional** ($99/month) - Advanced features for growing businesses  
- **Enterprise** ($299/month) - Complete solution for large organizations

### Feature Flags

Core feature flags control access to:
- AI Features (Professional+)
- Advanced Reporting (Professional+)
- API Access (Professional+)
- Custom Integrations (Enterprise only)
- Mobile App (All plans)

### Default Tenant

A demo tenant is created with:
- Name: "Demo Company"
- Slug: "demo-company"
- Professional subscription
- Admin user: <EMAIL> (password: Admin123!)

### Permissions System

Comprehensive permissions cover all CRM modules:
- **Resources**: contacts, companies, leads, opportunities, proposals, projects, users, roles, settings, ai_features, reports, calls
- **Actions**: create, read, update, delete, export, manage
- **Specialized**: import, merge, convert, assign, score, close, forecast, send, approve, invite

### Default Roles

Four system roles with appropriate permissions:
- **Tenant Admin** - Full access within tenant
- **Sales Manager** - Lead/opportunity management with team oversight
- **Sales Rep** - Basic CRM functions for individual contributors
- **Viewer** - Read-only access to assigned records

### Lead Sources

Eight default lead sources:
- Website, Google Ads, Social Media, Email Campaign
- Referral, Cold Call, Trade Show, Partner

### Sales Stages

Six default pipeline stages:
- Prospecting (0-20%), Qualification (20-40%), Proposal (40-70%)
- Negotiation (70-90%), Closed Won (100%), Closed Lost (0%)

## Troubleshooting

### Common Issues

1. **Prisma Client Not Generated**
   ```bash
   npx prisma generate
   ```

2. **Database Connection Issues**
   ```bash
   # Check database is running
   docker-compose ps postgres
   
   # Check connection
   docker-compose exec postgres pg_isready -U admin -d crm_platform
   ```

3. **Permission Conflicts**
   ```bash
   # Clear and re-seed permissions
   node scripts/master-seed.js --force
   ```

4. **Duplicate Data Errors**
   ```bash
   # Use skip-existing to avoid conflicts
   node scripts/master-seed.js --skip-existing
   ```

### Logs and Debugging

The seeding scripts provide comprehensive logging:
- ✅ Success indicators
- ❌ Error indicators  
- 📊 Progress summaries
- 🎯 Final statistics

### Reset and Re-seed

To completely reset and re-seed:

```bash
# Reset database
scripts/reset-db.bat dev

# Re-seed with sample data
scripts/seed-db.bat dev
```

## Best Practices

1. **Always run seeding after schema changes**
2. **Use --skip-existing in production**
3. **Include --sample-data only in development**
4. **Monitor seeding logs for errors**
5. **Test seeding in staging before production**
6. **Backup before running --force in production**

## Integration with CI/CD

The seeding system integrates with GitHub Actions:

```yaml
- name: Seed Database
  run: |
    npm run db:generate
    npm run db:migrate
    node scripts/master-seed.js --skip-existing
```

This ensures consistent database state across all environments.
