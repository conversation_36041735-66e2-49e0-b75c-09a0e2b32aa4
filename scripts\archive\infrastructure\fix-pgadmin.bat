@echo off
REM Fix pgAdmin email configuration issue
REM This script resolves the "invalid email address" error

echo 🔧 Fixing pgAdmin email configuration issue...
echo.

REM Check if Docker is running
docker info >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker is not running. Please start Docker and try again.
    pause
    exit /b 1
)

echo 📋 Current issue: pgAdmin shows "invalid email address" error
echo 🔧 Solution: Reset pgAdmin container and volume with correct email
echo.

REM Stop pgAdmin container
echo 🛑 Stopping pgAdmin container...
docker compose stop pgadmin

REM Remove pgAdmin container
echo 🗑️ Removing pgAdmin container...
docker compose rm -f pgadmin

REM Remove pgAdmin volume to clear cached configuration
echo 🗑️ Removing pgAdmin volume (this will clear all pgAdmin settings)...
docker volume rm crm_pgadmin-data 2>nul
docker volume rm crm_pgadmin-dev-data 2>nul

REM Start pgAdmin with fresh configuration
echo 🚀 Starting pgAdmin with correct email configuration...
docker compose up -d pgadmin

REM Wait for pgAdmin to start
echo ⏳ Waiting for pgAdmin to initialize...
timeout /t 15 /nobreak > nul

REM Check if pgAdmin is running
docker compose ps pgadmin | findstr "Up" >nul
if %errorlevel% equ 0 (
    echo ✅ pgAdmin is now running successfully!
    echo.
    echo 🔗 Access pgAdmin at: http://localhost:8082
    echo 🔑 Login credentials:
    echo   Email:    <EMAIL>
    echo   Password: admin
    echo.
    echo 📋 To add database connection in pgAdmin:
    echo   1. Right-click "Servers" ^> "Register" ^> "Server..."
    echo   2. General tab - Name: CRM Database
    echo   3. Connection tab:
    echo      Host: postgres
    echo      Port: 5432
    echo      Database: crm_platform
    echo      Username: admin
    echo      Password: admin
    echo   4. Click "Save"
) else (
    echo ❌ pgAdmin failed to start. Checking logs...
    docker compose logs pgadmin --tail=20
    echo.
    echo 💡 Try running the full restart script:
    echo   scripts\restart-docker.bat
)

echo.
pause
