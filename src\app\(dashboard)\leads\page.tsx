'use client';

import { LeadManagement } from '@/components/leads/lead-management';
import { PermissionGate } from '@/components/ui/permission-gate';
import { PermissionResource, PermissionAction } from '@/types';
import { Card, CardContent } from '@/components/ui/card';
import { EmptyState } from '@/components/ui/empty-state';
import { ExclamationTriangleIcon, PlusIcon, ArrowDownTrayIcon } from '@heroicons/react/24/outline';
import { PageLayout } from '@/components/layout/page-layout';
import { Button } from '@/components/ui/button';
import { useRouter } from 'next/navigation';
import { useSmoothNavigation } from '@/hooks/use-smooth-navigation';

export default function LeadsPage() {
  const router = useRouter();
  const { navigate } = useSmoothNavigation();

  const handleExport = async () => {
    try {
      const response = await fetch('/api/leads/export?format=csv');
      if (response.ok) {
        const blob = await response.blob();
        const filename = `leads-export-${new Date().toISOString().split('T')[0]}.csv`;

        if (typeof window !== 'undefined') {
          const url = window.URL.createObjectURL(blob);
          const a = window.document.createElement('a');
          a.href = url;
          a.download = filename;
          window.document.body.appendChild(a);
          a.click();
          window.URL.revokeObjectURL(url);
          window.document.body.removeChild(a);
        }
      }
    } catch (err) {
      console.error('Export error:', err);
    }
  };

  const actions = (
    <div className="flex items-center gap-2">
      <Button variant="outline" size="sm" onClick={handleExport}>
        <ArrowDownTrayIcon className="h-4 w-4 mr-2" />
        Export
      </Button>
      <PermissionGate
        resource={PermissionResource.LEADS}
        action={PermissionAction.CREATE}
      >
        <Button size="sm" onClick={() => router.push('/leads/new')}>
          <PlusIcon className="h-4 w-4 mr-2" />
          Add Lead
        </Button>
      </PermissionGate>
    </div>
  );

  return (
    <PermissionGate
      resource={PermissionResource.LEADS}
      action={PermissionAction.READ}
      fallback={
        <PageLayout>
          <Card>
            <CardContent className="p-8">
              <EmptyState
                icon={<ExclamationTriangleIcon className="w-12 h-12 text-muted-foreground" />}
                title="Access Denied"
                description="You don't have permission to view leads. Please contact your administrator for access."
                action={{
                  label: 'Back to Dashboard',
                  onClick: () => navigate('/dashboard'),
                  variant: 'primary'
                }}
              />
            </CardContent>
          </Card>
        </PageLayout>
      }
    >
      <PageLayout
        title="Lead Management"
        description="Manage and track your sales leads with AI-powered insights"
        actions={actions}
      >
        <LeadManagement />
      </PageLayout>
    </PermissionGate>
  );
}
