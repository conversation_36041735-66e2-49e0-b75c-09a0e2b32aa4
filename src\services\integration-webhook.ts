import { prisma } from '@/lib/prisma';
import { z } from 'zod';

// Webhook payload schemas for different providers
export const jiraWebhookSchema = z.object({
  webhookEvent: z.string(),
  issue: z.object({
    id: z.string(),
    key: z.string(),
    fields: z.object({
      summary: z.string(),
      description: z.string().optional(),
      status: z.object({
        name: z.string(),
      }),
      priority: z.object({
        name: z.string(),
      }).optional(),
      assignee: z.object({
        emailAddress: z.string(),
      }).optional(),
      updated: z.string(),
    }),
  }),
});

export const asanaWebhookSchema = z.object({
  events: z.array(z.object({
    action: z.string(),
    resource: z.object({
      gid: z.string(),
      name: z.string(),
      notes: z.string().optional(),
      completed: z.boolean().optional(),
      assignee: z.object({
        email: z.string(),
      }).optional(),
      modified_at: z.string(),
    }),
  })),
});

export const trelloWebhookSchema = z.object({
  action: z.object({
    type: z.string(),
    data: z.object({
      card: z.object({
        id: z.string(),
        name: z.string(),
        desc: z.string().optional(),
        closed: z.boolean().optional(),
      }).optional(),
      list: z.object({
        name: z.string(),
      }).optional(),
    }),
  }),
});

// Types
export type JiraWebhookPayload = z.infer<typeof jiraWebhookSchema>;
export type AsanaWebhookPayload = z.infer<typeof asanaWebhookSchema>;
export type TrelloWebhookPayload = z.infer<typeof trelloWebhookSchema>;

export interface WebhookProcessingResult {
  success: boolean;
  message: string;
  updatedTasks?: number;
  updatedProjects?: number;
  errors?: string[];
}

export class IntegrationWebhookService {
  /**
   * Process Jira webhook
   */
  async processJiraWebhook(
    tenantId: string,
    payload: JiraWebhookPayload
  ): Promise<WebhookProcessingResult> {
    try {
      const { webhookEvent, issue } = payload;
      
      // Find project and task by external ID
      const task = await prisma.projectTask.findFirst({
        where: {
          tenantId,
          externalTaskId: issue.id,
        },
        include: {
          project: true,
        },
      });

      if (!task) {
        return {
          success: false,
          message: `Task with external ID ${issue.id} not found`,
        };
      }

      // Map Jira status to our status
      const statusMapping: Record<string, string> = {
        'To Do': 'todo',
        'In Progress': 'in_progress',
        'In Review': 'review',
        'Done': 'done',
        'Closed': 'done',
      };

      // Map Jira priority to our priority
      const priorityMapping: Record<string, string> = {
        'Lowest': 'low',
        'Low': 'low',
        'Medium': 'medium',
        'High': 'high',
        'Highest': 'urgent',
      };

      const updateData: any = {
        title: issue.fields.summary,
        description: issue.fields.description,
        status: statusMapping[issue.fields.status.name] || task.status,
        updatedAt: new Date(),
      };

      if (issue.fields.priority) {
        updateData.priority = priorityMapping[issue.fields.priority.name] || task.priority;
      }

      // Handle completion
      if (updateData.status === 'done' && task.status !== 'done') {
        updateData.completedAt = new Date();
      } else if (updateData.status !== 'done' && task.status === 'done') {
        updateData.completedAt = null;
      }

      // Update task
      await prisma.projectTask.update({
        where: { id: task.id },
        data: updateData,
      });

      // Update project progress if task status changed
      if (updateData.status !== task.status) {
        await this.updateProjectProgress(task.projectId, tenantId);
      }

      return {
        success: true,
        message: `Successfully updated task ${task.title}`,
        updatedTasks: 1,
      };
    } catch (error) {
      console.error('Error processing Jira webhook:', error);
      return {
        success: false,
        message: 'Failed to process Jira webhook',
        errors: [error instanceof Error ? error.message : 'Unknown error'],
      };
    }
  }

  /**
   * Process Asana webhook
   */
  async processAsanaWebhook(
    tenantId: string,
    payload: AsanaWebhookPayload
  ): Promise<WebhookProcessingResult> {
    try {
      const results: WebhookProcessingResult[] = [];

      for (const event of payload.events) {
        const { action, resource } = event;

        // Find task by external ID
        const task = await prisma.projectTask.findFirst({
          where: {
            tenantId,
            externalTaskId: resource.gid,
          },
          include: {
            project: true,
          },
        });

        if (!task) {
          results.push({
            success: false,
            message: `Task with external ID ${resource.gid} not found`,
          });
          continue;
        }

        const updateData: any = {
          title: resource.name,
          description: resource.notes,
          updatedAt: new Date(resource.modified_at),
        };

        // Handle completion status
        if (resource.completed !== undefined) {
          updateData.status = resource.completed ? 'done' : 'todo';
          
          if (resource.completed && task.status !== 'done') {
            updateData.completedAt = new Date();
          } else if (!resource.completed && task.status === 'done') {
            updateData.completedAt = null;
          }
        }

        // Update task
        await prisma.projectTask.update({
          where: { id: task.id },
          data: updateData,
        });

        // Update project progress if status changed
        if (updateData.status && updateData.status !== task.status) {
          await this.updateProjectProgress(task.projectId, tenantId);
        }

        results.push({
          success: true,
          message: `Successfully updated task ${task.title}`,
          updatedTasks: 1,
        });
      }

      const successCount = results.filter(r => r.success).length;
      const errorCount = results.filter(r => !r.success).length;

      return {
        success: errorCount === 0,
        message: `Processed ${successCount} tasks successfully${errorCount > 0 ? `, ${errorCount} errors` : ''}`,
        updatedTasks: successCount,
        errors: results.filter(r => !r.success).map(r => r.message),
      };
    } catch (error) {
      console.error('Error processing Asana webhook:', error);
      return {
        success: false,
        message: 'Failed to process Asana webhook',
        errors: [error instanceof Error ? error.message : 'Unknown error'],
      };
    }
  }

  /**
   * Process Trello webhook
   */
  async processTrelloWebhook(
    tenantId: string,
    payload: TrelloWebhookPayload
  ): Promise<WebhookProcessingResult> {
    try {
      const { action } = payload;
      
      if (!action.data.card) {
        return {
          success: false,
          message: 'No card data in webhook payload',
        };
      }

      const card = action.data.card;

      // Find task by external ID
      const task = await prisma.projectTask.findFirst({
        where: {
          tenantId,
          externalTaskId: card.id,
        },
        include: {
          project: true,
        },
      });

      if (!task) {
        return {
          success: false,
          message: `Task with external ID ${card.id} not found`,
        };
      }

      // Map Trello list names to our status
      const statusMapping: Record<string, string> = {
        'To Do': 'todo',
        'Doing': 'in_progress',
        'Review': 'review',
        'Done': 'done',
      };

      const updateData: any = {
        title: card.name,
        description: card.desc,
        updatedAt: new Date(),
      };

      // Handle status based on list name or card closed status
      if (action.data.list) {
        updateData.status = statusMapping[action.data.list.name] || task.status;
      } else if (card.closed !== undefined) {
        updateData.status = card.closed ? 'done' : 'todo';
      }

      // Handle completion
      if (updateData.status === 'done' && task.status !== 'done') {
        updateData.completedAt = new Date();
      } else if (updateData.status !== 'done' && task.status === 'done') {
        updateData.completedAt = null;
      }

      // Update task
      await prisma.projectTask.update({
        where: { id: task.id },
        data: updateData,
      });

      // Update project progress if status changed
      if (updateData.status && updateData.status !== task.status) {
        await this.updateProjectProgress(task.projectId, tenantId);
      }

      return {
        success: true,
        message: `Successfully updated task ${task.title}`,
        updatedTasks: 1,
      };
    } catch (error) {
      console.error('Error processing Trello webhook:', error);
      return {
        success: false,
        message: 'Failed to process Trello webhook',
        errors: [error instanceof Error ? error.message : 'Unknown error'],
      };
    }
  }

  /**
   * Update project progress based on task completion
   */
  private async updateProjectProgress(projectId: string, tenantId: string): Promise<void> {
    const tasks = await prisma.projectTask.findMany({
      where: {
        projectId,
        tenantId,
      },
      select: {
        status: true,
      },
    });

    if (tasks.length === 0) {
      return;
    }

    const completedTasks = tasks.filter(task => task.status === 'done').length;
    const progress = (completedTasks / tasks.length) * 100;

    await prisma.project.update({
      where: { id: projectId },
      data: {
        progress: progress,
        actualHours: await this.calculateActualHours(projectId, tenantId),
      },
    });
  }

  /**
   * Calculate actual hours from tasks
   */
  private async calculateActualHours(projectId: string, tenantId: string): Promise<number> {
    const tasks = await prisma.projectTask.findMany({
      where: {
        projectId,
        tenantId,
      },
      select: {
        actualHours: true,
      },
    });

    return tasks.reduce((total, task) => {
      return total + (task.actualHours ? Number(task.actualHours) : 0);
    }, 0);
  }

  /**
   * Validate webhook signature (for security)
   */
  validateWebhookSignature(
    payload: string,
    signature: string,
    secret: string,
    provider: string
  ): boolean {
    const crypto = require('crypto');
    
    switch (provider) {
      case 'jira':
        // Jira uses HMAC-SHA256
        const jiraSignature = crypto
          .createHmac('sha256', secret)
          .update(payload)
          .digest('hex');
        return `sha256=${jiraSignature}` === signature;
        
      case 'asana':
        // Asana uses HMAC-SHA256
        const asanaSignature = crypto
          .createHmac('sha256', secret)
          .update(payload)
          .digest('hex');
        return asanaSignature === signature;
        
      case 'trello':
        // Trello uses base64 encoded HMAC-SHA1
        const trelloSignature = crypto
          .createHmac('sha1', secret)
          .update(payload)
          .digest('base64');
        return trelloSignature === signature;
        
      default:
        return false;
    }
  }
}
