'use client';

import React from 'react';
import { Alert<PERSON>riangle, Trash2, X, CheckCircle, Info, AlertCircle } from 'lucide-react';
import { Button } from './button';
import { cn } from '@/lib/utils';

interface ConfirmationDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  title: string;
  description: string;
  confirmText?: string;
  cancelText?: string;
  variant?: 'danger' | 'warning' | 'info' | 'success';
  icon?: React.ReactNode;
  loading?: boolean;
  details?: string[];
  className?: string;
}

const variantConfig = {
  danger: {
    icon: Trash2,
    iconBg: 'bg-red-100 dark:bg-red-900/20',
    iconColor: 'text-red-600 dark:text-red-400',
    confirmButton: 'destructive' as const,
    titleColor: 'text-red-900 dark:text-red-100',
    descriptionColor: 'text-red-700 dark:text-red-300'
  },
  warning: {
    icon: AlertTriangle,
    iconBg: 'bg-yellow-100 dark:bg-yellow-900/20',
    iconColor: 'text-yellow-600 dark:text-yellow-400',
    confirmButton: 'default' as const,
    titleColor: 'text-yellow-900 dark:text-yellow-100',
    descriptionColor: 'text-yellow-700 dark:text-yellow-300'
  },
  info: {
    icon: Info,
    iconBg: 'bg-blue-100 dark:bg-blue-900/20',
    iconColor: 'text-blue-600 dark:text-blue-400',
    confirmButton: 'default' as const,
    titleColor: 'text-blue-900 dark:text-blue-100',
    descriptionColor: 'text-blue-700 dark:text-blue-300'
  },
  success: {
    icon: CheckCircle,
    iconBg: 'bg-green-100 dark:bg-green-900/20',
    iconColor: 'text-green-600 dark:text-green-400',
    confirmButton: 'default' as const,
    titleColor: 'text-green-900 dark:text-green-100',
    descriptionColor: 'text-green-700 dark:text-green-300'
  }
};

export function ConfirmationDialog({
  isOpen,
  onClose,
  onConfirm,
  title,
  description,
  confirmText = 'Confirm',
  cancelText = 'Cancel',
  variant = 'danger',
  icon,
  loading = false,
  details = [],
  className
}: ConfirmationDialogProps) {
  const config = variantConfig[variant];
  const IconComponent = config.icon;

  if (!isOpen) return null;

  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  return (
    <div 
      className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50 backdrop-blur-sm"
      onClick={handleBackdropClick}
    >
      <div 
        className={cn(
          'relative w-full max-w-md mx-auto bg-white dark:bg-gray-800 rounded-2xl shadow-2xl transform transition-all duration-300 scale-100 opacity-100',
          'border border-gray-200 dark:border-gray-700',
          className
        )}
        onClick={(e) => e.stopPropagation()}
      >
        {/* Close Button */}
        <button
          onClick={onClose}
          className="absolute top-4 right-4 p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200"
          disabled={loading}
        >
          <X className="w-4 h-4 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300" />
        </button>

        <div className="p-8">
          {/* Icon */}
          <div className={cn(
            'w-16 h-16 mx-auto mb-6 rounded-full flex items-center justify-center',
            config.iconBg
          )}>
            {icon || <IconComponent className={cn('w-8 h-8', config.iconColor)} />}
          </div>

          {/* Content */}
          <div className="text-center space-y-4">
            <h3 className={cn(
              'text-xl font-semibold',
              config.titleColor
            )}>
              {title}
            </h3>
            
            <p className={cn(
              'text-sm leading-relaxed',
              config.descriptionColor
            )}>
              {description}
            </p>

            {/* Details */}
            {details.length > 0 && (
              <div className="mt-4 p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                <ul className="text-left space-y-1">
                  {details.map((detail, index) => (
                    <li key={index} className="text-sm text-gray-600 dark:text-gray-400 flex items-start">
                      <span className="w-1.5 h-1.5 bg-gray-400 rounded-full mt-2 mr-2 flex-shrink-0" />
                      {detail}
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </div>

          {/* Actions */}
          <div className="flex flex-col sm:flex-row gap-3 mt-8">
            <Button
              variant="outline"
              onClick={onClose}
              disabled={loading}
              className="flex-1 order-2 sm:order-1"
            >
              {cancelText}
            </Button>
            <Button
              variant={config.confirmButton}
              onClick={onConfirm}
              loading={loading}
              className="flex-1 order-1 sm:order-2"
            >
              {confirmText}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}

// Hook for easier usage
export function useConfirmationDialog() {
  const [dialog, setDialog] = React.useState<{
    isOpen: boolean;
    props: Partial<ConfirmationDialogProps>;
  }>({
    isOpen: false,
    props: {}
  });

  const showDialog = (props: Omit<ConfirmationDialogProps, 'isOpen' | 'onClose' | 'onConfirm'> & {
    onConfirm: () => void | Promise<void>;
  }) => {
    return new Promise<boolean>((resolve) => {
      setDialog({
        isOpen: true,
        props: {
          ...props,
          onConfirm: async () => {
            try {
              await props.onConfirm();
              resolve(true);
              setDialog(prev => ({ ...prev, isOpen: false }));
            } catch (error) {
              resolve(false);
            }
          },
          onClose: () => {
            resolve(false);
            setDialog(prev => ({ ...prev, isOpen: false }));
          }
        }
      });
    });
  };

  const DialogComponent = () => (
    <ConfirmationDialog
      isOpen={dialog.isOpen}
      onClose={() => setDialog(prev => ({ ...prev, isOpen: false }))}
      onConfirm={() => {}}
      title=""
      description=""
      {...dialog.props}
    />
  );

  return { showDialog, DialogComponent };
}

// Quick confirmation functions
export const confirmDelete = (itemName: string, onConfirm: () => void | Promise<void>) => {
  return new Promise<boolean>((resolve) => {
    const dialog = document.createElement('div');
    document.body.appendChild(dialog);

    const cleanup = () => {
      document.body.removeChild(dialog);
    };

    const handleConfirm = async () => {
      try {
        await onConfirm();
        resolve(true);
      } catch (error) {
        resolve(false);
      } finally {
        cleanup();
      }
    };

    const handleCancel = () => {
      resolve(false);
      cleanup();
    };

    // This would need to be implemented with a portal in a real app
    // For production, this should use a proper modal dialog
    handleCancel(); // Default to cancel for production safety
  });
};
