'use client';

import React, { useState } from 'react';
import { DocumentWithRelations } from '@/services/document-management';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  File,
  Download,
  Edit,
  Trash2,
  MoreHorizontal,
  Eye,
  Calendar,
  User,
  FileText,
  Image,
  Archive,
  FileSpreadsheet,
  Presentation,
} from 'lucide-react';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';
import { usePermissions } from '@/components/providers/permission-provider';
import { PermissionResource, PermissionAction } from '@/types';

interface DocumentListProps {
  documents: DocumentWithRelations[];
  onView?: (document: DocumentWithRelations) => void;
  onEdit?: (document: DocumentWithRelations) => void;
  onDelete?: (document: DocumentWithRelations) => void;
  onDownload?: (document: DocumentWithRelations) => void;
  loading?: boolean;
  className?: string;
  viewMode?: 'list' | 'grid';
}

export function DocumentList({
  documents,
  onView,
  onEdit,
  onDelete,
  onDownload,
  loading = false,
  className,
  viewMode = 'list',
}: DocumentListProps) {
  const { hasPermission } = usePermissions();
  const [downloadingIds, setDownloadingIds] = useState<Set<string>>(new Set());

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getFileIcon = (mimeType: string, className?: string) => {
    const iconClass = cn('w-5 h-5', className);
    
    if (mimeType.startsWith('image/')) return <Image className={iconClass} />;
    if (mimeType.includes('pdf')) return <FileText className={cn(iconClass, 'text-red-500')} />;
    if (mimeType.includes('word') || mimeType.includes('document')) return <FileText className={cn(iconClass, 'text-blue-500')} />;
    if (mimeType.includes('excel') || mimeType.includes('spreadsheet')) return <FileSpreadsheet className={cn(iconClass, 'text-green-500')} />;
    if (mimeType.includes('powerpoint') || mimeType.includes('presentation')) return <Presentation className={cn(iconClass, 'text-orange-500')} />;
    if (mimeType.includes('zip') || mimeType.includes('rar') || mimeType.includes('7z')) return <Archive className={iconClass} />;
    return <File className={iconClass} />;
  };

  const handleDownload = async (document: DocumentWithRelations) => {
    if (!onDownload) return;
    
    setDownloadingIds(prev => new Set(prev).add(document.id));
    try {
      await onDownload(document);
    } finally {
      setDownloadingIds(prev => {
        const newSet = new Set(prev);
        newSet.delete(document.id);
        return newSet;
      });
    }
  };

  const getUserDisplayName = (user: any) => {
    if (!user) return 'Unknown';
    return `${user.firstName || ''} ${user.lastName || ''}`.trim() || user.email;
  };

  const getUserInitials = (user: any) => {
    if (!user) return 'U';
    const firstName = user.firstName || '';
    const lastName = user.lastName || '';
    return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase() || user.email.charAt(0).toUpperCase();
  };

  if (loading) {
    return (
      <div className={cn('space-y-4', className)}>
        {Array.from({ length: 3 }).map((_, i) => (
          <Card key={i}>
            <CardContent className="p-4">
              <div className="animate-pulse">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-gray-200 rounded"></div>
                  <div className="flex-1">
                    <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                    <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (documents.length === 0) {
    return (
      <Card className={className}>
        <CardContent className="flex flex-col items-center justify-center py-12">
          <File className="w-12 h-12 text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No documents found</h3>
          <p className="text-sm text-gray-500 text-center">
            Upload your first document to get started.
          </p>
        </CardContent>
      </Card>
    );
  }

  if (viewMode === 'grid') {
    return (
      <div className={cn('grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4', className)}>
        {documents.map((document) => (
          <Card key={document.id} className="hover:shadow-md transition-shadow">
            <CardContent className="p-4">
              <div className="flex items-start justify-between mb-3">
                <div className="flex items-center space-x-3">
                  {getFileIcon(document.mimeType || '', 'w-8 h-8')}
                  <div className="flex-1 min-w-0">
                    <h4 className="font-medium text-sm text-gray-900 truncate">
                      {document.name}
                    </h4>
                    <p className="text-xs text-gray-500">
                      {document.fileSize ? formatFileSize(document.fileSize) : 'Unknown size'}
                    </p>
                  </div>
                </div>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="sm">
                      <MoreHorizontal className="w-4 h-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    {onView && (
                      <DropdownMenuItem onClick={() => onView(document)}>
                        <Eye className="w-4 h-4 mr-2" />
                        View
                      </DropdownMenuItem>
                    )}
                    {onDownload && (
                      <DropdownMenuItem 
                        onClick={() => handleDownload(document)}
                        disabled={downloadingIds.has(document.id)}
                      >
                        <Download className="w-4 h-4 mr-2" />
                        {downloadingIds.has(document.id) ? 'Downloading...' : 'Download'}
                      </DropdownMenuItem>
                    )}
                    {onEdit && hasPermission(PermissionResource.DOCUMENTS, PermissionAction.UPDATE) && (
                      <DropdownMenuItem onClick={() => onEdit(document)}>
                        <Edit className="w-4 h-4 mr-2" />
                        Edit
                      </DropdownMenuItem>
                    )}
                    {onDelete && hasPermission(PermissionResource.DOCUMENTS, PermissionAction.DELETE) && (
                      <DropdownMenuItem 
                        onClick={() => onDelete(document)}
                        className="text-red-600"
                      >
                        <Trash2 className="w-4 h-4 mr-2" />
                        Delete
                      </DropdownMenuItem>
                    )}
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>

              {document.description && (
                <p className="text-xs text-gray-600 mb-3 line-clamp-2">
                  {document.description}
                </p>
              )}

              {document.tags && Array.isArray(document.tags) && document.tags.length > 0 && (
                <div className="flex flex-wrap gap-1 mb-3">
                  {document.tags.slice(0, 2).map((tag, index) => (
                    <Badge key={index} variant="secondary" className="text-xs">
                      {tag}
                    </Badge>
                  ))}
                  {document.tags.length > 2 && (
                    <Badge variant="outline" className="text-xs">
                      +{document.tags.length - 2}
                    </Badge>
                  )}
                </div>
              )}

              <div className="flex items-center justify-between text-xs text-gray-500">
                <div className="flex items-center space-x-1">
                  <Calendar className="w-3 h-3" />
                  <span>{format(new Date(document.createdAt), 'MMM d, yyyy')}</span>
                </div>
                {document.uploadedBy && (
                  <div className="flex items-center space-x-1">
                    <User className="w-3 h-3" />
                    <span className="truncate max-w-20">
                      {getUserDisplayName(document.uploadedBy)}
                    </span>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className={cn('space-y-3', className)}>
      {documents.map((document) => (
        <Card key={document.id} className="hover:shadow-sm transition-shadow">
          <CardContent className="p-4">
            <div className="flex items-center space-x-4">
              <div className="flex-shrink-0">
                {getFileIcon(document.mimeType || '', 'w-8 h-8')}
              </div>
              
              <div className="flex-1 min-w-0">
                <div className="flex items-start justify-between">
                  <div className="flex-1 min-w-0">
                    <h4 className="font-medium text-sm text-gray-900 truncate">
                      {document.name}
                    </h4>
                    {document.description && (
                      <p className="text-xs text-gray-600 mt-1 line-clamp-1">
                        {document.description}
                      </p>
                    )}
                    <div className="flex items-center space-x-4 mt-2 text-xs text-gray-500">
                      <span>{document.fileSize ? formatFileSize(document.fileSize) : 'Unknown size'}</span>
                      <span>{format(new Date(document.createdAt), 'MMM d, yyyy')}</span>
                      {document.uploadedBy && (
                        <div className="flex items-center space-x-1">
                          <Avatar className="w-4 h-4">
                            <AvatarFallback className="text-xs">
                              {getUserInitials(document.uploadedBy)}
                            </AvatarFallback>
                          </Avatar>
                          <span>{getUserDisplayName(document.uploadedBy)}</span>
                        </div>
                      )}
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    {document.tags && Array.isArray(document.tags) && document.tags.length > 0 && (
                      <div className="flex flex-wrap gap-1">
                        {document.tags.slice(0, 2).map((tag, index) => (
                          <Badge key={index} variant="secondary" className="text-xs">
                            {tag}
                          </Badge>
                        ))}
                        {document.tags.length > 2 && (
                          <Badge variant="outline" className="text-xs">
                            +{document.tags.length - 2}
                          </Badge>
                        )}
                      </div>
                    )}
                    
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm">
                          <MoreHorizontal className="w-4 h-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        {onView && (
                          <DropdownMenuItem onClick={() => onView(document)}>
                            <Eye className="w-4 h-4 mr-2" />
                            View
                          </DropdownMenuItem>
                        )}
                        {onDownload && (
                          <DropdownMenuItem 
                            onClick={() => handleDownload(document)}
                            disabled={downloadingIds.has(document.id)}
                          >
                            <Download className="w-4 h-4 mr-2" />
                            {downloadingIds.has(document.id) ? 'Downloading...' : 'Download'}
                          </DropdownMenuItem>
                        )}
                        {onEdit && hasPermission(PermissionResource.DOCUMENTS, PermissionAction.UPDATE) && (
                          <DropdownMenuItem onClick={() => onEdit(document)}>
                            <Edit className="w-4 h-4 mr-2" />
                            Edit
                          </DropdownMenuItem>
                        )}
                        {onDelete && hasPermission(PermissionResource.DOCUMENTS, PermissionAction.DELETE) && (
                          <DropdownMenuItem 
                            onClick={() => onDelete(document)}
                            className="text-red-600"
                          >
                            <Trash2 className="w-4 h-4 mr-2" />
                            Delete
                          </DropdownMenuItem>
                        )}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
