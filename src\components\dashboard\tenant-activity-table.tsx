'use client';

import React, { useEffect, useState, useRef } from 'react';
import { useRouter } from 'next/navigation';
import {
  EyeIcon,
  UserIcon,
  BuildingOfficeIcon,
  ChartBarIcon,
  DocumentIcon
} from '@heroicons/react/24/outline';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent } from "@/components/ui/tabs";
import { TenantDashboardMetrics } from '@/services/tenant-dashboard-analytics';
import { cn } from '@/lib/utils';

interface ActivityItem {
  id: string;
  type: 'lead' | 'opportunity' | 'contact' | 'company';
  action: string;
  description: string;
  timestamp: Date;
  userId?: string;
  userName?: string;
}

interface TenantActivityTableProps {
  className?: string;
}

export function TenantActivityTable({ className }: TenantActivityTableProps) {
  const [activities, setActivities] = useState<ActivityItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedTab, setSelectedTab] = useState("all");
  const [canScrollLeft, setCanScrollLeft] = useState(false);
  const [canScrollRight, setCanScrollRight] = useState(false);
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const router = useRouter();

  useEffect(() => {
    fetchActivities();
  }, []);

  const checkScrollability = () => {
    const container = scrollContainerRef.current;
    if (container) {
      setCanScrollLeft(container.scrollLeft > 0);
      setCanScrollRight(
        container.scrollLeft < container.scrollWidth - container.clientWidth
      );
    }
  };

  useEffect(() => {
    checkScrollability();
    const container = scrollContainerRef.current;
    if (container) {
      container.addEventListener('scroll', checkScrollability);
      window.addEventListener('resize', checkScrollability);
      return () => {
        container.removeEventListener('scroll', checkScrollability);
        window.removeEventListener('resize', checkScrollability);
      };
    }
  }, [activities]);

  const fetchActivities = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await fetch('/api/dashboard/analytics?type=full');
      
      if (!response.ok) {
        throw new Error(`Failed to fetch activities: ${response.statusText}`);
      }
      
      const data = await response.json();
      
      if (!data.success) {
        throw new Error(data.error || 'Failed to fetch activities');
      }
      
      const metrics: TenantDashboardMetrics = data.data;
      setActivities(metrics.recentActivities);
      
    } catch (err) {
      console.error('Error fetching activities:', err);
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'lead':
        return ChartBarIcon;
      case 'opportunity':
        return DocumentIcon;
      case 'contact':
        return UserIcon;
      case 'company':
        return BuildingOfficeIcon;
      default:
        return DocumentIcon;
    }
  };

  const getActivityColor = (type: string) => {
    switch (type) {
      case 'lead':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
      case 'opportunity':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      case 'contact':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300';
      case 'company':
        return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
    }
  };

  const formatTimestamp = (timestamp: Date) => {
    const now = new Date();
    const diff = now.getTime() - new Date(timestamp).getTime();
    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));

    if (minutes < 60) {
      return `${minutes}m ago`;
    } else if (hours < 24) {
      return `${hours}h ago`;
    } else {
      return `${days}d ago`;
    }
  };

  const handleViewItem = (activity: ActivityItem) => {
    if (!activity || !activity.id || !activity.type) {
      console.warn('Invalid activity item:', activity);
      return;
    }

    try {
      switch (activity.type) {
        case 'lead':
          router.push(`/leads/${activity.id}`);
          break;
        case 'opportunity':
          router.push(`/opportunities/${activity.id}`);
          break;
        case 'contact':
          router.push(`/contacts/${activity.id}`);
          break;
        case 'company':
          router.push(`/companies/${activity.id}`);
          break;
        default:
          console.warn('Unknown activity type:', activity.type);
      }
    } catch (error) {
      console.error('Error navigating to activity item:', error);
    }
  };



  const filteredActivities = selectedTab === "all" 
    ? activities 
    : activities.filter(activity => activity.type === selectedTab);

  const tabs = [
    {
      name: "All",
      value: "all",
      count: activities.length,
      icon: DocumentIcon,
      color: "bg-gray-100 text-gray-600 dark:bg-gray-800 dark:text-gray-400"
    },
    {
      name: "Leads",
      value: "lead",
      count: activities.filter(a => a.type === 'lead').length,
      icon: UserIcon,
      color: "bg-blue-100 text-blue-600 dark:bg-blue-900/20 dark:text-blue-400"
    },
    {
      name: "Opportunities",
      value: "opportunity",
      count: activities.filter(a => a.type === 'opportunity').length,
      icon: ChartBarIcon,
      color: "bg-green-100 text-green-600 dark:bg-green-900/20 dark:text-green-400"
    },
    {
      name: "Contacts",
      value: "contact",
      count: activities.filter(a => a.type === 'contact').length,
      icon: UserIcon,
      color: "bg-purple-100 text-purple-600 dark:bg-purple-900/20 dark:text-purple-400"
    },
    {
      name: "Companies",
      value: "company",
      count: activities.filter(a => a.type === 'company').length,
      icon: BuildingOfficeIcon,
      color: "bg-orange-100 text-orange-600 dark:bg-orange-900/20 dark:text-orange-400"
    },
  ];

  if (loading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle>Recent Activities</CardTitle>
          <CardDescription>Loading recent activities...</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {Array.from({ length: 5 }).map((_, i) => (
              <div key={i} className="flex items-center space-x-4 animate-pulse">
                <div className="w-8 h-8 bg-gray-200 rounded-full"></div>
                <div className="flex-1 space-y-2">
                  <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                </div>
                <div className="h-6 bg-gray-200 rounded w-16"></div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle>Recent Activities</CardTitle>
          <CardDescription className="text-red-500">Error loading activities</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <p className="text-sm text-red-500 mb-4">{error}</p>
            <Button
              onClick={(e) => {
                e.preventDefault();
                fetchActivities();
              }}
              variant="outline"
              size="sm"
            >
              Retry
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Recent Activities</CardTitle>
            <CardDescription>
              Latest updates and interactions across your CRM
            </CardDescription>
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={(e) => {
                e.preventDefault();
                fetchActivities();
              }}
            >
              Refresh
            </Button>
          </div>
        </div>
      </CardHeader>

      <CardContent className="pt-0">
        <Tabs value={selectedTab} onValueChange={setSelectedTab} className="space-y-4">
          {/* Enhanced Scrollable Tab Navigation */}
          <div className="relative">
            {/* Left scroll indicator */}
            {canScrollLeft && (
              <div className="absolute left-0 top-0 bottom-0 w-6 bg-gradient-to-r from-muted/30 via-muted/20 to-transparent z-10 pointer-events-none rounded-l-lg" />
            )}

            {/* Right scroll indicator */}
            {canScrollRight && (
              <div className="absolute right-0 top-0 bottom-0 w-6 bg-gradient-to-l from-muted/30 via-muted/20 to-transparent z-10 pointer-events-none rounded-r-lg" />
            )}

            <div
              ref={scrollContainerRef}
              className="flex gap-1 p-0.5 bg-muted/30 rounded-lg overflow-x-auto scroll-smooth [&::-webkit-scrollbar]:hidden touch-pan-x"
              style={{
                scrollbarWidth: 'none',
                msOverflowStyle: 'none',
                WebkitOverflowScrolling: 'touch'
              }}
            >
              {tabs.map((tab, index) => {
                const Icon = tab.icon;
                const isActive = selectedTab === tab.value;

                return (
                  <button
                    key={tab.value}
                    onClick={(e) => {
                      setSelectedTab(tab.value);
                      // Scroll the clicked tab into view
                      const currentTarget = e.currentTarget;
                      setTimeout(() => {
                        if (currentTarget) {
                          currentTarget.scrollIntoView({ behavior: 'smooth', block: 'nearest', inline: 'center' });
                        }
                      }, 0);
                    }}
                    onKeyDown={(e) => {
                      if (e.key === 'ArrowRight' && index < tabs.length - 1) {
                        const nextTab = tabs[index + 1];
                        setSelectedTab(nextTab.value);
                        // Scroll the next tab into view
                        const currentTarget = e.currentTarget;
                        setTimeout(() => {
                          const nextButton = currentTarget?.nextElementSibling as HTMLElement;
                          if (nextButton) {
                            nextButton.scrollIntoView({ behavior: 'smooth', block: 'nearest', inline: 'center' });
                          }
                        }, 0);
                      } else if (e.key === 'ArrowLeft' && index > 0) {
                        const prevTab = tabs[index - 1];
                        setSelectedTab(prevTab.value);
                        // Scroll the previous tab into view
                        const currentTarget = e.currentTarget;
                        setTimeout(() => {
                          const prevButton = currentTarget?.previousElementSibling as HTMLElement;
                          if (prevButton) {
                            prevButton.scrollIntoView({ behavior: 'smooth', block: 'nearest', inline: 'center' });
                          }
                        }, 0);
                      }
                    }}
                    className={cn(
                      "flex items-center gap-2 px-3 py-1.5 rounded-md text-sm font-medium transition-all duration-200",
                      "hover:bg-background/80 focus:outline-none focus:ring-2 focus:ring-primary/20",
                      "whitespace-nowrap flex-shrink-0", // Prevent wrapping and shrinking
                      isActive
                        ? "bg-background shadow-sm text-foreground border border-border/50"
                        : "text-muted-foreground hover:text-foreground"
                    )}
                    role="tab"
                    aria-selected={isActive}
                    aria-controls={`tabpanel-${tab.value}`}
                    tabIndex={isActive ? 0 : -1}
                  >
                    <div className={cn(
                      "p-1 rounded-full transition-colors flex-shrink-0",
                      isActive ? tab.color : "bg-muted/50 text-muted-foreground"
                    )}>
                      <Icon className="h-3 w-3" />
                    </div>
                    <span className="whitespace-nowrap">{tab.name}</span>
                    {tab.count > 0 && (
                      <Badge
                        variant={isActive ? "default" : "secondary"}
                        className={cn(
                          "text-xs px-1.5 py-0.5 min-w-[1.25rem] h-5 flex items-center justify-center flex-shrink-0",
                          isActive ? "bg-primary/10 text-primary border-primary/20" : ""
                        )}
                      >
                        {tab.count}
                      </Badge>
                    )}
                  </button>
                );
              })}
            </div>
          </div>

          {/* Enhanced Tab Content */}
          <TabsContent
            value={selectedTab}
            className="space-y-4"
            id={`tabpanel-${selectedTab}`}
            role="tabpanel"
            aria-labelledby={`tab-${selectedTab}`}
          >
            {loading ? (
              <div className="space-y-2">
                {Array.from({ length: 3 }).map((_, i) => (
                  <div key={i} className="flex items-center space-x-3 p-3 rounded-lg border animate-pulse">
                    <div className="w-10 h-10 bg-muted rounded-full" />
                    <div className="flex-1 space-y-2">
                      <div className="h-4 bg-muted rounded w-3/4" />
                      <div className="h-3 bg-muted rounded w-1/2" />
                    </div>
                    <div className="w-8 h-8 bg-muted rounded" />
                  </div>
                ))}
              </div>
            ) : filteredActivities.length === 0 ? (
              <div className="text-center py-8">
                <div className="mx-auto w-16 h-16 bg-muted/30 rounded-full flex items-center justify-center mb-4">
                  {selectedTab !== 'all' ? (
                    <div className={cn("p-2 rounded-full", tabs.find(t => t.value === selectedTab)?.color)}>
                      {React.createElement(tabs.find(t => t.value === selectedTab)?.icon || DocumentIcon, { className: "h-6 w-6" })}
                    </div>
                  ) : (
                    <DocumentIcon className="h-6 w-6 text-muted-foreground" />
                  )}
                </div>
                <h3 className="text-sm font-medium text-foreground mb-2">
                  No {selectedTab === 'all' ? 'recent activities' : `${selectedTab} activities`} found
                </h3>
                <p className="text-sm text-muted-foreground">
                  {selectedTab === 'all'
                    ? 'Activities will appear here as you interact with leads, contacts, and opportunities.'
                    : `${selectedTab.charAt(0).toUpperCase() + selectedTab.slice(1)} activities will appear here.`
                  }
                </p>
              </div>
            ) : (
              <div className="space-y-2">
                {filteredActivities.map((activity) => {
                  const Icon = getActivityIcon(activity.type);
                  const tabInfo = tabs.find(t => t.value === activity.type);

                  return (
                    <div
                      key={activity.id}
                      className={cn(
                        "group flex items-center justify-between p-3 rounded-lg transition-all duration-200",
                        "hover:bg-muted/50 hover:shadow-sm border border-transparent hover:border-border/50",
                        "focus-within:ring-2 focus-within:ring-primary/20"
                      )}
                    >
                      <div className="flex items-start space-x-3 flex-1 min-w-0">
                        {/* Enhanced Activity Icon */}
                        <div className={cn(
                          "flex-shrink-0 p-2 rounded-full transition-colors",
                          tabInfo?.color || getActivityColor(activity.type)
                        )}>
                          <Icon className="h-4 w-4" />
                        </div>

                        {/* Enhanced Activity Content */}
                        <div className="flex-1 min-w-0">
                          <div className="flex items-start justify-between gap-2">
                            <p className="text-sm font-medium text-foreground leading-5 line-clamp-2">
                              {activity.description}
                            </p>
                            <span className="text-xs text-muted-foreground whitespace-nowrap">
                              {formatTimestamp(activity.timestamp)}
                            </span>
                          </div>

                          <div className="flex items-center gap-2 mt-2">
                            <Badge
                              variant="outline"
                              className={cn(
                                "text-xs px-2 py-0.5 border-0",
                                tabInfo?.color || "bg-muted text-muted-foreground"
                              )}
                            >
                              {activity.type}
                            </Badge>

                            {activity.userName && (
                              <>
                                <span className="text-xs text-muted-foreground">•</span>
                                <span className="text-xs text-muted-foreground">
                                  by {activity.userName}
                                </span>
                              </>
                            )}
                          </div>
                        </div>
                      </div>

                      {/* Enhanced Action Button */}
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={(e) => {
                          e.preventDefault();
                          e.stopPropagation();
                          handleViewItem(activity);
                        }}
                        className="opacity-0 group-hover:opacity-100 transition-opacity ml-2 flex-shrink-0"
                      >
                        <EyeIcon className="h-4 w-4" />
                        <span className="sr-only">View details</span>
                      </Button>
                    </div>
                  );
                })}

                {/* Show More Button */}
                {filteredActivities.length >= 5 && (
                  <div className="pt-3 border-t border-border/50">
                    <Button
                      variant="ghost"
                      size="sm"
                      className="w-full text-muted-foreground hover:text-foreground"
                      onClick={(e) => {
                        e.preventDefault();
                        // TODO: Navigate to full activities page
                        console.log('Show more activities');
                      }}
                    >
                      View all {selectedTab === 'all' ? 'activities' : `${selectedTab} activities`}
                      <ChartBarIcon className="h-4 w-4 ml-2" />
                    </Button>
                  </div>
                )}
              </div>
            )}
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
