'use client';

import React from 'react';
import { Settings2 } from 'lucide-react';
import { cn } from '@/lib/utils';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Button } from '@/components/ui/button';
import { ThemeToggle } from '@/components/ui/theme-toggle';
import { LanguageSwitcher } from '@/components/layout/language-switcher';
import { useTranslation } from '@/components/providers/locale-provider';

interface PreferencesSwitcherProps {
  variant?: 'default' | 'sidebar';
  className?: string;
}

export function PreferencesSwitcher({ 
  variant = 'default',
  className
}: PreferencesSwitcherProps) {
  const { t } = useTranslation();

  if (variant === 'sidebar') {
    return (
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="ghost"
            size="sm"
            className={cn(
              "h-8 w-8 px-0 data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground",
              className
            )}
            title={t('common.settings')}
          >
            <Settings2 className="h-4 w-4" />
            <span className="sr-only">{t('common.settings')}</span>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-56">
          <DropdownMenuLabel className="text-xs font-medium text-muted-foreground">
            {t('common.settings')}
          </DropdownMenuLabel>
          <DropdownMenuSeparator />
          
          <DropdownMenuGroup>
            <div className="px-2 py-1.5">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Theme</span>
                <ThemeToggle variant="sidebar" />
              </div>
            </div>
            
            <div className="px-2 py-1.5">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Language</span>
                <LanguageSwitcher variant="sidebar" />
              </div>
            </div>
          </DropdownMenuGroup>
        </DropdownMenuContent>
      </DropdownMenu>
    );
  }

  return (
    <div className={cn("flex items-center gap-2", className)}>
      <ThemeToggle variant="dropdown" size="sm" />
      <LanguageSwitcher variant="default" size="sm" />
    </div>
  );
}
