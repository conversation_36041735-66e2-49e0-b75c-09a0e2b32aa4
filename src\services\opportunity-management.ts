import { prisma } from '@/lib/prisma';
import { Opportunity } from '@prisma/client';
import { z } from 'zod';
import { ConversionResult } from './lead-management';
import { opportunityTriggersService } from './opportunity-triggers';
import { UnifiedNotificationService } from './unified-notification-service';

// Validation schemas
export const createOpportunitySchema = z.object({
  title: z.string().min(1, 'Title is required').max(255),
  description: z.string().optional().nullable(),
  value: z.number().positive().optional().nullable(),
  stage: z.string().min(1, 'Stage is required'),
  probability: z.number().min(0).max(100).default(0),
  expectedCloseDate: z.string().datetime().optional().nullable(),
  tags: z.array(z.string()).default([]),
  source: z.string().optional().nullable(),
  priority: z.enum(['low', 'medium', 'high', 'urgent']).default('medium'),
  closeReason: z.string().optional().nullable(),
  competitorInfo: z.record(z.any()).default({}),
  customFields: z.record(z.any()).default({}),
  leadId: z.string().optional().nullable(),
  contactId: z.string().optional().nullable(),
  companyId: z.string().optional().nullable(),
  ownerId: z.string().optional().nullable(),
});

export const updateOpportunitySchema = createOpportunitySchema.partial();

export const opportunityFiltersSchema = z.object({
  search: z.string().optional(),
  stage: z.string().optional(),
  priority: z.enum(['low', 'medium', 'high', 'urgent']).optional(),
  ownerId: z.string().optional(),
  companyId: z.string().optional(),
  minValue: z.number().optional(),
  maxValue: z.number().optional(),
  dateFrom: z.string().datetime().optional(),
  dateTo: z.string().datetime().optional(),
  tags: z.array(z.string()).optional(),
});

export const paginationSchema = z.object({
  page: z.number().min(1).default(1),
  limit: z.number().min(1).max(100).default(20),
  sortBy: z.enum(['title', 'value', 'stage', 'probability', 'expectedCloseDate', 'createdAt']).default('createdAt'),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
});

// Types
export interface OpportunityWithRelations extends Opportunity {
  lead?: {
    id: string;
    title: string;
    status: string;
  } | null;
  contact?: {
    id: string;
    firstName: string;
    lastName: string;
    email: string | null;
  } | null;
  company?: {
    id: string;
    name: string;
    industry: string | null;
  } | null;
  owner?: {
    id: string;
    firstName: string | null;
    lastName: string | null;
    email: string;
  } | null;
  createdBy?: {
    id: string;
    firstName: string | null;
    lastName: string | null;
    email: string;
  } | null;
  _count?: {
    proposals: number;
    projects: number;
    stageHistory: number;
  };
}

export interface OpportunityStats {
  totalOpportunities: number;
  totalValue: number;
  averageValue: number;
  conversionRate: number;
  stageDistribution: Array<{
    stage: string;
    count: number;
    value: number;
    percentage: number;
  }>;
  priorityDistribution: Array<{
    priority: string;
    count: number;
    value: number;
  }>;
  monthlyTrends: Array<{
    month: string;
    count: number;
    value: number;
    won: number;
    lost: number;
  }>;
}

export interface PipelineData {
  stages: Array<{
    id: string;
    name: string;
    description: string | null;
    orderIndex: number;
    color: string;
    probabilityMin: number;
    probabilityMax: number;
    isClosed: boolean;
    isWon: boolean;
    opportunities: OpportunityWithRelations[];
    totalValue: number;
    count: number;
  }>;
  totalValue: number;
  totalCount: number;
  weightedValue: number;
}

export class OpportunityManagementService {
  private notificationService = new UnifiedNotificationService();

  /**
   * Create a new opportunity
   */
  async createOpportunity(
    tenantId: string,
    opportunityData: z.infer<typeof createOpportunitySchema>,
    createdBy: string
  ): Promise<OpportunityWithRelations> {
    const validatedData = createOpportunitySchema.parse(opportunityData);

    // Verify owner exists if provided
    if (validatedData.ownerId) {
      const owner = await this.verifyUserInTenant(validatedData.ownerId, tenantId);
      if (!owner) {
        throw new Error('Owner not found or not part of tenant');
      }
    }

    // Verify related entities exist
    await this.verifyRelatedEntities(validatedData, tenantId);

    // Verify stage exists for tenant
    await this.verifySalesStage(validatedData.stage, tenantId);

    const opportunity = await prisma.opportunity.create({
      data: {
        ...validatedData,
        expectedCloseDate: validatedData.expectedCloseDate ? new Date(validatedData.expectedCloseDate) : null,
        tenantId,
        createdById: createdBy,
        ownerId: validatedData.ownerId || createdBy
      },
      include: this.getIncludeOptions()
    });

    // Create initial stage history entry
    await this.createStageHistoryEntry(
      opportunity.id,
      tenantId,
      null,
      validatedData.stage,
      createdBy,
      'Opportunity created'
    );

    // Trigger opportunity creation notification
    await this.triggerOpportunityCreationNotification(opportunity, tenantId);

    return opportunity;
  }

  /**
   * Update an opportunity
   */
  async updateOpportunity(
    opportunityId: string,
    tenantId: string,
    updateData: z.infer<typeof updateOpportunitySchema>,
    updatedBy: string
  ): Promise<OpportunityWithRelations> {
    const validatedData = updateOpportunitySchema.parse(updateData);

    // Get current opportunity
    const currentOpportunity = await this.getOpportunity(opportunityId, tenantId);
    if (!currentOpportunity) {
      throw new Error('Opportunity not found');
    }

    // Verify owner exists if provided
    if (validatedData.ownerId) {
      const owner = await this.verifyUserInTenant(validatedData.ownerId, tenantId);
      if (!owner) {
        throw new Error('Owner not found or not part of tenant');
      }
    }

    // Verify related entities exist
    if (validatedData.contactId || validatedData.companyId || validatedData.leadId) {
      await this.verifyRelatedEntities(validatedData, tenantId);
    }

    // Check if stage is changing
    const isStageChanging = validatedData.stage && validatedData.stage !== currentOpportunity.stage;
    if (isStageChanging) {
      await this.verifySalesStage(validatedData.stage!, tenantId);
    }

    const updatedOpportunity = await prisma.opportunity.update({
      where: { id: opportunityId, tenantId },
      data: {
        ...validatedData,
        expectedCloseDate: validatedData.expectedCloseDate ? new Date(validatedData.expectedCloseDate) : undefined,
        lastActivityDate: new Date(),
      },
      include: this.getIncludeOptions()
    });

    // Create stage history entry if stage changed
    if (isStageChanging) {
      await this.createStageHistoryEntry(
        opportunityId,
        tenantId,
        currentOpportunity.stage,
        validatedData.stage!,
        updatedBy,
        'Stage updated'
      );

      // Trigger stage change notification
      await this.triggerOpportunityStageChangeNotification(
        updatedOpportunity,
        tenantId,
        currentOpportunity.stage,
        validatedData.stage!
      );

      // Trigger automation for status change
      try {
        await opportunityTriggersService.handleOpportunityStatusChange({
          opportunityId,
          tenantId,
          oldStatus: currentOpportunity.stage,
          newStatus: validatedData.stage!,
          changedBy: updatedBy,
          changedAt: new Date(),
          metadata: {
            previousProbability: currentOpportunity.probability,
            newProbability: updatedOpportunity.probability
          }
        });
      } catch (triggerError) {
        console.error('Error executing opportunity triggers:', triggerError);
        // Don't fail the update if triggers fail
      }
    }

    // Check for other significant changes that should trigger notifications
    await this.handleOpportunityUpdateNotifications(
      updatedOpportunity,
      currentOpportunity,
      tenantId
    );

    return updatedOpportunity;
  }

  /**
   * Get a single opportunity
   */
  async getOpportunity(opportunityId: string, tenantId: string): Promise<OpportunityWithRelations | null> {
    return await prisma.opportunity.findFirst({
      where: { id: opportunityId, tenantId },
      include: this.getIncludeOptions()
    });
  }

  /**
   * Get opportunities with filtering and pagination
   */
  async getOpportunities(
    tenantId: string,
    filters: z.infer<typeof opportunityFiltersSchema> = {},
    pagination: z.infer<typeof paginationSchema> = {}
  ) {
    const validatedFilters = opportunityFiltersSchema.parse(filters);
    const validatedPagination = paginationSchema.parse(pagination);

    const where: Record<string, unknown> = { tenantId };

    // Apply filters
    if (validatedFilters.search) {
      where.OR = [
        { title: { contains: validatedFilters.search, mode: 'insensitive' } },
        { description: { contains: validatedFilters.search, mode: 'insensitive' } },
      ];
    }

    if (validatedFilters.stage) {
      where.stage = validatedFilters.stage;
    }

    if (validatedFilters.priority) {
      where.priority = validatedFilters.priority;
    }

    if (validatedFilters.ownerId) {
      where.ownerId = validatedFilters.ownerId;
    }

    if (validatedFilters.companyId) {
      where.companyId = validatedFilters.companyId;
    }

    if (validatedFilters.minValue || validatedFilters.maxValue) {
      where.value = {};
      if (validatedFilters.minValue) where.value.gte = validatedFilters.minValue;
      if (validatedFilters.maxValue) where.value.lte = validatedFilters.maxValue;
    }

    if (validatedFilters.dateFrom || validatedFilters.dateTo) {
      where.expectedCloseDate = {};
      if (validatedFilters.dateFrom) where.expectedCloseDate.gte = new Date(validatedFilters.dateFrom);
      if (validatedFilters.dateTo) where.expectedCloseDate.lte = new Date(validatedFilters.dateTo);
    }

    if (validatedFilters.tags && validatedFilters.tags.length > 0) {
      where.tags = {
        hasEvery: validatedFilters.tags
      };
    }

    const [opportunities, total] = await Promise.all([
      prisma.opportunity.findMany({
        where,
        include: this.getIncludeOptions(),
        orderBy: { [validatedPagination.sortBy]: validatedPagination.sortOrder },
        skip: (validatedPagination.page - 1) * validatedPagination.limit,
        take: validatedPagination.limit,
      }),
      prisma.opportunity.count({ where })
    ]);

    return {
      opportunities,
      pagination: {
        page: validatedPagination.page,
        limit: validatedPagination.limit,
        total,
        pages: Math.ceil(total / validatedPagination.limit),
      }
    };
  }

  /**
   * Delete an opportunity (hard delete)
   */
  async deleteOpportunity(opportunityId: string, tenantId: string): Promise<void> {
    const opportunity = await this.getOpportunity(opportunityId, tenantId);
    if (!opportunity) {
      throw new Error('Opportunity not found');
    }

    await prisma.opportunity.delete({
      where: { id: opportunityId, tenantId }
    });
  }

  /**
   * Bulk delete opportunities
   */
  async bulkDeleteOpportunities(opportunityIds: string[], tenantId: string): Promise<number> {
    const result = await prisma.opportunity.deleteMany({
      where: {
        id: { in: opportunityIds },
        tenantId
      }
    });

    return result.count;
  }

  /**
   * Convert lead to opportunity
   */
  async convertLeadToOpportunity(
    leadId: string,
    tenantId: string,
    opportunityData: Partial<z.infer<typeof createOpportunitySchema>>,
    convertedBy: string,
    conversionNotes?: string
  ): Promise<OpportunityWithRelations> {
    const opportunity = await prisma.$transaction(async (tx) => {
      // Get the lead with full data
      const lead = await tx.lead.findFirst({
        where: { id: leadId, tenantId, deletedAt: null },
        include: {
          contact: true,
          company: true,
          opportunities: true
        }
      });

      if (!lead) {
        throw new Error('Lead not found');
      }

      // Check if lead is already converted
      if (lead.isConverted) {
        throw new Error('Lead has already been converted to an opportunity');
      }

      // Check if lead already has opportunities
      if (lead.opportunities && lead.opportunities.length > 0) {
        throw new Error('Lead already has associated opportunities');
      }

      // Store original lead data for audit trail
      const originalLeadData = {
        id: lead.id,
        title: lead.title,
        description: lead.description,
        status: lead.status,
        score: lead.score,
        value: lead.value,
        priority: lead.priority,
        tags: lead.tags,
        customFields: lead.customFields,
        contactId: lead.contactId,
        companyId: lead.companyId,
        ownerId: lead.ownerId,
        createdAt: lead.createdAt
      };

      // Create opportunity from lead data
      const opportunityCreateData = createOpportunitySchema.parse({
        title: opportunityData.title || lead.title,
        description: opportunityData.description || lead.description,
        value: opportunityData.value || lead.value,
        stage: opportunityData.stage || 'qualification',
        probability: opportunityData.probability || 25,
        expectedCloseDate: opportunityData.expectedCloseDate || lead.expectedCloseDate?.toISOString(),
        tags: opportunityData.tags || (Array.isArray(lead.tags) ? lead.tags : []),
        source: opportunityData.source || 'lead_conversion',
        priority: opportunityData.priority || lead.priority,
        customFields: { ...lead.customFields, ...opportunityData.customFields },
        leadId: leadId,
        contactId: lead.contactId,
        companyId: lead.companyId,
        ownerId: opportunityData.ownerId || lead.ownerId,
      });

      // Create the opportunity
      const opportunity = await this.createOpportunity(tenantId, opportunityCreateData, convertedBy);

      // Create conversion history record
      await tx.leadConversionHistory.create({
        data: {
          tenantId,
          leadId,
          opportunityId: opportunity.id,
          convertedById: convertedBy,
          conversionType: 'manual',
          originalData: originalLeadData,
          conversionData: opportunityCreateData,
          notes: conversionNotes
        }
      });

      return opportunity;
    });

    // Mark lead as converted using the service to trigger notifications
    try {
      const { leadManagementService } = await import('./lead-management');
      await leadManagementService.markLeadAsConverted(leadId, tenantId, convertedBy, conversionNotes);
    } catch (error) {
      console.error('Failed to mark lead as converted with notifications:', error);
      // Don't fail the conversion if notification fails
    }

    return opportunity;
  }

  /**
   * Bulk convert leads to opportunities
   */
  async bulkConvertLeadsToOpportunities(
    leadIds: string[],
    tenantId: string,
    convertedBy: string,
    conversionData?: {
      stage?: string;
      priority?: string;
      ownerId?: string;
      notes?: string;
    }
  ): Promise<{ successful: ConversionResult[]; failed: ConversionResult[] }> {
    const successful: ConversionResult[] = [];
    const failed: ConversionResult[] = [];

    for (const leadId of leadIds) {
      try {
        const opportunity = await this.convertLeadToOpportunity(
          leadId,
          tenantId,
          {
            stage: conversionData?.stage,
            priority: conversionData?.priority as any,
            ownerId: conversionData?.ownerId
          },
          convertedBy,
          conversionData?.notes
        );

        successful.push({
          success: true,
          leadId,
          opportunityId: opportunity.id
        });
      } catch (error) {
        failed.push({
          success: false,
          leadId,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }

    return { successful, failed };
  }

  /**
   * Update opportunity stage
   */
  async updateOpportunityStage(
    opportunityId: string,
    tenantId: string,
    newStage: string,
    updatedBy: string,
    notes?: string
  ): Promise<OpportunityWithRelations> {
    const opportunity = await this.getOpportunity(opportunityId, tenantId);
    if (!opportunity) {
      throw new Error('Opportunity not found');
    }

    // Verify new stage exists
    const stage = await this.verifySalesStage(newStage, tenantId);

    // Update opportunity with new stage and probability
    const updatedOpportunity = await prisma.opportunity.update({
      where: { id: opportunityId, tenantId },
      data: {
        stage: newStage,
        probability: Math.floor((stage.probabilityMin + stage.probabilityMax) / 2),
        lastActivityDate: new Date(),
      },
      include: this.getIncludeOptions()
    });

    // Create stage history entry
    await this.createStageHistoryEntry(
      opportunityId,
      tenantId,
      opportunity.stage,
      newStage,
      updatedBy,
      notes
    );

    // Trigger stage change notification
    await this.triggerOpportunityStageChangeNotification(
      updatedOpportunity,
      tenantId,
      opportunity.stage,
      newStage
    );

    // Trigger automation for status change
    try {
      await opportunityTriggersService.handleOpportunityStatusChange({
        opportunityId,
        tenantId,
        oldStatus: opportunity.stage,
        newStatus: newStage,
        changedBy: updatedBy,
        changedAt: new Date(),
        metadata: {
          previousProbability: opportunity.probability,
          newProbability: updatedOpportunity.probability,
          notes
        }
      });
    } catch (triggerError) {
      console.error('Error executing opportunity triggers:', triggerError);
      // Don't fail the update if triggers fail
    }

    return updatedOpportunity;
  }

  /**
   * Get pipeline data for visual pipeline interface
   */
  async getPipelineData(tenantId: string, filters?: any): Promise<PipelineData> {
    // Get all sales stages for tenant
    const stages = await prisma.salesStage.findMany({
      where: { tenantId },
      orderBy: { orderIndex: 'asc' }
    });

    // Get opportunities grouped by stage
    const pipelineStages = await Promise.all(
      stages.map(async (stage) => {
        const opportunities = await prisma.opportunity.findMany({
          where: {
            tenantId,
            stage: stage.name,
            ...filters
          },
          include: this.getIncludeOptions(),
          orderBy: { createdAt: 'desc' }
        });

        const totalValue = opportunities.reduce((sum, opp) =>
          sum + (opp.value ? Number(opp.value) : 0), 0
        );

        return {
          id: stage.id,
          name: stage.name,
          description: stage.description,
          orderIndex: stage.orderIndex,
          color: stage.color,
          probabilityMin: stage.probabilityMin,
          probabilityMax: stage.probabilityMax,
          isClosed: stage.isClosed,
          isWon: stage.isWon,
          opportunities,
          totalValue,
          count: opportunities.length,
        };
      })
    );

    const totalValue = pipelineStages.reduce((sum, stage) => sum + stage.totalValue, 0);
    const totalCount = pipelineStages.reduce((sum, stage) => sum + stage.count, 0);
    const weightedValue = pipelineStages.reduce((sum, stage) =>
      sum + (stage.totalValue * (stage.probabilityMin + stage.probabilityMax) / 200), 0
    );

    return {
      stages: pipelineStages,
      totalValue,
      totalCount,
      weightedValue,
    };
  }

  /**
   * Get opportunity analytics and statistics
   */
  async getOpportunityStats(tenantId: string, dateRange?: { from: Date; to: Date }): Promise<OpportunityStats> {
    const where: any = { tenantId };

    if (dateRange) {
      where.createdAt = {
        gte: dateRange.from,
        lte: dateRange.to
      };
    }

    const [opportunities, stageStats, priorityStats] = await Promise.all([
      prisma.opportunity.findMany({ where }),
      prisma.opportunity.groupBy({
        by: ['stage'],
        where,
        _count: { _all: true },
        _sum: { value: true }
      }),
      prisma.opportunity.groupBy({
        by: ['priority'],
        where,
        _count: { _all: true },
        _sum: { value: true }
      })
    ]);

    const totalOpportunities = opportunities.length;
    const totalValue = opportunities.reduce((sum, opp) => sum + (Number(opp.value) || 0), 0);
    const averageValue = totalOpportunities > 0 ? totalValue / totalOpportunities : 0;

    // Calculate conversion rate (closed won / total)
    const closedWon = opportunities.filter(opp => opp.stage === 'Closed Won').length;
    const conversionRate = totalOpportunities > 0 ? (closedWon / totalOpportunities) * 100 : 0;

    // Stage distribution
    const stageDistribution = stageStats.map(stat => ({
      stage: stat.stage,
      count: stat._count._all,
      value: Number(stat._sum.value) || 0,
      percentage: totalOpportunities > 0 ? (stat._count._all / totalOpportunities) * 100 : 0
    }));

    // Priority distribution
    const priorityDistribution = priorityStats.map(stat => ({
      priority: stat.priority,
      count: stat._count._all,
      value: Number(stat._sum.value) || 0
    }));

    // Monthly trends (last 12 months)
    const monthlyTrends = await this.getMonthlyTrends(tenantId);

    return {
      totalOpportunities,
      totalValue,
      averageValue,
      conversionRate,
      stageDistribution,
      priorityDistribution,
      monthlyTrends
    };
  }

  /**
   * Bulk update opportunities
   */
  async bulkUpdateOpportunities(
    opportunityIds: string[],
    tenantId: string,
    updateData: Partial<z.infer<typeof updateOpportunitySchema>>,
    updatedBy: string
  ): Promise<{ updated: number; errors: string[] }> {
    const errors: string[] = [];
    let updated = 0;

    for (const opportunityId of opportunityIds) {
      try {
        await this.updateOpportunity(opportunityId, tenantId, updateData, updatedBy);
        updated++;
      } catch (error) {
        errors.push(`Failed to update opportunity ${opportunityId}: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }

    return { updated, errors };
  }

  /**
   * Search opportunities
   */
  async searchOpportunities(
    tenantId: string,
    query: string,
    limit: number = 10
  ): Promise<OpportunityWithRelations[]> {
    return await prisma.opportunity.findMany({
      where: {
        tenantId,
        OR: [
          { title: { contains: query, mode: 'insensitive' } },
          { description: { contains: query, mode: 'insensitive' } },
          { contact: { firstName: { contains: query, mode: 'insensitive' } } },
          { contact: { lastName: { contains: query, mode: 'insensitive' } } },
          { company: { name: { contains: query, mode: 'insensitive' } } },
        ]
      },
      include: this.getIncludeOptions(),
      take: limit,
      orderBy: { createdAt: 'desc' }
    });
  }

  private async getMonthlyTrends(tenantId: string) {
    // This would typically use raw SQL for better performance
    // For now, implementing a simplified version
    const months = [];
    const now = new Date();

    for (let i = 11; i >= 0; i--) {
      const date = new Date(now.getFullYear(), now.getMonth() - i, 1);
      const nextMonth = new Date(now.getFullYear(), now.getMonth() - i + 1, 1);

      const opportunities = await prisma.opportunity.findMany({
        where: {
          tenantId,
          createdAt: {
            gte: date,
            lt: nextMonth
          }
        }
      });

      const won = opportunities.filter(opp => opp.stage === 'Closed Won').length;
      const lost = opportunities.filter(opp => opp.stage === 'Closed Lost').length;
      const totalValue = opportunities.reduce((sum, opp) => sum + (Number(opp.value) || 0), 0);

      months.push({
        month: date.toISOString().substring(0, 7), // YYYY-MM format
        count: opportunities.length,
        value: totalValue,
        won,
        lost
      });
    }

    return months;
  }

  // Helper methods
  private getIncludeOptions() {
    return {
      lead: {
        select: {
          id: true,
          title: true,
          status: true,
        }
      },
      contact: {
        select: {
          id: true,
          firstName: true,
          lastName: true,
          email: true,
        }
      },
      company: {
        select: {
          id: true,
          name: true,
          industry: true,
        }
      },
      owner: {
        select: {
          id: true,
          firstName: true,
          lastName: true,
          email: true,
        }
      },
      createdBy: {
        select: {
          id: true,
          firstName: true,
          lastName: true,
          email: true,
        }
      },
      _count: {
        select: {
          proposals: true,
          projects: true,
          stageHistory: true,
        }
      }
    };
  }

  private async verifyUserInTenant(userId: string, tenantId: string) {
    return await prisma.user.findFirst({
      where: {
        id: userId,
        tenantUsers: {
          some: {
            tenantId,
            status: 'active'
          }
        }
      }
    });
  }

  private async verifyRelatedEntities(data: any, tenantId: string) {
    if (data.contactId) {
      const contact = await prisma.contact.findFirst({
        where: { id: data.contactId, tenantId }
      });
      if (!contact) {
        throw new Error('Contact not found');
      }
    }

    if (data.companyId) {
      const company = await prisma.company.findFirst({
        where: { id: data.companyId, tenantId }
      });
      if (!company) {
        throw new Error('Company not found');
      }
    }

    if (data.leadId) {
      const lead = await prisma.lead.findFirst({
        where: { id: data.leadId, tenantId }
      });
      if (!lead) {
        throw new Error('Lead not found');
      }
    }
  }

  private async verifySalesStage(stageName: string, tenantId: string) {
    const stage = await prisma.salesStage.findFirst({
      where: { name: stageName, tenantId }
    });
    if (!stage) {
      throw new Error(`Sales stage '${stageName}' not found for tenant`);
    }
    return stage;
  }

  private async createStageHistoryEntry(
    opportunityId: string,
    tenantId: string,
    fromStage: string | null,
    toStage: string,
    changedBy: string,
    notes?: string
  ) {
    await prisma.opportunityStageHistory.create({
      data: {
        opportunityId,
        tenantId,
        fromStage,
        toStage,
        changedById: changedBy,
        notes
      }
    });
  }

  /**
   * Trigger opportunity creation notification
   */
  private async triggerOpportunityCreationNotification(
    opportunity: OpportunityWithRelations,
    tenantId: string
  ): Promise<void> {
    try {
      const targetUsers = [];

      // Notify opportunity owner if assigned
      if (opportunity.ownerId) {
        targetUsers.push(opportunity.ownerId);
      }

      // Notify sales team members
      // TODO: Add logic to get sales team members with appropriate permissions

      // Create notification for each target user
      for (const userId of targetUsers) {
        await this.notificationService.createNotification({
          tenantId,
          userId,
          type: 'opportunity_created',
          category: 'lead',
          title: 'New Opportunity Created',
          message: `Opportunity "${opportunity.title}" has been created and assigned to you`,
          data: {
            opportunityId: opportunity.id,
            opportunityTitle: opportunity.title,
            opportunityStage: opportunity.stage,
            opportunityPriority: opportunity.priority,
            opportunityProbability: opportunity.probability,
            opportunityValue: opportunity.value?.toString(),
            contactName: opportunity.contact ? `${opportunity.contact.firstName} ${opportunity.contact.lastName}` : null,
            companyName: opportunity.company?.name,
            leadId: opportunity.leadId,
            createdBy: opportunity.createdBy ? `${opportunity.createdBy.firstName} ${opportunity.createdBy.lastName}` : 'System'
          },
          actionUrl: `/opportunities/${opportunity.id}`,
          actionLabel: 'View Opportunity'
        });
      }
    } catch (error) {
      console.error('Failed to send opportunity creation notification:', error);
      // Don't throw error to avoid breaking the main operation
    }
  }

  /**
   * Trigger opportunity stage change notification
   */
  private async triggerOpportunityStageChangeNotification(
    opportunity: OpportunityWithRelations,
    tenantId: string,
    previousStage: string,
    newStage: string
  ): Promise<void> {
    try {
      const targetUsers = [];

      // Notify opportunity owner
      if (opportunity.ownerId) {
        targetUsers.push(opportunity.ownerId);
      }

      // Notify sales team members
      // TODO: Add logic to get sales team members

      // Determine notification type and message based on stage change
      let notificationType = 'opportunity_stage_advanced';
      let title = 'Opportunity Stage Updated';
      let message = `Opportunity "${opportunity.title}" moved from ${previousStage} to ${newStage}`;

      // Check for won/lost stages
      const stage = await prisma.salesStage.findFirst({
        where: { name: newStage, tenantId }
      });

      if (stage?.isWon) {
        notificationType = 'opportunity_won';
        title = 'Opportunity Won! 🎉';
        message = `Congratulations! Opportunity "${opportunity.title}" has been won`;
      } else if (stage?.isClosed && !stage?.isWon) {
        notificationType = 'opportunity_lost';
        title = 'Opportunity Lost';
        message = `Opportunity "${opportunity.title}" has been marked as lost`;
      } else {
        // Check if this is a regression (moving to earlier stage)
        const [prevStageData, newStageData] = await Promise.all([
          prisma.salesStage.findFirst({ where: { name: previousStage, tenantId } }),
          prisma.salesStage.findFirst({ where: { name: newStage, tenantId } })
        ]);

        if (prevStageData && newStageData && newStageData.orderIndex < prevStageData.orderIndex) {
          notificationType = 'opportunity_stage_regressed';
          title = 'Opportunity Stage Moved Back';
          message = `Opportunity "${opportunity.title}" moved back from ${previousStage} to ${newStage}`;
        }
      }

      for (const userId of targetUsers) {
        await this.notificationService.createNotification({
          tenantId,
          userId,
          type: notificationType,
          category: 'lead',
          title,
          message,
          data: {
            opportunityId: opportunity.id,
            opportunityTitle: opportunity.title,
            previousStage,
            newStage,
            opportunityProbability: opportunity.probability,
            opportunityValue: opportunity.value?.toString(),
            contactName: opportunity.contact ? `${opportunity.contact.firstName} ${opportunity.contact.lastName}` : null,
            companyName: opportunity.company?.name
          },
          actionUrl: `/opportunities/${opportunity.id}`,
          actionLabel: 'View Opportunity'
        });
      }
    } catch (error) {
      console.error('Failed to send opportunity stage change notification:', error);
    }
  }

  /**
   * Handle notifications for opportunity updates
   */
  private async handleOpportunityUpdateNotifications(
    updatedOpportunity: OpportunityWithRelations,
    previousOpportunity: OpportunityWithRelations,
    tenantId: string
  ): Promise<void> {
    try {
      // Check for owner change
      if (updatedOpportunity.ownerId !== previousOpportunity.ownerId) {
        await this.triggerOpportunityAssignmentNotification(
          updatedOpportunity,
          tenantId,
          previousOpportunity.ownerId
        );
      }

      // Check for priority change to urgent
      if (updatedOpportunity.priority === 'urgent' && previousOpportunity.priority !== 'urgent') {
        await this.triggerUrgentOpportunityNotification(updatedOpportunity, tenantId);
      }

      // Check for significant value increase
      const previousValue = previousOpportunity.value || 0;
      const currentValue = updatedOpportunity.value || 0;
      if (currentValue > previousValue * 1.5 && currentValue > 50000) { // 50% increase and over $50k
        await this.triggerHighValueOpportunityNotification(updatedOpportunity, tenantId);
      }
    } catch (error) {
      console.error('Failed to handle opportunity update notifications:', error);
    }
  }

  /**
   * Trigger opportunity assignment notification
   */
  private async triggerOpportunityAssignmentNotification(
    opportunity: OpportunityWithRelations,
    tenantId: string,
    previousOwnerId?: string | null
  ): Promise<void> {
    try {
      // Notify new owner
      if (opportunity.ownerId) {
        await this.notificationService.createNotification({
          tenantId,
          userId: opportunity.ownerId,
          type: 'opportunity_assigned',
          category: 'lead',
          title: 'Opportunity Assigned to You',
          message: `Opportunity "${opportunity.title}" has been assigned to you`,
          data: {
            opportunityId: opportunity.id,
            opportunityTitle: opportunity.title,
            opportunityStage: opportunity.stage,
            opportunityPriority: opportunity.priority,
            opportunityValue: opportunity.value?.toString(),
            contactName: opportunity.contact ? `${opportunity.contact.firstName} ${opportunity.contact.lastName}` : null,
            companyName: opportunity.company?.name,
            previousOwner: previousOwnerId
          },
          actionUrl: `/opportunities/${opportunity.id}`,
          actionLabel: 'View Opportunity'
        });
      }

      // Notify previous owner if different
      if (previousOwnerId && previousOwnerId !== opportunity.ownerId) {
        await this.notificationService.createNotification({
          tenantId,
          userId: previousOwnerId,
          type: 'opportunity_reassigned',
          category: 'lead',
          title: 'Opportunity Reassigned',
          message: `Opportunity "${opportunity.title}" has been reassigned to another team member`,
          data: {
            opportunityId: opportunity.id,
            opportunityTitle: opportunity.title,
            newOwner: opportunity.owner ? `${opportunity.owner.firstName} ${opportunity.owner.lastName}` : 'Unknown'
          },
          actionUrl: `/opportunities/${opportunity.id}`,
          actionLabel: 'View Opportunity'
        });
      }
    } catch (error) {
      console.error('Failed to send opportunity assignment notification:', error);
    }
  }

  /**
   * Trigger urgent opportunity notification
   */
  private async triggerUrgentOpportunityNotification(
    opportunity: OpportunityWithRelations,
    tenantId: string
  ): Promise<void> {
    try {
      const targetUsers = [];
      if (opportunity.ownerId) {
        targetUsers.push(opportunity.ownerId);
      }

      // Notify managers
      // TODO: Add logic to get managers

      for (const userId of targetUsers) {
        await this.notificationService.createNotification({
          tenantId,
          userId,
          type: 'urgent_opportunity_alert',
          category: 'lead',
          title: 'Urgent Opportunity Alert',
          message: `Urgent opportunity "${opportunity.title}" requires immediate attention`,
          data: {
            opportunityId: opportunity.id,
            opportunityTitle: opportunity.title,
            opportunityPriority: opportunity.priority,
            opportunityStage: opportunity.stage,
            expectedCloseDate: opportunity.expectedCloseDate?.toISOString(),
            contactName: opportunity.contact ? `${opportunity.contact.firstName} ${opportunity.contact.lastName}` : null,
            companyName: opportunity.company?.name
          },
          actionUrl: `/opportunities/${opportunity.id}`,
          actionLabel: 'View Opportunity'
        });
      }
    } catch (error) {
      console.error('Failed to send urgent opportunity notification:', error);
    }
  }

  /**
   * Trigger high-value opportunity notification
   */
  private async triggerHighValueOpportunityNotification(
    opportunity: OpportunityWithRelations,
    tenantId: string
  ): Promise<void> {
    try {
      // Notify managers and senior sales team
      const targetUsers = [];
      if (opportunity.ownerId) {
        targetUsers.push(opportunity.ownerId);
      }

      // TODO: Add logic to get managers and senior sales team members

      for (const userId of targetUsers) {
        await this.notificationService.createNotification({
          tenantId,
          userId,
          type: 'high_value_opportunity_detected',
          category: 'lead',
          title: 'High-Value Opportunity Alert',
          message: `High-value opportunity "${opportunity.title}" worth $${opportunity.value} requires attention`,
          data: {
            opportunityId: opportunity.id,
            opportunityTitle: opportunity.title,
            opportunityValue: opportunity.value?.toString(),
            opportunityStage: opportunity.stage,
            opportunityProbability: opportunity.probability,
            contactName: opportunity.contact ? `${opportunity.contact.firstName} ${opportunity.contact.lastName}` : null,
            companyName: opportunity.company?.name
          },
          actionUrl: `/opportunities/${opportunity.id}`,
          actionLabel: 'View Opportunity'
        });
      }
    } catch (error) {
      console.error('Failed to send high-value opportunity notification:', error);
    }
  }
}

export const opportunityManagementService = new OpportunityManagementService();
