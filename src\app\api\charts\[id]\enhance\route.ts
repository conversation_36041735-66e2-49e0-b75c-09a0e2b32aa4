import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { hasPermission } from '@/lib/permissions';
import { PermissionResource, PermissionAction } from '@/types';
import { prisma } from '@/lib/prisma';
import { aiService } from '@/services/ai-service';
import { geminiFileService } from '@/services/gemini-file-service';
import { z } from 'zod';
import { detectLanguage, getLanguageInstructions, validateLanguageConsistency, getLanguageName, type SupportedLanguage } from '@/utils/language-detection';
import { GeminiFileContext } from '@/types/proposal';

// Request validation schema
const enhanceChartSchema = z.object({
  enhancementType: z.enum(['make_shorter', 'make_longer', 'change_tone', 'custom_enhance']),
  toneType: z.enum(['professional', 'casual', 'technical']).optional(),
  customPrompt: z.string().optional(),
  selectedFileIds: z.array(z.string()).optional(),
  locale: z.enum(['en', 'ar']).optional()
});

export type ChartEnhancementType = 'make_shorter' | 'make_longer' | 'change_tone' | 'custom_enhance';
export type ChartToneType = 'professional' | 'casual' | 'technical';

interface ChartEnhancementResponse {
  originalContent: string;
  enhancedContent: string;
  originalDescription?: string;
  enhancedDescription?: string;
  enhancementType: ChartEnhancementType;
  toneType?: ChartToneType;
  customPrompt?: string;
  explanation: string;
  tokensUsed: number;
  detectedLanguage: SupportedLanguage;
  languageConsistency: {
    isConsistent: boolean;
    originalLang: SupportedLanguage;
    enhancedLang: SupportedLanguage;
    warning?: string;
  };
}

/**
 * POST /api/charts/[id]/enhance
 * Enhance chart content using AI
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user's current tenant
    const currentTenant = session.user.tenants?.[0];
    if (!currentTenant) {
      return NextResponse.json({ error: 'No tenant access' }, { status: 403 });
    }

    // Check permission for chart updating
    const hasAccess = await hasPermission(
      PermissionResource.PROPOSALS,
      PermissionAction.UPDATE,
      currentTenant.id
    );

    if (!hasAccess) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const { id: chartId } = params;
    const body = await request.json();
    
    // Validate request data
    const validatedData = enhanceChartSchema.parse(body);

    // Verify chart exists and belongs to tenant
    const existingChart = await prisma.proposalChart.findFirst({
      where: {
        id: chartId,
        tenantId: currentTenant.id
      },
      include: {
        proposal: {
          include: {
            opportunity: true
          }
        }
      }
    });

    if (!existingChart) {
      return NextResponse.json(
        { error: 'Chart not found or access denied' },
        { status: 404 }
      );
    }

    if (!existingChart.mermaidCode) {
      return NextResponse.json(
        { error: 'Chart has no content to enhance' },
        { status: 400 }
      );
    }

    // Use provided locale or detect the language of the original content
    const proposalLocale = validatedData.locale || (existingChart.proposal.locale as 'en' | 'ar') || 'en';
    const detectedLanguage = proposalLocale === 'ar' ? 'ar' : detectLanguage(existingChart.mermaidCode + ' ' + (existingChart.description || ''));
    console.log(`Using language for chart ${chartId}:`, detectedLanguage, `(proposal locale: ${proposalLocale})`);

    // Get file contexts if file IDs are provided
    let fileContexts: GeminiFileContext[] = [];
    let fileUris: string[] = [];
    
    if (validatedData.selectedFileIds && validatedData.selectedFileIds.length > 0) {
      try {
        // Get proposal's Gemini file URIs
        const proposal = await prisma.proposal.findFirst({
          where: {
            id: existingChart.proposalId,
            tenantId: currentTenant.id
          }
        });

        const geminiFileUris = (proposal?.geminiFileUris as Record<string, string>) || {};
        
        // Get file contexts for the selected files
        fileContexts = await geminiFileService.getFileContexts(
          validatedData.selectedFileIds,
          currentTenant.id,
          geminiFileUris
        );

        // Extract active file URIs
        fileUris = fileContexts
          .filter(ctx => ctx.fileUri && ctx.uploadStatus === 'active')
          .map(ctx => ctx.fileUri);

        console.log(`Using ${fileUris.length} file contexts for chart enhancement`);
      } catch (error) {
        console.warn('Failed to load file contexts for chart enhancement:', error);
        // Continue without file context rather than failing
      }
    }

    // Generate enhancement prompt for chart
    const enhancementPrompt = generateChartEnhancementPrompt(
      existingChart.mermaidCode,
      existingChart.description || '',
      existingChart.title,
      validatedData.enhancementType,
      validatedData.toneType,
      detectedLanguage,
      validatedData.customPrompt,
      fileContexts
    );

    // Call AI service for enhancement
    const aiResponse = await Promise.race([
      aiService.generateResponse({
        prompt: enhancementPrompt,
        systemPrompt: getChartEnhancementSystemPrompt(detectedLanguage, validatedData.enhancementType),
        fileUris: fileUris.length > 0 ? fileUris : undefined,
        context: {
          chartType: existingChart.chartType,
          chartTitle: existingChart.title,
          proposalTitle: existingChart.proposal.title,
          opportunityName: existingChart.proposal.opportunity?.description || 'Unknown Opportunity',
          enhancementType: validatedData.enhancementType,
          toneType: validatedData.toneType,
          fileContexts
        },
        tenantId: currentTenant.id,
        userId: session.user.id,
        feature: 'chart_enhancement'
      }, {
        temperature: 0.6,
        maxTokens: 1500,
        model: 'gemini-2.0-flash'
      }),
      new Promise<never>((_, reject) =>
        setTimeout(() => reject(new Error('AI request timeout')), 30000) // 30 second timeout for charts
      )
    ]);

    // Parse the enhanced content
    const { enhancedMermaidCode, enhancedDescription } = parseEnhancedChartContent(aiResponse.content);

    // Log the final Mermaid code for debugging
    console.log('Final enhanced Mermaid code:');
    console.log(enhancedMermaidCode);
    console.log('---');

    // Validate the final code
    const validationResult = validateFinalMermaidCode(enhancedMermaidCode);
    if (!validationResult.isValid) {
      console.warn('Mermaid validation warnings:', validationResult.warnings);
    }

    // Validate language consistency
    const combinedOriginal = existingChart.mermaidCode + ' ' + (existingChart.description || '');
    const combinedEnhanced = enhancedMermaidCode + ' ' + (enhancedDescription || '');
    const languageConsistency = validateLanguageConsistency(combinedOriginal, combinedEnhanced);

    // Log language validation results
    if (!languageConsistency.isConsistent) {
      console.warn(`Language consistency warning for chart ${chartId}:`, languageConsistency.warning);
    }

    const response: ChartEnhancementResponse = {
      originalContent: existingChart.mermaidCode,
      enhancedContent: enhancedMermaidCode,
      originalDescription: existingChart.description || undefined,
      enhancedDescription: enhancedDescription || undefined,
      enhancementType: validatedData.enhancementType,
      toneType: validatedData.toneType,
      customPrompt: validatedData.customPrompt,
      explanation: getChartEnhancementExplanation(validatedData.enhancementType, validatedData.toneType, detectedLanguage, validatedData.customPrompt),
      tokensUsed: aiResponse.tokensUsed,
      detectedLanguage,
      languageConsistency
    };

    return NextResponse.json(response);

  } catch (error) {
    console.error('Chart enhancement error:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request data', details: error.errors },
        { status: 400 }
      );
    }

    // Handle timeout errors specifically
    if (error instanceof Error && error.message.includes('timeout')) {
      return NextResponse.json(
        { error: 'Chart enhancement request timed out. Please try again.' },
        { status: 408 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to enhance chart content. Please try again.' },
      { status: 500 }
    );
  }
}

function generateChartEnhancementPrompt(
  mermaidCode: string,
  description: string,
  title: string,
  enhancementType: ChartEnhancementType,
  toneType: ChartToneType | undefined,
  detectedLanguage: SupportedLanguage,
  customPrompt?: string,
  fileContexts?: GeminiFileContext[]
): string {
  // For custom enhancements, check if user is requesting a language change
  const isLanguageChangeRequest = enhancementType === 'custom_enhance' && customPrompt && (
    customPrompt.toLowerCase().includes('arabic') ||
    customPrompt.toLowerCase().includes('عربي') ||
    customPrompt.toLowerCase().includes('باللغه العربيه') ||
    customPrompt.toLowerCase().includes('باللغة العربية') ||
    customPrompt.toLowerCase().includes('اعاده كتابه') ||
    customPrompt.toLowerCase().includes('إعادة كتابة') ||
    customPrompt.toLowerCase().includes('english') ||
    customPrompt.toLowerCase().includes('translate') ||
    customPrompt.toLowerCase().includes('rewrite in')
  );

  // Get language-specific instructions, but be flexible for custom enhancements
  const languageInstructions = enhancementType === 'custom_enhance'
    ? (isLanguageChangeRequest 
        ? 'Follow the user\'s language instructions precisely. If they ask to rewrite in a specific language, do so completely.' 
        : 'Maintain the original language unless the user specifically requests otherwise.')
    : getLanguageInstructions(detectedLanguage);

  let basePrompt = '';
  const contentToEnhance = `Chart Title: ${title}
Mermaid Code:
${mermaidCode}
${description ? `\nDescription: ${description}` : ''}`;

  switch (enhancementType) {
    case 'make_shorter':
      basePrompt = `Simplify this Mermaid chart by reducing complexity while keeping essential elements:

${contentToEnhance}

Make the chart 20-30% simpler by removing non-essential nodes or relationships.

${languageInstructions}`;
      break;

    case 'make_longer':
      basePrompt = `Expand this Mermaid chart with additional relevant details and components:

${contentToEnhance}

Add 30-50% more relevant nodes, relationships, or details while maintaining clarity.

${languageInstructions}`;
      break;

    case 'change_tone':
      const toneMap = {
        professional: 'formal business terminology',
        casual: 'simple, accessible language',
        technical: 'detailed technical terminology'
      };

      basePrompt = `Rewrite this Mermaid chart using ${toneMap[toneType || 'professional']}:

${contentToEnhance}

Adjust the labels and descriptions while maintaining the chart structure.

${languageInstructions}`;
      break;

    case 'custom_enhance':
      basePrompt = `Instructions: ${customPrompt || 'Improve the chart quality and clarity'}

Chart to enhance:
${contentToEnhance}

${languageInstructions}`;
      break;
  }

  // Add file context if available
  if (fileContexts && fileContexts.length > 0) {
    const fileContext = fileContexts
      .filter(ctx => ctx.uploadStatus === 'active')
      .map(ctx => `- ${ctx.name}`)
      .join('\n');
    
    if (fileContext) {
      basePrompt += `\n\nReference Documents Available:\n${fileContext}\n\nUse information from these documents to enhance the chart where relevant.`;
    }
  }

  return basePrompt;
}

function getChartEnhancementSystemPrompt(detectedLanguage: SupportedLanguage, enhancementType?: ChartEnhancementType): string {
  const isCustomEnhancement = enhancementType === 'custom_enhance';

  const languageInstructions = isCustomEnhancement
    ? `Original content language: ${getLanguageName(detectedLanguage)}. Follow user instructions for language - if they request a specific language, use it completely.`
    : getLanguageInstructions(detectedLanguage);

  return `You are a Mermaid diagram expert and content enhancer. Your task is to improve chart content while maintaining accuracy and following user instructions precisely.

${languageInstructions}

CRITICAL MERMAID FORMATTING RULES:
- Each Mermaid statement MUST be on a separate line
- Use proper line breaks between all nodes, connections, and styling
- Never put multiple statements on the same line
- Always include proper indentation for subgraphs
- Node IDs must be simple alphanumeric (A, B, C, A1, B2, etc.) - NO special characters
- Use square brackets [Label] for rectangular nodes
- Use parentheses (Label) for rounded nodes
- Use curly braces {Label} for diamond/decision nodes
- Always escape special characters in labels with quotes if needed
- Connections must use --> or --- syntax only
- Subgraphs must have proper "end" statements

RESPONSE FORMAT:
Return the response in this exact format:
MERMAID_CODE:
[enhanced mermaid code with proper line breaks]

DESCRIPTION:
[enhanced description here]

MERMAID SYNTAX EXAMPLES:

✅ CORRECT FORMAT:
graph TD
    A[Component A]
    B[Component B]
    C{Decision Point}
    A --> B
    B --> C

    subgraph "Database Layer"
        D[Primary DB]
        E[Cache]
    end

    C --> D
    style A fill:#f9f,stroke:#333,stroke-width:2px

❌ INCORRECT FORMAT (DO NOT DO THIS):
graph TD A[Component A] B[Component B] A --> B subgraph "Database Layer" D[Primary DB] E[Cache] end

✅ VALID NODE ID EXAMPLES:
- A, B, C (single letters)
- A1, B2, C3 (letter + number)
- Node1, Node2 (word + number)

❌ INVALID NODE IDS (DO NOT USE):
- A-B (hyphens)
- A.B (dots)
- A B (spaces)
- 1A (starting with number)

✅ CORRECT SUBGRAPH FORMAT:
graph TD
    subgraph "Frontend"
        A[Web App]
        B[Mobile App]
    end

    subgraph "Backend"
        C[API Gateway]
        D[Microservice]
    end

    A --> C
    B --> C
    C --> D

IMPORTANT RULES:
- Do NOT add phrases like "Here's the enhanced version" or explanations
- Ensure the Mermaid syntax is valid and properly formatted with line breaks
- Each node definition and connection must be on its own line
- Follow the user's instructions exactly, including language changes if requested
- Preserve all factual information and technical accuracy
- Focus on the specific enhancement requested above all else
${isCustomEnhancement ? '- For custom enhancements, prioritize user instructions over language consistency' : '- Maintain language consistency unless explicitly instructed otherwise'}

Detected original language: ${getLanguageName(detectedLanguage)}`;
}

function parseEnhancedChartContent(aiResponse: string): { enhancedMermaidCode: string; enhancedDescription: string } {
  // Look for the structured format
  const mermaidMatch = aiResponse.match(/MERMAID_CODE:\s*([\s\S]*?)(?=DESCRIPTION:|$)/);
  const descriptionMatch = aiResponse.match(/DESCRIPTION:\s*([\s\S]*?)$/);

  let enhancedMermaidCode = '';
  let enhancedDescription = '';

  if (mermaidMatch) {
    enhancedMermaidCode = mermaidMatch[1].trim();

    // Clean up the mermaid code to ensure proper formatting
    enhancedMermaidCode = cleanMermaidCode(enhancedMermaidCode);
  } else {
    // Fallback: try to extract mermaid code from the response
    const lines = aiResponse.split('\n');
    const mermaidLines = lines.filter(line =>
      line.includes('graph') ||
      line.includes('flowchart') ||
      line.includes('-->') ||
      line.includes('---') ||
      line.match(/^\s*[A-Z]\d*\[/) ||
      line.includes('subgraph') ||
      line.includes('end') ||
      line.includes('style')
    );
    enhancedMermaidCode = cleanMermaidCode(mermaidLines.join('\n').trim());
  }

  if (descriptionMatch) {
    enhancedDescription = descriptionMatch[1].trim();
  }

  return { enhancedMermaidCode, enhancedDescription };
}

function cleanMermaidCode(mermaidCode: string): string {
  if (!mermaidCode) return '';

  console.log('Original Mermaid code:', mermaidCode);

  try {
    // Step 1: Basic cleanup
    let cleaned = mermaidCode
      .replace(/```mermaid\s*/g, '')
      .replace(/```\s*/g, '')
      .trim();

    // Step 2: Validate and fix the code
    cleaned = validateAndFixMermaidSyntax(cleaned);

    console.log('Cleaned Mermaid code:', cleaned);
    return cleaned;
  } catch (error) {
    console.error('Error cleaning Mermaid code:', error);
    // Return a simple fallback diagram
    return `graph TD
    A[Start]
    B[Process]
    C[End]
    A --> B
    B --> C`;
  }
}

function validateAndFixMermaidSyntax(code: string): string {
  const lines = code.split('\n');
  const cleanedLines: string[] = [];
  let indentLevel = 0;
  let nodeIdCounter = 1;
  const nodeIdMap = new Map<string, string>();

  for (let i = 0; i < lines.length; i++) {
    let line = lines[i].trim();

    // Skip empty lines
    if (!line) continue;

    // Handle graph declaration
    if (line.match(/^(graph|flowchart)\s+/)) {
      // Ensure proper graph declaration
      if (!line.match(/^(graph|flowchart)\s+(TD|TB|BT|RL|LR)$/)) {
        line = 'graph TD'; // Default to top-down
      }
      cleanedLines.push(line);
      continue;
    }

    // Handle subgraph
    if (line.startsWith('subgraph')) {
      // Fix subgraph syntax
      const subgraphMatch = line.match(/subgraph\s*(.*)$/);
      if (subgraphMatch) {
        const title = subgraphMatch[1].trim();
        // Ensure title is quoted if it contains spaces
        const quotedTitle = title && !title.startsWith('"') && title.includes(' ')
          ? `"${title}"`
          : title;
        cleanedLines.push('    '.repeat(indentLevel) + `subgraph ${quotedTitle}`);
        indentLevel++;
      }
      continue;
    }

    // Handle end
    if (line === 'end') {
      indentLevel = Math.max(0, indentLevel - 1);
      cleanedLines.push('    '.repeat(indentLevel) + 'end');
      continue;
    }

    // Handle style statements
    if (line.startsWith('style ')) {
      cleanedLines.push('    '.repeat(indentLevel) + line);
      continue;
    }

    // Process the line for nodes and connections
    const processedLine = processLineForNodesAndConnections(line, nodeIdMap, nodeIdCounter);
    if (processedLine.lines.length > 0) {
      for (const processedSubLine of processedLine.lines) {
        cleanedLines.push('    '.repeat(indentLevel) + processedSubLine);
      }
      nodeIdCounter = processedLine.nodeIdCounter;
    }
  }

  return cleanedLines.join('\n');
}

function processLineForNodesAndConnections(line: string, nodeIdMap: Map<string, string>, nodeIdCounter: number): { lines: string[], nodeIdCounter: number } {
  const result: string[] = [];

  // Handle multiple statements on one line by splitting on common patterns
  const statements = splitLineIntoStatements(line);

  for (const statement of statements) {
    const trimmed = statement.trim();
    if (!trimmed) continue;

    // Process node definitions
    if (trimmed.match(/^[A-Za-z]\w*[\[\(\{]/)) {
      const fixedNode = fixNodeDefinition(trimmed, nodeIdMap, nodeIdCounter);
      if (fixedNode.node) {
        result.push(fixedNode.node);
        nodeIdCounter = fixedNode.nodeIdCounter;
      }
    }
    // Process connections
    else if (trimmed.includes('-->') || trimmed.includes('---')) {
      const fixedConnection = fixConnectionDefinition(trimmed, nodeIdMap);
      if (fixedConnection) {
        result.push(fixedConnection);
      }
    }
    // Process other valid statements
    else {
      result.push(trimmed);
    }
  }

  return { lines: result, nodeIdCounter };
}

function splitLineIntoStatements(line: string): string[] {
  // Split on multiple patterns that indicate separate statements
  const statements: string[] = [];

  // Pattern 1: Multiple node definitions like "A[Label] B[Label]"
  const nodePattern = /([A-Za-z]\w*[\[\(\{][^\]\)\}]*[\]\)\}])/g;
  const nodes = line.match(nodePattern) || [];

  // Pattern 2: Connections
  const connectionPattern = /([A-Za-z]\w*\s*(?:-->|---)\s*[A-Za-z]\w*(?:[\[\(\{][^\]\)\}]*[\]\)\}])?)/g;
  const connections = line.match(connectionPattern) || [];

  // If we found multiple nodes or connections, split them
  if (nodes.length > 1) {
    statements.push(...nodes);
    statements.push(...connections);
  } else if (connections.length > 0) {
    statements.push(...connections);
  } else {
    statements.push(line);
  }

  return statements.filter(s => s.trim());
}

function fixNodeDefinition(node: string, nodeIdMap: Map<string, string>, nodeIdCounter: number): { node: string | null, nodeIdCounter: number } {
  // Extract node ID and label
  const nodeMatch = node.match(/^([A-Za-z]\w*)([\[\(\{])([^\]\)\}]*)([\]\)\}])/);
  if (!nodeMatch) return { node: null, nodeIdCounter };

  let [, originalId, openBracket, label] = nodeMatch;

  // Ensure node ID is valid (alphanumeric only)
  let nodeId = originalId.replace(/[^A-Za-z0-9]/g, '');
  if (!nodeId || !nodeId.match(/^[A-Za-z]/)) {
    nodeId = `Node${nodeIdCounter++}`;
  }

  // Store mapping for connections
  nodeIdMap.set(originalId, nodeId);

  // Clean the label
  const cleanLabel = label.replace(/"/g, '').trim();

  // Ensure bracket consistency
  const bracketPairs: { [key: string]: string } = {
    '[': ']',
    '(': ')',
    '{': '}'
  };

  const correctCloseBracket = bracketPairs[openBracket] || ']';

  return {
    node: `${nodeId}${openBracket}${cleanLabel}${correctCloseBracket}`,
    nodeIdCounter
  };
}

function fixConnectionDefinition(connection: string, nodeIdMap: Map<string, string>): string | null {
  // Extract connection parts
  const connectionMatch = connection.match(/^([A-Za-z]\w*)\s*(-->|---)\s*([A-Za-z]\w*(?:[\[\(\{][^\]\)\}]*[\]\)\}])?)/);
  if (!connectionMatch) return null;

  let [, fromId, arrow, toId] = connectionMatch;

  // Clean node IDs
  fromId = fromId.replace(/[^A-Za-z0-9]/g, '');

  // Handle case where toId includes a label
  const toMatch = toId.match(/^([A-Za-z]\w*)/);
  if (toMatch) {
    toId = toMatch[1].replace(/[^A-Za-z0-9]/g, '');
  }

  // Use mapped IDs if available
  const mappedFromId = nodeIdMap.get(fromId) || fromId;
  const mappedToId = nodeIdMap.get(toId) || toId;

  return `${mappedFromId} ${arrow} ${mappedToId}`;
}

function validateFinalMermaidCode(code: string): { isValid: boolean; warnings: string[] } {
  const warnings: string[] = [];
  const lines = code.split('\n');

  // Check for common issues
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim();
    const lineNumber = i + 1;

    if (!line) continue;

    // Check for invalid node IDs
    const nodeMatch = line.match(/^([A-Za-z]\w*)([\[\(\{])/);
    if (nodeMatch) {
      const nodeId = nodeMatch[1];
      if (!nodeId.match(/^[A-Za-z][A-Za-z0-9]*$/)) {
        warnings.push(`Line ${lineNumber}: Invalid node ID "${nodeId}" - should be alphanumeric starting with letter`);
      }
    }

    // Check for unmatched brackets
    const openBrackets = (line.match(/[\[\(\{]/g) || []).length;
    const closeBrackets = (line.match(/[\]\)\}]/g) || []).length;
    if (openBrackets !== closeBrackets) {
      warnings.push(`Line ${lineNumber}: Unmatched brackets in "${line}"`);
    }

    // Check for invalid connections
    if (line.includes('-->') || line.includes('---')) {
      const connectionMatch = line.match(/([A-Za-z]\w*)\s*(-->|---)\s*([A-Za-z]\w*)/);
      if (!connectionMatch) {
        warnings.push(`Line ${lineNumber}: Invalid connection syntax in "${line}"`);
      }
    }
  }

  return {
    isValid: warnings.length === 0,
    warnings
  };
}

function getChartEnhancementExplanation(
  enhancementType: ChartEnhancementType,
  toneType: ChartToneType | undefined,
  detectedLanguage: SupportedLanguage,
  customPrompt?: string
): string {
  const languageName = getLanguageName(detectedLanguage);

  switch (enhancementType) {
    case 'make_shorter':
      return `Chart simplified to focus on essential components in ${languageName}.`;
    case 'make_longer':
      return `Chart expanded with additional relevant details and components in ${languageName}.`;
    case 'change_tone':
      return `Chart terminology adjusted to ${toneType} style in ${languageName} while preserving structure.`;
    case 'custom_enhance':
      const instruction = customPrompt ? ` following the instruction: "${customPrompt.substring(0, 100)}${customPrompt.length > 100 ? '...' : ''}"` : '';
      return `Chart enhanced using custom AI instructions${instruction} in ${languageName}.`;
    default:
      return `Chart enhanced using AI optimization in ${languageName}.`;
  }
}
