'use client';

import { useCallback } from 'react';
import { useSession } from 'next-auth/react';
import { usePermissionContext } from '@/components/providers/permission-provider';
import { useAuth } from './use-auth';

export interface UsePermissionRefreshReturn {
  refreshUserPermissions: () => Promise<void>;
  refreshAfterRoleChange: (userId?: string) => Promise<void>;
  clearPermissionCache: () => Promise<void>;
}

/**
 * Hook for refreshing user permissions after role changes
 */
export function usePermissionRefresh(): UsePermissionRefreshReturn {
  const { update } = useSession();
  const { refreshSession } = useAuth();
  const { refreshPermissions } = usePermissionContext();

  /**
   * Refresh current user's permissions
   */
  const refreshUserPermissions = useCallback(async () => {
    try {
      // Refresh session to get updated user data
      await refreshSession();
      
      // Refresh permissions from the permission provider
      await refreshPermissions();
      
      // Force NextAuth session update
      await update();
    } catch (error) {
      console.error('Failed to refresh user permissions:', error);
    }
  }, [refreshSession, refreshPermissions, update]);

  /**
   * Refresh permissions after a role change
   * If userId is provided and matches current user, refresh immediately
   */
  const refreshAfterRoleChange = useCallback(async (userId?: string) => {
    try {
      // Always refresh permissions as roles might affect current user indirectly
      await refreshUserPermissions();
      
      // If specific user ID provided, we could implement user-specific refresh logic here
      if (userId) {
        // For now, just refresh current user's permissions
        // In the future, this could trigger a WebSocket event or similar
        console.log(`Role changed for user ${userId}, refreshing permissions`);
      }
    } catch (error) {
      console.error('Failed to refresh permissions after role change:', error);
    }
  }, [refreshUserPermissions]);

  /**
   * Clear permission cache via API
   */
  const clearPermissionCache = useCallback(async () => {
    try {
      const response = await fetch('/api/debug/clear-cache', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Failed to clear cache');
      }

      // Refresh permissions after clearing cache
      await refreshUserPermissions();
    } catch (error) {
      console.error('Failed to clear permission cache:', error);
      throw error;
    }
  }, [refreshUserPermissions]);

  return {
    refreshUserPermissions,
    refreshAfterRoleChange,
    clearPermissionCache,
  };
}

/**
 * Hook for components that modify user roles
 * Automatically refreshes permissions after role operations
 */
export function useRoleOperations() {
  const { refreshAfterRoleChange } = usePermissionRefresh();

  const assignRole = useCallback(async (userId: string, roleId: string, expiresAt?: Date) => {
    try {
      const response = await fetch('/api/roles/assign', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          userId,
          roleId,
          expiresAt: expiresAt?.toISOString()
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to assign role');
      }

      // Refresh permissions after successful role assignment
      await refreshAfterRoleChange(userId);

      return { success: true };
    } catch (error) {
      console.error('Failed to assign role:', error);
      throw error;
    }
  }, [refreshAfterRoleChange]);

  const removeRole = useCallback(async (userId: string, roleId: string) => {
    try {
      const response = await fetch('/api/roles/assign', {
        method: 'DELETE',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ userId, roleId }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to remove role');
      }

      // Refresh permissions after successful role removal
      await refreshAfterRoleChange(userId);

      return { success: true };
    } catch (error) {
      console.error('Failed to remove role:', error);
      throw error;
    }
  }, [refreshAfterRoleChange]);

  const updateRolePermissions = useCallback(async (roleId: string, permissions: string[]) => {
    try {
      const response = await fetch(`/api/roles/${roleId}/permissions`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ permissions }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update role permissions');
      }

      // Refresh permissions after successful role permission update
      // This affects all users with this role
      await refreshAfterRoleChange();

      return { success: true };
    } catch (error) {
      console.error('Failed to update role permissions:', error);
      throw error;
    }
  }, [refreshAfterRoleChange]);

  return {
    assignRole,
    removeRole,
    updateRolePermissions,
  };
}
