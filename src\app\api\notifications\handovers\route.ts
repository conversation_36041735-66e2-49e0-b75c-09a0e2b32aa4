import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { handoverNotificationService } from '@/services/handover-notifications';
import { z } from 'zod';

// Request validation schemas
const getNotificationsQuerySchema = z.object({
  isRead: z.coerce.boolean().optional(),
  handoverId: z.string().optional(),
  type: z.string().optional(),
  limit: z.coerce.number().int().positive().max(100).default(20),
  offset: z.coerce.number().int().min(0).default(0),
});

const markAsReadSchema = z.object({
  notificationIds: z.array(z.string()).optional(),
});

/**
 * GET /api/notifications/handovers - Get handover notifications for current user
 */
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get tenantId from user's tenants array (users have one tenant in this system)
    let tenantId: string | null = null;
    if (session.user.tenants && session.user.tenants.length > 0) {
      tenantId = session.user.tenants[0].id;
    }

    if (!tenantId) {
      return NextResponse.json({ error: 'Tenant not found' }, { status: 400 });
    }

    // Parse and validate query parameters
    const url = new URL(request.url);
    const queryParams = Object.fromEntries(url.searchParams.entries());
    
    const validatedQuery = getNotificationsQuerySchema.parse(queryParams);

    const result = await handoverNotificationService.getUserNotifications(
      tenantId,
      session.user.id,
      validatedQuery
    );

    return NextResponse.json(result);
  } catch (error) {
    console.error('Error fetching user handover notifications:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid query parameters', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to fetch handover notifications' },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/notifications/handovers - Mark handover notifications as read
 */
export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get tenantId from user's tenants array (users have one tenant in this system)
    let tenantId: string | null = null;
    if (session.user.tenants && session.user.tenants.length > 0) {
      tenantId = session.user.tenants[0].id;
    }

    if (!tenantId) {
      return NextResponse.json({ error: 'Tenant not found' }, { status: 400 });
    }

    const body = await request.json();
    const validatedData = markAsReadSchema.parse(body);

    const markedCount = await handoverNotificationService.markNotificationsAsRead(
      tenantId,
      session.user.id,
      validatedData.notificationIds
    );

    return NextResponse.json({ 
      message: `${markedCount} notifications marked as read`,
      markedCount 
    });
  } catch (error) {
    console.error('Error marking handover notifications as read:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request data', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to mark notifications as read' },
      { status: 500 }
    );
  }
}
