# AI Template Analysis Feature

## Overview

The AI Template Analysis feature automatically extracts styling configuration, branding configuration, and template variables from uploaded DOCX template files using Google's Gemini AI. This feature helps users quickly set up document templates by analyzing existing template files and auto-populating the configuration fields.

## How It Works

1. **File Upload**: User uploads a DOCX template file in the template creation dialog
2. **AI Analysis**: The system uses Gemini AI to analyze the document structure, styling, and content
3. **Configuration Extraction**: AI extracts:
   - **Styling Configuration**: Fonts, colors, spacing, margins, etc.
   - **Branding Configuration**: Company info, logos, themes, brand colors
   - **Template Variables**: Placeholders, merge fields, dynamic content areas
4. **Auto-Population**: The extracted configuration is automatically populated in the form fields

## Features

### Styling Configuration Extraction
- Font family and sizes (body, headers, subheaders)
- Colors (primary, secondary, accent, text, background, headers)
- Spacing and margins
- Line spacing
- Custom styling elements

### Branding Configuration Extraction
- Company name or placeholder detection
- Logo placement and style analysis
- Brand colors and color scheme identification
- Header and footer styles
- Watermarks or background elements
- Overall theme classification (professional/modern/classic/corporate)

### Template Variables Detection
- `{{variable}}` format placeholders
- `[PLACEHOLDER]` format fields
- Merge fields and content controls
- Dynamic content areas
- Conditional sections

## Usage

### In the Template Creation Dialog

1. Navigate to **Proposals > Templates**
2. Click **Create Template**
3. Upload a DOCX file in the "Template File" section
4. Click **"Analyze Template with AI"** button
5. Wait for the analysis to complete (usually 5-15 seconds)
6. Review and adjust the auto-populated configuration fields
7. Save the template

### API Endpoint

```
POST /api/document-templates/analyze
```

**Request:**
- Form data with `file` field containing the DOCX file

**Response:**
```json
{
  "success": true,
  "data": {
    "styling": {
      "fontFamily": "Arial",
      "fontSize": 12,
      "primaryColor": "#4f46e5",
      "secondaryColor": "#10b981",
      "accentColor": "#f59e0b",
      "headerColor": "#1f2937",
      "textColor": "#374151",
      "backgroundColor": "#ffffff",
      "lineSpacing": 1.15,
      "marginTop": 20,
      "marginBottom": 20,
      "marginLeft": 25,
      "marginRight": 25,
      "headerFontSize": 18,
      "subHeaderFontSize": 14,
      "bodyFontSize": 12
    },
    "branding": {
      "companyName": "Your Company",
      "logoPosition": "header",
      "primaryColor": "#4f46e5",
      "secondaryColor": "#10b981",
      "brandColors": ["#4f46e5", "#10b981"],
      "headerStyle": "professional",
      "footerStyle": "minimal",
      "theme": "professional"
    },
    "variables": [
      "proposal.title",
      "company.name",
      "contact.name",
      "date.current",
      "proposal.sections"
    ],
    "confidence": 0.85,
    "processingTime": 8500
  }
}
```

## Configuration Requirements

### Environment Variables
- `GOOGLE_AI_API_KEY`: Required for AI analysis functionality

### File Requirements
- **Format**: Only DOCX files are supported
- **Size**: Maximum 10MB file size
- **Content**: Should contain styling, branding elements, and/or template variables for best results

## Error Handling

The system includes comprehensive error handling:

- **File Validation**: Checks file type and size
- **AI Service Availability**: Graceful fallback when AI service is unavailable
- **Processing Errors**: Fallback configuration when analysis fails
- **Timeout Handling**: Prevents hanging requests

## Fallback Behavior

When AI analysis fails or is unavailable, the system provides sensible default configurations:

- Standard professional styling (Arial font, blue/green color scheme)
- Basic branding configuration
- Common template variables for proposals
- Confidence score of 0.5 to indicate fallback usage

## Performance

- **Analysis Time**: Typically 5-15 seconds depending on file size and complexity
- **File Processing**: Temporary files are automatically cleaned up
- **Memory Usage**: Optimized for handling multiple concurrent requests
- **Caching**: AI responses can be cached for identical files (future enhancement)

## Security

- **File Validation**: Strict DOCX file type validation
- **Temporary Storage**: Files are temporarily stored and immediately cleaned up
- **Permission Checks**: Requires appropriate permissions for template creation
- **Data Privacy**: Files are processed by Google's Gemini AI service

## Future Enhancements

- Support for additional file formats (DOC, PDF)
- Template preview generation
- Batch template analysis
- Custom styling rule extraction
- Advanced variable detection patterns
- Template similarity matching
- Caching of analysis results

## Troubleshooting

### Common Issues

1. **"AI analysis service is not configured"**
   - Ensure `GOOGLE_AI_API_KEY` environment variable is set

2. **"Only DOCX files are supported"**
   - Convert your template to DOCX format before uploading

3. **"Failed to process the template file"**
   - Ensure the DOCX file is not corrupted
   - Try with a simpler template file

4. **Analysis takes too long**
   - Large files may take longer to process
   - Check network connectivity to Google AI services

### Debug Information

The system logs detailed information about the analysis process:
- File upload and validation
- AI processing status
- Response parsing
- Error details

Check the server logs for detailed troubleshooting information.
