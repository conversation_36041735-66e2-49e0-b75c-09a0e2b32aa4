'use client';

import React from 'react';
import { ResponsiveContainer } from 'recharts';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { cn } from '@/lib/utils';

export interface BaseChartProps {
  title: string;
  description?: string;
  children: React.ReactNode;
  className?: string;
  height?: number;
  loading?: boolean;
  error?: string;
  actions?: React.ReactNode;
}

export function BaseChart({
  title,
  description,
  children,
  className,
  height = 300,
  loading = false,
  error,
  actions
}: BaseChartProps) {
  return (
    <Card className={cn('bg-white dark:bg-gray-800 rounded-lg shadow-soft', className)}>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <div className="space-y-1">
          <CardTitle className="text-base font-semibold">{title}</CardTitle>
          {description && (
            <CardDescription className="text-sm text-muted-foreground">
              {description}
            </CardDescription>
          )}
        </div>
        {actions && <div className="flex items-center space-x-2">{actions}</div>}
      </CardHeader>
      <CardContent>
        {loading ? (
          <div 
            className="flex items-center justify-center"
            style={{ height: `${height}px` }}
          >
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
          </div>
        ) : error ? (
          <div 
            className="flex items-center justify-center text-red-500"
            style={{ height: `${height}px` }}
          >
            <div className="text-center">
              <p className="text-sm font-medium">Error loading chart</p>
              <p className="text-xs text-muted-foreground mt-1">{error}</p>
            </div>
          </div>
        ) : (
          <ResponsiveContainer width="100%" height={height}>
            {children}
          </ResponsiveContainer>
        )}
      </CardContent>
    </Card>
  );
}
