'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { Phone, Plus, Calendar, Clock, Video, MapPin, Users, TrendingUp } from 'lucide-react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { Badge } from '@/components/ui/badge';
import { CallCard } from './call-card';
import { ScheduleCallDialog } from './schedule-call-dialog';
import { ScheduledCallWithRelations } from '@/services/scheduled-call-service';
import { useToast } from '@/hooks/use-toast';
import { usePermissions } from '@/components/providers/permission-provider';
import { PermissionResource, PermissionAction } from '@/types';
import { PermissionGate } from '@/components/ui/permission-gate';
import { cn } from '@/lib/utils';

interface LeadScheduledCallsProps {
  leadId: string;
  contactId?: string | null;
  className?: string;
}

interface User {
  id: string;
  firstName: string | null;
  lastName: string | null;
  email: string;
}

interface Contact {
  id: string;
  firstName: string;
  lastName: string;
  email: string | null;
}

interface Lead {
  id: string;
  title: string;
  status: string;
  contact?: {
    firstName: string;
    lastName: string;
  } | null;
}

export function LeadScheduledCalls({ leadId, contactId, className }: LeadScheduledCallsProps) {
  const { toast } = useToast();
  const { hasPermission } = usePermissions();
  const { data: session } = useSession();
  const [calls, setCalls] = useState<ScheduledCallWithRelations[]>([]);
  const [loading, setLoading] = useState(true);
  const [scheduleDialogOpen, setScheduleDialogOpen] = useState(false);
  const [editingCall, setEditingCall] = useState<ScheduledCallWithRelations | null>(null);
  const [users, setUsers] = useState<User[]>([]);
  const [contacts, setContacts] = useState<Contact[]>([]);
  const [leads, setLeads] = useState<Lead[]>([]);
  const [currentLead, setCurrentLead] = useState<Lead | null>(null);
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const [dataLoading, setDataLoading] = useState(true);

  // Load scheduled calls for this lead
  const loadCalls = async () => {
    try {
      const response = await fetch(`/api/calls?leadId=${leadId}&sortBy=scheduledAt&sortOrder=asc`);
      const data = await response.json();

      if (data.success) {
        setCalls(data.data.calls);
      } else {
        console.error('Failed to load calls:', data.error);
      }
    } catch (error) {
      console.error('Error loading calls:', error);
    } finally {
      setLoading(false);
    }
  };

  // Load users for assignment (tenant users only)
  const loadUsers = async () => {
    try {
      const response = await fetch('/api/users');
      const data = await response.json();

      if (data.success) {
        setUsers(data.data || []);
      }
    } catch (error) {
      console.error('Error loading users:', error);
    }
  };

  // Set current user from session
  useEffect(() => {
    if (session?.user) {
      setCurrentUser({
        id: session.user.id,
        firstName: session.user.name?.split(' ')[0] || null,
        lastName: session.user.name?.split(' ').slice(1).join(' ') || null,
        email: session.user.email || '',
      });
    }
  }, [session]);

  // Load contacts for selection
  const loadContacts = async () => {
    try {
      const response = await fetch('/api/contacts');
      const data = await response.json();

      if (data.success) {
        setContacts(data.data.contacts || []);
      }
    } catch (error) {
      console.error('Error loading contacts:', error);
    }
  };

  // Load leads for selection
  const loadLeads = async () => {
    try {
      const response = await fetch('/api/leads');
      const data = await response.json();

      if (data.success) {
        setLeads(data.data.leads || []);
      }
    } catch (error) {
      console.error('Error loading leads:', error);
    }
  };

  // Load current lead details
  const loadCurrentLead = async () => {
    try {
      const response = await fetch(`/api/leads/${leadId}`);
      const data = await response.json();

      if (data.success) {
        setCurrentLead(data.data.lead);
      }
    } catch (error) {
      console.error('Error loading current lead:', error);
    }
  };

  useEffect(() => {
    const loadAllData = async () => {
      setDataLoading(true);
      try {
        await Promise.all([
          loadCalls(),
          loadUsers(),
          loadContacts(),
          loadLeads(),
          loadCurrentLead()
        ]);
      } catch (error) {
        console.error('Error loading data:', error);
      } finally {
        setDataLoading(false);
      }
    };

    loadAllData();
  }, [leadId]);

  const handleScheduleCall = async (callData: any) => {
    try {
      const response = await fetch('/api/calls', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...callData,
          leadId,
          contactId: contactId || undefined,
        }),
      });

      const data = await response.json();

      if (data.success) {
        setCalls([...calls, data.data.scheduledCall]);
        toast({
          title: 'Success',
          description: 'Call scheduled successfully.',
        });
      } else {
        throw new Error(data.error || 'Failed to schedule call');
      }
    } catch (error) {
      console.error('Error scheduling call:', error);
      toast({
        title: 'Error',
        description: 'Failed to schedule call. Please try again.',
        variant: 'destructive',
      });
      throw error;
    }
  };

  const handleUpdateCall = async (callData: any) => {
    if (!editingCall) return;

    try {
      const response = await fetch(`/api/calls/${editingCall.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(callData),
      });

      const data = await response.json();

      if (data.success) {
        setCalls(calls.map(call => 
          call.id === editingCall.id ? data.data.scheduledCall : call
        ));
        setEditingCall(null);
        toast({
          title: 'Success',
          description: 'Call updated successfully.',
        });
      } else {
        throw new Error(data.error || 'Failed to update call');
      }
    } catch (error) {
      console.error('Error updating call:', error);
      toast({
        title: 'Error',
        description: 'Failed to update call. Please try again.',
        variant: 'destructive',
      });
      throw error;
    }
  };

  const handleDeleteCall = async (callId: string) => {
    try {
      const response = await fetch(`/api/calls/${callId}`, {
        method: 'DELETE',
      });

      const data = await response.json();

      if (data.success) {
        setCalls(calls.filter(call => call.id !== callId));
        toast({
          title: 'Success',
          description: 'Call deleted successfully.',
        });
      } else {
        throw new Error(data.error || 'Failed to delete call');
      }
    } catch (error) {
      console.error('Error deleting call:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete call. Please try again.',
        variant: 'destructive',
      });
    }
  };

  const handleCompleteCall = async (call: ScheduledCallWithRelations) => {
    try {
      const response = await fetch(`/api/calls/${call.id}/complete`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          callNotes: '', // Could be enhanced with a dialog to collect notes
          followUpRequired: false,
        }),
      });

      const data = await response.json();

      if (data.success) {
        setCalls(calls.map(c => 
          c.id === call.id ? data.data.scheduledCall : c
        ));
        toast({
          title: 'Success',
          description: 'Call marked as completed.',
        });
      } else {
        throw new Error(data.error || 'Failed to complete call');
      }
    } catch (error) {
      console.error('Error completing call:', error);
      toast({
        title: 'Error',
        description: 'Failed to complete call. Please try again.',
        variant: 'destructive',
      });
    }
  };

  const handleEditCall = (call: ScheduledCallWithRelations) => {
    setEditingCall(call);
    setScheduleDialogOpen(true);
  };

  const upcomingCalls = calls.filter(call => 
    call.status === 'scheduled' && new Date(call.scheduledAt) > new Date()
  );

  const pastCalls = calls.filter(call => 
    call.status === 'completed' || new Date(call.scheduledAt) <= new Date()
  );

  // Calculate call statistics
  const totalCalls = calls.length;
  const completedCalls = calls.filter(call => call.status === 'completed').length;
  const upcomingCallsCount = upcomingCalls.length;
  const callTypes = calls.reduce((acc, call) => {
    acc[call.callType] = (acc[call.callType] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  if (loading || dataLoading) {
    return (
      <Card className={cn("bg-white dark:bg-gray-800 shadow-soft border-0 ring-1 ring-gray-200 dark:ring-gray-700", className)}>
        <CardHeader className="pb-4">
          <CardTitle className="flex items-center space-x-2 text-lg font-semibold">
            <div className="p-2 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
              <Phone className="w-5 h-5 text-blue-600 dark:text-blue-400" />
            </div>
            <span>Scheduled Calls</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            {/* Loading Statistics */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {Array.from({ length: 3 }).map((_, i) => (
                <div key={i} className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4">
                  <Skeleton className="h-4 w-16 mb-2" />
                  <Skeleton className="h-6 w-8" />
                </div>
              ))}
            </div>
            {/* Loading Call Cards */}
            <div className="space-y-4">
              {Array.from({ length: 2 }).map((_, i) => (
                <div key={i} className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4">
                  <div className="space-y-3">
                    <div className="flex items-center space-x-3">
                      <Skeleton className="h-10 w-10 rounded-lg" />
                      <div className="flex-1">
                        <Skeleton className="h-4 w-3/4 mb-2" />
                        <Skeleton className="h-3 w-1/2" />
                      </div>
                    </div>
                    <Skeleton className="h-4 w-full" />
                  </div>
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <>
      <Card className={cn("bg-white dark:bg-gray-800 shadow-soft border-0 ring-1 ring-gray-200 dark:ring-gray-700", className)}>
        <CardHeader className="pb-4">
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center space-x-2 text-lg font-semibold">
              <div className="p-2 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
                <Phone className="w-5 h-5 text-blue-600 dark:text-blue-400" />
              </div>
              <span>Scheduled Calls</span>
              {totalCalls > 0 && (
                <Badge variant="secondary" className="ml-2">
                  {totalCalls} total
                </Badge>
              )}
            </CardTitle>
            <PermissionGate
              resource={PermissionResource.CALLS}
              action={PermissionAction.CREATE}
            >
              <Button
                size="sm"
                onClick={() => setScheduleDialogOpen(true)}
                className="bg-blue-600 hover:bg-blue-700 text-white"
              >
                <Plus className="w-4 h-4 mr-2" />
                Schedule Call
              </Button>
            </PermissionGate>
          </div>
        </CardHeader>

        <CardContent className="space-y-6">
          {calls.length === 0 ? (
            <div className="text-center py-12">
              <div className="bg-gray-100 dark:bg-gray-700 rounded-full w-20 h-20 flex items-center justify-center mx-auto mb-6">
                <Calendar className="h-10 w-10 text-gray-400" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
                No scheduled calls
              </h3>
              <p className="text-gray-500 dark:text-gray-400 mb-6 max-w-sm mx-auto">
                Schedule a call to start engaging with this lead and move them through your sales process
              </p>
              <PermissionGate
                resource={PermissionResource.CALLS}
                action={PermissionAction.CREATE}
              >
                <Button
                  onClick={() => setScheduleDialogOpen(true)}
                  className="bg-blue-600 hover:bg-blue-700 text-white"
                >
                  <Plus className="w-4 h-4 mr-2" />
                  Schedule First Call
                </Button>
              </PermissionGate>
            </div>
          ) : (
            <>
              {/* Call Statistics */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg p-4">
                  <div className="flex items-center space-x-3">
                    <div className="p-2 bg-blue-100 dark:bg-blue-800 rounded-lg">
                      <Clock className="w-5 h-5 text-blue-600 dark:text-blue-300" />
                    </div>
                    <div>
                      <p className="text-sm font-medium text-blue-900 dark:text-blue-100">Upcoming</p>
                      <p className="text-2xl font-bold text-blue-700 dark:text-blue-300">{upcomingCallsCount}</p>
                    </div>
                  </div>
                </div>

                <div className="bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-lg p-4">
                  <div className="flex items-center space-x-3">
                    <div className="p-2 bg-green-100 dark:bg-green-800 rounded-lg">
                      <TrendingUp className="w-5 h-5 text-green-600 dark:text-green-300" />
                    </div>
                    <div>
                      <p className="text-sm font-medium text-green-900 dark:text-green-100">Completed</p>
                      <p className="text-2xl font-bold text-green-700 dark:text-green-300">{completedCalls}</p>
                    </div>
                  </div>
                </div>

                <div className="bg-gradient-to-br from-purple-50 to-violet-50 dark:from-purple-900/20 dark:to-violet-900/20 rounded-lg p-4">
                  <div className="flex items-center space-x-3">
                    <div className="p-2 bg-purple-100 dark:bg-purple-800 rounded-lg">
                      <Users className="w-5 h-5 text-purple-600 dark:text-purple-300" />
                    </div>
                    <div>
                      <p className="text-sm font-medium text-purple-900 dark:text-purple-100">Total Calls</p>
                      <p className="text-2xl font-bold text-purple-700 dark:text-purple-300">{totalCalls}</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Call Type Distribution */}
              {Object.keys(callTypes).length > 0 && (
                <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4">
                  <h4 className="font-medium text-gray-900 dark:text-gray-100 mb-3 flex items-center">
                    <Phone className="w-4 h-4 mr-2 text-gray-500" />
                    Call Types
                  </h4>
                  <div className="flex flex-wrap gap-2">
                    {Object.entries(callTypes).map(([type, count]) => {
                      const Icon = type === 'phone' ? Phone : type === 'video' ? Video : MapPin;
                      const label = type === 'phone' ? 'Phone' : type === 'video' ? 'Video' : 'In-Person';
                      return (
                        <div key={type} className="flex items-center space-x-2 bg-white dark:bg-gray-600 rounded-lg px-3 py-2">
                          <Icon className="w-4 h-4 text-gray-500" />
                          <span className="text-sm font-medium text-gray-700 dark:text-gray-300">{label}</span>
                          <Badge variant="secondary" className="text-xs">{count}</Badge>
                        </div>
                      );
                    })}
                  </div>
                </div>
              )}
              {/* Upcoming Calls */}
              {upcomingCalls.length > 0 && (
                <div>
                  <div className="flex items-center justify-between mb-4">
                    <h4 className="font-semibold text-gray-900 dark:text-gray-100 flex items-center space-x-2">
                      <div className="p-1.5 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
                        <Clock className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                      </div>
                      <span>Upcoming Calls</span>
                      <Badge variant="outline" className="ml-2">
                        {upcomingCalls.length}
                      </Badge>
                    </h4>
                  </div>
                  <div className="space-y-4">
                    {upcomingCalls.map((call) => (
                      <CallCard
                        key={call.id}
                        call={call}
                        onEdit={handleEditCall}
                        onDelete={handleDeleteCall}
                        onComplete={handleCompleteCall}
                        compact
                        className="border-l-4 border-l-blue-500 bg-blue-50/50 dark:bg-blue-900/10"
                      />
                    ))}
                  </div>
                </div>
              )}

              {/* Past Calls */}
              {pastCalls.length > 0 && (
                <div>
                  <div className="flex items-center justify-between mb-4">
                    <h4 className="font-semibold text-gray-900 dark:text-gray-100 flex items-center space-x-2">
                      <div className="p-1.5 bg-gray-100 dark:bg-gray-700 rounded-lg">
                        <Calendar className="w-4 h-4 text-gray-600 dark:text-gray-400" />
                      </div>
                      <span>Call History</span>
                      <Badge variant="outline" className="ml-2">
                        {pastCalls.length}
                      </Badge>
                    </h4>
                    {pastCalls.length > 3 && (
                      <Button
                        variant="ghost"
                        size="sm"
                        className="text-blue-600 hover:text-blue-700"
                      >
                        View All ({pastCalls.length})
                      </Button>
                    )}
                  </div>
                  <div className="space-y-4">
                    {pastCalls.slice(0, 3).map((call) => (
                      <CallCard
                        key={call.id}
                        call={call}
                        onEdit={handleEditCall}
                        onDelete={handleDeleteCall}
                        onComplete={handleCompleteCall}
                        compact
                        className="border-l-4 border-l-gray-300 dark:border-l-gray-600"
                      />
                    ))}
                    {pastCalls.length > 3 && (
                      <div className="text-center py-4">
                        <p className="text-sm text-gray-500 dark:text-gray-400">
                          {pastCalls.length - 3} more calls in history
                        </p>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>

      {/* Schedule Call Dialog */}
      {!dataLoading && (
        <ScheduleCallDialog
          open={scheduleDialogOpen}
          onOpenChange={(open) => {
            setScheduleDialogOpen(open);
            if (!open) {
              setEditingCall(null);
            }
          }}
          onSubmit={editingCall ? handleUpdateCall : handleScheduleCall}
          call={editingCall}
          leadId={leadId}
          contactId={contactId}
          currentUserId={currentUser?.id}
          users={users}
          contacts={contacts}
          leads={leads}
        />
      )}
    </>
  );
}
