import { NextRequest, NextResponse } from 'next/server';
import { prisma } from './prisma';
import { getServerSession } from 'next-auth';
import { authOptions } from './auth';

// Tenant context interface
export interface TenantContextData {
  id: string;
  name: string;
  slug: string;
  subscription: {
    planId: string;
    status: string;
    features: string[];
    limits: Record<string, number>;
  };
  settings: Record<string, any>;
  branding: {
    logo?: string;
    primaryColor?: string;
    secondaryColor?: string;
  };
}

// Tenant resolution middleware (Single Database)
export async function resolveTenantContext(
  req: NextRequest,
  userId?: string
): Promise<TenantContextData | null> {
  try {
    // Get tenant ID from various sources
    let tenantId = req.headers.get('x-tenant-id');

    // If no tenant ID in headers, get user's single tenant
    if (!tenantId && userId) {
      const userTenant = await getUserTenant(userId);
      tenantId = userTenant?.tenantId || null;
    }

    if (!tenantId) {
      return null;
    }

    // Verify user has access to this tenant if userId is provided
    if (userId) {
      const hasAccess = await hasAccessToTenant(userId, tenantId);
      if (!hasAccess) {
        throw new Error('Access denied to tenant');
      }
    }

    // Get tenant details with subscription info
    const tenant = await prisma.tenant.findUnique({
      where: { id: tenantId },
      include: {
        subscriptions: {
          where: { status: 'active' },
          include: {
            plan: true
          },
          take: 1
        }
      }
    });

    if (!tenant) {
      throw new Error('Tenant not found');
    }

    // Check tenant status
    if (tenant.status !== 'active') {
      throw new Error('Tenant suspended');
    }

    const subscription = tenant.subscriptions[0];
    
    return {
      id: tenant.id,
      name: tenant.name,
      slug: tenant.slug,
      subscription: {
        planId: subscription?.planId || 'starter',
        status: subscription?.status || 'inactive',
        features: subscription?.plan?.features as string[] || [],
        limits: subscription?.plan?.limits as Record<string, number> || {}
      },
      settings: tenant.settings as Record<string, any>,
      branding: tenant.branding as any
    };
  } catch (error) {
    console.error('Tenant resolution failed:', error);
    return null;
  }
}

// Get user's single tenant
export async function getUserTenant(userId: string) {
  return await prisma.tenantUser.findFirst({
    where: {
      userId,
      status: 'active'
    },
    include: {
      tenant: {
        include: {
          subscriptions: {
            where: { status: 'active' },
            include: { plan: true },
            take: 1
          }
        }
      }
    }
  });
}

// Check if user has access to tenant
export async function hasAccessToTenant(userId: string, tenantId: string): Promise<boolean> {
  const tenantUser = await prisma.tenantUser.findUnique({
    where: {
      tenantId_userId: {
        tenantId,
        userId
      }
    }
  });

  return !!tenantUser && tenantUser.status === 'active';
}

// Set tenant context in database session
export async function setTenantContext(tenantId: string): Promise<void> {
  await prisma.$executeRaw`SET app.current_tenant_id = ${tenantId}`;
}

// Super Admin authentication middleware
export function withSuperAdminAuth(handler: Function) {
  return async (req: NextRequest) => {
    try {
      // Get user from NextAuth session
      const session = await getServerSession(authOptions);

      if (!session?.user) {
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
      }

      // Check if user is a Super Admin
      if (!session.user.isPlatformAdmin) {
        return NextResponse.json({
          error: 'Super Admin access required'
        }, { status: 403 });
      }

      // Add user context to request for Super Admin operations
      const requestWithContext = req as any;
      requestWithContext.userId = session.user.id;
      requestWithContext.session = session;
      requestWithContext.isSuperAdmin = true;

      return handler(requestWithContext);
    } catch (error) {
      console.error('Super Admin middleware error:', error);
      return NextResponse.json({ error: 'Authentication failed' }, { status: 500 });
    }
  };
}

// Tenant-aware API middleware (updated to handle Super Admin)
export function withTenantContext(handler: Function) {
  return async (req: NextRequest) => {
    try {
      // Get user from NextAuth session
      const session = await getServerSession(authOptions);

      if (!session?.user) {
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
      }

      const userId = session.user.id;

      // Super Admin bypass: Skip tenant context for platform admins
      if (session.user.isPlatformAdmin) {
        const requestWithContext = req as any;
        requestWithContext.userId = userId;
        requestWithContext.session = session;
        requestWithContext.isSuperAdmin = true;
        requestWithContext.tenantId = null; // No tenant context for Super Admin
        return handler(requestWithContext);
      }

      // Get tenant ID from headers or use user's first tenant
      let tenantId = req.headers.get('x-tenant-id');

      if (!tenantId && session.user.tenants?.length > 0) {
        // Use the first tenant as default
        tenantId = session.user.tenants[0].id;
      }

      if (!tenantId) {
        return NextResponse.json({ error: 'No tenant available' }, { status: 400 });
      }

      // Verify user has access to this tenant
      const hasAccess = await hasAccessToTenant(userId, tenantId);
      if (!hasAccess) {
        return NextResponse.json({ error: 'Access denied to tenant' }, { status: 403 });
      }

      // Resolve tenant context
      const tenantContext = await resolveTenantContext(req, userId);

      if (!tenantContext) {
        return NextResponse.json({ error: 'Tenant context required' }, { status: 400 });
      }

      // Set tenant context in database session
      await setTenantContext(tenantContext.id);

      // Add tenant context to request
      const requestWithContext = req as any;
      requestWithContext.tenant = tenantContext;
      requestWithContext.tenantId = tenantContext.id;
      requestWithContext.userId = userId;
      requestWithContext.session = session;

      return handler(requestWithContext);
    } catch (error) {
      console.error('Tenant middleware error:', error);
      return NextResponse.json({ error: 'Tenant resolution failed' }, { status: 500 });
    }
  };
}

// Utility to get current tenant from request context
export function getCurrentTenant(req: any): TenantContextData | null {
  return req.tenant || null;
}

// Utility to get current user ID from request context
export function getCurrentUserId(req: any): string | null {
  return req.userId || null;
}
