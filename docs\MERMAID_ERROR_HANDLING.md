# Enhanced Mermaid Error Handling

This document describes the comprehensive error handling system implemented for Mermaid chart rendering and regeneration.

## Overview

The enhanced error handling system provides:
- **Detailed error analysis** with categorization and severity levels
- **Automatic syntax sanitization** to fix common issues
- **AI-powered chart regeneration** with error context
- **User-friendly error messages** with actionable suggestions
- **Progressive error recovery** strategies

## Error Categories

### 1. Syntax Errors
- **Description**: Invalid Mermaid syntax that prevents parsing
- **Examples**: Unmatched brackets, invalid characters
- **Severity**: High
- **Fixable**: Yes (automatic sanitization)

### 2. Parsing Errors
- **Description**: Valid syntax but problematic content structure
- **Examples**: Quotes in node labels, parentheses in labels
- **Severity**: High
- **Fixable**: Yes (AI regeneration with context)

### 3. Rendering Errors
- **Description**: Technical issues during diagram rendering
- **Examples**: Missing node references, invalid connections
- **Severity**: Medium
- **Fixable**: Partially (retry with simplified structure)

### 4. Timeout Errors
- **Description**: Diagram too complex for rendering within time limit
- **Examples**: Too many nodes, complex nested structures
- **Severity**: Medium
- **Fixable**: Yes (AI simplification)

## Error Handling Flow

```mermaid
graph TD
    A[Mermaid Code] --> B[Validation]
    B --> C{Valid?}
    C -->|No| D[Parse Error Info]
    C -->|Yes| E[Sanitization]
    E --> F[Rendering]
    F --> G{Success?}
    G -->|No| H[Render Error Info]
    G -->|Yes| I[Display Diagram]
    D --> J[Show Error UI]
    H --> J
    J --> K{Fix Available?}
    K -->|Yes| L[AI Regeneration]
    K -->|No| M[Manual Fix Required]
    L --> N[Enhanced Prompt]
    N --> O[Generate New Code]
    O --> A
```

## Key Components

### 1. Enhanced Validation (`validateMermaidSyntax`)

```typescript
interface MermaidValidationResult {
  valid: boolean;
  error?: string;
  errorType?: 'syntax' | 'parsing' | 'structure' | 'content';
  suggestions?: string[];
  fixable?: boolean;
  line?: number;
  column?: number;
}
```

**Features:**
- Line-by-line syntax checking
- Pattern-based problematic syntax detection
- Specific error categorization
- Actionable suggestions for fixes

### 2. Error Parsing (`parseMermaidError`)

```typescript
interface MermaidErrorInfo {
  type: 'syntax' | 'parsing' | 'rendering' | 'timeout' | 'unknown';
  message: string;
  originalError: string;
  suggestions: string[];
  fixable: boolean;
  line?: number;
  column?: number;
  severity: 'low' | 'medium' | 'high';
}
```

**Features:**
- Regex-based error pattern matching
- User-friendly message generation
- Severity assessment
- Fix recommendations

### 3. Enhanced Sanitization (`sanitizeMermaidCode`)

**Progressive Fixing Strategies:**
1. **Quote Removal**: Remove problematic quotes from node labels
2. **Parentheses Handling**: Convert parentheses to safe alternatives
3. **Special Character Cleanup**: Replace problematic symbols
4. **Whitespace Normalization**: Clean up formatting issues

### 4. AI-Powered Regeneration

**API Endpoint**: `POST /api/proposals/[id]/regenerate-chart`

**Error-Aware Prompting:**
- Includes original error context in AI prompt
- Provides specific instructions based on error type
- Uses enhanced prompts for better results

## User Interface Enhancements

### Error Display
- **Color-coded severity indicators**
- **Detailed error information** with line numbers
- **Actionable suggestions** list
- **Progressive disclosure** of technical details

### Fix Button
- **Conditional display** based on error fixability
- **Loading states** during regeneration
- **Success feedback** with updated diagram
- **Fallback options** if regeneration fails

## Common Error Patterns and Fixes

### 1. Quotes in Node Labels
**Problem:**
```mermaid
graph TD
    A[Cloud Provider "AWS or Azure"]
```

**Error:** `Parse error: got 'STR'`

**Fix:** Remove quotes and use simple text
```mermaid
graph TD
    A[Cloud Provider AWS or Azure]
```

### 2. Parentheses in Labels
**Problem:**
```mermaid
graph TD
    A[Policy Engine (e.g., OPA)]
```

**Error:** `Parse error: Expecting 'SQE'`

**Fix:** Remove parentheses
```mermaid
graph TD
    A[Policy Engine - OPA]
```

### 3. Complex Special Characters
**Problem:**
```mermaid
graph TD
    A[Service A/B & C%]
```

**Fix:** Replace with safe alternatives
```mermaid
graph TD
    A[Service A or B and C percent]
```

## Testing

### Test Cases
The error handling system can be tested with these common problematic patterns:

1. **Quotes in node labels**
2. **Parentheses in node labels**
3. **Unmatched brackets**
4. **Complex problematic syntax**
5. **Valid syntax (control)**

## Best Practices

### For Developers
1. **Always validate** Mermaid code before rendering
2. **Use sanitization** as a first-line defense
3. **Implement progressive error recovery**
4. **Provide clear user feedback**
5. **Log errors** for debugging and improvement

### For AI Prompts
1. **Include error context** in regeneration prompts
2. **Specify syntax requirements** clearly
3. **Request simple, clean syntax**
4. **Avoid complex nested structures**
5. **Test generated code** before saving

### For Users
1. **Use simple text** in node labels
2. **Avoid quotes and special characters**
3. **Keep diagrams reasonably sized**
4. **Use the fix button** when available
5. **Report persistent issues**

## Configuration

### Timeout Settings
- **Validation timeout**: 5 seconds
- **Rendering timeout**: 15 seconds
- **Regeneration timeout**: 30 seconds

### Error Thresholds
- **High severity**: Syntax/parsing errors
- **Medium severity**: Rendering/timeout errors
- **Low severity**: Warning-level issues

## Word Document Optimization

### Enhanced Chart Generation for Documents

The system now includes specialized prompts for generating charts optimized for Word documents:

#### **Document-Optimized Features**
- **Page-aware sizing**: Charts designed for standard 8.5" x 11" pages
- **Layout optimization**: Horizontal layouts (LR) for better width utilization
- **Professional alignment**: Balanced, symmetrical layouts with proper spacing
- **Content limits**: 6-12 nodes maximum to prevent overcrowding
- **Clean styling**: Consistent formatting suitable for business documents

#### **Layout Guidelines by Chart Type**
- **Architecture High-Level**: `graph LR` with 5-8 main components
- **Architecture Low-Level**: `flowchart TD` with 8-12 components in layers
- **Project Timeline**: `gantt` format with 4-6 phases and milestones
- **System Flow**: `flowchart TD` with 6-10 process steps

#### **Content Standards**
- **Concise labels**: 2-4 words maximum per node
- **Clean text**: No special characters, quotes, or parentheses
- **Professional tone**: Business-appropriate terminology
- **Logical flow**: Easy-to-follow connections and relationships

### Regenerate Chart Feature

#### **User-Initiated Regeneration**
Users can now regenerate charts even when there are no errors:

```typescript
// Regenerate button in MermaidDiagram component
<Button onClick={handleRegenerateChart}>
  <RotateCcw className="h-4 w-4" />
</Button>
```

#### **Enhanced Regeneration Prompts**
- **Word document optimization**: Improved layout and spacing
- **Professional presentation**: Business-ready formatting
- **Error context awareness**: Learns from previous failures
- **Layout improvements**: Better alignment and visual balance

#### **API Enhancement**
The regeneration API now supports custom prompts for layout improvements:

```typescript
POST /api/proposals/[id]/regenerate-chart
{
  "chartId": "chart_id",
  "customPrompt": "Regenerate with improved Word document layout"
}
```

## Future Enhancements

1. **Machine Learning**: Learn from error patterns to improve sanitization
2. **Real-time Validation**: Live syntax checking as user types
3. **Error Analytics**: Track common error patterns for improvement
4. **Custom Error Rules**: Allow configuration of error handling rules
5. **Batch Processing**: Handle multiple chart errors simultaneously
6. **Template Library**: Pre-built chart templates optimized for documents
7. **Interactive Preview**: Live preview of chart changes before saving
8. **Export Optimization**: Direct export to Word-compatible formats
