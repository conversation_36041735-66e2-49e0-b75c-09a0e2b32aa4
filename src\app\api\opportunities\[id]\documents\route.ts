import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { hasPermission } from '@/lib/permissions';
import { PermissionResource, PermissionAction } from '@/types';
import { prisma } from '@/lib/prisma';

/**
 * GET /api/opportunities/[id]/documents
 * Get all documents associated with an opportunity
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user's current tenant
    const currentTenant = session.user.tenants?.[0];
    if (!currentTenant) {
      return NextResponse.json({ error: 'No tenant access' }, { status: 403 });
    }

    // Check permission for opportunity reading
    const hasAccess = await hasPermission(
      PermissionResource.OPPORTUNITIES,
      PermissionAction.READ,
      currentTenant.id
    );

    if (!hasAccess) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const opportunityId = params.id;

    // Verify opportunity exists and belongs to tenant
    const opportunity = await prisma.opportunity.findFirst({
      where: {
        id: opportunityId,
        tenantId: currentTenant.id
      }
    });

    if (!opportunity) {
      return NextResponse.json(
        { error: 'Opportunity not found or access denied' },
        { status: 404 }
      );
    }

    // Get all documents associated with the opportunity
    const documents = await prisma.document.findMany({
      where: {
        tenantId: currentTenant.id,
        relatedToType: 'opportunity',
        relatedToId: opportunityId
      },
      include: {
        uploadedBy: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    // Calculate statistics
    const totalSize = documents.reduce((sum, doc) => sum + (doc.fileSize || 0), 0);
    const fileTypes = [...new Set(documents.map(doc => doc.mimeType))].filter(Boolean);

    // Categorize by file type
    const categorizedDocuments = {
      pdf: documents.filter(doc => doc.mimeType?.includes('pdf')),
      word: documents.filter(doc => doc.mimeType?.includes('word') || doc.mimeType?.includes('document')),
      excel: documents.filter(doc => doc.mimeType?.includes('excel') || doc.mimeType?.includes('sheet')),
      text: documents.filter(doc => doc.mimeType?.includes('text')),
      other: documents.filter(doc => 
        !doc.mimeType?.includes('pdf') && 
        !doc.mimeType?.includes('word') && 
        !doc.mimeType?.includes('document') &&
        !doc.mimeType?.includes('excel') && 
        !doc.mimeType?.includes('sheet') &&
        !doc.mimeType?.includes('text')
      )
    };

    return NextResponse.json({
      success: true,
      data: documents,
      metadata: {
        opportunityId: opportunity.id,
        opportunityTitle: opportunity.title,
        totalDocuments: documents.length,
        totalSize,
        fileTypes: fileTypes.length,
        categories: {
          pdf: categorizedDocuments.pdf.length,
          word: categorizedDocuments.word.length,
          excel: categorizedDocuments.excel.length,
          text: categorizedDocuments.text.length,
          other: categorizedDocuments.other.length
        }
      },
      message: 'Documents retrieved successfully'
    });

  } catch (error) {
    console.error('Error retrieving opportunity documents:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * @swagger
 * /api/opportunities/{id}/documents:
 *   get:
 *     summary: Get opportunity documents
 *     description: Retrieve all documents associated with an opportunity
 *     tags: [Opportunities]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Opportunity ID
 *     responses:
 *       200:
 *         description: Documents retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: string
 *                       name:
 *                         type: string
 *                       filePath:
 *                         type: string
 *                       mimeType:
 *                         type: string
 *                       fileSize:
 *                         type: number
 *                       relatedToType:
 *                         type: string
 *                       relatedToId:
 *                         type: string
 *                       uploadedBy:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: string
 *                           firstName:
 *                             type: string
 *                           lastName:
 *                             type: string
 *                           email:
 *                             type: string
 *                       createdAt:
 *                         type: string
 *                         format: date-time
 *                       updatedAt:
 *                         type: string
 *                         format: date-time
 *                 metadata:
 *                   type: object
 *                   properties:
 *                     opportunityId:
 *                       type: string
 *                     opportunityTitle:
 *                       type: string
 *                     totalDocuments:
 *                       type: number
 *                     totalSize:
 *                       type: number
 *                     fileTypes:
 *                       type: number
 *                     categories:
 *                       type: object
 *                       properties:
 *                         pdf:
 *                           type: number
 *                         word:
 *                           type: number
 *                         excel:
 *                           type: number
 *                         text:
 *                           type: number
 *                         other:
 *                           type: number
 *                 message:
 *                   type: string
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - insufficient permissions
 *       404:
 *         description: Opportunity not found
 *       500:
 *         description: Internal server error
 */
