'use client';

import React, { useState, useEffect } from 'react';
import { PermissionGate } from '@/components/ui/permission-gate';
import { PermissionAction, PermissionResource, Role, User } from '@/types';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { useRoleOperations } from '@/hooks/use-permission-refresh';

interface UserRoleAssignmentProps {
  userId: string;
  onRolesUpdated?: () => void;
}

export function UserRoleAssignment({ userId, onRolesUpdated }: UserRoleAssignmentProps) {
  const [userRoles, setUserRoles] = useState<Role[]>([]);
  const [availableRoles, setAvailableRoles] = useState<Role[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showAssignModal, setShowAssignModal] = useState(false);
  const { assignRole, removeRole } = useRoleOperations();

  useEffect(() => {
    fetchUserRoles();
    fetchAvailableRoles();
  }, [userId]);

  const fetchUserRoles = async () => {
    try {
      const response = await fetch(`/api/roles/assign?userId=${userId}`);
      if (!response.ok) throw new Error('Failed to fetch user roles');
      
      const data = await response.json();
      setUserRoles(data.data.roles || []);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch user roles');
    }
  };

  const fetchAvailableRoles = async () => {
    try {
      const response = await fetch('/api/roles');
      if (!response.ok) throw new Error('Failed to fetch roles');
      
      const data = await response.json();
      setAvailableRoles(data.data.roles || []);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch roles');
    } finally {
      setIsLoading(false);
    }
  };

  const handleAssignRole = async (roleId: string, expiresAt?: Date) => {
    try {
      await assignRole(userId, roleId, expiresAt);
      await fetchUserRoles();
      setShowAssignModal(false);
      onRolesUpdated?.();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to assign role');
    }
  };

  const handleRemoveRole = async (roleId: string) => {
    if (!confirm('Are you sure you want to remove this role from the user?')) return;

    try {
      await removeRole(userId, roleId);
      await fetchUserRoles();
      onRolesUpdated?.();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to remove role');
    }
  };

  const getUnassignedRoles = () => {
    const assignedRoleIds = userRoles.map(role => role.id);
    return availableRoles.filter(role => !assignedRoleIds.includes(role.id));
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-32">
        <LoadingSpinner />
      </div>
    );
  }

  return (
    <PermissionGate
      resource={PermissionResource.USERS}
      action={PermissionAction.MANAGE}
      fallback={
        <div className="text-center py-4">
          <p className="text-gray-500">You don't have permission to manage user roles.</p>
        </div>
      }
    >
      <div className="space-y-4">
        {/* Header */}
        <div className="flex justify-between items-center">
          <h3 className="text-lg font-semibold text-gray-900">User Roles</h3>
          <button
            onClick={() => setShowAssignModal(true)}
            disabled={getUnassignedRoles().length === 0}
            className="bg-blue-600 text-white px-3 py-1 rounded-md text-sm hover:bg-blue-700 transition-colors disabled:bg-gray-400 disabled:cursor-not-allowed"
          >
            Assign Role
          </button>
        </div>

        {/* Error Display */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-md p-3">
            <p className="text-red-800 text-sm">{error}</p>
            <button
              onClick={() => setError(null)}
              className="text-red-600 hover:text-red-800 text-xs mt-1"
            >
              Dismiss
            </button>
          </div>
        )}

        {/* Current Roles */}
        <div className="space-y-2">
          {userRoles.length === 0 ? (
            <p className="text-gray-500 text-sm">No roles assigned to this user.</p>
          ) : (
            userRoles.map((role) => (
              <div
                key={role.id}
                className="flex items-center justify-between bg-gray-50 rounded-md p-3"
              >
                <div>
                  <div className="flex items-center space-x-2">
                    <span className="font-medium text-gray-900">{role.name}</span>
                    {role.isSystemRole && (
                      <span className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">
                        System
                      </span>
                    )}
                  </div>
                  {role.description && (
                    <p className="text-gray-600 text-sm mt-1">{role.description}</p>
                  )}
                  <div className="flex flex-wrap gap-1 mt-2">
                    {role.permissions.slice(0, 3).map((permission) => (
                      <span
                        key={permission.id}
                        className="bg-gray-200 text-gray-700 text-xs px-2 py-1 rounded"
                      >
                        {permission.name}
                      </span>
                    ))}
                    {role.permissions.length > 3 && (
                      <span className="bg-gray-200 text-gray-700 text-xs px-2 py-1 rounded">
                        +{role.permissions.length - 3} more
                      </span>
                    )}
                  </div>
                </div>
                
                {!role.isSystemRole && (
                  <button
                    onClick={() => handleRemoveRole(role.id)}
                    className="text-red-600 hover:text-red-800 p-1"
                    title="Remove role"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                )}
              </div>
            ))
          )}
        </div>

        {/* Assign Role Modal */}
        {showAssignModal && (
          <AssignRoleModal
            availableRoles={getUnassignedRoles()}
            onAssign={handleAssignRole}
            onCancel={() => setShowAssignModal(false)}
          />
        )}
      </div>
    </PermissionGate>
  );
}

interface AssignRoleModalProps {
  availableRoles: Role[];
  onAssign: (roleId: string, expiresAt?: Date) => void;
  onCancel: () => void;
}

function AssignRoleModal({ availableRoles, onAssign, onCancel }: AssignRoleModalProps) {
  const [selectedRoleId, setSelectedRoleId] = useState('');
  const [expiresAt, setExpiresAt] = useState('');
  const [hasExpiration, setHasExpiration] = useState(false);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!selectedRoleId) return;

    const expirationDate = hasExpiration && expiresAt ? new Date(expiresAt) : undefined;
    onAssign(selectedRoleId, expirationDate);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md">
        <h3 className="text-lg font-semibold mb-4">Assign Role</h3>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Select Role *
            </label>
            <select
              value={selectedRoleId}
              onChange={(e) => setSelectedRoleId(e.target.value)}
              className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              required
            >
              <option value="">Choose a role...</option>
              {availableRoles.map((role) => (
                <option key={role.id} value={role.id}>
                  {role.name} {role.isSystemRole ? '(System)' : ''}
                </option>
              ))}
            </select>
          </div>

          {selectedRoleId && (
            <div className="bg-gray-50 rounded-md p-3">
              <p className="text-sm text-gray-700">
                <strong>Description:</strong>{' '}
                {availableRoles.find(r => r.id === selectedRoleId)?.description || 'No description'}
              </p>
              <p className="text-sm text-gray-700 mt-1">
                <strong>Permissions:</strong>{' '}
                {availableRoles.find(r => r.id === selectedRoleId)?.permissions.length || 0} permissions
              </p>
            </div>
          )}

          <div>
            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={hasExpiration}
                onChange={(e) => setHasExpiration(e.target.checked)}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <span className="text-sm text-gray-700">Set expiration date</span>
            </label>
          </div>

          {hasExpiration && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Expires At
              </label>
              <input
                type="datetime-local"
                value={expiresAt}
                onChange={(e) => setExpiresAt(e.target.value)}
                min={new Date().toISOString().slice(0, 16)}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                required={hasExpiration}
              />
            </div>
          )}

          <div className="flex justify-end space-x-3 pt-4">
            <button
              type="button"
              onClick={onCancel}
              className="px-4 py-2 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={!selectedRoleId}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed"
            >
              Assign Role
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
