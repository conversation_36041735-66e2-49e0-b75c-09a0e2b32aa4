import { PrismaClient } from '@prisma/client';
import { z } from 'zod';

const prisma = new PrismaClient();

// ============================================================================
// VALIDATION SCHEMAS
// ============================================================================

export const CreateChatChannelSchema = z.object({
  name: z.string().min(1, 'Channel name is required').max(100),
  description: z.string().optional(),
  type: z.enum(['general', 'project', 'team', 'direct']).default('general'),
  isPrivate: z.boolean().default(false),
  relatedToType: z.enum(['project', 'opportunity', 'lead', 'contact']).optional(),
  relatedToId: z.string().optional(),
  allowFileSharing: z.boolean().default(true),
  allowMentions: z.boolean().default(true),
});

export const CreateChatMessageSchema = z.object({
  channelId: z.string(),
  content: z.string().min(1, 'Message content is required'),
  messageType: z.enum(['text', 'file', 'image', 'system']).default('text'),
  attachments: z.array(z.any()).optional(),
  parentMessageId: z.string().optional(),
  mentions: z.array(z.string()).optional(),
});

export const CreateCalendarEventSchema = z.object({
  title: z.string().min(1, 'Event title is required'),
  description: z.string().optional(),
  startTime: z.string().datetime(),
  endTime: z.string().datetime(),
  timezone: z.string().default('UTC'),
  isAllDay: z.boolean().default(false),
  location: z.string().optional(),
  meetingLink: z.string().url().optional(),
  eventType: z.enum(['meeting', 'task', 'reminder', 'deadline', 'holiday']).default('meeting'),
  status: z.enum(['confirmed', 'tentative', 'cancelled']).default('confirmed'),
  visibility: z.enum(['public', 'private', 'confidential']).default('public'),
  relatedToType: z.enum(['project', 'opportunity', 'lead', 'contact']).optional(),
  relatedToId: z.string().optional(),
  isRecurring: z.boolean().default(false),
  recurrenceRule: z.string().optional(),
  reminderMinutes: z.number().optional(),
  attendeeIds: z.array(z.string()).optional(),
});

export const CreateEmailTemplateSchema = z.object({
  name: z.string().min(1, 'Template name is required'),
  description: z.string().optional(),
  subject: z.string().min(1, 'Subject is required'),
  content: z.string().min(1, 'Content is required'),
  variables: z.array(z.string()).default([]),
  category: z.enum(['general', 'lead', 'opportunity', 'project', 'handover']).default('general'),
  type: z.string(),
  locale: z.string().default('en'),
  useCustomBranding: z.boolean().default(false),
  brandingConfig: z.any().optional(),
  isActive: z.boolean().default(true),
  isDefault: z.boolean().default(false),
});

// ============================================================================
// TYPES
// ============================================================================

export type CreateChatChannelInput = z.infer<typeof CreateChatChannelSchema>;
export type CreateChatMessageInput = z.infer<typeof CreateChatMessageSchema>;
export type CreateCalendarEventInput = z.infer<typeof CreateCalendarEventSchema>;
export type CreateEmailTemplateInput = z.infer<typeof CreateEmailTemplateSchema>;

// ============================================================================
// CHAT CHANNEL SERVICE
// ============================================================================

export class ChatChannelService {
  static async createChannel(
    tenantId: string,
    userId: string,
    data: CreateChatChannelInput
  ) {
    const validatedData = CreateChatChannelSchema.parse(data);

    const channel = await prisma.chatChannel.create({
      data: {
        ...validatedData,
        tenantId,
        createdById: userId,
        members: {
          create: {
            tenantId,
            userId,
            role: 'admin',
            canPost: true,
            canInvite: true,
            canModerate: true,
          },
        },
      },
      include: {
        createdBy: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            avatarUrl: true,
          },
        },
        members: {
          include: {
            user: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
                avatarUrl: true,
              },
            },
          },
        },
        _count: {
          select: {
            messages: true,
            members: true,
          },
        },
      },
    });

    return channel;
  }

  static async getChannels(tenantId: string, userId: string) {
    const channels = await prisma.chatChannel.findMany({
      where: {
        tenantId,
        members: {
          some: {
            userId,
            isActive: true,
          },
        },
      },
      include: {
        createdBy: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            avatarUrl: true,
          },
        },
        _count: {
          select: {
            messages: true,
            members: true,
          },
        },
      },
      orderBy: {
        updatedAt: 'desc',
      },
    });

    return channels;
  }

  static async getChannelById(tenantId: string, channelId: string, userId: string) {
    const channel = await prisma.chatChannel.findFirst({
      where: {
        id: channelId,
        tenantId,
        members: {
          some: {
            userId,
            isActive: true,
          },
        },
      },
      include: {
        createdBy: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            avatarUrl: true,
          },
        },
        members: {
          include: {
            user: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
                avatarUrl: true,
              },
            },
          },
          where: {
            isActive: true,
          },
        },
      },
    });

    if (!channel) {
      throw new Error('Channel not found or access denied');
    }

    return channel;
  }

  static async addMember(
    tenantId: string,
    channelId: string,
    userId: string,
    targetUserId: string,
    role: string = 'member'
  ) {
    // Check if user has permission to add members
    const membership = await prisma.chatChannelMember.findFirst({
      where: {
        tenantId,
        channelId,
        userId,
        isActive: true,
        canInvite: true,
      },
    });

    if (!membership) {
      throw new Error('Permission denied: Cannot add members to this channel');
    }

    // Check if target user is already a member
    const existingMember = await prisma.chatChannelMember.findFirst({
      where: {
        tenantId,
        channelId,
        userId: targetUserId,
      },
    });

    if (existingMember) {
      if (existingMember.isActive) {
        throw new Error('User is already a member of this channel');
      } else {
        // Reactivate existing membership
        return await prisma.chatChannelMember.update({
          where: { id: existingMember.id },
          data: {
            isActive: true,
            role,
            leftAt: null,
          },
          include: {
            user: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
                avatarUrl: true,
              },
            },
          },
        });
      }
    }

    // Create new membership
    const newMember = await prisma.chatChannelMember.create({
      data: {
        tenantId,
        channelId,
        userId: targetUserId,
        role,
        canPost: true,
        canInvite: role === 'admin' || role === 'moderator',
        canModerate: role === 'admin' || role === 'moderator',
      },
      include: {
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            avatarUrl: true,
          },
        },
      },
    });

    return newMember;
  }

  static async removeMember(
    tenantId: string,
    channelId: string,
    userId: string,
    targetUserId: string
  ) {
    // Check if user has permission to remove members
    const membership = await prisma.chatChannelMember.findFirst({
      where: {
        tenantId,
        channelId,
        userId,
        isActive: true,
        canModerate: true,
      },
    });

    if (!membership && userId !== targetUserId) {
      throw new Error('Permission denied: Cannot remove members from this channel');
    }

    // Update membership to inactive
    const updatedMember = await prisma.chatChannelMember.updateMany({
      where: {
        tenantId,
        channelId,
        userId: targetUserId,
        isActive: true,
      },
      data: {
        isActive: false,
        leftAt: new Date(),
      },
    });

    return updatedMember;
  }
}

// ============================================================================
// CHAT MESSAGE SERVICE
// ============================================================================

export class ChatMessageService {
  static async createMessage(
    tenantId: string,
    userId: string,
    data: CreateChatMessageInput
  ) {
    const validatedData = CreateChatMessageSchema.parse(data);

    // Check if user is a member of the channel
    const membership = await prisma.chatChannelMember.findFirst({
      where: {
        tenantId,
        channelId: validatedData.channelId,
        userId,
        isActive: true,
        canPost: true,
      },
    });

    if (!membership) {
      throw new Error('Permission denied: Cannot post to this channel');
    }

    const message = await prisma.chatMessage.create({
      data: {
        ...validatedData,
        tenantId,
        userId,
        attachments: validatedData.attachments || [],
        mentions: validatedData.mentions || [],
        reactions: {},
      },
      include: {
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            avatarUrl: true,
          },
        },
        parentMessage: {
          include: {
            user: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
                avatarUrl: true,
              },
            },
          },
        },
      },
    });

    // Update thread count if this is a reply
    if (validatedData.parentMessageId) {
      await prisma.chatMessage.update({
        where: { id: validatedData.parentMessageId },
        data: {
          threadCount: {
            increment: 1,
          },
        },
      });
    }

    // Update channel's last activity
    await prisma.chatChannel.update({
      where: { id: validatedData.channelId },
      data: { updatedAt: new Date() },
    });

    return message;
  }

  static async getMessages(
    tenantId: string,
    channelId: string,
    userId: string,
    options: {
      limit?: number;
      offset?: number;
      parentMessageId?: string;
    } = {}
  ) {
    const { limit = 50, offset = 0, parentMessageId } = options;

    // Check if user is a member of the channel
    const membership = await prisma.chatChannelMember.findFirst({
      where: {
        tenantId,
        channelId,
        userId,
        isActive: true,
      },
    });

    if (!membership) {
      throw new Error('Permission denied: Cannot access this channel');
    }

    const messages = await prisma.chatMessage.findMany({
      where: {
        tenantId,
        channelId,
        parentMessageId: parentMessageId || null,
        isDeleted: false,
      },
      include: {
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            avatarUrl: true,
          },
        },
        parentMessage: {
          include: {
            user: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
                avatarUrl: true,
              },
            },
          },
        },
        _count: {
          select: {
            replies: true,
          },
        },
      },
      orderBy: {
        createdAt: parentMessageId ? 'asc' : 'desc',
      },
      take: limit,
      skip: offset,
    });

    // Update last read timestamp
    await prisma.chatChannelMember.update({
      where: {
        id: membership.id,
      },
      data: {
        lastReadAt: new Date(),
      },
    });

    return messages;
  }

  static async updateMessage(
    tenantId: string,
    messageId: string,
    userId: string,
    content: string
  ) {
    const message = await prisma.chatMessage.findFirst({
      where: {
        id: messageId,
        tenantId,
        userId,
        isDeleted: false,
      },
    });

    if (!message) {
      throw new Error('Message not found or permission denied');
    }

    const updatedMessage = await prisma.chatMessage.update({
      where: { id: messageId },
      data: {
        content,
        isEdited: true,
        editedAt: new Date(),
      },
      include: {
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            avatarUrl: true,
          },
        },
      },
    });

    return updatedMessage;
  }

  static async deleteMessage(
    tenantId: string,
    messageId: string,
    userId: string
  ) {
    const message = await prisma.chatMessage.findFirst({
      where: {
        id: messageId,
        tenantId,
        userId,
        isDeleted: false,
      },
    });

    if (!message) {
      throw new Error('Message not found or permission denied');
    }

    const deletedMessage = await prisma.chatMessage.update({
      where: { id: messageId },
      data: {
        isDeleted: true,
        deletedAt: new Date(),
        content: '[Message deleted]',
      },
    });

    return deletedMessage;
  }

  static async addReaction(
    tenantId: string,
    messageId: string,
    userId: string,
    emoji: string
  ) {
    const message = await prisma.chatMessage.findFirst({
      where: {
        id: messageId,
        tenantId,
        isDeleted: false,
      },
    });

    if (!message) {
      throw new Error('Message not found');
    }

    // Check if user has access to the channel
    const membership = await prisma.chatChannelMember.findFirst({
      where: {
        tenantId,
        channelId: message.channelId,
        userId,
        isActive: true,
      },
    });

    if (!membership) {
      throw new Error('Permission denied: Cannot access this channel');
    }

    const reactions = message.reactions as Record<string, string[]> || {};

    if (!reactions[emoji]) {
      reactions[emoji] = [];
    }

    if (!reactions[emoji].includes(userId)) {
      reactions[emoji].push(userId);
    }

    const updatedMessage = await prisma.chatMessage.update({
      where: { id: messageId },
      data: { reactions },
    });

    return updatedMessage;
  }

  static async removeReaction(
    tenantId: string,
    messageId: string,
    userId: string,
    emoji: string
  ) {
    const message = await prisma.chatMessage.findFirst({
      where: {
        id: messageId,
        tenantId,
        isDeleted: false,
      },
    });

    if (!message) {
      throw new Error('Message not found');
    }

    const reactions = message.reactions as Record<string, string[]> || {};

    if (reactions[emoji]) {
      reactions[emoji] = reactions[emoji].filter(id => id !== userId);
      if (reactions[emoji].length === 0) {
        delete reactions[emoji];
      }
    }

    const updatedMessage = await prisma.chatMessage.update({
      where: { id: messageId },
      data: { reactions },
    });

    return updatedMessage;
  }
}

// ============================================================================
// CALENDAR EVENT SERVICE
// ============================================================================

export class CalendarEventService {
  static async createEvent(
    tenantId: string,
    userId: string,
    data: CreateCalendarEventInput
  ) {
    // Validate inputs
    if (!tenantId) {
      throw new Error('Tenant ID is required');
    }
    if (!userId) {
      throw new Error('User ID is required');
    }

    const validatedData = CreateCalendarEventSchema.parse(data);
    const { attendeeIds, ...eventData } = validatedData;

    console.log('Creating calendar event with:', { tenantId, userId, eventData });

    const event = await prisma.calendarEvent.create({
      data: {
        ...eventData,
        startTime: new Date(eventData.startTime),
        endTime: new Date(eventData.endTime),
        tenantId,
        createdById: userId,
        attendees: {
          create: [
            // Creator is always an attendee
            {
              tenantId,
              userId,
              status: 'accepted',
              canEdit: true,
            },
            // Add other attendees
            ...(attendeeIds || []).filter(id => id !== userId).map(attendeeId => ({
              tenantId,
              userId: attendeeId,
              status: 'pending' as const,
              canEdit: false,
            })),
          ],
        },
      },
      include: {
        createdBy: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            avatarUrl: true,
          },
        },
        attendees: {
          include: {
            user: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
                avatarUrl: true,
              },
            },
          },
        },
      },
    });

    return event;
  }

  static async getEvents(
    tenantId: string,
    userId: string,
    options: {
      startDate?: Date;
      endDate?: Date;
      eventType?: string;
      relatedToType?: string;
      relatedToId?: string;
    } = {}
  ) {
    const { startDate, endDate, eventType, relatedToType, relatedToId } = options;

    const events = await prisma.calendarEvent.findMany({
      where: {
        tenantId,
        attendees: {
          some: {
            userId,
          },
        },
        ...(startDate && { startTime: { gte: startDate } }),
        ...(endDate && { endTime: { lte: endDate } }),
        ...(eventType && { eventType }),
        ...(relatedToType && { relatedToType }),
        ...(relatedToId && { relatedToId }),
      },
      include: {
        createdBy: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            avatarUrl: true,
          },
        },
        attendees: {
          include: {
            user: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
                avatarUrl: true,
              },
            },
          },
        },
      },
      orderBy: {
        startTime: 'asc',
      },
    });

    return events;
  }

  static async updateEventAttendance(
    tenantId: string,
    eventId: string,
    userId: string,
    status: 'accepted' | 'declined' | 'tentative',
    response?: string
  ) {
    const attendance = await prisma.calendarEventAttendee.findFirst({
      where: {
        tenantId,
        eventId,
        userId,
      },
    });

    if (!attendance) {
      throw new Error('Event attendance not found');
    }

    const updatedAttendance = await prisma.calendarEventAttendee.update({
      where: { id: attendance.id },
      data: {
        status,
        response,
        respondedAt: new Date(),
      },
      include: {
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            avatarUrl: true,
          },
        },
        event: {
          select: {
            id: true,
            title: true,
            startTime: true,
            endTime: true,
          },
        },
      },
    });

    return updatedAttendance;
  }

  static async deleteEvent(
    tenantId: string,
    eventId: string,
    userId: string
  ) {
    const event = await prisma.calendarEvent.findFirst({
      where: {
        id: eventId,
        tenantId,
        createdById: userId,
      },
    });

    if (!event) {
      throw new Error('Event not found or permission denied');
    }

    await prisma.calendarEvent.delete({
      where: { id: eventId },
    });

    return { success: true };
  }
}

// ============================================================================
// EMAIL TEMPLATE SERVICE
// ============================================================================

export class EmailTemplateService {
  static async createTemplate(
    tenantId: string,
    userId: string,
    data: CreateEmailTemplateInput
  ) {
    const validatedData = CreateEmailTemplateSchema.parse(data);

    const template = await prisma.emailTemplate.create({
      data: {
        ...validatedData,
        tenantId,
        createdById: userId,
        variables: validatedData.variables || [],
        brandingConfig: validatedData.brandingConfig || null,
      },
      include: {
        createdBy: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            avatarUrl: true,
          },
        },
      },
    });

    return template;
  }

  static async getTemplates(
    tenantId: string,
    options: {
      category?: string;
      type?: string;
      locale?: string;
      isActive?: boolean;
    } = {}
  ) {
    const { category, type, locale, isActive } = options;

    const templates = await prisma.emailTemplate.findMany({
      where: {
        tenantId,
        ...(category && { category }),
        ...(type && { type }),
        ...(locale && { locale }),
        ...(isActive !== undefined && { isActive }),
      },
      include: {
        createdBy: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            avatarUrl: true,
          },
        },
      },
      orderBy: [
        { isDefault: 'desc' },
        { usageCount: 'desc' },
        { createdAt: 'desc' },
      ],
    });

    return templates;
  }

  static async getTemplateById(
    tenantId: string,
    templateId: string
  ) {
    const template = await prisma.emailTemplate.findFirst({
      where: {
        id: templateId,
        tenantId,
      },
      include: {
        createdBy: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            avatarUrl: true,
          },
        },
      },
    });

    if (!template) {
      throw new Error('Template not found');
    }

    return template;
  }

  static async updateTemplate(
    tenantId: string,
    templateId: string,
    userId: string,
    data: Partial<CreateEmailTemplateInput>
  ) {
    const template = await prisma.emailTemplate.findFirst({
      where: {
        id: templateId,
        tenantId,
        createdById: userId,
      },
    });

    if (!template) {
      throw new Error('Template not found or permission denied');
    }

    const updatedTemplate = await prisma.emailTemplate.update({
      where: { id: templateId },
      data: {
        ...data,
        ...(data.variables && { variables: data.variables }),
        ...(data.brandingConfig && { brandingConfig: data.brandingConfig }),
      },
      include: {
        createdBy: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            avatarUrl: true,
          },
        },
      },
    });

    return updatedTemplate;
  }

  static async deleteTemplate(
    tenantId: string,
    templateId: string,
    userId: string
  ) {
    const template = await prisma.emailTemplate.findFirst({
      where: {
        id: templateId,
        tenantId,
        createdById: userId,
      },
    });

    if (!template) {
      throw new Error('Template not found or permission denied');
    }

    await prisma.emailTemplate.delete({
      where: { id: templateId },
    });

    return { success: true };
  }

  static async incrementUsage(
    tenantId: string,
    templateId: string
  ) {
    await prisma.emailTemplate.update({
      where: {
        id: templateId,
        tenantId,
      },
      data: {
        usageCount: {
          increment: 1,
        },
        lastUsedAt: new Date(),
      },
    });
  }

  static async renderTemplate(
    tenantId: string,
    templateId: string,
    variables: Record<string, any>
  ) {
    const template = await this.getTemplateById(tenantId, templateId);

    let renderedSubject = template.subject;
    let renderedContent = template.content;

    // Simple variable replacement (in production, use a proper template engine)
    Object.entries(variables).forEach(([key, value]) => {
      const placeholder = `{{${key}}}`;
      renderedSubject = renderedSubject.replace(new RegExp(placeholder, 'g'), String(value));
      renderedContent = renderedContent.replace(new RegExp(placeholder, 'g'), String(value));
    });

    // Increment usage count
    await this.incrementUsage(tenantId, templateId);

    return {
      subject: renderedSubject,
      content: renderedContent,
      template,
    };
  }
}

// ============================================================================
// COMMUNICATION HUB SERVICE
// ============================================================================

export class CommunicationHubService {
  static async getActivityFeed(
    tenantId: string,
    userId: string,
    options: {
      limit?: number;
      offset?: number;
      relatedToType?: string;
      relatedToId?: string;
    } = {}
  ) {
    const { limit = 20, offset = 0, relatedToType, relatedToId } = options;

    // Get recent messages from user's channels
    const recentMessages = await prisma.chatMessage.findMany({
      where: {
        tenantId,
        channel: {
          members: {
            some: {
              userId,
              isActive: true,
            },
          },
        },
        ...(relatedToType && relatedToId && {
          channel: {
            relatedToType,
            relatedToId,
          },
        }),
        isDeleted: false,
      },
      include: {
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            avatarUrl: true,
          },
        },
        channel: {
          select: {
            id: true,
            name: true,
            type: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
      take: limit,
      skip: offset,
    });

    // Get upcoming events
    const upcomingEvents = await prisma.calendarEvent.findMany({
      where: {
        tenantId,
        attendees: {
          some: {
            userId,
          },
        },
        startTime: {
          gte: new Date(),
        },
        ...(relatedToType && { relatedToType }),
        ...(relatedToId && { relatedToId }),
      },
      include: {
        createdBy: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            avatarUrl: true,
          },
        },
        attendees: {
          include: {
            user: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
                avatarUrl: true,
              },
            },
          },
        },
      },
      orderBy: {
        startTime: 'asc',
      },
      take: 5,
    });

    return {
      messages: recentMessages,
      events: upcomingEvents,
    };
  }

  static async getUnreadCounts(tenantId: string, userId: string) {
    const channels = await prisma.chatChannel.findMany({
      where: {
        tenantId,
        members: {
          some: {
            userId,
            isActive: true,
          },
        },
      },
      include: {
        members: {
          where: {
            userId,
            isActive: true,
          },
          select: {
            lastReadAt: true,
          },
        },
        _count: {
          select: {
            messages: {
              where: {
                isDeleted: false,
              },
            },
          },
        },
      },
    });

    const unreadCounts = await Promise.all(
      channels.map(async (channel) => {
        const lastReadAt = channel.members[0]?.lastReadAt;
        const unreadCount = await prisma.chatMessage.count({
          where: {
            channelId: channel.id,
            tenantId,
            isDeleted: false,
            ...(lastReadAt && {
              createdAt: {
                gt: lastReadAt,
              },
            }),
          },
        });

        return {
          channelId: channel.id,
          channelName: channel.name,
          unreadCount,
        };
      })
    );

    return unreadCounts;
  }
}
