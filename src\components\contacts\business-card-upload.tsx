'use client';

import React, { useState, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Upload, 
  CreditCard, 
  X, 
  CheckCircle, 
  AlertCircle, 
  Loader2,
  Eye,
  Sparkles
} from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { BusinessCardAnalysisResponse } from '@/services/business-card-analysis';

interface BusinessCardUploadProps {
  onAnalysisComplete: (analysis: BusinessCardAnalysisResponse) => void;
  onError: (error: string) => void;
  disabled?: boolean;
  className?: string;
}

interface UploadState {
  file: File | null;
  preview: string | null;
  isAnalyzing: boolean;
  analysis: BusinessCardAnalysisResponse | null;
  error: string | null;
}

export function BusinessCardUpload({
  onAnalysisComplete,
  onError,
  disabled = false,
  className
}: BusinessCardUploadProps) {
  const [uploadState, setUploadState] = useState<UploadState>({
    file: null,
    preview: null,
    isAnalyzing: false,
    analysis: null,
    error: null
  });

  const onDrop = useCallback(
    async (acceptedFiles: File[], rejectedFiles: any[]) => {
      // Handle rejected files
      if (rejectedFiles.length > 0) {
        const rejection = rejectedFiles[0];
        setUploadState(prev => ({
          ...prev,
          error: `File rejected: ${rejection.errors[0]?.message || 'Invalid file type'}`
        }));
        return;
      }

      const file = acceptedFiles[0];
      if (!file) return;

      // Create preview
      const preview = URL.createObjectURL(file);
      
      setUploadState(prev => ({
        ...prev,
        file,
        preview,
        isAnalyzing: true,
        analysis: null,
        error: null
      }));

      try {
        console.log('🚀 Starting business card analysis...');

        // Call the analysis API
        const formData = new FormData();
        formData.append('file', file);

        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 90000); // 90 second timeout

        const response = await fetch('/api/contacts/analyze-business-card', {
          method: 'POST',
          body: formData,
          signal: controller.signal
        });

        clearTimeout(timeoutId);

        console.log('📡 Response received:', response.status);

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();
        console.log('📊 Analysis data:', data);

        if (data.success) {
          const analysis = data.data as BusinessCardAnalysisResponse;
          console.log('✅ Analysis successful:', analysis);

          setUploadState(prev => ({
            ...prev,
            isAnalyzing: false,
            analysis
          }));
          onAnalysisComplete(analysis);
        } else {
          throw new Error(data.error || 'Analysis failed');
        }
      } catch (error) {
        console.error('❌ Analysis error:', error);

        let errorMessage = 'Failed to analyze business card';

        if (error instanceof Error) {
          if (error.name === 'AbortError') {
            errorMessage = 'Analysis timeout - please try again with a clearer image';
          } else {
            errorMessage = error.message;
          }
        }

        setUploadState(prev => ({
          ...prev,
          isAnalyzing: false,
          error: errorMessage
        }));
        onError(errorMessage);
      }
    },
    [onAnalysisComplete, onError]
  );

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/*': ['.jpeg', '.jpg', '.png', '.webp', '.gif', '.bmp', '.tiff']
    },
    maxFiles: 1,
    maxSize: 10 * 1024 * 1024, // 10MB
    disabled: disabled || uploadState.isAnalyzing,
  });

  const clearUpload = () => {
    if (uploadState.preview) {
      URL.revokeObjectURL(uploadState.preview);
    }
    setUploadState({
      file: null,
      preview: null,
      isAnalyzing: false,
      analysis: null,
      error: null
    });
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className={cn('space-y-4', className)}>
      <AnimatePresence mode="wait">
        {!uploadState.file ? (
          <motion.div
            key="upload-zone"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.3 }}
          >
            <Card
              {...getRootProps()}
              className={cn(
                'border-2 border-dashed transition-all duration-200 cursor-pointer hover:border-primary/50',
                isDragActive ? 'border-primary bg-primary/5' : 'border-gray-300',
                disabled && 'opacity-50 cursor-not-allowed'
              )}
            >
              <CardContent className="flex flex-col items-center justify-center py-12 px-6 text-center">
                <input {...getInputProps()} />
                <div className="flex items-center justify-center w-16 h-16 mb-4 rounded-full bg-primary/10">
                  <CreditCard className={cn(
                    'w-8 h-8',
                    isDragActive ? 'text-primary' : 'text-gray-400'
                  )} />
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  {isDragActive ? 'Drop business card here' : 'Upload Business Card'}
                </h3>
                <p className="text-sm text-gray-500 mb-4">
                  Drag and drop a business card image, or click to browse
                </p>
                <div className="flex items-center gap-2 text-xs text-gray-400 mb-4">
                  <Sparkles className="w-4 h-4" />
                  <span>AI will automatically extract contact information</span>
                </div>
                <div className="text-xs text-gray-400 space-y-1">
                  <p>Maximum file size: 10MB</p>
                  <p>Supported formats: JPG, PNG, WebP, GIF, BMP, TIFF</p>
                </div>
                {!disabled && (
                  <Button variant="outline" className="mt-4">
                    <Upload className="w-4 h-4 mr-2" />
                    Choose Image
                  </Button>
                )}
              </CardContent>
            </Card>
          </motion.div>
        ) : (
          <motion.div
            key="preview-zone"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.3 }}
          >
            <Card>
              <CardContent className="p-6">
                <div className="flex items-start gap-4">
                  {/* Image Preview */}
                  <div className="flex-shrink-0">
                    <div className="relative w-32 h-20 rounded-lg overflow-hidden bg-gray-100">
                      {uploadState.preview && (
                        <img
                          src={uploadState.preview}
                          alt="Business card preview"
                          className="w-full h-full object-cover"
                        />
                      )}
                      <div className="absolute inset-0 bg-black/20 flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity">
                        <Eye className="w-5 h-5 text-white" />
                      </div>
                    </div>
                  </div>

                  {/* File Info and Status */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="text-sm font-medium text-gray-900 truncate">
                        {uploadState.file?.name}
                      </h4>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={clearUpload}
                        disabled={uploadState.isAnalyzing}
                      >
                        <X className="w-4 h-4" />
                      </Button>
                    </div>
                    
                    <p className="text-xs text-gray-500 mb-3">
                      {uploadState.file && formatFileSize(uploadState.file.size)}
                    </p>

                    {/* Status */}
                    <div className="flex items-center gap-2">
                      {uploadState.isAnalyzing && (
                        <>
                          <Loader2 className="w-4 h-4 animate-spin text-blue-500" />
                          <span className="text-sm text-blue-600">Analyzing business card...</span>
                        </>
                      )}
                      
                      {uploadState.analysis && (
                        <>
                          <CheckCircle className="w-4 h-4 text-green-500" />
                          <span className="text-sm text-green-600">
                            Analysis complete ({uploadState.analysis.confidence}% confidence)
                          </span>
                        </>
                      )}
                      
                      {uploadState.error && (
                        <>
                          <AlertCircle className="w-4 h-4 text-red-500" />
                          <span className="text-sm text-red-600">{uploadState.error}</span>
                        </>
                      )}
                    </div>

                    {/* Analysis Results Preview */}
                    {uploadState.analysis && uploadState.analysis.confidence > 0 && (
                      <div className="mt-3 p-3 bg-green-50 rounded-lg">
                        <h5 className="text-xs font-medium text-green-800 mb-2">Extracted Information:</h5>
                        <div className="text-xs text-green-700 space-y-1">
                          {uploadState.analysis.firstName && uploadState.analysis.lastName && (
                            <div>Name: {uploadState.analysis.firstName} {uploadState.analysis.lastName}</div>
                          )}
                          {uploadState.analysis.email && (
                            <div>Email: {uploadState.analysis.email}</div>
                          )}
                          {uploadState.analysis.phone && (
                            <div>Phone: {uploadState.analysis.phone}</div>
                          )}
                          {uploadState.analysis.company && (
                            <div>Company: {uploadState.analysis.company}</div>
                          )}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
