'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { toast } from 'sonner';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Loader2, CheckCircle, XCircle } from 'lucide-react';

const bulkConvertSchema = z.object({
  stage: z.string().optional(),
  priority: z.enum(['low', 'medium', 'high', 'urgent']).optional(),
  ownerId: z.string().optional(),
  notes: z.string().optional(),
});

type BulkConvertFormData = z.infer<typeof bulkConvertSchema>;

interface LeadConversionEligibility {
  leadId: string;
  eligible: boolean;
  reason?: string;
  qualificationScore?: number;
  missingRequirements?: string[];
}

interface ConversionResult {
  success: boolean;
  leadId: string;
  opportunityId?: string;
  error?: string;
}

interface BulkConvertLeadsDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  selectedLeads: Array<{ id: string; title: string }>;
  onSuccess?: () => void;
}

export function BulkConvertLeadsDialog({
  open,
  onOpenChange,
  selectedLeads,
  onSuccess,
}: BulkConvertLeadsDialogProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [isCheckingEligibility, setIsCheckingEligibility] = useState(false);
  const [eligibilityResults, setEligibilityResults] = useState<LeadConversionEligibility[]>([]);
  const [conversionResults, setConversionResults] = useState<ConversionResult[]>([]);
  const [conversionProgress, setConversionProgress] = useState(0);
  const [salesStages, setSalesStages] = useState<Array<{ id: string; name: string }>>([]);
  const [users, setUsers] = useState<Array<{ id: string; firstName: string; lastName: string; email: string }>>([]);

  const form = useForm<BulkConvertFormData>({
    resolver: zodResolver(bulkConvertSchema),
    defaultValues: {
      priority: 'medium',
      stage: 'qualification',
    },
  });

  // Check eligibility when dialog opens
  useEffect(() => {
    if (open && selectedLeads.length > 0) {
      checkEligibility();
      loadSalesStages();
      loadUsers();
      // Reset form with proper defaults
      form.reset({
        priority: 'medium',
        stage: 'qualification',
      });
    }
  }, [open, selectedLeads]);

  const checkEligibility = async () => {
    setIsCheckingEligibility(true);
    try {
      const response = await fetch('/api/leads/conversion/eligibility', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ leadIds: selectedLeads.map(lead => lead.id) }),
      });

      if (!response.ok) {
        throw new Error('Failed to check eligibility');
      }

      const data = await response.json();
      setEligibilityResults(data.data);
    } catch (error) {
      console.error('Error checking eligibility:', error);
      toast.error('Failed to check conversion eligibility');
    } finally {
      setIsCheckingEligibility(false);
    }
  };

  const loadSalesStages = async () => {
    try {
      const response = await fetch('/api/opportunities/stages');
      if (response.ok) {
        const data = await response.json();
        // Opportunity stages API returns array directly
        setSalesStages(Array.isArray(data) ? data : []);
      }
    } catch (error) {
      console.error('Error loading opportunity stages:', error);
      setSalesStages([]);
    }
  };

  const loadUsers = async () => {
    try {
      const response = await fetch('/api/users');
      if (response.ok) {
        const result = await response.json();
        // Handle the API response format
        if (result.success && result.data) {
          setUsers(result.data);
        } else {
          setUsers([]);
        }
      }
    } catch (error) {
      console.error('Error loading users:', error);
      setUsers([]);
    }
  };

  const onSubmit = async (data: BulkConvertFormData) => {
    const eligibleLeads = eligibilityResults.filter(result => result.eligible);
    
    if (eligibleLeads.length === 0) {
      toast.error('No leads are eligible for conversion');
      return;
    }

    setIsLoading(true);
    setConversionProgress(0);
    setConversionResults([]);

    try {
      const response = await fetch('/api/leads/conversion/bulk', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          leadIds: eligibleLeads.map(lead => lead.leadId),
          conversionData: {
            stage: data.stage,
            priority: data.priority,
            ownerId: data.ownerId,
            notes: data.notes,
          },
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to convert leads');
      }

      const result = await response.json();
      setConversionResults([...result.data.successful, ...result.data.failed]);
      setConversionProgress(100);

      const successCount = result.data.successful.length;
      const failedCount = result.data.failed.length;

      if (successCount > 0) {
        toast.success(`Successfully converted ${successCount} lead${successCount > 1 ? 's' : ''} to opportunities`);
      }
      
      if (failedCount > 0) {
        toast.error(`Failed to convert ${failedCount} lead${failedCount > 1 ? 's' : ''}`);
      }

      if (successCount > 0) {
        onSuccess?.();
      }
    } catch (error) {
      console.error('Error converting leads:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to convert leads');
    } finally {
      setIsLoading(false);
    }
  };

  const eligibleCount = eligibilityResults.filter(result => result.eligible).length;
  const ineligibleCount = eligibilityResults.length - eligibleCount;

  const getLeadTitle = (leadId: string) => {
    return selectedLeads.find(lead => lead.id === leadId)?.title || 'Unknown Lead';
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[700px] max-h-[80vh]">
        <DialogHeader>
          <DialogTitle>Bulk Convert Leads to Opportunities</DialogTitle>
          <DialogDescription>
            Convert {selectedLeads.length} selected leads to opportunities in your sales pipeline.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Eligibility Summary */}
          <div className="grid grid-cols-3 gap-4">
            <div className="text-center p-3 bg-blue-50 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">{selectedLeads.length}</div>
              <div className="text-sm text-blue-600">Total Selected</div>
            </div>
            <div className="text-center p-3 bg-green-50 rounded-lg">
              <div className="text-2xl font-bold text-green-600">
                {isCheckingEligibility ? '...' : eligibleCount}
              </div>
              <div className="text-sm text-green-600">Eligible</div>
            </div>
            <div className="text-center p-3 bg-red-50 rounded-lg">
              <div className="text-2xl font-bold text-red-600">
                {isCheckingEligibility ? '...' : ineligibleCount}
              </div>
              <div className="text-sm text-red-600">Ineligible</div>
            </div>
          </div>

          {/* Eligibility Details */}
          {eligibilityResults.length > 0 && (
            <div>
              <h4 className="font-medium mb-3">Eligibility Details</h4>
              <ScrollArea className="h-32 border rounded-lg p-3">
                <div className="space-y-2">
                  {eligibilityResults.map((result) => (
                    <div key={result.leadId} className="flex items-center justify-between text-sm">
                      <div className="flex items-center gap-2">
                        {result.eligible ? (
                          <CheckCircle className="h-4 w-4 text-green-500" />
                        ) : (
                          <XCircle className="h-4 w-4 text-red-500" />
                        )}
                        <span className="font-medium">{getLeadTitle(result.leadId)}</span>
                        <Badge variant={result.eligible ? 'default' : 'destructive'} className="text-xs">
                          {result.qualificationScore || 0}/100
                        </Badge>
                      </div>
                      {!result.eligible && (
                        <span className="text-red-600 text-xs max-w-xs truncate">
                          {result.reason}
                        </span>
                      )}
                    </div>
                  ))}
                </div>
              </ScrollArea>
            </div>
          )}

          {/* Conversion Progress */}
          {isLoading && (
            <div>
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium">Converting leads...</span>
                <span className="text-sm text-gray-500">{conversionProgress}%</span>
              </div>
              <Progress value={conversionProgress} className="h-2" />
            </div>
          )}

          {/* Conversion Results */}
          {conversionResults.length > 0 && (
            <div>
              <h4 className="font-medium mb-3">Conversion Results</h4>
              <ScrollArea className="h-32 border rounded-lg p-3">
                <div className="space-y-2">
                  {conversionResults.map((result) => (
                    <div key={result.leadId} className="flex items-center justify-between text-sm">
                      <div className="flex items-center gap-2">
                        {result.success ? (
                          <CheckCircle className="h-4 w-4 text-green-500" />
                        ) : (
                          <XCircle className="h-4 w-4 text-red-500" />
                        )}
                        <span className="font-medium">{getLeadTitle(result.leadId)}</span>
                      </div>
                      {result.error && (
                        <span className="text-red-600 text-xs max-w-xs truncate">
                          {result.error}
                        </span>
                      )}
                    </div>
                  ))}
                </div>
              </ScrollArea>
            </div>
          )}

          {/* Conversion Form */}
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="stage"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Initial Stage</FormLabel>
                      <Select onValueChange={field.onChange} value={field.value || 'qualification'}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select stage" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {salesStages.map((stage) => (
                            <SelectItem key={stage.id} value={stage.name}>
                              {stage.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="priority"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Priority</FormLabel>
                      <Select onValueChange={field.onChange} value={field.value || 'medium'}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select priority" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="low">Low</SelectItem>
                          <SelectItem value="medium">Medium</SelectItem>
                          <SelectItem value="high">High</SelectItem>
                          <SelectItem value="urgent">Urgent</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="ownerId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Opportunity Owner</FormLabel>
                    <Select onValueChange={field.onChange} value={field.value || ''}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Leave empty to use lead owners" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {users.map((user) => (
                          <SelectItem key={user.id} value={user.id}>
                            {user.firstName} {user.lastName} {user.email ? `(${user.email})` : ''}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="notes"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Conversion Notes</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Add notes about this bulk conversion..."
                        className="resize-none"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      These notes will be applied to all converted opportunities.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <DialogFooter>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => onOpenChange(false)}
                  disabled={isLoading}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  disabled={isLoading || isCheckingEligibility || eligibleCount === 0}
                  className="min-w-[140px]"
                >
                  {isLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Converting...
                    </>
                  ) : (
                    `Convert ${eligibleCount} Lead${eligibleCount !== 1 ? 's' : ''}`
                  )}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </div>
      </DialogContent>
    </Dialog>
  );
}
