'use client';

import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { SearchableSelect } from '@/components/ui/searchable-select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { AlertCircle, Loader2, MessageSquare } from 'lucide-react';

interface CreateThreadDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onCreateThread: (threadData: NewThreadData) => Promise<void>;
  creating: boolean;
}

export interface NewThreadData {
  title: string;
  category: string;
  priority: string;
  assignedToId?: string;
  initialMessage?: string;
}

const CATEGORY_OPTIONS = [
  { value: 'general', label: 'General' },
  { value: 'technical', label: 'Technical' },
  { value: 'requirements', label: 'Requirements' },
  { value: 'timeline', label: 'Timeline' },
  { value: 'budget', label: 'Budget' },
];

const PRIORITY_OPTIONS = [
  { value: 'low', label: 'Low' },
  { value: 'medium', label: 'Medium' },
  { value: 'high', label: 'High' },
  { value: 'urgent', label: 'Urgent' },
];

export default function CreateThreadDialog({
  open,
  onOpenChange,
  onCreateThread,
  creating,
}: CreateThreadDialogProps) {
  const [formData, setFormData] = useState<NewThreadData>({
    title: '',
    category: 'general',
    priority: 'medium',
    assignedToId: '',
    initialMessage: '',
  });
  const [error, setError] = useState<string | null>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.title.trim()) {
      setError('Thread title is required');
      return;
    }

    try {
      setError(null);
      await onCreateThread(formData);
      // Reset form on success
      setFormData({
        title: '',
        category: 'general',
        priority: 'medium',
        assignedToId: '',
        initialMessage: '',
      });
      onOpenChange(false);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create thread');
    }
  };

  const handleCancel = () => {
    setFormData({
      title: '',
      category: 'general',
      priority: 'medium',
      assignedToId: '',
      initialMessage: '',
    });
    setError(null);
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <MessageSquare className="w-5 h-5" />
            Create New Q&A Thread
          </DialogTitle>
          <DialogDescription>
            Start a new discussion thread about this handover. Add details to help others understand your question or topic.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* Thread Title */}
          <div className="space-y-2">
            <Label htmlFor="title">
              Thread Title <span className="text-red-500">*</span>
            </Label>
            <Input
              id="title"
              value={formData.title}
              onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
              placeholder="Enter a descriptive title for your thread"
              disabled={creating}
              className="mt-1"
            />
          </div>

          {/* Category and Priority */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="category">Category</Label>
              <SearchableSelect
                options={CATEGORY_OPTIONS}
                value={formData.category}
                onValueChange={(value) => setFormData(prev => ({ ...prev, category: value }))}
                placeholder="Select category"
                searchPlaceholder="Search categories..."
                emptyMessage="No categories found"
                disabled={creating}
                className="mt-1"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="priority">Priority</Label>
              <SearchableSelect
                options={PRIORITY_OPTIONS}
                value={formData.priority}
                onValueChange={(value) => setFormData(prev => ({ ...prev, priority: value }))}
                placeholder="Select priority"
                searchPlaceholder="Search priorities..."
                emptyMessage="No priorities found"
                disabled={creating}
                className="mt-1"
              />
            </div>
          </div>

          {/* Initial Message */}
          <div className="space-y-2">
            <Label htmlFor="initialMessage">Initial Message (Optional)</Label>
            <Textarea
              id="initialMessage"
              value={formData.initialMessage}
              onChange={(e) => setFormData(prev => ({ ...prev, initialMessage: e.target.value }))}
              placeholder="Add your question or initial message here..."
              rows={4}
              disabled={creating}
              className="mt-1"
            />
          </div>

          {/* Form Actions */}
          <div className="flex gap-3 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={handleCancel}
              disabled={creating}
              className="flex-1"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={creating || !formData.title.trim()}
              className="flex-1"
            >
              {creating && <Loader2 className="w-4 h-4 mr-2 animate-spin" />}
              Create Thread
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
