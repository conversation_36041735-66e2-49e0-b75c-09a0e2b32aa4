import { NextRequest, NextResponse } from 'next/server';
import { withSuperAdminAuth } from '@/lib/tenant-context';
import { FeatureFlagService } from '@/services/feature-flag';
import { z } from 'zod';

const updateFeatureFlagSchema = z.object({
  name: z.string().min(1).max(100).optional(),
  description: z.string().optional(),
  isEnabled: z.boolean().optional(),
  requiredPlanLevel: z.number().int().min(1).max(10).optional()
});

/**
 * @swagger
 * /api/admin/feature-flags/{id}:
 *   get:
 *     summary: Get feature flag by ID (Super Admin only)
 *     description: Retrieve a specific feature flag by its ID
 *     tags:
 *       - Super Admin - Feature Flags
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Feature flag ID
 *     responses:
 *       200:
 *         description: Feature flag retrieved successfully
 *       404:
 *         description: Feature flag not found
 */
export const GET = withSuperAdminAuth(async (
  request: NextRequest,
  { params }: { params: { id: string } }
) => {
  try {
    const featureFlagService = new FeatureFlagService();
    const featureFlag = await featureFlagService.getFeatureFlagById(params.id);

    if (!featureFlag) {
      return NextResponse.json(
        { error: 'Feature flag not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: featureFlag
    });

  } catch (error) {
    console.error('Get feature flag error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
});

/**
 * @swagger
 * /api/admin/feature-flags/{id}:
 *   put:
 *     summary: Update feature flag (Super Admin only)
 *     description: Update a specific feature flag
 *     tags:
 *       - Super Admin - Feature Flags
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Feature flag ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *               description:
 *                 type: string
 *               isEnabled:
 *                 type: boolean
 *               requiredPlanLevel:
 *                 type: number
 *     responses:
 *       200:
 *         description: Feature flag updated successfully
 *       400:
 *         description: Invalid input data
 *       404:
 *         description: Feature flag not found
 */
export const PUT = withSuperAdminAuth(async (
  request: NextRequest,
  { params }: { params: { id: string } }
) => {
  try {
    const body = await request.json();
    const validatedData = updateFeatureFlagSchema.parse(body);

    const featureFlagService = new FeatureFlagService();
    const featureFlag = await featureFlagService.updateFeatureFlag(params.id, validatedData);

    return NextResponse.json({
      success: true,
      data: featureFlag
    });

  } catch (error) {
    console.error('Update feature flag error:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid input data', details: error.errors },
        { status: 400 }
      );
    }

    if (error instanceof Error && error.message.includes('not found')) {
      return NextResponse.json(
        { error: error.message },
        { status: 404 }
      );
    }

    if (error instanceof Error && error.message.includes('already exists')) {
      return NextResponse.json(
        { error: error.message },
        { status: 409 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
});

/**
 * @swagger
 * /api/admin/feature-flags/{id}:
 *   delete:
 *     summary: Delete feature flag (Super Admin only)
 *     description: Delete a specific feature flag
 *     tags:
 *       - Super Admin - Feature Flags
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Feature flag ID
 *     responses:
 *       200:
 *         description: Feature flag deleted successfully
 *       404:
 *         description: Feature flag not found
 */
export const DELETE = withSuperAdminAuth(async (
  request: NextRequest,
  { params }: { params: { id: string } }
) => {
  try {
    const featureFlagService = new FeatureFlagService();
    await featureFlagService.deleteFeatureFlag(params.id);

    return NextResponse.json({
      success: true,
      message: 'Feature flag deleted successfully'
    });

  } catch (error) {
    console.error('Delete feature flag error:', error);
    
    if (error instanceof Error && error.message.includes('not found')) {
      return NextResponse.json(
        { error: error.message },
        { status: 404 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
});

/**
 * @swagger
 * /api/admin/feature-flags/{id}/toggle:
 *   post:
 *     summary: Toggle feature flag status (Super Admin only)
 *     description: Toggle the enabled/disabled status of a feature flag
 *     tags:
 *       - Super Admin - Feature Flags
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Feature flag ID
 *     responses:
 *       200:
 *         description: Feature flag toggled successfully
 *       404:
 *         description: Feature flag not found
 */
export const POST = withSuperAdminAuth(async (
  request: NextRequest,
  { params }: { params: { id: string } }
) => {
  try {
    const featureFlagService = new FeatureFlagService();
    const featureFlag = await featureFlagService.toggleFeatureFlag(params.id);

    return NextResponse.json({
      success: true,
      data: featureFlag,
      message: `Feature flag ${featureFlag.isEnabled ? 'enabled' : 'disabled'} successfully`
    });

  } catch (error) {
    console.error('Toggle feature flag error:', error);
    
    if (error instanceof Error && error.message.includes('not found')) {
      return NextResponse.json(
        { error: error.message },
        { status: 404 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
});
