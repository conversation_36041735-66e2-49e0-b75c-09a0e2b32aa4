import { NextResponse } from 'next/server';
import { z } from 'zod';
import { withPermission, AuthenticatedRequest } from '@/lib/permission-middleware';
import { roleManagementService } from '@/services/role-management';
import { permissionService } from '@/services/permission-service';
import { PermissionAction, PermissionResource } from '@/types';

const updatePermissionsSchema = z.object({
  permissions: z.array(z.string()).min(0, 'Permissions array is required')
});

interface RouteParams {
  params: Promise<{
    id: string;
  }>;
}

/**
 * GET /api/roles/[id]/permissions
 * Get detailed permission assignments for a specific role
 */
export const GET = withPermission({
  resource: PermissionResource.ROLES,
  action: PermissionAction.READ,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest, { params }: RouteParams) => {
  try {
    const { id: roleId } = await params;

    const rolePermissions = await roleManagementService.getRolePermissions(roleId, req.tenantId);

    return NextResponse.json({
      success: true,
      data: {
        roleId,
        permissions: rolePermissions
      }
    });
  } catch (error) {
    console.error('Get role permissions error:', error);
    
    if (error instanceof Error && error.message.includes('not found')) {
      return NextResponse.json(
        { error: 'Role not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to fetch role permissions' },
      { status: 500 }
    );
  }
});

/**
 * PUT /api/roles/[id]/permissions
 * Update permission assignments for a specific role
 */
export const PUT = withPermission({
  resource: PermissionResource.ROLES,
  action: PermissionAction.UPDATE,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest, { params }: RouteParams) => {
  try {
    const { id: roleId } = await params;
    const body = await req.json();
    const validatedData = updatePermissionsSchema.parse(body);

    // Check if this is a system role and if user has permission to edit it
    const role = await roleManagementService.getRoleById(roleId, req.tenantId);
    if (!role) {
      return NextResponse.json(
        { error: 'Role not found' },
        { status: 404 }
      );
    }

    if (role.isSystemRole) {
      // Check for system role management permission
      const hasSystemRolePermission = await permissionService.checkUserPermission(
        req.userId,
        req.tenantId,
        PermissionResource.ROLES,
        PermissionAction.MANAGE_SYSTEM_ROLES
      );

      if (!hasSystemRolePermission) {
        return NextResponse.json(
          { error: 'Insufficient permissions to modify system roles' },
          { status: 403 }
        );
      }

      // Use system role update method
      await roleManagementService.updateSystemRolePermissions(
        roleId,
        req.tenantId,
        validatedData.permissions,
        req.userId
      );
    } else {
      // Regular role update
      await roleManagementService.updateRolePermissions(
        roleId,
        req.tenantId,
        validatedData.permissions,
        req.userId
      );
    }

    return NextResponse.json({
      success: true,
      message: `${role.isSystemRole ? 'System role' : 'Role'} permissions updated successfully`
    });
  } catch (error) {
    console.error('Update role permissions error:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          error: 'Validation failed',
          details: error.errors.map(e => ({
            field: e.path.join('.'),
            message: e.message
          }))
        },
        { status: 400 }
      );
    }

    if (error instanceof Error) {
      if (error.message.includes('not found')) {
        return NextResponse.json(
          { error: 'Role not found' },
          { status: 404 }
        );
      }
      if (error.message.includes('privilege escalation')) {
        return NextResponse.json(
          { error: 'Cannot assign permissions you do not have' },
          { status: 403 }
        );
      }
    }

    return NextResponse.json(
      { error: 'Failed to update role permissions' },
      { status: 500 }
    );
  }
});
