import { NextResponse } from 'next/server';
import { ActivityManagementService } from '@/services/activity-management';
import { withPermission, AuthenticatedRequest } from '@/lib/permission-middleware';
import { PermissionResource, PermissionAction } from '@/types';

const activityService = new ActivityManagementService();

/**
 * GET /api/activities/[activityId]
 * Get a specific activity by ID
 */
export const GET = withPermission({
  resource: PermissionResource.CONTACTS, // Using contacts permission for now
  action: PermissionAction.READ,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest) => {
  try {
    const url = new URL(req.url);
    const pathSegments = url.pathname.split('/');
    const activityId = pathSegments[pathSegments.indexOf('activities') + 1];

    const activity = await activityService.getActivity(activityId, req.tenantId);

    if (!activity) {
      return NextResponse.json(
        { error: 'Activity not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: { activity },
      message: 'Activity retrieved successfully'
    });

  } catch (error) {
    console.error('Get activity error:', error);

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
});

/**
 * PUT /api/activities/[activityId]
 * Update a specific activity
 */
export const PUT = withPermission({
  resource: PermissionResource.CONTACTS, // Using contacts permission for now
  action: PermissionAction.UPDATE,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest) => {
  try {
    const url = new URL(req.url);
    const pathSegments = url.pathname.split('/');
    const activityId = pathSegments[pathSegments.indexOf('activities') + 1];
    const body = await req.json();

    const activity = await activityService.updateActivity(
      activityId,
      req.tenantId,
      body,
      req.userId
    );

    return NextResponse.json({
      success: true,
      data: { activity },
      message: 'Activity updated successfully'
    });

  } catch (error) {
    console.error('Update activity error:', error);

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
});

/**
 * DELETE /api/activities/[activityId]
 * Delete a specific activity
 */
export const DELETE = withPermission({
  resource: PermissionResource.CONTACTS, // Using contacts permission for now
  action: PermissionAction.DELETE,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest) => {
  try {
    const url = new URL(req.url);
    const pathSegments = url.pathname.split('/');
    const activityId = pathSegments[pathSegments.indexOf('activities') + 1];

    await activityService.deleteActivity(activityId, req.tenantId);

    return NextResponse.json({
      success: true,
      message: 'Activity deleted successfully'
    });

  } catch (error) {
    console.error('Delete activity error:', error);

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
});

/**
 * PATCH /api/activities/[activityId]/complete
 * Mark activity as completed
 */
export const PATCH = withPermission({
  resource: PermissionResource.CONTACTS, // Using contacts permission for now
  action: PermissionAction.UPDATE,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest) => {
  try {
    const url = new URL(req.url);
    const pathSegments = url.pathname.split('/');
    const activityId = pathSegments[pathSegments.indexOf('activities') + 1];

    const activity = await activityService.completeActivity(activityId, req.tenantId);

    return NextResponse.json({
      success: true,
      data: { activity },
      message: 'Activity marked as completed'
    });

  } catch (error) {
    console.error('Complete activity error:', error);

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
});
