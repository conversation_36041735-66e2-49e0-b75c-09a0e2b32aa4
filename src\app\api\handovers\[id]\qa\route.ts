import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { handoverQAService } from '@/services/handover-qa';
import { z } from 'zod';

// Request validation schemas
const getQAThreadsQuerySchema = z.object({
  status: z.string().optional(),
  priority: z.string().optional(),
  category: z.string().optional(),
  assignedToId: z.string().optional(),
  includeMessages: z.coerce.boolean().default(true),
});

const createQAThreadSchema = z.object({
  title: z.string().min(1, 'Title is required').max(255),
  category: z.enum(['general', 'technical', 'requirements', 'timeline', 'budget']).default('general'),
  priority: z.enum(['low', 'medium', 'high', 'urgent']).default('medium'),
  assignedToId: z.string().optional().nullable().transform(val => val === '' ? null : val),
});

interface RouteParams {
  params: {
    id: string;
  };
}

/**
 * GET /api/handovers/[id]/qa - Get Q&A threads for a handover
 */
export async function GET(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get tenantId from user's tenants array (users have one tenant in this system)
    let tenantId: string | null = null;
    if (session.user.tenants && session.user.tenants.length > 0) {
      tenantId = session.user.tenants[0].id;
    }

    if (!tenantId) {
      return NextResponse.json({ error: 'Tenant not found' }, { status: 400 });
    }

    // Parse and validate query parameters
    const url = new URL(request.url);
    const queryParams = Object.fromEntries(url.searchParams.entries());
    
    const validatedQuery = getQAThreadsQuerySchema.parse(queryParams);

    const resolvedParams = await params;
    const threads = await handoverQAService.getQAThreads(
      tenantId,
      resolvedParams.id,
      validatedQuery
    );

    return NextResponse.json({ threads });
  } catch (error) {
    console.error('Error fetching Q&A threads:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid query parameters', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to fetch Q&A threads' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/handovers/[id]/qa - Create a new Q&A thread
 */
export async function POST(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get tenantId from user's tenants array (users have one tenant in this system)
    let tenantId: string | null = null;
    if (session.user.tenants && session.user.tenants.length > 0) {
      tenantId = session.user.tenants[0].id;
    }

    if (!tenantId) {
      return NextResponse.json({ error: 'Tenant not found' }, { status: 400 });
    }

    const body = await request.json();
    const validatedData = createQAThreadSchema.parse(body);

    const resolvedParams = await params;
    const thread = await handoverQAService.createQAThread(
      tenantId,
      resolvedParams.id,
      validatedData,
      session.user.id
    );

    return NextResponse.json(thread, { status: 201 });
  } catch (error) {
    console.error('Error creating Q&A thread:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request data', details: error.errors },
        { status: 400 }
      );
    }

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to create Q&A thread' },
      { status: 500 }
    );
  }
}
