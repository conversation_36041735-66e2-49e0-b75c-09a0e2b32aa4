# syntax=docker/dockerfile:1

# =============================================================================
# CRM Platform Dockerfile
# Multi-stage build for optimal production image size and security
# =============================================================================

# -----------------------------------------------------------------------------
# Base Stage: Common dependencies and system packages
# -----------------------------------------------------------------------------
FROM node:20.12-alpine3.19 AS base

# Install system dependencies required for the application
# - libc6-compat: Compatibility layer for glibc
# - chromium: For Puppeteer/Mermaid PNG generation
# - nss, freetype, harfbuzz: Font rendering dependencies
# - ca-certificates: SSL certificate validation
# - ttf-freefont: Font files for rendering
# - dumb-init: Proper signal handling in containers
RUN apk add --no-cache \
    libc6-compat \
    openssl \
    curl \
    wget \
    chromium \
    nss \
    freetype \
    freetype-dev \
    harfbuzz \
    ca-certificates \
    ttf-freefont \
    font-noto-emoji \
    wqy-zenhei \
    dumb-init \
    && rm -rf /var/cache/apk/*

# Configure Puppeteer to use system Chromium
ENV PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true \
    PUPPETEER_EXECUTABLE_PATH=/usr/bin/chromium-browser \
    PUPPETEER_ARGS="--no-sandbox --disable-setuid-sandbox --disable-dev-shm-usage --disable-accelerated-2d-canvas --no-first-run --no-zygote --disable-gpu"

# Set working directory
WORKDIR /app

# -----------------------------------------------------------------------------
# Dependencies Stage: Install Node.js dependencies
# -----------------------------------------------------------------------------
FROM base AS deps

# Copy package files for dependency installation
COPY package.json package-lock.json* ./

# Install production dependencies with npm ci for faster, reliable builds
RUN npm ci --only=production && npm cache clean --force

# -----------------------------------------------------------------------------
# Development Dependencies Stage: For development builds
# -----------------------------------------------------------------------------
FROM base AS deps-dev

# Copy package files
COPY package.json package-lock.json* ./

# Install all dependencies (including dev dependencies)
RUN npm ci && npm cache clean --force

# -----------------------------------------------------------------------------
# Development Stage: For local development with hot reloading
# -----------------------------------------------------------------------------
FROM base AS development

# Copy all dependencies (including dev dependencies)
COPY --from=deps-dev /app/node_modules ./node_modules

# Copy source code
COPY . .

# Generate Prisma client
RUN npx prisma generate

# Create necessary directories with proper permissions
RUN mkdir -p ./uploads /tmp/mermaid && \
    chmod 755 ./uploads /tmp/mermaid

# Make scripts executable if they exist
RUN find ./scripts -name "*.sh" -type f -exec chmod +x {} \; 2>/dev/null || true

# Expose development port
EXPOSE 3000

# Development command with hot reloading
CMD ["npm", "run", "dev"]

# -----------------------------------------------------------------------------
# Builder Stage: Build the application for production
# -----------------------------------------------------------------------------
FROM base AS builder

# Copy production dependencies
COPY --from=deps /app/node_modules ./node_modules

# Copy source code
COPY . .

# Generate Prisma client
RUN npx prisma generate

# Install Chrome headless shell for Puppeteer (required for Mermaid PNG generation)
RUN npx puppeteer browsers install chrome-headless-shell

# Build the application
ENV NEXT_TELEMETRY_DISABLED=1 \
    NODE_ENV=production

RUN npm run build

# -----------------------------------------------------------------------------
# Production Stage: Optimized runtime image
# -----------------------------------------------------------------------------
FROM base AS production

# Set production environment
ENV NODE_ENV=production \
    NEXT_TELEMETRY_DISABLED=1 \
    PORT=3000 \
    HOSTNAME="0.0.0.0"

# Mermaid PNG generation environment variables
ENV PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true \
    PUPPETEER_EXECUTABLE_PATH=/usr/bin/chromium-browser \
    PUPPETEER_ARGS="--no-sandbox --disable-setuid-sandbox --disable-dev-shm-usage --disable-accelerated-2d-canvas --no-first-run --no-zygote --disable-gpu" \
    MERMAID_TEMP_DIR="/tmp/mermaid" \
    CHROME_BIN=/usr/bin/chromium-browser

# Create non-root user for security
RUN addgroup --system --gid 1001 nodejs && \
    adduser --system --uid 1001 nextjs

# Copy built application from builder stage
COPY --from=builder --chown=nextjs:nodejs /app/public ./public
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

# Copy Prisma files for database operations
COPY --from=builder --chown=nextjs:nodejs /app/node_modules/.prisma ./node_modules/.prisma
COPY --from=builder --chown=nextjs:nodejs /app/prisma ./prisma

# Copy necessary scripts
COPY --from=builder --chown=nextjs:nodejs /app/scripts ./scripts

# Create application directories with proper permissions
RUN mkdir -p ./uploads /tmp/mermaid && \
    chown -R nextjs:nodejs ./uploads /tmp/mermaid && \
    chmod 755 ./uploads /tmp/mermaid

# Switch to non-root user
USER nextjs

# Expose application port
EXPOSE 3000

# Health check to ensure container is healthy
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
    CMD curl -f http://localhost:3000/api/health || exit 1

# Use dumb-init to handle signals properly
ENTRYPOINT ["dumb-init", "--"]

# Start the application
CMD ["node", "server.js"]
