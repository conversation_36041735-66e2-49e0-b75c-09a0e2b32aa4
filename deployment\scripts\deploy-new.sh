#!/bin/bash
# =============================================================================
# CRM Platform Deployment Script
# Supports multiple environments with Docker containerization
# =============================================================================

set -euo pipefail  # Exit on error, undefined vars, pipe failures

# -----------------------------------------------------------------------------
# Configuration and Constants
# -----------------------------------------------------------------------------
readonly SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
readonly PROJECT_ROOT="$(cd "${SCRIPT_DIR}/../.." && pwd)"
readonly DEPLOYMENT_DIR="$(cd "${SCRIPT_DIR}/.." && pwd)"

# Default values
ENVIRONMENT="${1:-development}"
COMPOSE_FILE="${DEPLOYMENT_DIR}/docker/docker-compose.new.yml"
COMPOSE_OVERRIDE=""
BUILD_IMAGES="${BUILD:-true}"
RUN_MIGRATIONS="${MIGRATE:-true}"
GENERATE_SSL="${SSL:-auto}"
VERBOSE="${VERBOSE:-false}"

# Colors for output
readonly RED='\033[0;31m'
readonly GREEN='\033[0;32m'
readonly YELLOW='\033[1;33m'
readonly BLUE='\033[0;34m'
readonly NC='\033[0m' # No Color

# -----------------------------------------------------------------------------
# Utility Functions
# -----------------------------------------------------------------------------

# Print colored output
print_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
print_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
print_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
print_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# Print usage information
usage() {
  cat << EOF
Usage: $0 [ENVIRONMENT] [OPTIONS]

ENVIRONMENTS:
  development    Local development with hot reloading (default)
  staging        Pre-production testing environment
  production     Production environment with optimizations

OPTIONS:
  --no-build     Skip building Docker images
  --no-migrate   Skip database migrations
  --ssl          Force SSL certificate generation
  --no-ssl       Skip SSL certificate generation
  --verbose      Enable verbose output
  --help         Show this help message

EXAMPLES:
  $0                           # Deploy to development
  $0 production                # Deploy to production
  $0 staging --no-build        # Deploy to staging without rebuilding
  $0 production --ssl          # Deploy to production with SSL

ENVIRONMENT VARIABLES:
  BUILD=false                  # Skip building images
  MIGRATE=false                # Skip migrations
  SSL=true                     # Force SSL generation
  VERBOSE=true                 # Enable verbose output

EOF
  exit 1
}

# -----------------------------------------------------------------------------
# Validation Functions
# -----------------------------------------------------------------------------

# Check if Docker and Docker Compose are installed
check_prerequisites() {
  print_info "Checking prerequisites..."
  
  if ! command -v docker &> /dev/null; then
    print_error "Docker is not installed or not in PATH"
    print_info "Please install Docker: https://docs.docker.com/get-docker/"
    exit 1
  fi
  
  if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
    print_error "Docker Compose is not installed or not in PATH"
    print_info "Please install Docker Compose: https://docs.docker.com/compose/install/"
    exit 1
  fi
  
  # Check if Docker daemon is running
  if ! docker info &> /dev/null; then
    print_error "Docker daemon is not running"
    print_info "Please start Docker daemon"
    exit 1
  fi
  
  print_success "Prerequisites check passed"
}

# Validate environment
validate_environment() {
  case "$ENVIRONMENT" in
    development|dev)
      ENVIRONMENT="development"
      COMPOSE_OVERRIDE="-f ${DEPLOYMENT_DIR}/docker/environments/development.yml"
      ;;
    staging|stage)
      ENVIRONMENT="staging"
      COMPOSE_OVERRIDE="-f ${DEPLOYMENT_DIR}/docker/environments/staging.yml"
      ;;
    production|prod)
      ENVIRONMENT="production"
      COMPOSE_OVERRIDE="-f ${DEPLOYMENT_DIR}/docker/environments/production.yml"
      ;;
    *)
      print_error "Unknown environment: $ENVIRONMENT"
      print_info "Supported environments: development, staging, production"
      exit 1
      ;;
  esac
  
  print_info "Environment: $ENVIRONMENT"
}

# -----------------------------------------------------------------------------
# SSL Certificate Management
# -----------------------------------------------------------------------------

# Generate SSL certificates if needed
generate_ssl_certificates() {
  local ssl_dir="${DEPLOYMENT_DIR}/config/nginx/ssl"
  local cert_file="${ssl_dir}/server.crt"
  local key_file="${ssl_dir}/server.key"
  
  # Check if SSL generation is needed
  if [[ "$GENERATE_SSL" == "false" ]]; then
    print_info "Skipping SSL certificate generation"
    return 0
  fi
  
  if [[ "$GENERATE_SSL" == "auto" ]] && [[ "$ENVIRONMENT" == "development" ]]; then
    print_info "Skipping SSL for development environment"
    return 0
  fi
  
  if [[ -f "$cert_file" ]] && [[ -f "$key_file" ]] && [[ "$GENERATE_SSL" != "true" ]]; then
    print_info "SSL certificates already exist"
    return 0
  fi
  
  print_info "Generating SSL certificates..."
  
  # Create SSL directory if it doesn't exist
  mkdir -p "$ssl_dir"
  
  # Run SSL generation script
  if [[ -f "${DEPLOYMENT_DIR}/scripts/ssl/generate-certs.sh" ]]; then
    bash "${DEPLOYMENT_DIR}/scripts/ssl/generate-certs.sh"
    print_success "SSL certificates generated successfully"
  else
    print_warning "SSL generation script not found, skipping..."
  fi
}

# -----------------------------------------------------------------------------
# Environment File Management
# -----------------------------------------------------------------------------

# Check and create environment files
setup_environment_files() {
  print_info "Setting up environment files..."
  
  local env_file="${PROJECT_ROOT}/.env.docker"
  local env_example="${PROJECT_ROOT}/.env.example"
  
  if [[ ! -f "$env_file" ]]; then
    if [[ -f "$env_example" ]]; then
      print_warning ".env.docker not found, copying from .env.example"
      cp "$env_example" "$env_file"
    else
      print_warning ".env.docker not found, creating minimal configuration"
      cat > "$env_file" << EOF
# CRM Platform Environment Configuration
NODE_ENV=${ENVIRONMENT}
DATABASE_URL=**************************************/crm_platform
REDIS_URL=redis://redis:6379
NEXTAUTH_SECRET=your-secret-key-here
NEXTAUTH_URL=http://localhost:3010
EOF
    fi
  fi
  
  print_success "Environment files configured"
}

# -----------------------------------------------------------------------------
# Docker Operations
# -----------------------------------------------------------------------------

# Build Docker images
build_images() {
  if [[ "$BUILD_IMAGES" != "true" ]]; then
    print_info "Skipping image build"
    return 0
  fi
  
  print_info "Building Docker images..."
  
  cd "$PROJECT_ROOT"
  
  if [[ "$VERBOSE" == "true" ]]; then
    docker-compose -f "$COMPOSE_FILE" $COMPOSE_OVERRIDE build --no-cache
  else
    docker-compose -f "$COMPOSE_FILE" $COMPOSE_OVERRIDE build --no-cache > /dev/null 2>&1
  fi
  
  print_success "Docker images built successfully"
}

# Pull latest images
pull_images() {
  print_info "Pulling latest images..."
  
  cd "$PROJECT_ROOT"
  
  if [[ "$VERBOSE" == "true" ]]; then
    docker-compose -f "$COMPOSE_FILE" $COMPOSE_OVERRIDE pull
  else
    docker-compose -f "$COMPOSE_FILE" $COMPOSE_OVERRIDE pull > /dev/null 2>&1
  fi
  
  print_success "Images pulled successfully"
}

# Start services
start_services() {
  print_info "Starting services..."
  
  cd "$PROJECT_ROOT"
  
  if [[ "$VERBOSE" == "true" ]]; then
    docker-compose -f "$COMPOSE_FILE" $COMPOSE_OVERRIDE up -d
  else
    docker-compose -f "$COMPOSE_FILE" $COMPOSE_OVERRIDE up -d > /dev/null 2>&1
  fi
  
  print_success "Services started successfully"
}

# Run database migrations
run_migrations() {
  if [[ "$RUN_MIGRATIONS" != "true" ]]; then
    print_info "Skipping database migrations"
    return 0
  fi
  
  print_info "Running database migrations..."
  
  cd "$PROJECT_ROOT"
  
  # Wait for database to be ready
  print_info "Waiting for database to be ready..."
  sleep 10
  
  if docker-compose -f "$COMPOSE_FILE" $COMPOSE_OVERRIDE exec -T app npx prisma migrate deploy; then
    print_success "Database migrations completed successfully"
  else
    print_warning "Database migrations failed or not needed"
  fi
}

# -----------------------------------------------------------------------------
# Health Check and Status
# -----------------------------------------------------------------------------

# Check service health
check_health() {
  print_info "Checking service health..."

  cd "$PROJECT_ROOT"

  local max_attempts=30
  local attempt=1

  while [[ $attempt -le $max_attempts ]]; do
    if docker-compose -f "$COMPOSE_FILE" $COMPOSE_OVERRIDE ps | grep -q "Up (healthy)"; then
      print_success "Services are healthy"
      return 0
    fi

    print_info "Waiting for services to be healthy... (attempt $attempt/$max_attempts)"
    sleep 10
    ((attempt++))
  done

  print_warning "Services may not be fully healthy yet"
  return 1
}

# Display deployment status
show_status() {
  print_info "Deployment Status:"

  cd "$PROJECT_ROOT"

  echo ""
  docker-compose -f "$COMPOSE_FILE" $COMPOSE_OVERRIDE ps

  echo ""
  print_info "Application URLs:"
  case "$ENVIRONMENT" in
    development)
      echo "  Application: http://localhost:3010"
      echo "  pgAdmin:     http://localhost:8082 (with --profile tools)"
      ;;
    staging)
      echo "  Application: https://staging.yourdomain.com"
      ;;
    production)
      echo "  Application: https://yourdomain.com"
      ;;
  esac

  echo ""
  print_info "Useful commands:"
  echo "  View logs:    docker-compose -f $COMPOSE_FILE $COMPOSE_OVERRIDE logs -f"
  echo "  Stop:         docker-compose -f $COMPOSE_FILE $COMPOSE_OVERRIDE down"
  echo "  Restart:      docker-compose -f $COMPOSE_FILE $COMPOSE_OVERRIDE restart"
}

# -----------------------------------------------------------------------------
# Argument Parsing
# -----------------------------------------------------------------------------

# Parse command line arguments
parse_arguments() {
  while [[ $# -gt 0 ]]; do
    case $1 in
      --no-build)
        BUILD_IMAGES="false"
        shift
        ;;
      --no-migrate)
        RUN_MIGRATIONS="false"
        shift
        ;;
      --ssl)
        GENERATE_SSL="true"
        shift
        ;;
      --no-ssl)
        GENERATE_SSL="false"
        shift
        ;;
      --verbose)
        VERBOSE="true"
        shift
        ;;
      --help|-h)
        usage
        ;;
      -*)
        print_error "Unknown option: $1"
        usage
        ;;
      *)
        if [[ -z "${ENVIRONMENT_SET:-}" ]]; then
          ENVIRONMENT="$1"
          ENVIRONMENT_SET="true"
        else
          print_error "Multiple environments specified"
          usage
        fi
        shift
        ;;
    esac
  done
}

# -----------------------------------------------------------------------------
# Main Deployment Function
# -----------------------------------------------------------------------------

# Main deployment orchestration
main() {
  print_info "🚀 CRM Platform Deployment"
  print_info "=========================="

  # Parse arguments (skip first argument which is environment)
  if [[ $# -gt 1 ]]; then
    shift  # Remove first argument (environment)
    parse_arguments "$@"
  fi

  # Validate and setup
  check_prerequisites
  validate_environment
  setup_environment_files

  # SSL certificates
  generate_ssl_certificates

  # Docker operations
  pull_images
  build_images
  start_services

  # Database setup
  run_migrations

  # Health check and status
  check_health
  show_status

  print_success "🎉 Deployment completed successfully!"
}

# -----------------------------------------------------------------------------
# Script Entry Point
# -----------------------------------------------------------------------------

# Run main function with all arguments
main "$@"
