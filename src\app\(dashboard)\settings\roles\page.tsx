'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { PermissionProvider } from '@/components/providers/permission-provider';
import { PermissionGate } from '@/components/ui/permission-gate';
import { PermissionAction, PermissionResource } from '@/types';
import { useTenant } from '@/hooks/use-tenant';
import { PageLayout } from '@/components/layout/page-layout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { useToast } from '@/hooks/use-toast';
import {
  PlusIcon,
  MagnifyingGlassIcon,
  PencilIcon,
  TrashIcon,
  UserGroupIcon,
  ShieldCheckIcon,
  ExclamationTriangleIcon,
  KeyIcon
} from '@heroicons/react/24/outline';

interface Role {
  id: string;
  name: string;
  description: string;
  isSystemRole: boolean;
  parentRoleId?: string;
  permissions: Permission[];
  childRoles: Role[];
  userCount?: number;
}

interface Permission {
  id: string;
  name: string;
  resource: string;
  action: string;
  category: string;
  description: string;
}

export default function RolesPage() {
  const { currentTenant } = useTenant();
  const router = useRouter();
  const { toast } = useToast();

  const [roles, setRoles] = useState<Role[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [isDeleting, setIsDeleting] = useState<string | null>(null);

  useEffect(() => {
    if (currentTenant) {
      fetchRoles();
    }
  }, [currentTenant]);

  const fetchRoles = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch('/api/roles?includeUserCount=true&includePermissions=true');
      if (response.ok) {
        const data = await response.json();
        setRoles(data.data.roles || []);
      } else {
        const errorData = await response.json();
        setError(errorData.error || 'Failed to fetch roles');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch roles');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCreateRole = () => {
    router.push('/settings/roles/new');
  };

  const handleEditRole = (roleId: string, isSystemRole: boolean = false) => {
    if (isSystemRole) {
      router.push(`/settings/roles/${roleId}/edit-system`);
    } else {
      router.push(`/settings/roles/${roleId}/edit`);
    }
  };

  const handleViewRole = (roleId: string) => {
    router.push(`/settings/roles/${roleId}`);
  };

  const handleDeleteRole = async (roleId: string, roleName: string) => {
    if (!confirm(`Are you sure you want to delete the role "${roleName}"? This action cannot be undone.`)) {
      return;
    }

    try {
      setIsDeleting(roleId);
      const response = await fetch(`/api/roles?roleId=${roleId}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        toast({
          title: 'Success',
          description: 'Role deleted successfully',
        });
        fetchRoles();
      } else {
        const errorData = await response.json();
        toast({
          title: 'Error',
          description: errorData.error || 'Failed to delete role',
          variant: 'destructive',
        });
      }
    } catch (err) {
      toast({
        title: 'Error',
        description: err instanceof Error ? err.message : 'Failed to delete role',
        variant: 'destructive',
      });
    } finally {
      setIsDeleting(null);
    }
  };

  const filteredRoles = roles.filter(role =>
    role.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    role.description.toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (!currentTenant) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <ExclamationTriangleIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-500">No tenant context available</p>
        </div>
      </div>
    );
  }

  const actions = (
    <PermissionGate
      resource={PermissionResource.ROLES}
      action={PermissionAction.CREATE}
    >
      <Button onClick={handleCreateRole} className="flex items-center gap-2">
        <PlusIcon className="w-4 h-4" />
        Create Role
      </Button>
    </PermissionGate>
  );

  return (
    <PermissionProvider>
      <PageLayout
        title="Role Management"
        description={`Manage roles and permissions for ${currentTenant.name}`}
        actions={actions}
      >
        <PermissionGate
          resource={PermissionResource.ROLES}
          action={PermissionAction.READ}
          fallback={<AccessDeniedMessage />}
        >
          <div className="space-y-6">
            {/* Search and Filters */}
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center gap-4">
                  <div className="relative flex-1">
                    <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                    <input
                      type="text"
                      placeholder="Search roles..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Error Display */}
            {error && (
              <Card className="border-red-200 bg-red-50">
                <CardContent className="p-4">
                  <div className="flex items-center gap-2 text-red-800">
                    <ExclamationTriangleIcon className="w-5 h-5" />
                    <p>{error}</p>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setError(null)}
                    className="mt-2"
                  >
                    Dismiss
                  </Button>
                </CardContent>
              </Card>
            )}

            {/* Roles List */}
            {isLoading ? (
              <div className="flex items-center justify-center py-12">
                <LoadingSpinner />
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {filteredRoles.map((role) => (
                  <RoleCard
                    key={role.id}
                    role={role}
                    onView={() => handleViewRole(role.id)}
                    onEdit={() => handleEditRole(role.id, role.isSystemRole)}
                    onDelete={() => handleDeleteRole(role.id, role.name)}
                    isDeleting={isDeleting === role.id}
                  />
                ))}
              </div>
            )}

            {!isLoading && filteredRoles.length === 0 && (
              <Card>
                <CardContent className="p-12 text-center">
                  <KeyIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No roles found</h3>
                  <p className="text-gray-500 mb-4">
                    {searchTerm ? 'No roles match your search criteria.' : 'Get started by creating your first role.'}
                  </p>
                  {!searchTerm && (
                    <PermissionGate
                      resource={PermissionResource.ROLES}
                      action={PermissionAction.CREATE}
                    >
                      <Button onClick={handleCreateRole}>
                        <PlusIcon className="w-4 h-4 mr-2" />
                        Create Role
                      </Button>
                    </PermissionGate>
                  )}
                </CardContent>
              </Card>
            )}
          </div>
        </PermissionGate>
      </PageLayout>
    </PermissionProvider>
  );
}

interface RoleCardProps {
  role: Role;
  onView: () => void;
  onEdit: () => void;
  onDelete: () => void;
  isDeleting: boolean;
}

function RoleCard({ role, onView, onEdit, onDelete, isDeleting }: RoleCardProps) {
  return (
    <Card className="hover:shadow-md transition-shadow cursor-pointer" onClick={onView}>
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-center gap-2">
            <div className={`p-2 rounded-lg ${role.isSystemRole ? 'bg-blue-100' : 'bg-gray-100'}`}>
              {role.isSystemRole ? (
                <ShieldCheckIcon className="w-5 h-5 text-blue-600" />
              ) : (
                <KeyIcon className="w-5 h-5 text-gray-600" />
              )}
            </div>
            <div>
              <CardTitle className="text-lg">{role.name}</CardTitle>
              {role.isSystemRole && (
                <span className="inline-block bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full mt-1">
                  System Role
                </span>
              )}
            </div>
          </div>
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        <p className="text-gray-600 text-sm mb-4 line-clamp-2">
          {role.description || 'No description provided'}
        </p>
        
        <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
          <div className="flex items-center gap-1">
            <UserGroupIcon className="w-4 h-4" />
            <span>{role.userCount || 0} users</span>
          </div>
          <div className="flex items-center gap-1">
            <KeyIcon className="w-4 h-4" />
            <span>{role.permissions.length} permissions</span>
          </div>
        </div>

        <div className="flex items-center gap-2" onClick={(e) => e.stopPropagation()}>
          {/* Regular role editing */}
          {!role.isSystemRole && (
            <PermissionGate
              resource={PermissionResource.ROLES}
              action={PermissionAction.UPDATE}
            >
              <Button
                variant="outline"
                size="sm"
                onClick={onEdit}
                className="flex-1"
              >
                <PencilIcon className="w-4 h-4 mr-1" />
                Edit
              </Button>
            </PermissionGate>
          )}

          {/* System role editing */}
          {role.isSystemRole && (
            <PermissionGate
              resource={PermissionResource.ROLES}
              action={PermissionAction.MANAGE_SYSTEM_ROLES}
            >
              <Button
                variant="outline"
                size="sm"
                onClick={onEdit}
                className="flex-1 border-orange-300 text-orange-700 hover:bg-orange-50"
              >
                <PencilIcon className="w-4 h-4 mr-1" />
                Edit System
              </Button>
            </PermissionGate>
          )}

          <PermissionGate
            resource={PermissionResource.ROLES}
            action={PermissionAction.DELETE}
          >
            <Button
              variant="outline"
              size="sm"
              onClick={onDelete}
              disabled={role.isSystemRole || isDeleting}
              className="text-red-600 hover:text-red-700 hover:bg-red-50"
            >
              {isDeleting ? (
                <LoadingSpinner className="w-4 h-4" />
              ) : (
                <TrashIcon className="w-4 h-4" />
              )}
            </Button>
          </PermissionGate>
        </div>
      </CardContent>
    </Card>
  );
}

function AccessDeniedMessage() {
  return (
    <Card>
      <CardContent className="p-12 text-center">
        <ExclamationTriangleIcon className="w-12 h-12 text-red-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">Access Denied</h3>
        <p className="text-gray-500">
          You don't have permission to view roles. Contact your administrator for access.
        </p>
      </CardContent>
    </Card>
  );
}
