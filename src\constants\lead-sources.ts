/**
 * Lead Sources Constants
 * 
 * This file defines common lead sources that can be used throughout the application.
 * These sources are also available via the /api/leads/sources endpoint.
 */

export interface LeadSource {
  id: string;
  name: string;
  description: string;
  category: LeadSourceCategory;
  icon: string;
  isCustom?: boolean;
}

export type LeadSourceCategory = 
  | 'digital'
  | 'outbound'
  | 'paid_advertising'
  | 'events'
  | 'partnerships'
  | 'traditional'
  | 'word_of_mouth'
  | 'existing_relationships'
  | 'custom'
  | 'other';

export interface LeadSourceCategoryInfo {
  id: LeadSourceCategory;
  name: string;
  description: string;
}

// Common lead sources available across all tenants
export const DEFAULT_LEAD_SOURCES: LeadSource[] = [
  {
    id: 'website',
    name: 'Website',
    description: 'Leads from website forms and landing pages',
    category: 'digital',
    icon: 'globe'
  },
  {
    id: 'referral',
    name: 'Referral',
    description: 'Leads from customer or partner referrals',
    category: 'word_of_mouth',
    icon: 'users'
  },
  {
    id: 'cold_call',
    name: 'Cold Call',
    description: 'Leads from outbound cold calling',
    category: 'outbound',
    icon: 'phone'
  },
  {
    id: 'cold_email',
    name: 'Cold Email',
    description: 'Leads from outbound email campaigns',
    category: 'outbound',
    icon: 'mail'
  },
  {
    id: 'social_media',
    name: 'Social Media',
    description: 'Leads from social media platforms',
    category: 'digital',
    icon: 'share-2'
  },
  {
    id: 'linkedin',
    name: 'LinkedIn',
    description: 'Leads from LinkedIn outreach and connections',
    category: 'digital',
    icon: 'linkedin'
  },
  {
    id: 'google_ads',
    name: 'Google Ads',
    description: 'Leads from Google advertising campaigns',
    category: 'paid_advertising',
    icon: 'search'
  },
  {
    id: 'facebook_ads',
    name: 'Facebook Ads',
    description: 'Leads from Facebook advertising campaigns',
    category: 'paid_advertising',
    icon: 'facebook'
  },
  {
    id: 'trade_show',
    name: 'Trade Show',
    description: 'Leads from trade shows and events',
    category: 'events',
    icon: 'calendar'
  },
  {
    id: 'webinar',
    name: 'Webinar',
    description: 'Leads from webinars and online events',
    category: 'events',
    icon: 'video'
  },
  {
    id: 'content_marketing',
    name: 'Content Marketing',
    description: 'Leads from blog posts, whitepapers, and content',
    category: 'digital',
    icon: 'file-text'
  },
  {
    id: 'email_marketing',
    name: 'Email Marketing',
    description: 'Leads from email marketing campaigns',
    category: 'digital',
    icon: 'mail'
  },
  {
    id: 'partner',
    name: 'Partner',
    description: 'Leads from business partners',
    category: 'partnerships',
    icon: 'handshake'
  },
  {
    id: 'direct_mail',
    name: 'Direct Mail',
    description: 'Leads from direct mail campaigns',
    category: 'traditional',
    icon: 'mail'
  },
  {
    id: 'word_of_mouth',
    name: 'Word of Mouth',
    description: 'Leads from word-of-mouth recommendations',
    category: 'word_of_mouth',
    icon: 'message-circle'
  },
  {
    id: 'existing_customer',
    name: 'Existing Customer',
    description: 'Leads from existing customers (upsell/cross-sell)',
    category: 'existing_relationships',
    icon: 'user-check'
  },
  {
    id: 'other',
    name: 'Other',
    description: 'Other lead sources not listed above',
    category: 'other',
    icon: 'more-horizontal'
  }
];

// Lead source categories with descriptions
export const LEAD_SOURCE_CATEGORIES: LeadSourceCategoryInfo[] = [
  {
    id: 'digital',
    name: 'Digital Marketing',
    description: 'Online and digital channels'
  },
  {
    id: 'outbound',
    name: 'Outbound Sales',
    description: 'Proactive outreach efforts'
  },
  {
    id: 'paid_advertising',
    name: 'Paid Advertising',
    description: 'Paid marketing campaigns'
  },
  {
    id: 'events',
    name: 'Events & Webinars',
    description: 'In-person and virtual events'
  },
  {
    id: 'partnerships',
    name: 'Partnerships',
    description: 'Partner and referral channels'
  },
  {
    id: 'traditional',
    name: 'Traditional Marketing',
    description: 'Traditional marketing methods'
  },
  {
    id: 'word_of_mouth',
    name: 'Word of Mouth',
    description: 'Organic referrals and recommendations'
  },
  {
    id: 'existing_relationships',
    name: 'Existing Relationships',
    description: 'Current customers and contacts'
  },
  {
    id: 'custom',
    name: 'Custom Sources',
    description: 'Tenant-specific lead sources'
  },
  {
    id: 'other',
    name: 'Other',
    description: 'Miscellaneous sources'
  }
];

// Helper functions
export const getLeadSourceById = (id: string): LeadSource | undefined => {
  return DEFAULT_LEAD_SOURCES.find(source => source.id === id);
};

export const getLeadSourcesByCategory = (category: LeadSourceCategory): LeadSource[] => {
  return DEFAULT_LEAD_SOURCES.filter(source => source.category === category);
};

export const getCategoryInfo = (category: LeadSourceCategory): LeadSourceCategoryInfo | undefined => {
  return LEAD_SOURCE_CATEGORIES.find(cat => cat.id === category);
};

// Most commonly used lead sources (for quick access in forms)
export const POPULAR_LEAD_SOURCES = [
  'website',
  'referral',
  'cold_call',
  'social_media',
  'linkedin',
  'google_ads'
];

export const getPopularLeadSources = (): LeadSource[] => {
  return POPULAR_LEAD_SOURCES.map(id => getLeadSourceById(id)).filter(Boolean) as LeadSource[];
};
