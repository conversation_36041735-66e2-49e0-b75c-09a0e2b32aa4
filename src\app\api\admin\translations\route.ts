import { NextResponse } from 'next/server';
import { withTenantAdmin } from '@/lib/permission-middleware';
import { TranslationService } from '@/services/translation-service';
import { AuthenticatedRequest } from '@/lib/permission-middleware';

async function getHandler(req: AuthenticatedRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const query = searchParams.get('query') || '';
    const category = searchParams.get('category') || undefined;
    const language = searchParams.get('language') as 'en' | 'ar' | undefined;

    let translations;
    if (query || category || language) {
      translations = await TranslationService.searchTranslations(query, category, language);
    } else {
      translations = await TranslationService.loadTranslations();
    }

    return NextResponse.json({ translations });
  } catch (error) {
    console.error('Failed to load translations:', error);
    return NextResponse.json(
      { error: 'Failed to load translations' },
      { status: 500 }
    );
  }
}

async function postHandler(req: AuthenticatedRequest) {
  try {
    const body = await req.json();
    const { key, en, ar, category, description } = body;

    // Validate required fields
    if (!key || !en || !ar) {
      return NextResponse.json(
        { error: 'Key, English text, and Arabic text are required' },
        { status: 400 }
      );
    }

    // Validate key format
    const keyValidation = TranslationService.validateKey(key);
    if (!keyValidation.valid) {
      return NextResponse.json(
        { error: keyValidation.error },
        { status: 400 }
      );
    }

    await TranslationService.addTranslation({
      key,
      en,
      ar,
      category: category || 'custom',
      description
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Failed to add translation:', error);
    
    if (error instanceof Error && error.message.includes('already exists')) {
      return NextResponse.json(
        { error: error.message },
        { status: 409 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to add translation' },
      { status: 500 }
    );
  }
}

async function putHandler(req: AuthenticatedRequest) {
  try {
    const body = await req.json();
    const { key, en, ar, category, description } = body;

    if (!key) {
      return NextResponse.json(
        { error: 'Translation key is required' },
        { status: 400 }
      );
    }

    const updates: any = {};
    if (en !== undefined) updates.en = en;
    if (ar !== undefined) updates.ar = ar;
    if (category !== undefined) updates.category = category;
    if (description !== undefined) updates.description = description;

    await TranslationService.updateTranslation(key, updates);

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Failed to update translation:', error);
    
    if (error instanceof Error && error.message.includes('not found')) {
      return NextResponse.json(
        { error: error.message },
        { status: 404 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to update translation' },
      { status: 500 }
    );
  }
}

async function deleteHandler(req: AuthenticatedRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const key = searchParams.get('key');

    if (!key) {
      return NextResponse.json(
        { error: 'Translation key is required' },
        { status: 400 }
      );
    }

    await TranslationService.deleteTranslation(key);

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Failed to delete translation:', error);
    
    if (error instanceof Error && error.message.includes('not found')) {
      return NextResponse.json(
        { error: error.message },
        { status: 404 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to delete translation' },
      { status: 500 }
    );
  }
}

export const GET = withTenantAdmin(getHandler);
export const POST = withTenantAdmin(postHandler);
export const PUT = withTenantAdmin(putHandler);
export const DELETE = withTenantAdmin(deleteHandler);
