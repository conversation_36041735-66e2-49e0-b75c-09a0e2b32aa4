'use client';

import React from 'react';
import { TeamMember } from '@/types';
import { ContactAvatar } from '@/components/ui/contact-avatar';
import { PermissionGate } from '@/components/ui/permission-gate';
import { PermissionAction, PermissionResource } from '@/types';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import {
  UserPlusIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
  TrashIcon,
  UserIcon,
  EnvelopeIcon,
  CalendarIcon
} from '@heroicons/react/24/outline';

interface TeamMembersProps {
  members: TeamMember[];
  isLoading: boolean;
  searchTerm: string;
  roleFilter: string;
  onSearchChange: (term: string) => void;
  onRoleFilterChange: (role: string) => void;
  onAddMember: () => void;
  onRemoveMember: (userId: string) => void;
}

export function TeamMembers({
  members,
  isLoading,
  searchTerm,
  roleFilter,
  onSearchChange,
  onRoleFilterChange,
  onAddMember,
  onRemoveMember
}: TeamMembersProps) {
  const uniqueRoles = Array.from(new Set(members.map(member => member.role)));

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header and Controls */}
      <div className="flex flex-col sm:flex-row gap-4 justify-between">
        <div className="flex-1">
          <div className="relative">
            <MagnifyingGlassIcon className="w-5 h-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              placeholder="Search members..."
              value={searchTerm}
              onChange={(e) => onSearchChange(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
        </div>

        <div className="flex space-x-3">
          <select
            value={roleFilter}
            onChange={(e) => onRoleFilterChange(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="all">All Roles</option>
            {uniqueRoles.map(role => (
              <option key={role} value={role}>{role}</option>
            ))}
          </select>

          <PermissionGate resource={PermissionResource.USERS} action={PermissionAction.MANAGE}>
            <button
              onClick={onAddMember}
              className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors flex items-center space-x-2"
            >
              <UserPlusIcon className="w-4 h-4" />
              <span>Add Member</span>
            </button>
          </PermissionGate>
        </div>
      </div>

      {/* Members List */}
      {members.length === 0 ? (
        <div className="text-center py-12">
          <UserIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No members found</h3>
          <p className="text-gray-500">
            {searchTerm || roleFilter !== 'all' 
              ? 'Try adjusting your search or filter criteria.'
              : 'Add members to get started with this team.'
            }
          </p>
        </div>
      ) : (
        <div className="bg-white shadow-sm rounded-lg border border-gray-200">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Member
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Role
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Department
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Joined
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Last Login
                  </th>
                  <th className="relative px-6 py-3">
                    <span className="sr-only">Actions</span>
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {members.map((member) => (
                  <MemberRow
                    key={member.id}
                    member={member}
                    onRemove={() => onRemoveMember(member.id)}
                  />
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}
    </div>
  );
}

interface MemberRowProps {
  member: TeamMember;
  onRemove: () => void;
}

function MemberRow({ member, onRemove }: MemberRowProps) {
  const getStatusBadge = (status: string) => {
    const statusConfig = {
      active: { bg: 'bg-green-100', text: 'text-green-800', label: 'Active' },
      pending: { bg: 'bg-yellow-100', text: 'text-yellow-800', label: 'Pending' },
      inactive: { bg: 'bg-gray-100', text: 'text-gray-800', label: 'Inactive' }
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.active;

    return (
      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${config.bg} ${config.text}`}>
        {config.label}
      </span>
    );
  };

  return (
    <tr className="hover:bg-gray-50">
      {/* Member Info */}
      <td className="px-6 py-4 whitespace-nowrap">
        <div className="flex items-center">
          <div className="flex-shrink-0">
            <ContactAvatar
              firstName={member.firstName}
              lastName={member.lastName}
              avatarUrl={member.avatarUrl}
              size="lg"
            />
          </div>
          <div className="ml-4">
            <div className="text-sm font-medium text-gray-900">
              {member.firstName} {member.lastName}
            </div>
            <div className="text-sm text-gray-500 flex items-center">
              <EnvelopeIcon className="w-4 h-4 mr-1" />
              {member.email}
            </div>
            {member.jobTitle && (
              <div className="text-xs text-gray-400 mt-1">{member.jobTitle}</div>
            )}
          </div>
        </div>
      </td>

      {/* Role */}
      <td className="px-6 py-4 whitespace-nowrap">
        <div className="flex flex-col">
          <span className="text-sm font-medium text-gray-900">{member.role}</span>
          {member.roles && member.roles.length > 0 && (
            <div className="flex flex-wrap gap-1 mt-1">
              {member.roles.slice(0, 2).map((role) => (
                <span
                  key={role.id}
                  className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                >
                  {role.name}
                </span>
              ))}
              {member.roles.length > 2 && (
                <span className="text-xs text-gray-500">
                  +{member.roles.length - 2} more
                </span>
              )}
            </div>
          )}
        </div>
      </td>

      {/* Department */}
      <td className="px-6 py-4 whitespace-nowrap">
        <span className="text-sm text-gray-900">
          {member.department || 'Not specified'}
        </span>
      </td>

      {/* Joined Date */}
      <td className="px-6 py-4 whitespace-nowrap">
        <div className="flex items-center text-sm text-gray-500">
          <CalendarIcon className="w-4 h-4 mr-1" />
          {new Date(member.joinedAt).toLocaleDateString()}
        </div>
      </td>

      {/* Last Login */}
      <td className="px-6 py-4 whitespace-nowrap">
        <div className="text-sm text-gray-500">
          {member.lastLogin 
            ? new Date(member.lastLogin).toLocaleDateString()
            : 'Never'
          }
        </div>
        <div className="mt-1">
          {getStatusBadge(member.status)}
        </div>
      </td>

      {/* Actions */}
      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
        <PermissionGate resource={PermissionResource.USERS} action={PermissionAction.MANAGE}>
          <button
            onClick={onRemove}
            className="text-red-600 hover:text-red-900 p-1 rounded-full hover:bg-red-50"
            title="Remove from team"
          >
            <TrashIcon className="w-4 h-4" />
          </button>
        </PermissionGate>
      </td>
    </tr>
  );
}
