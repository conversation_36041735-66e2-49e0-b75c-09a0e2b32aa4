'use client';

import { useState, useEffect, useCallback, useMemo } from 'react';
import { useApiLoading } from './use-api-loading';

interface LeadFilters {
  search?: string;
  status?: string;
  source?: string;
  priority?: string;
  page?: number;
  limit?: number;
}

interface UseOptimizedLeadsOptions {
  initialFilters?: LeadFilters;
  enableCache?: boolean;
  cacheTime?: number;
  staleTime?: number;
}

interface CachedLeadData {
  data: any;
  timestamp: number;
  filters: LeadFilters;
}

// In-memory cache for lead data
const leadCache = new Map<string, CachedLeadData>();

export function useOptimizedLeads(options: UseOptimizedLeadsOptions = {}) {
  const {
    initialFilters = {},
    enableCache = true,
    cacheTime = 5 * 60 * 1000, // 5 minutes
    staleTime = 30 * 1000, // 30 seconds
  } = options;

  const [leads, setLeads] = useState<any[]>([]);
  const [totalPages, setTotalPages] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState<LeadFilters>(initialFilters);

  const { withApiLoading } = useApiLoading();

  // Generate cache key from filters
  const cacheKey = useMemo(() => {
    return JSON.stringify(filters);
  }, [filters]);

  // Check if cached data is still valid
  const getCachedData = useCallback((key: string): CachedLeadData | null => {
    if (!enableCache) return null;
    
    const cached = leadCache.get(key);
    if (!cached) return null;
    
    const now = Date.now();
    const isStale = now - cached.timestamp > staleTime;
    const isExpired = now - cached.timestamp > cacheTime;
    
    if (isExpired) {
      leadCache.delete(key);
      return null;
    }
    
    return isStale ? null : cached;
  }, [enableCache, cacheTime, staleTime]);

  // Cache data
  const setCachedData = useCallback((key: string, data: any, filters: LeadFilters) => {
    if (!enableCache) return;
    
    leadCache.set(key, {
      data,
      timestamp: Date.now(),
      filters,
    });
  }, [enableCache]);

  // Load leads with caching and optimization
  const loadLeads = useCallback(async (newFilters?: LeadFilters, force = false) => {
    const currentFilters = newFilters || filters;
    const currentCacheKey = JSON.stringify(currentFilters);
    
    // Check cache first
    if (!force) {
      const cached = getCachedData(currentCacheKey);
      if (cached) {
        setLeads(cached.data.leads);
        setTotalPages(cached.data.totalPages);
        setError(null);
        return;
      }
    }

    try {
      await withApiLoading(async () => {
        setLoading(true);
        setError(null);

        const params = new URLSearchParams({
          page: (currentFilters.page || 1).toString(),
          limit: (currentFilters.limit || 10).toString(),
          ...(currentFilters.search && { search: currentFilters.search }),
          ...(currentFilters.status && currentFilters.status !== 'all' && { status: currentFilters.status }),
          ...(currentFilters.source && { source: currentFilters.source }),
          ...(currentFilters.priority && currentFilters.priority !== 'all' && { priority: currentFilters.priority })
        });

        const response = await fetch(`/api/leads?${params}`, {
          headers: {
            'Cache-Control': 'max-age=30', // Browser cache for 30 seconds
          },
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();

        if (data.success) {
          setLeads(data.data.leads);
          setTotalPages(data.data.totalPages);
          
          // Cache the successful response
          setCachedData(currentCacheKey, data.data, currentFilters);
        } else {
          throw new Error(data.message || 'Failed to load leads');
        }
      }, { showGlobalLoading: true });
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load leads';
      setError(errorMessage);
      console.error('Error loading leads:', err);
    } finally {
      setLoading(false);
    }
  }, [filters, getCachedData, setCachedData, withApiLoading]);

  // Update filters and reload
  const updateFilters = useCallback((newFilters: Partial<LeadFilters>) => {
    const updatedFilters = { ...filters, ...newFilters };
    setFilters(updatedFilters);
    loadLeads(updatedFilters);
  }, [filters, loadLeads]);

  // Refresh data (bypass cache)
  const refresh = useCallback(() => {
    loadLeads(filters, true);
  }, [filters, loadLeads]);

  // Clear cache
  const clearCache = useCallback(() => {
    leadCache.clear();
  }, []);

  // Load initial data
  useEffect(() => {
    loadLeads();
  }, []); // Only run on mount

  return {
    leads,
    totalPages,
    loading,
    error,
    filters,
    updateFilters,
    refresh,
    clearCache,
    // Expose cache stats for debugging
    cacheSize: leadCache.size,
    isCached: !!getCachedData(cacheKey),
  };
}
