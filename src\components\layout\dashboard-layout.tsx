'use client';

import { AppSidebar } from "@/components/app-sidebar"
import {
  SidebarInset,
  SidebarProvider,
} from "@/components/ui/sidebar"
import { SimplePageWrapper } from "@/components/layout/simple-page-wrapper"
import { useLocale } from "@/components/providers/locale-provider"

interface DashboardLayoutProps {
  children: React.ReactNode;
}

export function DashboardLayout({ children }: DashboardLayoutProps) {
  // Get locale context for RTL support
  const { sidebarSide, isRTL } = useLocale()

  return (
    <SidebarProvider
      style={
        {
          "--sidebar-width": "calc(var(--spacing) * 72)",
          "--header-height": "calc(var(--spacing) * 12)",
        } as React.CSSProperties
      }
    >
      <AppSidebar variant="inset" side={sidebarSide} />
      <SidebarInset className={isRTL ? "rtl:pr-[--sidebar-width]" : ""}>
        <SimplePageWrapper>
          {children}
        </SimplePageWrapper>
      </SidebarInset>
    </SidebarProvider>
  );
}
