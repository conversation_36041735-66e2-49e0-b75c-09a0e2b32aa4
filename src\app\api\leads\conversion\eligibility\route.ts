import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { leadManagementService, leadConversionEligibilitySchema } from '@/services/lead-management';
import { hasPermission } from '@/lib/permissions';
import { PermissionResource, PermissionAction } from '@/types';

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const tenantId = session.user.tenants?.[0]?.id;
    if (!tenantId) {
      return NextResponse.json({ error: 'Tenant not found' }, { status: 400 });
    }

    // Check permissions
    const hasLeadPermission = await hasPermission(
      PermissionResource.LEADS,
      PermissionAction.UPDATE,
      tenantId
    );

    if (!hasLeadPermission) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    const body = await request.json();
    const validatedData = leadConversionEligibilitySchema.parse(body);

    const eligibilityResults = await leadManagementService.checkConversionEligibility(
      validatedData.leadIds,
      tenantId
    );

    return NextResponse.json({
      success: true,
      data: eligibilityResults
    });

  } catch (error) {
    console.error('Error checking lead conversion eligibility:', error);
    
    if (error instanceof Error && error.message.includes('validation')) {
      return NextResponse.json(
        { error: 'Invalid request data', details: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
