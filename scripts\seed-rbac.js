#!/usr/bin/env node

/**
 * <PERSON><PERSON> (Role-Based Access Control) Seeding Script
 * Creates predefined roles with appropriate permission assignments
 */

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

// Define role hierarchy and permissions
const PREDEFINED_ROLES = {
  SUPER_ADMIN: {
    name: 'Super Admin',
    description: 'Full platform access including tenant management',
    isSystemRole: true,
    permissions: 'ALL' // Special case - gets all permissions
  },
  
  TENANT_ADMIN: {
    name: 'Tenant Admin',
    description: 'Full access within their tenant',
    isSystemRole: true,
    permissions: [
      // Full CRM access
      'contacts.*', 'companies.*', 'leads.*', 'opportunities.*',
      'proposals.*', 'projects.*', 'handovers.*', 'activities.*', 'documents.*', 'calls.*',

      // Communication access
      'communication.*',

      // User management
      'users.*', 'roles.create', 'roles.read', 'roles.update', 'roles.assign',

      // Settings and configuration
      'settings.*', 'billing.view_invoices', 'billing.view_usage',

      // AI and reporting
      'ai_features.*', 'reporting.*', 'analytics.*',

      // Audit access
      'audit.read', 'audit.list'
    ]
  },
  
  SALES_MANAGER: {
    name: 'Sales Manager',
    description: 'Lead/opportunity management with team oversight',
    isSystemRole: true,
    permissions: [
      // Full CRM access
      'contacts.*', 'companies.*', 'leads.*', 'opportunities.*',
      'activities.*', 'documents.*', 'calls.*',

      // Communication access
      'communication.*',

      // Limited user management
      'users.read', 'users.list', 'users.view_all',

      // Proposals and projects
      'proposals.*', 'projects.read', 'projects.list', 'projects.view',

      // AI features
      'ai_features.access', 'ai_features.lead_scoring', 'ai_features.insights',

      // Reporting
      'reporting.*', 'analytics.revenue_dashboard', 'analytics.team_performance',

      // Special permissions
      'leads.bulk_assign', 'opportunities.view_pipeline', 'opportunities.manage_stages'
    ]
  },
  
  SALES_REP: {
    name: 'Sales Rep',
    description: 'Basic CRM functions for individual contributors',
    isSystemRole: true,
    permissions: [
      // Basic CRM access
      'contacts.create', 'contacts.read', 'contacts.update', 'contacts.list', 'contacts.view',
      'companies.create', 'companies.read', 'companies.update', 'companies.list', 'companies.view',
      'leads.create', 'leads.read', 'leads.update', 'leads.list', 'leads.view', 'leads.convert',
      'opportunities.create', 'opportunities.read', 'opportunities.update', 'opportunities.list', 'opportunities.view',

      // Communication access
      'communication.read', 'communication.create', 'communication.update',

      // Activities, documents, and calls
      'activities.*', 'documents.create', 'documents.read', 'documents.update', 'documents.list',
      'calls.create', 'calls.read', 'calls.update',

      // Limited proposals
      'proposals.create', 'proposals.read', 'proposals.update', 'proposals.list', 'proposals.view',

      // Basic AI features
      'ai_features.access', 'ai_features.lead_scoring',

      // Basic reporting
      'reporting.read', 'reporting.list', 'analytics.view_dashboard'
    ]
  },
  
  VIEWER: {
    name: 'Viewer',
    description: 'Read-only access to assigned records',
    isSystemRole: true,
    permissions: [
      // Read-only access
      'contacts.read', 'contacts.list', 'contacts.view',
      'companies.read', 'companies.list', 'companies.view',
      'leads.read', 'leads.list', 'leads.view',
      'opportunities.read', 'opportunities.list', 'opportunities.view',
      'proposals.read', 'proposals.list', 'proposals.view',
      'projects.read', 'projects.list', 'projects.view',
      'activities.read', 'activities.list', 'activities.view',
      'documents.read', 'documents.list', 'documents.view',
      'calls.read',

      // Communication read access
      'communication.read',

      // Basic reporting
      'reporting.read', 'reporting.list'
    ]
  }
};

async function seedRoles() {
  console.log('🎭 Starting RBAC role seeding...\n');

  try {
    // Get all tenants
    const tenants = await prisma.tenant.findMany();
    
    if (tenants.length === 0) {
      console.log('❌ No tenants found. Please run tenant seeding first.');
      return;
    }

    for (const tenant of tenants) {
      console.log(`🏢 Creating roles for tenant: ${tenant.name}`);
      
      // Get all permissions for this tenant
      const allPermissions = await prisma.permission.findMany({
        where: { tenantId: tenant.id }
      });
      
      if (allPermissions.length === 0) {
        console.log(`⚠️  No permissions found for tenant ${tenant.name}. Run permission seeding first.`);
        continue;
      }

      for (const [roleKey, roleData] of Object.entries(PREDEFINED_ROLES)) {
        console.log(`  Creating role: ${roleData.name}`);
        
        // Create the role
        const role = await prisma.role.upsert({
          where: {
            tenantId_name: {
              tenantId: tenant.id,
              name: roleData.name
            }
          },
          update: {
            description: roleData.description,
            isSystemRole: roleData.isSystemRole
          },
          create: {
            tenantId: tenant.id,
            name: roleData.name,
            description: roleData.description,
            isSystemRole: roleData.isSystemRole
          }
        });

        // Assign permissions to role
        let permissionsToAssign = [];
        
        if (roleData.permissions === 'ALL') {
          // Super Admin gets all permissions
          permissionsToAssign = allPermissions;
        } else {
          // Filter permissions based on role definition
          for (const permPattern of roleData.permissions) {
            if (permPattern.endsWith('.*')) {
              // Wildcard pattern - get all permissions for this resource
              const resource = permPattern.replace('.*', '');
              const resourcePermissions = allPermissions.filter(p => p.resource === resource);
              permissionsToAssign.push(...resourcePermissions);
            } else {
              // Exact permission name
              const permission = allPermissions.find(p => p.name === permPattern);
              if (permission) {
                permissionsToAssign.push(permission);
              }
            }
          }
        }

        // Remove duplicates
        permissionsToAssign = permissionsToAssign.filter((perm, index, self) => 
          index === self.findIndex(p => p.id === perm.id)
        );

        console.log(`    Assigning ${permissionsToAssign.length} permissions...`);

        // Create role-permission assignments
        for (const permission of permissionsToAssign) {
          try {
            await prisma.rolePermission.upsert({
              where: {
                tenantId_roleId_permissionId: {
                  tenantId: tenant.id,
                  roleId: role.id,
                  permissionId: permission.id
                }
              },
              update: {},
              create: {
                tenantId: tenant.id,
                roleId: role.id,
                permissionId: permission.id
              }
            });
          } catch (error) {
            if (!error.message.includes('Unique constraint')) {
              console.error(`Error assigning permission ${permission.name} to role ${role.name}:`, error.message);
            }
          }
        }

        console.log(`    ✅ Role ${roleData.name} created with ${permissionsToAssign.length} permissions`);
      }

      console.log(`✅ Completed roles for tenant: ${tenant.name}\n`);
    }

    console.log('🎉 RBAC role seeding completed successfully!');
    
    // Summary
    const totalRoles = await prisma.role.count();
    const totalRolePermissions = await prisma.rolePermission.count();
    console.log(`📊 Summary:`);
    console.log(`   - Total roles: ${totalRoles}`);
    console.log(`   - Total role-permission assignments: ${totalRolePermissions}`);

  } catch (error) {
    console.error('❌ RBAC seeding failed:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run seeding
seedRoles().catch(console.error);
