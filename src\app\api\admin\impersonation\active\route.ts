import { NextRequest, NextResponse } from 'next/server';
import { withSuperAdminAuth } from '@/lib/tenant-context';
import { TenantImpersonationService } from '@/services/tenant-impersonation';

/**
 * @swagger
 * /api/admin/impersonation/active:
 *   get:
 *     summary: Get active impersonation session (Super Admin only)
 *     description: Get the currently active impersonation session for the super admin
 *     tags:
 *       - Super Admin - Impersonation
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Active impersonation session retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   nullable: true
 *                   properties:
 *                     id:
 *                       type: string
 *                     superAdminId:
 *                       type: string
 *                     tenantId:
 *                       type: string
 *                     userId:
 *                       type: string
 *                     startedAt:
 *                       type: string
 *                       format: date-time
 *                     reason:
 *                       type: string
 *                     superAdmin:
 *                       type: object
 *                     tenant:
 *                       type: object
 *                     user:
 *                       type: object
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Super Admin access required
 */
export const GET = withSuperAdminAuth(async (request: NextRequest, context: any) => {
  try {
    const superAdminId = context.user.id;

    const impersonationService = new TenantImpersonationService();
    const activeSession = await impersonationService.getActiveImpersonationSession(superAdminId);

    return NextResponse.json({
      success: true,
      data: activeSession
    });

  } catch (error) {
    console.error('Get active impersonation session error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
});

/**
 * @swagger
 * /api/admin/impersonation/active:
 *   delete:
 *     summary: End active impersonation session (Super Admin only)
 *     description: End the currently active impersonation session for the super admin
 *     tags:
 *       - Super Admin - Impersonation
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Active impersonation session ended successfully
 *       404:
 *         description: No active impersonation session found
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Super Admin access required
 */
export const DELETE = withSuperAdminAuth(async (request: NextRequest, context: any) => {
  try {
    const superAdminId = context.user.id;

    const impersonationService = new TenantImpersonationService();
    
    // Get active session first
    const activeSession = await impersonationService.getActiveImpersonationSession(superAdminId);
    
    if (!activeSession) {
      return NextResponse.json(
        { error: 'No active impersonation session found' },
        { status: 404 }
      );
    }

    // End the session
    await impersonationService.endImpersonation(activeSession.id, superAdminId);

    return NextResponse.json({
      success: true,
      message: 'Active impersonation session ended successfully'
    });

  } catch (error) {
    console.error('End active impersonation session error:', error);
    
    if (error instanceof Error && error.message.includes('not found')) {
      return NextResponse.json(
        { error: error.message },
        { status: 404 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
});
