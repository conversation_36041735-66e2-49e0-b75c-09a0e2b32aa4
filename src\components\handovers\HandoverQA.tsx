'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';
import { SearchableSelect } from '@/components/ui/searchable-select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  MessageSquare,
  Plus,
  Send,
  Clock,
  AlertTriangle,
  CheckCircle,
  User,
  Filter,
  AlertCircle,
  ArrowUp,
  ArrowDown
} from 'lucide-react';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';
import CreateThreadDialog, { NewThreadData } from './CreateThreadDialog';

// Types
interface QAMessage {
  id: string;
  content: string;
  messageType: string;
  attachments: string[];
  isRead: boolean;
  readAt?: string;
  authorId: string;
  createdAt: string;
  updatedAt: string;
  author: {
    id: string;
    firstName?: string;
    lastName?: string;
    email: string;
  };
}

interface QAThread {
  id: string;
  title: string;
  category: string;
  priority: string;
  status: string;
  createdById: string;
  assignedToId?: string;
  responseTime?: number;
  isEscalated: boolean;
  escalatedAt?: string;
  resolvedAt?: string;
  createdAt: string;
  updatedAt: string;
  createdBy: {
    id: string;
    firstName?: string;
    lastName?: string;
  };
  assignedTo?: {
    id: string;
    firstName?: string;
    lastName?: string;
  };
  messages: QAMessage[];
  _count: {
    messages: number;
  };
}

interface QAThreadsResponse {
  threads: QAThread[];
}

interface HandoverQAProps {
  handoverId: string;
}

// Status and priority configurations
const statusConfig = {
  open: { label: 'Open', color: 'bg-blue-100 text-blue-800', icon: MessageSquare },
  answered: { label: 'Answered', color: 'bg-green-100 text-green-800', icon: CheckCircle },
  closed: { label: 'Closed', color: 'bg-gray-100 text-gray-800', icon: CheckCircle },
  escalated: { label: 'Escalated', color: 'bg-red-100 text-red-800', icon: AlertTriangle },
};

const priorityConfig = {
  low: { label: 'Low', color: 'bg-gray-100 text-gray-800' },
  medium: { label: 'Medium', color: 'bg-blue-100 text-blue-800' },
  high: { label: 'High', color: 'bg-orange-100 text-orange-800' },
  urgent: { label: 'Urgent', color: 'bg-red-100 text-red-800' },
};

const categoryConfig = {
  general: { label: 'General', color: 'bg-gray-100 text-gray-800' },
  technical: { label: 'Technical', color: 'bg-blue-100 text-blue-800' },
  requirements: { label: 'Requirements', color: 'bg-purple-100 text-purple-800' },
  timeline: { label: 'Timeline', color: 'bg-green-100 text-green-800' },
  budget: { label: 'Budget', color: 'bg-yellow-100 text-yellow-800' },
};

export default function HandoverQA({ handoverId }: HandoverQAProps) {
  const [threads, setThreads] = useState<QAThread[]>([]);
  const [selectedThread, setSelectedThread] = useState<QAThread | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [creating, setCreating] = useState(false);
  const [sending, setSending] = useState(false);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [newMessage, setNewMessage] = useState('');
  const [filters, setFilters] = useState({
    status: 'all',
    priority: 'all',
    category: 'all',
  });



  // Fetch Q&A threads
  const fetchThreads = async () => {
    try {
      setLoading(true);
      setError(null);

      const params = new URLSearchParams();
      Object.entries(filters).forEach(([key, value]) => {
        if (value && value !== 'all') params.append(key, value);
      });

      const response = await fetch(`/api/handovers/${handoverId}/qa?${params}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch Q&A threads');
      }

      const data: QAThreadsResponse = await response.json();
      setThreads(data.threads || []);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchThreads();
  }, [handoverId, filters]);

  // Create new thread
  const createThread = async (threadData: NewThreadData) => {
    try {
      setCreating(true);

      const response = await fetch(`/api/handovers/${handoverId}/qa`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(threadData),
      });

      if (!response.ok) {
        throw new Error('Failed to create Q&A thread');
      }

      const thread = await response.json();
      setThreads(prev => [thread, ...prev]);
      setSelectedThread(thread);

      // If there's an initial message, send it
      if (threadData.initialMessage?.trim()) {
        try {
          const messageResponse = await fetch(`/api/handovers/qa/${thread.id}/messages`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              content: threadData.initialMessage,
              messageType: 'message',
              attachments: [],
            }),
          });

          if (messageResponse.ok) {
            const message = await messageResponse.json();
            setSelectedThread(prev => prev ? {
              ...prev,
              messages: [...prev.messages, message]
            } : null);
          }
        } catch (msgErr) {
          console.error('Failed to send initial message:', msgErr);
        }
      }
    } catch (err) {
      throw err; // Re-throw to be handled by the dialog
    } finally {
      setCreating(false);
    }
  };

  // Send message
  const sendMessage = async () => {
    if (!selectedThread || !newMessage.trim()) return;

    try {
      setSending(true);
      
      const response = await fetch(`/api/handovers/qa/${selectedThread.id}/messages`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          content: newMessage,
          messageType: 'message',
          attachments: [],
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to send message');
      }

      const message = await response.json();
      
      // Update the selected thread with the new message
      setSelectedThread(prev => prev ? {
        ...prev,
        messages: [...prev.messages, message]
      } : null);
      
      // Update the thread in the list
      setThreads(prev => prev.map(thread => 
        thread.id === selectedThread.id 
          ? { ...thread, messages: [...thread.messages, message] }
          : thread
      ));
      
      setNewMessage('');
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to send message');
    } finally {
      setSending(false);
    }
  };

  const getStatusBadge = (status: string) => {
    const config = statusConfig[status as keyof typeof statusConfig];
    if (!config) return null;

    const Icon = config.icon;
    return (
      <Badge className={config.color}>
        <Icon className="w-3 h-3 mr-1" />
        {config.label}
      </Badge>
    );
  };

  const getPriorityBadge = (priority: string) => {
    const config = priorityConfig[priority as keyof typeof priorityConfig];
    if (!config) return null;

    return (
      <Badge variant="outline" className={config.color}>
        {config.label}
      </Badge>
    );
  };

  const getCategoryBadge = (category: string) => {
    const config = categoryConfig[category as keyof typeof categoryConfig];
    if (!config) return null;

    return (
      <Badge variant="outline" className={config.color}>
        {config.label}
      </Badge>
    );
  };

  const getUserDisplayName = (user?: { firstName?: string; lastName?: string; email?: string }) => {
    if (!user) return 'Unknown';
    return user.firstName && user.lastName
      ? `${user.firstName} ${user.lastName}`
      : user.email || 'Unknown';
  };

  // Filter options for SearchableSelect
  const getStatusOptions = () => [
    { value: 'all', label: 'All' },
    { value: 'open', label: 'Open' },
    { value: 'answered', label: 'Answered' },
    { value: 'closed', label: 'Closed' },
    { value: 'escalated', label: 'Escalated' },
  ];

  const getPriorityOptions = () => [
    { value: 'all', label: 'All' },
    { value: 'low', label: 'Low' },
    { value: 'medium', label: 'Medium' },
    { value: 'high', label: 'High' },
    { value: 'urgent', label: 'Urgent' },
  ];

  const getCategoryOptions = () => [
    { value: 'all', label: 'All' },
    { value: 'general', label: 'General' },
    { value: 'technical', label: 'Technical' },
    { value: 'requirements', label: 'Requirements' },
    { value: 'timeline', label: 'Timeline' },
    { value: 'budget', label: 'Budget' },
  ];

  if (loading && threads.length === 0) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-sm text-gray-600">Loading Q&A threads...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with Filters */}
      <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
        <div>
          <h2 className="text-xl font-semibold">Q&A Threads</h2>
          <p className="text-sm text-muted-foreground mt-1">
            Ask questions and get answers about this handover
          </p>
        </div>
        <Button
          size="sm"
          onClick={() => setShowCreateForm(true)}
        >
          <Plus className="w-4 h-4 mr-2" />
          New Thread
        </Button>
      </div>

      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Filters Bar */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-sm">
            <Filter className="w-4 h-4" />
            Filters
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label className="text-xs font-medium">Status</Label>
              <SearchableSelect
                options={getStatusOptions()}
                value={filters.status}
                onValueChange={(value) => setFilters(prev => ({ ...prev, status: value }))}
                placeholder="All"
                searchPlaceholder="Search status..."
                emptyMessage="No status found"
                className="h-8"
              />
            </div>

            <div className="space-y-2">
              <Label className="text-xs font-medium">Priority</Label>
              <SearchableSelect
                options={getPriorityOptions()}
                value={filters.priority}
                onValueChange={(value) => setFilters(prev => ({ ...prev, priority: value }))}
                placeholder="All"
                searchPlaceholder="Search priority..."
                emptyMessage="No priority found"
                className="h-8"
              />
            </div>

            <div className="space-y-2">
              <Label className="text-xs font-medium">Category</Label>
              <SearchableSelect
                options={getCategoryOptions()}
                value={filters.category}
                onValueChange={(value) => setFilters(prev => ({ ...prev, category: value }))}
                placeholder="All"
                searchPlaceholder="Search category..."
                emptyMessage="No category found"
                className="h-8"
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Main Content Area */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 h-[600px]">

        {/* Threads List */}
        <div className="lg:col-span-1 space-y-4">
          <div className="space-y-2 overflow-y-auto h-full">
            {threads.length > 0 ? (
              threads.map((thread) => (
                <Card
                  key={thread.id}
                  className={cn(
                    "cursor-pointer",
                    selectedThread?.id === thread.id
                      ? "border-blue-500 bg-blue-50"
                      : ""
                  )}
                  onClick={() => setSelectedThread(thread)}
                >
                  <CardContent className="p-3">
                    <div className="space-y-2">
                      <div className="flex items-start justify-between">
                        <h3 className="font-medium text-sm line-clamp-2">{thread.title}</h3>
                        <div className="flex items-center gap-1">
                          {thread.priority === 'urgent' && <ArrowUp className="w-3 h-3 text-red-500" />}
                          {thread.priority === 'high' && <ArrowUp className="w-3 h-3 text-orange-500" />}
                          {thread.priority === 'low' && <ArrowDown className="w-3 h-3 text-gray-500" />}
                        </div>
                      </div>

                      <div className="flex items-center gap-1 flex-wrap">
                        {getStatusBadge(thread.status)}
                        {getCategoryBadge(thread.category)}
                      </div>

                      <div className="text-xs text-gray-500">
                        <div>Created by {getUserDisplayName(thread.createdBy)}</div>
                        <div>{format(new Date(thread.createdAt), 'MMM d, h:mm a')}</div>
                        <div>{thread._count.messages} message(s)</div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))
            ) : !loading ? (
              <Card className="h-full flex items-center justify-center">
                <CardContent>
                  <div className="text-center py-8">
                    <MessageSquare className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">No Q&A threads found</h3>
                    <p className="text-gray-600 mb-4">
                      Start a conversation by creating your first thread
                    </p>
                    <Button
                      size="sm"
                      onClick={() => setShowCreateForm(true)}
                    >
                      <Plus className="w-4 h-4 mr-2" />
                      Create First Thread
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ) : null}
          </div>
        </div>

        {/* Thread Messages */}
        <div className="lg:col-span-1">
        {selectedThread ? (
          <Card className="h-full flex flex-col">
            <CardHeader>
              <div className="flex items-start justify-between">
                <div>
                  <CardTitle className="text-lg">{selectedThread.title}</CardTitle>
                  <div className="flex items-center gap-2 mt-1">
                    {getStatusBadge(selectedThread.status)}
                    {getPriorityBadge(selectedThread.priority)}
                    {getCategoryBadge(selectedThread.category)}
                  </div>
                </div>
                <div className="text-sm text-gray-500 text-right">
                  <div>Created by {getUserDisplayName(selectedThread.createdBy)}</div>
                  <div>{format(new Date(selectedThread.createdAt), 'MMM d, yyyy h:mm a')}</div>
                  {selectedThread.responseTime && (
                    <div>Response time: {selectedThread.responseTime}h</div>
                  )}
                </div>
              </div>
            </CardHeader>
            
            <CardContent className="flex-1 flex flex-col">
              {/* Messages */}
              <div className="flex-1 overflow-y-auto space-y-4 mb-4">
                {selectedThread.messages.map((message) => (
                  <div
                    key={message.id}
                    className="flex items-start gap-3"
                  >
                    <div className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center">
                      <User className="w-4 h-4" />
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <span className="font-medium text-sm">
                          {getUserDisplayName(message.author)}
                        </span>
                        <span className="text-xs text-gray-500">
                          {format(new Date(message.createdAt), 'MMM d, h:mm a')}
                        </span>
                        {message.messageType !== 'message' && (
                          <Badge variant="outline" className="text-xs">
                            {message.messageType}
                          </Badge>
                        )}
                      </div>
                      <div className="text-sm bg-gray-50 p-3 rounded-lg">
                        {message.content}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
              
              {/* Message Input */}
              <div className="border-t pt-4">
                <div className="flex gap-2">
                  <Textarea
                    value={newMessage}
                    onChange={(e) => setNewMessage(e.target.value)}
                    placeholder="Type your message..."
                    rows={2}
                    className="flex-1"
                  />
                  <Button
                    onClick={sendMessage}
                    disabled={sending || !newMessage.trim()}
                    className="self-end"
                  >
                    <Send className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ) : (
          <Card className="h-full flex items-center justify-center">
            <CardContent>
              <div className="text-center">
                <MessageSquare className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">Select a Q&A Thread</h3>
                <p className="text-gray-600">
                  Choose a thread from the list to view and participate in the conversation
                </p>
              </div>
            </CardContent>
          </Card>
        )}
        </div>
      </div>

      {/* Create Thread Dialog */}
      <CreateThreadDialog
        open={showCreateForm}
        onOpenChange={setShowCreateForm}
        onCreateThread={createThread}
        creating={creating}
      />
    </div>
  );
}
