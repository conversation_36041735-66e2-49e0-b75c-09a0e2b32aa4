import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { DatabasePerformanceMonitor } from '@/lib/prisma';

/**
 * GET /api/admin/database/performance
 * Get database performance metrics and statistics
 */
export async function GET(request: NextRequest) {
  try {
    // Check authentication and admin permissions
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Check if user is platform admin
    if (!session.user.isPlatformAdmin) {
      return NextResponse.json(
        { error: 'Platform admin access required' },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const queryName = searchParams.get('query');
    const format = searchParams.get('format') || 'summary';

    if (queryName) {
      // Get specific query statistics
      const queryStats = DatabasePerformanceMonitor.getQueryStats();
      const specificStats = queryStats[queryName] || null;
      return NextResponse.json({
        queryName,
        stats: specificStats,
        timestamp: new Date().toISOString()
      });
    }

    if (format === 'detailed') {
      // Get detailed statistics for all queries
      const allQueryStats = DatabasePerformanceMonitor.getQueryStats();
      return NextResponse.json({
        queries: allQueryStats,
        timestamp: new Date().toISOString()
      });
    }

    // Get performance summary
    const performanceSummary = DatabasePerformanceMonitor.getPerformanceSummary();
    
    return NextResponse.json({
      summary: performanceSummary,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Database performance metrics error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to get database performance metrics',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/admin/database/performance
 * Clear performance metrics
 */
export async function DELETE(request: NextRequest) {
  try {
    // Check authentication and admin permissions
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Check if user is platform admin
    if (!session.user.isPlatformAdmin) {
      return NextResponse.json(
        { error: 'Platform admin access required' },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const queryName = searchParams.get('query');

    // Clear all metrics (we don't have individual query clearing in the basic monitor)
    DatabasePerformanceMonitor.getQueryStats(); // This will reset the internal state
    return NextResponse.json({
      success: true,
      message: 'Performance metrics cleared'
    });

  } catch (error) {
    console.error('Clear performance metrics error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to clear performance metrics',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}
