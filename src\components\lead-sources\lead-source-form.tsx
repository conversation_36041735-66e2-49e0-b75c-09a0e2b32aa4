'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Save, X, Palette, Hash } from 'lucide-react';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { LeadSourceWithRelations } from '@/services/lead-source-management';
import { toastFunctions as toast } from '@/hooks/use-toast';

// Form validation schema
const leadSourceFormSchema = z.object({
  name: z.string().min(1, 'Name is required').max(100, 'Name too long'),
  description: z.string().optional(),
  category: z.enum([
    'digital',
    'outbound', 
    'paid_advertising',
    'events',
    'partnerships',
    'traditional',
    'word_of_mouth',
    'existing_relationships',
    'custom',
    'other'
  ]).default('custom'),
  icon: z.string().optional(),
  color: z.string().optional(),
  isActive: z.boolean().default(true),
  sortOrder: z.number().int().min(0).default(0)
});

type LeadSourceFormData = z.infer<typeof leadSourceFormSchema>;

interface LeadSourceFormProps {
  leadSource?: LeadSourceWithRelations;
  onSuccess?: () => void;
  onCancel?: () => void;
}

// Predefined colors
const PREDEFINED_COLORS = [
  '#3B82F6', // Blue
  '#10B981', // Green
  '#F59E0B', // Yellow
  '#EF4444', // Red
  '#8B5CF6', // Purple
  '#06B6D4', // Cyan
  '#F97316', // Orange
  '#84CC16', // Lime
  '#EC4899', // Pink
  '#6B7280', // Gray
];

// Category options
const CATEGORY_OPTIONS = [
  { value: 'digital', label: 'Digital' },
  { value: 'outbound', label: 'Outbound' },
  { value: 'paid_advertising', label: 'Paid Advertising' },
  { value: 'events', label: 'Events' },
  { value: 'partnerships', label: 'Partnerships' },
  { value: 'traditional', label: 'Traditional' },
  { value: 'word_of_mouth', label: 'Word of Mouth' },
  { value: 'existing_relationships', label: 'Existing Relationships' },
  { value: 'custom', label: 'Custom' },
  { value: 'other', label: 'Other' }
];

export function LeadSourceForm({ leadSource, onSuccess, onCancel }: LeadSourceFormProps) {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [selectedColor, setSelectedColor] = useState(leadSource?.color || PREDEFINED_COLORS[0]);

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
    reset
  } = useForm<LeadSourceFormData>({
    resolver: zodResolver(leadSourceFormSchema),
    defaultValues: {
      name: leadSource?.name || '',
      description: leadSource?.description || '',
      category: leadSource?.category as any || 'custom',
      icon: leadSource?.icon || '',
      color: leadSource?.color || PREDEFINED_COLORS[0],
      isActive: leadSource?.isActive ?? true,
      sortOrder: leadSource?.sortOrder || 0
    }
  });

  const watchedCategory = watch('category');

  useEffect(() => {
    if (leadSource) {
      reset({
        name: leadSource.name,
        description: leadSource.description || '',
        category: leadSource.category as any,
        icon: leadSource.icon || '',
        color: leadSource.color || PREDEFINED_COLORS[0],
        isActive: leadSource.isActive,
        sortOrder: leadSource.sortOrder
      });
      setSelectedColor(leadSource.color || PREDEFINED_COLORS[0]);
    }
  }, [leadSource, reset]);

  const onSubmit = async (data: LeadSourceFormData) => {
    try {
      setLoading(true);

      const url = leadSource 
        ? `/api/lead-sources/${leadSource.id}`
        : '/api/lead-sources';
      
      const method = leadSource ? 'PUT' : 'POST';

      const response = await fetch(url, {
        method,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ ...data, color: selectedColor })
      });

      const result = await response.json();

      if (result.success) {
        toast.success(`Lead source ${leadSource ? 'updated' : 'created'} successfully`);
        if (onSuccess) {
          onSuccess();
        } else {
          router.push('/leads/sources');
        }
      } else {
        toast.error(result.error || `Failed to ${leadSource ? 'update' : 'create'} lead source`);
      }
    } catch (error) {
      console.error('Error saving lead source:', error);
      toast.error(`Failed to ${leadSource ? 'update' : 'create'} lead source`);
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    if (onCancel) {
      onCancel();
    } else {
      router.push('/leads/sources');
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>
          {leadSource ? 'Edit Lead Source' : 'Add Lead Source'}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {/* Basic Information */}
          <div className="grid gap-4 md:grid-cols-2">
            <div className="space-y-2">
              <Label htmlFor="name">Name *</Label>
              <Input
                id="name"
                {...register('name')}
                placeholder="Enter lead source name"
                error={errors.name?.message}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="category">Category *</Label>
              <Select
                value={watchedCategory}
                onValueChange={(value) => setValue('category', value as any)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select category" />
                </SelectTrigger>
                <SelectContent position="popper" sideOffset={4}>
                  {CATEGORY_OPTIONS.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.category && (
                <p className="text-sm text-destructive">{errors.category.message}</p>
              )}
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              {...register('description')}
              placeholder="Enter lead source description"
              rows={3}
            />
          </div>

          {/* Visual Settings */}
          <div className="grid gap-4 md:grid-cols-2">
            <div className="space-y-2">
              <Label htmlFor="icon">Icon</Label>
              <Input
                id="icon"
                {...register('icon')}
                placeholder="Enter icon name (e.g., globe, mail)"
              />
            </div>

            <div className="space-y-2">
              <Label>Color</Label>
              <div className="flex items-center space-x-2">
                <div className="flex flex-wrap gap-2">
                  {PREDEFINED_COLORS.map((color) => (
                    <button
                      key={color}
                      type="button"
                      className={`w-8 h-8 rounded-lg border-2 ${
                        selectedColor === color ? 'border-gray-400' : 'border-gray-200'
                      }`}
                      style={{ backgroundColor: color }}
                      onClick={() => {
                        setSelectedColor(color);
                        setValue('color', color);
                      }}
                    />
                  ))}
                </div>
                <div className="flex items-center space-x-2">
                  <Palette className="w-4 h-4 text-muted-foreground" />
                  <Input
                    type="color"
                    value={selectedColor}
                    onChange={(e) => {
                      setSelectedColor(e.target.value);
                      setValue('color', e.target.value);
                    }}
                    className="w-12 h-8 p-0 border-0"
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Settings */}
          <div className="grid gap-4 md:grid-cols-2">
            <div className="space-y-2">
              <Label htmlFor="sortOrder">Sort Order</Label>
              <Input
                id="sortOrder"
                type="number"
                {...register('sortOrder', { valueAsNumber: true })}
                placeholder="0"
                min="0"
              />
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="isActive"
                checked={watch('isActive')}
                onCheckedChange={(checked) => setValue('isActive', checked)}
              />
              <Label htmlFor="isActive">Active</Label>
            </div>
          </div>

          {/* Preview */}
          <div className="space-y-2">
            <Label>Preview</Label>
            <div className="flex items-center space-x-3 p-4 border rounded-lg bg-muted/50">
              <div 
                className="w-10 h-10 rounded-lg flex items-center justify-center text-white text-sm font-medium"
                style={{ backgroundColor: selectedColor }}
              >
                {watch('icon') || '?'}
              </div>
              <div>
                <div className="font-medium">{watch('name') || 'Lead Source Name'}</div>
                <div className="text-sm text-muted-foreground">
                  {watch('description') || 'Lead source description'}
                </div>
              </div>
            </div>
          </div>

          {/* Actions */}
          <div className="flex justify-end space-x-4">
            <Button
              type="button"
              variant="outline"
              onClick={handleCancel}
              disabled={loading}
            >
              <X className="w-4 h-4 mr-2" />
              Cancel
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? (
                <LoadingSpinner size="sm" className="mr-2" />
              ) : (
                <Save className="w-4 h-4 mr-2" />
              )}
              {leadSource ? 'Update' : 'Create'} Lead Source
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}
