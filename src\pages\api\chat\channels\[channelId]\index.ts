import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../../../../../lib/auth';
import { ChatChannelService } from '../../../../../services/communication';
import { withTenant } from '../../../../../lib/middleware/withTenant';
import { withRateLimit } from '../../../../../lib/middleware/withRateLimit';

async function handler(req: NextApiRequest, res: NextApiResponse) {
  const session = await getServerSession(req, res, authOptions);
  
  if (!session?.user?.id) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  const tenantId = req.headers['x-tenant-id'] as string;
  const userId = session.user.id;
  const channelId = req.query.channelId as string;

  if (!channelId) {
    return res.status(400).json({ error: 'Channel ID is required' });
  }

  try {
    switch (req.method) {
      case 'GET':
        const channel = await ChatChannelService.getChannelById(tenantId, channelId, userId);
        return res.status(200).json(channel);

      default:
        res.setHeader('Allow', ['GET']);
        return res.status(405).json({ error: `Method ${req.method} not allowed` });
    }
  } catch (error) {
    console.error('Chat channel API error:', error);
    return res.status(500).json({ 
      error: error instanceof Error ? error.message : 'Internal server error' 
    });
  }
}

export default withRateLimit(withTenant(handler));
