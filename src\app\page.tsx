'use client';

import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';
import { LoadingSpinner } from '@/components/ui/loading-spinner';

export default function HomePage() {
  const { data: session, status } = useSession();
  const router = useRouter();

  useEffect(() => {
    if (status === 'loading') return;

    if (status === 'authenticated') {
      if (session?.user?.isPlatformAdmin) {
        // Redirect platform admins to admin dashboard
        router.replace('/admin/dashboard');
      } else {
        // Redirect regular users to dashboard
        router.replace('/dashboard');
      }
    } else {
      // Redirect unauthenticated users to signin
      router.replace('/auth/signin');
    }
  }, [status, session, router]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
      <LoadingSpinner size="lg" />
    </div>
  );
}