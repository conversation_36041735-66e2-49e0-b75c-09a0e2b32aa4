# Enhanced User Management System

This document describes the enhanced user management features implemented in the CRM system.

## Overview

The enhanced user management system provides comprehensive password management, email notifications, portal notifications, and advanced permission assignment capabilities while maintaining security best practices and tenant isolation.

## Features

### 1. Password Management

#### Password Strength Indicator
- **Component**: `src/components/ui/password-strength-indicator.tsx`
- **Features**:
  - Real-time password strength visualization (weak, fair, good, strong)
  - Color-coded strength bar with percentage indicator
  - Requirements checklist with visual feedback
  - Configurable minimum length and requirements

#### Password Input Component
- **Component**: `src/components/ui/password-input.tsx`
- **Features**:
  - Password visibility toggle
  - Error state display
  - Integration with form validation

#### Password Utilities
- **Module**: `src/lib/password-utils.ts`
- **Features**:
  - Secure password generation (temporary and user-friendly)
  - Password strength validation
  - Password hashing and verification
  - Configurable character sets and requirements

### 2. Email Notifications

#### Email Templates
- **Module**: `src/lib/email-templates.ts`
- **Templates**:
  - Welcome email with login credentials
  - Password reset notifications
  - User invitation emails
- **Features**:
  - Professional HTML and text versions
  - Organization branding
  - Responsive design
  - Security best practices

#### Notification Service
- **Service**: `src/services/user-notification-service.ts`
- **Features**:
  - Welcome email automation
  - User invitation management
  - Account activation notifications
  - Role change notifications
  - Integration with existing SMTP service

### 3. Portal Notifications

#### Database Schema
- **Table**: `notifications`
- **Fields**:
  - `id`, `tenant_id`, `user_id`
  - `type`, `title`, `message`
  - `data` (JSON), `is_read`, `read_at`, `created_at`

#### Notification Types
- `welcome` - New user account creation
- `invite` - User invitation sent/received
- `password_reset` - Password changed by admin
- `account_activated` - Account activation
- `role_changed` - Role/permission changes

### 4. Advanced Permission Assignment

#### Permission Management Component
- **Component**: `src/components/admin/advanced-permission-assignment.tsx`
- **Features**:
  - Role-based permission assignment
  - Individual permission grants
  - Privilege escalation prevention
  - Permission inheritance visualization
  - Security validation and warnings

#### Security Features
- Users cannot assign permissions they don't have
- Tenant admin override capabilities
- Audit logging for permission changes
- Real-time validation feedback

## Implementation Details

### User Creation Flow

1. **Form Validation**
   - Password strength requirements
   - Email format validation
   - Required field validation

2. **User Creation**
   - Password hashing with bcrypt
   - Database transaction for consistency
   - Role and team assignment

3. **Notification Flow**
   - Portal notification creation
   - Welcome email generation and sending
   - Toast notification for admin feedback

### User Edit Flow

1. **Optional Password Change**
   - Toggle-based password update
   - Strength validation for new passwords
   - Secure password generation option

2. **Permission Updates**
   - Advanced permission assignment interface
   - Privilege escalation checks
   - Audit logging

3. **Notification Updates**
   - Role change notifications
   - Password change alerts
   - Admin feedback messages

### Security Considerations

#### Password Security
- Minimum 8 character requirement
- Mixed case, numbers, and special characters
- Secure random generation
- bcrypt hashing with salt rounds

#### Permission Security
- Principle of least privilege
- Privilege escalation prevention
- Tenant isolation enforcement
- Audit trail maintenance

#### Email Security
- Template injection prevention
- Rate limiting (via SMTP service)
- Secure credential handling
- Optional email sending (configurable)

## Configuration

### Environment Variables

```env
# Email Configuration
ENABLE_EMAIL_SENDING=true
SMTP_HOST=smtp.example.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your-password
SMTP_FROM_EMAIL=<EMAIL>
SMTP_FROM_NAME=CRM Platform
SMTP_REPLY_TO=<EMAIL>
SUPPORT_EMAIL=<EMAIL>

# Application URLs
NEXTAUTH_URL=https://your-domain.com
```

### Database Migration

Run the notification table migration:

```sql
-- See: prisma/migrations/20241201_add_notifications_table.sql
```

## API Endpoints

### Password Management
- `PUT /api/users/[id]/password` - Update user password (admin)

### User Management
- `POST /api/users` - Create user with notification options
- `PUT /api/users/[id]` - Update user with permission changes

### Notifications
- `GET /api/notifications` - Get user notifications
- `PUT /api/notifications/[id]/read` - Mark notification as read

## Testing

### Password Utilities Tests
- **File**: `src/lib/__tests__/password-utils.test.ts`
- **Coverage**: Password generation, validation, strength calculation

### Manual Testing Checklist

1. **User Creation**
   - [ ] Password strength indicator works
   - [ ] Password generation functions
   - [ ] Welcome email sent (if enabled)
   - [ ] Portal notification created
   - [ ] Toast notification displayed

2. **User Editing**
   - [ ] Optional password change works
   - [ ] Password strength validation
   - [ ] Permission assignment functions
   - [ ] Privilege escalation prevented
   - [ ] Notifications sent for changes

3. **Email Notifications**
   - [ ] Welcome email template renders correctly
   - [ ] Email contains correct information
   - [ ] SMTP integration works
   - [ ] Fallback to mock mode when disabled

4. **Portal Notifications**
   - [ ] Notifications created in database
   - [ ] Notifications display in UI
   - [ ] Read/unread status tracking
   - [ ] Notification filtering works

## Troubleshooting

### Common Issues

1. **Email Not Sending**
   - Check `ENABLE_EMAIL_SENDING` environment variable
   - Verify SMTP configuration
   - Check SMTP service logs

2. **Password Validation Errors**
   - Verify password meets all requirements
   - Check minimum length configuration
   - Ensure special characters are allowed

3. **Permission Assignment Issues**
   - Verify user has required permissions
   - Check tenant admin status
   - Review privilege escalation warnings

4. **Notification Issues**
   - Check database connection
   - Verify notification table exists
   - Check tenant isolation

### Debugging

Enable debug logging:
```env
DEBUG=smtp,notifications,permissions
```

Check application logs for detailed error messages and troubleshooting information.

## Future Enhancements

- Multi-factor authentication integration
- Advanced password policies
- Bulk user operations
- Email template customization
- Real-time notification delivery
- Mobile push notifications
- Advanced audit reporting
