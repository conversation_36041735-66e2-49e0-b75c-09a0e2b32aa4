'use client';

import { PipelineBoard } from '@/components/opportunities/pipeline-board';
import { PermissionGate } from '@/components/ui/permission-gate';
import { PermissionResource, PermissionAction } from '@/types';
import { Card, CardContent } from '@/components/ui/card';
import { EmptyState } from '@/components/ui/empty-state';
import { ExclamationTriangleIcon, ArrowLeftIcon, PlusIcon, TableCellsIcon } from '@heroicons/react/24/outline';
import { PageLayout } from '@/components/layout/page-layout';
import { Button } from '@/components/ui/button';
import { useRouter } from 'next/navigation';
import { useSmoothNavigation } from '@/hooks/use-smooth-navigation';

export default function PipelinePage() {
  const router = useRouter();
  const { navigate } = useSmoothNavigation();

  const actions = (
    <div className="flex items-center gap-2">
      <Button variant="outline" size="sm" onClick={() => router.push('/opportunities')}>
        <TableCellsIcon className="h-4 w-4 mr-2" />
        Table View
      </Button>
      <Button variant="outline" size="sm" onClick={() => router.back()}>
        <ArrowLeftIcon className="h-4 w-4 mr-2" />
        Back
      </Button>
      <PermissionGate
        resource={PermissionResource.OPPORTUNITIES}
        action={PermissionAction.CREATE}
      >
        <Button size="sm" onClick={() => router.push('/opportunities/new')}>
          <PlusIcon className="h-4 w-4 mr-2" />
          Add Opportunity
        </Button>
      </PermissionGate>
    </div>
  );

  return (
    <PermissionGate
      resource={PermissionResource.OPPORTUNITIES}
      action={PermissionAction.READ}
      fallback={
        <PageLayout>
          <Card>
            <CardContent className="p-8">
              <EmptyState
                icon={<ExclamationTriangleIcon className="w-12 h-12 text-muted-foreground" />}
                title="Access Denied"
                description="You don't have permission to view the sales pipeline. Please contact your administrator for access."
                action={{
                  label: 'Back to Dashboard',
                  onClick: () => navigate('/dashboard'),
                  variant: 'primary'
                }}
              />
            </CardContent>
          </Card>
        </PageLayout>
      }
    >
      <PageLayout
        title="Sales Pipeline"
        description="Visual pipeline view with drag-and-drop opportunity management"
        actions={actions}
      >
        <PipelineBoard />
      </PageLayout>
    </PermissionGate>
  );
}
