'use client';

import React, { createContext, useContext, useEffect, useState, useCallback } from 'react';
import { useAuth } from '@/hooks/use-auth';
import { useTenant } from '@/hooks/use-tenant';
import { PermissionAction, PermissionResource, Permission, Role } from '@/types';
import { PERMISSIONS, isValidPermission } from '@/constants/permissions';
import { createPermissionString, isValidResourceAction } from '@/utils/permission-helpers';

export interface PermissionContextValue {
  permissions: Permission[];
  roles: Role[];
  isLoading: boolean;
  error: string | null;
  hasPermission: (resource: PermissionResource, action: PermissionAction) => boolean;
  hasAnyPermission: (permissions: Array<{ resource: PermissionResource; action: PermissionAction }>) => boolean;
  hasAllPermissions: (permissions: Array<{ resource: PermissionResource; action: PermissionAction }>) => boolean;
  canAccessResource: (resource: PermissionResource, action: PermissionAction, resourceOwnerId?: string) => boolean;
  isTenantAdmin: boolean;
  isSuperAdmin: boolean;
  refreshPermissions: () => Promise<void>;
  getPermissionsByCategory: () => Record<string, Permission[]>;
  getUserRoles: () => Role[];
  hasRole: (roleName: string) => boolean;
  canPerformBulkAction: (resource: PermissionResource, action: PermissionAction, itemCount: number) => boolean;
}

const PermissionContext = createContext<PermissionContextValue | undefined>(undefined);

export interface PermissionProviderProps {
  children: React.ReactNode;
}

export function PermissionProvider({ children }: PermissionProviderProps) {
  const { user, isAuthenticated } = useAuth();
  const { currentTenant } = useTenant();
  const [permissions, setPermissions] = useState<Permission[]>([]);
  const [roles, setRoles] = useState<Role[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Track ongoing requests to prevent duplicates
  const [ongoingRequest, setOngoingRequest] = useState<string | null>(null);

  // Fetch user permissions and roles (simplified)
  const fetchPermissions = useCallback(async (forceRefresh: boolean = false) => {
    if (!isAuthenticated || !user || !currentTenant) {
      setPermissions([]);
      setRoles([]);
      setIsLoading(false);
      return;
    }

    // Super admin has all permissions
    if (user.isPlatformAdmin) {
      setPermissions([]);
      setRoles([]);
      setIsLoading(false);
      return;
    }

    // Prevent duplicate requests (unless force refresh)
    if (ongoingRequest && !forceRefresh) {
      return;
    }

    setOngoingRequest(currentTenant.id);
    setIsLoading(true);
    setError(null);

    try {
      // If force refresh is requested, call the refresh endpoint first
      if (forceRefresh) {
        await fetch('/api/auth/refresh-permissions', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' }
        });
      }

      const response = await fetch(`/api/permissions/user?tenantId=${currentTenant.id}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('PermissionProvider: API error', {
          status: response.status,
          statusText: response.statusText,
          error: errorText
        });
        throw new Error(`Failed to fetch permissions: ${response.status} ${errorText}`);
      }

      const data = await response.json();

      setPermissions(data.data.effectivePermissions || []);
      setRoles(data.data.roles || []);
    } catch (err) {
      console.error('PermissionProvider: Failed to fetch permissions:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch permissions');
      setPermissions([]);
      setRoles([]);
    } finally {
      setIsLoading(false);
      setOngoingRequest(null);
    }
  }, [isAuthenticated, user, currentTenant, ongoingRequest]);

  // Fetch permissions when dependencies change (simplified)
  useEffect(() => {
    // Only fetch if we don't have permissions and no request is ongoing
    if (isAuthenticated && user && currentTenant && permissions.length === 0 && !ongoingRequest) {
      fetchPermissions();
    }
  }, [isAuthenticated, user, currentTenant, permissions.length, ongoingRequest, fetchPermissions]);

  // Check if user has specific permission
  const hasPermission = useCallback((
    resource: PermissionResource,
    action: PermissionAction
  ): boolean => {
    if (!isAuthenticated || !user) {

      return false;
    }

    // Super admin has all permissions
    if (user.isPlatformAdmin) {
      return true;
    }

    // Check if user has the specific permission using constants
    const permissionName = createPermissionString(resource, action);

    // Validate that the permission exists in our constants
    if (!isValidResourceAction(resource, action)) {
      console.warn('PermissionProvider.hasPermission: Invalid permission requested', {
        resource,
        action,
        permissionName,
        availableForResource: isValidPermission(permissionName)
      });
    }

    const hasAccess = permissions.some(p =>
      p.name === permissionName ||
      p.name === `${resource}.*` ||
      p.name === '*'
    );


    return hasAccess;
  }, [isAuthenticated, user, permissions]);

  // Check if user has any of the specified permissions
  const hasAnyPermission = useCallback((
    requiredPermissions: Array<{ resource: PermissionResource; action: PermissionAction }>
  ): boolean => {
    if (!isAuthenticated || !user) return false;

    // Super admin has all permissions
    if (user.isPlatformAdmin) return true;

    return requiredPermissions.some(({ resource, action }) => 
      hasPermission(resource, action)
    );
  }, [isAuthenticated, user, hasPermission]);

  // Check if user has all specified permissions
  const hasAllPermissions = useCallback((
    requiredPermissions: Array<{ resource: PermissionResource; action: PermissionAction }>
  ): boolean => {
    if (!isAuthenticated || !user) return false;

    // Super admin has all permissions
    if (user.isPlatformAdmin) return true;

    return requiredPermissions.every(({ resource, action }) => 
      hasPermission(resource, action)
    );
  }, [isAuthenticated, user, hasPermission]);

  // Check if user can access specific resource (ownership-based)
  const canAccessResource = useCallback((
    resource: PermissionResource,
    action: PermissionAction,
    resourceOwnerId?: string
  ): boolean => {
    if (!isAuthenticated || !user) return false;

    // Super admin has all permissions
    if (user.isPlatformAdmin) return true;

    // Check basic permission first
    if (!hasPermission(resource, action)) return false;

    // If no resource owner specified, basic permission is enough
    if (!resourceOwnerId) return true;

    // Check if user has manage permission (can access all resources)
    if (hasPermission(resource, PermissionAction.MANAGE)) return true;

    // User can only access their own resources
    return user.id === resourceOwnerId;
  }, [isAuthenticated, user, hasPermission]);

  // Check if user is tenant admin
  const isTenantAdmin = useCallback((): boolean => {
    if (!isAuthenticated || !user || !currentTenant) return false;
    
    // Super admin is considered tenant admin
    if (user.isPlatformAdmin) return true;

    // Check if user is tenant admin for current tenant
    const tenantUser = user.tenants?.find(t => t.id === currentTenant.id);
    return tenantUser?.isTenantAdmin || false;
  }, [isAuthenticated, user, currentTenant]);

  // Check if user is super admin
  const isSuperAdmin = useCallback((): boolean => {
    return user?.isPlatformAdmin || false;
  }, [user]);

  // Get permissions grouped by category
  const getPermissionsByCategory = useCallback((): Record<string, Permission[]> => {
    return permissions.reduce((acc, permission) => {
      const category = permission.category || 'other';
      if (!acc[category]) {
        acc[category] = [];
      }
      acc[category].push(permission);
      return acc;
    }, {} as Record<string, Permission[]>);
  }, [permissions]);

  // Get user roles
  const getUserRoles = useCallback((): Role[] => {
    return roles;
  }, [roles]);

  // Check if user has specific role
  const hasRole = useCallback((roleName: string): boolean => {
    if (!isAuthenticated || !user) return false;

    // Super admin has all roles
    if (user.isPlatformAdmin) return true;

    return roles.some(role => role.name.toLowerCase() === roleName.toLowerCase());
  }, [isAuthenticated, user, roles]);

  // Check if user can perform bulk actions
  const canPerformBulkAction = useCallback((
    resource: PermissionResource,
    action: PermissionAction,
    itemCount: number
  ): boolean => {
    if (!hasPermission(resource, action)) return false;

    // Additional checks for bulk operations
    if (itemCount > 100 && !hasPermission(resource, PermissionAction.MANAGE)) {
      return false; // Require manage permission for large bulk operations
    }

    return true;
  }, [hasPermission]);

  const contextValue: PermissionContextValue = {
    permissions,
    roles,
    isLoading,
    error,
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    canAccessResource,
    isTenantAdmin: isTenantAdmin(),
    isSuperAdmin: isSuperAdmin(),
    refreshPermissions: fetchPermissions,
    getPermissionsByCategory,
    getUserRoles,
    hasRole,
    canPerformBulkAction,
  };

  return (
    <PermissionContext.Provider value={contextValue}>
      {children}
    </PermissionContext.Provider>
  );
}

export function usePermissionContext(): PermissionContextValue {
  const context = useContext(PermissionContext);
  if (context === undefined) {
    throw new Error('usePermissionContext must be used within a PermissionProvider');
  }
  return context;
}

// Enhanced usePermissions hook that uses the context
export function usePermissions() {
  return usePermissionContext();
}
