import { NextResponse } from 'next/server';
import { z } from 'zod';
import { withPermission, AuthenticatedRequest } from '@/lib/permission-middleware';
import { roleManagementService } from '@/services/role-management';
import { PermissionAction, PermissionResource } from '@/types';

const assignRoleSchema = z.object({
  userId: z.string().min(1, 'User ID is required'),
  roleId: z.string().min(1, 'Role ID is required'),
  expiresAt: z.string().datetime().optional().transform(val => val ? new Date(val) : undefined)
});

const removeRoleSchema = z.object({
  userId: z.string().min(1, 'User ID is required'),
  roleId: z.string().min(1, 'Role ID is required')
});

/**
 * POST /api/roles/assign
 * Assign a role to a user
 */
export const POST = withPermission({
  resource: PermissionResource.USERS,
  action: PermissionAction.MANAGE,
  allowSuperAdmin: true
})(async (req) => {
  try {
    const body = await req.json();
    const validatedData = assignRoleSchema.parse(body);

    await roleManagementService.assignRoleToUser(
      req.tenantId,
      {
        userId: validatedData.userId,
        roleId: validatedData.roleId,
        expiresAt: validatedData.expiresAt
      },
      req.userId
    );

    return NextResponse.json({
      success: true,
      message: 'Role assigned successfully'
    });

  } catch (error) {
    console.error('Assign role error:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          error: 'Validation failed',
          details: error.errors.map(e => ({
            field: e.path.join('.'),
            message: e.message
          }))
        },
        { status: 400 }
      );
    }

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to assign role' },
      { status: 500 }
    );
  }
});

/**
 * DELETE /api/roles/assign
 * Remove a role from a user
 */
export const DELETE = withPermission({
  resource: PermissionResource.USERS,
  action: PermissionAction.MANAGE,
  allowSuperAdmin: true
})(async (req) => {
  try {
    const body = await req.json();
    const validatedData = removeRoleSchema.parse(body);

    await roleManagementService.removeRoleFromUser(
      req.tenantId,
      validatedData.userId,
      validatedData.roleId,
      req.userId
    );

    return NextResponse.json({
      success: true,
      message: 'Role removed successfully'
    });

  } catch (error) {
    console.error('Remove role error:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          error: 'Validation failed',
          details: error.errors.map(e => ({
            field: e.path.join('.'),
            message: e.message
          }))
        },
        { status: 400 }
      );
    }

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to remove role' },
      { status: 500 }
    );
  }
});

/**
 * GET /api/roles/assign
 * Get user roles
 */
export const GET = withPermission({
  resource: PermissionResource.USERS,
  action: PermissionAction.READ,
  allowSuperAdmin: true
})(async (req) => {
  try {
    const { searchParams } = new URL(req.url);
    const userId = searchParams.get('userId');

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      );
    }

    const roles = await roleManagementService.getUserRoles(userId, req.tenantId);

    return NextResponse.json({
      success: true,
      data: {
        userId,
        roles,
        count: roles.length
      }
    });

  } catch (error) {
    console.error('Get user roles error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch user roles' },
      { status: 500 }
    );
  }
});
