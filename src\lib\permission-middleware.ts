import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { permissionService } from '@/services/permission-service';
import { PermissionAction, PermissionResource } from '@/types';
import { createPermissionString, isValidResourceAction } from '@/utils/permission-helpers';
import { prisma } from '@/lib/prisma';

export interface PermissionMiddlewareOptions {
  resource: PermissionResource;
  action: PermissionAction;
  requireOwnership?: boolean;
  getResourceOwnerId?: (req: NextRequest) => Promise<string | null>;
  allowSuperAdmin?: boolean;
}

export interface AuthenticatedRequest extends NextRequest {
  userId: string;
  tenantId: string;
  session: any;
  isSuperAdmin: boolean;
}

/**
 * Middleware to check if user has required permission
 */
export function withPermission(options: PermissionMiddlewareOptions) {
  return function (handler: (req: AuthenticatedRequest, context?: any) => Promise<NextResponse>) {
    return async function (req: NextRequest, context?: any): Promise<NextResponse> {
      try {
        // Get user session
        const session = await getServerSession(authOptions);
        
        if (!session?.user) {
          return NextResponse.json(
            { error: 'Authentication required' },
            { status: 401 }
          );
        }

        const userId = session.user.id;
        const isSuperAdmin = session.user.isPlatformAdmin || false;

        // Allow super admin if specified
        if (options.allowSuperAdmin && isSuperAdmin) {
          const authenticatedReq = req as AuthenticatedRequest;
          authenticatedReq.userId = userId;
          authenticatedReq.tenantId = ''; // Super admin has no tenant context
          authenticatedReq.session = session;
          authenticatedReq.isSuperAdmin = true;
          return handler(authenticatedReq, context);
        }

        // Get tenant context for regular users
        if (isSuperAdmin && !options.allowSuperAdmin) {
          return NextResponse.json(
            { error: 'This endpoint is not available for super admins' },
            { status: 403 }
          );
        }

        // Get user's tenant context
        const tenantUser = await prisma.tenantUser.findFirst({
          where: {
            userId,
            status: 'active'
          },
          include: {
            tenant: true
          }
        });

        if (!tenantUser) {
          return NextResponse.json(
            { error: 'No active tenant found for user' },
            { status: 403 }
          );
        }

        const tenantId = tenantUser.tenantId;

        // Check basic permission
        const hasPermission = await permissionService.hasPermission(
          userId,
          tenantId,
          options.resource,
          options.action
        );

        if (!hasPermission) {
          // Log unauthorized access attempt
          await prisma.auditLog.create({
            data: {
              tenantId,
              userId,
              action: 'UNAUTHORIZED_ACCESS_ATTEMPT',
              resourceType: options.resource,
              newValues: {
                requiredPermission: `${options.resource}.${options.action.toLowerCase()}`,
                endpoint: req.url,
                method: req.method
              }
            }
          });

          return NextResponse.json(
            { 
              error: 'Insufficient permissions',
              required: `${options.resource}.${options.action.toLowerCase()}`
            },
            { status: 403 }
          );
        }

        // Check resource ownership if required
        if (options.requireOwnership && options.getResourceOwnerId) {
          const resourceOwnerId = await options.getResourceOwnerId(req);
          
          if (resourceOwnerId) {
            const accessResult = await permissionService.canAccessResource(
              userId,
              tenantId,
              options.resource,
              options.action,
              resourceOwnerId
            );

            if (!accessResult.allowed) {
              // Log unauthorized resource access attempt
              await prisma.auditLog.create({
                data: {
                  tenantId,
                  userId,
                  action: 'UNAUTHORIZED_RESOURCE_ACCESS',
                  resourceType: options.resource,
                  newValues: {
                    reason: accessResult.reason,
                    resourceOwnerId,
                    endpoint: req.url,
                    method: req.method
                  }
                }
              });

              return NextResponse.json(
                { 
                  error: accessResult.reason || 'Access denied',
                  required: accessResult.requiredPermission
                },
                { status: 403 }
              );
            }
          }
        }

        // Add user context to request
        const authenticatedReq = req as AuthenticatedRequest;
        authenticatedReq.userId = userId;
        authenticatedReq.tenantId = tenantId;
        authenticatedReq.session = session;
        authenticatedReq.isSuperAdmin = false;

        return handler(authenticatedReq, context);
      } catch (error) {
        console.error('Permission middleware error:', error);
        return NextResponse.json(
          { error: 'Internal server error' },
          { status: 500 }
        );
      }
    };
  };
}

/**
 * Middleware for tenant admin only endpoints
 */
export function withTenantAdmin(handler: (req: AuthenticatedRequest, context?: any) => Promise<NextResponse>) {
  return withPermission({
    resource: PermissionResource.USERS,
    action: PermissionAction.MANAGE,
    allowSuperAdmin: true
  })(handler);
}

/**
 * Middleware for super admin only endpoints
 */
export function withSuperAdmin(handler: (req: AuthenticatedRequest, context?: any) => Promise<NextResponse>) {
  return async function (req: NextRequest, context?: any): Promise<NextResponse> {
    try {
      const session = await getServerSession(authOptions);
      
      if (!session?.user) {
        return NextResponse.json(
          { error: 'Authentication required' },
          { status: 401 }
        );
      }

      if (!session.user.isPlatformAdmin) {
        return NextResponse.json(
          { error: 'Super admin access required' },
          { status: 403 }
        );
      }

      const authenticatedReq = req as AuthenticatedRequest;
      authenticatedReq.userId = session.user.id;
      authenticatedReq.tenantId = '';
      authenticatedReq.session = session;
      authenticatedReq.isSuperAdmin = true;

      return handler(authenticatedReq, context);
    } catch (error) {
      console.error('Super admin middleware error:', error);
      return NextResponse.json(
        { error: 'Internal server error' },
        { status: 500 }
      );
    }
  };
}

/**
 * Helper function to get resource owner ID from URL parameters
 */
export function getResourceOwnerFromParams(paramName: string = 'id') {
  return async function (req: NextRequest): Promise<string | null> {
    try {
      const url = new URL(req.url);
      const pathSegments = url.pathname.split('/');
      const paramIndex = pathSegments.findIndex(segment => segment === paramName);
      
      if (paramIndex !== -1 && pathSegments[paramIndex + 1]) {
        return pathSegments[paramIndex + 1];
      }

      // Try to get from search params
      return url.searchParams.get(paramName);
    } catch (error) {
      console.error('Failed to get resource owner from params:', error);
      return null;
    }
  };
}

/**
 * Helper function to get resource owner ID from database
 */
export function getResourceOwnerFromDB(
  table: string,
  idField: string = 'id',
  ownerField: string = 'ownerId'
) {
  return async function (req: NextRequest): Promise<string | null> {
    try {
      const url = new URL(req.url);
      const resourceId = url.searchParams.get('id') || 
                        url.pathname.split('/').pop();

      if (!resourceId) return null;

      // Use raw query to avoid type issues
      const result = await prisma.$queryRawUnsafe(
        `SELECT ${ownerField} FROM ${table} WHERE ${idField} = $1`,
        resourceId
      ) as any[];

      return result[0]?.[ownerField] || null;
    } catch (error) {
      console.error('Failed to get resource owner from DB:', error);
      return null;
    }
  };
}

/**
 * Permission checking utility for API routes
 */
export async function checkPermission(
  userId: string,
  tenantId: string,
  resource: PermissionResource,
  action: PermissionAction
): Promise<{ allowed: boolean; error?: string }> {
  try {
    const hasPermission = await permissionService.hasPermission(
      userId,
      tenantId,
      resource,
      action
    );

    if (!hasPermission) {
      return {
        allowed: false,
        error: `Permission denied: ${resource}.${action.toLowerCase()} required`
      };
    }

    return { allowed: true };
  } catch (error) {
    console.error('Permission check failed:', error);
    return {
      allowed: false,
      error: 'Permission check failed'
    };
  }
}
