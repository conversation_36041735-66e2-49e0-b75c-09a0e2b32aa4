# Production-Ready Docker Setup

This document outlines the comprehensive Docker setup for the CRM platform, designed for production deployment with automatic initialization, health checks, and multi-environment support.

## 🚀 Key Features

### Automated Initialization
- ✅ **Database Migrations**: Automatically runs Prisma migrations on startup
- ✅ **Prisma Client Generation**: Ensures Prisma client is always available
- ✅ **Database Seeding**: Creates initial users and data (configurable)
- ✅ **Health Checks**: Waits for all services to be healthy
- ✅ **Error Recovery**: Robust error handling and retry mechanisms

### Multi-Environment Support
- 🔧 **Development**: Hot reloading, debug tools, exposed ports
- 🏗️ **Staging**: Production-like with monitoring and logging
- 🚀 **Production**: Optimized performance, security, and resource limits

### Security & Performance
- 🔒 **Security**: Non-root users, resource limits, network isolation
- ⚡ **Performance**: Optimized builds, caching, resource allocation
- 📊 **Monitoring**: Health checks, logging, metrics collection

## 📁 File Structure

```
├── docker-compose.yml              # Base configuration
├── docker-compose.dev.yml          # Development overrides
├── docker-compose.staging.yml      # Staging overrides  
├── docker-compose.production.yml   # Production overrides
├── Dockerfile                      # Multi-stage build
├── scripts/
│   ├── docker-migrate.sh          # Enhanced startup script
│   ├── docker-start.sh            # Linux/Mac startup
│   ├── restart-docker.bat         # Windows startup
│   └── fix-auth-issues.js         # Authentication diagnostics
└── docs/
    ├── TROUBLESHOOTING_AUTH.md     # Auth troubleshooting
    ├── PGADMIN_SETUP.md           # pgAdmin configuration
    └── PRODUCTION_READY_DOCKER.md # This document
```

## 🎯 Quick Start

### Windows
```bash
# Development
scripts\restart-docker.bat

# Production
scripts\restart-docker.bat production
```

### Linux/Mac
```bash
# Development
./scripts/docker-start.sh

# Production  
./scripts/docker-start.sh production
```

## 🔧 Configuration

### Environment Variables

**Development** (`.env.docker`):
```bash
DATABASE_URL="**************************************/crm_platform"
NEXTAUTH_URL="http://localhost:3010"
NEXTAUTH_SECRET="your-secret-here"
AUTO_MIGRATE=true
AUTO_SEED=true
```

**Production** (`.env.production`):
```bash
DATABASE_URL="postgresql://admin:${DB_PASSWORD}@postgres:5432/crm_platform"
NEXTAUTH_URL="https://your-domain.com"
NEXTAUTH_SECRET="${NEXTAUTH_SECRET}"
AUTO_MIGRATE=true
AUTO_SEED=false
```

### Service Configuration

| Service | Development | Staging | Production |
|---------|-------------|---------|------------|
| App Port | 3010 | 3010 | 80/443 |
| DB Port | 5434 | - | - |
| Redis Port | 6380 | - | - |
| pgAdmin | 8082 | 8082 | Optional |
| Resources | 2GB RAM | 4GB RAM | 8GB RAM |

## 🏗️ Architecture

### Container Dependencies
```
┌─────────────┐
│    nginx    │ (Production only)
└─────┬───────┘
      │
┌─────▼───────┐
│     app     │
└─────┬───────┘
      │
┌─────▼───────┐    ┌─────────────┐
│  postgres   │    │    redis    │
└─────────────┘    └─────────────┘
      │
┌─────▼───────┐
│   pgadmin   │ (Optional)
└─────────────┘
```

### Startup Sequence
1. **Infrastructure**: PostgreSQL, Redis start with health checks
2. **Database**: Migrations run, Prisma client generated
3. **Application**: Next.js starts with authentication ready
4. **Verification**: Health checks confirm all services ready

## 🔍 Monitoring & Health Checks

### Application Health
- **Endpoint**: `/api/health`
- **Checks**: Database, Redis, Application status
- **Interval**: 30s
- **Timeout**: 10s

### Container Health
```bash
# Check all services
docker compose ps

# View health status
docker compose ps --filter "health=healthy"

# Monitor logs
docker compose logs -f app
```

## 🛠️ Troubleshooting

### Common Issues

**Prisma Client Not Generated**:
```bash
docker compose exec app npx prisma generate
```

**Database Connection Failed**:
```bash
docker compose exec app node scripts/fix-auth-issues.js
```

**Authentication Errors**:
- Check `NEXTAUTH_URL` matches your domain/port
- Verify database has users
- Run diagnostic script

### Diagnostic Commands
```bash
# Full system check
docker compose exec app node scripts/fix-auth-issues.js

# Database status
docker compose exec postgres pg_isready -U admin -d crm_platform

# Application logs
docker compose logs app --tail=50

# Container resources
docker stats
```

## 🚀 Deployment

### Development
```bash
scripts\restart-docker.bat development
```

### Staging
```bash
scripts\restart-docker.bat staging
```

### Production
```bash
scripts\restart-docker.bat production
```

## 📊 Performance Optimization

### Resource Allocation
- **Development**: 2 CPU, 2GB RAM
- **Staging**: 2 CPU, 4GB RAM  
- **Production**: 4 CPU, 8GB RAM

### Caching Strategy
- **Redis**: Session and application cache
- **Docker**: Multi-stage builds with layer caching
- **Next.js**: Static generation and ISR

### Security Measures
- **Non-root users**: All containers run as non-root
- **Network isolation**: Internal Docker networks
- **Resource limits**: CPU and memory constraints
- **Security options**: No new privileges, seccomp profiles

## 🔄 Maintenance

### Regular Tasks
```bash
# Update containers
docker compose pull && docker compose up -d

# Clean up unused resources
docker system prune

# Backup database
docker compose exec postgres pg_dump -U admin crm_platform > backup.sql

# View resource usage
docker stats
```

### Scaling
- **Horizontal**: Multiple app containers behind load balancer
- **Vertical**: Increase resource limits in compose files
- **Database**: Read replicas, connection pooling

## 📝 Best Practices

1. **Always use startup scripts** for consistent deployment
2. **Monitor health checks** before considering deployment complete
3. **Use environment-specific configurations** 
4. **Implement proper logging** and monitoring
5. **Regular backups** of database and volumes
6. **Keep images updated** for security patches
7. **Use secrets management** for production credentials

## 🎉 Benefits

- **Zero Manual Setup**: Everything automated from container start
- **Environment Parity**: Consistent behavior across dev/staging/prod
- **Production Ready**: Security, monitoring, and performance optimized
- **Developer Friendly**: Easy debugging and development workflow
- **Scalable**: Ready for horizontal and vertical scaling
- **Maintainable**: Clear structure and comprehensive documentation
