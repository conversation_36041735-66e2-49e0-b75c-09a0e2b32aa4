/**
 * Utility functions for consistent date handling between frontend and backend
 */

/**
 * Converts a date value to a string format suitable for API submission
 * Handles Date objects, date strings, and undefined values
 * 
 * @param dateValue - The date value to convert (Date, string, or undefined)
 * @returns A string in YYYY-MM-DD format, ISO string, or undefined
 */
export function convertDateForAPI(dateValue: Date | string | undefined | null): string | undefined {
  if (!dateValue) return undefined;
  
  // If it's already a string, return as-is (assuming it's in correct format)
  if (typeof dateValue === 'string') {
    return dateValue;
  }
  
  // If it's a Date object, convert to ISO string
  if (dateValue instanceof Date) {
    return dateValue.toISOString();
  }
  
  return undefined;
}

/**
 * Converts form data dates to API-compatible format
 * This ensures all date fields are strings before sending to the API
 * 
 * @param formData - The form data object
 * @param dateFields - Array of field names that contain dates
 * @returns Form data with converted date fields
 */
export function convertFormDatesForAPI<T extends Record<string, any>>(
  formData: T,
  dateFields: (keyof T)[]
): T {
  const converted = { ...formData };
  
  dateFields.forEach(field => {
    if (converted[field] !== undefined) {
      converted[field] = convertDateForAPI(converted[field]) as T[keyof T];
    }
  });
  
  return converted;
}

/**
 * Converts a date string from HTML date input (YYYY-MM-DD) to ISO string
 * This is useful when you need to send a specific time along with the date
 * 
 * @param dateString - Date string in YYYY-MM-DD format
 * @param time - Optional time string in HH:MM format (defaults to 00:00)
 * @returns ISO string or undefined
 */
export function convertDateStringToISO(
  dateString: string | undefined,
  time: string = '00:00'
): string | undefined {
  if (!dateString) return undefined;
  
  // If it's already an ISO string, return as-is
  if (dateString.includes('T')) {
    return dateString;
  }
  
  // Convert YYYY-MM-DD to ISO string
  try {
    const date = new Date(`${dateString}T${time}:00.000Z`);
    return date.toISOString();
  } catch (error) {
    console.warn('Invalid date string:', dateString);
    return undefined;
  }
}

/**
 * Prepares project data for API submission
 * Ensures all date fields are properly formatted as strings
 * 
 * @param projectData - The project data from the form
 * @returns Project data with properly formatted dates
 */
export function prepareProjectDataForAPI(projectData: any) {
  return convertFormDatesForAPI(projectData, ['startDate', 'endDate']);
}

/**
 * Formats a date value for display in HTML date inputs
 * Converts Date objects or ISO strings to YYYY-MM-DD format
 * 
 * @param dateValue - The date value to format
 * @returns Date string in YYYY-MM-DD format or empty string
 */
export function formatDateForInput(dateValue: Date | string | undefined | null): string {
  if (!dateValue) return '';
  
  try {
    const date = typeof dateValue === 'string' ? new Date(dateValue) : dateValue;
    
    if (isNaN(date.getTime())) {
      return '';
    }
    
    // Format as YYYY-MM-DD for HTML date inputs
    return date.toISOString().split('T')[0];
  } catch (error) {
    console.warn('Error formatting date for input:', dateValue);
    return '';
  }
}
