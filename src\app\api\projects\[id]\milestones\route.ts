import { NextRequest, NextResponse } from 'next/server';
import { withPermission, AuthenticatedRequest } from '@/lib/permission-middleware';
import { PermissionResource, PermissionAction } from '@/types';
import { MilestoneService } from '@/services/milestone-management';
import { createMilestoneSchema } from '@/services/milestone-management';

const milestoneService = new MilestoneService();

interface RouteParams {
  params: Promise<{
    id: string;
  }>;
}

/**
 * GET /api/projects/[id]/milestones
 * Get all milestones for a project
 */
export const GET = withPermission({
  resource: PermissionResource.PROJECTS,
  action: PermissionAction.READ,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest, context: RouteParams) => {
  try {
    const { id: projectId } = await context.params;
    const { searchParams } = new URL(req.url);
    
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const status = searchParams.get('status') || undefined;
    const search = searchParams.get('search') || undefined;

    const result = await milestoneService.getProjectMilestones(
      projectId,
      req.tenantId,
      {
        page,
        limit,
        status,
        search,
      }
    );

    return NextResponse.json({
      success: true,
      data: result,
      message: 'Milestones retrieved successfully'
    });
  } catch (error) {
    console.error('Error fetching milestones:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch milestones'
      },
      { status: 500 }
    );
  }
});

/**
 * POST /api/projects/[id]/milestones
 * Create a new milestone for a project
 */
export const POST = withPermission({
  resource: PermissionResource.PROJECTS,
  action: PermissionAction.UPDATE,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest, context: RouteParams) => {
  try {
    const { id: projectId } = await context.params;
    const body = await req.json();
    const validatedData = createMilestoneSchema.parse(body);

    const milestone = await milestoneService.createMilestone(
      projectId,
      req.tenantId,
      validatedData,
      req.userId
    );

    return NextResponse.json({
      success: true,
      data: milestone,
      message: 'Milestone created successfully'
    }, { status: 201 });
  } catch (error) {
    console.error('Error creating milestone:', error);
    
    if (error instanceof Error && error.message.includes('not found')) {
      return NextResponse.json(
        {
          success: false,
          error: 'Project not found'
        },
        { status: 404 }
      );
    }

    return NextResponse.json(
      {
        success: false,
        error: 'Failed to create milestone'
      },
      { status: 500 }
    );
  }
});
