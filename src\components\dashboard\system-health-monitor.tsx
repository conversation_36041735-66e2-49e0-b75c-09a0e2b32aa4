'use client';

import React, { useState, useEffect } from 'react';
import {
  CpuChipIcon,
  CircleStackIcon,
  ClockIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  SignalIcon
} from '@heroicons/react/24/outline';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { SystemHealthMetrics } from '@/services/super-admin';
import { cn } from '@/lib/utils';

interface SystemHealthMonitorProps {
  className?: string;
  autoRefresh?: boolean;
  refreshInterval?: number;
}

interface HealthMetric {
  label: string;
  value: number;
  unit: string;
  icon: React.ComponentType<{ className?: string }>;
  threshold: { warning: number; critical: number };
  format?: 'percentage' | 'number' | 'time';
}

export function SystemHealthMonitor({
  className,
  autoRefresh = true,
  refreshInterval = 10000 // 10 seconds
}: SystemHealthMonitorProps) {
  const [metrics, setMetrics] = useState<SystemHealthMetrics | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchMetrics = async () => {
    try {
      setError(null);
      const response = await fetch('/api/admin/platform/analytics/system-health');

      if (!response.ok) {
        throw new Error('Failed to fetch system health metrics');
      }

      const data = await response.json();
      // The API returns the metrics under data.current
      setMetrics(data.data?.current || null);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchMetrics();

    if (autoRefresh) {
      const interval = setInterval(fetchMetrics, refreshInterval);
      return () => clearInterval(interval);
    }
  }, [autoRefresh, refreshInterval]);

  const getHealthMetrics = (data: SystemHealthMetrics): HealthMetric[] => [
    {
      label: 'CPU Usage',
      value: data.cpuUsage ?? 0,
      unit: '%',
      icon: CpuChipIcon,
      threshold: { warning: 70, critical: 90 },
      format: 'percentage'
    },
    {
      label: 'Memory Usage',
      value: data.memoryUsage ?? 0,
      unit: '%',
      icon: CircleStackIcon,
      threshold: { warning: 80, critical: 95 },
      format: 'percentage'
    },
    {
      label: 'Response Time',
      value: data.responseTime ?? 0,
      unit: 'ms',
      icon: ClockIcon,
      threshold: { warning: 200, critical: 500 },
      format: 'time'
    },
    {
      label: 'Error Rate',
      value: data.errorRate ?? 0,
      unit: '%',
      icon: ExclamationTriangleIcon,
      threshold: { warning: 1, critical: 5 },
      format: 'percentage'
    },
    {
      label: 'DB Connections',
      value: data.databaseConnections ?? 0,
      unit: '',
      icon: CircleStackIcon,
      threshold: { warning: 80, critical: 95 },
      format: 'number'
    },
    {
      label: 'Active Users',
      value: data.activeConnections ?? 0,
      unit: '',
      icon: SignalIcon,
      threshold: { warning: 1000, critical: 2000 },
      format: 'number'
    }
  ];

  const getStatusColor = (value: number, threshold: { warning: number; critical: number }) => {
    if (value >= threshold.critical) return 'critical';
    if (value >= threshold.warning) return 'warning';
    return 'healthy';
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'critical':
        return (
          <Badge className="bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400">
            <ExclamationTriangleIcon className="h-3 w-3 mr-1" />
            Critical
          </Badge>
        );
      case 'warning':
        return (
          <Badge className="bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400">
            <ExclamationTriangleIcon className="h-3 w-3 mr-1" />
            Warning
          </Badge>
        );
      default:
        return (
          <Badge className="bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400">
            <CheckCircleIcon className="h-3 w-3 mr-1" />
            Healthy
          </Badge>
        );
    }
  };

  const formatValue = (value: number, format?: string) => {
    // Handle undefined or null values
    if (value === undefined || value === null || isNaN(value)) {
      return 'N/A';
    }

    switch (format) {
      case 'percentage':
        return `${value.toFixed(1)}%`;
      case 'time':
        return `${Math.round(value)}ms`;
      case 'number':
        return value.toString();
      default:
        return value.toString();
    }
  };

  const getProgressColor = (status: string) => {
    switch (status) {
      case 'critical':
        return 'bg-red-500';
      case 'warning':
        return 'bg-yellow-500';
      default:
        return 'bg-green-500';
    }
  };

  if (loading) {
    return (
      <Card className={cn('bg-white dark:bg-gray-800 rounded-lg shadow-soft', className)}>
        <CardHeader>
          <CardTitle className="text-base font-semibold">System Health</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {[...Array(6)].map((_, i) => (
              <div key={i} className="space-y-2">
                <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
                <div className="h-2 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error || !metrics) {
    return (
      <Card className={cn('bg-white dark:bg-gray-800 rounded-lg shadow-soft', className)}>
        <CardHeader>
          <CardTitle className="text-base font-semibold">System Health</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <ExclamationTriangleIcon className="h-8 w-8 text-red-500 mx-auto mb-2" />
            <p className="text-sm text-red-600">{error || 'Failed to load metrics'}</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const healthMetrics = getHealthMetrics(metrics);
  const overallStatus = healthMetrics.some(m => getStatusColor(m.value, m.threshold) === 'critical') 
    ? 'critical' 
    : healthMetrics.some(m => getStatusColor(m.value, m.threshold) === 'warning')
    ? 'warning'
    : 'healthy';

  return (
    <Card className={cn('bg-white dark:bg-gray-800 rounded-lg shadow-soft', className)}>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-base font-semibold">System Health</CardTitle>
        <div className="flex items-center space-x-2">
          {getStatusBadge(overallStatus)}
          <span className="text-xs text-muted-foreground">
            Updated: {metrics.lastUpdated ? new Date(metrics.lastUpdated).toLocaleTimeString() : 'Unknown'}
          </span>
        </div>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {healthMetrics.map((metric) => {
            const status = getStatusColor(metric.value, metric.threshold);
            const IconComponent = metric.icon;
            
            return (
              <div key={metric.label} className="space-y-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <IconComponent className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm font-medium">{metric.label}</span>
                  </div>
                  <span className="text-sm font-semibold">
                    {formatValue(metric.value, metric.format)}
                  </span>
                </div>
                
                {metric.format === 'percentage' && (
                  <Progress 
                    value={metric.value} 
                    className="h-2"
                    // Note: You might need to add custom styling for different colors
                  />
                )}
                
                <div className="flex justify-between text-xs text-muted-foreground">
                  <span>Warning: {metric.threshold.warning}{metric.unit}</span>
                  <span>Critical: {metric.threshold.critical}{metric.unit}</span>
                </div>
              </div>
            );
          })}
        </div>
      </CardContent>
    </Card>
  );
}
