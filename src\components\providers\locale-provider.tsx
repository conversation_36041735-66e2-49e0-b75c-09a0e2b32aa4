'use client';

import React, { createContext, useContext, useState, useEffect } from 'react';

type Locale = 'en' | 'ar';

interface LocaleContextType {
  locale: Locale;
  setLocale: (locale: Locale) => void;
  isRTL: boolean;
  sidebarSide: 'left' | 'right';
}

const LocaleContext = createContext<LocaleContextType | undefined>(undefined);

interface LocaleProviderProps {
  children: React.ReactNode;
}

export function LocaleProvider({ children }: LocaleProviderProps) {
  const [locale, setLocaleState] = useState<Locale>('en');

  useEffect(() => {
    // Load locale from localStorage or browser preference
    const savedLocale = localStorage.getItem('locale') as Locale;
    if (savedLocale && ['en', 'ar'].includes(savedLocale)) {
      setLocaleState(savedLocale);
    } else {
      // Detect browser language
      const browserLang = navigator.language.split('-')[0];
      if (browserLang === 'ar') {
        setLocaleState('ar');
      }
    }
  }, []);

  useEffect(() => {
    // Update document direction and language
    document.documentElement.lang = locale;
    document.documentElement.dir = locale === 'ar' ? 'rtl' : 'ltr';
    
    // Update body classes for font switching
    if (locale === 'ar') {
      document.body.classList.add('font-arabic');
      document.body.classList.remove('font-sans');
    } else {
      document.body.classList.add('font-sans');
      document.body.classList.remove('font-arabic');
    }
  }, [locale]);

  const setLocale = (newLocale: Locale) => {
    setLocaleState(newLocale);
    localStorage.setItem('locale', newLocale);
  };

  const value = {
    locale,
    setLocale,
    isRTL: locale === 'ar',
    sidebarSide: locale === 'ar' ? 'right' : 'left' as 'left' | 'right'
  };

  return (
    <LocaleContext.Provider value={value}>
      {children}
    </LocaleContext.Provider>
  );
}

export function useLocale() {
  const context = useContext(LocaleContext);
  if (context === undefined) {
    throw new Error('useLocale must be used within a LocaleProvider');
  }
  return context;
}

// Hook for getting translated messages
export function useMessages() {
  const { locale } = useLocale();
  const [messages, setMessages] = useState<Record<string, any>>({});

  useEffect(() => {
    const loadMessages = async () => {
      try {
        const messageModule = await import(`@/i18n/locales/${locale}.json`);
        setMessages(messageModule.default);
      } catch (error) {
        console.error(`Failed to load messages for locale ${locale}:`, error);
        // Fallback to English
        const fallbackModule = await import('@/i18n/locales/en.json');
        setMessages(fallbackModule.default);
      }
    };

    loadMessages();
  }, [locale]);

  return messages;
}

// Simple translation function
export function useTranslation() {
  const messages = useMessages();
  
  const t = (key: string, params?: Record<string, string | number>) => {
    const keys = key.split('.');
    let value = messages;
    
    for (const k of keys) {
      value = value?.[k];
      if (value === undefined) break;
    }
    
    if (typeof value !== 'string') {
      return key; // Return key if translation not found
    }
    
    // Simple parameter replacement
    if (params) {
      const stringValue = String(Array.isArray(value) ? value[0] : value);
      return Object.entries(params).reduce((str: string, [param, val]) => {
        return str.replace(`{${param}}`, String(val));
      }, stringValue);
    }

    return Array.isArray(value) ? value[0] : value;
  };
  
  return { t };
}
