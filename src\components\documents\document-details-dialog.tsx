'use client';

import React, { useState } from 'react';
import { DocumentWithRelations } from '@/services/document-management';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Download,
  Eye,
  FileText,
  Calendar,
  User,
  Tag,
  FileIcon,
  Image,
  Video,
  Music,
  Archive,
  Code,
  Sparkles,
  Loader2,
  CheckCircle,
  AlertCircle,
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { toast } from 'sonner';

// AI Analysis types
interface DocumentAnalysisResult {
  suggestedTitle: string;
  suggestedDescription: string;
  documentType: string;
  keyTopics: string[];
  summary: string;
  confidence: number;
  processingTime: number;
}

interface DocumentDetailsDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  document: DocumentWithRelations | null;
  onDownload?: (document: DocumentWithRelations) => void;
  onView?: (document: DocumentWithRelations) => void;
  aiAvailable?: boolean;
}

export function DocumentDetailsDialog({
  open,
  onOpenChange,
  document,
  onDownload,
  onView,
  aiAvailable = false,
}: DocumentDetailsDialogProps) {
  const [analyzing, setAnalyzing] = useState(false);
  const [aiAnalysis, setAiAnalysis] = useState<DocumentAnalysisResult | null>(null);
  const [selectedLanguage, setSelectedLanguage] = useState<'english' | 'arabic'>('english');

  if (!document) return null;

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getFileIcon = (mimeType: string, className?: string) => {
    const iconClass = cn('w-6 h-6', className);
    
    if (mimeType?.startsWith('image/')) {
      return <Image className={iconClass} />;
    } else if (mimeType?.startsWith('video/')) {
      return <Video className={iconClass} />;
    } else if (mimeType?.startsWith('audio/')) {
      return <Music className={iconClass} />;
    } else if (mimeType?.includes('pdf')) {
      return <FileText className={cn(iconClass, 'text-red-500')} />;
    } else if (mimeType?.includes('word') || mimeType?.includes('document')) {
      return <FileText className={cn(iconClass, 'text-blue-500')} />;
    } else if (mimeType?.includes('excel') || mimeType?.includes('spreadsheet')) {
      return <FileText className={cn(iconClass, 'text-green-500')} />;
    } else if (mimeType?.includes('powerpoint') || mimeType?.includes('presentation')) {
      return <FileText className={cn(iconClass, 'text-orange-500')} />;
    } else if (mimeType?.includes('zip') || mimeType?.includes('rar') || mimeType?.includes('archive')) {
      return <Archive className={iconClass} />;
    } else if (mimeType?.includes('text/') || mimeType?.includes('json') || mimeType?.includes('xml')) {
      return <Code className={iconClass} />;
    } else {
      return <FileIcon className={iconClass} />;
    }
  };

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    }).format(new Date(date));
  };

  // AI Analysis functionality
  const handleAnalyzeDocument = async () => {
    if (!aiAvailable || analyzing) return;

    setAnalyzing(true);
    setAiAnalysis(null);

    try {
      // Download the document file first
      const response = await fetch(`/api/documents/${document.id}/download`);
      if (!response.ok) {
        throw new Error('Failed to download document for analysis');
      }

      const blob = await response.blob();
      const file = new File([blob], document.name, {
        type: document.mimeType || 'application/octet-stream'
      });

      // Analyze the document
      const formData = new FormData();
      formData.append('file', file);
      formData.append('relatedToType', document.relatedToType || 'general');
      formData.append('relatedToId', document.relatedToId || 'system');
      formData.append('language', selectedLanguage);

      const analysisResponse = await fetch('/api/documents/analyze', {
        method: 'POST',
        body: formData,
      });

      const analysisData = await analysisResponse.json();

      if (analysisData.success) {
        setAiAnalysis(analysisData.data);
        toast.success('Document analyzed successfully');
      } else {
        throw new Error(analysisData.error || 'Analysis failed');
      }
    } catch (error) {
      console.error('Error analyzing document:', error);
      toast.error('Failed to analyze document');
    } finally {
      setAnalyzing(false);
    }
  };

  const tags = Array.isArray(document.tags)
    ? document.tags.filter((tag): tag is string => typeof tag === 'string')
    : [];

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] flex flex-col">
        <DialogHeader className="flex-shrink-0">
          <div className="flex items-start space-x-3">
            {getFileIcon(document.mimeType || '', 'w-8 h-8')}
            <div className="flex-1 min-w-0">
              <DialogTitle className="text-lg font-semibold text-gray-900 break-words">
                {document.name}
              </DialogTitle>
              <DialogDescription className="text-sm text-gray-500">
                {document.mimeType} • {document.fileSize ? formatFileSize(document.fileSize) : 'Unknown size'}
              </DialogDescription>
            </div>
          </div>
        </DialogHeader>

        <ScrollArea className="flex-1 pr-4 max-h-[calc(90vh-200px)]">
          <div className="space-y-6">
            {/* Description */}
            {document.description && (
              <div>
                <h3 className="text-sm font-medium text-gray-900 mb-2">Description</h3>
                <div className="text-sm text-gray-700 bg-gray-50 rounded-lg p-3 whitespace-pre-wrap">
                  {document.description}
                </div>
              </div>
            )}

            {/* Tags */}
            {tags.length > 0 && (
              <div>
                <h3 className="text-sm font-medium text-gray-900 mb-2 flex items-center">
                  <Tag className="w-4 h-4 mr-1" />
                  Tags
                </h3>
                <div className="flex flex-wrap gap-2">
                  {tags.map((tag, index) => (
                    <Badge key={index} variant="secondary" className="text-xs">
                      {tag}
                    </Badge>
                  ))}
                </div>
              </div>
            )}

            <Separator />

            {/* AI Analysis Section */}
            {aiAvailable && (
              <div>
                <div className="flex items-center justify-between mb-3">
                  <h3 className="text-sm font-medium text-gray-900 flex items-center">
                    <Sparkles className="w-4 h-4 mr-2 text-blue-500" />
                    AI Document Analysis
                  </h3>
                  <div className="flex items-center space-x-2">
                    <Select value={selectedLanguage} onValueChange={(value) => setSelectedLanguage(value as 'english' | 'arabic')}>
                      <SelectTrigger className="w-32">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="english">English</SelectItem>
                        <SelectItem value="arabic">العربية</SelectItem>
                      </SelectContent>
                    </Select>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleAnalyzeDocument}
                      disabled={analyzing}
                      className="flex items-center space-x-2"
                    >
                      {analyzing ? (
                        <>
                          <Loader2 className="w-4 h-4 animate-spin" />
                          <span>Analyzing...</span>
                        </>
                      ) : (
                        <>
                          <Sparkles className="w-4 h-4" />
                          <span>Analyze Document</span>
                        </>
                      )}
                    </Button>
                  </div>
                </div>

                {analyzing && (
                  <Card className="bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200">
                    <CardContent className="p-4">
                      <div className="flex items-center space-x-3">
                        <div className="w-6 h-6 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
                        <div>
                          <p className="text-sm font-medium text-gray-900">Analyzing document content...</p>
                          <p className="text-xs text-gray-600">This may take a few moments</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                )}

                {aiAnalysis && (
                  <Card className="bg-gradient-to-r from-green-50 to-blue-50 border-green-200">
                    <CardHeader className="pb-3">
                      <CardTitle className="text-sm flex items-center text-green-800">
                        <CheckCircle className="w-4 h-4 mr-2" />
                        Document Summary
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="space-y-2">
                        <div className="p-4 bg-white rounded-lg border text-sm leading-relaxed"
                             style={{ direction: selectedLanguage === 'arabic' ? 'rtl' : 'ltr' }}>
                          {aiAnalysis.summary}
                        </div>
                      </div>

                      <div className="flex items-center justify-between pt-2 border-t">
                        <div className="text-xs text-gray-500 flex items-center space-x-4">
                          <span className="flex items-center">
                            <AlertCircle className="w-3 h-3 mr-1" />
                            Confidence: {aiAnalysis.confidence}%
                          </span>
                          <span>
                            Processed in {Math.round(aiAnalysis.processingTime / 1000)}s
                          </span>
                          <span>
                            Language: {selectedLanguage === 'arabic' ? 'العربية' : 'English'}
                          </span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                )}
              </div>
            )}

            <Separator />

            {/* File Information */}
            <div>
              <h3 className="text-sm font-medium text-gray-900 mb-3">File Information</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div className="flex items-center space-x-2">
                  <FileText className="w-4 h-4 text-gray-400" />
                  <span className="text-gray-500">Type:</span>
                  <span className="text-gray-900">{document.mimeType || 'Unknown'}</span>
                </div>
                
                {document.fileSize && (
                  <div className="flex items-center space-x-2">
                    <FileIcon className="w-4 h-4 text-gray-400" />
                    <span className="text-gray-500">Size:</span>
                    <span className="text-gray-900">{formatFileSize(document.fileSize)}</span>
                  </div>
                )}

                <div className="flex items-center space-x-2">
                  <Calendar className="w-4 h-4 text-gray-400" />
                  <span className="text-gray-500">Uploaded:</span>
                  <span className="text-gray-900">{formatDate(document.createdAt)}</span>
                </div>

                {document.uploadedBy && (
                  <div className="flex items-center space-x-2">
                    <User className="w-4 h-4 text-gray-400" />
                    <span className="text-gray-500">Uploaded by:</span>
                    <span className="text-gray-900">
                      {document.uploadedBy.firstName && document.uploadedBy.lastName
                        ? `${document.uploadedBy.firstName} ${document.uploadedBy.lastName}`
                        : document.uploadedBy.email}
                    </span>
                  </div>
                )}
              </div>
            </div>
          </div>
        </ScrollArea>

        {/* Actions */}
        <div className="flex justify-end space-x-2 pt-4 border-t flex-shrink-0">
          {onView && (
            <Button
              variant="outline"
              onClick={() => onView(document)}
              className="flex items-center space-x-2"
            >
              <Eye className="w-4 h-4" />
              <span>Open File</span>
            </Button>
          )}

          {onDownload && (
            <Button
              onClick={() => onDownload(document)}
              className="flex items-center space-x-2"
            >
              <Download className="w-4 h-4" />
              <span>Download</span>
            </Button>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
