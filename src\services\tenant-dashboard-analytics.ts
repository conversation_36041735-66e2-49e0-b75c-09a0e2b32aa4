import { prisma } from '@/lib/prisma';

export interface TenantDashboardMetrics {
  // Core Metrics
  totalContacts: number;
  totalLeads: number;
  totalOpportunities: number;
  totalCompanies: number;
  
  // Growth Metrics
  contactsGrowth: number;
  leadsGrowth: number;
  opportunitiesGrowth: number;
  companiesGrowth: number;
  
  // Performance Metrics
  leadConversionRate: number;
  opportunityWinRate: number;
  averageDealSize: number;
  totalRevenue: number;
  
  // Activity Metrics
  recentActivities: Array<{
    id: string;
    type: 'lead' | 'opportunity' | 'contact' | 'company';
    action: string;
    description: string;
    timestamp: Date;
    userId?: string;
    userName?: string;
  }>;
  
  // Usage Metrics
  usage: {
    contacts: number;
    leads: number;
    users: number;
    ai_requests: number;
    storage_mb: number;
  };
  
  // Trend Data
  trends: {
    leads: Array<{ date: string; count: number; }>;
    opportunities: Array<{ date: string; count: number; value: number; }>;
    contacts: Array<{ date: string; count: number; }>;
  };
}

export interface TenantQuickStats {
  totalRevenue: number;
  newCustomers: number;
  activeAccounts: number;
  growthRate: number;
  revenueGrowth: number;
  customerGrowth: number;
  accountGrowth: number;
}

export class TenantDashboardAnalyticsService {
  /**
   * Get comprehensive dashboard metrics for a tenant
   */
  async getTenantDashboardMetrics(tenantId: string): Promise<TenantDashboardMetrics> {
    const [
      coreMetrics,
      growthMetrics,
      performanceMetrics,
      recentActivities,
      usage,
      trends
    ] = await Promise.all([
      this.getCoreMetrics(tenantId),
      this.getGrowthMetrics(tenantId),
      this.getPerformanceMetrics(tenantId),
      this.getRecentActivities(tenantId),
      this.getUsageMetrics(tenantId),
      this.getTrendData(tenantId)
    ]);

    return {
      ...coreMetrics,
      ...growthMetrics,
      ...performanceMetrics,
      recentActivities,
      usage,
      trends
    };
  }

  /**
   * Get quick stats for section cards
   */
  async getTenantQuickStats(tenantId: string): Promise<TenantQuickStats> {
    const [
      totalRevenue,
      customerMetrics,
      growthMetrics,
      revenueGrowth
    ] = await Promise.all([
      this.calculateTotalRevenue(tenantId),
      this.getCustomerMetrics(tenantId),
      this.getGrowthMetrics(tenantId),
      this.getRevenueGrowth(tenantId)
    ]);

    return {
      totalRevenue,
      newCustomers: customerMetrics.newCustomers,
      activeAccounts: customerMetrics.activeAccounts,
      growthRate: growthMetrics.opportunitiesGrowth,
      revenueGrowth: revenueGrowth,
      customerGrowth: growthMetrics.contactsGrowth,
      accountGrowth: growthMetrics.companiesGrowth
    };
  }

  /**
   * Get core metrics (counts) - OPTIMIZED with single aggregation query
   */
  private async getCoreMetrics(tenantId: string) {
    // Use single optimized query instead of multiple COUNT operations
    const metrics = await prisma.$queryRaw<Array<{
      total_contacts: bigint;
      total_leads: bigint;
      total_opportunities: bigint;
      total_companies: bigint;
    }>>`
      SELECT
        COUNT(DISTINCT c.id) as total_contacts,
        COUNT(DISTINCT l.id) FILTER (WHERE l.deleted_at IS NULL) as total_leads,
        COUNT(DISTINCT o.id) as total_opportunities,
        COUNT(DISTINCT comp.id) as total_companies
      FROM tenants t
      LEFT JOIN contacts c ON t.id = c.tenant_id
      LEFT JOIN leads l ON t.id = l.tenant_id
      LEFT JOIN opportunities o ON t.id = o.tenant_id
      LEFT JOIN companies comp ON t.id = comp.tenant_id
      WHERE t.id = ${tenantId}
    `;

    const result = metrics[0];
    return {
      totalContacts: Number(result.total_contacts),
      totalLeads: Number(result.total_leads),
      totalOpportunities: Number(result.total_opportunities),
      totalCompanies: Number(result.total_companies)
    };
  }

  /**
   * Get growth metrics - OPTIMIZED with single aggregation query
   */
  private async getGrowthMetrics(tenantId: string) {
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    const sixtyDaysAgo = new Date(thirtyDaysAgo.getTime() - 30 * 24 * 60 * 60 * 1000);

    // Use single optimized query for all growth metrics
    const metrics = await prisma.$queryRaw<Array<{
      contacts_this_month: bigint;
      contacts_last_month: bigint;
      leads_this_month: bigint;
      leads_last_month: bigint;
      opportunities_this_month: bigint;
      opportunities_last_month: bigint;
      companies_this_month: bigint;
      companies_last_month: bigint;
    }>>`
      SELECT
        COUNT(DISTINCT c.id) FILTER (WHERE c.created_at >= ${thirtyDaysAgo}) as contacts_this_month,
        COUNT(DISTINCT c.id) FILTER (WHERE c.created_at >= ${sixtyDaysAgo} AND c.created_at < ${thirtyDaysAgo}) as contacts_last_month,
        COUNT(DISTINCT l.id) FILTER (WHERE l.created_at >= ${thirtyDaysAgo} AND l.deleted_at IS NULL) as leads_this_month,
        COUNT(DISTINCT l.id) FILTER (WHERE l.created_at >= ${sixtyDaysAgo} AND l.created_at < ${thirtyDaysAgo} AND l.deleted_at IS NULL) as leads_last_month,
        COUNT(DISTINCT o.id) FILTER (WHERE o.created_at >= ${thirtyDaysAgo}) as opportunities_this_month,
        COUNT(DISTINCT o.id) FILTER (WHERE o.created_at >= ${sixtyDaysAgo} AND o.created_at < ${thirtyDaysAgo}) as opportunities_last_month,
        COUNT(DISTINCT comp.id) FILTER (WHERE comp.created_at >= ${thirtyDaysAgo}) as companies_this_month,
        COUNT(DISTINCT comp.id) FILTER (WHERE comp.created_at >= ${sixtyDaysAgo} AND comp.created_at < ${thirtyDaysAgo}) as companies_last_month
      FROM tenants t
      LEFT JOIN contacts c ON t.id = c.tenant_id
      LEFT JOIN leads l ON t.id = l.tenant_id
      LEFT JOIN opportunities o ON t.id = o.tenant_id
      LEFT JOIN companies comp ON t.id = comp.tenant_id
      WHERE t.id = ${tenantId}
    `;

    const result = metrics[0];
    const contactsThisMonth = Number(result.contacts_this_month);
    const contactsLastMonth = Number(result.contacts_last_month);
    const leadsThisMonth = Number(result.leads_this_month);
    const leadsLastMonth = Number(result.leads_last_month);
    const opportunitiesThisMonth = Number(result.opportunities_this_month);
    const opportunitiesLastMonth = Number(result.opportunities_last_month);
    const companiesThisMonth = Number(result.companies_this_month);
    const companiesLastMonth = Number(result.companies_last_month);

    const calculateGrowth = (current: number, previous: number) => {
      if (previous === 0) return current > 0 ? 100 : 0;
      return ((current - previous) / previous) * 100;
    };

    return {
      contactsGrowth: calculateGrowth(contactsThisMonth, contactsLastMonth),
      leadsGrowth: calculateGrowth(leadsThisMonth, leadsLastMonth),
      opportunitiesGrowth: calculateGrowth(opportunitiesThisMonth, opportunitiesLastMonth),
      companiesGrowth: calculateGrowth(companiesThisMonth, companiesLastMonth)
    };
  }

  private async getPerformanceMetrics(tenantId: string) {
    const [
      leadStats,
      opportunityStats,
      revenueStats
    ] = await Promise.all([
      this.getLeadConversionRate(tenantId),
      this.getOpportunityWinRate(tenantId),
      this.getRevenueMetrics(tenantId)
    ]);

    return {
      leadConversionRate: leadStats.conversionRate,
      opportunityWinRate: opportunityStats.winRate,
      averageDealSize: opportunityStats.averageDealSize,
      totalRevenue: revenueStats.totalRevenue
    };
  }

  /**
   * Get lead conversion rate - OPTIMIZED with single query
   */
  private async getLeadConversionRate(tenantId: string) {
    const result = await prisma.$queryRaw<Array<{
      total_leads: bigint;
      converted_leads: bigint;
    }>>`
      SELECT
        COUNT(*) as total_leads,
        COUNT(*) FILTER (WHERE is_converted = true) as converted_leads
      FROM leads
      WHERE tenant_id = ${tenantId} AND deleted_at IS NULL
    `;

    const { total_leads, converted_leads } = result[0];
    const totalLeads = Number(total_leads);
    const convertedLeads = Number(converted_leads);

    return {
      conversionRate: totalLeads > 0 ? (convertedLeads / totalLeads) * 100 : 0
    };
  }

  private async getOpportunityWinRate(tenantId: string) {
    const [totalOpportunities, wonOpportunities, avgDealSize] = await Promise.all([
      prisma.opportunity.count({ where: { tenantId } }),
      prisma.opportunity.count({ where: { tenantId, stage: 'closed_won' } }),
      prisma.opportunity.aggregate({
        where: { tenantId, stage: 'closed_won' },
        _avg: { value: true }
      })
    ]);

    return {
      winRate: totalOpportunities > 0 ? (wonOpportunities / totalOpportunities) * 100 : 0,
      averageDealSize: avgDealSize._avg.value || 0
    };
  }

  private async getRevenueMetrics(tenantId: string) {
    const totalRevenue = await prisma.opportunity.aggregate({
      where: { tenantId, stage: 'closed_won' },
      _sum: { value: true }
    });

    return {
      totalRevenue: totalRevenue._sum.value || 0
    };
  }

  private async getRevenueGrowth(tenantId: string): Promise<number> {
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const [revenueThisMonth, revenueLastMonth] = await Promise.all([
      prisma.opportunity.aggregate({
        where: {
          tenantId,
          stage: 'closed_won',
          updatedAt: { gte: thirtyDaysAgo }
        },
        _sum: { value: true }
      }),
      prisma.opportunity.aggregate({
        where: {
          tenantId,
          stage: 'closed_won',
          updatedAt: {
            gte: new Date(thirtyDaysAgo.getTime() - 30 * 24 * 60 * 60 * 1000),
            lt: thirtyDaysAgo
          }
        },
        _sum: { value: true }
      })
    ]);

    const currentRevenue = revenueThisMonth._sum.value || 0;
    const previousRevenue = revenueLastMonth._sum.value || 0;

    if (previousRevenue === 0) return currentRevenue > 0 ? 100 : 0;
    return ((currentRevenue - previousRevenue) / previousRevenue) * 100;
  }

  private async calculateTotalRevenue(tenantId: string): Promise<number> {
    const result = await prisma.opportunity.aggregate({
      where: { tenantId, stage: 'closed_won' },
      _sum: { value: true }
    });

    return result._sum.value || 0;
  }

  private async getCustomerMetrics(tenantId: string) {
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const [newCustomers, activeAccounts] = await Promise.all([
      prisma.contact.count({
        where: { tenantId, createdAt: { gte: thirtyDaysAgo } }
      }),
      prisma.company.count({ where: { tenantId } })
    ]);

    return {
      newCustomers,
      activeAccounts
    };
  }

  private async getRecentActivities(tenantId: string, limit: number = 10) {
    // Get recent activities from audit logs or create from recent records
    const [recentLeads, recentOpportunities, recentContacts] = await Promise.all([
      prisma.lead.findMany({
        where: { tenantId, deletedAt: null },
        orderBy: { createdAt: 'desc' },
        take: 3,
        include: { contact: { select: { firstName: true, lastName: true } } }
      }),
      prisma.opportunity.findMany({
        where: { tenantId },
        orderBy: { updatedAt: 'desc' },
        take: 3,
        include: { contact: { select: { firstName: true, lastName: true } } }
      }),
      prisma.contact.findMany({
        where: { tenantId },
        orderBy: { createdAt: 'desc' },
        take: 4,
        select: { id: true, firstName: true, lastName: true, createdAt: true }
      })
    ]);

    const activities = [
      ...recentLeads.map(lead => ({
        id: lead.id,
        type: 'lead' as const,
        action: 'created',
        description: `New lead: ${lead.contact?.firstName} ${lead.contact?.lastName}`,
        timestamp: lead.createdAt
      })),
      ...recentOpportunities.map(opp => ({
        id: opp.id,
        type: 'opportunity' as const,
        action: 'updated',
        description: `Opportunity updated: ${opp.title}`,
        timestamp: opp.updatedAt
      })),
      ...recentContacts.map(contact => ({
        id: contact.id,
        type: 'contact' as const,
        action: 'created',
        description: `New contact: ${contact.firstName} ${contact.lastName}`,
        timestamp: contact.createdAt
      }))
    ];

    return activities
      .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
      .slice(0, limit);
  }

  private async getUsageMetrics(tenantId: string) {
    const currentUsage = await prisma.usageMetric.findMany({
      where: {
        tenantId,
        periodStart: {
          gte: new Date(new Date().getFullYear(), new Date().getMonth(), 1)
        }
      },
      select: {
        metricName: true,
        metricValue: true
      }
    });

    const usage = currentUsage.reduce((acc, metric) => {
      acc[metric.metricName] = metric.metricValue;
      return acc;
    }, {} as Record<string, number>);

    return {
      contacts: usage.contacts || 0,
      leads: usage.leads || 0,
      users: usage.users || 0,
      ai_requests: usage.ai_requests || 0,
      storage_mb: usage.storage_mb || 0
    };
  }

  private async getTrendData(tenantId: string, days: number = 30) {
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    // Generate daily trend data for the last 30 days
    const trends = {
      leads: [] as Array<{ date: string; count: number; }>,
      opportunities: [] as Array<{ date: string; count: number; value: number; }>,
      contacts: [] as Array<{ date: string; count: number; }>
    };

    for (let i = 0; i < days; i++) {
      const date = new Date(startDate);
      date.setDate(date.getDate() + i);
      const nextDate = new Date(date);
      nextDate.setDate(nextDate.getDate() + 1);

      const [leadCount, opportunityData, contactCount] = await Promise.all([
        prisma.lead.count({
          where: {
            tenantId,
            deletedAt: null,
            createdAt: { gte: date, lt: nextDate }
          }
        }),
        prisma.opportunity.aggregate({
          where: {
            tenantId,
            createdAt: { gte: date, lt: nextDate }
          },
          _count: { id: true },
          _sum: { value: true }
        }),
        prisma.contact.count({
          where: {
            tenantId,
            createdAt: { gte: date, lt: nextDate }
          }
        })
      ]);

      const dateStr = date.toISOString().split('T')[0];
      trends.leads.push({ date: dateStr, count: leadCount });
      trends.opportunities.push({
        date: dateStr,
        count: opportunityData._count.id,
        value: opportunityData._sum.value || 0
      });
      trends.contacts.push({ date: dateStr, count: contactCount });
    }

    return trends;
  }
}
