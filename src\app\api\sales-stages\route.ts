import { NextResponse } from 'next/server';
import { withPermission, AuthenticatedRequest } from '@/lib/permission-middleware';
import { PermissionResource, PermissionAction } from '@/types';
import { prisma } from '@/lib/prisma';

export const GET = withPermission({
  resource: PermissionResource.OPPORTUNITIES,
  action: PermissionAction.READ,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest) => {
  try {
    let salesStages = await prisma.salesStage.findMany({
      where: { tenantId: req.tenantId },
      orderBy: { orderIndex: 'asc' },
      select: {
        id: true,
        name: true,
        description: true,
        orderIndex: true,
        color: true,
        probabilityMin: true,
        probabilityMax: true,
        isClosed: true,
        isWon: true
      }
    });

    // If no stages exist, create default ones
    if (salesStages.length === 0) {
      console.log(`No sales stages found for tenant ${req.tenantId}, creating defaults...`);

      // Import default stages from seed script
      const { DEFAULT_SALES_STAGES } = require('../../../../scripts/seed-sales-stages.js');

      const stagesToCreate = DEFAULT_SALES_STAGES.map((stage: any) => ({
        ...stage,
        tenantId: req.tenantId
      }));

      await prisma.salesStage.createMany({
        data: stagesToCreate
      });

      // Fetch the newly created stages
      salesStages = await prisma.salesStage.findMany({
        where: { tenantId: req.tenantId },
        orderBy: { orderIndex: 'asc' },
        select: {
          id: true,
          name: true,
          description: true,
          orderIndex: true,
          color: true,
          probabilityMin: true,
          probabilityMax: true,
          isClosed: true,
          isWon: true
        }
      });

      console.log(`Created ${salesStages.length} default sales stages for tenant ${req.tenantId}`);
    }

    return NextResponse.json(salesStages);

  } catch (error) {
    console.error('Error fetching sales stages:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
});
