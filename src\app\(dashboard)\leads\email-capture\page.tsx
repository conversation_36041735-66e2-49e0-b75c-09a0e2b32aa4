'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  EnvelopeIcon,
  CheckCircleIcon,
  XCircleIcon,
  ClockIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';
import { formatDistanceToNow } from 'date-fns';
import { useToast } from '@/hooks/use-toast';
import { EmailLeadCapture } from '@/services/email-lead-capture';
import { PageLayout } from '@/components/layout/page-layout';
import { useRouter } from 'next/navigation';
import { PlusIcon } from '@heroicons/react/24/outline';

export default function EmailCapturePage() {
  const { data: session } = useSession();
  const router = useRouter();
  const [captures, setCaptures] = useState<EmailLeadCapture[]>([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState<'all' | 'processed' | 'unprocessed'>('all');
  const { toast } = useToast();

  const tenantId = session?.user?.tenants?.[0]?.id;

  useEffect(() => {
    if (tenantId) {
      fetchCaptures();
    }
  }, [tenantId, filter]);

  const fetchCaptures = async () => {
    try {
      const processedParam = filter === 'processed' ? 'true' : filter === 'unprocessed' ? 'false' : undefined;
      const url = `/api/leads/email-capture${processedParam ? `?processed=${processedParam}` : ''}`;

      const response = await fetch(url, {
        headers: {
          'x-tenant-id': tenantId!
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch email captures');
      }

      const data = await response.json();
      setCaptures(data.data || []);
    } catch (error) {
      console.error('Error fetching email captures:', error);
      toast({
        title: 'Error',
        description: 'Failed to fetch email captures',
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  };

  const getSentimentColor = (sentiment: string) => {
    switch (sentiment) {
      case 'positive': return 'bg-green-100 text-green-800';
      case 'negative': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getUrgencyColor = (urgency: string) => {
    switch (urgency) {
      case 'high': return 'bg-red-100 text-red-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-blue-100 text-blue-800';
    }
  };

  if (!tenantId) {
    return (
      <PageLayout>
        <div className="flex items-center justify-center h-64">
          <p className="text-muted-foreground">Loading...</p>
        </div>
      </PageLayout>
    );
  }

  if (loading) {
    return (
      <PageLayout
        title="Email Lead Capture"
        description="Monitor and manage leads captured from incoming emails"
      >
        <div className="grid gap-4">
          {[1, 2, 3].map((i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader>
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
              </CardHeader>
              <CardContent>
                <div className="h-20 bg-gray-200 rounded"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </PageLayout>
    );
  }

  const actions = (
    <div className="flex space-x-2">
      <Button
        onClick={() => router.push('/leads/email-capture/add')}
        className="flex items-center space-x-2"
      >
        <PlusIcon className="h-4 w-4" />
        <span>Add Email</span>
      </Button>
      <Button
        variant={filter === 'all' ? 'default' : 'outline'}
        onClick={() => setFilter('all')}
      >
        All
      </Button>
      <Button
        variant={filter === 'processed' ? 'default' : 'outline'}
        onClick={() => setFilter('processed')}
      >
        Processed
      </Button>
      <Button
        variant={filter === 'unprocessed' ? 'default' : 'outline'}
        onClick={() => setFilter('unprocessed')}
      >
        Unprocessed
      </Button>
    </div>
  );

  return (
    <PageLayout
      title="Email Lead Capture"
      description="Monitor and manage leads captured from incoming emails with AI-powered sentiment analysis"
      actions={actions}
    >

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Captures</CardTitle>
            <EnvelopeIcon className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{captures.length}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Processed</CardTitle>
            <CheckCircleIcon className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {captures.filter(c => c.processed).length}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Leads Created</CardTitle>
            <CheckCircleIcon className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {captures.filter(c => c.leadId).length}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Errors</CardTitle>
            <XCircleIcon className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {captures.filter(c => c.processingError).length}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Email Captures List */}
      <div className="space-y-4">
        {captures.length === 0 ? (
          <Card>
            <CardContent className="flex flex-col items-center justify-center py-12">
              <EnvelopeIcon className="h-12 w-12 text-muted-foreground mb-4" />
              <h3 className="text-lg font-semibold mb-2">No Email Captures</h3>
              <p className="text-muted-foreground text-center">
                No emails have been captured yet. Set up email forwarding to start capturing leads from emails.
              </p>
            </CardContent>
          </Card>
        ) : (
          captures.map((capture) => (
            <Card key={capture.id}>
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="space-y-1">
                    <CardTitle className="text-lg">{capture.subject}</CardTitle>
                    <CardDescription>
                      From: {capture.fromName ? `${capture.fromName} <${capture.fromEmail}>` : capture.fromEmail}
                    </CardDescription>
                  </div>
                  <div className="flex items-center space-x-2">
                    {capture.processed ? (
                      <Badge variant="default" className="bg-green-100 text-green-800">
                        <CheckCircleIcon className="h-3 w-3 mr-1" />
                        Processed
                      </Badge>
                    ) : capture.processingError ? (
                      <Badge variant="destructive">
                        <XCircleIcon className="h-3 w-3 mr-1" />
                        Error
                      </Badge>
                    ) : (
                      <Badge variant="secondary">
                        <ClockIcon className="h-3 w-3 mr-1" />
                        Pending
                      </Badge>
                    )}
                  </div>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="text-sm text-muted-foreground">
                  <p className="line-clamp-3">{capture.content}</p>
                </div>

                {capture.sentimentAnalysis && (
                  <div className="flex flex-wrap gap-2">
                    <Badge className={getSentimentColor(capture.sentimentAnalysis.sentiment)}>
                      Sentiment: {capture.sentimentAnalysis.sentiment}
                    </Badge>
                    <Badge className={getUrgencyColor(capture.sentimentAnalysis.urgency)}>
                      Urgency: {capture.sentimentAnalysis.urgency}
                    </Badge>
                    <Badge variant="outline">
                      Intent: {capture.sentimentAnalysis.intent}
                    </Badge>
                  </div>
                )}

                <div className="flex items-center justify-between text-sm text-muted-foreground">
                  <span>
                    Received {formatDistanceToNow(new Date(capture.receivedAt), { addSuffix: true })}
                  </span>
                  {capture.leadId && (
                    <span className="text-green-600">Lead created</span>
                  )}
                  {capture.processingError && (
                    <span className="text-red-600">
                      <ExclamationTriangleIcon className="h-4 w-4 inline mr-1" />
                      Processing failed
                    </span>
                  )}
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>
    </PageLayout>
  );
}
