export interface WelcomeEmailData {
  firstName: string;
  lastName: string;
  email: string;
  temporaryPassword: string;
  organizationName: string;
  loginUrl: string;
  supportEmail?: string;
}

export interface PasswordResetEmailData {
  firstName: string;
  lastName: string;
  resetUrl: string;
  organizationName: string;
  expirationHours?: number;
}

export interface UserInviteEmailData {
  firstName: string;
  lastName: string;
  inviterName: string;
  organizationName: string;
  inviteUrl: string;
  role: string;
}

export function generateWelcomeEmail(data: WelcomeEmailData): { subject: string; html: string; text: string } {
  const subject = `Welcome to ${data.organizationName}`;
  
  const html = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Welcome to ${data.organizationName}</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
        .content { background-color: #ffffff; padding: 20px; border: 1px solid #e9ecef; border-radius: 8px; }
        .credentials { background-color: #f8f9fa; padding: 15px; border-radius: 6px; margin: 20px 0; }
        .button { display: inline-block; background-color: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 20px 0; }
        .footer { margin-top: 30px; padding-top: 20px; border-top: 1px solid #e9ecef; font-size: 14px; color: #6c757d; }
        .warning { background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 6px; margin: 20px 0; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>Welcome to ${data.organizationName}!</h1>
        </div>
        
        <div class="content">
          <p>Hello ${data.firstName} ${data.lastName},</p>
          
          <p>Your account has been created successfully. We're excited to have you join our team!</p>
          
          <div class="credentials">
            <h3>Your Login Credentials</h3>
            <p><strong>Email:</strong> ${data.email}</p>
            <p><strong>Temporary Password:</strong> <code>${data.temporaryPassword}</code></p>
          </div>
          
          <div class="warning">
            <p><strong>Important:</strong> For security reasons, you'll be required to change your password on your first login.</p>
          </div>
          
          <p>To get started, click the button below to log in:</p>
          
          <a href="${data.loginUrl}" class="button">Log In to Your Account</a>
          
          <p>If the button doesn't work, you can copy and paste this link into your browser:</p>
          <p><a href="${data.loginUrl}">${data.loginUrl}</a></p>
          
          <h3>Next Steps:</h3>
          <ul>
            <li>Log in using your temporary credentials</li>
            <li>Set up a secure password</li>
            <li>Complete your profile information</li>
            <li>Explore the platform features</li>
          </ul>
          
          <p>If you have any questions or need assistance, please don't hesitate to reach out to our support team${data.supportEmail ? ` at ${data.supportEmail}` : ''}.</p>
          
          <p>Welcome aboard!</p>
          <p>The ${data.organizationName} Team</p>
        </div>
        
        <div class="footer">
          <p>This email was sent to ${data.email}. If you didn't expect this email, please contact our support team immediately.</p>
        </div>
      </div>
    </body>
    </html>
  `;

  const text = `
Welcome to ${data.organizationName}!

Hello ${data.firstName} ${data.lastName},

Your account has been created successfully. We're excited to have you join our team!

Your Login Credentials:
Email: ${data.email}
Temporary Password: ${data.temporaryPassword}

IMPORTANT: For security reasons, you'll be required to change your password on your first login.

To get started, visit: ${data.loginUrl}

Next Steps:
- Log in using your temporary credentials
- Set up a secure password
- Complete your profile information
- Explore the platform features

If you have any questions or need assistance, please don't hesitate to reach out to our support team${data.supportEmail ? ` at ${data.supportEmail}` : ''}.

Welcome aboard!
The ${data.organizationName} Team

---
This email was sent to ${data.email}. If you didn't expect this email, please contact our support team immediately.
  `;

  return { subject, html, text };
}

export function generatePasswordResetEmail(data: PasswordResetEmailData): { subject: string; html: string; text: string } {
  const subject = `Password Reset Request - ${data.organizationName}`;
  const expirationHours = data.expirationHours || 24;
  
  const html = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Password Reset Request</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
        .content { background-color: #ffffff; padding: 20px; border: 1px solid #e9ecef; border-radius: 8px; }
        .button { display: inline-block; background-color: #dc3545; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 20px 0; }
        .footer { margin-top: 30px; padding-top: 20px; border-top: 1px solid #e9ecef; font-size: 14px; color: #6c757d; }
        .warning { background-color: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 6px; margin: 20px 0; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>Password Reset Request</h1>
        </div>
        
        <div class="content">
          <p>Hello ${data.firstName} ${data.lastName},</p>
          
          <p>We received a request to reset your password for your ${data.organizationName} account.</p>
          
          <p>To reset your password, click the button below:</p>
          
          <a href="${data.resetUrl}" class="button">Reset Your Password</a>
          
          <p>If the button doesn't work, you can copy and paste this link into your browser:</p>
          <p><a href="${data.resetUrl}">${data.resetUrl}</a></p>
          
          <div class="warning">
            <p><strong>Important:</strong> This link will expire in ${expirationHours} hours for security reasons.</p>
          </div>
          
          <p>If you didn't request a password reset, please ignore this email. Your password will remain unchanged.</p>
          
          <p>For security reasons, if you continue to receive these emails without requesting them, please contact our support team immediately.</p>
        </div>
        
        <div class="footer">
          <p>This email was sent to your registered email address. If you have any concerns, please contact our support team.</p>
        </div>
      </div>
    </body>
    </html>
  `;

  const text = `
Password Reset Request - ${data.organizationName}

Hello ${data.firstName} ${data.lastName},

We received a request to reset your password for your ${data.organizationName} account.

To reset your password, visit: ${data.resetUrl}

IMPORTANT: This link will expire in ${expirationHours} hours for security reasons.

If you didn't request a password reset, please ignore this email. Your password will remain unchanged.

For security reasons, if you continue to receive these emails without requesting them, please contact our support team immediately.

---
This email was sent to your registered email address. If you have any concerns, please contact our support team.
  `;

  return { subject, html, text };
}

export function generateUserInviteEmail(data: UserInviteEmailData): { subject: string; html: string; text: string } {
  const subject = `You've been invited to join ${data.organizationName}`;
  
  const html = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Invitation to ${data.organizationName}</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
        .content { background-color: #ffffff; padding: 20px; border: 1px solid #e9ecef; border-radius: 8px; }
        .button { display: inline-block; background-color: #28a745; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 20px 0; }
        .footer { margin-top: 30px; padding-top: 20px; border-top: 1px solid #e9ecef; font-size: 14px; color: #6c757d; }
        .role-info { background-color: #e7f3ff; border: 1px solid #b3d9ff; padding: 15px; border-radius: 6px; margin: 20px 0; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>You're Invited to Join ${data.organizationName}!</h1>
        </div>
        
        <div class="content">
          <p>Hello ${data.firstName} ${data.lastName},</p>
          
          <p>${data.inviterName} has invited you to join ${data.organizationName}.</p>
          
          <div class="role-info">
            <p><strong>Your Role:</strong> ${data.role}</p>
          </div>
          
          <p>To accept this invitation and set up your account, click the button below:</p>
          
          <a href="${data.inviteUrl}" class="button">Accept Invitation</a>
          
          <p>If the button doesn't work, you can copy and paste this link into your browser:</p>
          <p><a href="${data.inviteUrl}">${data.inviteUrl}</a></p>
          
          <p>Once you accept the invitation, you'll be able to:</p>
          <ul>
            <li>Set up your account and password</li>
            <li>Access the platform features</li>
            <li>Collaborate with your team</li>
            <li>Get started with your new role</li>
          </ul>
          
          <p>If you have any questions about this invitation, please contact ${data.inviterName} or our support team.</p>
          
          <p>We look forward to having you on the team!</p>
          <p>The ${data.organizationName} Team</p>
        </div>
        
        <div class="footer">
          <p>This invitation was sent to your email address. If you weren't expecting this invitation, you can safely ignore this email.</p>
        </div>
      </div>
    </body>
    </html>
  `;

  const text = `
You're Invited to Join ${data.organizationName}!

Hello ${data.firstName} ${data.lastName},

${data.inviterName} has invited you to join ${data.organizationName}.

Your Role: ${data.role}

To accept this invitation and set up your account, visit: ${data.inviteUrl}

Once you accept the invitation, you'll be able to:
- Set up your account and password
- Access the platform features
- Collaborate with your team
- Get started with your new role

If you have any questions about this invitation, please contact ${data.inviterName} or our support team.

We look forward to having you on the team!
The ${data.organizationName} Team

---
This invitation was sent to your email address. If you weren't expecting this invitation, you can safely ignore this email.
  `;

  return { subject, html, text };
}
