{"openapi": "3.0.0", "info": {"title": "Multi-Tenant SaaS CRM API", "version": "1.0.0", "description": "Comprehensive API documentation for the multi-tenant SaaS CRM platform", "contact": {"name": "API Support", "email": "<EMAIL>"}}, "servers": [{"url": "http://localhost:3000", "description": "Development server"}, {"url": "https://api.crm-platform.com", "description": "Production server"}], "components": {"securitySchemes": {"bearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT"}, "tenantHeader": {"type": "<PERSON><PERSON><PERSON><PERSON>", "in": "header", "name": "x-tenant-id", "description": "Tenant ID for multi-tenant operations"}}, "schemas": {"Error": {"type": "object", "properties": {"error": {"type": "string", "description": "Error message"}, "details": {"type": "array", "items": {"type": "object", "properties": {"field": {"type": "string"}, "message": {"type": "string"}}}}}}, "ApiResponse": {"type": "object", "properties": {"success": {"type": "boolean", "description": "Indicates if the request was successful"}, "data": {"type": "object", "description": "Response data"}, "message": {"type": "string", "description": "Response message"}}}, "PaginatedResponse": {"allOf": [{"$ref": "#/components/schemas/ApiResponse"}, {"type": "object", "properties": {"pagination": {"type": "object", "properties": {"page": {"type": "integer"}, "limit": {"type": "integer"}, "total": {"type": "integer"}, "totalPages": {"type": "integer"}}}}}]}, "Tenant": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string"}, "slug": {"type": "string"}, "status": {"type": "string", "enum": ["active", "suspended", "cancelled"]}, "settings": {"type": "object"}, "branding": {"type": "object"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}}, "User": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "email": {"type": "string", "format": "email"}, "firstName": {"type": "string"}, "lastName": {"type": "string"}, "locale": {"type": "string", "enum": ["en", "ar"]}, "timezone": {"type": "string"}, "avatarUrl": {"type": "string", "format": "uri"}, "status": {"type": "string", "enum": ["active", "inactive", "pending"]}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}}, "Subscription": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "tenantId": {"type": "string", "format": "uuid"}, "planId": {"type": "string", "format": "uuid"}, "status": {"type": "string", "enum": ["active", "cancelled", "past_due", "trialing"]}, "currentPeriodStart": {"type": "string", "format": "date-time"}, "currentPeriodEnd": {"type": "string", "format": "date-time"}, "stripeSubscriptionId": {"type": "string"}, "stripeCustomerId": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}}, "SubscriptionPlan": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string"}, "description": {"type": "string"}, "priceMonthly": {"type": "number", "format": "decimal"}, "priceYearly": {"type": "number", "format": "decimal"}, "features": {"type": "object"}, "limits": {"type": "object"}, "isActive": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}}}}}, "security": [{"bearerAuth": [], "tenantHeader": []}], "paths": {"/api/admin/backup": {"post": {"summary": "Create database backup", "description": "Create full database backup or tenant-specific backup (Admin only)", "tags": ["Admin - Backup"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"type": {"type": "string", "enum": ["full", "tenant"], "description": "Type of backup to create"}, "tenantId": {"type": "string", "description": "Tenant ID (required for tenant backup)"}, "includeData": {"type": "boolean", "default": true, "description": "Whether to include data in backup"}, "compress": {"type": "boolean", "default": true, "description": "Whether to compress the backup file"}}}}}}, "responses": {"200": {"description": "Backup created successfully", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/ApiResponse"}, {"type": "object", "properties": {"data": {"type": "object", "properties": {"backupPath": {"type": "string"}, "size": {"type": "number"}, "type": {"type": "string"}}}}}]}}}}, "400": {"description": "Validation error"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden - Admin access required"}}}, "put": {"summary": "Restore database from backup", "description": "Restore database from backup file (Admin only)", "tags": ["Admin - Backup"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["backupFile"], "properties": {"backupFile": {"type": "string", "description": "Path to backup file"}, "targetDatabase": {"type": "string", "description": "Target database name (optional)"}, "dropExisting": {"type": "boolean", "default": false, "description": "Whether to drop existing database"}, "tenantMapping": {"type": "object", "description": "Map old tenant IDs to new ones"}}}}}}, "responses": {"200": {"description": "Database restored successfully"}, "400": {"description": "Validation error"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden - Admin access required"}}}}, "/api/admin/feature-flags/{id}": {"get": {"summary": "Get feature flag by ID (Super Admin only)", "description": "Retrieve a specific feature flag by its ID", "tags": ["Super Admin - Feature Flags"], "security": [{"bearerAuth": []}], "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "string"}, "description": "Feature flag ID"}], "responses": {"200": {"description": "Feature flag retrieved successfully"}, "404": {"description": "Feature flag not found"}}}, "put": {"summary": "Update feature flag (Super Admin only)", "description": "Update a specific feature flag", "tags": ["Super Admin - Feature Flags"], "security": [{"bearerAuth": []}], "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "string"}, "description": "Feature flag ID"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"name": {"type": "string"}, "description": {"type": "string"}, "isEnabled": {"type": "boolean"}, "requiredPlanLevel": {"type": "number"}}}}}}, "responses": {"200": {"description": "Feature flag updated successfully"}, "400": {"description": "Invalid input data"}, "404": {"description": "Feature flag not found"}}}, "delete": {"summary": "Delete feature flag (Super Admin only)", "description": "Delete a specific feature flag", "tags": ["Super Admin - Feature Flags"], "security": [{"bearerAuth": []}], "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "string"}, "description": "Feature flag ID"}], "responses": {"200": {"description": "Feature flag deleted successfully"}, "404": {"description": "Feature flag not found"}}}}, "/api/admin/feature-flags/{id}/toggle": {"post": {"summary": "Toggle feature flag status (Super Admin only)", "description": "Toggle the enabled/disabled status of a feature flag", "tags": ["Super Admin - Feature Flags"], "security": [{"bearerAuth": []}], "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "string"}, "description": "Feature flag ID"}], "responses": {"200": {"description": "Feature flag toggled successfully"}, "404": {"description": "Feature flag not found"}}}}, "/api/admin/feature-flags": {"get": {"summary": "Get all feature flags (Super Admin only)", "description": "Retrieve all feature flags with their current status", "tags": ["Super Admin - Feature Flags"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "Feature flags retrieved successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "description": {"type": "string"}, "isEnabled": {"type": "boolean"}, "requiredPlanLevel": {"type": "number"}, "createdAt": {"type": "string", "format": "date-time"}}}}}}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Super Admin access required"}}}, "post": {"summary": "Create new feature flag (Super Admin only)", "description": "Create a new feature flag for the platform", "tags": ["Super Admin - Feature Flags"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["name"], "properties": {"name": {"type": "string", "description": "Unique name for the feature flag"}, "description": {"type": "string", "description": "Description of the feature"}, "isEnabled": {"type": "boolean", "description": "Whether the feature is enabled", "default": true}, "requiredPlanLevel": {"type": "number", "description": "Minimum plan level required for this feature", "default": 1}}}}}}, "responses": {"201": {"description": "Feature flag created successfully"}, "400": {"description": "Invalid input data"}, "409": {"description": "Feature flag with this name already exists"}}}, "put": {"summary": "Bulk update feature flags (Super Admin only)", "description": "Update multiple feature flags at once", "tags": ["Super Admin - Feature Flags"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["updates"], "properties": {"updates": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "isEnabled": {"type": "boolean"}}}}}}}}}, "responses": {"200": {"description": "Feature flags updated successfully"}, "400": {"description": "Invalid input data"}}}}, "/api/admin/impersonation/active": {"get": {"summary": "Get active impersonation session (Super Admin only)", "description": "Get the currently active impersonation session for the super admin", "tags": ["Super Admin - Impersonation"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "Active impersonation session retrieved successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"type": "object", "nullable": true, "properties": {"id": {"type": "string"}, "superAdminId": {"type": "string"}, "tenantId": {"type": "string"}, "userId": {"type": "string"}, "startedAt": {"type": "string", "format": "date-time"}, "reason": {"type": "string"}, "superAdmin": {"type": "object"}, "tenant": {"type": "object"}, "user": {"type": "object"}}}}}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Super Admin access required"}}}, "delete": {"summary": "End active impersonation session (Super Admin only)", "description": "End the currently active impersonation session for the super admin", "tags": ["Super Admin - Impersonation"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "Active impersonation session ended successfully"}, "401": {"description": "Unauthorized"}, "403": {"description": "Super Admin access required"}, "404": {"description": "No active impersonation session found"}}}}, "/api/admin/impersonation": {"get": {"summary": "Get impersonation history (Super Admin only)", "description": "Retrieve impersonation session history with filtering options", "tags": ["Super Admin - Impersonation"], "security": [{"bearerAuth": []}], "parameters": [{"in": "query", "name": "page", "schema": {"type": "integer", "default": 1}, "description": "Page number"}, {"in": "query", "name": "limit", "schema": {"type": "integer", "default": 20}, "description": "Items per page"}, {"in": "query", "name": "superAdminId", "schema": {"type": "string"}, "description": "Filter by super admin ID"}, {"in": "query", "name": "tenantId", "schema": {"type": "string"}, "description": "Filter by tenant ID"}, {"in": "query", "name": "userId", "schema": {"type": "string"}, "description": "Filter by user ID"}], "responses": {"200": {"description": "Impersonation history retrieved successfully"}, "401": {"description": "Unauthorized"}, "403": {"description": "Super Admin access required"}}}, "post": {"summary": "Start impersonation session (Super Admin only)", "description": "Start impersonating a user in a specific tenant", "tags": ["Super Admin - Impersonation"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["tenantId", "userId"], "properties": {"tenantId": {"type": "string", "description": "ID of the tenant"}, "userId": {"type": "string", "description": "ID of the user to impersonate"}, "reason": {"type": "string", "description": "Reason for impersonation"}}}}}}, "responses": {"201": {"description": "Impersonation session started successfully"}, "400": {"description": "Invalid input data"}, "404": {"description": "Tenant or user not found"}}}, "delete": {"summary": "End impersonation session (Super Admin only)", "description": "End an active impersonation session", "tags": ["Super Admin - Impersonation"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["sessionId"], "properties": {"sessionId": {"type": "string", "description": "ID of the impersonation session to end"}}}}}}, "responses": {"200": {"description": "Impersonation session ended successfully"}, "400": {"description": "Invalid input data"}, "404": {"description": "Session not found"}}}}, "/api/admin/impersonation/tenants/{tenantId}/users": {"get": {"summary": "Get users available for impersonation in a tenant (Super Admin only)", "description": "Retrieve all users in a tenant that can be impersonated", "tags": ["Super Admin - Impersonation"], "security": [{"bearerAuth": []}], "parameters": [{"in": "path", "name": "tenantId", "required": true, "schema": {"type": "string"}, "description": "Tenant ID"}], "responses": {"200": {"description": "Users retrieved successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "email": {"type": "string"}, "firstName": {"type": "string"}, "lastName": {"type": "string"}, "role": {"type": "string"}, "isBeingImpersonated": {"type": "boolean"}}}}}}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Super Admin access required"}, "404": {"description": "Tenant not found"}}}}, "/api/admin/performance": {"get": {"summary": "Get system performance metrics (Super Admin only)", "description": "Returns comprehensive performance metrics including database, cache, API, and system health", "tags": ["Admin", "Performance"], "security": [{"bearerAuth": []}], "parameters": [{"in": "query", "name": "timeWindow", "schema": {"type": "string", "enum": ["5m", "15m", "1h", "6h", "24h"], "default": "15m"}, "description": "Time window for metrics aggregation"}, {"in": "query", "name": "detailed", "schema": {"type": "boolean", "default": false}, "description": "Include detailed metrics breakdown"}], "responses": {"200": {"description": "Performance metrics retrieved successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"type": "object", "properties": {"systemHealth": {"type": "object"}, "performanceSummary": {"type": "object"}, "databaseMetrics": {"type": "object"}, "cacheMetrics": {"type": "object"}}}}}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Super Admin access required"}}}, "post": {"summary": "Clear performance metrics or trigger cleanup (Super Admin only)", "description": "Allows clearing old metrics or triggering system cleanup operations", "tags": ["Admin", "Performance"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"action": {"type": "string", "enum": ["clear_metrics", "cleanup_cache", "force_gc"]}, "maxAge": {"type": "number", "description": "Maximum age in milliseconds for metrics to keep (for clear_metrics action)"}}}}}}, "responses": {"200": {"description": "Action completed successfully"}, "401": {"description": "Unauthorized"}, "403": {"description": "Super Admin access required"}}}}, "/api/admin/platform/analytics/activity": {"get": {"summary": "Get real-time activity feed (Super Admin only)", "description": "Retrieve recent platform activity and events", "tags": ["Super Admin - Analytics"], "security": [{"bearerAuth": []}], "parameters": [{"in": "query", "name": "limit", "schema": {"type": "integer", "default": 50}, "description": "Maximum number of activities to retrieve"}], "responses": {"200": {"description": "Activity feed retrieved successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "type": {"type": "string", "enum": ["user_registration", "tenant_created", "subscription_change", "system_alert", "error"]}, "title": {"type": "string"}, "description": {"type": "string"}, "timestamp": {"type": "string", "format": "date-time"}, "severity": {"type": "string", "enum": ["low", "medium", "high", "critical"]}, "tenantId": {"type": "string", "nullable": true}, "userId": {"type": "string", "nullable": true}, "metadata": {"type": "object"}}}}}}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Super Admin access required"}}}}, "/api/admin/platform/analytics/revenue": {"get": {"summary": "Get platform revenue analytics (Super Admin only)", "description": "Retrieve comprehensive revenue and billing analytics", "tags": ["Super Admin - Analytics"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "Revenue analytics retrieved successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"type": "object", "properties": {"totalRevenue": {"type": "number"}, "monthlyRecurringRevenue": {"type": "number"}, "averageRevenuePerUser": {"type": "number"}, "revenueGrowthRate": {"type": "number"}, "subscriptionBreakdown": {"type": "array", "items": {"type": "object", "properties": {"planName": {"type": "string"}, "count": {"type": "number"}, "revenue": {"type": "number"}, "percentage": {"type": "number"}}}}, "revenueByMonth": {"type": "array", "items": {"type": "object", "properties": {"month": {"type": "string"}, "revenue": {"type": "number"}, "subscriptions": {"type": "number"}}}}}}}}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Super Admin access required"}}}}, "/api/admin/platform/analytics": {"get": {"summary": "Get platform-wide analytics (Super Admin only)", "description": "Retrieve comprehensive analytics and metrics for the entire platform", "tags": ["Super Admin - Analytics"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "Platform analytics retrieved successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"type": "object", "properties": {"totalTenants": {"type": "number"}, "activeTenants": {"type": "number"}, "totalUsers": {"type": "number"}, "activeUsers": {"type": "number"}, "totalRevenue": {"type": "number"}, "monthlyGrowth": {"type": "number"}, "topTenantsByUsers": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "userCount": {"type": "number"}, "status": {"type": "string"}}}}, "recentActivity": {"type": "array", "items": {"type": "object", "properties": {"type": {"type": "string"}, "description": {"type": "string"}, "timestamp": {"type": "string", "format": "date-time"}, "tenantId": {"type": "string"}, "userId": {"type": "string"}}}}}}}}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Super Admin access required"}}}}, "/api/admin/platform/analytics/system-health": {"get": {"summary": "Get system health metrics (Super Admin only)", "description": "Retrieve real-time system performance and health metrics", "tags": ["Super Admin - Analytics"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "System health metrics retrieved successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"type": "object", "properties": {"uptime": {"type": "number", "description": "System uptime percentage"}, "responseTime": {"type": "number", "description": "Average response time in milliseconds"}, "errorRate": {"type": "number", "description": "Error rate percentage"}, "databaseConnections": {"type": "number", "description": "Number of active database connections"}, "memoryUsage": {"type": "number", "description": "Memory usage percentage"}, "cpuUsage": {"type": "number", "description": "CPU usage percentage"}, "activeConnections": {"type": "number", "description": "Number of active user connections"}, "queueSize": {"type": "number", "description": "Background job queue size"}, "lastUpdated": {"type": "string", "format": "date-time"}}}}}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Super Admin access required"}}}}, "/api/admin/platform/analytics/trends": {"get": {"summary": "Get platform trend data for charts (Super Admin only)", "description": "Retrieve time-series data for tenant and user growth trends", "tags": ["Super Admin - Analytics"], "security": [{"bearerAuth": []}], "parameters": [{"in": "query", "name": "days", "schema": {"type": "integer", "default": 30}, "description": "Number of days to retrieve trend data for"}], "responses": {"200": {"description": "Trend data retrieved successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"type": "array", "items": {"type": "object", "properties": {"date": {"type": "string", "format": "date"}, "tenants": {"type": "number"}, "users": {"type": "number"}, "revenue": {"type": "number"}}}}}}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Super Admin access required"}}}}, "/api/admin/platform/users": {"get": {"summary": "Get all users across all tenants (Super Admin only)", "description": "Retrieve a paginated list of all users in the platform", "tags": ["Super Admin - Platform Users"], "security": [{"bearerAuth": []}], "parameters": [{"in": "query", "name": "page", "schema": {"type": "integer", "default": 1}, "description": "Page number"}, {"in": "query", "name": "limit", "schema": {"type": "integer", "default": 20}, "description": "Number of items per page"}, {"in": "query", "name": "search", "schema": {"type": "string"}, "description": "Search by email, first name, or last name"}, {"in": "query", "name": "status", "schema": {"type": "string", "enum": ["active", "suspended", "inactive"]}, "description": "Filter by user status"}], "responses": {"200": {"description": "Users retrieved successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"type": "object", "properties": {"users": {"type": "array", "items": {"type": "object"}}, "pagination": {"type": "object"}}}}}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Super Admin access required"}}}, "put": {"summary": "Update user (Super Admin only)", "description": "Update user information across the platform", "tags": ["Super Admin - Platform Users"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"userId": {"type": "string"}, "firstName": {"type": "string"}, "lastName": {"type": "string"}, "status": {"type": "string", "enum": ["active", "suspended", "inactive"]}, "isPlatformAdmin": {"type": "boolean"}}, "required": ["userId"]}}}}, "responses": {"200": {"description": "User updated successfully"}, "400": {"description": "Validation error"}, "401": {"description": "Unauthorized"}, "403": {"description": "Super Admin access required"}, "404": {"description": "User not found"}}}, "delete": {"summary": "Delete user (Super Admin only)", "description": "Permanently delete a user from the platform", "tags": ["Super Admin - Platform Users"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"userId": {"type": "string"}}, "required": ["userId"]}}}}, "responses": {"200": {"description": "User deleted successfully"}, "400": {"description": "Validation error"}, "401": {"description": "Unauthorized"}, "403": {"description": "Super Admin access required"}, "404": {"description": "User not found"}}}}, "/api/admin/roles": {"get": {"summary": "Get all roles in tenant", "description": "Retrieve all roles with user and permission counts for the current tenant", "tags": ["Admin - Roles"], "security": [{"bearerAuth": []}, {"tenantHeader": []}], "responses": {"200": {"description": "List of roles with metadata", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "description": {"type": "string"}, "isSystemRole": {"type": "boolean"}, "userCount": {"type": "integer"}, "permissionCount": {"type": "integer"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}}}}}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden - Admin access required"}}}, "post": {"summary": "Create a new role", "description": "Create a new custom role with specified permissions", "tags": ["Admin - Roles"], "security": [{"bearerAuth": []}, {"tenantHeader": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["name"], "properties": {"name": {"type": "string", "description": "Role name", "example": "Sales Manager"}, "description": {"type": "string", "description": "Role description", "example": "Manages sales team and processes"}, "permissions": {"type": "array", "items": {"type": "string"}, "description": "Array of permission IDs to assign"}}}}}}, "responses": {"201": {"description": "Role created successfully"}, "400": {"description": "Validation error"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden - Admin access required"}, "409": {"description": "Role name already exists"}}}}, "/api/admin/tenants/{tenantId}": {"get": {"summary": "Get specific tenant details (Super Admin only)", "description": "Retrieve detailed information about a specific tenant", "tags": ["Super Admin - Tenants"], "security": [{"bearerAuth": []}], "parameters": [{"in": "path", "name": "tenantId", "required": true, "schema": {"type": "string"}, "description": "Tenant ID"}], "responses": {"200": {"description": "Tenant details retrieved successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "slug": {"type": "string"}, "status": {"type": "string"}, "userCount": {"type": "number"}, "subscription": {"type": "object"}, "usage": {"type": "object"}}}}}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Super Admin access required"}, "404": {"description": "Tenant not found"}}}}, "/api/admin/tenants/{tenantId}/users": {"get": {"summary": "Get users in specific tenant (Super Admin only)", "description": "Retrieve all users belonging to a specific tenant", "tags": ["Super Admin - Tenant Users"], "security": [{"bearerAuth": []}], "parameters": [{"in": "path", "name": "tenantId", "required": true, "schema": {"type": "string"}, "description": "Tenant ID"}, {"in": "query", "name": "page", "schema": {"type": "integer", "default": 1}, "description": "Page number"}, {"in": "query", "name": "limit", "schema": {"type": "integer", "default": 20}, "description": "Number of items per page"}], "responses": {"200": {"description": "Tenant users retrieved successfully"}, "401": {"description": "Unauthorized"}, "403": {"description": "Super Admin access required"}, "404": {"description": "Tenant not found"}}}, "put": {"summary": "Update user role in tenant (Super Admin only)", "description": "Update a user's role and permissions within a specific tenant", "tags": ["Super Admin - Tenant Users"], "security": [{"bearerAuth": []}], "parameters": [{"in": "path", "name": "tenantId", "required": true, "schema": {"type": "string"}, "description": "Tenant ID"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"userId": {"type": "string"}, "role": {"type": "string"}, "isTenantAdmin": {"type": "boolean"}}, "required": ["userId", "role"]}}}}, "responses": {"200": {"description": "User role updated successfully"}, "400": {"description": "Validation error"}, "401": {"description": "Unauthorized"}, "403": {"description": "Super Admin access required"}, "404": {"description": "Tenant or user not found"}}}, "delete": {"summary": "Remove user from tenant (Super Admin only)", "description": "Remove a user from a specific tenant", "tags": ["Super Admin - Tenant Users"], "security": [{"bearerAuth": []}], "parameters": [{"in": "path", "name": "tenantId", "required": true, "schema": {"type": "string"}, "description": "Tenant ID"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"userId": {"type": "string"}}, "required": ["userId"]}}}}, "responses": {"200": {"description": "User removed from tenant successfully"}, "400": {"description": "Validation error"}, "401": {"description": "Unauthorized"}, "403": {"description": "Super Admin access required"}, "404": {"description": "Tenant or user not found"}}}}, "/api/admin/tenants": {"get": {"summary": "Get all tenants (Super Admin only)", "description": "Retrieve a paginated list of all tenants with details", "tags": ["Super Admin - Tenants"], "security": [{"bearerAuth": []}], "parameters": [{"in": "query", "name": "page", "schema": {"type": "integer", "default": 1}, "description": "Page number"}, {"in": "query", "name": "limit", "schema": {"type": "integer", "default": 20}, "description": "Number of items per page"}], "responses": {"200": {"description": "List of tenants retrieved successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"type": "object", "properties": {"tenants": {"type": "array", "items": {"type": "object"}}, "pagination": {"type": "object"}}}}}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Super Admin access required"}}}, "put": {"summary": "Update tenant (Super Admin only)", "description": "Update tenant information", "tags": ["Super Admin - Tenants"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"tenantId": {"type": "string"}, "name": {"type": "string"}, "status": {"type": "string", "enum": ["active", "suspended", "cancelled"]}, "settings": {"type": "object"}, "branding": {"type": "object"}}}}}}, "responses": {"200": {"description": "Tenant updated successfully"}, "400": {"description": "Validation error"}, "401": {"description": "Unauthorized"}, "403": {"description": "Super Admin access required"}, "404": {"description": "Tenant not found"}}}, "delete": {"summary": "Delete tenant (Super Admin only)", "description": "Permanently delete a tenant and all associated data", "tags": ["Super Admin - Tenants"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"tenantId": {"type": "string"}}, "required": ["tenantId"]}}}}, "responses": {"200": {"description": "Tenant deleted successfully"}, "400": {"description": "Validation error"}, "401": {"description": "Unauthorized"}, "403": {"description": "Super Admin access required"}, "404": {"description": "Tenant not found"}}}}, "/api/charts/{id}/image": {"get": {"summary": "Get rendered chart image", "description": "Serve the rendered image file for a chart", "tags": ["Charts"], "security": [{"bearerAuth": []}], "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "string"}, "description": "Chart ID"}], "responses": {"200": {"description": "Chart image file", "content": {"image/png": {"schema": {"type": "string", "format": "binary"}}, "image/svg+xml": {"schema": {"type": "string", "format": "binary"}}}, "headers": {"Content-Type": {"description": "MIME type of the image", "schema": {"type": "string"}}, "Content-Length": {"description": "Size of the image in bytes", "schema": {"type": "integer"}}, "Cache-Control": {"description": "Cache control header", "schema": {"type": "string"}}, "ETag": {"description": "Entity tag for caching", "schema": {"type": "string"}}}}, "304": {"description": "Not Modified - client has cached version"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden - insufficient permissions"}, "404": {"description": "Chart not found or image not available"}, "500": {"description": "Internal server error"}}}}, "/api/charts/{id}/render": {"post": {"summary": "Render Mermaid chart to image", "description": "Convert a Mermaid chart to PNG or SVG image format", "tags": ["Charts"], "security": [{"bearerAuth": []}], "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "string"}, "description": "Chart ID"}], "requestBody": {"required": false, "content": {"application/json": {"schema": {"type": "object", "properties": {"format": {"type": "string", "enum": ["png", "svg"], "default": "png", "description": "Output image format"}, "theme": {"type": "string", "enum": ["default", "dark", "forest", "neutral"], "default": "default", "description": "Mermaid theme"}, "width": {"type": "number", "minimum": 200, "maximum": 2000, "default": 800, "description": "Image width in pixels"}, "height": {"type": "number", "minimum": 200, "maximum": 2000, "default": 600, "description": "Image height in pixels"}}}}}}, "responses": {"200": {"description": "Chart rendered successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"type": "object", "properties": {"imagePath": {"type": "string", "description": "Relative path to the rendered image"}, "imageUrl": {"type": "string", "description": "URL to access the rendered image"}, "width": {"type": "number", "description": "Image width in pixels"}, "height": {"type": "number", "description": "Image height in pixels"}, "format": {"type": "string", "description": "Image format (png or svg)"}}}, "message": {"type": "string"}}}}}}, "400": {"description": "Invalid request data or chart not found"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden - insufficient permissions"}, "500": {"description": "Internal server error"}}}}, "/api/dashboard/analytics": {"get": {"summary": "Get tenant dashboard analytics", "description": "Retrieve comprehensive analytics and metrics for the tenant dashboard", "tags": ["Dashboard", "Analytics"], "security": [{"bearerAuth": []}], "parameters": [{"in": "query", "name": "type", "schema": {"type": "string", "enum": ["full", "quick", "trends"], "default": "full"}, "description": "Type of analytics to retrieve"}, {"in": "query", "name": "days", "schema": {"type": "integer", "default": 30}, "description": "Number of days for trend data"}], "responses": {"200": {"description": "Dashboard analytics retrieved successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"type": "object", "properties": {"totalContacts": {"type": "number"}, "totalLeads": {"type": "number"}, "totalOpportunities": {"type": "number"}, "totalCompanies": {"type": "number"}, "contactsGrowth": {"type": "number"}, "leadsGrowth": {"type": "number"}, "opportunitiesGrowth": {"type": "number"}, "companiesGrowth": {"type": "number"}, "leadConversionRate": {"type": "number"}, "opportunityWinRate": {"type": "number"}, "averageDealSize": {"type": "number"}, "totalRevenue": {"type": "number"}, "recentActivities": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "type": {"type": "string"}, "action": {"type": "string"}, "description": {"type": "string"}, "timestamp": {"type": "string", "format": "date-time"}}}}, "usage": {"type": "object", "properties": {"contacts": {"type": "number"}, "leads": {"type": "number"}, "users": {"type": "number"}, "ai_requests": {"type": "number"}, "storage_mb": {"type": "number"}}}, "trends": {"type": "object", "properties": {"leads": {"type": "array", "items": {"type": "object", "properties": {"date": {"type": "string"}, "count": {"type": "number"}}}}, "opportunities": {"type": "array", "items": {"type": "object", "properties": {"date": {"type": "string"}, "count": {"type": "number"}, "value": {"type": "number"}}}}, "contacts": {"type": "array", "items": {"type": "object", "properties": {"date": {"type": "string"}, "count": {"type": "number"}}}}}}}}}}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "500": {"description": "Internal server error"}}}}, "/api/docs": {"get": {"summary": "Get API documentation in OpenAPI format", "description": "Returns the complete OpenAPI specification for the CRM API", "tags": ["Documentation"], "responses": {"200": {"description": "OpenAPI specification", "content": {"application/json": {"schema": {"type": "object"}}}}}}}, "/api/opportunities/{id}/documents": {"get": {"summary": "Get opportunity documents", "description": "Retrieve all documents associated with an opportunity", "tags": ["Opportunities"], "security": [{"bearerAuth": []}], "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "string"}, "description": "Opportunity ID"}], "responses": {"200": {"description": "Documents retrieved successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "filePath": {"type": "string"}, "mimeType": {"type": "string"}, "fileSize": {"type": "number"}, "relatedToType": {"type": "string"}, "relatedToId": {"type": "string"}, "uploadedBy": {"type": "object", "properties": {"id": {"type": "string"}, "firstName": {"type": "string"}, "lastName": {"type": "string"}, "email": {"type": "string"}}}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}}}, "metadata": {"type": "object", "properties": {"opportunityId": {"type": "string"}, "opportunityTitle": {"type": "string"}, "totalDocuments": {"type": "number"}, "totalSize": {"type": "number"}, "fileTypes": {"type": "number"}, "categories": {"type": "object", "properties": {"pdf": {"type": "number"}, "word": {"type": "number"}, "excel": {"type": "number"}, "text": {"type": "number"}, "other": {"type": "number"}}}}}, "message": {"type": "string"}}}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden - insufficient permissions"}, "404": {"description": "Opportunity not found"}, "500": {"description": "Internal server error"}}}}, "/api/proposals/{id}/download": {"get": {"summary": "Download proposal document", "description": "Download the generated Word or PDF document for a proposal", "tags": ["Proposals"], "security": [{"bearerAuth": []}], "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "string"}, "description": "Proposal ID"}, {"in": "query", "name": "disposition", "required": false, "schema": {"type": "string", "enum": ["attachment", "inline"], "default": "attachment"}, "description": "Whether to download the file or display it inline"}], "responses": {"200": {"description": "Document file", "content": {"application/vnd.openxmlformats-officedocument.wordprocessingml.document": {"schema": {"type": "string", "format": "binary"}}, "application/pdf": {"schema": {"type": "string", "format": "binary"}}, "text/html": {"schema": {"type": "string", "format": "binary"}}}, "headers": {"Content-Type": {"description": "MIME type of the document", "schema": {"type": "string"}}, "Content-Length": {"description": "Size of the document in bytes", "schema": {"type": "integer"}}, "Content-Disposition": {"description": "Disposition header for download/inline display", "schema": {"type": "string"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden - insufficient permissions"}, "404": {"description": "Proposal or document not found"}, "500": {"description": "Internal server error"}}}}, "/api/proposals/{id}/files": {"get": {"summary": "Get opportunity files for proposal generation", "description": "Retrieve all files associated with the proposal's opportunity for RFP context selection", "tags": ["Proposals"], "security": [{"bearerAuth": []}], "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "string"}, "description": "Proposal ID"}], "responses": {"200": {"description": "Files retrieved successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"type": "object", "properties": {"proposalId": {"type": "string"}, "opportunityId": {"type": "string"}, "opportunityTitle": {"type": "string"}, "totalFiles": {"type": "number"}, "supportedFiles": {"type": "array", "items": {"type": "object", "properties": {"documentId": {"type": "string"}, "name": {"type": "string"}, "filePath": {"type": "string"}, "mimeType": {"type": "string"}, "fileSize": {"type": "number"}, "isSelected": {"type": "boolean"}, "geminiFileUri": {"type": "string"}}}}, "unsupportedFiles": {"type": "array", "items": {"type": "object", "properties": {"documentId": {"type": "string"}, "name": {"type": "string"}, "mimeType": {"type": "string"}}}}, "selectedCount": {"type": "number"}, "supportedMimeTypes": {"type": "array", "items": {"type": "string"}}}}, "message": {"type": "string"}}}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden - insufficient permissions"}, "404": {"description": "Proposal not found"}, "500": {"description": "Internal server error"}}}}, "/api/proposals/{id}/generate-document": {"post": {"summary": "Generate document from proposal", "description": "Create a Word or PDF document from the proposal content including sections and charts", "tags": ["Proposals"], "security": [{"bearerAuth": []}], "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "string"}, "description": "Proposal ID"}], "requestBody": {"required": false, "content": {"application/json": {"schema": {"type": "object", "properties": {"format": {"type": "string", "enum": ["docx", "pdf"], "default": "docx", "description": "Output document format"}, "includeCharts": {"type": "boolean", "default": true, "description": "Whether to include Mermaid charts in the document"}, "includeBranding": {"type": "boolean", "default": true, "description": "Whether to include tenant branding"}, "customStyling": {"type": "object", "description": "Custom styling options for the document", "properties": {"fontFamily": {"type": "string", "description": "<PERSON>ont family for the document"}, "fontSize": {"type": "number", "minimum": 8, "maximum": 24, "description": "Base font size in points"}, "headerColor": {"type": "string", "description": "Color for headers (hex format)"}, "accentColor": {"type": "string", "description": "Accent color (hex format)"}, "logoUrl": {"type": "string", "description": "URL to company logo"}, "companyName": {"type": "string", "description": "Company name for branding"}}}}}}}}, "responses": {"200": {"description": "Document generated successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"type": "object", "properties": {"documentPath": {"type": "string", "description": "Relative path to the generated document"}, "fileName": {"type": "string", "description": "Name of the generated file"}, "fileSize": {"type": "number", "description": "Size of the generated file in bytes"}, "downloadUrl": {"type": "string", "description": "URL to download the document"}}}, "message": {"type": "string"}}}}}}, "400": {"description": "Invalid request data or proposal has no content"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden - insufficient permissions"}, "404": {"description": "Proposal not found"}, "409": {"description": "Conflict - proposal is being generated"}, "500": {"description": "Internal server error"}}}}, "/api/proposals/{id}/regenerate-section": {"post": {"summary": "Regenerate proposal section", "description": "Use AI to regenerate a specific section of a proposal with optional custom prompt", "tags": ["Proposals"], "security": [{"bearerAuth": []}], "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "string"}, "description": "Proposal ID"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["sectionId"], "properties": {"sectionId": {"type": "string", "description": "ID of the section to regenerate"}, "customPrompt": {"type": "string", "description": "Optional custom prompt to guide AI regeneration"}, "preserveManualEdits": {"type": "boolean", "description": "Whether to preserve any manual edits made to the section", "default": false}}}}}}, "responses": {"200": {"description": "Section regenerated successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"type": "object", "properties": {"section": {"type": "object", "description": "Updated section data"}, "regenerationStats": {"type": "object", "properties": {"tokensUsed": {"type": "number"}, "wordCount": {"type": "number"}, "confidence": {"type": "number"}, "preservedManualEdits": {"type": "boolean"}}}}}, "message": {"type": "string"}}}}}}, "400": {"description": "Invalid request data"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden - insufficient permissions"}, "404": {"description": "Proposal or section not found"}, "409": {"description": "Conflict - proposal is being generated"}, "500": {"description": "Internal server error"}}}}, "/api/proposals/{id}/sections/{sectionId}": {"get": {"summary": "Get proposal section", "description": "Retrieve a specific section of a proposal", "tags": ["Proposals"], "security": [{"bearerAuth": []}], "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "string"}, "description": "Proposal ID"}, {"in": "path", "name": "sectionId", "required": true, "schema": {"type": "string"}, "description": "Section ID"}], "responses": {"200": {"description": "Section retrieved successfully"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Section not found"}, "500": {"description": "Internal server error"}}}, "put": {"summary": "Update proposal section", "description": "Update content, status, or other properties of a proposal section", "tags": ["Proposals"], "security": [{"bearerAuth": []}], "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "string"}, "description": "Proposal ID"}, {"in": "path", "name": "sectionId", "required": true, "schema": {"type": "string"}, "description": "Section ID"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"title": {"type": "string", "maxLength": 255}, "content": {"type": "string"}, "status": {"type": "string", "enum": ["draft", "generated", "reviewed", "approved"]}, "orderIndex": {"type": "number", "minimum": 0}}}}}}, "responses": {"200": {"description": "Section updated successfully"}, "400": {"description": "Invalid request data"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Section not found"}, "409": {"description": "Conflict - proposal is being generated"}, "500": {"description": "Internal server error"}}}, "delete": {"summary": "Delete proposal section", "description": "Delete a specific section from a proposal", "tags": ["Proposals"], "security": [{"bearerAuth": []}], "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "string"}, "description": "Proposal ID"}, {"in": "path", "name": "sectionId", "required": true, "schema": {"type": "string"}, "description": "Section ID"}], "responses": {"200": {"description": "Section deleted successfully"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Section not found"}, "409": {"description": "Conflict - proposal is being generated"}, "500": {"description": "Internal server error"}}}}, "/api/proposals/{id}/sections": {"get": {"summary": "Get proposal sections", "description": "Retrieve all sections and charts for a proposal with statistics", "tags": ["Proposals"], "security": [{"bearerAuth": []}], "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "string"}, "description": "Proposal ID"}], "responses": {"200": {"description": "Sections retrieved successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"type": "object", "properties": {"proposalId": {"type": "string"}, "proposalTitle": {"type": "string"}, "generationStatus": {"type": "string", "enum": ["pending", "generating", "completed", "failed"]}, "sections": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "sectionType": {"type": "string"}, "title": {"type": "string"}, "content": {"type": "string"}, "status": {"type": "string"}, "wordCount": {"type": "number"}, "tokensUsed": {"type": "number"}, "orderIndex": {"type": "number"}, "isAiGenerated": {"type": "boolean"}, "generatedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "string"}, "firstName": {"type": "string"}, "lastName": {"type": "string"}, "email": {"type": "string"}}}}}}, "charts": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "chartType": {"type": "string"}, "title": {"type": "string"}, "description": {"type": "string"}, "mermaidCode": {"type": "string"}, "renderedImagePath": {"type": "string"}, "status": {"type": "string"}, "orderIndex": {"type": "number"}, "isAiGenerated": {"type": "boolean"}, "generatedAt": {"type": "string", "format": "date-time"}}}}, "statistics": {"type": "object", "properties": {"totalSections": {"type": "number"}, "completedSections": {"type": "number"}, "totalCharts": {"type": "number"}, "renderedCharts": {"type": "number"}, "totalWordCount": {"type": "number"}, "totalTokensUsed": {"type": "number"}, "completionPercentage": {"type": "number"}}}}}, "message": {"type": "string"}}}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden - insufficient permissions"}, "404": {"description": "Proposal not found"}, "500": {"description": "Internal server error"}}}}, "/api/proposals/{id}/select-files": {"post": {"summary": "Select RFP files for proposal generation", "description": "Select which opportunity files to use as context for AI proposal generation", "tags": ["Proposals"], "security": [{"bearerAuth": []}], "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "string"}, "description": "Proposal ID"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["selectedFileIds"], "properties": {"selectedFileIds": {"type": "array", "items": {"type": "string"}, "description": "Array of document IDs to use as RFP context", "minItems": 1}, "uploadToGemini": {"type": "boolean", "description": "Whether to upload files to Gemini API immediately", "default": true}}}}}}, "responses": {"200": {"description": "File selection updated successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"type": "object", "properties": {"proposalId": {"type": "string"}, "selectedFileIds": {"type": "array", "items": {"type": "string"}}, "selectedDocuments": {"type": "array", "items": {"type": "object", "properties": {"documentId": {"type": "string"}, "name": {"type": "string"}, "mimeType": {"type": "string"}, "fileSize": {"type": "number"}, "geminiFileUri": {"type": "string"}}}}, "geminiUploadStatus": {"type": "string", "enum": ["completed", "skipped", "failed"]}, "totalSelected": {"type": "number"}}}, "message": {"type": "string"}}}}}}, "400": {"description": "Invalid request data"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden - insufficient permissions"}, "404": {"description": "Proposal not found"}, "409": {"description": "Conflict - proposal is being generated"}, "500": {"description": "Internal server error"}}}}, "/api/proposals/{id}/validate-files": {"post": {"summary": "Validate files for Gemini upload compatibility", "description": "Check if selected files are compatible with Gemini API upload requirements", "tags": ["Proposals"], "security": [{"bearerAuth": []}], "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "string"}, "description": "Proposal ID"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["documentIds"], "properties": {"documentIds": {"type": "array", "items": {"type": "string"}, "description": "Array of document IDs to validate", "minItems": 1}}}}}}, "responses": {"200": {"description": "File validation completed", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"type": "object", "properties": {"proposalId": {"type": "string"}, "totalFiles": {"type": "number"}, "validFiles": {"type": "number"}, "invalidFiles": {"type": "number"}, "validationResults": {"type": "array", "items": {"type": "object", "properties": {"documentId": {"type": "string"}, "name": {"type": "string"}, "mimeType": {"type": "string"}, "fileSize": {"type": "number"}, "isValid": {"type": "boolean"}, "error": {"type": "string"}, "canUpload": {"type": "boolean"}}}}, "canProceedWithUpload": {"type": "boolean"}, "summary": {"type": "object", "properties": {"allValid": {"type": "boolean"}, "hasValidFiles": {"type": "boolean"}, "hasInvalidFiles": {"type": "boolean"}}}}}, "message": {"type": "string"}}}}}}, "400": {"description": "Bad request - Invalid input or documents not found"}, "401": {"description": "Authentication required"}, "403": {"description": "Insufficient permissions"}, "404": {"description": "Proposal not found"}, "500": {"description": "Internal server error"}}}}, "/api/proposals/generate": {"post": {"summary": "Generate AI-powered proposal", "description": "Generate a comprehensive proposal using AI based on opportunity data and selected RFP files", "tags": ["Proposals"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["opportunityId", "title", "selectedFileIds", "sections"], "properties": {"opportunityId": {"type": "string", "description": "ID of the opportunity to generate proposal for"}, "title": {"type": "string", "description": "Title of the proposal", "maxLength": 255}, "selectedFileIds": {"type": "array", "items": {"type": "string"}, "description": "Array of document IDs to use as RFP context", "minItems": 1}, "sections": {"type": "array", "items": {"type": "string", "enum": ["executive_summary", "scope_of_work", "architecture_high_level", "architecture_low_level", "out_of_scope", "assumptions", "project_plan", "resource_estimation"]}, "description": "Sections to generate", "minItems": 1}, "includeCharts": {"type": "boolean", "description": "Whether to generate Mermaid charts", "default": false}, "chartTypes": {"type": "array", "items": {"type": "string", "enum": ["architecture_high_level", "architecture_low_level", "project_timeline", "system_flow"]}, "description": "Types of charts to generate (required if includeCharts is true)"}, "customPrompt": {"type": "string", "description": "Optional custom prompt to guide AI generation"}}}}}}, "responses": {"201": {"description": "Proposal generation started successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"type": "object", "properties": {"proposalId": {"type": "string"}, "status": {"type": "string", "enum": ["pending", "generating", "completed", "failed"]}, "message": {"type": "string"}, "estimatedCompletionTime": {"type": "number", "description": "Estimated completion time in minutes"}}}, "message": {"type": "string"}}}}}}, "400": {"description": "Invalid request data"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden - insufficient permissions"}, "404": {"description": "Opportunity not found"}, "500": {"description": "Internal server error"}}}}, "/api/subscriptions/plans": {"get": {"summary": "Get all available subscription plans", "description": "Retrieve all active subscription plans with features and pricing", "tags": ["Subscription Plans"], "responses": {"200": {"description": "Subscription plans retrieved successfully", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/ApiResponse"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/SubscriptionPlan"}}}}]}}}}, "500": {"description": "Internal server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/api/subscriptions": {"get": {"summary": "Get tenant subscription details", "description": "Retrieve current subscription information including usage metrics", "tags": ["Subscriptions"], "security": [{"bearerAuth": []}, {"tenantHeader": []}], "responses": {"200": {"description": "Subscription details retrieved successfully", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/ApiResponse"}, {"type": "object", "properties": {"data": {"type": "object", "properties": {"subscription": {"$ref": "#/components/schemas/Subscription"}, "plan": {"$ref": "#/components/schemas/SubscriptionPlan"}, "usage": {"type": "object", "additionalProperties": {"type": "number"}}}}}}]}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "404": {"description": "Subscription not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}, "post": {"summary": "Create or update tenant subscription", "description": "Create a new subscription or update existing subscription for the tenant", "tags": ["Subscriptions"], "security": [{"bearerAuth": []}, {"tenantHeader": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["planId", "billingCycle"], "properties": {"planId": {"type": "string", "format": "uuid", "description": "ID of the subscription plan"}, "billingCycle": {"type": "string", "enum": ["monthly", "yearly"], "description": "Billing cycle for the subscription"}, "paymentMethodId": {"type": "string", "description": "Stripe payment method ID"}, "trialDays": {"type": "integer", "minimum": 0, "maximum": 30, "description": "Number of trial days (0-30)"}}}}}}, "responses": {"201": {"description": "Subscription created successfully", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/ApiResponse"}, {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/Subscription"}}}]}}}}, "400": {"description": "Validation error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}, "put": {"summary": "Update tenant subscription", "description": "Update existing subscription plan, billing cycle, or cancellation status", "tags": ["Subscriptions"], "security": [{"bearerAuth": []}, {"tenantHeader": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"planId": {"type": "string", "format": "uuid", "description": "New subscription plan ID"}, "billingCycle": {"type": "string", "enum": ["monthly", "yearly"], "description": "New billing cycle"}, "cancelAtPeriodEnd": {"type": "boolean", "description": "Whether to cancel subscription at period end"}}}}}}, "responses": {"200": {"description": "Subscription updated successfully", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/ApiResponse"}, {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/Subscription"}}}]}}}}, "400": {"description": "Validation error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "404": {"description": "Subscription not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/api/subscriptions/usage": {"get": {"summary": "Get tenant usage metrics", "description": "Retrieve current usage metrics and limits for the tenant", "tags": ["Usage Tracking"], "security": [{"bearerAuth": []}, {"tenantHeader": []}], "parameters": [{"in": "query", "name": "metric", "schema": {"type": "string"}, "description": "Specific metric to check (optional)"}], "responses": {"200": {"description": "Usage metrics retrieved successfully", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/ApiResponse"}, {"type": "object", "properties": {"data": {"type": "object", "properties": {"usage": {"type": "object", "additionalProperties": {"type": "object", "properties": {"current": {"type": "number"}, "limit": {"type": "number"}, "isExceeded": {"type": "boolean"}, "percentage": {"type": "number"}}}}}}}}]}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}, "post": {"summary": "Track usage for a metric", "description": "Increment usage counter for a specific metric", "tags": ["Usage Tracking"], "security": [{"bearerAuth": []}, {"tenantHeader": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["metric"], "properties": {"metric": {"type": "string", "description": "Name of the metric to track", "example": "contacts"}, "increment": {"type": "number", "minimum": 1, "default": 1, "description": "Amount to increment the metric by"}}}}}}, "responses": {"200": {"description": "Usage tracked successfully", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/ApiResponse"}, {"type": "object", "properties": {"data": {"type": "object", "properties": {"metric": {"type": "string"}, "newUsage": {"type": "object", "properties": {"current": {"type": "number"}, "limit": {"type": "number"}, "isExceeded": {"type": "boolean"}, "percentage": {"type": "number"}}}}}}}]}}}}, "400": {"description": "Validation error or usage limit exceeded", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/api/tenants/check-slug": {"post": {"summary": "Check if tenant slug is available", "description": "Validates if a tenant slug is available for registration", "tags": ["Tenants"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["slug"], "properties": {"slug": {"type": "string", "description": "The slug to check", "example": "my-company"}}}}}}, "responses": {"200": {"description": "Slug availability status", "content": {"application/json": {"schema": {"type": "object", "properties": {"available": {"type": "boolean", "description": "Whether the slug is available"}, "slug": {"type": "string", "description": "The checked slug"}}}}}}, "400": {"description": "Validation error", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "properties": {"field": {"type": "string"}, "message": {"type": "string"}}}}}}}}}}}}}, "tags": []}