'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { PermissionProvider } from '@/components/providers/permission-provider';
import { PermissionGate } from '@/components/ui/permission-gate';
import { PermissionAction, PermissionResource } from '@/types';
import { useTenant } from '@/hooks/use-tenant';
import { PageLayout } from '@/components/layout/page-layout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import {
  ArrowLeftIcon,
  UserPlusIcon,
  ExclamationTriangleIcon,
  EyeIcon,
  EyeSlashIcon,
  KeyIcon
} from '@heroicons/react/24/outline';
import { PasswordInput } from '@/components/ui/password-input';
import { PasswordStrengthIndicator } from '@/components/ui/password-strength-indicator';
import { generateUserFriendlyPassword } from '@/lib/client-password-utils';
import { useToast } from '@/hooks/use-toast';

interface Team {
  id: string;
  name: string;
  color: string | null;
}

interface Role {
  id: string;
  name: string;
  description: string | null;
  isSystemRole: boolean;
}

interface CreateUserFormData {
  email: string;
  password: string;
  confirmPassword: string;
  firstName: string;
  lastName: string;
  phone: string;
  role: string;
  teamId: string;
  department: string;
  jobTitle: string;
  locale: string;
  timezone: string;
}

export default function CreateUserPage() {
  const { currentTenant } = useTenant();
  const router = useRouter();
  const { toast } = useToast();

  const [formData, setFormData] = useState<CreateUserFormData>({
    email: '',
    password: '',
    confirmPassword: '',
    firstName: '',
    lastName: '',
    phone: '',
    role: 'user',
    teamId: '',
    department: '',
    jobTitle: '',
    locale: 'en',
    timezone: 'UTC'
  });

  const [teams, setTeams] = useState<Team[]>([]);
  const [roles, setRoles] = useState<Role[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingTeams, setIsLoadingTeams] = useState(true);
  const [isLoadingRoles, setIsLoadingRoles] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [sendWelcomeEmail, setSendWelcomeEmail] = useState(true);

  useEffect(() => {
    if (currentTenant) {
      fetchTeams();
      fetchRoles();
    }
  }, [currentTenant]);

  const fetchTeams = async () => {
    try {
      setIsLoadingTeams(true);
      const response = await fetch('/api/teams');

      if (response.ok) {
        const data = await response.json();
        // Ensure we always set an array
        const teamsData = data.data;
        setTeams(Array.isArray(teamsData) ? teamsData : []);
      } else {
        console.error('Failed to fetch teams: HTTP', response.status);
        setTeams([]);
      }
    } catch (err) {
      console.error('Failed to fetch teams:', err);
      setTeams([]);
    } finally {
      setIsLoadingTeams(false);
    }
  };

  const fetchRoles = async () => {
    try {
      setIsLoadingRoles(true);
      const response = await fetch('/api/roles/assignable');

      if (response.ok) {
        const data = await response.json();
        // Ensure we always set an array
        const rolesData = data.data;
        const roles = Array.isArray(rolesData) ? rolesData : [];
        setRoles(roles);

        // Set default role to the first non-admin role if available
        if (roles.length > 0) {
          const defaultRole = roles.find((role: Role) =>
            role.name.toLowerCase().includes('user') ||
            role.name.toLowerCase().includes('sales rep') ||
            role.name.toLowerCase().includes('member')
          ) || roles[0];

          setFormData(prev => ({
            ...prev,
            role: defaultRole.name
          }));
        }
      } else {
        console.error('Failed to fetch roles: HTTP', response.status);
        setRoles([]);
      }
    } catch (err) {
      console.error('Failed to fetch roles:', err);
      setRoles([]);
    } finally {
      setIsLoadingRoles(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleGeneratePassword = () => {
    const newPassword = generateUserFriendlyPassword();
    setFormData(prev => ({
      ...prev,
      password: newPassword,
      confirmPassword: newPassword
    }));
    toast({
      title: 'Password Generated',
      description: 'A secure password has been generated for you.',
      variant: 'success'
    });
  };

  const validateForm = (): string | null => {
    if (!formData.email.trim()) return 'Email is required';
    if (!formData.firstName.trim()) return 'First name is required';
    if (!formData.lastName.trim()) return 'Last name is required';
    if (!formData.password) return 'Password is required';
    if (formData.password.length < 8) return 'Password must be at least 8 characters';
    if (formData.password !== formData.confirmPassword) return 'Passwords do not match';
    if (!formData.role.trim()) return 'Role is required';

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(formData.email)) return 'Please enter a valid email address';

    return null;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    const validationError = validateForm();
    if (validationError) {
      setError(validationError);
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/users', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'create',
          email: formData.email.trim(),
          password: formData.password,
          firstName: formData.firstName.trim(),
          lastName: formData.lastName.trim(),
          phone: formData.phone.trim() || undefined,
          role: formData.role,
          teamId: formData.teamId || undefined,
          department: formData.department.trim() || undefined,
          jobTitle: formData.jobTitle.trim() || undefined,
          locale: formData.locale,
          timezone: formData.timezone,
          sendWelcomeEmail: sendWelcomeEmail
        }),
      });

      if (response.ok) {
        const result = await response.json();
        toast({
          title: 'User Created Successfully',
          description: `${formData.firstName} ${formData.lastName} has been added to your organization.${sendWelcomeEmail ? ' A welcome email has been sent.' : ''}`,
          variant: 'success'
        });
        router.push('/settings/users');
      } else {
        const errorData = await response.json();
        setError(errorData.error || 'Failed to create user');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create user');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    router.push('/settings/users');
  };

  if (!currentTenant) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <ExclamationTriangleIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-500">No tenant context available</p>
        </div>
      </div>
    );
  }

  const actions = (
    <div className="flex items-center gap-2">
      <Button variant="outline" onClick={handleCancel}>
        <ArrowLeftIcon className="w-4 h-4 mr-2" />
        Back to Users
      </Button>
    </div>
  );

  return (
    <PermissionProvider>
      <PageLayout
        title="Create New User"
        description="Add a new user to your organization"
        actions={actions}
      >
        <PermissionGate
          resource={PermissionResource.USERS}
          action={PermissionAction.CREATE}
          fallback={<AccessDeniedMessage />}
        >
          <div className="max-w-2xl mx-auto">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <UserPlusIcon className="w-5 h-5" />
                  User Information
                </CardTitle>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleSubmit} className="space-y-6">
                  {error && (
                    <div className="bg-red-50 border border-red-200 rounded-md p-4">
                      <div className="flex">
                        <ExclamationTriangleIcon className="w-5 h-5 text-red-400 mr-2 flex-shrink-0 mt-0.5" />
                        <p className="text-sm text-red-600">{error}</p>
                      </div>
                    </div>
                  )}

                  {/* Basic Information */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label htmlFor="firstName" className="block text-sm font-medium text-gray-700 mb-1">
                        First Name *
                      </label>
                      <input
                        type="text"
                        id="firstName"
                        name="firstName"
                        value={formData.firstName}
                        onChange={handleInputChange}
                        required
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder="Enter first name"
                      />
                    </div>

                    <div>
                      <label htmlFor="lastName" className="block text-sm font-medium text-gray-700 mb-1">
                        Last Name *
                      </label>
                      <input
                        type="text"
                        id="lastName"
                        name="lastName"
                        value={formData.lastName}
                        onChange={handleInputChange}
                        required
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder="Enter last name"
                      />
                    </div>
                  </div>

                  <div>
                    <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                      Email Address *
                    </label>
                    <input
                      type="email"
                      id="email"
                      name="email"
                      value={formData.email}
                      onChange={handleInputChange}
                      required
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="Enter email address"
                    />
                  </div>

                  <div>
                    <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-1">
                      Phone Number
                    </label>
                    <input
                      type="tel"
                      id="phone"
                      name="phone"
                      value={formData.phone}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="Enter phone number"
                    />
                  </div>

                  {/* Password Fields */}
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <h4 className="text-sm font-medium text-gray-900">Password Setup</h4>
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={handleGeneratePassword}
                        className="flex items-center gap-2"
                      >
                        <KeyIcon className="w-4 h-4" />
                        Generate Password
                      </Button>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">
                          Password *
                        </label>
                        <PasswordInput
                          id="password"
                          name="password"
                          value={formData.password}
                          onChange={handleInputChange}
                          required
                          placeholder="Enter password"
                        />
                      </div>

                      <div>
                        <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-700 mb-1">
                          Confirm Password *
                        </label>
                        <PasswordInput
                          id="confirmPassword"
                          name="confirmPassword"
                          value={formData.confirmPassword}
                          onChange={handleInputChange}
                          required
                          placeholder="Confirm password"
                          error={formData.password && formData.confirmPassword && formData.password !== formData.confirmPassword ? 'Passwords do not match' : undefined}
                        />
                      </div>
                    </div>

                    {/* Password Strength Indicator */}
                    {formData.password && (
                      <PasswordStrengthIndicator
                        password={formData.password}
                        minLength={8}
                        showRequirements={true}
                        className="mt-3"
                      />
                    )}
                  </div>

                  {/* Role and Team */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label htmlFor="role" className="block text-sm font-medium text-gray-700 mb-1">
                        Role
                      </label>
                      <select
                        id="role"
                        name="role"
                        value={formData.role}
                        onChange={handleInputChange}
                        disabled={isLoadingRoles}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-50"
                      >
                        {isLoadingRoles ? (
                          <option value="">Loading roles...</option>
                        ) : roles.length === 0 ? (
                          <option value="">No roles available</option>
                        ) : (
                          <>
                            <option value="">Select a role</option>
                            {roles.map((role) => (
                              <option key={role.id} value={role.name}>
                                {role.name}
                                {role.description && ` - ${role.description}`}
                              </option>
                            ))}
                          </>
                        )}
                      </select>
                    </div>

                    <div>
                      <label htmlFor="teamId" className="block text-sm font-medium text-gray-700 mb-1">
                        Team
                      </label>
                      <select
                        id="teamId"
                        name="teamId"
                        value={formData.teamId}
                        onChange={handleInputChange}
                        disabled={isLoadingTeams}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-50"
                      >
                        <option value="">No team</option>
                        {isLoadingTeams ? (
                          <option value="">Loading teams...</option>
                        ) : Array.isArray(teams) && teams.length > 0 ? (
                          teams.map((team) => (
                            <option key={team.id} value={team.id}>
                              {team.name}
                            </option>
                          ))
                        ) : (
                          <option value="">No teams available</option>
                        )}
                      </select>
                    </div>
                  </div>

                  {/* Additional Information */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label htmlFor="department" className="block text-sm font-medium text-gray-700 mb-1">
                        Department
                      </label>
                      <input
                        type="text"
                        id="department"
                        name="department"
                        value={formData.department}
                        onChange={handleInputChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder="Enter department"
                      />
                    </div>

                    <div>
                      <label htmlFor="jobTitle" className="block text-sm font-medium text-gray-700 mb-1">
                        Job Title
                      </label>
                      <input
                        type="text"
                        id="jobTitle"
                        name="jobTitle"
                        value={formData.jobTitle}
                        onChange={handleInputChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder="Enter job title"
                      />
                    </div>
                  </div>

                  {/* Preferences */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label htmlFor="locale" className="block text-sm font-medium text-gray-700 mb-1">
                        Language
                      </label>
                      <select
                        id="locale"
                        name="locale"
                        value={formData.locale}
                        onChange={handleInputChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      >
                        <option value="en">English</option>
                        <option value="ar">Arabic</option>
                      </select>
                    </div>

                    <div>
                      <label htmlFor="timezone" className="block text-sm font-medium text-gray-700 mb-1">
                        Timezone
                      </label>
                      <select
                        id="timezone"
                        name="timezone"
                        value={formData.timezone}
                        onChange={handleInputChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      >
                        <option value="UTC">UTC</option>
                        <option value="America/New_York">Eastern Time</option>
                        <option value="America/Chicago">Central Time</option>
                        <option value="America/Denver">Mountain Time</option>
                        <option value="America/Los_Angeles">Pacific Time</option>
                        <option value="Europe/London">London</option>
                        <option value="Europe/Paris">Paris</option>
                        <option value="Asia/Dubai">Dubai</option>
                        <option value="Asia/Tokyo">Tokyo</option>
                      </select>
                    </div>
                  </div>

                  {/* Notification Options */}
                  <div className="space-y-3">
                    <h4 className="text-sm font-medium text-gray-900">Notification Options</h4>
                    <div className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        id="sendWelcomeEmail"
                        checked={sendWelcomeEmail}
                        onChange={(e) => setSendWelcomeEmail(e.target.checked)}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                      <label htmlFor="sendWelcomeEmail" className="text-sm text-gray-700">
                        Send welcome email with login credentials
                      </label>
                    </div>
                    <p className="text-xs text-gray-500 ml-6">
                      The user will receive an email with their temporary password and login instructions.
                    </p>
                  </div>

                  {/* Form Actions */}
                  <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={handleCancel}
                      disabled={isLoading}
                    >
                      Cancel
                    </Button>
                    <Button
                      type="submit"
                      disabled={isLoading}
                      className="flex items-center gap-2"
                    >
                      {isLoading ? (
                        <>
                          <LoadingSpinner size="sm" />
                          Creating...
                        </>
                      ) : (
                        <>
                          <UserPlusIcon className="w-4 h-4" />
                          Create User
                        </>
                      )}
                    </Button>
                  </div>
                </form>
              </CardContent>
            </Card>
          </div>
        </PermissionGate>
      </PageLayout>
    </PermissionProvider>
  );
}

function AccessDeniedMessage() {
  return (
    <div className="text-center py-12">
      <ExclamationTriangleIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
      <h3 className="text-lg font-medium text-gray-900 mb-2">Access Denied</h3>
      <p className="text-gray-500 max-w-md mx-auto">
        You don't have permission to create users.
      </p>
    </div>
  );
}
