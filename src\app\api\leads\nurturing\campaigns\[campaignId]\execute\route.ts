import { NextResponse } from 'next/server';
import { withPermission } from '@/lib/permission-middleware';
import { leadNurturingService } from '@/services/lead-nurturing';
import { PermissionAction, PermissionResource } from '@/types';
import { AuthenticatedRequest } from '@/lib/permission-middleware';

/**
 * POST /api/leads/nurturing/campaigns/[campaignId]/execute
 * Execute a nurturing campaign
 */
export const POST = withPermission({
  resource: PermissionResource.LEADS,
  action: PermissionAction.UPDATE,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest) => {
  try {
    // Extract campaignId from URL
    const url = new URL(req.url);
    const pathSegments = url.pathname.split('/');
    const campaignId = pathSegments[pathSegments.length - 2]; // -2 because last segment is 'execute'

    if (!campaignId) {
      return NextResponse.json({ error: 'Campaign ID required' }, { status: 400 });
    }

    const body = await req.json();
    const { dryRun = false } = body;

    const result = await leadNurturingService.executeCampaign(
      campaignId,
      req.tenantId,
      dryRun
    );

    return NextResponse.json({
      success: true,
      data: result
    });
  } catch (error) {
    return NextResponse.json(
      { error: 'Failed to execute campaign' },
      { status: 500 }
    );
  }
});
