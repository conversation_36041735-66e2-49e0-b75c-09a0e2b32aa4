'use client';

import React, { createContext, useContext, useEffect, useState, useCallback, useRef, useMemo } from 'react';
import { useAuth } from '@/hooks/use-auth';
import { useTenant } from '@/hooks/use-tenant';
import { PermissionAction, PermissionResource, Permission, Role } from '@/types';
import { isValidPermission } from '@/constants/permissions';
import { createPermissionString, isValidResourceAction } from '@/utils/permission-helpers';
import { PermissionCacheService } from '@/lib/cache/permission-cache';
import { getPermissionWebSocketService, getCrossTabPermissionSync, PermissionUpdateEvent } from '@/lib/websocket/permission-websocket';

export interface EnhancedPermissionContextValue {
  permissions: Permission[];
  roles: Role[];
  isLoading: boolean;
  error: string | null;
  hasPermission: (resource: PermissionResource, action: PermissionAction) => boolean;
  hasAnyPermission: (permissions: Array<{ resource: PermissionResource; action: PermissionAction }>) => boolean;
  hasAllPermissions: (permissions: Array<{ resource: PermissionResource; action: PermissionAction }>) => boolean;
  canAccessResource: (resource: PermissionResource, action: PermissionAction, resourceOwnerId?: string) => boolean;
  isTenantAdmin: boolean;
  isSuperAdmin: boolean;
  refreshPermissions: (forceRefresh?: boolean) => Promise<void>;
  getPermissionsByCategory: () => Record<string, Permission[]>;
  getUserRoles: () => Role[];
  hasRole: (roleName: string) => boolean;
  canPerformBulkAction: (resource: PermissionResource, action: PermissionAction, itemCount: number) => boolean;
  // Enhanced caching features
  isCacheEnabled: boolean;
  cacheStats: () => any;
  clearCache: () => void;
  lastCacheUpdate: string | null;
  isFromCache: boolean;
  cacheHitRate: number;
}

const EnhancedPermissionContext = createContext<EnhancedPermissionContextValue | undefined>(undefined);

export interface EnhancedPermissionProviderProps {
  children: React.ReactNode;
  enableCache?: boolean;
  enableRealTimeUpdates?: boolean;
  cacheOptions?: {
    ttl?: number;
    encrypt?: boolean;
    validateIntegrity?: boolean;
  };
}

export function EnhancedPermissionProvider({ 
  children, 
  enableCache = true,
  enableRealTimeUpdates = true,
  cacheOptions = {}
}: EnhancedPermissionProviderProps) {
  const { user, isAuthenticated } = useAuth();
  const { currentTenant } = useTenant();
  
  // Core state
  const [permissions, setPermissions] = useState<Permission[]>([]);
  const [roles, setRoles] = useState<Role[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [ongoingRequest, setOngoingRequest] = useState<string | null>(null);
  
  // Cache-related state
  const [lastCacheUpdate, setLastCacheUpdate] = useState<string | null>(null);
  const [isFromCache, setIsFromCache] = useState(false);
  const [cacheHitCount, setCacheHitCount] = useState(0);
  const [totalRequests, setTotalRequests] = useState(0);
  
  // WebSocket and cross-tab sync refs (with error handling)
  const webSocketService = useRef<any>(null);
  const crossTabSync = useRef<any>(null);
  const permissionUpdateCallbackRef = useRef<((event: PermissionUpdateEvent) => void) | null>(null);

  // Initialize WebSocket and cross-tab sync with error handling
  useEffect(() => {
    if (enableRealTimeUpdates && typeof window !== 'undefined') {
      try {
        webSocketService.current = getPermissionWebSocketService();
        crossTabSync.current = getCrossTabPermissionSync();
        console.log('✅ Real-time services initialized');
      } catch (error) {
        console.warn('⚠️ Real-time services not available, continuing with caching only:', error);
        webSocketService.current = null;
        crossTabSync.current = null;
      }
    }
  }, [enableRealTimeUpdates]);

  // Cache hit rate calculation
  const cacheHitRate = useMemo(() => {
    return totalRequests > 0 ? (cacheHitCount / totalRequests) * 100 : 0;
  }, [cacheHitCount, totalRequests]);

  // Enhanced fetch permissions with caching
  const fetchPermissions = useCallback(async (forceRefresh: boolean = false) => {
    if (!isAuthenticated || !user || !currentTenant) {
      setPermissions([]);
      setRoles([]);
      setIsLoading(false);
      setIsFromCache(false);
      return;
    }

    // Super admin has all permissions
    if (user.isPlatformAdmin) {
      setPermissions([]);
      setRoles([]);
      setIsLoading(false);
      setIsFromCache(false);
      return;
    }

    // Prevent duplicate requests (unless force refresh)
    if (ongoingRequest && !forceRefresh) {
      return;
    }

    setTotalRequests(prev => prev + 1);

    // Try to load from cache first (if enabled and not forcing refresh)
    if (enableCache && !forceRefresh) {
      try {
        const cachedData = await PermissionCacheService.getPermissions(
          user.id,
          currentTenant.id,
          cacheOptions
        );

        if (cachedData) {
          console.log('✅ Loaded permissions from cache');
          setPermissions(cachedData.effectivePermissions);
          setRoles(cachedData.roles);
          setLastCacheUpdate(cachedData.lastUpdated);
          setIsFromCache(true);
          setIsLoading(false);
          setCacheHitCount(prev => prev + 1);
          return;
        }
      } catch (error) {
        console.warn('Failed to load from cache, falling back to API:', error);
      }
    }

    // Fetch from API
    setOngoingRequest(currentTenant.id);
    setIsLoading(true);
    setError(null);
    setIsFromCache(false);

    try {
      // If force refresh is requested, call the refresh endpoint first
      if (forceRefresh) {
        await fetch('/api/auth/refresh-permissions', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' }
        });
      }

      const response = await fetch(`/api/permissions/user?tenantId=${currentTenant.id}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('EnhancedPermissionProvider: API error', {
          status: response.status,
          statusText: response.statusText,
          error: errorText
        });
        throw new Error(`Failed to fetch permissions: ${response.status} ${errorText}`);
      }

      const data = await response.json();
      const fetchedPermissions = data.data.effectivePermissions || [];
      const fetchedRoles = data.data.roles || [];
      const lastUpdated = data.data.lastUpdated || new Date().toISOString();

      setPermissions(fetchedPermissions);
      setRoles(fetchedRoles);
      setLastCacheUpdate(lastUpdated);

      // Cache the fetched data (if enabled)
      if (enableCache) {
        try {
          await PermissionCacheService.setPermissions(
            user.id,
            currentTenant.id,
            {
              permissions: data.data.permissions || [],
              roles: fetchedRoles,
              effectivePermissions: fetchedPermissions,
              lastUpdated
            },
            cacheOptions
          );
          console.log('✅ Cached permissions successfully');
        } catch (error) {
          console.warn('Failed to cache permissions:', error);
        }
      }

    } catch (err) {
      console.error('EnhancedPermissionProvider: Failed to fetch permissions:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch permissions');
      setPermissions([]);
      setRoles([]);
    } finally {
      setIsLoading(false);
      setOngoingRequest(null);
    }
  }, [isAuthenticated, user, currentTenant, ongoingRequest, enableCache, cacheOptions]);

  // Handle real-time permission updates
  const handlePermissionUpdate = useCallback((event: PermissionUpdateEvent) => {
    // Only process updates for the current user and tenant
    if (!user || !currentTenant || 
        event.userId !== user.id || 
        event.tenantId !== currentTenant.id) {
      return;
    }

    console.log('🔄 Processing real-time permission update:', event);

    // Update permissions based on the event type
    if (event.effectivePermissions) {
      setPermissions(event.effectivePermissions);
    }
    
    if (event.roles) {
      setRoles(event.roles);
    }

    // Update cache with new data
    if (enableCache && event.effectivePermissions && event.roles) {
      PermissionCacheService.setPermissions(
        user.id,
        currentTenant.id,
        {
          permissions: event.permissions || [],
          roles: event.roles,
          effectivePermissions: event.effectivePermissions,
          lastUpdated: event.timestamp
        },
        cacheOptions
      ).catch(error => {
        console.warn('Failed to update cache after real-time update:', error);
      });
    }

    setLastCacheUpdate(event.timestamp);
    setIsFromCache(false);

    // Broadcast to other tabs
    if (crossTabSync.current) {
      crossTabSync.current.broadcastPermissionUpdate(event);
    }
  }, [user, currentTenant, enableCache, cacheOptions]);

  // Setup real-time updates
  useEffect(() => {
    if (!enableRealTimeUpdates || !webSocketService.current || !crossTabSync.current) {
      return;
    }

    // Store callback ref to avoid stale closures
    permissionUpdateCallbackRef.current = handlePermissionUpdate;

    // WebSocket updates
    const wsCallback = (event: PermissionUpdateEvent) => {
      permissionUpdateCallbackRef.current?.(event);
    };
    webSocketService.current.onPermissionUpdate(wsCallback);

    // Cross-tab updates
    const crossTabCallback = (event: PermissionUpdateEvent) => {
      permissionUpdateCallbackRef.current?.(event);
    };
    crossTabSync.current.onPermissionUpdate(crossTabCallback);

    return () => {
      webSocketService.current?.offPermissionUpdate(wsCallback);
      crossTabSync.current?.offPermissionUpdate(crossTabCallback);
    };
  }, [enableRealTimeUpdates, handlePermissionUpdate]);

  // Fetch permissions when dependencies change
  useEffect(() => {
    if (isAuthenticated && user && currentTenant && permissions.length === 0 && !ongoingRequest) {
      fetchPermissions();
    }
  }, [isAuthenticated, user, currentTenant, permissions.length, ongoingRequest, fetchPermissions]);

  // Cleanup expired caches periodically
  useEffect(() => {
    if (!enableCache) return;

    const cleanupInterval = setInterval(() => {
      const cleanedCount = PermissionCacheService.cleanupExpiredCaches();
      if (cleanedCount > 0) {
        console.log(`🧹 Cleaned up ${cleanedCount} expired permission caches`);
      }
    }, 5 * 60 * 1000); // Every 5 minutes

    return () => clearInterval(cleanupInterval);
  }, [enableCache]);

  // Store user info for WebSocket authentication
  useEffect(() => {
    if (user && currentTenant && typeof window !== 'undefined') {
      localStorage.setItem('user_info', JSON.stringify({
        userId: user.id,
        tenantId: currentTenant.id
      }));
    }
  }, [user, currentTenant]);

  // All the permission checking methods (same as original)
  const hasPermission = useCallback((
    resource: PermissionResource,
    action: PermissionAction
  ): boolean => {
    if (!isAuthenticated || !user) {
      return false;
    }

    if (user.isPlatformAdmin) {
      return true;
    }

    const permissionName = createPermissionString(resource, action);

    if (!isValidResourceAction(resource, action)) {
      console.warn('EnhancedPermissionProvider.hasPermission: Invalid permission requested', {
        resource,
        action,
        permissionName,
        availableForResource: isValidPermission(permissionName)
      });
    }

    const hasAccess = permissions.some(p =>
      p.name === permissionName ||
      p.name === `${resource}.*` ||
      p.name === '*'
    );

    return hasAccess;
  }, [isAuthenticated, user, permissions]);

  const hasAnyPermission = useCallback((
    requiredPermissions: Array<{ resource: PermissionResource; action: PermissionAction }>
  ): boolean => {
    if (!isAuthenticated || !user) return false;
    if (user.isPlatformAdmin) return true;

    return requiredPermissions.some(({ resource, action }) => 
      hasPermission(resource, action)
    );
  }, [isAuthenticated, user, hasPermission]);

  const hasAllPermissions = useCallback((
    requiredPermissions: Array<{ resource: PermissionResource; action: PermissionAction }>
  ): boolean => {
    if (!isAuthenticated || !user) return false;
    if (user.isPlatformAdmin) return true;

    return requiredPermissions.every(({ resource, action }) => 
      hasPermission(resource, action)
    );
  }, [isAuthenticated, user, hasPermission]);

  const canAccessResource = useCallback((
    resource: PermissionResource,
    action: PermissionAction,
    resourceOwnerId?: string
  ): boolean => {
    if (!isAuthenticated || !user) return false;
    if (user.isPlatformAdmin) return true;

    if (!hasPermission(resource, action)) return false;
    if (!resourceOwnerId) return true;
    if (hasPermission(resource, PermissionAction.MANAGE)) return true;

    return user.id === resourceOwnerId;
  }, [isAuthenticated, user, hasPermission]);

  const isTenantAdmin = useMemo((): boolean => {
    if (!isAuthenticated || !user || !currentTenant) return false;
    if (user.isPlatformAdmin) return true;

    const tenantUser = user.tenants?.find(t => t.id === currentTenant.id);
    return tenantUser?.isTenantAdmin || false;
  }, [isAuthenticated, user, currentTenant]);

  const isSuperAdmin = useMemo((): boolean => {
    return user?.isPlatformAdmin || false;
  }, [user]);

  const getPermissionsByCategory = useCallback((): Record<string, Permission[]> => {
    return permissions.reduce((acc, permission) => {
      const category = permission.category || 'other';
      if (!acc[category]) {
        acc[category] = [];
      }
      acc[category].push(permission);
      return acc;
    }, {} as Record<string, Permission[]>);
  }, [permissions]);

  const getUserRoles = useCallback((): Role[] => {
    return roles;
  }, [roles]);

  const hasRole = useCallback((roleName: string): boolean => {
    if (!isAuthenticated || !user) return false;
    if (user.isPlatformAdmin) return true;

    return roles.some(role => role.name.toLowerCase() === roleName.toLowerCase());
  }, [isAuthenticated, user, roles]);

  const canPerformBulkAction = useCallback((
    resource: PermissionResource,
    action: PermissionAction,
    itemCount: number
  ): boolean => {
    if (!hasPermission(resource, action)) return false;

    if (itemCount > 100 && !hasPermission(resource, PermissionAction.MANAGE)) {
      return false;
    }

    return true;
  }, [hasPermission]);

  // Cache management methods
  const clearCache = useCallback(() => {
    if (user && currentTenant) {
      PermissionCacheService.invalidateCache(user.id, currentTenant.id);
      setLastCacheUpdate(null);
      setIsFromCache(false);
      console.log('🗑️ Permission cache cleared');
    }
  }, [user, currentTenant]);

  const cacheStats = useCallback(() => {
    const stats = PermissionCacheService.getCacheStats();
    return {
      ...stats,
      hitRate: cacheHitRate,
      lastUpdate: lastCacheUpdate,
      isFromCache,
      enabled: enableCache
    };
  }, [cacheHitRate, lastCacheUpdate, isFromCache, enableCache]);

  const contextValue: EnhancedPermissionContextValue = {
    permissions,
    roles,
    isLoading,
    error,
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    canAccessResource,
    isTenantAdmin,
    isSuperAdmin,
    refreshPermissions: fetchPermissions,
    getPermissionsByCategory,
    getUserRoles,
    hasRole,
    canPerformBulkAction,
    isCacheEnabled: enableCache,
    cacheStats,
    clearCache,
    lastCacheUpdate,
    isFromCache,
    cacheHitRate,
  };

  return (
    <EnhancedPermissionContext.Provider value={contextValue}>
      {children}
    </EnhancedPermissionContext.Provider>
  );
}

export function useEnhancedPermissions(): EnhancedPermissionContextValue {
  const context = useContext(EnhancedPermissionContext);
  if (context === undefined) {
    throw new Error('useEnhancedPermissions must be used within an EnhancedPermissionProvider');
  }
  return context;
}
