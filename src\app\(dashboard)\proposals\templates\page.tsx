'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { PageLayout } from '@/components/layout/page-layout';
import { PermissionGate } from '@/components/ui/permission-gate';
import { EmptyState } from '@/components/ui/empty-state';
import { PermissionResource, PermissionAction } from '@/types';
import { toast } from 'sonner';
import {
  Plus,
  Edit,
  Trash2,
  Star,
  Palette,
  AlertCircle,
  CheckCircle,
  ArrowLeft,
  AlertTriangle
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useSmoothNavigation } from '@/hooks/use-smooth-navigation';

interface DocumentTemplate {
  id: string;
  name: string;
  description?: string;
  category: string;
  templateType: string;
  filePath?: string;
  htmlTemplate?: string;
  isDefault: boolean;
  isActive: boolean;
  styling: any;
  branding: any;
  variables: string[];
  usageCount: number;
  lastUsedAt?: string;
  createdAt: string;
  updatedAt: string;
  createdBy?: {
    firstName: string;
    lastName: string;
  };
}

export default function ProposalTemplatesPage() {
  const { navigate } = useSmoothNavigation();

  // Actions for the page header
  const actions = (
    <>
      <Button
        variant="outline"
        onClick={() => navigate('/proposals')}
      >
        <ArrowLeft className="h-4 w-4 mr-2" />
        Back to Proposals
      </Button>
      <Button onClick={() => navigate('/proposals/templates/create')}>
        <Plus className="h-4 w-4 mr-2" />
        Create Template
      </Button>
    </>
  );

  return (
    <PermissionGate
      resource={PermissionResource.PROPOSALS}
      action={PermissionAction.READ}
      fallback={
        <PageLayout>
          <Card>
            <CardContent className="p-8">
              <EmptyState
                icon={<AlertTriangle className="w-12 h-12 text-muted-foreground" />}
                title="Access Denied"
                description="You don't have permission to manage templates. Please contact your administrator for access."
                action={{
                  label: 'Back to Proposals',
                  onClick: () => navigate('/proposals'),
                  variant: 'primary'
                }}
              />
            </CardContent>
          </Card>
        </PageLayout>
      }
    >
      <PageLayout
        title="Document Templates"
        description="Manage document templates for proposal generation with different branding and styling"
        actions={actions}
      >
        <TemplateManagement />
      </PageLayout>
    </PermissionGate>
  );
}

function TemplateManagement() {
  const { navigate } = useSmoothNavigation();
  const [templates, setTemplates] = useState<DocumentTemplate[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [categoryFilter, setCategoryFilter] = useState<string>('proposal');

  useEffect(() => {
    loadTemplates();
  }, [categoryFilter]);

  const loadTemplates = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/document-templates?category=${categoryFilter}&includeInactive=true`);
      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to load templates');
      }

      setTemplates(result.data.templates || []);
    } catch (error) {
      console.error('Error loading templates:', error);
      toast.error('Failed to load templates');
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteTemplate = async (templateId: string) => {
    if (!confirm('Are you sure you want to delete this template?')) return;

    try {
      const response = await fetch(`/api/document-templates/${templateId}`, {
        method: 'DELETE'
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to delete template');
      }

      toast.success('Template deleted successfully');
      loadTemplates();

    } catch (error) {
      console.error('Error deleting template:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to delete template');
    }
  };

  const handleSetDefault = async (templateId: string) => {
    try {
      const response = await fetch(`/api/document-templates/${templateId}/set-default`, {
        method: 'POST'
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to set default template');
      }

      toast.success('Template set as default');
      loadTemplates();

    } catch (error) {
      console.error('Error setting default template:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to set default template');
    }
  };

  const filteredTemplates = templates.filter(template => {
    const matchesSearch = template.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         template.description?.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         template.templateType.toLowerCase().includes(searchQuery.toLowerCase());
    
    return matchesSearch;
  });

  const getTemplatePreview = (template: DocumentTemplate) => {
    const branding = template.branding || {};
    const styling = template.styling || {};
    
    return {
      primaryColor: branding.primaryColor || styling.primaryColor || '#4f46e5',
      secondaryColor: branding.secondaryColor || styling.secondaryColor || '#10b981',
      fontFamily: styling.fontFamily || 'Arial'
    };
  };

  return (
    <div className="space-y-6">
      {/* Filters */}
      <div className="flex items-center space-x-4">
        <div className="flex-1 max-w-sm">
          <Input
            placeholder="Search templates..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        <Select value={categoryFilter} onValueChange={setCategoryFilter}>
          <SelectTrigger className="w-48">
            <SelectValue placeholder="Filter by category" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="proposal">Proposal</SelectItem>
            <SelectItem value="contract">Contract</SelectItem>
            <SelectItem value="report">Report</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Templates Grid */}
      {loading ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[...Array(6)].map((_, i) => (
            <Card key={i}>
              <CardContent className="p-6">
                <Skeleton className="h-20 w-full mb-4" />
                <Skeleton className="h-4 w-3/4 mb-2" />
                <Skeleton className="h-3 w-full mb-2" />
                <Skeleton className="h-3 w-1/2" />
              </CardContent>
            </Card>
          ))}
        </div>
      ) : filteredTemplates.length === 0 ? (
        <Card>
          <CardContent className="p-12 text-center">
            <Palette className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">No Templates Found</h3>
            <p className="text-muted-foreground mb-4">
              {searchQuery 
                ? 'No templates match your search criteria.'
                : 'Create your first document template to get started.'
              }
            </p>
            {!searchQuery && (
              <Button onClick={() => navigate('/proposals/templates/create')}>
                <Plus className="h-4 w-4 mr-2" />
                Create Template
              </Button>
            )}
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredTemplates.map((template) => {
            const preview = getTemplatePreview(template);
            
            return (
              <Card key={template.id} className="relative">
                <CardContent className="p-6">
                  {/* Template Preview */}
                  <div 
                    className="h-20 rounded-lg mb-4 border-2 flex items-center justify-center text-white font-medium text-sm"
                    style={{ 
                      backgroundColor: preview.primaryColor,
                      borderColor: preview.secondaryColor,
                      fontFamily: preview.fontFamily
                    }}
                  >
                    <div className="text-center">
                      <div className="opacity-90">Company Name</div>
                      <div className="text-xs opacity-75">Proposal Template</div>
                    </div>
                  </div>

                  {/* Template Info */}
                  <div className="space-y-3">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <h3 className="font-semibold text-lg">{template.name}</h3>
                        {template.description && (
                          <p className="text-sm text-muted-foreground line-clamp-2">
                            {template.description}
                          </p>
                        )}
                      </div>
                      {template.isDefault && (
                        <Badge variant="secondary" className="ml-2">
                          <Star className="h-3 w-3 mr-1" />
                          Default
                        </Badge>
                      )}
                    </div>

                    <div className="flex items-center justify-between text-sm">
                      <div className="flex items-center space-x-2">
                        <Badge variant="outline">{template.templateType}</Badge>
                      </div>
                      <div className={cn(
                        "flex items-center space-x-1",
                        template.isActive ? "text-green-600" : "text-red-600"
                      )}>
                        {template.isActive ? (
                          <CheckCircle className="h-3 w-3" />
                        ) : (
                          <AlertCircle className="h-3 w-3" />
                        )}
                        <span className="text-xs">
                          {template.isActive ? 'Active' : 'Inactive'}
                        </span>
                      </div>
                    </div>

                    <div className="text-xs text-muted-foreground">
                      Used {template.usageCount} times
                      {template.createdBy && (
                        <span> • Created by {template.createdBy.firstName} {template.createdBy.lastName}</span>
                      )}
                    </div>

                    {/* Actions */}
                    <div className="flex items-center justify-between pt-2 border-t">
                      <div className="flex items-center space-x-1">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => navigate(`/proposals/templates/${template.id}/edit`)}
                        >
                          <Edit className="h-3 w-3" />
                        </Button>
                        {!template.isDefault && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleSetDefault(template.id)}
                          >
                            <Star className="h-3 w-3" />
                          </Button>
                        )}
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDeleteTemplate(template.id)}
                          className="text-destructive hover:text-destructive"
                        >
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>
      )}


    </div>
  );
}