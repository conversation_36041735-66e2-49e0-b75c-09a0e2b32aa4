import { prisma } from '@/lib/prisma';

/**
 * Setup default automation triggers for a tenant
 */
export async function setupDefaultAutomationTriggers(tenantId: string, createdBy: string = 'system') {
  console.log(`Setting up default automation triggers for tenant: ${tenantId}`);

  const defaultTriggers = [
    {
      name: 'Opportunity Closed Won - Create Handover',
      description: 'Automatically create a handover when an opportunity is marked as "Closed Won"',
      triggerType: 'opportunity_status_change',
      triggerConditions: {
        toStatus: 'Closed Won'
      },
      actions: [
        {
          type: 'create_handover',
          config: {
            priority: 'high',
            defaultTemplateId: null, // Will be set to default template if available
            defaultDeliveryManagerId: null, // Will be set to first delivery manager if available
            defaultTeamIds: [], // Will be populated with delivery team members
            scheduledDelayHours: 24 // Schedule handover for 24 hours later
          }
        },
        {
          type: 'send_notification',
          config: {
            notificationType: 'handover_created',
            recipients: ['sales_rep', 'delivery_manager', 'delivery_team'],
            template: 'opportunity_closed_won_handover'
          }
        }
      ],
      isActive: true,
      priority: 9
    },
    {
      name: 'Opportunity Closed Won - Create Project',
      description: 'Automatically create a project structure when an opportunity is closed won',
      triggerType: 'opportunity_status_change',
      triggerConditions: {
        toStatus: 'Closed Won'
      },
      actions: [
        {
          type: 'create_project',
          config: {
            createInitialMilestones: true,
            assignDeliveryTeam: true,
            notifyStakeholders: true
          }
        }
      ],
      isActive: false, // Disabled by default - can be enabled if needed
      priority: 7
    },
    {
      name: 'Opportunity Moved to Proposal - Notify Team',
      description: 'Send notifications when an opportunity moves to proposal stage',
      triggerType: 'opportunity_status_change',
      triggerConditions: {
        toStatus: 'Proposal'
      },
      actions: [
        {
          type: 'send_notification',
          config: {
            notificationType: 'proposal_stage_reached',
            recipients: ['sales_rep', 'sales_manager'],
            template: 'opportunity_proposal_stage'
          }
        }
      ],
      isActive: true,
      priority: 5
    },
    {
      name: 'Opportunity Moved to Negotiation - High Priority',
      description: 'Escalate notifications when opportunity reaches negotiation stage',
      triggerType: 'opportunity_status_change',
      triggerConditions: {
        toStatus: 'Negotiation'
      },
      actions: [
        {
          type: 'send_notification',
          config: {
            notificationType: 'negotiation_stage_reached',
            recipients: ['sales_rep', 'sales_manager', 'delivery_manager'],
            template: 'opportunity_negotiation_stage',
            priority: 'high'
          }
        }
      ],
      isActive: true,
      priority: 8
    },
    {
      name: 'Handover Completion - Create Project',
      description: 'Automatically create project when handover is completed',
      triggerType: 'handover_completion',
      triggerConditions: {
        status: 'completed',
        checklistProgress: 100
      },
      actions: [
        {
          type: 'create_project',
          config: {
            useHandoverData: true,
            assignTeamFromHandover: true,
            createFromTemplate: true
          }
        },
        {
          type: 'send_notification',
          config: {
            notificationType: 'project_created_from_handover',
            recipients: ['delivery_manager', 'delivery_team', 'sales_rep'],
            template: 'project_creation_notification'
          }
        }
      ],
      isActive: true,
      priority: 9
    }
  ];

  const createdTriggers = [];

  for (const triggerData of defaultTriggers) {
    try {
      // Check if trigger already exists
      const existingTrigger = await prisma.automationTrigger.findFirst({
        where: {
          tenantId,
          name: triggerData.name
        }
      });

      if (existingTrigger) {
        console.log(`Trigger "${triggerData.name}" already exists, skipping...`);
        continue;
      }

      // Get default template for handover creation
      if (triggerData.actions.some(action => action.type === 'create_handover')) {
        const defaultTemplate = await prisma.handoverChecklistTemplate.findFirst({
          where: {
            tenantId,
            isDefault: true,
            isActive: true
          }
        });

        if (defaultTemplate) {
          const handoverAction = triggerData.actions.find(action => action.type === 'create_handover');
          if (handoverAction) {
            handoverAction.config.defaultTemplateId = defaultTemplate.id;
          }
        }

        // Get default delivery manager
        const deliveryManager = await prisma.user.findFirst({
          where: {
            tenantUsers: {
              some: {
                tenantId,
                role: 'delivery_manager',
                status: 'active'
              }
            }
          }
        });

        if (deliveryManager) {
          const handoverAction = triggerData.actions.find(action => action.type === 'create_handover');
          if (handoverAction) {
            handoverAction.config.defaultDeliveryManagerId = deliveryManager.id;
          }
        }

        // Get delivery team members
        const deliveryTeam = await prisma.user.findMany({
          where: {
            tenantUsers: {
              some: {
                tenantId,
                role: { in: ['delivery_manager', 'project_manager', 'developer'] },
                status: 'active'
              }
            }
          },
          select: { id: true },
          take: 5 // Limit to 5 team members
        });

        if (deliveryTeam.length > 0) {
          const handoverAction = triggerData.actions.find(action => action.type === 'create_handover');
          if (handoverAction) {
            handoverAction.config.defaultTeamIds = deliveryTeam.map(user => user.id);
          }
        }
      }

      // Create the trigger
      const trigger = await prisma.automationTrigger.create({
        data: {
          tenantId,
          name: triggerData.name,
          description: triggerData.description,
          triggerType: triggerData.triggerType,
          triggerConditions: triggerData.triggerConditions,
          actions: triggerData.actions,
          isActive: triggerData.isActive,
          priority: triggerData.priority,
          createdById: createdBy
        }
      });

      createdTriggers.push(trigger);
      console.log(`✅ Created trigger: ${trigger.name}`);

    } catch (error) {
      console.error(`❌ Failed to create trigger "${triggerData.name}":`, error);
    }
  }

  console.log(`✅ Setup completed. Created ${createdTriggers.length} automation triggers for tenant ${tenantId}`);
  return createdTriggers;
}

/**
 * Setup automation triggers for all existing tenants
 */
export async function setupAutomationForAllTenants() {
  console.log('Setting up automation triggers for all existing tenants...');

  const tenants = await prisma.tenant.findMany({
    where: {
      status: 'active'
    },
    select: {
      id: true,
      name: true
    }
  });

  console.log(`Found ${tenants.length} active tenants`);

  for (const tenant of tenants) {
    try {
      console.log(`\n🏢 Setting up automation for tenant: ${tenant.name} (${tenant.id})`);
      await setupDefaultAutomationTriggers(tenant.id);
    } catch (error) {
      console.error(`❌ Failed to setup automation for tenant ${tenant.name}:`, error);
    }
  }

  console.log('\n✅ Automation setup completed for all tenants');
}

/**
 * Test automation trigger
 */
export async function testAutomationTrigger(tenantId: string, triggerId: string) {
  console.log(`Testing automation trigger ${triggerId} for tenant ${tenantId}`);

  const trigger = await prisma.automationTrigger.findUnique({
    where: { id: triggerId, tenantId }
  });

  if (!trigger) {
    throw new Error('Trigger not found');
  }

  console.log(`Trigger: ${trigger.name}`);
  console.log(`Type: ${trigger.triggerType}`);
  console.log(`Conditions:`, trigger.triggerConditions);
  console.log(`Actions:`, trigger.actions);
  console.log(`Active: ${trigger.isActive}`);

  return {
    trigger,
    wouldExecute: trigger.isActive,
    testResult: 'Test completed successfully'
  };
}

// Export for use in other scripts
export { setupDefaultAutomationTriggers as default };
