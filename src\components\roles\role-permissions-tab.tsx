'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { useToast } from '@/hooks/use-toast';
import { PermissionGate } from '@/components/ui/permission-gate';
import { PermissionManager } from '@/components/roles/permission-manager';
import { PermissionAction, PermissionResource, Permission } from '@/types';
import {
  KeyIcon,
  PencilIcon,
  CheckIcon,
  XMarkIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';

interface RolePermissionsTabProps {
  roleId: string;
  roleName: string;
  isSystemRole: boolean;
  initialPermissions: Permission[];
}

export function RolePermissionsTab({ 
  roleId, 
  roleName, 
  isSystemRole, 
  initialPermissions 
}: RolePermissionsTabProps) {
  const [permissions, setPermissions] = useState<Permission[]>([]);
  const [groupedPermissions, setGroupedPermissions] = useState<Record<string, Permission[]>>({});
  const [rolePermissions, setRolePermissions] = useState<Permission[]>(initialPermissions);
  const [selectedPermissions, setSelectedPermissions] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const { toast } = useToast();

  useEffect(() => {
    setSelectedPermissions(rolePermissions.map(p => p.id));
  }, [rolePermissions]);

  useEffect(() => {
    if (isEditing) {
      fetchAllPermissions();
    }
  }, [isEditing]);

  const fetchAllPermissions = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch('/api/permissions');
      if (response.ok) {
        const data = await response.json();
        const allPermissions = Array.isArray(data.data?.permissions) ? data.data.permissions : [];
        setPermissions(allPermissions);

        // Use the pre-grouped permissions from the API if available, otherwise group them
        if (data.data?.groupedPermissions) {
          setGroupedPermissions(data.data.groupedPermissions);
        } else {
          // Group permissions by category
          const grouped = allPermissions.reduce((acc: Record<string, Permission[]>, permission: Permission) => {
            const category = permission.category || 'other';
            if (!acc[category]) {
              acc[category] = [];
            }
            acc[category].push(permission);
            return acc;
          }, {});
          setGroupedPermissions(grouped);
        }
      } else {
        const errorData = await response.json();
        setError(errorData.error || 'Failed to fetch permissions');
        // Set empty arrays as fallback
        setPermissions([]);
        setGroupedPermissions({});
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch permissions');
      // Set empty arrays as fallback
      setPermissions([]);
      setGroupedPermissions({});
    } finally {
      setIsLoading(false);
    }
  };

  const handlePermissionToggle = (permissionId: string) => {
    setSelectedPermissions(prev => 
      prev.includes(permissionId)
        ? prev.filter(id => id !== permissionId)
        : [...prev, permissionId]
    );
  };

  const handleSelectAllCategory = (category: string, selected: boolean) => {
    const categoryPermissions = groupedPermissions[category] || [];
    const categoryPermissionIds = categoryPermissions.map(p => p.id);

    if (selected) {
      setSelectedPermissions(prev => [
        ...prev.filter(id => !categoryPermissionIds.includes(id)),
        ...categoryPermissionIds
      ]);
    } else {
      setSelectedPermissions(prev => 
        prev.filter(id => !categoryPermissionIds.includes(id))
      );
    }
  };

  const handleSavePermissions = async () => {
    try {
      setIsSaving(true);
      
      const response = await fetch(`/api/roles/${roleId}/permissions`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ permissions: selectedPermissions })
      });

      if (response.ok) {
        // Update role permissions with the new selection
        const updatedPermissions = permissions.filter(p => selectedPermissions.includes(p.id));
        setRolePermissions(updatedPermissions);
        setIsEditing(false);
        
        toast({
          title: 'Success',
          description: 'Role permissions updated successfully',
        });
      } else {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update permissions');
      }
    } catch (err) {
      toast({
        title: 'Error',
        description: err instanceof Error ? err.message : 'Failed to update permissions',
        variant: 'destructive',
      });
    } finally {
      setIsSaving(false);
    }
  };

  const handleCancelEdit = () => {
    setSelectedPermissions(rolePermissions.map(p => p.id));
    setIsEditing(false);
    setError(null);
  };

  const canEditPermissions = () => {
    // For system roles, check MANAGE_SYSTEM_ROLES permission
    // For regular roles, check UPDATE permission
    return isSystemRole 
      ? true // Will be checked by PermissionGate
      : true; // Will be checked by PermissionGate
  };

  const getPermissionResource = () => {
    return isSystemRole ? PermissionResource.SYSTEM_ROLES : PermissionResource.ROLES;
  };

  const getPermissionAction = () => {
    return isSystemRole ? PermissionAction.MANAGE : PermissionAction.UPDATE;
  };

  // Group current role permissions by category for display
  const groupedRolePermissions = rolePermissions.reduce((acc: Record<string, Permission[]>, permission) => {
    const category = permission.category || 'other';
    if (!acc[category]) {
      acc[category] = [];
    }
    acc[category].push(permission);
    return acc;
  }, {});

  if (error && !isEditing) {
    return (
      <Card className="border-red-200 bg-red-50">
        <CardContent className="p-12 text-center">
          <ExclamationTriangleIcon className="w-12 h-12 text-red-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Error Loading Permissions</h3>
          <p className="text-gray-500 mb-4">{error}</p>
          <Button onClick={() => setError(null)}>Try Again</Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Role Permissions</CardTitle>
              <p className="text-sm text-gray-600 mt-1">
                {rolePermissions.length} permission{rolePermissions.length !== 1 ? 's' : ''} assigned to this role
              </p>
            </div>
            
            {!isEditing && canEditPermissions() && (
              <PermissionGate
                resource={getPermissionResource()}
                action={getPermissionAction()}
              >
                <Button
                  variant="outline"
                  onClick={() => setIsEditing(true)}
                  className="flex items-center gap-2"
                >
                  <PencilIcon className="w-4 h-4" />
                  Edit Permissions
                </Button>
              </PermissionGate>
            )}

            {isEditing && (
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  onClick={handleCancelEdit}
                  disabled={isSaving}
                  className="flex items-center gap-2"
                >
                  <XMarkIcon className="w-4 h-4" />
                  Cancel
                </Button>
                <Button
                  onClick={handleSavePermissions}
                  disabled={isSaving}
                  className="flex items-center gap-2"
                >
                  {isSaving ? (
                    <LoadingSpinner className="w-4 h-4" />
                  ) : (
                    <CheckIcon className="w-4 h-4" />
                  )}
                  Save Changes
                </Button>
              </div>
            )}
          </div>
        </CardHeader>
      </Card>

      {/* Permissions Content */}
      {isEditing ? (
        <Card>
          <CardHeader>
            <CardTitle>Edit Permissions</CardTitle>
            <p className="text-sm text-gray-600">
              Select the permissions this role should have. Selected: {selectedPermissions.length}
            </p>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="flex items-center justify-center py-12">
                <LoadingSpinner />
              </div>
            ) : error ? (
              <div className="text-center py-8">
                <ExclamationTriangleIcon className="w-12 h-12 text-red-400 mx-auto mb-4" />
                <p className="text-red-600 mb-4">{error}</p>
                <Button onClick={fetchAllPermissions}>Retry</Button>
              </div>
            ) : (
              <PermissionManager
                permissions={permissions}
                groupedPermissions={groupedPermissions}
                selectedPermissions={selectedPermissions}
                onPermissionToggle={handlePermissionToggle}
                onSelectAllCategory={handleSelectAllCategory}
                isSystemRole={isSystemRole}
                disabled={isSaving}
                showSearch={true}
                showStats={true}
              />
            )}
          </CardContent>
        </Card>
      ) : (
        // Display current permissions
        <Card>
          <CardContent className="p-6">
            {rolePermissions.length === 0 ? (
              <div className="text-center py-8">
                <KeyIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500">No permissions assigned to this role</p>
              </div>
            ) : (
              <div className="space-y-6">
                {Object.entries(groupedRolePermissions).map(([category, categoryPermissions]) => (
                  <div key={category}>
                    <h4 className="font-medium text-gray-900 mb-3 capitalize">
                      {category.replace('_', ' ')} ({categoryPermissions.length})
                    </h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                      {categoryPermissions.map((permission) => (
                        <div
                          key={permission.id}
                          className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg"
                        >
                          <KeyIcon className="w-4 h-4 text-gray-400 flex-shrink-0" />
                          <div className="min-w-0 flex-1">
                            <p className="text-sm font-medium text-gray-900 truncate">
                              {permission.name}
                            </p>
                            {permission.description && (
                              <p className="text-xs text-gray-500 truncate">
                                {permission.description}
                              </p>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
}
