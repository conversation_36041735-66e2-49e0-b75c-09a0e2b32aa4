import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { hasPermission } from '@/lib/permissions';
import { PermissionResource, PermissionAction } from '@/types';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';

// Update validation schema
const updateProposalSchema = z.object({
  title: z.string().min(1, 'Title is required').max(255).optional(),
  content: z.string().optional(),
  status: z.enum(['draft', 'generating', 'generated', 'reviewed', 'approved', 'sent', 'accepted', 'rejected']).optional(),
  totalAmount: z.number().min(0).optional()
});

/**
 * GET /api/proposals/[id]
 * Get a specific proposal
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user's current tenant
    const currentTenant = session.user.tenants?.[0];
    if (!currentTenant) {
      return NextResponse.json({ error: 'No tenant access' }, { status: 403 });
    }

    // Check permission for proposal reading
    const hasAccess = await hasPermission(
      PermissionResource.PROPOSALS,
      PermissionAction.READ,
      currentTenant.id
    );

    if (!hasAccess) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const proposalId = params.id;

    // Get proposal with all related data
    const proposal = await prisma.proposal.findFirst({
      where: {
        id: proposalId,
        tenantId: currentTenant.id
      },
      include: {
        tenant: true,
        opportunity: {
          select: {
            id: true,
            title: true,
            value: true,
            stage: true
          }
        },
        createdBy: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true
          }
        },
        sections: {
          include: {
            createdBy: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true
              }
            }
          },
          orderBy: {
            orderIndex: 'asc'
          }
        },
        charts: {
          include: {
            createdBy: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true
              }
            }
          },
          orderBy: {
            orderIndex: 'asc'
          }
        }
      }
    });

    if (!proposal) {
      return NextResponse.json(
        { error: 'Proposal not found or access denied' },
        { status: 404 }
      );
    }

    // Calculate statistics
    const completedSections = proposal.sections.filter(s => s.status === 'generated' || s.status === 'approved').length;
    const renderedCharts = proposal.charts.filter(c => c.status === 'rendered' || c.status === 'approved').length;
    const totalWordCount = proposal.sections.reduce((sum, section) => sum + (section.wordCount || 0), 0);
    const totalTokensUsed = proposal.sections.reduce((sum, section) => sum + (section.tokensUsed || 0), 0);

    const proposalWithStats = {
      ...proposal,
      statistics: {
        totalSections: proposal.sections.length,
        completedSections,
        totalCharts: proposal.charts.length,
        renderedCharts,
        totalWordCount,
        totalTokensUsed,
        completionPercentage: proposal.sections.length > 0 
          ? Math.round((completedSections / proposal.sections.length) * 100)
          : 0
      }
    };

    return NextResponse.json({
      success: true,
      data: proposalWithStats,
      message: 'Proposal retrieved successfully'
    });

  } catch (error) {
    console.error('Error retrieving proposal:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/proposals/[id]
 * Update a specific proposal
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user's current tenant
    const currentTenant = session.user.tenants?.[0];
    if (!currentTenant) {
      return NextResponse.json({ error: 'No tenant access' }, { status: 403 });
    }

    // Check permission for proposal updating
    const hasAccess = await hasPermission(
      PermissionResource.PROPOSALS,
      PermissionAction.UPDATE,
      currentTenant.id
    );

    if (!hasAccess) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const proposalId = params.id;
    const body = await request.json();
    const validatedData = updateProposalSchema.parse(body);

    // Verify proposal exists and belongs to tenant
    const existingProposal = await prisma.proposal.findFirst({
      where: {
        id: proposalId,
        tenantId: currentTenant.id
      }
    });

    if (!existingProposal) {
      return NextResponse.json(
        { error: 'Proposal not found or access denied' },
        { status: 404 }
      );
    }

    // Update the proposal
    const updatedProposal = await prisma.proposal.update({
      where: { id: proposalId },
      data: {
        ...validatedData,
        updatedAt: new Date()
      },
      include: {
        createdBy: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true
          }
        },
        sections: {
          select: {
            id: true,
            status: true
          }
        },
        charts: {
          select: {
            id: true,
            status: true
          }
        }
      }
    });

    return NextResponse.json({
      success: true,
      data: updatedProposal,
      message: 'Proposal updated successfully'
    });

  } catch (error) {
    console.error('Error updating proposal:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/proposals/[id]
 * Delete a specific proposal
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user's current tenant
    const currentTenant = session.user.tenants?.[0];
    if (!currentTenant) {
      return NextResponse.json({ error: 'No tenant access' }, { status: 403 });
    }

    // Check permission for proposal deletion
    const hasAccess = await hasPermission(
      PermissionResource.PROPOSALS,
      PermissionAction.DELETE,
      currentTenant.id
    );

    if (!hasAccess) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const proposalId = params.id;

    // Verify proposal exists and belongs to tenant
    const existingProposal = await prisma.proposal.findFirst({
      where: {
        id: proposalId,
        tenantId: currentTenant.id
      }
    });

    if (!existingProposal) {
      return NextResponse.json(
        { error: 'Proposal not found or access denied' },
        { status: 404 }
      );
    }

    // Delete the proposal (cascade will handle sections and charts)
    await prisma.proposal.delete({
      where: { id: proposalId }
    });

    return NextResponse.json({
      success: true,
      message: 'Proposal deleted successfully'
    });

  } catch (error) {
    console.error('Error deleting proposal:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
