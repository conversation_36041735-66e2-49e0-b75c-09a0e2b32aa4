import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { handoverDocumentService } from '@/services/handover-documents';
import { z } from 'zod';

// Request validation schemas
const updateDocumentSchema = z.object({
  category: z.enum(['requirements', 'contracts', 'technical', 'legal', 'financial']).optional(),
  isRequired: z.boolean().optional(),
  isApproved: z.boolean().optional(),
  isClientVisible: z.boolean().optional(),
  accessLevel: z.enum(['internal', 'delivery_team', 'client']).optional(),
  orderIndex: z.number().int().min(0).optional(),
  notes: z.string().optional(),
});

interface RouteParams {
  params: {
    docId: string;
  };
}

/**
 * PUT /api/handovers/documents/[docId] - Update a handover document
 */
export async function PUT(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get tenantId from user's tenants array (users have one tenant in this system)
    let tenantId: string | null = null;
    if (session.user.tenants && session.user.tenants.length > 0) {
      tenantId = session.user.tenants[0].id;
    }

    if (!tenantId) {
      return NextResponse.json({ error: 'Tenant not found' }, { status: 400 });
    }

    const body = await request.json();
    const validatedData = updateDocumentSchema.parse(body);

    const resolvedParams = await params;
    const handoverDocument = await handoverDocumentService.updateHandoverDocument(
      tenantId,
      resolvedParams.docId,
      validatedData,
      session.user.id
    );

    return NextResponse.json(handoverDocument);
  } catch (error) {
    console.error('Error updating handover document:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request data', details: error.errors },
        { status: 400 }
      );
    }

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to update handover document' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/handovers/documents/[docId] - Remove a document from a handover
 */
export async function DELETE(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get tenantId from user's tenants array (users have one tenant in this system)
    let tenantId: string | null = null;
    if (session.user.tenants && session.user.tenants.length > 0) {
      tenantId = session.user.tenants[0].id;
    }

    if (!tenantId) {
      return NextResponse.json({ error: 'Tenant not found' }, { status: 400 });
    }

    const resolvedParams = await params;
    await handoverDocumentService.removeDocumentFromHandover(
      tenantId,
      resolvedParams.docId
    );

    return NextResponse.json({ message: 'Document removed from handover successfully' });
  } catch (error) {
    console.error('Error removing document from handover:', error);
    
    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to remove document from handover' },
      { status: 500 }
    );
  }
}
