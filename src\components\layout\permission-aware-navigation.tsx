'use client';

import React from 'react';
import { SmoothLink } from '@/components/ui/smooth-link';
import { usePathname } from 'next/navigation';
import { usePermissions } from '@/components/providers/permission-provider';
import { PermissionAction, PermissionResource } from '@/types';
import {
  HomeIcon,
  UserGroupIcon,
  ShieldCheckIcon,
  DocumentTextIcon,
  ChartBarIcon,
  CogIcon,
  BuildingOfficeIcon,
  UserIcon,
  ClipboardDocumentListIcon,
  BriefcaseIcon,
  CurrencyDollarIcon,
  ChatBubbleLeftRightIcon,
  CalendarIcon,
  BellIcon,
  RectangleGroupIcon,
  KeyIcon
} from '@heroicons/react/24/outline';

export interface NavigationItem {
  name: string;
  href: string;
  icon: React.ComponentType<{ className?: string }>;
  permission?: {
    resource: PermissionResource;
    action: PermissionAction;
  };
  badge?: string | number;
  children?: NavigationItem[];
  adminOnly?: boolean;
  superAdminOnly?: boolean;
}

const navigationItems: NavigationItem[] = [
  {
    name: 'Dashboard',
    href: '/dashboard',
    icon: HomeIcon,
  },
  {
    name: 'Contacts',
    href: '/contacts',
    icon: UserIcon,
    permission: {
      resource: PermissionResource.CONTACTS,
      action: PermissionAction.READ,
    },
  },
  {
    name: 'Leads',
    href: '/leads',
    icon: ClipboardDocumentListIcon,
    permission: {
      resource: PermissionResource.LEADS,
      action: PermissionAction.READ,
    },
  },
  {
    name: 'Opportunities',
    href: '/opportunities',
    icon: BriefcaseIcon,
    permission: {
      resource: PermissionResource.OPPORTUNITIES,
      action: PermissionAction.READ,
    },
  },
  {
    name: 'Proposals',
    href: '/proposals',
    icon: DocumentTextIcon,
    permission: {
      resource: PermissionResource.PROPOSALS,
      action: PermissionAction.READ,
    },
  },
  {
    name: 'Projects',
    href: '/projects',
    icon: BuildingOfficeIcon,
    permission: {
      resource: PermissionResource.PROJECTS,
      action: PermissionAction.READ,
    },
  },
  {
    name: 'Communications',
    href: '/communications',
    icon: ChatBubbleLeftRightIcon,
    permission: {
      resource: PermissionResource.CONTACTS,
      action: PermissionAction.READ,
    },
    children: [
      {
        name: 'Email Campaigns',
        href: '/communications/email',
        icon: BellIcon,
        permission: {
          resource: PermissionResource.CONTACTS,
          action: PermissionAction.READ,
        },
      },
      {
        name: 'SMS Campaigns',
        href: '/communications/sms',
        icon: ChatBubbleLeftRightIcon,
        permission: {
          resource: PermissionResource.CONTACTS,
          action: PermissionAction.READ,
        },
      },
    ],
  },
  {
    name: 'Calendar',
    href: '/calendar',
    icon: CalendarIcon,
    permission: {
      resource: PermissionResource.CONTACTS,
      action: PermissionAction.READ,
    },
  },
  {
    name: 'Settings',
    href: '/settings',
    icon: CogIcon,
    permission: {
      resource: PermissionResource.SETTINGS,
      action: PermissionAction.READ,
    },
    children: [
      {
        name: 'General',
        href: '/settings/general',
        icon: CogIcon,
        permission: {
          resource: PermissionResource.SETTINGS,
          action: PermissionAction.READ,
        },
      },
      {
        name: 'Role Management',
        href: '/settings/roles',
        icon: KeyIcon,
        permission: {
          resource: PermissionResource.ROLES,
          action: PermissionAction.READ,
        },
      },
      {
        name: 'Team Management',
        href: '/settings/team',
        icon: RectangleGroupIcon,
        permission: {
          resource: PermissionResource.USERS,
          action: PermissionAction.READ,
        },
      },
      {
        name: 'Notifications',
        href: '/settings/notifications',
        icon: BellIcon,
        permission: {
          resource: PermissionResource.SETTINGS,
          action: PermissionAction.READ,
        },
      },
      {
        name: 'Security',
        href: '/settings/security',
        icon: ShieldCheckIcon,
        permission: {
          resource: PermissionResource.SETTINGS,
          action: PermissionAction.READ,
        },
      },
    ],
  },
  {
    name: 'Reports',
    href: '/reports',
    icon: ChartBarIcon,
    permission: {
      resource: PermissionResource.REPORTS,
      action: PermissionAction.READ,
    },
    children: [
      {
        name: 'Sales Reports',
        href: '/reports/sales',
        icon: CurrencyDollarIcon,
        permission: {
          resource: PermissionResource.REPORTS,
          action: PermissionAction.READ,
        },
      },
      {
        name: 'User Activity',
        href: '/reports/activity',
        icon: UserGroupIcon,
        permission: {
          resource: PermissionResource.REPORTS,
          action: PermissionAction.READ,
        },
      },
    ],
  },
  {
    name: 'Administration',
    href: '/admin',
    icon: CogIcon,
    adminOnly: true,
    children: [
      {
        name: 'User Management',
        href: '/admin/users',
        icon: UserGroupIcon,
        permission: {
          resource: PermissionResource.USERS,
          action: PermissionAction.READ,
        },
        adminOnly: true,
      },
      {
        name: 'Role Management',
        href: '/admin/roles',
        icon: ShieldCheckIcon,
        permission: {
          resource: PermissionResource.ROLES,
          action: PermissionAction.READ,
        },
        adminOnly: true,
      },
      {
        name: 'Permission Dashboard',
        href: '/admin/permissions',
        icon: ShieldCheckIcon,
        permission: {
          resource: PermissionResource.ROLES,
          action: PermissionAction.READ,
        },
        adminOnly: true,
      },
      {
        name: 'Tenant Settings',
        href: '/admin/settings',
        icon: CogIcon,
        permission: {
          resource: PermissionResource.SETTINGS,
          action: PermissionAction.READ,
        },
        adminOnly: true,
      },
    ],
  },
  {
    name: 'Super Admin',
    href: '/super-admin',
    icon: ShieldCheckIcon,
    superAdminOnly: true,
    children: [
      {
        name: 'Platform Overview',
        href: '/super-admin/overview',
        icon: ChartBarIcon,
        superAdminOnly: true,
      },
      {
        name: 'Tenant Management',
        href: '/super-admin/tenants',
        icon: BuildingOfficeIcon,
        superAdminOnly: true,
      },
      {
        name: 'Platform Users',
        href: '/super-admin/users',
        icon: UserGroupIcon,
        superAdminOnly: true,
      },
      {
        name: 'System Settings',
        href: '/super-admin/settings',
        icon: CogIcon,
        superAdminOnly: true,
      },
    ],
  },
];

export function PermissionAwareNavigation() {
  const pathname = usePathname();
  const { hasPermission, isTenantAdmin, isSuperAdmin } = usePermissions();

  const shouldShowItem = (item: NavigationItem): boolean => {
    // Check super admin only items
    if (item.superAdminOnly && !isSuperAdmin) {
      return false;
    }

    // Check admin only items
    if (item.adminOnly && !isTenantAdmin && !isSuperAdmin) {
      return false;
    }

    // Check permission requirements
    if (item.permission) {
      return hasPermission(item.permission.resource, item.permission.action);
    }

    return true;
  };

  const renderNavigationItem = (item: NavigationItem, level: number = 0) => {
    if (!shouldShowItem(item)) {
      return null;
    }

    const isActive = pathname === item.href || pathname.startsWith(item.href + '/');
    const hasChildren = item.children && item.children.length > 0;
    const visibleChildren = hasChildren ? item.children!.filter(shouldShowItem) : [];

    return (
      <div key={item.name}>
        <SmoothLink
          href={item.href}
          className={`
            group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors
            ${level === 0 ? 'pl-3' : 'pl-8'}
            ${isActive 
              ? 'bg-blue-100 text-blue-900 border-r-2 border-blue-500' 
              : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
            }
          `}
        >
          <item.icon
            className={`
              mr-3 flex-shrink-0 h-5 w-5 transition-colors
              ${isActive ? 'text-blue-500' : 'text-gray-400 group-hover:text-gray-500'}
            `}
          />
          <span className="flex-1">{item.name}</span>
          
          {item.badge && (
            <span className="ml-3 inline-block py-0.5 px-2 text-xs font-medium rounded-full bg-blue-100 text-blue-800">
              {item.badge}
            </span>
          )}
          
          {hasChildren && visibleChildren.length > 0 && (
            <svg
              className={`
                ml-3 h-4 w-4 transition-transform
                ${isActive ? 'rotate-90' : ''}
              `}
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          )}
        </SmoothLink>

        {/* Render children if expanded */}
        {hasChildren && visibleChildren.length > 0 && isActive && (
          <div className="mt-1 space-y-1">
            {visibleChildren.map(child => renderNavigationItem(child, level + 1))}
          </div>
        )}
      </div>
    );
  };

  return (
    <nav className="space-y-1">
      {navigationItems.map(item => renderNavigationItem(item))}
    </nav>
  );
}

// Breadcrumb component with permission awareness
export function PermissionAwareBreadcrumb() {
  const pathname = usePathname();
  const { hasPermission } = usePermissions();

  const generateBreadcrumbs = () => {
    const pathSegments = pathname.split('/').filter(Boolean);
    const breadcrumbs = [];

    let currentPath = '';
    for (const segment of pathSegments) {
      currentPath += `/${segment}`;
      
      // Find matching navigation item
      const findItem = (items: NavigationItem[]): NavigationItem | null => {
        for (const item of items) {
          if (item.href === currentPath) {
            return item;
          }
          if (item.children) {
            const found = findItem(item.children);
            if (found) return found;
          }
        }
        return null;
      };

      const navItem = findItem(navigationItems);
      
      if (navItem) {
        // Check if user has permission to see this breadcrumb
        if (navItem.permission) {
          if (!hasPermission(navItem.permission.resource, navItem.permission.action)) {
            continue;
          }
        }

        breadcrumbs.push({
          name: navItem.name,
          href: currentPath,
          icon: navItem.icon,
        });
      } else {
        // Fallback for dynamic routes
        breadcrumbs.push({
          name: segment.charAt(0).toUpperCase() + segment.slice(1),
          href: currentPath,
        });
      }
    }

    return breadcrumbs;
  };

  const breadcrumbs = generateBreadcrumbs();

  if (breadcrumbs.length <= 1) {
    return null;
  }

  return (
    <nav className="flex" aria-label="Breadcrumb">
      <ol className="flex items-center space-x-4">
        <li>
          <div>
            <SmoothLink href="/dashboard" className="text-gray-400 hover:text-gray-500">
              <HomeIcon className="flex-shrink-0 h-5 w-5" />
              <span className="sr-only">Home</span>
            </SmoothLink>
          </div>
        </li>
        
        {breadcrumbs.map((breadcrumb, index) => (
          <li key={breadcrumb.href}>
            <div className="flex items-center">
              <svg
                className="flex-shrink-0 h-5 w-5 text-gray-300"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path
                  fillRule="evenodd"
                  d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                  clipRule="evenodd"
                />
              </svg>
              
              {index === breadcrumbs.length - 1 ? (
                <span className="ml-4 text-sm font-medium text-gray-500">
                  {breadcrumb.name}
                </span>
              ) : (
                <SmoothLink
                  href={breadcrumb.href}
                  className="ml-4 text-sm font-medium text-gray-500 hover:text-gray-700"
                >
                  {breadcrumb.name}
                </SmoothLink>
              )}
            </div>
          </li>
        ))}
      </ol>
    </nav>
  );
}

// Quick actions component with permission checks
export function PermissionAwareQuickActions() {
  const { hasPermission } = usePermissions();

  const quickActions = [
    {
      name: 'Add Contact',
      href: '/contacts/new',
      icon: UserIcon,
      permission: {
        resource: PermissionResource.CONTACTS,
        action: PermissionAction.CREATE,
      },
      color: 'bg-blue-600 hover:bg-blue-700',
    },
    {
      name: 'Create Lead',
      href: '/leads/new',
      icon: ClipboardDocumentListIcon,
      permission: {
        resource: PermissionResource.LEADS,
        action: PermissionAction.CREATE,
      },
      color: 'bg-green-600 hover:bg-green-700',
    },
    {
      name: 'New Opportunity',
      href: '/opportunities/new',
      icon: BriefcaseIcon,
      permission: {
        resource: PermissionResource.OPPORTUNITIES,
        action: PermissionAction.CREATE,
      },
      color: 'bg-purple-600 hover:bg-purple-700',
    },
    {
      name: 'Schedule Meeting',
      href: '/calendar/new',
      icon: CalendarIcon,
      permission: {
        resource: PermissionResource.CONTACTS,
        action: PermissionAction.CREATE,
      },
      color: 'bg-orange-600 hover:bg-orange-700',
    },
  ];

  const visibleActions = quickActions.filter(action => 
    hasPermission(action.permission.resource, action.permission.action)
  );

  if (visibleActions.length === 0) {
    return null;
  }

  return (
    <div className="flex space-x-3">
      {visibleActions.map((action) => (
        <SmoothLink
          key={action.name}
          href={action.href}
          className={`
            inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white transition-colors
            ${action.color}
          `}
        >
          <action.icon className="h-4 w-4 mr-2" />
          {action.name}
        </SmoothLink>
      ))}
    </div>
  );
}
