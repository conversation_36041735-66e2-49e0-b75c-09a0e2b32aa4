'use client';

import { useEffect, useState } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import {
  ServerIcon,
  CircleStackIcon,
  CloudIcon,
  CpuChipIcon,
  ClockIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  XCircleIcon,
} from '@heroicons/react/24/outline';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { EnhancedMetricCard } from '@/components/dashboard/enhanced-metric-card';
import { SystemHealthMetrics, ServiceHealthStatus, SystemHealthHistory } from '@/services/super-admin';
import { HealthTrendChart } from '@/components/charts/health-trend-chart';
import { ServiceStatusGrid } from '@/components/dashboard/service-status-grid';
import { AlertsPanel } from '@/components/dashboard/alerts-panel';
import { SuperAdminSiteHeader } from '@/components/super-admin-site-header';

interface HealthDashboardState {
  currentHealth: SystemHealthMetrics | null;
  healthHistory: SystemHealthHistory[];
  serviceStatus: ServiceHealthStatus | null;
  loading: boolean;
  error: string | null;
  timeRange: string;
  autoRefresh: boolean;
}

export default function HealthDashboard() {
  const { data: session, status } = useSession();
  const router = useRouter();

  const [state, setState] = useState<HealthDashboardState>({
    currentHealth: null,
    healthHistory: [],
    serviceStatus: null,
    loading: true,
    error: null,
    timeRange: '1h',
    autoRefresh: true,
  });

  useEffect(() => {
    if (status === 'loading') return;

    if (!session?.user?.isPlatformAdmin) {
      router.push('/dashboard');
      return;
    }

    fetchHealthData();
  }, [session, status, router, state.timeRange]);

  useEffect(() => {
    if (!state.autoRefresh) return;

    const interval = setInterval(() => {
      fetchHealthData();
    }, 10000); // Refresh every 10 seconds

    return () => clearInterval(interval);
  }, [state.autoRefresh, state.timeRange]);

  const fetchHealthData = async () => {
    try {
      setState(prev => ({ ...prev, error: null }));
      
      const response = await fetch(
        `/api/admin/platform/analytics/system-health?includeHistory=true&timeRange=${state.timeRange}`
      );

      if (!response.ok) {
        throw new Error('Failed to fetch health data');
      }

      const data = await response.json();
      
      setState(prev => ({
        ...prev,
        currentHealth: data.data.current,
        healthHistory: data.data.history || [],
        serviceStatus: data.data.services,
        loading: false,
      }));
    } catch (err) {
      setState(prev => ({
        ...prev,
        error: err instanceof Error ? err.message : 'An error occurred',
        loading: false,
      }));
    }
  };

  const handleTimeRangeChange = (newTimeRange: string) => {
    setState(prev => ({ ...prev, timeRange: newTimeRange, loading: true }));
  };

  const toggleAutoRefresh = () => {
    setState(prev => ({ ...prev, autoRefresh: !prev.autoRefresh }));
  };

  const getHealthStatus = (health: SystemHealthMetrics | null) => {
    if (!health) return 'unknown';
    
    if (health.errorRate > 5 || health.responseTime > 2000 || health.memoryUsage > 90) {
      return 'critical';
    }
    if (health.errorRate > 2 || health.responseTime > 1000 || health.memoryUsage > 80) {
      return 'warning';
    }
    return 'healthy';
  };

  const formatUptime = (uptime: number) => {
    const days = Math.floor(uptime / (24 * 60 * 60));
    const hours = Math.floor((uptime % (24 * 60 * 60)) / (60 * 60));
    const minutes = Math.floor((uptime % (60 * 60)) / 60);
    
    if (days > 0) return `${days}d ${hours}h ${minutes}m`;
    if (hours > 0) return `${hours}h ${minutes}m`;
    return `${minutes}m`;
  };

  if (status === 'loading' || state.loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (state.error) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle className="text-destructive">Error</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground mb-4">{state.error}</p>
            <Button onClick={fetchHealthData} variant="outline" size="sm">
              Retry
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  const { currentHealth, serviceStatus, healthHistory } = state;
  const healthStatus = getHealthStatus(currentHealth);

  return (
    <>
      <SuperAdminSiteHeader />
      <div className="px-4 sm:px-6 lg:px-8 py-8 space-y-8">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
              System Health Dashboard
            </h1>
            <p className="mt-1 text-gray-600 dark:text-gray-400">
              Real-time monitoring of system health and performance metrics
            </p>
          </div>
          <div className="mt-4 sm:mt-0 flex items-center space-x-4">
            <Select value={state.timeRange} onValueChange={handleTimeRangeChange}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="15m">15 minutes</SelectItem>
                <SelectItem value="1h">1 hour</SelectItem>
                <SelectItem value="6h">6 hours</SelectItem>
                <SelectItem value="24h">24 hours</SelectItem>
                <SelectItem value="7d">7 days</SelectItem>
              </SelectContent>
            </Select>

            <Button
              variant={state.autoRefresh ? "default" : "outline"}
              size="sm"
              onClick={toggleAutoRefresh}
            >
              {state.autoRefresh ? "Auto-refresh ON" : "Auto-refresh OFF"}
            </Button>

            <Button onClick={fetchHealthData} variant="outline" size="sm">
              Refresh Now
            </Button>
          </div>
        </div>

      {/* Overall Health Status */}
      <Card className="bg-white dark:bg-gray-800 rounded-lg shadow-soft">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <ServerIcon className="h-5 w-5" />
              Overall System Health
            </CardTitle>
            <Badge 
              variant={healthStatus === 'healthy' ? 'default' : healthStatus === 'warning' ? 'secondary' : 'destructive'}
              className="text-sm"
            >
              {healthStatus === 'healthy' && <CheckCircleIcon className="h-4 w-4 mr-1" />}
              {healthStatus === 'warning' && <ExclamationTriangleIcon className="h-4 w-4 mr-1" />}
              {healthStatus === 'critical' && <XCircleIcon className="h-4 w-4 mr-1" />}
              {healthStatus.toUpperCase()}
            </Badge>
          </div>
        </CardHeader>
        <CardContent>
          <div className="text-sm text-muted-foreground">
            Last updated: {currentHealth ? new Date(currentHealth.lastUpdated).toLocaleString() : 'Never'}
          </div>
        </CardContent>
      </Card>

      {/* Key Metrics */}
      {currentHealth && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <EnhancedMetricCard
            title="System Uptime"
            value={formatUptime(currentHealth.uptime)}
            subtitle="Current uptime"
            icon={ClockIcon}
            format="text"
            color="success"
          />
          <EnhancedMetricCard
            title="Response Time"
            value={currentHealth.responseTime}
            subtitle="Average API response"
            icon={CpuChipIcon}
            format="number"
            color={currentHealth.responseTime > 1000 ? "warning" : "success"}
          />
          <EnhancedMetricCard
            title="Error Rate"
            value={currentHealth.errorRate}
            subtitle="Current error percentage"
            icon={ExclamationTriangleIcon}
            format="percentage"
            color={currentHealth.errorRate > 2 ? "danger" : "success"}
          />
          <EnhancedMetricCard
            title="Memory Usage"
            value={currentHealth.memoryUsage}
            subtitle="System memory utilization"
            icon={CircleStackIcon}
            format="percentage"
            color={currentHealth.memoryUsage > 80 ? "warning" : "success"}
          />
        </div>
      )}

      {/* Detailed Health Information */}
      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="services">Services</TabsTrigger>
          <TabsTrigger value="trends">Trends</TabsTrigger>
          <TabsTrigger value="alerts">Alerts</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {serviceStatus && <ServiceStatusGrid services={serviceStatus} />}
        </TabsContent>

        <TabsContent value="services" className="space-y-6">
          {serviceStatus && <ServiceStatusGrid services={serviceStatus} detailed={true} />}
        </TabsContent>

        <TabsContent value="trends" className="space-y-6">
          {healthHistory.length > 0 && (
            <HealthTrendChart
              title="System Health Trends"
              description={`Health metrics over the last ${state.timeRange}`}
              data={healthHistory}
              timeRange={state.timeRange}
            />
          )}
        </TabsContent>

        <TabsContent value="alerts" className="space-y-6">
          <AlertsPanel healthStatus={healthStatus} currentHealth={currentHealth} />
        </TabsContent>
      </Tabs>
      </div>
    </>
  );
}
