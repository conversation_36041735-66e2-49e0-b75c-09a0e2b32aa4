'use client';

import { OpportunityForm } from '@/components/opportunities/opportunity-form';
import { PermissionGate } from '@/components/ui/permission-gate';
import { PermissionResource, PermissionAction } from '@/types';
import { Card, CardContent } from '@/components/ui/card';
import { EmptyState } from '@/components/ui/empty-state';
import { ExclamationTriangleIcon, ArrowLeftIcon } from '@heroicons/react/24/outline';
import { PageLayout } from '@/components/layout/page-layout';
import { Button } from '@/components/ui/button';
import { useRouter, useSearchParams } from 'next/navigation';

export default function NewOpportunityPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const leadId = searchParams.get('leadId');

  const handleSuccess = (opportunityId: string) => {
    router.push(`/opportunities/${opportunityId}`);
  };

  const handleCancel = () => {
    router.back();
  };

  const actions = (
    <Button variant="outline" size="sm" onClick={() => router.back()}>
      <ArrowLeftIcon className="h-4 w-4 mr-2" />
      Back
    </Button>
  );

  return (
    <PermissionGate
      resource={PermissionResource.OPPORTUNITIES}
      action={PermissionAction.CREATE}
      fallback={
        <PageLayout>
          <Card>
            <CardContent className="p-8">
              <EmptyState
                icon={<ExclamationTriangleIcon className="w-12 h-12 text-muted-foreground" />}
                title="Access Denied"
                description="You don't have permission to create opportunities. Please contact your administrator for access."
                action={{
                  label: 'Back to Opportunities',
                  onClick: () => router.push('/opportunities'),
                  variant: 'primary'
                }}
              />
            </CardContent>
          </Card>
        </PageLayout>
      }
    >
      <PageLayout
        title={leadId ? "Convert Lead to Opportunity" : "Create New Opportunity"}
        description={leadId ? "Convert your qualified lead into a sales opportunity" : "Create a new sales opportunity to track through your pipeline"}
        actions={actions}
      >
        <OpportunityForm
          leadId={leadId || undefined}
          onSuccess={handleSuccess}
          onCancel={handleCancel}
        />
      </PageLayout>
    </PermissionGate>
  );
}
