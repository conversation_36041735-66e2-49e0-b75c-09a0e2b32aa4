import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { hasPermission } from '@/lib/permissions';
import { PermissionResource, PermissionAction } from '@/types';
import { prisma } from '@/lib/prisma';

// Default content sections based on current AI service prompts
const DEFAULT_CONTENT_SECTIONS = [
  {
    sectionType: 'executive_summary',
    title: 'Executive Summary',
    description: 'High-level overview and business value',
    promptEn: 'Write an executive summary for this software development proposal. Include business value, key benefits, and solution overview. Use the reference documents to understand requirements and tailor the response accordingly.',
    promptAr: 'اكتب ملخصاً تنفيذياً لهذا الاقتراح لتطوير البرمجيات. اشمل القيمة التجارية والفوائد الرئيسية ونظرة عامة على الحل. استخدم المستندات المرجعية لفهم المتطلبات وتخصيص الاستجابة وفقاً لذلك.',
    systemPromptEn: 'You are a proposal writer. Write executive summaries that are concise, compelling, and focused on business value. Provide only the content without conversational preambles.',
    systemPromptAr: 'أنت كاتب اقتراحات. اكتب ملخصات تنفيذية موجزة ومقنعة ومركزة على القيمة التجارية. قدم المحتوى فقط دون مقدمات محادثة.',
    orderIndex: 1
  },
  {
    sectionType: 'scope_of_work',
    title: 'Scope of Work',
    description: 'Detailed project requirements and deliverables',
    promptEn: 'Define the scope of work including deliverables, features, and technical requirements. Base your response on the reference documents and project context provided.',
    promptAr: 'حدد نطاق العمل بما في ذلك المخرجات والميزات والمتطلبات التقنية. اجعل استجابتك مبنية على المستندات المرجعية وسياق المشروع المقدم.',
    systemPromptEn: 'You are a technical analyst. Define project scope with clear deliverables and requirements. Provide only the content without explanatory text.',
    systemPromptAr: 'أنت محلل تقني. حدد نطاق المشروع بمخرجات ومتطلبات واضحة. قدم المحتوى فقط دون نص توضيحي.',
    orderIndex: 2
  },
  {
    sectionType: 'architecture_high_level',
    title: 'High-Level Architecture',
    description: 'System overview and major components',
    promptEn: 'Describe the high-level system architecture including major components, data flow, and technology stack. Reference the documents provided for specific requirements.',
    promptAr: 'اوصف الهيكل المعماري عالي المستوى بما في ذلك المكونات الرئيسية وتدفق البيانات ومجموعة التقنيات. ارجع إلى المستندات المقدمة للمتطلبات المحددة.',
    systemPromptEn: 'You are a system architect. Describe high-level architecture with clear component relationships. Provide only the content without conversational phrases.',
    systemPromptAr: 'أنت مهندس أنظمة. اوصف الهيكل المعماري عالي المستوى بعلاقات مكونات واضحة. قدم المحتوى فقط دون عبارات محادثة.',
    orderIndex: 3
  },
  {
    sectionType: 'architecture_low_level',
    title: 'Detailed Technical Architecture',
    description: 'Detailed technical implementation',
    promptEn: 'Provide detailed technical architecture including technologies, frameworks, databases, and implementation details. Use the reference documents to ensure alignment with requirements.',
    promptAr: 'قدم هيكلاً معمارياً تقنياً مفصلاً بما في ذلك التقنيات والأطر وقواعد البيانات وتفاصيل التنفيذ. استخدم المستندات المرجعية لضمان التوافق مع المتطلبات.',
    systemPromptEn: 'You are a technical architect. Provide detailed technical specifications and implementation details. Provide only the content without preambles.',
    systemPromptAr: 'أنت مهندس معماري تقني. قدم مواصفات تقنية مفصلة وتفاصيل التنفيذ. قدم المحتوى فقط دون مقدمات.',
    orderIndex: 4
  },
  {
    sectionType: 'out_of_scope',
    title: 'Out of Scope',
    description: 'Items explicitly excluded from project',
    promptEn: 'List items that are NOT included in this proposal. Be specific and clear to avoid scope creep.',
    promptAr: 'اذكر العناصر التي لا تشملها هذا الاقتراح. كن محدداً وواضحاً لتجنب توسع النطاق.',
    systemPromptEn: 'You are a project manager. List exclusions clearly and specifically. Provide only the content without conversational text.',
    systemPromptAr: 'أنت مدير مشروع. اذكر الاستثناءات بوضوح وتحديد. قدم المحتوى فقط دون نص محادثة.',
    orderIndex: 5
  },
  {
    sectionType: 'assumptions',
    title: 'Assumptions and Constraints',
    description: 'Project assumptions and dependencies',
    promptEn: 'List assumptions made during proposal creation including technical, business, and project assumptions. Base these on the project context and reference documents.',
    promptAr: 'اذكر الافتراضات المتخذة أثناء إنشاء الاقتراح بما في ذلك الافتراضات التقنية والتجارية والمشروع. اجعل هذه مبنية على سياق المشروع والمستندات المرجعية.',
    systemPromptEn: 'You are a business analyst. List project assumptions clearly and logically. Provide only the content without explanatory phrases.',
    systemPromptAr: 'أنت محلل أعمال. اذكر افتراضات المشروع بوضوح ومنطق. قدم المحتوى فقط دون عبارات توضيحية.',
    orderIndex: 6
  },
  {
    sectionType: 'project_plan',
    title: 'Project Plan and Timeline',
    description: 'Project phases, milestones, and deliverables',
    promptEn: 'Create a project timeline with phases, milestones, and deliverables. Consider the project scope and requirements from the reference documents.',
    promptAr: 'أنشئ جدولاً زمنياً للمشروع مع المراحل والمعالم والمخرجات. اعتبر نطاق المشروع والمتطلبات من المستندات المرجعية.',
    systemPromptEn: 'You are a project manager. Create structured timelines with clear phases and milestones. Provide only the content without conversational text.',
    systemPromptAr: 'أنت مدير مشروع. أنشئ جداول زمنية منظمة بمراحل ومعالم واضحة. قدم المحتوى فقط دون نص محادثة.',
    orderIndex: 7
  },
  {
    sectionType: 'resource_estimation',
    title: 'Resource Estimation',
    description: 'Resource requirements and effort estimation',
    promptEn: 'Provide resource estimation in person-months for different roles (developers, designers, QA, etc.) with justification based on project scope.',
    promptAr: 'قدم تقدير الموارد بالأشهر الشخصية للأدوار المختلفة (المطورين والمصممين وضمان الجودة وغيرها) مع التبرير بناءً على نطاق المشروع.',
    systemPromptEn: 'You are a resource planner. Provide accurate resource estimates with clear justifications. Provide only the content without preambles.',
    systemPromptAr: 'أنت مخطط موارد. قدم تقديرات موارد دقيقة بتبريرات واضحة. قدم المحتوى فقط دون مقدمات.',
    orderIndex: 8
  }
];

/**
 * POST /api/proposal-content-sections/seed
 * Seed default proposal content sections for the current tenant
 */
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user's current tenant
    const currentTenant = session.user.tenants?.[0];
    if (!currentTenant) {
      return NextResponse.json({ error: 'No tenant access' }, { status: 403 });
    }

    // Check permission
    const hasAccess = await hasPermission(
      PermissionResource.PROPOSALS,
      PermissionAction.CREATE,
      currentTenant.id
    );

    if (!hasAccess) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Check if default sections already exist
    const existingSections = await prisma.proposalContentSection.findMany({
      where: {
        tenantId: currentTenant.id,
        isDefault: true
      }
    });

    if (existingSections.length > 0) {
      return NextResponse.json(
        { 
          message: 'Default content sections already exist',
          existingCount: existingSections.length
        },
        { status: 200 }
      );
    }

    // Create default content sections
    const createdSections = await Promise.all(
      DEFAULT_CONTENT_SECTIONS.map(section =>
        prisma.proposalContentSection.create({
          data: {
            tenantId: currentTenant.id,
            sectionType: section.sectionType,
            title: section.title,
            description: section.description,
            promptEn: section.promptEn,
            promptAr: section.promptAr,
            systemPromptEn: section.systemPromptEn,
            systemPromptAr: section.systemPromptAr,
            orderIndex: section.orderIndex,
            isActive: true,
            isDefault: true,
            createdById: session.user.id
          }
        })
      )
    );

    return NextResponse.json({
      message: 'Default content sections created successfully',
      createdCount: createdSections.length,
      sections: createdSections
    }, { status: 201 });

  } catch (error) {
    console.error('Error seeding proposal content sections:', error);
    return NextResponse.json(
      { error: 'Failed to seed proposal content sections' },
      { status: 500 }
    );
  }
}
