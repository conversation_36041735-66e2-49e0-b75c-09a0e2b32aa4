'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { PermissionProvider } from '@/components/providers/permission-provider';
import { PermissionGate } from '@/components/ui/permission-gate';
import { PermissionAction, PermissionResource } from '@/types';
import { useTenant } from '@/hooks/use-tenant';
import { PageLayout } from '@/components/layout/page-layout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
// Removed LegacySelect import - using standard HTML select elements
import { ContactAvatar } from '@/components/ui/contact-avatar';
import { TeamWithDetails, User } from '@/types';
import {
  ArrowLeftIcon,
  UserGroupIcon,
  ColorSwatchIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';

interface CreateTeamFormData {
  name: string;
  description: string;
  color: string;
  managerId: string;
  parentTeamId: string;
}

export default function CreateTeamPage() {
  const { currentTenant } = useTenant();
  const router = useRouter();
  const [formData, setFormData] = useState<CreateTeamFormData>({
    name: '',
    description: '',
    color: '#6B7280',
    managerId: '',
    parentTeamId: ''
  });
  const [availableManagers, setAvailableManagers] = useState<User[]>([]);
  const [existingTeams, setExistingTeams] = useState<TeamWithDetails[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const colorOptions = [
    { value: '#6B7280', label: 'Gray', color: '#6B7280' },
    { value: '#EF4444', label: 'Red', color: '#EF4444' },
    { value: '#F97316', label: 'Orange', color: '#F97316' },
    { value: '#EAB308', label: 'Yellow', color: '#EAB308' },
    { value: '#22C55E', label: 'Green', color: '#22C55E' },
    { value: '#06B6D4', label: 'Cyan', color: '#06B6D4' },
    { value: '#3B82F6', label: 'Blue', color: '#3B82F6' },
    { value: '#8B5CF6', label: 'Purple', color: '#8B5CF6' },
    { value: '#EC4899', label: 'Pink', color: '#EC4899' },
  ];

  useEffect(() => {
    if (currentTenant) {
      fetchAvailableManagers();
      fetchExistingTeams();
    }
  }, [currentTenant]);

  const fetchAvailableManagers = async () => {
    try {
      const response = await fetch('/api/users');
      if (response.ok) {
        const data = await response.json();
        setAvailableManagers(data.data?.users || []);
      }
    } catch (error) {
      console.error('Failed to fetch managers:', error);
    }
  };

  const fetchExistingTeams = async () => {
    try {
      const response = await fetch('/api/teams');
      if (response.ok) {
        const data = await response.json();
        setExistingTeams(data.data?.teams || []);
      }
    } catch (error) {
      console.error('Failed to fetch teams:', error);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);

    if (!formData.name.trim()) {
      setError('Team name is required');
      return;
    }

    // Check for duplicate team names
    const isDuplicate = existingTeams.some(
      team => team.name.toLowerCase() === formData.name.toLowerCase()
    );

    if (isDuplicate) {
      setError('A team with this name already exists');
      return;
    }

    setIsLoading(true);

    try {
      const response = await fetch('/api/teams', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: formData.name.trim(),
          description: formData.description.trim() || undefined,
          color: formData.color,
          managerId: formData.managerId || undefined,
          parentTeamId: formData.parentTeamId || undefined
        }),
      });

      if (response.ok) {
        router.push('/settings/team');
      } else {
        const errorData = await response.json();
        setError(errorData.error || 'Failed to create team');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create team');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    router.push('/settings/team');
  };

  if (!currentTenant) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <ExclamationTriangleIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-500">No tenant context available</p>
        </div>
      </div>
    );
  }

  const actions = (
    <div className="flex items-center gap-2">
      <Button variant="outline" onClick={handleCancel}>
        Cancel
      </Button>
      <Button 
        type="submit" 
        form="create-team-form"
        loading={isLoading}
        disabled={!formData.name.trim()}
      >
        Create Team
      </Button>
    </div>
  );

  return (
    <PermissionProvider>
      <PageLayout
        title="Create New Team"
        description="Add a new team to your organization"
        actions={actions}
      >
        <PermissionGate
          resource={PermissionResource.USERS}
          action={PermissionAction.MANAGE}
          fallback={<AccessDeniedMessage />}
        >
          <div className="max-w-2xl">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <UserGroupIcon className="w-5 h-5" />
                  Team Information
                </CardTitle>
              </CardHeader>
              <CardContent>
                <form id="create-team-form" onSubmit={handleSubmit} className="space-y-6">
                  {error && (
                    <div className="bg-red-50 border border-red-200 rounded-md p-4">
                      <p className="text-red-600">{error}</p>
                    </div>
                  )}

                  {/* Team Name */}
                  <div>
                    <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
                      Team Name *
                    </label>
                    <Input
                      id="name"
                      type="text"
                      value={formData.name}
                      onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                      placeholder="Enter team name"
                      required
                    />
                  </div>

                  {/* Description */}
                  <div>
                    <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-2">
                      Description
                    </label>
                    <textarea
                      id="description"
                      value={formData.description}
                      onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                      placeholder="Enter team description"
                      rows={3}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>

                  {/* Team Color */}
                  <div>
                    <label htmlFor="color" className="block text-sm font-medium text-gray-700 mb-2">
                      Team Color
                    </label>
                    <div className="flex items-center gap-3">
                      <div 
                        className="w-8 h-8 rounded-full border-2 border-gray-300"
                        style={{ backgroundColor: formData.color }}
                      />
                      <select
                        value={formData.color}
                        onChange={(e) => setFormData({ ...formData, color: e.target.value })}
                        className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      >
                        {colorOptions.map((option) => (
                          <option key={option.value} value={option.value}>
                            {option.label}
                          </option>
                        ))}
                      </select>
                    </div>
                  </div>

                  {/* Team Manager */}
                  <div>
                    <label htmlFor="managerId" className="block text-sm font-medium text-gray-700 mb-2">
                      Team Manager
                    </label>
                    <select
                      value={formData.managerId}
                      onChange={(e) => setFormData({ ...formData, managerId: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      <option value="">Select a manager (optional)</option>
                      {availableManagers.map((manager) => (
                        <option key={manager.id} value={manager.id}>
                          {manager.firstName} {manager.lastName}
                        </option>
                      ))}
                    </select>
                  </div>

                  {/* Parent Team */}
                  <div>
                    <label htmlFor="parentTeamId" className="block text-sm font-medium text-gray-700 mb-2">
                      Parent Team
                    </label>
                    <select
                      value={formData.parentTeamId}
                      onChange={(e) => setFormData({ ...formData, parentTeamId: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      <option value="">No parent team</option>
                      {existingTeams.map((team) => (
                        <option key={team.id} value={team.id}>
                          {team.name}
                        </option>
                      ))}
                    </select>
                  </div>
                </form>
              </CardContent>
            </Card>
          </div>
        </PermissionGate>
      </PageLayout>
    </PermissionProvider>
  );
}

function AccessDeniedMessage() {
  return (
    <div className="text-center py-12">
      <ExclamationTriangleIcon className="w-12 h-12 text-red-400 mx-auto mb-4" />
      <h3 className="text-lg font-medium text-gray-900 mb-2">Access Denied</h3>
      <p className="text-gray-500">
        You don't have permission to create teams. Contact your administrator for access.
      </p>
    </div>
  );
}
