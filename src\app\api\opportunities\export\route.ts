import { NextResponse } from 'next/server';
import { OpportunityManagementService, OpportunityFilters } from '@/services/opportunity-management';
import { withPermission, AuthenticatedRequest } from '@/lib/permission-middleware';
import { PermissionResource, PermissionAction } from '@/types';
import { z } from 'zod';

const opportunityService = new OpportunityManagementService();

// Validation schema for export parameters
const exportQuerySchema = z.object({
  format: z.enum(['csv', 'json']).default('csv'),
  search: z.string().optional(),
  stage: z.string().optional(),
  priority: z.enum(['low', 'medium', 'high', 'urgent']).optional(),
  ownerId: z.string().optional(),
  contactId: z.string().optional(),
  companyId: z.string().optional(),
  minValue: z.string().transform(val => val ? parseFloat(val) : undefined).optional(),
  maxValue: z.string().transform(val => val ? parseFloat(val) : undefined).optional(),
  dateFrom: z.string().optional(),
  dateTo: z.string().optional(),
});

/**
 * GET /api/opportunities/export
 * Export opportunities to CSV or JSON format
 */
export const GET = withPermission({
  resource: PermissionResource.OPPORTUNITIES,
  action: PermissionAction.READ,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest) => {
  try {
    const { searchParams } = new URL(req.url);
    const queryParams = Object.fromEntries(searchParams.entries());
    
    const validatedQuery = exportQuerySchema.parse(queryParams);
    
    const filters: OpportunityFilters = {
      search: validatedQuery.search,
      stage: validatedQuery.stage,
      priority: validatedQuery.priority,
      ownerId: validatedQuery.ownerId,
      contactId: validatedQuery.contactId,
      companyId: validatedQuery.companyId,
      minValue: validatedQuery.minValue,
      maxValue: validatedQuery.maxValue,
      dateFrom: validatedQuery.dateFrom ? new Date(validatedQuery.dateFrom) : undefined,
      dateTo: validatedQuery.dateTo ? new Date(validatedQuery.dateTo) : undefined,
      limit: 10000, // Large limit for export
      sortBy: 'createdAt',
      sortOrder: 'desc',
    };

    console.log('Export filters:', filters);

    const result = await opportunityService.getOpportunities(req.tenantId, filters);
    const opportunities = result.opportunities;

    console.log(`Exporting ${opportunities.length} opportunities`);

    if (validatedQuery.format === 'json') {
      return NextResponse.json({
        success: true,
        data: opportunities,
        total: opportunities.length,
        exported: new Date().toISOString()
      });
    }

    // Generate CSV
    const headers = [
      'ID',
      'Title',
      'Description',
      'Stage',
      'Priority',
      'Value',
      'Probability',
      'Expected Close Date',
      'Contact Name',
      'Contact Email',
      'Contact Phone',
      'Company Name',
      'Company Website',
      'Company Industry',
      'Owner Name',
      'Owner Email',
      'Created By',
      'Lead Source',
      'Tags',
      'Custom Fields',
      'Created At',
      'Updated At'
    ];

    const csvRows = [
      headers.join(','),
      ...opportunities.map(opportunity => [
        `"${opportunity.id}"`,
        `"${(opportunity.title || '').replace(/"/g, '""')}"`,
        `"${(opportunity.description || '').replace(/"/g, '""')}"`,
        `"${opportunity.stage}"`,
        `"${opportunity.priority}"`,
        opportunity.value ? Number(opportunity.value) : '',
        opportunity.probability || 0,
        opportunity.expectedCloseDate ? `"${opportunity.expectedCloseDate.toISOString()}"` : '',
        `"${opportunity.contact ? `${opportunity.contact.firstName || ''} ${opportunity.contact.lastName || ''}`.trim() : ''}"`,
        `"${(opportunity.contact?.email || '').replace(/"/g, '""')}"`,
        `"${(opportunity.contact?.phone || '').replace(/"/g, '""')}"`,
        `"${(opportunity.company?.name || '').replace(/"/g, '""')}"`,
        `"${(opportunity.company?.website || '').replace(/"/g, '""')}"`,
        `"${(opportunity.company?.industry || '').replace(/"/g, '""')}"`,
        `"${opportunity.owner ? `${opportunity.owner.firstName || ''} ${opportunity.owner.lastName || ''}`.trim() : ''}"`,
        `"${(opportunity.owner?.email || '').replace(/"/g, '""')}"`,
        `"${opportunity.createdBy ? `${opportunity.createdBy.firstName || ''} ${opportunity.createdBy.lastName || ''}`.trim() : ''}"`,
        `"${(opportunity.lead?.leadSource?.name || '').replace(/"/g, '""')}"`,
        `"${Array.isArray(opportunity.tags) ? opportunity.tags.join('; ') : ''}"`,
        `"${opportunity.customFields ? JSON.stringify(opportunity.customFields).replace(/"/g, '""') : ''}"`,
        `"${opportunity.createdAt.toISOString()}"`,
        `"${opportunity.updatedAt.toISOString()}"`
      ].join(','))
    ];

    // Add UTF-8 BOM for proper Arabic character support
    const BOM = '\uFEFF';
    const csvContent = BOM + csvRows.join('\n');
    const filename = `opportunities-export-${new Date().toISOString().split('T')[0]}.csv`;

    return new NextResponse(csvContent, {
      status: 200,
      headers: {
        'Content-Type': 'text/csv; charset=utf-8',
        'Content-Disposition': `attachment; filename="${filename}"`,
        'Cache-Control': 'no-cache',
      },
    });

  } catch (error) {
    console.error('Export opportunities error:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid query parameters', details: error.errors },
        { status: 400 }
      );
    }

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
});
