'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useParams } from 'next/navigation';
import {
  ArrowLeftIcon,
  LightBulbIcon,
  ChartBarIcon,
  TrophyIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  ClockIcon,
  CurrencyDollarIcon,
  UserGroupIcon,
  BuildingOfficeIcon,
  ArrowTrendingUpIcon,
  ArrowTrendingDownIcon,
  SparklesIcon
} from '@heroicons/react/24/outline';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Skeleton } from '@/components/ui/skeleton';
import { PageLayout } from '@/components/layout/page-layout';
import { toastFunctions as toast } from '@/hooks/use-toast';
import { cn } from '@/lib/utils';
import { <PERSON><PERSON><PERSON><PERSON>, ForecastT<PERSON><PERSON><PERSON><PERSON> } from '@/components/insights/scoring-chart';

interface OpportunityInsights {
  scoring?: {
    totalScore: number;
    confidence: number;
    factors: {
      dealSize: { score: number; weight: number; reasoning: string };
      timeline: { score: number; weight: number; reasoning: string };
      authority: { score: number; weight: number; reasoning: string };
      need: { score: number; weight: number; reasoning: string };
      competition: { score: number; weight: number; reasoning: string };
    };
    recommendations: string[];
    riskFactors: string[];
    nextActions: string[];
  };
  forecast?: {
    predictedCloseDate: string;
    confidence: number;
    probabilityAdjustment: number;
    reasoning: string;
    factors: {
      historical: string;
      seasonal: string;
      competitive: string;
      internal: string;
    };
  };
  competition?: {
    competitors: Array<{
      name: string;
      strength: 'high' | 'medium' | 'low';
      advantages: string[];
      weaknesses: string[];
    }>;
    positioning: {
      strengths: string[];
      weaknesses: string[];
      differentiators: string[];
    };
    strategy: {
      recommendations: string[];
      messaging: string[];
      pricing: string[];
    };
  };
  nextActions?: string[];
}

export default function OpportunityInsightsPage() {
  const router = useRouter();
  const params = useParams();
  const opportunityId = params.id as string;

  const [insights, setInsights] = useState<OpportunityInsights | null>(null);
  const [opportunity, setOpportunity] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [refreshing, setRefreshing] = useState(false);

  // Load opportunity and insights data
  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);
        setError(null);

        // Load opportunity details
        const opportunityResponse = await fetch(`/api/opportunities/${opportunityId}`);
        if (!opportunityResponse.ok) {
          throw new Error('Failed to load opportunity');
        }
        const opportunityData = await opportunityResponse.json();
        setOpportunity(opportunityData);

        // Load AI insights
        const insightsResponse = await fetch(`/api/opportunities/${opportunityId}/insights`);
        if (!insightsResponse.ok) {
          throw new Error('Failed to load insights');
        }
        const insightsData = await insightsResponse.json();
        setInsights(insightsData);

      } catch (error) {
        console.error('Error loading data:', error);
        setError(error instanceof Error ? error.message : 'Failed to load data');
        toast.error('Failed to load AI insights');
      } finally {
        setLoading(false);
      }
    };

    if (opportunityId) {
      loadData();
    }
  }, [opportunityId]);

  // Refresh insights
  const handleRefresh = async () => {
    try {
      setRefreshing(true);
      const response = await fetch(`/api/opportunities/${opportunityId}/insights`);
      if (!response.ok) {
        throw new Error('Failed to refresh insights');
      }
      const data = await response.json();
      setInsights(data);
      toast.success('Insights refreshed successfully');
    } catch (error) {
      console.error('Error refreshing insights:', error);
      toast.error('Failed to refresh insights');
    } finally {
      setRefreshing(false);
    }
  };

  const actions = (
    <div className="flex items-center gap-2">
      <Button variant="outline" size="sm" onClick={() => router.back()}>
        <ArrowLeftIcon className="h-4 w-4 mr-2" />
        Back
      </Button>
      <Button 
        variant="outline" 
        size="sm" 
        onClick={handleRefresh}
        disabled={refreshing}
      >
        <SparklesIcon className="h-4 w-4 mr-2" />
        {refreshing ? 'Refreshing...' : 'Refresh Insights'}
      </Button>
      {opportunity && (
        <Button 
          variant="outline" 
          size="sm" 
          onClick={() => router.push(`/opportunities/${opportunityId}`)}
        >
          View Opportunity
        </Button>
      )}
    </div>
  );

  if (loading) {
    return (
      <PageLayout
        title="AI Insights"
        description="Loading AI-powered opportunity insights..."
        actions={actions}
      >
        <InsightsLoadingSkeleton />
      </PageLayout>
    );
  }

  if (error) {
    return (
      <PageLayout
        title="AI Insights"
        description="AI-powered opportunity analysis and recommendations"
        actions={actions}
      >
        <Card className="bg-red-50 border-red-200">
          <CardContent className="pt-6">
            <div className="flex items-center space-x-2 text-red-800">
              <ExclamationTriangleIcon className="h-5 w-5" />
              <span>{error}</span>
            </div>
          </CardContent>
        </Card>
      </PageLayout>
    );
  }

  return (
    <PageLayout
      title={`AI Insights: ${opportunity?.title || 'Opportunity'}`}
      description="AI-powered opportunity analysis and recommendations"
      actions={actions}
    >
      <div className="space-y-6">
        {/* Overview Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <OverviewCard
            title="Deal Score"
            value={insights?.scoring?.totalScore || 0}
            max={100}
            icon={TrophyIcon}
            color="blue"
            suffix="/100"
          />
          <OverviewCard
            title="Confidence"
            value={Math.round((insights?.scoring?.confidence || 0) * 100)}
            max={100}
            icon={CheckCircleIcon}
            color="green"
            suffix="%"
          />
          <OverviewCard
            title="Probability"
            value={opportunity?.probability || 0}
            max={100}
            icon={ChartBarIcon}
            color="purple"
            suffix="%"
          />
          <OverviewCard
            title="Value"
            value={opportunity?.value ? `$${Number(opportunity.value).toLocaleString()}` : 'N/A'}
            icon={CurrencyDollarIcon}
            color="emerald"
          />
        </div>

        {/* Main Insights Content */}
        <div className="space-y-8">
          {/* Deal Scoring - Full Width */}
          {insights?.scoring && (
            <DealScoringCard scoring={insights.scoring} />
          )}

          {/* Deal Forecast - Full Width */}
          {insights?.forecast && (
            <DealForecastCard forecast={insights.forecast} />
          )}

          {/* Secondary Insights Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Competitive Analysis */}
            {insights?.competition && (
              <CompetitiveAnalysisCard competition={insights.competition} />
            )}

            {/* Next Actions */}
            {insights?.nextActions && (
              <NextActionsCard actions={insights.nextActions} />
            )}
          </div>
        </div>
      </div>
    </PageLayout>
  );
}

// Overview Card Component
interface OverviewCardProps {
  title: string;
  value: number | string;
  max?: number;
  icon: React.ComponentType<{ className?: string }>;
  color: 'blue' | 'green' | 'purple' | 'emerald';
  suffix?: string;
}

function OverviewCard({ title, value, max, icon: Icon, color, suffix }: OverviewCardProps) {
  const colorClasses = {
    blue: 'text-blue-600 bg-blue-50 border-blue-200',
    green: 'text-green-600 bg-green-50 border-green-200',
    purple: 'text-purple-600 bg-purple-50 border-purple-200',
    emerald: 'text-emerald-600 bg-emerald-50 border-emerald-200'
  };

  const progressValue = max && typeof value === 'number' ? (value / max) * 100 : 0;

  return (
    <Card className={cn('border-l-4', colorClasses[color])}>
      <CardContent className="pt-6">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-muted-foreground">{title}</p>
            <p className="text-2xl font-bold">
              {typeof value === 'number' ? value.toLocaleString() : value}
              {suffix}
            </p>
          </div>
          <Icon className={cn('h-8 w-8', colorClasses[color].split(' ')[0])} />
        </div>
        {max && typeof value === 'number' && (
          <div className="mt-4">
            <Progress value={progressValue} className="h-2" />
          </div>
        )}
      </CardContent>
    </Card>
  );
}

// Deal Scoring Card Component
interface DealScoringCardProps {
  scoring: NonNullable<OpportunityInsights['scoring']>;
}

function DealScoringCard({ scoring }: DealScoringCardProps) {
  return (
    <div className="lg:col-span-2 space-y-6">
      {/* Scoring Charts */}
      <ScoringChart
        factors={scoring.factors}
        totalScore={scoring.totalScore}
        confidence={scoring.confidence}
      />

      {/* Detailed Analysis */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Risk Factors */}
        {scoring.riskFactors.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="text-red-600 flex items-center space-x-2">
                <ExclamationTriangleIcon className="h-5 w-5" />
                <span>Risk Factors</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="space-y-2">
                {scoring.riskFactors.map((risk, index) => (
                  <li key={index} className="text-sm text-red-700 flex items-start space-x-2">
                    <span className="text-red-500 mt-1 flex-shrink-0">•</span>
                    <span>{risk}</span>
                  </li>
                ))}
              </ul>
            </CardContent>
          </Card>
        )}

        {/* Recommendations */}
        {scoring.recommendations.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="text-green-600 flex items-center space-x-2">
                <CheckCircleIcon className="h-5 w-5" />
                <span>Recommendations</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="space-y-2">
                {scoring.recommendations.map((rec, index) => (
                  <li key={index} className="text-sm text-green-700 flex items-start space-x-2">
                    <span className="text-green-500 mt-1 flex-shrink-0">•</span>
                    <span>{rec}</span>
                  </li>
                ))}
              </ul>
            </CardContent>
          </Card>
        )}

      </div>
    </div>
  );
}

// Deal Forecast Card Component
interface DealForecastCardProps {
  forecast: NonNullable<OpportunityInsights['forecast']>;
}

function DealForecastCard({ forecast }: DealForecastCardProps) {
  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleDateString();
    } catch {
      return dateString;
    }
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.8) return 'text-green-600 bg-green-50';
    if (confidence >= 0.6) return 'text-yellow-600 bg-yellow-50';
    return 'text-red-600 bg-red-50';
  };

  // Mock original close date for demonstration (would come from opportunity data)
  const originalCloseDate = new Date(forecast.predictedCloseDate);
  originalCloseDate.setDate(originalCloseDate.getDate() - 7);

  return (
    <div className="space-y-6">
      {/* Timeline Chart */}
      <ForecastTimelineChart
        predictedCloseDate={forecast.predictedCloseDate}
        originalCloseDate={originalCloseDate.toISOString()}
        confidence={forecast.confidence}
      />

      {/* Detailed Analysis */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Summary Card */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <ClockIcon className="h-5 w-5" />
              <span>Forecast Summary</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Predicted Close Date */}
            <div className="text-center">
              <div className="text-2xl font-bold text-foreground">
                {formatDate(forecast.predictedCloseDate)}
              </div>
              <p className="text-sm text-muted-foreground">Predicted Close Date</p>
              <div className={cn('inline-flex items-center px-3 py-1 rounded-full text-sm font-medium mt-2', getConfidenceColor(forecast.confidence))}>
                {Math.round(forecast.confidence * 100)}% confidence
              </div>
            </div>

            {/* Probability Adjustment */}
            {forecast.probabilityAdjustment !== 0 && (
              <div className="flex items-center justify-center space-x-2">
                {forecast.probabilityAdjustment > 0 ? (
                  <ArrowTrendingUpIcon className="h-5 w-5 text-green-600" />
                ) : (
                  <ArrowTrendingDownIcon className="h-5 w-5 text-red-600" />
                )}
                <span className={cn('font-medium', forecast.probabilityAdjustment > 0 ? 'text-green-600' : 'text-red-600')}>
                  {forecast.probabilityAdjustment > 0 ? '+' : ''}{forecast.probabilityAdjustment}% probability adjustment
                </span>
              </div>
            )}

            {/* Reasoning */}
            <div className="space-y-2">
              <h4 className="font-medium">Analysis</h4>
              <p className="text-sm text-muted-foreground">{forecast.reasoning}</p>
            </div>
          </CardContent>
        </Card>

        {/* Factors Card */}
        <Card>
          <CardHeader>
            <CardTitle>Key Factors</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {Object.entries(forecast.factors).map(([key, value]) => (
                <div key={key} className="space-y-1">
                  <h5 className="text-sm font-medium capitalize">{key}</h5>
                  <p className="text-xs text-muted-foreground">{value}</p>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

// Competitive Analysis Card Component
interface CompetitiveAnalysisCardProps {
  competition: NonNullable<OpportunityInsights['competition']>;
}

function CompetitiveAnalysisCard({ competition }: CompetitiveAnalysisCardProps) {
  const getStrengthColor = (strength: 'high' | 'medium' | 'low') => {
    switch (strength) {
      case 'high': return 'bg-red-100 text-red-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'low': return 'bg-green-100 text-green-800';
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <UserGroupIcon className="h-5 w-5" />
          <span>Competitive Analysis</span>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Competitors */}
        {competition.competitors.length > 0 && (
          <div className="space-y-3">
            <h4 className="font-medium">Identified Competitors</h4>
            {competition.competitors.map((competitor, index) => (
              <div key={index} className="border rounded-lg p-3 space-y-2">
                <div className="flex items-center justify-between">
                  <h5 className="font-medium">{competitor.name}</h5>
                  <Badge className={getStrengthColor(competitor.strength)}>
                    {competitor.strength} threat
                  </Badge>
                </div>
                {competitor.advantages.length > 0 && (
                  <div>
                    <p className="text-xs font-medium text-red-600 mb-1">Their Advantages:</p>
                    <ul className="text-xs text-muted-foreground space-y-1">
                      {competitor.advantages.map((advantage, i) => (
                        <li key={i} className="flex items-start space-x-1">
                          <span className="text-red-500">•</span>
                          <span>{advantage}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
                {competitor.weaknesses.length > 0 && (
                  <div>
                    <p className="text-xs font-medium text-green-600 mb-1">Their Weaknesses:</p>
                    <ul className="text-xs text-muted-foreground space-y-1">
                      {competitor.weaknesses.map((weakness, i) => (
                        <li key={i} className="flex items-start space-x-1">
                          <span className="text-green-500">•</span>
                          <span>{weakness}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            ))}
          </div>
        )}

        {/* Our Positioning */}
        <div className="space-y-3">
          <h4 className="font-medium">Our Positioning</h4>

          {competition.positioning.strengths.length > 0 && (
            <div>
              <p className="text-sm font-medium text-green-600 mb-2">Our Strengths</p>
              <ul className="space-y-1">
                {competition.positioning.strengths.map((strength, index) => (
                  <li key={index} className="text-sm text-green-700 flex items-start space-x-2">
                    <CheckCircleIcon className="h-4 w-4 mt-0.5 flex-shrink-0" />
                    <span>{strength}</span>
                  </li>
                ))}
              </ul>
            </div>
          )}

          {competition.positioning.differentiators.length > 0 && (
            <div>
              <p className="text-sm font-medium text-blue-600 mb-2">Key Differentiators</p>
              <ul className="space-y-1">
                {competition.positioning.differentiators.map((diff, index) => (
                  <li key={index} className="text-sm text-blue-700 flex items-start space-x-2">
                    <SparklesIcon className="h-4 w-4 mt-0.5 flex-shrink-0" />
                    <span>{diff}</span>
                  </li>
                ))}
              </ul>
            </div>
          )}
        </div>

        {/* Strategy Recommendations */}
        {competition.strategy.recommendations.length > 0 && (
          <div className="space-y-2">
            <h4 className="font-medium">Strategic Recommendations</h4>
            <ul className="space-y-1">
              {competition.strategy.recommendations.map((rec, index) => (
                <li key={index} className="text-sm text-muted-foreground flex items-start space-x-2">
                  <span className="text-blue-500 mt-1">•</span>
                  <span>{rec}</span>
                </li>
              ))}
            </ul>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

// Next Actions Card Component
interface NextActionsCardProps {
  actions: string[];
}

function NextActionsCard({ actions }: NextActionsCardProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <CheckCircleIcon className="h-5 w-5" />
          <span>Recommended Next Actions</span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          {actions.map((action, index) => (
            <div key={index} className="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg">
              <div className="flex-shrink-0 w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-medium">
                {index + 1}
              </div>
              <div className="flex-1">
                <p className="text-sm text-gray-900">{action}</p>
              </div>
            </div>
          ))}
        </div>

        {actions.length === 0 && (
          <div className="text-center py-8 text-muted-foreground">
            <CheckCircleIcon className="h-12 w-12 mx-auto mb-2 opacity-50" />
            <p>No specific actions recommended at this time.</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

// Loading skeleton component
function InsightsLoadingSkeleton() {
  return (
    <div className="space-y-6">
      {/* Overview Cards Skeleton */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {Array.from({ length: 4 }).map((_, i) => (
          <Card key={i}>
            <CardContent className="pt-6">
              <div className="space-y-2">
                <Skeleton className="h-4 w-20" />
                <Skeleton className="h-8 w-16" />
                <Skeleton className="h-2 w-full" />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Main Content Skeleton */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {Array.from({ length: 4 }).map((_, i) => (
          <Card key={i}>
            <CardHeader>
              <Skeleton className="h-6 w-32" />
              <Skeleton className="h-4 w-48" />
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <Skeleton className="h-32 w-full" />
                <div className="space-y-2">
                  <Skeleton className="h-4 w-full" />
                  <Skeleton className="h-4 w-3/4" />
                  <Skeleton className="h-4 w-1/2" />
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}
