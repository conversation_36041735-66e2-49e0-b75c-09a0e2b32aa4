import { NextResponse } from 'next/server';
import { withPermission } from '@/lib/permission-middleware';
import { leadManagementService } from '@/services/lead-management';
import { PermissionAction, PermissionResource } from '@/types';
import { AuthenticatedRequest } from '@/lib/permission-middleware';
import { z } from 'zod';

const searchQuerySchema = z.object({
  q: z.string().min(1, 'Search query is required'),
  limit: z.string().optional().transform((val) => val ? parseInt(val, 10) : 10)
});

/**
 * GET /api/leads/search
 * Search leads by title, description, or source
 */
export const GET = withPermission({
  resource: PermissionResource.LEADS,
  action: PermissionAction.READ,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest) => {
  try {
    const { searchParams } = new URL(req.url);
    const queryParams = Object.fromEntries(searchParams.entries());
    
    const { q: query, limit } = searchQuerySchema.parse(queryParams);

    const leads = await leadManagementService.searchLeads(
      req.tenantId,
      query,
      limit
    );

    return NextResponse.json({
      success: true,
      data: { 
        leads,
        query,
        count: leads.length 
      },
      message: 'Lead search completed successfully'
    });

  } catch (error) {
    console.error('Search leads API error:', error);
    
    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      { error: 'Failed to search leads' },
      { status: 500 }
    );
  }
});
