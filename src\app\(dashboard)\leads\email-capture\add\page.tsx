'use client';

import { useRouter } from 'next/navigation';
import { PageLayout } from '@/components/layout/page-layout';
import { EmailCaptureForm } from '@/components/email-capture/email-capture-form';
import { Button } from '@/components/ui/button';
import { ArrowLeftIcon } from '@heroicons/react/24/outline';

export default function AddEmailCapturePage() {
  const router = useRouter();

  const handleSuccess = () => {
    // Redirect back to email capture list
    router.push('/leads/email-capture');
  };

  const handleCancel = () => {
    router.back();
  };

  const actions = (
    <Button
      variant="outline"
      onClick={() => router.back()}
      className="flex items-center space-x-2"
    >
      <ArrowLeftIcon className="h-4 w-4" />
      <span>Back</span>
    </Button>
  );

  return (
    <PageLayout
      title="Add Email Capture"
      description="Manually capture an email for lead processing and analysis"
      actions={actions}
    >
      <div className="max-w-4xl mx-auto">
        <EmailCaptureForm
          onSuccess={handleSuccess}
          onCancel={handleCancel}
        />
      </div>
    </PageLayout>
  );
}
