import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { projectCreationService } from '@/services/project-creation';
import { z } from 'zod';

// Helper function to parse date values (supports Date objects, strings in YYYY-MM-DD and ISO datetime formats)
const dateSchema = z.union([
  z.string(),
  z.date()
]).optional().transform((val, ctx) => {
  if (!val) return undefined;

  // If it's already a Date object, return it
  if (val instanceof Date) {
    if (isNaN(val.getTime())) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Invalid date object',
      });
      return z.NEVER;
    }
    return val;
  }

  // Handle string values
  if (typeof val === 'string') {
    // Handle YYYY-MM-DD format (from HTML date inputs)
    if (/^\d{4}-\d{2}-\d{2}$/.test(val)) {
      const date = new Date(val + 'T00:00:00.000Z');
      if (isNaN(date.getTime())) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Invalid date',
        });
        return z.NEVER;
      }
      return date;
    }

    // Handle ISO datetime format
    if (/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(\.\d{3})?Z?$/.test(val)) {
      const date = new Date(val);
      if (isNaN(date.getTime())) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Invalid datetime',
        });
        return z.NEVER;
      }
      return date;
    }

    // Try to parse any other string format
    const date = new Date(val);
    if (isNaN(date.getTime())) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Invalid date format. Expected YYYY-MM-DD, ISO datetime, or Date object',
      });
      return z.NEVER;
    }
    return date;
  }

  // Invalid type
  ctx.addIssue({
    code: z.ZodIssueCode.custom,
    message: 'Expected string or Date object',
  });
  return z.NEVER;
});

// Request validation schemas
const createProjectSchema = z.object({
  name: z.string().min(1, 'Project name is required').max(255),
  description: z.string().optional(),
  startDate: dateSchema,
  endDate: dateSchema,
  budget: z.number().positive().optional(),
  managerId: z.string().optional(),
  clientPortalEnabled: z.boolean().default(false),
  customFields: z.record(z.any()).default({}),
});

interface RouteParams {
  params: {
    id: string;
  };
}

/**
 * POST /api/opportunities/[id]/create-project - Create a project from an opportunity
 */
export async function POST(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get tenantId from user's tenants array (users have one tenant in this system)
    let tenantId: string | null = null;
    if (session.user.tenants && session.user.tenants.length > 0) {
      tenantId = session.user.tenants[0].id;
    }

    if (!tenantId) {
      return NextResponse.json({ error: 'Tenant not found' }, { status: 400 });
    }

    const body = await request.json();
    const validatedData = createProjectSchema.parse(body);

    const project = await projectCreationService.createProjectFromOpportunity(
      tenantId,
      params.id,
      validatedData,
      session.user.id
    );

    return NextResponse.json(project, { status: 201 });
  } catch (error) {
    console.error('Error creating project from opportunity:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request data', details: error.errors },
        { status: 400 }
      );
    }

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to create project from opportunity' },
      { status: 500 }
    );
  }
}
