import { NextResponse } from 'next/server';
import { withPermission, AuthenticatedRequest } from '@/lib/permission-middleware';
import { scheduledMeetingService } from '@/services/scheduled-meeting-service';
import { PermissionAction, PermissionResource } from '@/types';

/**
 * GET /api/meetings/[meetingId]
 * Get a specific scheduled meeting by ID
 */
export const GET = withPermission({
  resource: PermissionResource.MEETINGS,
  action: PermissionAction.READ,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest) => {
  try {
    const url = new URL(req.url);
    const pathSegments = url.pathname.split('/');
    const meetingId = pathSegments[pathSegments.indexOf('meetings') + 1];

    const scheduledMeeting = await scheduledMeetingService.getScheduledMeeting(meetingId, req.tenantId);

    if (!scheduledMeeting) {
      return NextResponse.json(
        { error: 'Scheduled meeting not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: { scheduledMeeting },
      message: 'Scheduled meeting retrieved successfully'
    });

  } catch (error) {
    console.error('Get scheduled meeting error:', error);

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
});

/**
 * PUT /api/meetings/[meetingId]
 * Update a specific scheduled meeting
 */
export const PUT = withPermission({
  resource: PermissionResource.MEETINGS,
  action: PermissionAction.UPDATE,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest) => {
  try {
    const url = new URL(req.url);
    const pathSegments = url.pathname.split('/');
    const meetingId = pathSegments[pathSegments.indexOf('meetings') + 1];
    
    const body = await req.json();
    
    const scheduledMeeting = await scheduledMeetingService.updateScheduledMeeting(
      meetingId,
      req.tenantId,
      body
    );

    return NextResponse.json({
      success: true,
      data: { scheduledMeeting },
      message: 'Scheduled meeting updated successfully'
    });

  } catch (error) {
    console.error('Update scheduled meeting error:', error);

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
});

/**
 * DELETE /api/meetings/[meetingId]
 * Delete a specific scheduled meeting
 */
export const DELETE = withPermission({
  resource: PermissionResource.MEETINGS,
  action: PermissionAction.DELETE,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest) => {
  try {
    const url = new URL(req.url);
    const pathSegments = url.pathname.split('/');
    const meetingId = pathSegments[pathSegments.indexOf('meetings') + 1];

    await scheduledMeetingService.deleteScheduledMeeting(meetingId, req.tenantId);

    return NextResponse.json({
      success: true,
      message: 'Scheduled meeting deleted successfully'
    });

  } catch (error) {
    console.error('Delete scheduled meeting error:', error);

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
});
