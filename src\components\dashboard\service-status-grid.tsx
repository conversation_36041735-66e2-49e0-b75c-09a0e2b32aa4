'use client';

import React from 'react';
import {
  CheckCircleIcon,
  ExclamationTriangleIcon,
  XCircleIcon,
  CircleStackIcon,
  ServerIcon,
  CloudIcon,
  CpuChipIcon,
  ClockIcon,
} from '@heroicons/react/24/outline';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { ServiceHealthStatus } from '@/services/super-admin';
import { cn } from '@/lib/utils';

interface ServiceStatusGridProps {
  services: ServiceHealthStatus;
  detailed?: boolean;
  className?: string;
}

export function ServiceStatusGrid({ 
  services, 
  detailed = false, 
  className 
}: ServiceStatusGridProps) {
  const getStatusIcon = (status: 'healthy' | 'degraded' | 'down') => {
    switch (status) {
      case 'healthy':
        return <CheckCircleIcon className="h-5 w-5 text-green-500" />;
      case 'degraded':
        return <ExclamationTriangleIcon className="h-5 w-5 text-yellow-500" />;
      case 'down':
        return <XCircleIcon className="h-5 w-5 text-red-500" />;
      default:
        return <CircleStackIcon className="h-5 w-5 text-gray-400" />;
    }
  };

  const getStatusBadge = (status: 'healthy' | 'degraded' | 'down') => {
    const variants = {
      healthy: 'default',
      degraded: 'secondary',
      down: 'destructive',
    } as const;

    return (
      <Badge variant={variants[status]} className="text-xs">
        {status.toUpperCase()}
      </Badge>
    );
  };

  const formatResponseTime = (time: number) => {
    if (time < 1000) {
      return `${Math.round(time)}ms`;
    }
    return `${(time / 1000).toFixed(2)}s`;
  };

  const formatBytes = (bytes: number) => {
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    if (bytes === 0) return '0 B';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return `${Math.round(bytes / Math.pow(1024, i) * 100) / 100} ${sizes[i]}`;
  };

  const formatLastCheck = (date: Date) => {
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const seconds = Math.floor(diff / 1000);
    
    if (seconds < 60) return `${seconds}s ago`;
    const minutes = Math.floor(seconds / 60);
    if (minutes < 60) return `${minutes}m ago`;
    const hours = Math.floor(minutes / 60);
    return `${hours}h ago`;
  };

  return (
    <div className={cn("grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6", className)}>
      {/* Database Service */}
      <Card className="bg-white dark:bg-gray-800 rounded-lg shadow-soft">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <div className="flex items-center space-x-2">
            <CircleStackIcon className="h-5 w-5 text-muted-foreground" />
            <CardTitle className="text-sm font-medium">Database</CardTitle>
          </div>
          {getStatusIcon(services.database.status)}
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-xs text-muted-foreground">Status</span>
              {getStatusBadge(services.database.status)}
            </div>
            <div className="flex items-center justify-between">
              <span className="text-xs text-muted-foreground">Response Time</span>
              <span className="text-xs font-medium">
                {formatResponseTime(services.database.responseTime)}
              </span>
            </div>
            {detailed && (
              <div className="flex items-center justify-between">
                <span className="text-xs text-muted-foreground">Last Check</span>
                <span className="text-xs">{formatLastCheck(services.database.lastCheck)}</span>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Redis Service */}
      <Card className="bg-white dark:bg-gray-800 rounded-lg shadow-soft">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <div className="flex items-center space-x-2">
            <ServerIcon className="h-5 w-5 text-muted-foreground" />
            <CardTitle className="text-sm font-medium">Redis Cache</CardTitle>
          </div>
          {getStatusIcon(services.redis.status)}
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-xs text-muted-foreground">Status</span>
              {getStatusBadge(services.redis.status)}
            </div>
            <div className="flex items-center justify-between">
              <span className="text-xs text-muted-foreground">Response Time</span>
              <span className="text-xs font-medium">
                {formatResponseTime(services.redis.responseTime)}
              </span>
            </div>
            {detailed && (
              <div className="flex items-center justify-between">
                <span className="text-xs text-muted-foreground">Last Check</span>
                <span className="text-xs">{formatLastCheck(services.redis.lastCheck)}</span>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* API Service */}
      <Card className="bg-white dark:bg-gray-800 rounded-lg shadow-soft">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <div className="flex items-center space-x-2">
            <CloudIcon className="h-5 w-5 text-muted-foreground" />
            <CardTitle className="text-sm font-medium">API Gateway</CardTitle>
          </div>
          {getStatusIcon(services.api.status)}
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-xs text-muted-foreground">Status</span>
              {getStatusBadge(services.api.status)}
            </div>
            <div className="flex items-center justify-between">
              <span className="text-xs text-muted-foreground">Avg Response</span>
              <span className="text-xs font-medium">
                {formatResponseTime(services.api.averageResponseTime)}
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-xs text-muted-foreground">Error Rate</span>
              <span className="text-xs font-medium">
                {services.api.errorRate.toFixed(2)}%
              </span>
            </div>
            {detailed && (
              <div className="flex items-center justify-between">
                <span className="text-xs text-muted-foreground">Last Check</span>
                <span className="text-xs">{formatLastCheck(services.api.lastCheck)}</span>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Storage Service */}
      <Card className="bg-white dark:bg-gray-800 rounded-lg shadow-soft">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <div className="flex items-center space-x-2">
            <CpuChipIcon className="h-5 w-5 text-muted-foreground" />
            <CardTitle className="text-sm font-medium">Storage</CardTitle>
          </div>
          {getStatusIcon(services.storage.status)}
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-xs text-muted-foreground">Status</span>
              {getStatusBadge(services.storage.status)}
            </div>
            <div className="space-y-1">
              <div className="flex items-center justify-between">
                <span className="text-xs text-muted-foreground">Free Space</span>
                <span className="text-xs font-medium">
                  {formatBytes(services.storage.freeSpace * 1024 * 1024 * 1024)}
                </span>
              </div>
              <Progress 
                value={(services.storage.freeSpace / services.storage.totalSpace) * 100} 
                className="h-1"
              />
              <div className="flex items-center justify-between">
                <span className="text-xs text-muted-foreground">Total</span>
                <span className="text-xs">
                  {formatBytes(services.storage.totalSpace * 1024 * 1024 * 1024)}
                </span>
              </div>
            </div>
            {detailed && (
              <div className="flex items-center justify-between">
                <span className="text-xs text-muted-foreground">Last Check</span>
                <span className="text-xs">{formatLastCheck(services.storage.lastCheck)}</span>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Detailed Service Information */}
      {detailed && (
        <Card className="md:col-span-2 lg:col-span-4 bg-white dark:bg-gray-800 rounded-lg shadow-soft">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <ClockIcon className="h-5 w-5" />
              Service Health Summary
            </CardTitle>
            <CardDescription>
              Detailed health information for all monitored services
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className="space-y-2">
                <h4 className="text-sm font-semibold">Database Health</h4>
                <div className="text-xs space-y-1">
                  <div>Connection pool status: Active</div>
                  <div>Query performance: Good</div>
                  <div>Replication lag: &lt; 1ms</div>
                </div>
              </div>
              
              <div className="space-y-2">
                <h4 className="text-sm font-semibold">Cache Health</h4>
                <div className="text-xs space-y-1">
                  <div>Memory usage: Normal</div>
                  <div>Hit rate: Optimal</div>
                  <div>Eviction rate: Low</div>
                </div>
              </div>
              
              <div className="space-y-2">
                <h4 className="text-sm font-semibold">API Health</h4>
                <div className="text-xs space-y-1">
                  <div>Request throughput: Normal</div>
                  <div>Rate limiting: Active</div>
                  <div>Circuit breaker: Closed</div>
                </div>
              </div>
              
              <div className="space-y-2">
                <h4 className="text-sm font-semibold">Storage Health</h4>
                <div className="text-xs space-y-1">
                  <div>I/O performance: Good</div>
                  <div>Backup status: Current</div>
                  <div>Disk health: Excellent</div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
