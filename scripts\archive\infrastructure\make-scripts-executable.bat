@echo off
REM Script to make all shell scripts executable in the CRM project for Windows

echo Making scripts executable...

REM Set script directory and project root
set "SCRIPT_DIR=%~dp0"
set "PROJECT_ROOT=%SCRIPT_DIR%.."

REM Create .bat versions of all shell scripts
for %%f in ("%SCRIPT_DIR%*.sh") do (
    echo Creating Windows batch version for %%~nxf
    if not exist "%SCRIPT_DIR%%%~nf.bat" (
        echo @echo off > "%SCRIPT_DIR%%%~nf.bat"
        echo REM Windows version of %%~nxf >> "%SCRIPT_DIR%%%~nf.bat"
        echo. >> "%SCRIPT_DIR%%%~nf.bat"
        echo docker run --rm -v "%PROJECT_ROOT%:/app" -w /app bash bash ./scripts/%%~nxf %%* >> "%SCRIPT_DIR%%%~nf.bat"
    )
)

echo.
echo The following batch files are now available:
dir /b "%SCRIPT_DIR%*.bat"

echo.
echo Done!
