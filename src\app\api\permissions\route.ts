import { NextResponse } from 'next/server';
import { withPermission, AuthenticatedRequest } from '@/lib/permission-middleware';
import { PermissionAction, PermissionResource } from '@/types';
import { prisma } from '@/lib/prisma';

/**
 * GET /api/permissions
 * Get all available permissions for the tenant
 */
export const GET = withPermission({
  resource: PermissionResource.ROLES,
  action: PermissionAction.READ,
  allowSuperAdmin: true
})(async (req) => {
  try {
    const permissions = await prisma.permission.findMany({
      where: {
        tenantId: req.tenantId
      },
      orderBy: [
        { category: 'asc' },
        { resource: 'asc' },
        { action: 'asc' }
      ]
    });

    const formattedPermissions = permissions.map(permission => ({
      id: permission.id,
      name: permission.name,
      resource: permission.resource,
      action: permission.action,
      category: permission.category,
      description: permission.description || ''
    }));

    // Group permissions by category for better organization
    const groupedPermissions = formattedPermissions.reduce((acc, permission) => {
      const category = permission.category;
      if (!acc[category]) {
        acc[category] = [];
      }
      acc[category].push(permission);
      return acc;
    }, {} as Record<string, typeof formattedPermissions>);

    return NextResponse.json({
      success: true,
      data: {
        permissions: formattedPermissions,
        groupedPermissions,
        count: formattedPermissions.length
      }
    });

  } catch (error) {
    console.error('Get permissions error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch permissions' },
      { status: 500 }
    );
  }
});
