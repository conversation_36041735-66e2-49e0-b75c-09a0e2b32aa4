'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useSession } from 'next-auth/react';
import { PageLayout } from '@/components/layout/page-layout';
import { PermissionGate } from '@/components/ui/permission-gate';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { EmptyState } from '@/components/ui/empty-state';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import {
  MessageSquare,
  Calendar,
  Mail,
  Users,
  Plus,
  Bell,
  Search,
  Filter,
  Settings,
  ExternalLink
} from 'lucide-react';
import { ExclamationTriangleIcon } from '@heroicons/react/24/outline';
import ChatChannel from '@/components/communication/ChatChannel';
import CalendarView from '@/components/communication/CalendarView';
import CreateChannelModal from '@/components/communication/CreateChannelModal';
import CreateEventModal from '@/components/communication/CreateEventModal';
import { useCommunication } from '@/hooks/use-communication';
import { PermissionResource, PermissionAction } from '@/types';

export default function CommunicationPage() {
  const router = useRouter();
  const { data: session, status } = useSession();
  const [activeTab, setActiveTab] = useState('overview');

  // Create user object for communication hook
  const user = session?.user ? {
    id: session.user.id,
    email: session.user.email || '',
    firstName: session.user.name?.split(' ')[0] || '',
    lastName: session.user.name?.split(' ').slice(1).join(' ') || '',
  } : undefined;

  const {
    channels,
    events,
    messages,
    unreadCounts,
    selectedChannel,
    loading,
    error,
    setSelectedChannel,
    loadCommunicationData,
    loadMessages,
    sendMessage,
    createChannel,
    createEvent,
    addReaction,
    updateEventAttendance,
    getTotalUnreadCount,
  } = useCommunication(user);

  useEffect(() => {
    loadCommunicationData();
  }, [loadCommunicationData]);

  const handleCreateEvent = async (eventData: any) => {
    try {
      await createEvent(eventData);
    } catch (error) {
      console.error('Failed to create event:', error);
    }
  };

  const handleEventClick = (event: any) => {
    // TODO: Open event details modal
    console.log('Event clicked:', event);
  };

  const actions = (
    <div className="flex items-center gap-2">
      <Button variant="outline" size="sm">
        <Search className="h-4 w-4 mr-2" />
        Search
      </Button>
      <Button variant="outline" size="sm">
        <Filter className="h-4 w-4 mr-2" />
        Filter
      </Button>
      <Button variant="outline" size="sm">
        <Bell className="h-4 w-4 mr-2" />
        Notifications
        {getTotalUnreadCount() > 0 && (
          <Badge variant="destructive" className="ml-2 text-xs">
            {getTotalUnreadCount()}
          </Badge>
        )}
      </Button>
      <PermissionGate
        resource={PermissionResource.COMMUNICATION}
        action={PermissionAction.CREATE}
      >
        <Button variant="outline" size="sm">
          <Settings className="h-4 w-4 mr-2" />
          Settings
        </Button>
      </PermissionGate>
    </div>
  );

  // Show loading state while session is loading
  if (status === 'loading') {
    return (
      <PageLayout
        title="Communication Hub"
        description="Manage team communication, chat channels, and calendar events"
      >
        <div className="flex items-center justify-center py-12">
          <LoadingSpinner />
        </div>
      </PageLayout>
    );
  }

  // Redirect to signin if not authenticated
  if (status === 'unauthenticated') {
    router.push('/auth/signin');
    return null;
  }

  if (loading && !channels.length && !events.length) {
    return (
      <PageLayout
        title="Communication Hub"
        description="Manage team communication, chat channels, and calendar events"
        actions={actions}
      >
        <div className="flex items-center justify-center py-12">
          <LoadingSpinner />
        </div>
      </PageLayout>
    );
  }

  return (
    <PermissionGate
      resource={PermissionResource.COMMUNICATION}
      action={PermissionAction.READ}
      fallback={
        <PageLayout>
          <Card>
            <CardContent className="p-8">
              <EmptyState
                icon={<ExclamationTriangleIcon className="w-12 h-12 text-muted-foreground" />}
                title="Access Denied"
                description="You don't have permission to access the communication hub. Please contact your administrator for access."
                action={{
                  label: 'Back to Dashboard',
                  onClick: () => router.push('/dashboard'),
                  variant: 'primary'
                }}
              />
            </CardContent>
          </Card>
        </PageLayout>
      }
    >
      <PageLayout
        title="Communication Hub"
        description="Manage team communication, chat channels, and calendar events"
        actions={actions}
      >
        <div className="space-y-6">
          {/* Error Display */}
          {error && (
            <Card className="border-destructive">
              <CardContent className="p-6">
                <div className="flex items-start space-x-3">
                  <ExclamationTriangleIcon className="h-5 w-5 text-destructive mt-0.5" />
                  <div className="flex-1">
                    <h3 className="text-sm font-medium text-destructive">
                      Error loading communication data
                    </h3>
                    <p className="mt-1 text-sm text-muted-foreground">{error}</p>
                    <div className="mt-3">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={loadCommunicationData}
                        className="text-destructive border-destructive hover:bg-destructive/10"
                      >
                        Try Again
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Main Content */}
          <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="overview" className="flex items-center space-x-2">
                <Users className="h-4 w-4" />
                <span>Overview</span>
              </TabsTrigger>
              <TabsTrigger value="chat" className="flex items-center space-x-2">
                <MessageSquare className="h-4 w-4" />
                <span>Chat</span>
                {getTotalUnreadCount() > 0 && (
                  <Badge variant="destructive" className="text-xs ml-1">
                    {getTotalUnreadCount()}
                  </Badge>
                )}
              </TabsTrigger>
              <TabsTrigger value="calendar" className="flex items-center space-x-2">
                <Calendar className="h-4 w-4" />
                <span>Calendar</span>
              </TabsTrigger>
              <TabsTrigger value="templates" className="flex items-center space-x-2">
                <Mail className="h-4 w-4" />
                <span>Templates</span>
              </TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-6">
              {/* Quick Stats */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Active Channels</CardTitle>
                    <MessageSquare className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{channels.length}</div>
                    <p className="text-xs text-muted-foreground">
                      {unreadCounts.filter(c => c.unreadCount > 0).length} with unread messages
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Today's Events</CardTitle>
                    <Calendar className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      {events.filter(e => 
                        new Date(e.startTime).toDateString() === new Date().toDateString()
                      ).length}
                    </div>
                    <p className="text-xs text-muted-foreground">
                      {events.filter(e => new Date(e.startTime) > new Date()).length} upcoming
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Unread Messages</CardTitle>
                    <Bell className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{getTotalUnreadCount()}</div>
                    <p className="text-xs text-muted-foreground">
                      Across {unreadCounts.filter(c => c.unreadCount > 0).length} channels
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Email Templates</CardTitle>
                    <Mail className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">12</div>
                    <p className="text-xs text-muted-foreground">
                      3 recently used
                    </p>
                  </CardContent>
                </Card>
              </div>

              {/* Recent Activity */}
              <Card>
                <CardHeader>
                  <CardTitle>Recent Activity</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {loading ? (
                      <div className="flex justify-center py-8">
                        <LoadingSpinner />
                      </div>
                    ) : (
                      <EmptyState
                        icon={<MessageSquare className="w-8 h-8" />}
                        title="No recent activity"
                        description="Recent communication activity will appear here"
                      />
                    )}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="chat" className="space-y-6">
              <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
                {/* Channel List */}
                <div className="lg:col-span-1">
                  <Card>
                    <CardHeader>
                      <div className="flex items-center justify-between">
                        <CardTitle className="text-lg">Channels</CardTitle>
                        <PermissionGate
                          resource={PermissionResource.COMMUNICATION}
                          action={PermissionAction.CREATE}
                        >
                          <CreateChannelModal
                            onCreateChannel={createChannel}
                            trigger={
                              <Button size="sm">
                                <Plus className="h-4 w-4" />
                              </Button>
                            }
                          />
                        </PermissionGate>
                      </div>
                    </CardHeader>
                    <CardContent className="p-0">
                      {channels.length === 0 ? (
                        <div className="p-6">
                          <EmptyState
                            icon={<MessageSquare className="w-8 h-8" />}
                            title="No channels"
                            description="Create your first channel to start chatting"
                            action={{
                              label: 'Create Channel',
                              onClick: () => {}, // Will be handled by CreateChannelModal
                              variant: 'primary'
                            }}
                          />
                        </div>
                      ) : (
                        <div className="space-y-1">
                          {channels.map((channel) => {
                            const unread = unreadCounts.find(c => c.channelId === channel.id);
                            return (
                              <div
                                key={channel.id}
                                className={`
                                  p-3 cursor-pointer hover:bg-muted/50 border-b transition-colors
                                  ${selectedChannel?.id === channel.id ? 'bg-primary/10 border-primary' : ''}
                                `}
                                onClick={() => {
                                  setSelectedChannel(channel);
                                  loadMessages(channel.id);
                                }}
                              >
                                <div className="flex items-center justify-between">
                                  <span className="font-medium text-sm"># {channel.name}</span>
                                  {unread?.unreadCount > 0 && (
                                    <Badge variant="destructive" className="text-xs">
                                      {unread.unreadCount}
                                    </Badge>
                                  )}
                                </div>
                                <p className="text-xs text-muted-foreground truncate">
                                  {channel.description || `${channel._count.members} members`}
                                </p>
                              </div>
                            );
                          })}
                        </div>
                      )}
                    </CardContent>
                  </Card>
                </div>

                {/* Chat Area */}
                <div className="lg:col-span-3">
                  <Card className="h-[600px]">
                    {selectedChannel ? (
                      <ChatChannel
                        channel={selectedChannel}
                        messages={messages}
                        onSendMessage={sendMessage}
                        onLoadMessages={(options) => loadMessages(selectedChannel.id, options)}
                        loading={loading}
                        currentUserId={session?.user?.id || ''}
                      />
                    ) : (
                      <div className="flex items-center justify-center h-full">
                        <EmptyState
                          icon={<MessageSquare className="w-12 h-12" />}
                          title="Select a channel"
                          description="Choose a channel from the sidebar to start chatting"
                        />
                      </div>
                    )}
                  </Card>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="calendar" className="space-y-6">
              <div className="flex items-center justify-between">
                <div>
                  <h2 className="text-xl font-semibold">Calendar</h2>
                  <p className="text-sm text-muted-foreground">
                    Manage your events and meetings
                  </p>
                </div>
                <PermissionGate
                  resource={PermissionResource.COMMUNICATION}
                  action={PermissionAction.CREATE}
                >
                  <CreateEventModal
                    onCreateEvent={handleCreateEvent}
                    trigger={
                      <Button>
                        <Plus className="h-4 w-4 mr-2" />
                        New Event
                      </Button>
                    }
                  />
                </PermissionGate>
              </div>

              <Card>
                <CardContent className="p-6">
                  <CalendarView
                    events={events}
                    onCreateEvent={() => {}} // Not used since we have the modal
                    onEventClick={handleEventClick}
                    loading={loading}
                    currentUserId={session?.user?.id || ''}
                  />
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="templates" className="space-y-6">
              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div>
                      <CardTitle>Email Templates</CardTitle>
                      <p className="text-sm text-muted-foreground mt-1">
                        Create and manage reusable email templates
                      </p>
                    </div>
                    <PermissionGate
                      resource={PermissionResource.COMMUNICATION}
                      action={PermissionAction.CREATE}
                    >
                      <Button>
                        <Plus className="h-4 w-4 mr-2" />
                        New Template
                      </Button>
                    </PermissionGate>
                  </div>
                </CardHeader>
                <CardContent>
                  <EmptyState
                    icon={<Mail className="w-12 h-12" />}
                    title="No templates yet"
                    description="Email template management will be implemented here. Create your first template to get started."
                    action={{
                      label: 'Create Template',
                      onClick: () => {
                        // TODO: Implement template creation
                        console.log('Create template clicked');
                      },
                      variant: 'primary'
                    }}
                  />
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </PageLayout>
    </PermissionGate>
  );
}
