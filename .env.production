# Database (Single Database Multi-Tenant)
DATABASE_URL="postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require"
DATABASE_POOL_SIZE=20
DATABASE_TIMEOUT=30000

# Authentication & Authorization
NEXTAUTH_SECRET="c0d67f6e8b9fc3fa1e5c8f39a81423f01cf67c50e8ec7af5b87f0dbe14764b43"
NEXTAUTH_URL="http://localhost:3000"
JWT_SECRET="7f9c2ba4f5a1b1d3c72e8df94bfa2e8f4c75b4a07bcbba6b92f6e7f3d9b44d6cf09b5eaa00b2bbf282f3e97ad162af3f67a1c89c3ec9b747be734e0de75f6f23"
JWT_EXPIRES_IN="24h"
PERMISSION_CACHE_TTL=3600  # seconds

# Permission Caching Configuration
NEXT_PUBLIC_CACHE_ENCRYPTION_KEY="828d96ed727c280fff91479f1605e4982e38468c358937f8a0ae5e8a8014e299"
NEXT_PUBLIC_WEBSOCKET_URL="http://localhost:3000"
WEBSOCKET_PORT=3001
ENABLE_PERMISSION_CACHE=true
ENABLE_REALTIME_UPDATES=true
PERMISSION_CACHE_DEFAULT_TTL=3600000
PERMISSION_CACHE_ENCRYPT=true
PERMISSION_CACHE_VALIDATE_INTEGRITY=true

# AI Provider Configuration (Configurable)
AI_PROVIDER="google"  # Options: google, openai, anthropic
AI_FALLBACK_PROVIDER="openai"  # Fallback provider if primary fails

# Google AI Configuration
GOOGLE_AI_API_KEY="AIzaSyACZP3XksZA30Wx-y-JzQTSy6hzr6APQik"
GOOGLE_AI_MODEL="gemini-2.0-flash"  # Options: gemini-1.5-pro, gemini-1.5-flash
GOOGLE_AI_TEMPERATURE=0.7
GOOGLE_AI_MAX_TOKENS=1000

# OpenAI Configuration
OPENAI_API_KEY="your_openai_api_key_here"
OPENAI_MODEL="gpt-4"  # Options: gpt-4, gpt-4-turbo, gpt-3.5-turbo
OPENAI_TEMPERATURE=0.7
OPENAI_MAX_TOKENS=1000

# Anthropic Configuration
ANTHROPIC_API_KEY="your_anthropic_api_key_here"
ANTHROPIC_MODEL="claude-3-sonnet-20240229"  # Options: claude-3-opus, claude-3-sonnet, claude-3-haiku
ANTHROPIC_TEMPERATURE=0.7
ANTHROPIC_MAX_TOKENS=1000

# Redis (for AI caching)
REDIS_URL="redis://**************:6379"

# Internationalization
DEFAULT_LOCALE="en"
SUPPORTED_LOCALES="en,ar"

# Multi-Tenant Configuration
PLATFORM_DOMAIN="crm-platform.com"
DEFAULT_TENANT_PLAN="starter"
MAX_TENANTS_PER_EMAIL=5
ENABLE_TENANT_REGISTRATION=true

# Stripe Configuration
STRIPE_SECRET_KEY="sk_test_your_stripe_secret_key"
STRIPE_PUBLISHABLE_KEY="pk_test_your_stripe_publishable_key"
STRIPE_WEBHOOK_SECRET="whsec_your_webhook_secret"
STRIPE_PRICE_STARTER="price_starter_monthly"
STRIPE_PRICE_PRO="price_pro_monthly"
STRIPE_PRICE_ENTERPRISE="price_enterprise_monthly"

# Usage Limits (Default for Starter Plan)
DEFAULT_CONTACTS_LIMIT=1000
DEFAULT_LEADS_LIMIT=500
DEFAULT_USERS_LIMIT=5
DEFAULT_AI_REQUESTS_LIMIT=100
DEFAULT_STORAGE_LIMIT_MB=1000

# AI Configuration (Provider-agnostic)
AI_RATE_LIMIT_PER_MINUTE=60
AI_CACHE_TTL_SECONDS=3600
AI_ENABLE_FALLBACK=true
AI_HEALTH_CHECK_INTERVAL=300  # seconds

# Billing Configuration
BILLING_CYCLE_DAY=1  # Day of month for billing
TRIAL_PERIOD_DAYS=14
GRACE_PERIOD_DAYS=7
DUNNING_RETRY_ATTEMPTS=3

# Performance Configuration
ENABLE_PERFORMANCE_MONITORING=true
SLOW_QUERY_THRESHOLD_MS=1000
CACHE_DEFAULT_TTL=300  # 5 minutes
API_RESPONSE_CACHE_TTL=300  # 5 minutes
SESSION_CACHE_TTL=86400  # 24 hours
PERFORMANCE_METRICS_RETENTION_HOURS=24
ENABLE_QUERY_LOGGING=false  # Set to true only in development
BUNDLE_ANALYZER=false  # Set to true to enable webpack bundle analyzer

# Memory and Resource Limits
MAX_MEMORY_USAGE_PERCENT=80
ENABLE_GARBAGE_COLLECTION_MONITORING=true
CLEANUP_INTERVAL_MINUTES=60

# SMTP Email Configuration
SMTP_HOST="smtp.gmail.com"
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER="<EMAIL>"
SMTP_PASS="zybdcywbjmydfmza"
SMTP_FROM_EMAIL="<EMAIL>"
SMTP_FROM_NAME="CRM Platform"
SMTP_REPLY_TO="<EMAIL>"

# Email Features
ENABLE_EMAIL_SENDING=true
EMAIL_RATE_LIMIT_PER_HOUR=100
EMAIL_ATTACHMENT_MAX_SIZE_MB=10
EMAIL_TEMPLATE_CACHE_TTL=3600

# File Upload Configuration
UPLOAD_DIR="./uploads"
MAX_FILE_SIZE_MB=50
ALLOWED_FILE_TYPES="pdf,doc,docx,xls,xlsx,ppt,pptx,txt,csv,jpg,jpeg,png,gif,webp,svg,zip,rar,7z,json,xml"