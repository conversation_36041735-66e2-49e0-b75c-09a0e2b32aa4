'use client';

import React, { useState, useEffect } from 'react';
import { TeamWithDetails, TeamMember } from '@/types';
import { ContactAvatar } from '@/components/ui/contact-avatar';
import { PermissionGate } from '@/components/ui/permission-gate';
import { PermissionAction, PermissionResource } from '@/types';
import { TeamMembers } from '@/components/admin/team-members';
import { AddMemberModal } from '@/components/admin/add-member-modal';
import { EditTeamModal } from '@/components/admin/edit-team-modal';
import { TeamAnalytics } from '@/components/admin/team-analytics';
import {
  XMarkIcon,
  UserGroupIcon,
  UserPlusIcon,
  PencilIcon,
  TrashIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
  ChartBarIcon
} from '@heroicons/react/24/outline';

interface TeamDetailsModalProps {
  team: TeamWithDetails;
  onClose: () => void;
  onTeamUpdated: () => void;
}

export function TeamDetailsModal({ team, onClose, onTeamUpdated }: TeamDetailsModalProps) {
  const [activeTab, setActiveTab] = useState<'overview' | 'members' | 'analytics'>('overview');
  const [members, setMembers] = useState<TeamMember[]>([]);
  const [filteredMembers, setFilteredMembers] = useState<TeamMember[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [roleFilter, setRoleFilter] = useState<string>('all');
  const [showAddMemberModal, setShowAddMemberModal] = useState(false);
  const [showEditTeamModal, setShowEditTeamModal] = useState(false);

  useEffect(() => {
    if (activeTab === 'members') {
      fetchMembers();
    }
  }, [activeTab, team.id]);

  useEffect(() => {
    filterMembers();
  }, [members, searchTerm, roleFilter]);

  const fetchMembers = async () => {
    try {
      setIsLoading(true);
      const response = await fetch(`/api/teams/members?teamId=${team.id}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch team members');
      }

      const data = await response.json();
      setMembers(data.data.members);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch members');
    } finally {
      setIsLoading(false);
    }
  };

  const filterMembers = () => {
    let filtered = members;

    if (searchTerm) {
      filtered = filtered.filter(member =>
        member.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        member.lastName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        member.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
        member.jobTitle?.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    if (roleFilter !== 'all') {
      filtered = filtered.filter(member => member.role === roleFilter);
    }

    setFilteredMembers(filtered);
  };

  const handleRemoveMember = async (userId: string) => {
    if (!confirm('Are you sure you want to remove this member from the team?')) {
      return;
    }

    try {
      const response = await fetch('/api/teams/members', {
        method: 'DELETE',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ teamId: team.id, userId }),
      });

      if (!response.ok) {
        throw new Error('Failed to remove member');
      }

      await fetchMembers();
      onTeamUpdated();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to remove member');
    }
  };

  const tabs = [
    { id: 'overview' as const, name: 'Overview', icon: ChartBarIcon },
    { id: 'members' as const, name: 'Members', icon: UserGroupIcon },
    { id: 'analytics' as const, name: 'Analytics', icon: ChartBarIcon }
  ];

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg w-full max-w-4xl max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center space-x-4">
            <div 
              className="w-12 h-12 rounded-lg flex items-center justify-center"
              style={{ backgroundColor: team.color || '#6B7280' }}
            >
              <UserGroupIcon className="w-6 h-6 text-white" />
            </div>
            <div>
              <h2 className="text-xl font-semibold text-gray-900">{team.name}</h2>
              {team.description && (
                <p className="text-gray-600">{team.description}</p>
              )}
            </div>
          </div>

          <div className="flex items-center space-x-3">
            <PermissionGate resource={PermissionResource.USERS} action={PermissionAction.MANAGE}>
              <button
                onClick={() => setShowEditTeamModal(true)}
                className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors flex items-center space-x-2"
              >
                <PencilIcon className="w-4 h-4" />
                <span>Edit Team</span>
              </button>
            </PermissionGate>

            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 p-2"
            >
              <XMarkIcon className="w-6 h-6" />
            </button>
          </div>
        </div>

        {/* Tabs */}
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-6">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <div className="flex items-center space-x-2">
                  <tab.icon className="w-4 h-4" />
                  <span>{tab.name}</span>
                </div>
              </button>
            ))}
          </nav>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-200px)]">
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-md p-4 mb-6">
              <p className="text-red-600">{error}</p>
              <button
                onClick={() => setError(null)}
                className="text-red-600 hover:text-red-800 underline mt-2"
              >
                Dismiss
              </button>
            </div>
          )}

          {activeTab === 'overview' && (
            <TeamOverview team={team} />
          )}

          {activeTab === 'members' && (
            <TeamMembers
              members={filteredMembers}
              isLoading={isLoading}
              searchTerm={searchTerm}
              roleFilter={roleFilter}
              onSearchChange={setSearchTerm}
              onRoleFilterChange={setRoleFilter}
              onAddMember={() => setShowAddMemberModal(true)}
              onRemoveMember={handleRemoveMember}
            />
          )}

          {activeTab === 'analytics' && (
            <TeamAnalytics team={team} members={members} />
          )}
        </div>
      </div>

      {/* Add Member Modal */}
      {showAddMemberModal && (
        <AddMemberModal
          teamId={team.id}
          onClose={() => setShowAddMemberModal(false)}
          onMemberAdded={() => {
            fetchMembers();
            onTeamUpdated();
            setShowAddMemberModal(false);
          }}
        />
      )}

      {/* Edit Team Modal */}
      {showEditTeamModal && (
        <EditTeamModal
          team={team}
          onClose={() => setShowEditTeamModal(false)}
          onTeamUpdated={() => {
            onTeamUpdated();
            setShowEditTeamModal(false);
          }}
        />
      )}
    </div>
  );
}

// Team Overview Component
function TeamOverview({ team }: { team: TeamWithDetails }) {
  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-blue-50 rounded-lg p-4">
          <div className="flex items-center">
            <UserGroupIcon className="w-8 h-8 text-blue-600" />
            <div className="ml-3">
              <p className="text-sm font-medium text-blue-600">Total Members</p>
              <p className="text-2xl font-bold text-blue-900">{team.memberCount}</p>
            </div>
          </div>
        </div>

        <div className="bg-green-50 rounded-lg p-4">
          <div className="flex items-center">
            <ChartBarIcon className="w-8 h-8 text-green-600" />
            <div className="ml-3">
              <p className="text-sm font-medium text-green-600">Status</p>
              <p className="text-lg font-semibold text-green-900">
                {team.isActive ? 'Active' : 'Inactive'}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-purple-50 rounded-lg p-4">
          <div className="flex items-center">
            <UserGroupIcon className="w-8 h-8 text-purple-600" />
            <div className="ml-3">
              <p className="text-sm font-medium text-purple-600">Created</p>
              <p className="text-lg font-semibold text-purple-900">
                {new Date(team.createdAt).toLocaleDateString()}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Team Manager */}
      {team.manager && (
        <div className="bg-gray-50 rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Team Manager</h3>
          <div className="flex items-center space-x-4">
            <ContactAvatar
              firstName={team.manager.firstName}
              lastName={team.manager.lastName}
              size="lg"
            />
            <div>
              <p className="text-lg font-medium text-gray-900">
                {team.manager.firstName} {team.manager.lastName}
              </p>
              <p className="text-gray-600">{team.manager.email}</p>
            </div>
          </div>
        </div>
      )}

      {/* Team Description */}
      {team.description && (
        <div>
          <h3 className="text-lg font-medium text-gray-900 mb-3">Description</h3>
          <p className="text-gray-600 leading-relaxed">{team.description}</p>
        </div>
      )}
    </div>
  );
}
