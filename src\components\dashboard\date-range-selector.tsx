'use client';

import React, { useState } from 'react';
import { CalendarIcon } from '@heroicons/react/24/outline';
import { Button } from '@/components/ui/button';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Calendar } from '@/components/ui/calendar';
import { cn } from '@/lib/utils';
import { format } from 'date-fns';

export interface DateRange {
  from: Date;
  to: Date;
}

interface DateRangeSelectorProps {
  value?: DateRange;
  onChange: (range: DateRange) => void;
  className?: string;
  presets?: Array<{
    label: string;
    value: DateRange;
  }>;
}

const defaultPresets = [
  {
    label: 'Last 7 days',
    value: {
      from: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
      to: new Date()
    }
  },
  {
    label: 'Last 30 days',
    value: {
      from: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
      to: new Date()
    }
  },
  {
    label: 'Last 90 days',
    value: {
      from: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000),
      to: new Date()
    }
  },
  {
    label: 'Last 12 months',
    value: {
      from: new Date(Date.now() - 365 * 24 * 60 * 60 * 1000),
      to: new Date()
    }
  }
];

export function DateRangeSelector({
  value,
  onChange,
  className,
  presets = defaultPresets
}: DateRangeSelectorProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedRange, setSelectedRange] = useState<DateRange | undefined>(value);

  const handlePresetSelect = (preset: DateRange) => {
    setSelectedRange(preset);
    onChange(preset);
    setIsOpen(false);
  };

  const handleDateSelect = (range: any) => {
    if (range?.from && range?.to) {
      const newRange = { from: range.from, to: range.to };
      setSelectedRange(newRange);
      onChange(newRange);
      setIsOpen(false);
    }
  };

  const formatDateRange = (range?: DateRange) => {
    if (!range) return 'Select date range';
    
    const fromStr = format(range.from, 'MMM dd, yyyy');
    const toStr = format(range.to, 'MMM dd, yyyy');
    
    return `${fromStr} - ${toStr}`;
  };

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          className={cn(
            'justify-start text-left font-normal',
            !selectedRange && 'text-muted-foreground',
            className
          )}
        >
          <CalendarIcon className="mr-2 h-4 w-4" />
          {formatDateRange(selectedRange)}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-auto p-0" align="start">
        <div className="flex">
          {/* Presets */}
          <div className="border-r p-3 space-y-1">
            <div className="text-sm font-medium mb-2">Quick Select</div>
            {presets.map((preset, index) => (
              <Button
                key={index}
                variant="ghost"
                size="sm"
                className="w-full justify-start text-sm"
                onClick={() => handlePresetSelect(preset.value)}
              >
                {preset.label}
              </Button>
            ))}
          </div>
          
          {/* Calendar */}
          <div className="p-3">
            <Calendar
              mode="range"
              selected={selectedRange}
              onSelect={handleDateSelect}
              numberOfMonths={2}
              className="rounded-md border"
            />
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
}
