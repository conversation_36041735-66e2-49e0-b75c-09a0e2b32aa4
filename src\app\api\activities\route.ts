import { NextResponse } from 'next/server';
import { ActivityManagementService, createActivitySchema, ActivityFilters } from '@/services/activity-management';
import { withPermission, AuthenticatedRequest } from '@/lib/permission-middleware';
import { PermissionResource, PermissionAction } from '@/types';
import { z } from 'zod';

const activityService = new ActivityManagementService();

// Validation schema for query parameters
const getActivitiesQuerySchema = z.object({
  relatedToType: z.string().optional(),
  relatedToId: z.string().optional(),
  type: z.string().optional(),
  ownerId: z.string().optional(),
  completed: z.string().transform(val => val === 'true').optional(),
  page: z.string().transform(val => parseInt(val) || 1).optional(),
  limit: z.string().transform(val => Math.min(parseInt(val) || 20, 100)).optional(),
  sortBy: z.enum(['scheduledAt', 'completedAt', 'createdAt', 'updatedAt']).optional(),
  sortOrder: z.enum(['asc', 'desc']).optional(),
});

/**
 * GET /api/activities
 * Get activities with filtering and pagination
 */
export const GET = withPermission({
  resource: PermissionResource.CONTACTS, // Using contacts permission for now
  action: PermissionAction.READ,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest) => {
  try {
    const { searchParams } = new URL(req.url);
    const queryParams = Object.fromEntries(searchParams.entries());
    
    const validatedQuery = getActivitiesQuerySchema.parse(queryParams);
    
    const filters: ActivityFilters = {
      relatedToType: validatedQuery.relatedToType,
      relatedToId: validatedQuery.relatedToId,
      type: validatedQuery.type,
      ownerId: validatedQuery.ownerId,
      completed: validatedQuery.completed,
      page: validatedQuery.page,
      limit: validatedQuery.limit,
      sortBy: validatedQuery.sortBy,
      sortOrder: validatedQuery.sortOrder,
    };

    const result = await activityService.getActivities(req.tenantId, filters);

    return NextResponse.json({
      success: true,
      data: result,
      message: 'Activities retrieved successfully'
    });

  } catch (error) {
    console.error('Get activities error:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          error: 'Invalid query parameters',
          details: error.errors.map(e => ({
            field: e.path.join('.'),
            message: e.message
          }))
        },
        { status: 400 }
      );
    }

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
});

/**
 * POST /api/activities
 * Create a new activity
 */
export const POST = withPermission({
  resource: PermissionResource.CONTACTS, // Using contacts permission for now
  action: PermissionAction.CREATE,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest) => {
  try {
    const body = await req.json();
    const validatedData = createActivitySchema.parse(body);

    const activity = await activityService.createActivity(
      req.tenantId,
      validatedData,
      req.userId
    );

    return NextResponse.json({
      success: true,
      data: { activity },
      message: 'Activity created successfully'
    }, { status: 201 });

  } catch (error) {
    console.error('Create activity error:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          error: 'Validation failed',
          details: error.errors.map(e => ({
            field: e.path.join('.'),
            message: e.message
          }))
        },
        { status: 400 }
      );
    }

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
});

/**
 * PUT /api/activities
 * Update an existing activity
 */
export const PUT = withPermission({
  resource: PermissionResource.CONTACTS, // Using contacts permission for now
  action: PermissionAction.UPDATE,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest) => {
  try {
    const body = await req.json();
    const { activityId, ...updateData } = body;

    if (!activityId) {
      return NextResponse.json(
        { error: 'Activity ID is required' },
        { status: 400 }
      );
    }

    const activity = await activityService.updateActivity(
      activityId,
      req.tenantId,
      updateData,
      req.userId
    );

    return NextResponse.json({
      success: true,
      data: { activity },
      message: 'Activity updated successfully'
    });

  } catch (error) {
    console.error('Update activity error:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          error: 'Validation failed',
          details: error.errors.map(e => ({
            field: e.path.join('.'),
            message: e.message
          }))
        },
        { status: 400 }
      );
    }

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
});

/**
 * DELETE /api/activities
 * Delete an activity
 */
export const DELETE = withPermission({
  resource: PermissionResource.CONTACTS, // Using contacts permission for now
  action: PermissionAction.DELETE,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest) => {
  try {
    const { searchParams } = new URL(req.url);
    const activityId = searchParams.get('activityId');

    if (!activityId) {
      return NextResponse.json(
        { error: 'Activity ID is required' },
        { status: 400 }
      );
    }

    await activityService.deleteActivity(activityId, req.tenantId);

    return NextResponse.json({
      success: true,
      message: 'Activity deleted successfully'
    });

  } catch (error) {
    console.error('Delete activity error:', error);

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
});
