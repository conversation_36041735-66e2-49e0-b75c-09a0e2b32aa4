# Markdown to DOCX Integration Guide

This document explains the integration of `@mohtasham/md-to-docx` library for generating professional Word documents from proposal content.

## Overview

The integration replaces the previous placeholder DOCX generation with a robust markdown-to-DOCX conversion system that produces properly formatted Microsoft Word documents with:

- Table of Contents with clickable links
- Professional styling and formatting
- Support for images, charts, and diagrams
- Page breaks and proper document structure
- Customizable styling options

## Architecture

### Components

1. **MarkdownConverter Service** (`src/services/markdown-converter.ts`)
   - Converts proposal content to markdown format
   - Handles DOCX generation with custom styling
   - Validates markdown content

2. **Enhanced DocumentGenerationService** (`src/services/document-generation.ts`)
   - Updated to use the new markdown converter
   - Maintains backward compatibility
   - Includes fallback to HTML generation

3. **Updated API Endpoints** (`src/app/api/proposals/[id]/generate-document/route.ts`)
   - Enhanced validation schema with new styling options
   - Support for advanced document formatting

## Features

### Markdown Conversion

The system converts proposal content to markdown with:

- **Table of Contents**: Automatically generated with `[TOC]` marker
- **Document Metadata**: Proposal information, generation details
- **Sections**: Properly formatted headings and content
- **Charts**: Embedded images with mermaid code references
- **Page Breaks**: Strategic placement for professional layout

### DOCX Generation

Generated documents include:

- **Professional Styling**: Consistent fonts, spacing, and alignment
- **Clickable TOC**: Navigation links to sections
- **Page Numbering**: Centered footer pagination
- **Image Support**: Embedded charts and diagrams
- **Custom Formatting**: Configurable styling options

### Styling Options

#### Basic Options (Legacy Support)
```typescript
{
  fontFamily: string;
  fontSize: number;
  headerColor: string;
  accentColor: string;
  logoUrl: string;
  companyName: string;
}
```

#### Advanced Options (New)
```typescript
{
  documentType: 'document' | 'report';
  titleSize: number;
  headingSpacing: number;
  paragraphSpacing: number;
  lineSpacing: number;
  heading1Size: number;
  heading2Size: number;
  heading3Size: number;
  heading4Size: number;
  heading5Size: number;
  paragraphSize: number;
  listItemSize: number;
  codeBlockSize: number;
  blockquoteSize: number;
  tocFontSize: number;
  paragraphAlignment: 'LEFT' | 'RIGHT' | 'CENTER' | 'JUSTIFIED';
  headingAlignment: 'LEFT' | 'RIGHT' | 'CENTER' | 'JUSTIFIED';
  heading1Alignment: 'LEFT' | 'RIGHT' | 'CENTER' | 'JUSTIFIED';
  heading2Alignment: 'LEFT' | 'RIGHT' | 'CENTER' | 'JUSTIFIED';
  heading3Alignment: 'LEFT' | 'RIGHT' | 'CENTER' | 'JUSTIFIED';
  heading4Alignment: 'LEFT' | 'RIGHT' | 'CENTER' | 'JUSTIFIED';
  heading5Alignment: 'LEFT' | 'RIGHT' | 'CENTER' | 'JUSTIFIED';
  blockquoteAlignment: 'LEFT' | 'RIGHT' | 'CENTER' | 'JUSTIFIED';
}
```

## Usage

### API Request

```javascript
POST /api/proposals/{id}/generate-document

{
  "format": "docx",
  "includeCharts": true,
  "includeBranding": true,
  "customStyling": {
    "documentType": "report",
    "titleSize": 36,
    "paragraphAlignment": "JUSTIFIED",
    "headingAlignment": "LEFT",
    "lineSpacing": 1.2
  }
}
```

### Programmatic Usage

```typescript
import { MarkdownConverter } from '@/services/markdown-converter';

// Convert content to markdown
const markdown = MarkdownConverter.convertToMarkdown(proposalContent);

// Generate DOCX
await MarkdownConverter.generateDocx(
  markdown, 
  outputPath, 
  customStyling
);
```

## Testing

### Unit Tests

Run the test suite:
```bash
npm test src/__tests__/markdown-converter.test.ts
```

### Integration Test

Test the complete integration:
```bash
node scripts/test-markdown-docx.js
```

This will generate sample documents with both default and custom styling.

## Document Structure

Generated documents follow this structure:

1. **Table of Contents**
2. **Document Title**
3. **Document Information Section**
   - Generation details
   - Opportunity information
   - Contact details
4. **Main Content Sections**
   - Executive Summary
   - Technical Approach
   - Implementation Plan
   - etc.
5. **Architecture Diagrams** (if included)
   - Chart images
   - Mermaid code references
6. **Document Footer**
   - Proposal ID
   - Generation timestamp

## Error Handling

The system includes comprehensive error handling:

- **Validation**: Markdown content validation before processing
- **Fallback**: Automatic fallback to HTML generation if markdown fails
- **File Operations**: Proper error handling for file I/O operations
- **Logging**: Detailed error logging for debugging

## Performance Considerations

- **File Size**: Generated DOCX files are typically 50-200KB
- **Processing Time**: Document generation takes 1-3 seconds
- **Memory Usage**: Efficient processing with minimal memory footprint
- **Concurrent Requests**: Supports multiple simultaneous generations

## Troubleshooting

### Common Issues

1. **Installation Problems**
   ```bash
   npm install @mohtasham/md-to-docx
   ```

2. **File Permission Errors**
   - Ensure write permissions to uploads directory
   - Check disk space availability

3. **Styling Issues**
   - Verify styling options are within valid ranges
   - Check alignment values are correct enums

4. **Image Problems**
   - Ensure chart images exist at specified paths
   - Verify image file formats are supported

### Debug Mode

Enable debug mode in development:
```typescript
// Saves markdown files for inspection
process.env.NODE_ENV = 'development'
```

## Migration Guide

### From Previous Implementation

The new system is backward compatible:

1. **Existing API calls** continue to work
2. **Legacy styling options** are automatically mapped
3. **Fallback mechanism** ensures reliability

### Recommended Updates

1. **Update styling options** to use new advanced features
2. **Test document generation** with your content
3. **Customize styling** to match brand guidelines

## Future Enhancements

Planned improvements:

1. **Template System**: Predefined document templates
2. **Brand Integration**: Automatic logo and color scheme application
3. **Multi-language Support**: Internationalization for document content
4. **Advanced Charts**: Enhanced chart rendering and embedding
5. **Collaborative Features**: Comments and review workflows

## Support

For issues or questions:

1. Check the test script output for diagnostics
2. Review error logs in the console
3. Verify @mohtasham/md-to-docx documentation
4. Test with minimal content to isolate issues
