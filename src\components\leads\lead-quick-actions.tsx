'use client';

import React, { useState } from 'react';
import { Menu } from '@headlessui/react';
import {
  ChevronDownIcon,
  PlusIcon,
  SparklesIcon,
  EnvelopeIcon,
  PhoneIcon,
  ArrowPathIcon,
  DocumentTextIcon,
  PencilIcon,
  TrashIcon
} from '@heroicons/react/24/outline';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { AnalysisDialog, LeadAnalysisResult } from '@/components/ui/analysis-dialog';
import { useToast } from '@/hooks/use-toast';
import { PermissionGate } from '@/components/ui/permission-gate';
import { PermissionResource, PermissionAction } from '@/types';

interface LeadQuickActionsProps {
  leadId: string;
  leadData: any;
  onEmailClick?: () => void;
  onMeetingClick?: () => void;
  onConvertClick?: () => void;
  onEditClick?: () => void;
  onDeleteClick?: () => void;
  onScoreUpdate?: (score: LeadAnalysisResult) => void;
}

export function LeadQuickActions({
  leadId,
  leadData,
  onEmailClick,
  onMeetingClick,
  onConvertClick,
  onEditClick,
  onDeleteClick,
  onScoreUpdate
}: LeadQuickActionsProps) {
  const [analysisDialogOpen, setAnalysisDialogOpen] = useState(false);
  const [analysisResult, setAnalysisResult] = useState<LeadAnalysisResult | null>(null);
  const [analyzing, setAnalyzing] = useState(false);
  const { toast } = useToast();

  const handleAnalyzeLead = async () => {
    setAnalysisDialogOpen(true);
    setAnalyzing(true);
    setAnalysisResult(null);

    try {
      // First, fetch comprehensive lead data including activities, emails, and calls
      let leadDataToAnalyze = leadData;

      try {
        const comprehensiveDataResponse = await fetch(`/api/leads/${leadId}/comprehensive`);

        if (comprehensiveDataResponse.ok) {
          const comprehensiveData = await comprehensiveDataResponse.json();

          if (comprehensiveData.success) {
            leadDataToAnalyze = comprehensiveData.data.lead;
            console.log('Using comprehensive lead data for AI analysis');
          } else {
            console.warn('Failed to fetch comprehensive data, using basic lead data:', comprehensiveData.error);
          }
        } else {
          console.warn('Comprehensive data endpoint failed, using basic lead data');
        }
      } catch (comprehensiveError) {
        console.warn('Error fetching comprehensive data, falling back to basic lead data:', comprehensiveError);
      }

      // Now send the lead data (comprehensive or basic) to AI for scoring
      const response = await fetch('/api/ai/lead-scoring', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          leadData: leadDataToAnalyze,
          includeQualification: true
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to analyze lead');
      }

      const result = await response.json();
      const analysisData: LeadAnalysisResult = {
        ...result.data.score,
        qualification: result.data.qualification,
        processingTime: Date.now() // Approximate processing time
      };

      setAnalysisResult(analysisData);

      if (onScoreUpdate) {
        onScoreUpdate(analysisData);
      }

      toast({
        title: "Analysis Complete",
        description: `Lead scored ${result.data.score.overall}/100 with ${Math.round(result.data.score.confidence * 100)}% confidence`,
      });

    } catch (error) {
      console.error('Error analyzing lead:', error);
      toast({
        title: "Analysis Failed",
        description: error instanceof Error ? error.message : 'Failed to analyze lead',
        variant: "destructive",
      });
      setAnalysisDialogOpen(false);
    } finally {
      setAnalyzing(false);
    }
  };

  const quickActions = [
    {
      name: 'Analyze Lead',
      description: 'AI-powered lead scoring and qualification',
      icon: SparklesIcon,
      color: 'text-blue-600 dark:text-blue-400',
      bgColor: 'bg-blue-50 dark:bg-blue-900/20',
      onClick: handleAnalyzeLead,
    },
    {
      name: 'Send Email',
      description: 'Compose and send an email',
      icon: EnvelopeIcon,
      color: 'text-green-600 dark:text-green-400',
      bgColor: 'bg-green-50 dark:bg-green-900/20',
      onClick: onEmailClick,
    },
    {
      name: 'Schedule Meeting',
      description: 'Schedule a meeting with the lead',
      icon: PhoneIcon,
      color: 'text-purple-600 dark:text-purple-400',
      bgColor: 'bg-purple-50 dark:bg-purple-900/20',
      onClick: onMeetingClick,
    },
    {
      name: 'Convert Lead',
      description: 'Convert to opportunity',
      icon: ArrowPathIcon,
      color: 'text-orange-600 dark:text-orange-400',
      bgColor: 'bg-orange-50 dark:bg-orange-900/20',
      onClick: onConvertClick,
    },
    {
      name: 'Edit Lead',
      description: 'Edit lead information',
      icon: PencilIcon,
      color: 'text-indigo-600 dark:text-indigo-400',
      bgColor: 'bg-indigo-50 dark:bg-indigo-900/20',
      onClick: onEditClick,
      permission: { resource: PermissionResource.LEADS, action: PermissionAction.UPDATE },
    },
    {
      name: 'Delete Lead',
      description: 'Delete this lead',
      icon: TrashIcon,
      color: 'text-red-600 dark:text-red-400',
      bgColor: 'bg-red-50 dark:bg-red-900/20',
      onClick: onDeleteClick,
      permission: { resource: PermissionResource.LEADS, action: PermissionAction.DELETE },
    },
  ];

  return (
    <>
      <Menu as="div" className="relative inline-block text-left">
        <div>
          <Menu.Button as={Button} variant="outline" size="sm" className="flex items-center gap-1">
            <PlusIcon className="h-4 w-4" aria-hidden="true" />
            Quick Actions
            <ChevronDownIcon className="h-4 w-4" aria-hidden="true" />
          </Menu.Button>
        </div>

        <Menu.Items className="absolute right-0 z-10 mt-2 w-80 origin-top-right rounded-md bg-white dark:bg-gray-800 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
          <div className="py-1">
            {quickActions.map((action) => {
              const ActionButton = (
                <Menu.Item key={action.name}>
                  {({ active }) => (
                    <button
                      onClick={action.onClick}
                      disabled={action.name === 'Analyze Lead' && analyzing}
                      className={cn(
                        active
                          ? 'bg-gray-100 dark:bg-gray-700'
                          : 'bg-white dark:bg-gray-800',
                        'group flex w-full items-center px-4 py-3 text-sm transition-colors disabled:opacity-50 disabled:cursor-not-allowed'
                      )}
                    >
                      <div className={cn(
                        'flex-shrink-0 p-2 rounded-md mr-3',
                        action.bgColor
                      )}>
                        {action.name === 'Analyze Lead' && analyzing ? (
                          <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600"></div>
                        ) : (
                          <action.icon
                            className={cn('h-5 w-5', action.color)}
                            aria-hidden="true"
                          />
                        )}
                      </div>
                      <div className="flex-1 text-left">
                        <p className="font-medium text-gray-900 dark:text-white">
                          {action.name === 'Analyze Lead' && analyzing ? 'Analyzing...' : action.name}
                        </p>
                        <p className="text-xs text-gray-500 dark:text-gray-400">
                          {action.description}
                        </p>
                      </div>
                    </button>
                  )}
                </Menu.Item>
              );

              // Wrap actions that require permissions with PermissionGate
              if (action.permission) {
                return (
                  <PermissionGate
                    key={action.name}
                    resource={action.permission.resource}
                    action={action.permission.action}
                  >
                    {ActionButton}
                  </PermissionGate>
                );
              }

              return ActionButton;
            })}
          </div>
        </Menu.Items>
      </Menu>

      {/* Analysis Dialog */}
      <AnalysisDialog
        open={analysisDialogOpen}
        onOpenChange={setAnalysisDialogOpen}
        result={analysisResult}
        loading={analyzing}
        title="Lead Analysis Results"
        description="AI-powered lead scoring and qualification analysis"
      />
    </>
  );
}
