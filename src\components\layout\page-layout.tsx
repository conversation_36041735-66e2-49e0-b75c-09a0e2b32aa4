"use client"

import { SiteHeader } from "@/components/site-header"

interface PageLayoutProps {
  children: React.ReactNode
  title?: string
  description?: string
  actions?: React.ReactNode
}

export function PageLayout({ children, title, description, actions }: PageLayoutProps) {
  return (
    <>
      <SiteHeader />
      <div className="flex flex-1 flex-col">
        <div className="@container/main flex flex-1 flex-col gap-2">
          <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
            {(title || description || actions) && (
              <div className="px-4 lg:px-6">
                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    {title && (
                      <h1 className="text-2xl font-semibold tracking-tight">
                        {title}
                      </h1>
                    )}
                    {description && (
                      <p className="text-sm text-muted-foreground">
                        {description}
                      </p>
                    )}
                  </div>
                  {actions && (
                    <div className="flex items-center gap-2">
                      {actions}
                    </div>
                  )}
                </div>
              </div>
            )}
            <div className="px-4 lg:px-6">
              {children}
            </div>
          </div>
        </div>
      </div>
    </>
  )
}
