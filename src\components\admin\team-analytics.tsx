'use client';

import React from 'react';
import { TeamWithDetails, TeamMember } from '@/types';
import {
  ChartBarIcon,
  UserGroupIcon,
  ClockIcon,
  ArrowTrendingUpIcon,
  CalendarIcon
} from '@heroicons/react/24/outline';

interface TeamAnalyticsProps {
  team: TeamWithDetails;
  members: TeamMember[];
}

export function TeamAnalytics({ team, members }: TeamAnalyticsProps) {
  // Calculate analytics
  const totalMembers = members.length;
  const activeMembers = members.filter(m => m.status === 'active').length;
  const pendingMembers = members.filter(m => m.status === 'pending').length;
  const inactiveMembers = members.filter(m => m.status === 'inactive').length;

  // Calculate role distribution
  const roleDistribution = members.reduce((acc, member) => {
    acc[member.role] = (acc[member.role] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  // Calculate recent activity (mock data for now)
  const recentJoins = members.filter(m => {
    const joinDate = new Date(m.joinedAt);
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    return joinDate > thirtyDaysAgo;
  }).length;

  const stats = [
    {
      name: 'Total Members',
      value: totalMembers,
      icon: UserGroupIcon,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50'
    },
    {
      name: 'Active Members',
      value: activeMembers,
      icon: ArrowTrendingUpIcon,
      color: 'text-green-600',
      bgColor: 'bg-green-50'
    },
    {
      name: 'Pending Invites',
      value: pendingMembers,
      icon: ClockIcon,
      color: 'text-yellow-600',
      bgColor: 'bg-yellow-50'
    },
    {
      name: 'Recent Joins (30d)',
      value: recentJoins,
      icon: CalendarIcon,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50'
    }
  ];

  return (
    <div className="space-y-6">
      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {stats.map((stat) => (
          <div key={stat.name} className={`${stat.bgColor} rounded-lg p-4`}>
            <div className="flex items-center">
              <div className={`${stat.color} p-2 rounded-lg`}>
                <stat.icon className="w-6 h-6" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-600">{stat.name}</p>
                <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Role Distribution */}
      <div className="bg-white border border-gray-200 rounded-lg p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
          <ChartBarIcon className="w-5 h-5 mr-2" />
          Role Distribution
        </h3>
        
        {Object.keys(roleDistribution).length === 0 ? (
          <p className="text-gray-500 text-center py-8">No role data available</p>
        ) : (
          <div className="space-y-3">
            {Object.entries(roleDistribution).map(([role, count]) => {
              const percentage = totalMembers > 0 ? (count / totalMembers) * 100 : 0;
              return (
                <div key={role} className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <span className="text-sm font-medium text-gray-900">{role}</span>
                    <span className="text-sm text-gray-500">({count} member{count !== 1 ? 's' : ''})</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-24 bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-blue-600 h-2 rounded-full"
                        style={{ width: `${percentage}%` }}
                      />
                    </div>
                    <span className="text-sm text-gray-600 w-12 text-right">
                      {percentage.toFixed(0)}%
                    </span>
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </div>

      {/* Team Health */}
      <div className="bg-white border border-gray-200 rounded-lg p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Team Health</h3>
        
        <div className="space-y-4">
          {/* Activity Rate */}
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium text-gray-700">Activity Rate</span>
            <div className="flex items-center space-x-2">
              <div className="w-32 bg-gray-200 rounded-full h-2">
                <div
                  className="bg-green-500 h-2 rounded-full"
                  style={{ width: `${totalMembers > 0 ? (activeMembers / totalMembers) * 100 : 0}%` }}
                />
              </div>
              <span className="text-sm text-gray-600">
                {totalMembers > 0 ? Math.round((activeMembers / totalMembers) * 100) : 0}%
              </span>
            </div>
          </div>

          {/* Completion Rate */}
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium text-gray-700">Onboarding Rate</span>
            <div className="flex items-center space-x-2">
              <div className="w-32 bg-gray-200 rounded-full h-2">
                <div
                  className="bg-blue-500 h-2 rounded-full"
                  style={{ 
                    width: `${totalMembers > 0 ? ((totalMembers - pendingMembers) / totalMembers) * 100 : 0}%` 
                  }}
                />
              </div>
              <span className="text-sm text-gray-600">
                {totalMembers > 0 ? Math.round(((totalMembers - pendingMembers) / totalMembers) * 100) : 0}%
              </span>
            </div>
          </div>

          {/* Growth Trend */}
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium text-gray-700">30-Day Growth</span>
            <div className="flex items-center space-x-2">
              <ArrowTrendingUpIcon className="w-4 h-4 text-green-500" />
              <span className="text-sm text-green-600">+{recentJoins} new members</span>
            </div>
          </div>
        </div>
      </div>

      {/* Quick Insights */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h4 className="text-sm font-medium text-blue-900 mb-2">Quick Insights</h4>
        <ul className="text-sm text-blue-800 space-y-1">
          {activeMembers === totalMembers && totalMembers > 0 && (
            <li>• All team members are active - great engagement!</li>
          )}
          {pendingMembers > 0 && (
            <li>• {pendingMembers} pending invite{pendingMembers !== 1 ? 's' : ''} - follow up on onboarding</li>
          )}
          {recentJoins > 0 && (
            <li>• {recentJoins} new member{recentJoins !== 1 ? 's' : ''} joined recently - ensure proper onboarding</li>
          )}
          {totalMembers === 0 && (
            <li>• Team has no members yet - start by adding team members</li>
          )}
        </ul>
      </div>
    </div>
  );
}
