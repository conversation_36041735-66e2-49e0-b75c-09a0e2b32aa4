import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { hasPermission } from '@/lib/permissions';
import { PermissionResource, PermissionAction } from '@/types';
import { prisma } from '@/lib/prisma';

/**
 * GET /api/proposals/[id]/sections
 * Get all sections for a proposal
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user's current tenant
    const currentTenant = session.user.tenants?.[0];
    if (!currentTenant) {
      return NextResponse.json({ error: 'No tenant access' }, { status: 403 });
    }

    // Check permission for proposal reading
    const hasAccess = await hasPermission(
      PermissionResource.PROPOSALS,
      PermissionAction.READ,
      currentTenant.id
    );

    if (!hasAccess) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const proposalId = params.id;

    // Verify proposal exists and belongs to tenant
    const proposal = await prisma.proposal.findFirst({
      where: {
        id: proposalId,
        tenantId: currentTenant.id
      }
    });

    if (!proposal) {
      return NextResponse.json(
        { error: 'Proposal not found or access denied' },
        { status: 404 }
      );
    }

    // Get all sections for the proposal
    const sections = await prisma.proposalSection.findMany({
      where: {
        proposalId,
        tenantId: currentTenant.id
      },
      include: {
        createdBy: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true
          }
        }
      },
      orderBy: {
        orderIndex: 'asc'
      }
    });

    // Get charts for the proposal
    const charts = await prisma.proposalChart.findMany({
      where: {
        proposalId,
        tenantId: currentTenant.id
      },
      include: {
        createdBy: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true
          }
        }
      },
      orderBy: {
        orderIndex: 'asc'
      }
    });

    // Calculate statistics
    const totalWordCount = sections.reduce((sum, section) => sum + (section.wordCount || 0), 0);
    const totalTokensUsed = sections.reduce((sum, section) => sum + (section.tokensUsed || 0), 0);
    const completedSections = sections.filter(s => s.status === 'generated' || s.status === 'approved').length;
    const renderedCharts = charts.filter(c => c.status === 'rendered').length;

    return NextResponse.json({
      success: true,
      data: {
        proposalId: proposal.id,
        proposalTitle: proposal.title,
        generationStatus: proposal.generationStatus,
        sections,
        charts,
        statistics: {
          totalSections: sections.length,
          completedSections,
          totalCharts: charts.length,
          renderedCharts,
          totalWordCount,
          totalTokensUsed,
          completionPercentage: sections.length > 0 ? Math.round((completedSections / sections.length) * 100) : 0
        }
      },
      message: 'Sections retrieved successfully'
    });

  } catch (error) {
    console.error('Error retrieving proposal sections:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * @swagger
 * /api/proposals/{id}/sections:
 *   get:
 *     summary: Get proposal sections
 *     description: Retrieve all sections and charts for a proposal with statistics
 *     tags: [Proposals]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Proposal ID
 *     responses:
 *       200:
 *         description: Sections retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     proposalId:
 *                       type: string
 *                     proposalTitle:
 *                       type: string
 *                     generationStatus:
 *                       type: string
 *                       enum: [pending, generating, completed, failed]
 *                     sections:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: string
 *                           sectionType:
 *                             type: string
 *                           title:
 *                             type: string
 *                           content:
 *                             type: string
 *                           status:
 *                             type: string
 *                           wordCount:
 *                             type: number
 *                           tokensUsed:
 *                             type: number
 *                           orderIndex:
 *                             type: number
 *                           isAiGenerated:
 *                             type: boolean
 *                           generatedAt:
 *                             type: string
 *                             format: date-time
 *                           createdBy:
 *                             type: object
 *                             properties:
 *                               id:
 *                                 type: string
 *                               firstName:
 *                                 type: string
 *                               lastName:
 *                                 type: string
 *                               email:
 *                                 type: string
 *                     charts:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: string
 *                           chartType:
 *                             type: string
 *                           title:
 *                             type: string
 *                           description:
 *                             type: string
 *                           mermaidCode:
 *                             type: string
 *                           renderedImagePath:
 *                             type: string
 *                           status:
 *                             type: string
 *                           orderIndex:
 *                             type: number
 *                           isAiGenerated:
 *                             type: boolean
 *                           generatedAt:
 *                             type: string
 *                             format: date-time
 *                     statistics:
 *                       type: object
 *                       properties:
 *                         totalSections:
 *                           type: number
 *                         completedSections:
 *                           type: number
 *                         totalCharts:
 *                           type: number
 *                         renderedCharts:
 *                           type: number
 *                         totalWordCount:
 *                           type: number
 *                         totalTokensUsed:
 *                           type: number
 *                         completionPercentage:
 *                           type: number
 *                 message:
 *                   type: string
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - insufficient permissions
 *       404:
 *         description: Proposal not found
 *       500:
 *         description: Internal server error
 */
