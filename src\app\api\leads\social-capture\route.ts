import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { socialMediaIntegrationService, socialMediaPostSchema } from '@/services/social-media-integration';
import { hasPermission } from '@/lib/permissions';
import { PermissionResource, PermissionAction } from '@/types';

export async function POST(request: NextRequest) {
  const tenantId = request.headers.get('x-tenant-id');

  try {
    // Social capture API is open for external integrations (webhooks, social media monitoring, etc.)
    if (!tenantId) {
      return NextResponse.json({ error: 'Tenant ID required' }, { status: 400 });
    }

    const body = await request.json();
    console.log('📱 Social capture request body:', body);
    console.log('📱 Tenant ID:', tenantId);

    // Validate request body
    const validationResult = socialMediaPostSchema.safeParse(body);
    if (!validationResult.success) {
      console.error('❌ Social validation failed:', validationResult.error.errors);
      return NextResponse.json(
        {
          error: 'Validation failed',
          details: validationResult.error.errors
        },
        { status: 400 }
      );
    }

    console.log('✅ Social validation passed, processing...');
    const result = await socialMediaIntegrationService.processSocialMediaPost(
      tenantId,
      validationResult.data
    );

    console.log('✅ Social post processed successfully:', {
      captureId: result.capture.id,
      leadCreated: !!result.lead,
      contactCreated: !!result.contact
    });

    return NextResponse.json({
      success: true,
      data: {
        capture: result.capture,
        lead: result.lead,
        contact: result.contact,
        analysis: result.analysis
      }
    }, { status: 201 });
  } catch (error) {
    console.error('❌ Error processing social media capture:', error);

    // Return more detailed error information
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    const errorStack = error instanceof Error ? error.stack : undefined;

    console.error('Error details:', {
      message: errorMessage,
      stack: errorStack,
      tenantId,
      timestamp: new Date().toISOString()
    });

    return NextResponse.json(
      {
        error: 'Failed to process social media post',
        details: errorMessage,
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const tenantId = request.headers.get('x-tenant-id');
    if (!tenantId) {
      return NextResponse.json({ error: 'Tenant ID required' }, { status: 400 });
    }

    // Check permissions
    const canView = await hasPermission(
      PermissionResource.LEADS,
      PermissionAction.READ,
      tenantId
    );

    if (!canView) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '50');
    const offset = parseInt(searchParams.get('offset') || '0');
    const platform = searchParams.get('platform') || undefined;
    const processed = searchParams.get('processed') === 'true' ? true : 
                     searchParams.get('processed') === 'false' ? false : undefined;

    const result = await socialMediaIntegrationService.getSocialCaptures(
      tenantId,
      { limit, offset, platform, processed }
    );

    return NextResponse.json({
      success: true,
      data: result.captures,
      total: result.total,
      hasMore: result.total > offset + limit
    });
  } catch (error) {
    console.error('Error fetching social captures:', error);
    return NextResponse.json(
      { error: 'Failed to fetch social captures' },
      { status: 500 }
    );
  }
}
