'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { PageLayout } from '@/components/layout/page-layout';
import { PermissionGate } from '@/components/ui/permission-gate';
import { HTMLTemplateEditor } from '@/components/ui/html-template-editor';
import { PermissionResource, PermissionAction } from '@/types';
import { toast } from 'sonner';
import {
  ArrowLeft,
  Upload,
  Sparkles,
  Save,
  Eye,
  FileText,
  Settings,
  Loader2
} from 'lucide-react';

export default function CreateTemplatePage() {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState<'basic' | 'html' | 'preview'>('basic');
  const [submitting, setSubmitting] = useState(false);
  const [analyzing, setAnalyzing] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);

  // Form state
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    category: 'proposal',
    templateType: '',
    isDefault: false,
    isActive: true,
    styling: JSON.stringify({
      fontFamily: 'Tajawal',
      primaryColor: '#3A7D5E',
      secondaryColor: '#C7A653'
    }, null, 2),
    branding: JSON.stringify({
      companyName: 'Your Company',
      primaryColor: '#3A7D5E',
      secondaryColor: '#C7A653'
    }, null, 2),
    variables: JSON.stringify([
      'Client_Name',
      'Project_Name',
      'Company_Name',
      'Submission_Date_Hijri',
      'Project_Duration_Months'
    ], null, 2),
    htmlTemplate: ''
  });

  // Helper functions

  // File upload handler
  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setSelectedFile(file);
    }
  };

  // AI Analysis handler
  const handleAIAnalysis = async () => {
    if (!selectedFile) {
      toast.error('Please select a file first');
      return;
    }

    setAnalyzing(true);
    try {
      const formData = new FormData();
      formData.append('file', selectedFile);

      const response = await fetch('/api/document-templates/analyze', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        throw new Error('Analysis failed');
      }

      const result = await response.json();
      
      if (result.success) {
        const { styling, branding, variables, htmlTemplate } = result.data;

        setFormData(prev => ({
          ...prev,
          styling: JSON.stringify(styling, null, 2),
          branding: JSON.stringify(branding, null, 2),
          variables: JSON.stringify(variables, null, 2),
          htmlTemplate: htmlTemplate || ''
        }));

        // Switch to HTML tab if template was generated
        if (htmlTemplate) {
          setActiveTab('html');
        }

        toast.success('Template analyzed successfully!');
      } else {
        throw new Error(result.message || 'Analysis failed');
      }
    } catch (error) {
      console.error('AI analysis error:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to analyze template');
    } finally {
      setAnalyzing(false);
    }
  };

  // Form submission handler
  const handleSubmit = async () => {
    if (!formData.name || !formData.templateType) {
      toast.error('Please fill in all required fields');
      return;
    }

    setSubmitting(true);
    try {
      const submitFormData = new FormData();
      
      // Add basic fields
      submitFormData.append('name', formData.name);
      submitFormData.append('description', formData.description);
      submitFormData.append('category', formData.category);
      submitFormData.append('templateType', formData.templateType);
      submitFormData.append('isDefault', formData.isDefault.toString());
      submitFormData.append('isActive', formData.isActive.toString());
      
      // Add configuration fields
      submitFormData.append('styling', formData.styling);
      submitFormData.append('branding', formData.branding);
      submitFormData.append('variables', formData.variables);
      
      // Add HTML template fields
      if (formData.htmlTemplate) {
        submitFormData.append('htmlTemplate', formData.htmlTemplate);
      }
      
      // Add file if selected
      if (selectedFile) {
        submitFormData.append('file', selectedFile);
      }

      const response = await fetch('/api/document-templates', {
        method: 'POST',
        body: submitFormData,
      });

      if (!response.ok) {
        throw new Error('Failed to create template');
      }

      const result = await response.json();
      
      if (result.success) {
        toast.success('Template created successfully!');
        router.push('/proposals/templates');
      } else {
        throw new Error(result.message || 'Failed to create template');
      }
    } catch (error) {
      console.error('Template creation error:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to create template');
    } finally {
      setSubmitting(false);
    }
  };

  // Page actions
  const actions = (
    <div className="flex items-center space-x-2">
      <Button
        variant="outline"
        onClick={() => router.push('/proposals/templates')}
      >
        <ArrowLeft className="w-4 h-4 mr-2" />
        Back to Templates
      </Button>
      <Button
        onClick={handleSubmit}
        disabled={submitting || !formData.name || !formData.templateType}
      >
        {submitting ? (
          <>
            <Loader2 className="w-4 h-4 mr-2 animate-spin" />
            Creating...
          </>
        ) : (
          <>
            <Save className="w-4 h-4 mr-2" />
            Create Template
          </>
        )}
      </Button>
    </div>
  );

  return (
    <PermissionGate 
      resource={PermissionResource.PROPOSALS} 
      action={PermissionAction.CREATE}
    >
      <PageLayout
        title="Create Document Template"
        description="Create a new document template for proposals and other documents"
        actions={actions}
      >
        <div className="max-w-6xl mx-auto space-y-6">
          {/* File Upload Section */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Upload className="w-5 h-5 mr-2" />
                Upload Template File
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center space-x-4">
                <div className="flex-1">
                  <Input
                    type="file"
                    accept=".docx"
                    onChange={handleFileUpload}
                    className="cursor-pointer"
                  />
                  <p className="text-sm text-gray-500 mt-1">
                    Upload a DOCX file to analyze and extract template structure
                  </p>
                </div>
                <Button
                  onClick={handleAIAnalysis}
                  disabled={!selectedFile || analyzing}
                  variant="outline"
                >
                  {analyzing ? (
                    <>
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      Analyzing...
                    </>
                  ) : (
                    <>
                      <Sparkles className="w-4 h-4 mr-2" />
                      AI Analysis
                    </>
                  )}
                </Button>
              </div>
              {selectedFile && (
                <div className="flex items-center space-x-2">
                  <Badge variant="outline">
                    {selectedFile.name} ({(selectedFile.size / 1024 / 1024).toFixed(2)} MB)
                  </Badge>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Main Form */}
          <Card>
            <CardHeader>
              <CardTitle>Template Configuration</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Tab Navigation */}
              <div className="flex space-x-1 border-b">
                {[
                  { id: 'basic', label: 'Basic Info', icon: Settings },
                  { id: 'html', label: 'HTML Editor', icon: FileText },
                  { id: 'preview', label: 'Preview', icon: Eye }
                ].map((tab) => {
                  const Icon = tab.icon;
                  return (
                    <Button
                      key={tab.id}
                      variant={activeTab === tab.id ? 'default' : 'ghost'}
                      size="sm"
                      onClick={() => setActiveTab(tab.id as any)}
                      className="rounded-b-none"
                    >
                      <Icon className="w-4 h-4 mr-2" />
                      {tab.label}
                    </Button>
                  );
                })}
              </div>

              {/* Tab Content */}
              <div className="min-h-[500px]">
                {/* Basic Info Tab */}
                {activeTab === 'basic' && (
                  <div className="space-y-6">
                    {/* Basic Information */}
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="name">Template Name *</Label>
                        <Input
                          id="name"
                          value={formData.name}
                          onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                          placeholder="Enter template name"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="templateType">Template Type *</Label>
                        <Input
                          id="templateType"
                          value={formData.templateType}
                          onChange={(e) => setFormData(prev => ({ ...prev, templateType: e.target.value }))}
                          placeholder="e.g., Business Proposal, Contract"
                        />
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="description">Description</Label>
                      <Textarea
                        id="description"
                        value={formData.description}
                        onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                        placeholder="Describe the template purpose and usage"
                        rows={3}
                      />
                    </div>

                    <div className="grid grid-cols-3 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="category">Category</Label>
                        <Select
                          value={formData.category}
                          onValueChange={(value) => setFormData(prev => ({ ...prev, category: value }))}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="proposal">Proposal</SelectItem>
                            <SelectItem value="contract">Contract</SelectItem>
                            <SelectItem value="report">Report</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Switch
                          id="isDefault"
                          checked={formData.isDefault}
                          onCheckedChange={(checked) => setFormData(prev => ({ ...prev, isDefault: checked }))}
                        />
                        <Label htmlFor="isDefault">Set as default template</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Switch
                          id="isActive"
                          checked={formData.isActive}
                          onCheckedChange={(checked) => setFormData(prev => ({ ...prev, isActive: checked }))}
                        />
                        <Label htmlFor="isActive">Active template</Label>
                      </div>
                    </div>

                    {/* Configuration */}
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="styling">Styling Configuration (JSON)</Label>
                        <Textarea
                          id="styling"
                          value={formData.styling}
                          onChange={(e) => setFormData(prev => ({ ...prev, styling: e.target.value }))}
                          placeholder="JSON configuration for styling"
                          rows={6}
                          className="font-mono text-sm"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="branding">Branding Configuration (JSON)</Label>
                        <Textarea
                          id="branding"
                          value={formData.branding}
                          onChange={(e) => setFormData(prev => ({ ...prev, branding: e.target.value }))}
                          placeholder="JSON configuration for branding"
                          rows={6}
                          className="font-mono text-sm"
                        />
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="variables">Template Variables (JSON Array)</Label>
                      <Textarea
                        id="variables"
                        value={formData.variables}
                        onChange={(e) => setFormData(prev => ({ ...prev, variables: e.target.value }))}
                        placeholder="Array of available template variables"
                        rows={4}
                        className="font-mono text-sm"
                      />
                    </div>
                  </div>
                )}

                {/* HTML Editor Tab */}
                {activeTab === 'html' && (
                  <HTMLTemplateEditor
                    value={formData.htmlTemplate}
                    onChange={(value) => setFormData(prev => ({ ...prev, htmlTemplate: value }))}
                    placeholder="Enter your HTML template content..."
                    onPlaceholderInsert={(placeholder) => {
                      console.log('Placeholder inserted:', placeholder);
                    }}
                    className="border-0"
                    minHeight="500px"
                  />
                )}

                {/* Preview Tab */}
                {activeTab === 'preview' && (
                  <div className="border rounded-lg p-6 bg-white min-h-[500px]">
                    <div 
                      className="prose prose-sm max-w-none"
                      dangerouslySetInnerHTML={{ 
                        __html: formData.htmlTemplate || '<p class="text-gray-500 italic">No content to preview. Switch to the HTML Editor tab to create your template.</p>' 
                      }}
                    />
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </PageLayout>
    </PermissionGate>
  );
}
