import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { businessCardAnalysisService } from '@/services/business-card-analysis';
import { withPermission, AuthenticatedRequest } from '@/lib/permission-middleware';
import { PermissionResource, PermissionAction } from '@/types';

/**
 * POST /api/contacts/analyze-business-card
 * Analyze a business card image to extract contact information
 */
export const POST = withPermission({
  resource: PermissionResource.CONTACTS,
  action: PermissionAction.CREATE,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest) => {
  try {
    console.log('📥 Business card analysis request received');

    const formData = await req.formData();
    const file = formData.get('file') as File;

    if (!file) {
      console.log('❌ No file provided');
      return NextResponse.json(
        {
          success: false,
          error: 'No file provided'
        },
        { status: 400 }
      );
    }

    console.log(`📄 File received: ${file.name}, size: ${file.size}, type: ${file.type}`);

    // Validate file type
    if (!file.type.startsWith('image/')) {
      console.log('❌ Invalid file type:', file.type);
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid file type. Please upload an image file.'
        },
        { status: 400 }
      );
    }

    // Validate file size (10MB limit)
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (file.size > maxSize) {
      console.log('❌ File too large:', file.size);
      return NextResponse.json(
        {
          success: false,
          error: 'File too large. Maximum size is 10MB.'
        },
        { status: 400 }
      );
    }

    console.log('✅ File validation passed');

    // Check if Google AI is configured
    if (!process.env.GOOGLE_AI_API_KEY) {
      console.log('❌ Google AI API key not configured');
      return NextResponse.json(
        {
          success: false,
          error: 'AI service not configured. Please contact administrator.'
        },
        { status: 500 }
      );
    }

    console.log('🚀 Starting business card analysis...');

    // Perform business card analysis with timeout
    const analysisPromise = businessCardAnalysisService.analyzeBusinessCard({
      file,
      tenantId: req.tenantId,
      userId: req.userId
    });

    // Add timeout to prevent hanging requests
    const timeoutPromise = new Promise<never>((_, reject) => {
      setTimeout(() => reject(new Error('Analysis timeout - please try again')), 60000); // 60 second timeout
    });

    const analysis = await Promise.race([analysisPromise, timeoutPromise]);

    console.log('✅ Business card analysis completed successfully');

    return NextResponse.json({
      success: true,
      data: analysis,
      message: 'Business card analyzed successfully'
    });

  } catch (error) {
    console.error('❌ Business card analysis error:', error);

    const errorMessage = error instanceof Error ? error.message : 'Failed to analyze business card';

    return NextResponse.json(
      {
        success: false,
        error: errorMessage
      },
      { status: 500 }
    );
  }
});
