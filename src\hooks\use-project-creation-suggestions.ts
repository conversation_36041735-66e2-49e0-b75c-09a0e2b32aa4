'use client';

import { useState, useEffect } from 'react';
import { useToast } from '@/hooks/use-toast';

export interface ProjectCreationSuggestion {
  opportunity: {
    id: string;
    title: string;
    value: number;
    stage: string;
    contact: {
      firstName: string;
      lastName: string;
      email: string;
    };
    company: {
      name: string;
    };
  };
  handover?: {
    id: string;
    title: string;
    status: string;
    checklistProgress: number;
    salesRep: {
      firstName: string;
      lastName: string;
    };
    deliveryManager: {
      firstName: string;
      lastName: string;
    };
  };
  suggestedProjectName: string;
  hasHandover: boolean;
  readyForProject: boolean;
}

interface UseProjectCreationSuggestionsOptions {
  limit?: number;
  includeWithHandovers?: boolean;
  autoRefresh?: boolean;
  refreshInterval?: number;
}

interface UseProjectCreationSuggestionsReturn {
  suggestions: ProjectCreationSuggestion[];
  loading: boolean;
  error: string | null;
  refresh: () => Promise<void>;
}

export function useProjectCreationSuggestions(
  options: UseProjectCreationSuggestionsOptions = {}
): UseProjectCreationSuggestionsReturn {
  const {
    limit = 10,
    includeWithHandovers = true,
    autoRefresh = false,
    refreshInterval = 30000, // 30 seconds
  } = options;

  const [suggestions, setSuggestions] = useState<ProjectCreationSuggestion[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToast();

  const fetchSuggestions = async () => {
    try {
      setError(null);
      
      const params = new URLSearchParams({
        limit: limit.toString(),
        includeWithHandovers: includeWithHandovers.toString(),
      });

      const response = await fetch(`/api/projects/creation-suggestions?${params}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch project creation suggestions');
      }

      const data = await response.json();
      setSuggestions(data.suggestions || []);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An unexpected error occurred';
      setError(errorMessage);
      
      toast({
        title: 'Error',
        description: `Failed to load project creation suggestions: ${errorMessage}`,
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const refresh = async () => {
    setLoading(true);
    await fetchSuggestions();
  };

  useEffect(() => {
    fetchSuggestions();
  }, [limit, includeWithHandovers]);

  useEffect(() => {
    if (autoRefresh && refreshInterval > 0) {
      const interval = setInterval(fetchSuggestions, refreshInterval);
      return () => clearInterval(interval);
    }
  }, [autoRefresh, refreshInterval]);

  return {
    suggestions,
    loading,
    error,
    refresh,
  };
}
