'use client';

import { useState, useEffect, useRef } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { format } from 'date-fns';
import { Calendar as CalendarIcon, Clock, Phone, Video, MapPin } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { ScheduledCallWithRelations } from '@/services/scheduled-call-service';
import { cn } from '@/lib/utils';

const scheduleCallSchema = z.object({
  leadId: z.string().optional().transform(val => val === '' ? undefined : val),
  contactId: z.string().optional().transform(val => val === '' ? undefined : val),
  assignedTo: z.string().min(1, 'Assigned user is required'),
  title: z.string().min(1, 'Title is required').max(255),
  description: z.string().optional(),
  callType: z.enum(['phone', 'video', 'in-person']),
  scheduledDate: z.date({
    required_error: 'Scheduled date is required',
  }),
  scheduledTime: z.string().min(1, 'Scheduled time is required'),
  duration: z.number().min(5).max(480),
  timezone: z.string(),
  followUpRequired: z.boolean(),
  nextCallDate: z.date().optional(),
});

type ScheduleCallFormData = z.infer<typeof scheduleCallSchema>;

interface ScheduleCallDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (data: any) => Promise<void>;
  call?: ScheduledCallWithRelations | null;
  leadId?: string;
  contactId?: string;
  currentUserId?: string;
  users?: Array<{
    id: string;
    firstName: string | null;
    lastName: string | null;
    email: string;
  }>;
  contacts?: Array<{
    id: string;
    firstName: string;
    lastName: string;
    email: string | null;
  }>;
  leads?: Array<{
    id: string;
    title: string;
    status: string;
    contact?: {
      firstName: string;
      lastName: string;
    } | null;
  }>;
}

const callTypeOptions = [
  { value: 'phone', label: 'Phone Call', icon: Phone },
  { value: 'video', label: 'Video Call', icon: Video },
  { value: 'in-person', label: 'In-Person Meeting', icon: MapPin },
];

const durationOptions = [
  { value: 15, label: '15 minutes' },
  { value: 30, label: '30 minutes' },
  { value: 45, label: '45 minutes' },
  { value: 60, label: '1 hour' },
  { value: 90, label: '1.5 hours' },
  { value: 120, label: '2 hours' },
];

const timeSlots = Array.from({ length: 48 }, (_, i) => {
  const hour = Math.floor(i / 2);
  const minute = i % 2 === 0 ? '00' : '30';
  const time24 = `${hour.toString().padStart(2, '0')}:${minute}`;
  const hour12 = hour === 0 ? 12 : hour > 12 ? hour - 12 : hour;
  const ampm = hour < 12 ? 'AM' : 'PM';
  const time12 = `${hour12}:${minute} ${ampm}`;
  return { value: time24, label: time12 };
});

export function ScheduleCallDialog({
  open,
  onOpenChange,
  onSubmit,
  call,
  leadId,
  contactId,
  currentUserId,
  users = [],
  contacts = [],
  leads = [],
}: ScheduleCallDialogProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [selectedContact, setSelectedContact] = useState<string | null>(contactId || null);
  const [selectedLead, setSelectedLead] = useState<string | null>(leadId || null);
  const formInitialized = useRef(false);

  const isEditing = !!call;

  const form = useForm<ScheduleCallFormData>({
    resolver: zodResolver(scheduleCallSchema),
    defaultValues: {
      leadId: leadId || call?.leadId || '',
      contactId: contactId || call?.contactId || '',
      assignedTo: call?.assignedTo || currentUserId || '',
      title: call?.title || (leadId ? `Call regarding ${leads.find(l => l.id === leadId)?.title || 'Lead'}` : ''),
      description: call?.description || '',
      callType: call?.callType as 'phone' | 'video' | 'in-person' || 'phone',
      scheduledDate: call ? new Date(call.scheduledAt) : new Date(),
      scheduledTime: call ? format(new Date(call.scheduledAt), 'HH:mm') : '09:00',
      duration: call?.duration || 30,
      timezone: call?.timezone || 'UTC',
      followUpRequired: call?.followUpRequired || false,
      nextCallDate: call?.nextCallDate ? new Date(call.nextCallDate) : undefined,
    },
  });

  useEffect(() => {
    if (leadId) {
      setSelectedLead(leadId);
      form.setValue('leadId', leadId);
      // Auto-generate title if not editing
      if (!call && leads.length > 0) {
        const currentLead = leads.find(l => l.id === leadId);
        if (currentLead) {
          form.setValue('title', `Call regarding ${currentLead.title}`);
        }
      }
    }
    if (contactId) {
      setSelectedContact(contactId);
      form.setValue('contactId', contactId);
    }
    // Set current user as assigned user if available
    if (currentUserId && !call) {
      form.setValue('assignedTo', currentUserId);
    }
  }, [leadId, contactId, currentUserId, call, leads, form]);

  // Reset form when dialog opens
  useEffect(() => {
    if (open && !formInitialized.current) {
      if (!call) {
        // Reset for new call
        setSelectedContact(contactId || null);
        setSelectedLead(leadId || null);

        const currentLead = leads.find(l => l.id === leadId);
        const defaultTitle = currentLead ? `Call regarding ${currentLead.title}` : '';

        form.reset({
          leadId: leadId || '',
          contactId: contactId || '',
          assignedTo: currentUserId || '',
          title: defaultTitle,
          description: '',
          callType: 'phone',
          scheduledDate: new Date(),
          scheduledTime: '09:00',
          duration: 30,
          timezone: 'UTC',
          followUpRequired: false,
          nextCallDate: undefined,
        });
      } else {
        // Reset for editing existing call
        setSelectedContact(call.contactId || null);
        setSelectedLead(call.leadId || null);

        form.reset({
          leadId: call.leadId || '',
          contactId: call.contactId || '',
          assignedTo: call.assignedTo,
          title: call.title,
          description: call.description || '',
          callType: call.callType as 'phone' | 'video' | 'in-person',
          scheduledDate: new Date(call.scheduledAt),
          scheduledTime: format(new Date(call.scheduledAt), 'HH:mm'),
          duration: call.duration,
          timezone: call.timezone,
          followUpRequired: call.followUpRequired,
          nextCallDate: call.nextCallDate ? new Date(call.nextCallDate) : undefined,
        });
      }
      formInitialized.current = true;
    } else if (!open) {
      // Reset the flag when dialog closes
      formInitialized.current = false;
    }
  }, [open, call, leadId, contactId, leads, currentUserId]);

  // Debug logging
  useEffect(() => {
    if (open) {
      console.log('Schedule Call Dialog opened with:', {
        leadId,
        contactId,
        currentUserId,
        leadsCount: leads.length,
        contactsCount: contacts.length,
        usersCount: users.length,
        isEditing: !!call
      });
    }
  }, [open, leadId, contactId, leads.length, contacts.length, users.length, call]);

  // Debug users data
  useEffect(() => {
    if (users.length > 0) {
      console.log('Users loaded for assignment:', users.map(u => ({
        id: u.id,
        name: `${u.firstName} ${u.lastName}`,
        email: u.email
      })));
    }
  }, [users]);

  const handleSubmit = async (data: ScheduleCallFormData) => {
    setIsLoading(true);
    try {
      // Combine date and time
      const scheduledDateTime = new Date(data.scheduledDate);
      const [hours, minutes] = data.scheduledTime.split(':').map(Number);
      scheduledDateTime.setHours(hours, minutes, 0, 0);

      const submitData = {
        ...data,
        leadId: data.leadId || undefined,
        contactId: data.contactId || undefined,
        scheduledAt: scheduledDateTime.toISOString(),
        nextCallDate: data.nextCallDate?.toISOString(),
      };

      await onSubmit(submitData);
      onOpenChange(false);
      // Reset form state
      setSelectedContact(null);
      setSelectedLead(null);
      form.reset();
    } catch (error) {
      console.error('Error submitting call:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleContactChange = (contactId: string) => {
    const actualContactId = contactId === 'none' ? null : contactId;
    setSelectedContact(actualContactId);
    form.setValue('contactId', actualContactId || '');
    // Only clear lead selection if contact is selected and we're not in a lead context
    if (actualContactId && !leadId) {
      setSelectedLead(null);
      form.setValue('leadId', '');
    }
  };

  const handleLeadChange = (leadId: string) => {
    const actualLeadId = leadId === 'none' ? null : leadId;
    setSelectedLead(actualLeadId);
    form.setValue('leadId', actualLeadId || '');
    // Only clear contact selection if lead is selected and we're not in a contact context
    if (actualLeadId && !contactId) {
      setSelectedContact(null);
      form.setValue('contactId', '');
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {isEditing ? 'Edit Scheduled Call' : 'Schedule New Call'}
          </DialogTitle>
          <DialogDescription>
            {isEditing 
              ? 'Update the details of your scheduled call.'
              : 'Schedule a new call with a lead or contact.'
            }
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
            {/* Contact/Lead Selection */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="contactId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      Contact
                      {!!contactId && !isEditing && (
                        <span className="text-xs text-gray-500 ml-2">(from lead)</span>
                      )}
                    </FormLabel>
                    <Select
                      value={selectedContact || 'none'}
                      onValueChange={handleContactChange}
                      disabled={!!contactId && !isEditing}
                    >
                      <FormControl>
                        <SelectTrigger className={!!contactId && !isEditing ? 'opacity-60' : ''}>
                          <SelectValue placeholder="Select contact" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="none">No contact</SelectItem>
                        {contacts.map((contact) => (
                          <SelectItem key={contact.id} value={contact.id}>
                            {contact.firstName} {contact.lastName}
                            {contact.email && (
                              <span className="text-gray-500 ml-2">
                                ({contact.email})
                              </span>
                            )}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="leadId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      Lead
                      {!!leadId && !isEditing && (
                        <span className="text-xs text-gray-500 ml-2">(current lead)</span>
                      )}
                    </FormLabel>
                    <Select
                      value={selectedLead || 'none'}
                      onValueChange={handleLeadChange}
                      disabled={!!leadId && !isEditing}
                    >
                      <FormControl>
                        <SelectTrigger className={!!leadId && !isEditing ? 'opacity-60' : ''}>
                          <SelectValue placeholder="Select lead" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="none">No lead</SelectItem>
                        {leads.map((lead) => (
                          <SelectItem key={lead.id} value={lead.id}>
                            {lead.title}
                            {lead.contact && (
                              <span className="text-gray-500 ml-2">
                                ({lead.contact.firstName} {lead.contact.lastName})
                              </span>
                            )}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Basic Information */}
            <FormField
              control={form.control}
              name="title"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Call Title</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter call title" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Enter call description or agenda"
                      className="resize-none"
                      rows={3}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Call Type */}
            <FormField
              control={form.control}
              name="callType"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Call Type</FormLabel>
                  <FormControl>
                    <div className="grid grid-cols-3 gap-3">
                      {callTypeOptions.map((option) => {
                        const Icon = option.icon;
                        return (
                          <button
                            key={option.value}
                            type="button"
                            onClick={() => field.onChange(option.value)}
                            className={cn(
                              'flex flex-col items-center justify-center p-3 border rounded-lg transition-colors',
                              field.value === option.value
                                ? 'border-primary bg-primary/5 text-primary'
                                : 'border-gray-200 hover:border-gray-300'
                            )}
                          >
                            <Icon className="h-5 w-5 mb-1" />
                            <span className="text-sm font-medium">{option.label}</span>
                          </button>
                        );
                      })}
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Scheduling */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <FormField
                control={form.control}
                name="scheduledDate"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>Date</FormLabel>
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant="outline"
                            className={cn(
                              'w-full pl-3 text-left font-normal',
                              !field.value && 'text-muted-foreground'
                            )}
                          >
                            {field.value ? (
                              format(field.value, 'PPP')
                            ) : (
                              <span>Pick a date</span>
                            )}
                            <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent
                        className="w-auto p-0 z-[1000]"
                        align="start"
                        side="bottom"
                        sideOffset={4}
                        avoidCollisions={true}
                        collisionPadding={20}
                      >
                        <Calendar
                          mode="single"
                          selected={field.value}
                          onSelect={field.onChange}
                          disabled={(date) =>
                            date < new Date(new Date().setHours(0, 0, 0, 0))
                          }
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="scheduledTime"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Time</FormLabel>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select time" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent className="max-h-60">
                        {timeSlots.map((slot) => (
                          <SelectItem key={slot.value} value={slot.value}>
                            {slot.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="duration"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Duration</FormLabel>
                    <Select
                      onValueChange={(value) => field.onChange(Number(value))}
                      value={field.value?.toString()}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Duration" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {durationOptions.map((option) => (
                          <SelectItem key={option.value} value={option.value.toString()}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Assigned To */}
            <FormField
              control={form.control}
              name="assignedTo"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Assigned To</FormLabel>
                  <Select onValueChange={field.onChange} value={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select user" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {users.map((user) => (
                        <SelectItem key={user.id} value={user.id}>
                          {user.firstName} {user.lastName}
                          <span className="text-gray-500 ml-2">({user.email})</span>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Follow-up */}
            <FormField
              control={form.control}
              name="followUpRequired"
              render={({ field }) => (
                <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                  <FormControl>
                    <Checkbox
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                  <div className="space-y-1 leading-none">
                    <FormLabel>Follow-up required</FormLabel>
                    <FormDescription>
                      Mark this call as requiring a follow-up action
                    </FormDescription>
                  </div>
                </FormItem>
              )}
            />

            {form.watch('followUpRequired') && (
              <FormField
                control={form.control}
                name="nextCallDate"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>Next Call Date</FormLabel>
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant="outline"
                            className={cn(
                              'w-full pl-3 text-left font-normal',
                              !field.value && 'text-muted-foreground'
                            )}
                          >
                            {field.value ? (
                              format(field.value, 'PPP')
                            ) : (
                              <span>Pick a date for next call</span>
                            )}
                            <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent
                        className="w-auto p-0 z-[1000]"
                        align="start"
                        side="bottom"
                        sideOffset={4}
                        avoidCollisions={true}
                        collisionPadding={20}
                      >
                        <Calendar
                          mode="single"
                          selected={field.value}
                          onSelect={field.onChange}
                          disabled={(date) =>
                            date < new Date(new Date().setHours(0, 0, 0, 0))
                          }
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
                disabled={isLoading}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isLoading}>
                {isLoading ? 'Saving...' : isEditing ? 'Update Call' : 'Schedule Call'}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
