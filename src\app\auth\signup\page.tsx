'use client';

import { AuthLayout } from '@/components/auth/auth-layout';
import { RegisterForm } from '@/components/auth/register-form';
import { useRedirectIfAuthenticated } from '@/hooks/use-auth';

export default function SignUpPage() {
  // Redirect if already authenticated
  useRedirectIfAuthenticated();

  return (
    <AuthLayout
      title="Create your account"
      subtitle="Enter your details below to create your account"
    >
      <RegisterForm />
    </AuthLayout>
  );
}


