import { z } from 'zod';
import { prisma } from '@/lib/prisma';
import { aiService } from './ai-service';
import { leadManagementService } from './lead-management';
import { leadNotificationService } from './lead-notifications';

// Validation schemas
export const socialMediaPostSchema = z.object({
  platform: z.enum(['twitter', 'linkedin', 'facebook', 'instagram', 'tiktok', 'youtube']),
  postId: z.string(),
  authorId: z.string().optional(),
  authorName: z.string(),
  authorHandle: z.string().optional(),
  content: z.string(),
  url: z.string().url().optional(),
  createdAt: z.union([
    z.date(),
    z.string().datetime('Invalid date format')
  ]).transform((val) => typeof val === 'string' ? new Date(val) : val).default(() => new Date()),
  engagement: z.object({
    likes: z.number().default(0),
    shares: z.number().default(0),
    comments: z.number().default(0),
    views: z.number().default(0)
  }).optional(),
  metadata: z.record(z.any()).optional()
});

export const socialMediaAnalysisSchema = z.object({
  sentiment: z.enum(['positive', 'negative', 'neutral']),
  confidence: z.number().min(0).max(1),
  intent: z.enum(['inquiry', 'complaint', 'praise', 'question', 'mention', 'other']),
  urgency: z.enum(['low', 'medium', 'high']),
  leadPotential: z.enum(['low', 'medium', 'high']),
  topics: z.array(z.string()),
  mentions: z.array(z.string()).optional(),
  hashtags: z.array(z.string()).optional(),
  keyPhrases: z.array(z.string()).optional(),
  businessRelevance: z.number().min(0).max(1)
});

export type SocialMediaPost = z.infer<typeof socialMediaPostSchema>;
export type SocialMediaAnalysis = z.infer<typeof socialMediaAnalysisSchema>;

export interface SocialMediaCapture {
  id: string;
  tenantId: string;
  platform: string;
  postId: string;
  authorId: string;
  authorName: string;
  authorHandle?: string;
  content: string;
  url?: string;
  createdAt: Date;
  processed: boolean;
  leadId?: string;
  contactId?: string;
  analysis?: SocialMediaAnalysis;
  engagement?: Record<string, number>;
  metadata?: Record<string, any>;
  processingError?: string;
  capturedAt: Date;
}

export interface ExtractedSocialLeadData {
  firstName?: string;
  lastName?: string;
  email?: string;
  phone?: string;
  company?: string;
  title: string;
  description: string;
  source: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  socialProfiles?: Record<string, string>;
}

export class SocialMediaIntegrationService {
  /**
   * Process social media post and extract lead information
   */
  async processSocialMediaPost(
    tenantId: string,
    postData: SocialMediaPost
  ): Promise<{
    capture: SocialMediaCapture;
    lead?: any;
    contact?: any;
    analysis: SocialMediaAnalysis;
  }> {
    try {
      // 1. Analyze content with AI
      const analysis = await this.analyzeSocialContent(
        postData.content,
        postData.platform
      );

      // 2. Check if this is relevant for lead generation
      if (analysis.businessRelevance < 0.3 || analysis.leadPotential === 'low') {
        // Store but don't create lead
        const capture = await this.createSocialCapture(tenantId, postData, analysis, false);
        return { capture, analysis };
      }

      // 3. Extract lead data
      const extractedData = await this.extractSocialLeadData(postData, analysis);

      // 4. Create social media capture record
      const capture = await this.createSocialCapture(tenantId, postData, analysis, true);

      // 5. Check if contact already exists
      let contact = await this.findOrCreateContact(tenantId, postData, extractedData);

      // 6. Create lead if relevant
      let lead = null;
      if (analysis.leadPotential !== 'low' && extractedData.title) {
        lead = await this.createLeadFromSocial(tenantId, extractedData, contact, capture);

        // Update capture with lead ID
        await prisma.socialMediaCapture.update({
          where: { id: capture.id },
          data: {
            leadId: lead.id,
            contactId: contact?.id,
            processed: true
          }
        });

        // Send notification
        if (lead) {
          await leadNotificationService.triggerLeadNotification(
            'new_lead',
            lead,
            {
              source: `social_${postData.platform}`,
              sentiment: analysis.sentiment,
              leadPotential: analysis.leadPotential
            }
          );
        }
      }

      return { capture, lead, contact, analysis };
    } catch (error) {
      // Create error record
      const capture = await prisma.socialMediaCapture.create({
        data: {
          tenantId,
          platform: postData.platform,
          postId: postData.postId,
          authorId: postData.authorId,
          authorName: postData.authorName,
          authorHandle: postData.authorHandle,
          content: postData.content,
          url: postData.url,
          createdAt: postData.createdAt,
          processed: false,
          engagement: postData.engagement,
          metadata: postData.metadata,
          processingError: error instanceof Error ? error.message : 'Unknown error',
          capturedAt: new Date()
        }
      });

      throw error;
    }
  }

  /**
   * Analyze social media content with AI
   */
  private async analyzeSocialContent(
    content: string,
    platform: string
  ): Promise<SocialMediaAnalysis> {
    try {
      const prompt = `
        Analyze this ${platform} post for business relevance and lead potential:
        
        Content: ${content}
        
        Provide analysis in this JSON format:
        {
          "sentiment": "positive|negative|neutral",
          "confidence": 0.0-1.0,
          "intent": "inquiry|complaint|praise|question|mention|other",
          "urgency": "low|medium|high",
          "leadPotential": "low|medium|high",
          "topics": ["topic1", "topic2"],
          "mentions": ["@mention1", "@mention2"],
          "hashtags": ["#hashtag1", "#hashtag2"],
          "keyPhrases": ["phrase1", "phrase2"],
          "businessRelevance": 0.0-1.0
        }
        
        Consider:
        - Business inquiries, service requests, product mentions
        - Complaints or support needs
        - Potential customer interest signals
        - Brand mentions and engagement
      `;

      const response = await aiService.generateResponse({
        prompt,
        systemPrompt: 'You are an expert at analyzing social media content for business opportunities. Respond only with valid JSON.',
        context: { content, platform },
        tenantId: 'system',
        userId: 'system',
        feature: 'social_analysis'
      });

      const analysis = JSON.parse(response.content);
      return socialMediaAnalysisSchema.parse(analysis);
    } catch (error) {
      // Return default analysis
      return {
        sentiment: 'neutral',
        confidence: 0.5,
        intent: 'other',
        urgency: 'low',
        leadPotential: 'low',
        topics: [],
        businessRelevance: 0.1
      };
    }
  }

  /**
   * Extract lead data from social media post
   */
  private async extractSocialLeadData(
    postData: SocialMediaPost,
    analysis: SocialMediaAnalysis
  ): Promise<ExtractedSocialLeadData> {
    try {
      const prompt = `
        Extract potential lead information from this social media post:
        
        Platform: ${postData.platform}
        Author: ${postData.authorName} (${postData.authorHandle || 'N/A'})
        Content: ${postData.content}
        Analysis: ${JSON.stringify(analysis)}
        
        Extract information in JSON format:
        {
          "firstName": "string|null",
          "lastName": "string|null", 
          "email": "string|null",
          "phone": "string|null",
          "company": "string|null",
          "title": "string (lead title/summary)",
          "description": "string (detailed description)",
          "source": "social_${postData.platform}",
          "priority": "low|medium|high|urgent",
          "socialProfiles": {
            "${postData.platform}": "profile_url_or_handle"
          }
        }
        
        Guidelines:
        - Extract names from author name or content
        - Create meaningful lead title based on intent
        - Set priority based on urgency and lead potential
        - Include social profile information
      `;

      const response = await aiService.generateResponse({
        prompt,
        systemPrompt: 'You are an expert at extracting lead data from social media. Respond only with valid JSON.',
        context: { postData, analysis },
        tenantId: 'system',
        userId: 'system',
        feature: 'social_lead_extraction'
      });

      const extractedData = JSON.parse(response.content);
      
      return {
        firstName: extractedData.firstName,
        lastName: extractedData.lastName,
        email: extractedData.email,
        phone: extractedData.phone,
        company: extractedData.company,
        title: extractedData.title || `${postData.platform} inquiry from ${postData.authorName}`,
        description: extractedData.description || postData.content,
        source: `social_${postData.platform}`,
        priority: this.determinePriority(analysis),
        socialProfiles: {
          [postData.platform]: postData.authorHandle || postData.authorId,
          ...extractedData.socialProfiles
        }
      };
    } catch (error) {
      return {
        title: `${postData.platform} mention by ${postData.authorName}`,
        description: postData.content,
        source: `social_${postData.platform}`,
        priority: this.determinePriority(analysis),
        socialProfiles: {
          [postData.platform]: postData.authorHandle || postData.authorId
        }
      };
    }
  }

  /**
   * Create social media capture record
   */
  private async createSocialCapture(
    tenantId: string,
    postData: SocialMediaPost,
    analysis: SocialMediaAnalysis,
    processed: boolean
  ): Promise<SocialMediaCapture> {
    const capture = await prisma.socialMediaCapture.create({
      data: {
        tenantId,
        platform: postData.platform,
        postId: postData.postId,
        authorId: postData.authorId,
        authorName: postData.authorName,
        authorHandle: postData.authorHandle,
        content: postData.content,
        url: postData.url,
        createdAt: postData.createdAt,
        processed,
        analysis,
        engagement: postData.engagement,
        metadata: postData.metadata,
        capturedAt: new Date()
      }
    });

    return capture as SocialMediaCapture;
  }

  /**
   * Find or create contact from social media data
   */
  private async findOrCreateContact(
    tenantId: string,
    postData: SocialMediaPost,
    extractedData: ExtractedSocialLeadData
  ) {
    // Try to find existing contact by email or social profile
    let contact = null;
    
    if (extractedData.email) {
      contact = await prisma.contact.findFirst({
        where: {
          tenantId,
          email: extractedData.email
        }
      });
    }

    // If no contact found and we have enough data, create one
    if (!contact && (extractedData.firstName || extractedData.email)) {
      contact = await prisma.contact.create({
        data: {
          tenantId,
          firstName: extractedData.firstName || postData.authorName.split(' ')[0] || 'Unknown',
          lastName: extractedData.lastName || postData.authorName.split(' ').slice(1).join(' ') || '',
          email: extractedData.email,
          phone: extractedData.phone,
          createdById: null // System created
        }
      });
    }

    return contact;
  }

  /**
   * Create lead from social media data
   */
  private async createLeadFromSocial(
    tenantId: string,
    extractedData: ExtractedSocialLeadData,
    contact: any,
    capture: SocialMediaCapture
  ) {
    // Get or create social media lead source
    let socialLeadSource = await prisma.leadSource.findFirst({
      where: {
        tenantId,
        name: 'Social Media'
      }
    });

    if (!socialLeadSource) {
      socialLeadSource = await prisma.leadSource.create({
        data: {
          tenantId,
          name: 'Social Media',
          description: 'Leads captured from social media platforms',
          category: 'digital',
          icon: 'chat-bubble-left-right',
          color: '#8B5CF6'
        }
      });
    }

    return await leadManagementService.createLead(
      tenantId,
      {
        title: extractedData.title,
        description: extractedData.description,
        contactId: contact?.id,
        leadSourceId: socialLeadSource.id,
        priority: extractedData.priority,
        customFields: {
          socialProfiles: extractedData.socialProfiles,
          originalPost: {
            platform: capture.platform,
            postId: capture.postId,
            url: capture.url,
            engagement: capture.engagement
          }
        }
      },
      null
    );
  }

  /**
   * Determine lead priority based on analysis
   */
  private determinePriority(analysis: SocialMediaAnalysis): 'low' | 'medium' | 'high' | 'urgent' {
    if (analysis.urgency === 'high' || analysis.intent === 'complaint') return 'urgent';
    if (analysis.leadPotential === 'high') return 'high';
    if (analysis.leadPotential === 'medium') return 'medium';
    return 'low';
  }

  /**
   * Get social media captures for a tenant
   */
  async getSocialCaptures(
    tenantId: string,
    options: {
      limit?: number;
      offset?: number;
      platform?: string;
      processed?: boolean;
    } = {}
  ): Promise<{ captures: SocialMediaCapture[]; total: number }> {
    const { limit = 50, offset = 0, platform, processed } = options;

    const where = {
      tenantId,
      ...(platform && { platform }),
      ...(processed !== undefined && { processed })
    };

    const [captures, total] = await Promise.all([
      prisma.socialMediaCapture.findMany({
        where,
        include: {
          lead: {
            include: {
              contact: true,
              company: true
            }
          },
          contact: true
        },
        orderBy: { createdAt: 'desc' },
        take: limit,
        skip: offset
      }),
      prisma.socialMediaCapture.count({ where })
    ]);

    return {
      captures: captures as SocialMediaCapture[],
      total
    };
  }

  /**
   * Setup social media monitoring for keywords/hashtags
   */
  async setupSocialMonitoring(
    tenantId: string,
    config: {
      keywords: string[];
      hashtags: string[];
      mentions: string[];
      platforms: string[];
      enabled: boolean;
    }
  ): Promise<void> {
    await prisma.tenantSetting.upsert({
      where: {
        tenantId_settingKey: {
          tenantId,
          settingKey: 'social_monitoring'
        }
      },
      create: {
        tenantId,
        settingKey: 'social_monitoring',
        settingValue: config,
        description: 'Social media monitoring configuration'
      },
      update: {
        settingValue: config
      }
    });
  }
}

export const socialMediaIntegrationService = new SocialMediaIntegrationService();
