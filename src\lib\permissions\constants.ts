import { PermissionResource, PermissionAction } from '@/types';

// Permission constants to avoid typos and maintain consistency
export const PERMISSIONS = {
  // Contact permissions
  CONTACTS: {
    CREATE: { resource: PermissionResource.CONTACTS, action: PermissionAction.CREATE },
    READ: { resource: PermissionResource.CONTACTS, action: PermissionAction.READ },
    UPDATE: { resource: PermissionResource.CONTACTS, action: PermissionAction.UPDATE },
    DELETE: { resource: PermissionResource.CONTACTS, action: PermissionAction.DELETE },
    IMPORT: { resource: PermissionResource.CONTACTS, action: PermissionAction.IMPORT },
    EXPORT: { resource: PermissionResource.CONTACTS, action: PermissionAction.EXPORT },
  },

  // Company permissions
  COMPANIES: {
    CREATE: { resource: PermissionResource.COMPANIES, action: PermissionAction.CREATE },
    READ: { resource: PermissionResource.COMPANIES, action: PermissionAction.READ },
    UPDATE: { resource: PermissionResource.COMPANIES, action: PermissionAction.UPDATE },
    DELETE: { resource: PermissionResource.COMPANIES, action: PermissionAction.DELETE },
    IMPORT: { resource: PermissionResource.COMPANIES, action: PermissionAction.IMPORT },
    EXPORT: { resource: PermissionResource.COMPANIES, action: PermissionAction.EXPORT },
  },

  // Lead permissions
  LEADS: {
    CREATE: { resource: PermissionResource.LEADS, action: PermissionAction.CREATE },
    READ: { resource: PermissionResource.LEADS, action: PermissionAction.READ },
    UPDATE: { resource: PermissionResource.LEADS, action: PermissionAction.UPDATE },
    DELETE: { resource: PermissionResource.LEADS, action: PermissionAction.DELETE },
    IMPORT: { resource: PermissionResource.LEADS, action: PermissionAction.IMPORT },
    EXPORT: { resource: PermissionResource.LEADS, action: PermissionAction.EXPORT },
    ASSIGN: { resource: PermissionResource.LEADS, action: PermissionAction.ASSIGN },
  },

  // User permissions
  USERS: {
    CREATE: { resource: PermissionResource.USERS, action: PermissionAction.CREATE },
    READ: { resource: PermissionResource.USERS, action: PermissionAction.READ },
    UPDATE: { resource: PermissionResource.USERS, action: PermissionAction.UPDATE },
    DELETE: { resource: PermissionResource.USERS, action: PermissionAction.DELETE },
    MANAGE_ROLES: { resource: PermissionResource.USERS, action: PermissionAction.MANAGE_ROLES },
  },

  // Role permissions
  ROLES: {
    CREATE: { resource: PermissionResource.ROLES, action: PermissionAction.CREATE },
    READ: { resource: PermissionResource.ROLES, action: PermissionAction.READ },
    UPDATE: { resource: PermissionResource.ROLES, action: PermissionAction.UPDATE },
    DELETE: { resource: PermissionResource.ROLES, action: PermissionAction.DELETE },
  },

  // Permission permissions
  PERMISSIONS: {
    CREATE: { resource: PermissionResource.PERMISSIONS, action: PermissionAction.CREATE },
    READ: { resource: PermissionResource.PERMISSIONS, action: PermissionAction.READ },
    UPDATE: { resource: PermissionResource.PERMISSIONS, action: PermissionAction.UPDATE },
    DELETE: { resource: PermissionResource.PERMISSIONS, action: PermissionAction.DELETE },
  },

  // Tenant permissions
  TENANTS: {
    CREATE: { resource: PermissionResource.TENANTS, action: PermissionAction.CREATE },
    READ: { resource: PermissionResource.TENANTS, action: PermissionAction.READ },
    UPDATE: { resource: PermissionResource.TENANTS, action: PermissionAction.UPDATE },
    DELETE: { resource: PermissionResource.TENANTS, action: PermissionAction.DELETE },
  },

  // Report permissions
  REPORTS: {
    CREATE: { resource: PermissionResource.REPORTS, action: PermissionAction.CREATE },
    READ: { resource: PermissionResource.REPORTS, action: PermissionAction.READ },
    UPDATE: { resource: PermissionResource.REPORTS, action: PermissionAction.UPDATE },
    DELETE: { resource: PermissionResource.REPORTS, action: PermissionAction.DELETE },
    EXPORT: { resource: PermissionResource.REPORTS, action: PermissionAction.EXPORT },
  },

  // Integration permissions
  INTEGRATIONS: {
    CREATE: { resource: PermissionResource.INTEGRATIONS, action: PermissionAction.CREATE },
    READ: { resource: PermissionResource.INTEGRATIONS, action: PermissionAction.READ },
    UPDATE: { resource: PermissionResource.INTEGRATIONS, action: PermissionAction.UPDATE },
    DELETE: { resource: PermissionResource.INTEGRATIONS, action: PermissionAction.DELETE },
  },

  // Settings permissions
  SETTINGS: {
    READ: { resource: PermissionResource.SETTINGS, action: PermissionAction.READ },
    UPDATE: { resource: PermissionResource.SETTINGS, action: PermissionAction.UPDATE },
  },
} as const;

// Helper function to get permission key
export function getPermissionKey(resource: PermissionResource, action: PermissionAction): string {
  return `${resource}:${action}`;
}

// Helper function to check if a permission exists
export function isValidPermission(resource: string, action: string): boolean {
  return Object.values(PermissionResource).includes(resource as PermissionResource) &&
         Object.values(PermissionAction).includes(action as PermissionAction);
}
