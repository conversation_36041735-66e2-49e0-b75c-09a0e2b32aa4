import { prisma } from '@/lib/prisma';
import { z } from 'zod';

// Validation schemas
export const createQAThreadSchema = z.object({
  title: z.string().min(1, 'Title is required').max(255),
  category: z.enum(['general', 'technical', 'requirements', 'timeline', 'budget']).default('general'),
  priority: z.enum(['low', 'medium', 'high', 'urgent']).default('medium'),
  assignedToId: z.string().optional().nullable().transform(val => val === '' ? null : val),
});

export const updateQAThreadSchema = z.object({
  title: z.string().min(1, 'Title is required').max(255).optional(),
  category: z.enum(['general', 'technical', 'requirements', 'timeline', 'budget']).optional(),
  priority: z.enum(['low', 'medium', 'high', 'urgent']).optional(),
  status: z.enum(['open', 'answered', 'closed', 'escalated']).optional(),
  assignedToId: z.string().optional().nullable().transform(val => val === '' ? null : val),
});

export const createQAMessageSchema = z.object({
  content: z.string().min(1, 'Content is required'),
  messageType: z.enum(['message', 'answer', 'clarification', 'escalation']).default('message'),
  attachments: z.array(z.string()).default([]),
});

// Types
export type CreateQAThreadData = z.infer<typeof createQAThreadSchema>;
export type UpdateQAThreadData = z.infer<typeof updateQAThreadSchema>;
export type CreateQAMessageData = z.infer<typeof createQAMessageSchema>;

export interface QAThreadWithDetails {
  id: string;
  tenantId: string;
  handoverId: string;
  title: string;
  category: string;
  priority: string;
  status: string;
  createdById: string;
  assignedToId?: string;
  responseTime?: number;
  isEscalated: boolean;
  escalatedAt?: Date;
  resolvedAt?: Date;
  createdAt: Date;
  updatedAt: Date;
  
  // Relations
  createdBy: {
    id: string;
    firstName?: string;
    lastName?: string;
    email: string;
  };
  assignedTo?: {
    id: string;
    firstName?: string;
    lastName?: string;
    email: string;
  };
  messages: Array<{
    id: string;
    content: string;
    messageType: string;
    attachments: string[];
    isRead: boolean;
    readAt?: Date;
    authorId: string;
    createdAt: Date;
    updatedAt: Date;
    author: {
      id: string;
      firstName?: string;
      lastName?: string;
      email: string;
    };
  }>;
  _count: {
    messages: number;
  };
}

export interface QAMessageWithDetails {
  id: string;
  tenantId: string;
  threadId: string;
  content: string;
  messageType: string;
  attachments: string[];
  isRead: boolean;
  readAt?: Date;
  authorId: string;
  createdAt: Date;
  updatedAt: Date;
  author: {
    id: string;
    firstName?: string;
    lastName?: string;
    email: string;
  };
}

export class HandoverQAService {
  private includeThreadOptions = {
    createdBy: {
      select: {
        id: true,
        firstName: true,
        lastName: true,
        email: true,
      }
    },
    assignedTo: {
      select: {
        id: true,
        firstName: true,
        lastName: true,
        email: true,
      }
    },
    messages: {
      include: {
        author: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          }
        }
      },
      orderBy: {
        createdAt: 'asc'
      }
    },
    _count: {
      select: {
        messages: true
      }
    }
  };

  private includeMessageOptions = {
    author: {
      select: {
        id: true,
        firstName: true,
        lastName: true,
        email: true,
      }
    }
  };

  /**
   * Create a new Q&A thread for a handover
   */
  async createQAThread(
    tenantId: string,
    handoverId: string,
    threadData: CreateQAThreadData,
    createdBy: string
  ): Promise<QAThreadWithDetails> {
    const validatedData = createQAThreadSchema.parse(threadData);

    return await prisma.$transaction(async (tx) => {
      // Verify handover exists and belongs to tenant
      const handover = await tx.projectHandover.findUnique({
        where: {
          id: handoverId,
          tenantId
        }
      });

      if (!handover) {
        throw new Error('Handover not found or access denied');
      }

      // Verify createdBy user exists and belongs to tenant
      const createdByUser = await tx.user.findFirst({
        where: {
          id: createdBy,
          tenantUsers: {
            some: { tenantId }
          }
        }
      });

      if (!createdByUser) {
        throw new Error('Creating user not found or not part of tenant');
      }

      // Verify assigned user belongs to tenant if provided
      let finalAssignedToId: string | null = null;
      if (validatedData.assignedToId && validatedData.assignedToId.trim() !== '') {
        const assignedUser = await tx.user.findFirst({
          where: {
            id: validatedData.assignedToId,
            tenantUsers: {
              some: { tenantId }
            }
          }
        });

        if (!assignedUser) {
          throw new Error('Assigned user not found or not part of tenant');
        }
        finalAssignedToId = validatedData.assignedToId;
      }

      // Create the Q&A thread
      const thread = await tx.handoverQAThread.create({
        data: {
          tenantId,
          handoverId,
          title: validatedData.title,
          category: validatedData.category,
          priority: validatedData.priority,
          assignedToId: finalAssignedToId,
          createdById: createdBy,
        },
        include: this.includeThreadOptions,
      });

      // Update handover Q&A threads count
      await tx.projectHandover.update({
        where: {
          id: handoverId,
          tenantId
        },
        data: {
          qaThreadsCount: {
            increment: 1
          }
        }
      });

      return thread as QAThreadWithDetails;
    });
  }

  /**
   * Get Q&A threads for a handover
   */
  async getQAThreads(
    tenantId: string,
    handoverId: string,
    options: {
      status?: string;
      priority?: string;
      category?: string;
      assignedToId?: string;
      includeMessages?: boolean;
    } = {}
  ): Promise<QAThreadWithDetails[]> {
    const {
      status,
      priority,
      category,
      assignedToId,
      includeMessages = true
    } = options;

    const where: any = {
      tenantId,
      handoverId,
      ...(status && { status }),
      ...(priority && { priority }),
      ...(category && { category }),
      ...(assignedToId && { assignedToId }),
    };

    const includeOptions = includeMessages ? this.includeThreadOptions : {
      createdBy: this.includeThreadOptions.createdBy,
      assignedTo: this.includeThreadOptions.assignedTo,
      _count: this.includeThreadOptions._count
    };

    const threads = await prisma.handoverQAThread.findMany({
      where,
      include: includeOptions,
      orderBy: [
        { priority: 'desc' },
        { createdAt: 'desc' }
      ]
    });

    return threads as QAThreadWithDetails[];
  }

  /**
   * Get a single Q&A thread by ID
   */
  async getQAThreadById(
    tenantId: string,
    threadId: string
  ): Promise<QAThreadWithDetails | null> {
    const thread = await prisma.handoverQAThread.findUnique({
      where: {
        id: threadId,
        tenantId
      },
      include: this.includeThreadOptions,
    });

    return thread as QAThreadWithDetails | null;
  }

  /**
   * Update a Q&A thread
   */
  async updateQAThread(
    tenantId: string,
    threadId: string,
    updateData: UpdateQAThreadData
  ): Promise<QAThreadWithDetails> {
    const validatedData = updateQAThreadSchema.parse(updateData);

    return await prisma.$transaction(async (tx) => {
      // Verify thread exists and belongs to tenant
      const existingThread = await tx.handoverQAThread.findUnique({
        where: {
          id: threadId,
          tenantId
        }
      });

      if (!existingThread) {
        throw new Error('Q&A thread not found or access denied');
      }

      // Verify assigned user belongs to tenant if provided
      if (validatedData.assignedToId && validatedData.assignedToId.trim() !== '') {
        const assignedUser = await tx.user.findFirst({
          where: {
            id: validatedData.assignedToId,
            tenantUsers: {
              some: { tenantId }
            }
          }
        });

        if (!assignedUser) {
          throw new Error('Assigned user not found or not part of tenant');
        }
      }

      // Handle status changes
      const updateFields: any = { ...validatedData };
      
      if (validatedData.status === 'escalated' && !existingThread.isEscalated) {
        updateFields.isEscalated = true;
        updateFields.escalatedAt = new Date();
      }
      
      if (validatedData.status === 'closed' && !existingThread.resolvedAt) {
        updateFields.resolvedAt = new Date();
      }

      // Update the thread
      const thread = await tx.handoverQAThread.update({
        where: {
          id: threadId,
          tenantId
        },
        data: updateFields,
        include: this.includeThreadOptions,
      });

      return thread as QAThreadWithDetails;
    });
  }

  /**
   * Add a message to a Q&A thread
   */
  async addMessageToThread(
    tenantId: string,
    threadId: string,
    messageData: CreateQAMessageData,
    authorId: string
  ): Promise<QAMessageWithDetails> {
    const validatedData = createQAMessageSchema.parse(messageData);

    return await prisma.$transaction(async (tx) => {
      // Verify thread exists and belongs to tenant
      const thread = await tx.handoverQAThread.findUnique({
        where: {
          id: threadId,
          tenantId
        }
      });

      if (!thread) {
        throw new Error('Q&A thread not found or access denied');
      }

      // Create the message
      const message = await tx.handoverQAMessage.create({
        data: {
          tenantId,
          threadId,
          content: validatedData.content,
          messageType: validatedData.messageType,
          attachments: validatedData.attachments,
          authorId,
        },
        include: this.includeMessageOptions,
      });

      // Calculate response time if this is a response to the original question
      if (validatedData.messageType === 'answer' && !thread.responseTime) {
        const responseTimeMs = new Date().getTime() - thread.createdAt.getTime();
        const responseTimeHours = Math.round(responseTimeMs / (1000 * 60 * 60));
        
        await tx.handoverQAThread.update({
          where: {
            id: threadId,
            tenantId
          },
          data: {
            responseTime: responseTimeHours,
            status: 'answered'
          }
        });
      }

      return message as QAMessageWithDetails;
    });
  }

  /**
   * Mark messages as read
   */
  async markMessagesAsRead(
    tenantId: string,
    threadId: string,
    userId: string,
    messageIds?: string[]
  ): Promise<void> {
    const where: any = {
      tenantId,
      threadId,
      isRead: false,
      authorId: { not: userId }, // Don't mark own messages as read
    };

    if (messageIds && messageIds.length > 0) {
      where.id = { in: messageIds };
    }

    await prisma.handoverQAMessage.updateMany({
      where,
      data: {
        isRead: true,
        readAt: new Date()
      }
    });
  }

  /**
   * Get unread message count for a user across all handovers
   */
  async getUnreadMessageCount(
    tenantId: string,
    userId: string,
    handoverId?: string
  ): Promise<number> {
    const where: any = {
      tenantId,
      isRead: false,
      authorId: { not: userId }, // Don't count own messages
    };

    if (handoverId) {
      where.thread = {
        handoverId
      };
    } else {
      // Count across all handovers where user is involved
      where.thread = {
        OR: [
          { createdById: userId },
          { assignedToId: userId }
        ]
      };
    }

    return await prisma.handoverQAMessage.count({ where });
  }
}

// Export singleton instance
export const handoverQAService = new HandoverQAService();
