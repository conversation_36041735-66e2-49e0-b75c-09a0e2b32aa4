# Use Node.js 20 as the base image with a specific version for security
FROM node:20.12-alpine3.19 AS base

# Install dependencies required for Prisma, health checks, and Puppeteer/Chrome
RUN apk add --no-cache \
    libc6-compat \
    openssl \
    curl \
    wget \
    chromium \
    nss \
    freetype \
    freetype-dev \
    harfbuzz \
    ca-certificates \
    ttf-freefont \
    font-noto-emoji \
    wqy-zenhei

# Set Puppeteer to use installed Chromium
ENV PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true \
    PUPPETEER_EXECUTABLE_PATH=/usr/bin/chromium-browser

# Install dependencies only when needed
FROM base AS deps
WORKDIR /app

# Copy package files
COPY package.json package-lock.json* ./

# Install dependencies
RUN npm ci

# Development image for local development with hot reloading
FROM base AS dev
WORKDIR /app

COPY --from=deps /app/node_modules ./node_modules
COPY . .

# Generate Prisma client
RUN npx prisma generate

# Make scripts executable
RUN chmod +x ./scripts/docker-migrate.sh
RUN chmod +x ./scripts/docker-mermaid-init.sh 2>/dev/null || true

EXPOSE 3000
CMD ["sh", "-c", "./scripts/docker-migrate.sh && npm run dev"]

# Build the application
FROM base AS builder
WORKDIR /app

COPY --from=deps /app/node_modules ./node_modules
COPY . .

# Generate Prisma client
RUN npx prisma generate

# Install Chrome headless shell for Puppeteer (required for Mermaid PNG generation)
RUN npx puppeteer browsers install chrome-headless-shell

# Build the Next.js application
RUN npm run build

# Production image, copy all the files and run next
FROM base AS runner
WORKDIR /app

ENV NODE_ENV=production

# Mermaid PNG generation environment variables
ENV PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true \
    PUPPETEER_EXECUTABLE_PATH=/usr/bin/chromium-browser \
    PUPPETEER_ARGS="--no-sandbox --disable-setuid-sandbox --disable-dev-shm-usage --disable-accelerated-2d-canvas --no-first-run --no-zygote --disable-gpu" \
    MERMAID_TEMP_DIR="/tmp/mermaid" \
    CHROME_BIN=/usr/bin/chromium-browser

# Create a non-root user to run the app
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

# Copy necessary files
COPY --from=builder /app/public ./public
COPY --from=builder /app/prisma ./prisma
COPY --from=builder /app/scripts ./scripts
COPY --from=builder /app/next.config.ts ./
COPY --from=builder /app/package.json ./
COPY --from=builder /app/node_modules/.prisma ./node_modules/.prisma
COPY --from=builder /app/node_modules/@prisma ./node_modules/@prisma

# Make scripts executable
RUN chmod +x ./scripts/docker-migrate.sh
RUN chmod +x ./scripts/docker-mermaid-init.sh 2>/dev/null || true

# Set the correct permissions for the uploads directory and temp directories
RUN mkdir -p ./uploads /tmp/mermaid && chown -R nextjs:nodejs ./uploads /tmp/mermaid

# Copy Chrome headless shell installation from builder
COPY --from=builder --chown=nextjs:nodejs /root/.cache/puppeteer /home/<USER>/.cache/puppeteer

# Automatically leverage output traces to reduce image size
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

# Install only production dependencies and Prisma CLI for migrations
RUN npm ci --omit=dev && npm install prisma@latest

# Generate Prisma client in production
RUN npx prisma generate

# Ensure nextjs user has access to Chrome and temp directories
RUN chown -R nextjs:nodejs /home/<USER>/.cache /tmp/mermaid

# Set the user to run the app
USER nextjs

# Expose the port the app will run on
EXPOSE 3000

# Create a healthcheck
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 CMD wget --no-verbose --tries=1 --spider http://localhost:3000/api/health || exit 1

# Command to run the app
CMD ["sh", "-c", "./scripts/docker-migrate.sh && node server.js"]
