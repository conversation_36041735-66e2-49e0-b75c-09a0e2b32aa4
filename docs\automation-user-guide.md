# Process Automation User Guide

## Overview

The Process Automation system automatically handles routine tasks when certain events occur in your CRM. This saves time and ensures consistent processes across your organization.

## Accessing Process Automation

1. **Navigate to Admin Section**: Click on "Administration" in the sidebar
2. **Select Process Automation**: Click on "Process Automation" in the admin menu
3. **View Dashboard**: You'll see the automation dashboard with metrics and active triggers

## How It Works

### Current Automation Triggers

The system comes with these pre-configured automations:

#### 1. **Opportunity Closed Won → Create Handover**
- **When**: An opportunity status changes to "Closed Won"
- **What happens**:
  - Automatically creates a handover document
  - Assigns it to the delivery team
  - Sends notifications to sales rep and delivery manager
  - Sets high priority for immediate attention

#### 2. **Opportunity → Proposal Stage**
- **When**: An opportunity moves to "Proposal" stage
- **What happens**:
  - Notifies sales rep and sales manager
  - Sets follow-up reminders

#### 3. **Opportunity → Negotiation Stage**
- **When**: An opportunity moves to "Negotiation" stage
- **What happens**:
  - Escalates notifications to include delivery manager
  - <PERSON> as high priority

## Testing the Automation

### Method 1: Change Opportunity Stage
1. Go to **Opportunities** → Select any opportunity
2. Change the stage to "Closed Won"
3. Check the **Automation Status** section on the opportunity page
4. Visit **Admin** → **Process Automation** to see execution logs

### Method 2: Use Pipeline Board
1. Go to **Opportunities** → **Pipeline View**
2. Drag an opportunity to the "Closed Won" column
3. The automation will trigger automatically

## Monitoring Automation

### On Individual Records
- **Opportunity Pages**: Look for the "Automation Status" card in the sidebar
- **Shows**: Recent automation activity, execution status, and action results

### On Automation Dashboard
- **Execution Metrics**: Success/failure rates, total executions
- **Active Triggers**: List of all automation rules
- **Recent Activity**: Latest automation executions across all records

## Understanding Automation Status

### Status Indicators
- **✅ Completed**: Automation ran successfully
- **⏳ Pending**: Automation is currently running
- **❌ Failed**: Automation encountered an error

### Execution Details
- **Trigger Name**: Which automation rule was activated
- **Actions Performed**: What the automation did (e.g., "Created handover", "Sent notification")
- **Timestamp**: When the automation ran
- **Results**: Success/failure details for each action

## Managing Automation

### Enabling/Disabling Triggers
1. Go to **Admin** → **Process Automation**
2. Click the **Triggers** tab
3. Use the toggle switch to enable/disable specific automations

### Viewing Execution History
1. In the automation dashboard, click **Executions** tab
2. Filter by:
   - Entity type (Opportunity, Handover, etc.)
   - Status (Completed, Failed, Pending)
   - Date range

## Troubleshooting

### Automation Not Triggering
1. **Check if trigger is active**: Go to automation dashboard → Triggers tab
2. **Verify conditions**: Ensure the opportunity stage change matches trigger conditions
3. **Check permissions**: User must have appropriate permissions to trigger automation

### Failed Executions
1. **View error details**: Click on failed execution in dashboard
2. **Common issues**:
   - Missing handover template
   - Invalid team assignments
   - Email notification failures
3. **Contact admin**: If issues persist, contact your system administrator

## Best Practices

### For Sales Teams
- **Update opportunity stages promptly**: This ensures timely automation
- **Use consistent stage names**: Automation depends on exact stage matches
- **Monitor automation status**: Check the automation section on opportunity pages

### For Delivery Teams
- **Check for new handovers**: Automation creates handovers automatically
- **Respond to notifications**: Act on automated notifications promptly
- **Update handover status**: Keep handover progress updated

### For Administrators
- **Monitor execution metrics**: Regular check automation success rates
- **Review failed executions**: Investigate and resolve automation failures
- **Update trigger conditions**: Adjust automation rules as business processes evolve

## Future Enhancements

The automation system is designed to be extensible. Future updates may include:

- **Custom trigger creation**: Build your own automation rules
- **Advanced conditions**: More complex trigger conditions
- **Integration actions**: Connect with external systems
- **Scheduled automations**: Time-based automation triggers
- **Approval workflows**: Multi-step approval processes

## Support

For technical issues or questions about process automation:

1. **Check execution logs**: Review automation dashboard for error details
2. **Contact system administrator**: For configuration or permission issues
3. **Submit feedback**: Suggest improvements or new automation ideas

---

*This guide covers the current automation features. As new capabilities are added, this documentation will be updated accordingly.*
