import type { Metadata } from "next";
import { Providers } from '@/components/providers/providers';
import { LoadingBar } from '@/components/ui/loading-bar';
import "./globals.css";

// Use system fonts temporarily to avoid Turbopack font loading issues
const fontVariables = "--font-inter --font-arabic";

export const metadata: Metadata = {
  title: "CRM Platform - Multi-Tenant SaaS Solution",
  description: "Comprehensive multi-tenant CRM platform with AI-powered features, internationalization, and enterprise-grade security.",
  keywords: ["CRM", "SaaS", "Multi-tenant", "AI", "Customer Management"],
  authors: [{ name: "CRM Platform Team" }],
};

export const viewport = {
  width: 'device-width',
  initialScale: 1,
};

interface RootLayoutProps {
  children: React.ReactNode;
}

export default function RootLayout({
  children,
}: RootLayoutProps) {
  return (
    <html lang="en" dir="ltr" className="font-sans" suppressHydrationWarning>
      <head>
        <meta name="theme-color" content="#ffffff" />
        <meta name="color-scheme" content="light dark" />
      </head>
      <body className="font-sans antialiased bg-gray-50 dark:bg-gray-900 text-gray-900 dark:text-gray-100 min-h-screen transition-all duration-300" suppressHydrationWarning>
        <LoadingBar />
        <Providers>
          {children}
        </Providers>
      </body>
    </html>
  );
}
