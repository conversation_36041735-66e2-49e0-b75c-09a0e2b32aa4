import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { hasPermission } from '@/lib/permissions';
import { PermissionResource, PermissionAction } from '@/types';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';

// Query validation schema
const getProposalsQuerySchema = z.object({
  search: z.string().optional(),
  status: z.string().optional(),
  version: z.string().optional(),
  dateRange: z.string().optional(),
  page: z.coerce.number().min(1).default(1),
  limit: z.coerce.number().min(1).max(100).default(20),
  sortBy: z.enum(['createdAt', 'updatedAt', 'title', 'version']).default('createdAt'),
  sortOrder: z.enum(['asc', 'desc']).default('desc')
});

/**
 * GET /api/opportunities/[id]/proposals
 * Get all proposals for a specific opportunity
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user's current tenant
    const currentTenant = session.user.tenants?.[0];
    if (!currentTenant) {
      return NextResponse.json({ error: 'No tenant access' }, { status: 403 });
    }

    // Check permission for proposal reading
    const hasAccess = await hasPermission(
      PermissionResource.PROPOSALS,
      PermissionAction.READ,
      currentTenant.id
    );

    if (!hasAccess) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const opportunityId = params.id;

    // Verify opportunity exists and belongs to tenant
    const opportunity = await prisma.opportunity.findFirst({
      where: {
        id: opportunityId,
        tenantId: currentTenant.id
      }
    });

    if (!opportunity) {
      return NextResponse.json(
        { error: 'Opportunity not found or access denied' },
        { status: 404 }
      );
    }

    // Parse and validate query parameters
    const { searchParams } = new URL(request.url);
    const queryParams = Object.fromEntries(searchParams.entries());
    const validatedQuery = getProposalsQuerySchema.parse(queryParams);

    // Build where clause
    const where: any = {
      opportunityId,
      tenantId: currentTenant.id
    };

    // Add search filter
    if (validatedQuery.search) {
      where.title = {
        contains: validatedQuery.search,
        mode: 'insensitive'
      };
    }

    // Add status filter
    if (validatedQuery.status && validatedQuery.status !== 'all') {
      where.status = validatedQuery.status;
    }

    // Add version filter
    if (validatedQuery.version && validatedQuery.version !== 'all') {
      if (validatedQuery.version === 'latest') {
        // Get latest version for each proposal title
        const latestVersions = await prisma.proposal.groupBy({
          by: ['title'],
          where: {
            opportunityId,
            tenantId: currentTenant.id
          },
          _max: {
            version: true
          }
        });

        const titleVersionPairs = latestVersions.map(lv => ({
          title: lv.title,
          version: lv._max.version
        }));

        where.OR = titleVersionPairs.map(pair => ({
          title: pair.title,
          version: pair.version
        }));
      } else {
        where.version = parseInt(validatedQuery.version);
      }
    }

    // Add date range filter
    if (validatedQuery.dateRange && validatedQuery.dateRange !== 'all') {
      const now = new Date();
      let startDate: Date;

      switch (validatedQuery.dateRange) {
        case 'today':
          startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
          break;
        case 'week':
          startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
          break;
        case 'month':
          startDate = new Date(now.getFullYear(), now.getMonth(), 1);
          break;
        case 'quarter':
          const quarterStart = Math.floor(now.getMonth() / 3) * 3;
          startDate = new Date(now.getFullYear(), quarterStart, 1);
          break;
        default:
          startDate = new Date(0);
      }

      where.createdAt = {
        gte: startDate
      };
    }

    // Calculate pagination
    const skip = (validatedQuery.page - 1) * validatedQuery.limit;

    // Get proposals with pagination
    const [proposals, totalCount] = await Promise.all([
      prisma.proposal.findMany({
        where,
        include: {
          createdBy: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true
            }
          },
          sections: {
            select: {
              id: true,
              status: true
            }
          },
          charts: {
            select: {
              id: true,
              status: true
            }
          }
        },
        orderBy: {
          [validatedQuery.sortBy]: validatedQuery.sortOrder
        },
        skip,
        take: validatedQuery.limit
      }),
      prisma.proposal.count({ where })
    ]);

    // Calculate statistics for each proposal
    const proposalsWithStats = proposals.map(proposal => {
      const completedSections = proposal.sections.filter(s => s.status === 'generated' || s.status === 'approved').length;
      const renderedCharts = proposal.charts.filter(c => c.status === 'rendered' || c.status === 'approved').length;
      
      return {
        ...proposal,
        statistics: {
          totalSections: proposal.sections.length,
          completedSections,
          totalCharts: proposal.charts.length,
          renderedCharts,
          completionPercentage: proposal.sections.length > 0 
            ? Math.round((completedSections / proposal.sections.length) * 100)
            : 0
        }
      };
    });

    const totalPages = Math.ceil(totalCount / validatedQuery.limit);

    return NextResponse.json({
      success: true,
      data: {
        proposals: proposalsWithStats,
        pagination: {
          page: validatedQuery.page,
          limit: validatedQuery.limit,
          totalCount,
          totalPages,
          hasNext: validatedQuery.page < totalPages,
          hasPrev: validatedQuery.page > 1
        }
      },
      message: 'Proposals retrieved successfully'
    });

  } catch (error) {
    console.error('Error retrieving proposals:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
