# Enhanced Mermaid Diagram Features

This document describes the enhanced features added to the MermaidDiagram component for better chart interaction and visibility.

## New Features

### 1. Auto-Rendering
Charts now automatically render when the component mounts, eliminating the need for manual render buttons.

**Props:**
- `autoRender?: boolean` (default: `true`)

**Usage:**
```tsx
<MermaidDiagram
  code={chartCode}
  autoRender={true} // Chart renders automatically
/>
```

### 2. Zoom Controls
Interactive zoom functionality with mouse wheel and button controls.

**Props:**
- `showZoomControls?: boolean` (default: `true`)
- `maxZoom?: number` (default: `3`)
- `minZoom?: number` (default: `0.1`)

**Features:**
- Zoom in/out buttons in the header
- Mouse wheel zoom support
- Reset zoom button
- Zoom level indicator overlay

### 3. Pan Functionality
Click and drag to move around large charts that don't fit in the container.

**Props:**
- `enablePanZoom?: boolean` (default: `true`)

**Features:**
- Click and drag to pan
- Cursor changes to indicate pan mode
- Smooth transitions
- Pan instructions overlay

### 4. Fullscreen Mode
View charts in a larger dialog for better visibility.

**Props:**
- `showFullscreenButton?: boolean` (default: `true`)

**Features:**
- Fullscreen button in header
- Large dialog with zoom/pan controls
- All interaction features work in fullscreen

## Component Interface

```tsx
interface MermaidDiagramProps {
  // Existing props
  code: string;
  title?: string;
  theme?: 'default' | 'dark' | 'forest' | 'neutral';
  className?: string;
  showControls?: boolean;
  onError?: (error: string) => void;
  onSuccess?: () => void;
  chartId?: string;
  proposalId?: string;
  onChartRegenerated?: (newCode: string) => void;
  showFixButton?: boolean;
  showRegenerateButton?: boolean;
  
  // New enhanced features
  autoRender?: boolean;
  showZoomControls?: boolean;
  showFullscreenButton?: boolean;
  enablePanZoom?: boolean;
  maxZoom?: number;
  minZoom?: number;
}
```

## Usage Examples

### Basic Enhanced Chart
```tsx
<MermaidDiagram
  code={chartCode}
  title="System Architecture"
  autoRender={true}
  enablePanZoom={true}
  showZoomControls={true}
  showFullscreenButton={true}
/>
```

### Chart with Custom Zoom Limits
```tsx
<MermaidDiagram
  code={chartCode}
  title="Detailed Flow Chart"
  maxZoom={5}
  minZoom={0.2}
  enablePanZoom={true}
/>
```

### Fullscreen Only (No Pan/Zoom)
```tsx
<MermaidDiagram
  code={chartCode}
  title="Simple Diagram"
  enablePanZoom={false}
  showZoomControls={false}
  showFullscreenButton={true}
/>
```

### Manual Render Mode
```tsx
<MermaidDiagram
  code={chartCode}
  title="Manual Chart"
  autoRender={false} // User must click refresh to render
  enablePanZoom={true}
/>
```

## User Interactions

### Zoom Controls
1. **Zoom In Button**: Increases zoom by 20%
2. **Zoom Out Button**: Decreases zoom by 20%
3. **Reset Button**: Returns to 100% zoom and center position
4. **Mouse Wheel**: Scroll up to zoom in, down to zoom out

### Pan Controls
1. **Click and Drag**: Move around the chart
2. **Cursor Feedback**: Changes to grab/grabbing cursor
3. **Smooth Transitions**: Animated zoom/pan changes

### Fullscreen Mode
1. **Fullscreen Button**: Opens chart in large dialog
2. **All Controls Available**: Zoom, pan, and other controls work
3. **Responsive**: Adapts to different screen sizes

## Visual Indicators

### Zoom Level Indicator
- Shows current zoom percentage (e.g., "150%")
- Appears in top-right corner when zoomed
- Only visible when zoom ≠ 100%

### Pan/Zoom Instructions
- Shows "Drag to pan • Scroll to zoom" in bottom-left
- Appears when pan/zoom is enabled
- Includes move icon for clarity

## Best Practices

### When to Use Auto-Render
- **Enable** for most use cases (default behavior)
- **Disable** for large/complex charts that might impact performance
- **Disable** when you want user control over rendering

### When to Use Pan/Zoom
- **Enable** for complex architecture diagrams
- **Enable** for large flowcharts or process diagrams
- **Disable** for simple charts that fit well in container

### Zoom Limits
- **Default limits** (0.1 - 3x) work for most cases
- **Increase maxZoom** for very detailed diagrams
- **Adjust minZoom** based on chart complexity

### Fullscreen Mode
- **Always enable** for better user experience
- Especially useful for mobile devices
- Provides better visibility for complex charts

## Performance Considerations

1. **Auto-render**: Minimal impact, charts render efficiently
2. **Zoom/Pan**: Uses CSS transforms for smooth performance
3. **Fullscreen**: Creates separate render context, no duplication
4. **Memory**: No significant memory overhead

## Browser Compatibility

- **Modern browsers**: Full support for all features
- **CSS transforms**: Required for zoom/pan functionality
- **Dialog API**: Uses Radix UI for cross-browser compatibility
- **Mouse events**: Standard event handling, widely supported

## Migration Guide

### From Basic MermaidDiagram
```tsx
// Before
<MermaidDiagram code={code} title="Chart" />

// After (enhanced features enabled by default)
<MermaidDiagram code={code} title="Chart" />
```

### Disabling New Features
```tsx
<MermaidDiagram
  code={code}
  title="Chart"
  autoRender={false}
  enablePanZoom={false}
  showZoomControls={false}
  showFullscreenButton={false}
/>
```

## Testing

The enhanced Mermaid features can be tested by:
1. Creating diagrams with the new features enabled
2. Verifying different chart types work correctly
3. Checking performance with complex diagrams
4. Validating user interactions

## Troubleshooting

### Chart Not Auto-Rendering
- Check `autoRender` prop is `true`
- Verify chart code is valid
- Check browser console for errors

### Zoom/Pan Not Working
- Ensure `enablePanZoom` is `true`
- Check if container has proper dimensions
- Verify browser supports CSS transforms

### Fullscreen Issues
- Check if Dialog component is properly imported
- Verify no CSS conflicts with modal styles
- Test on different screen sizes
