'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { PermissionProvider } from '@/components/providers/permission-provider';
import { PermissionGate } from '@/components/ui/permission-gate';
import { PermissionAction, PermissionResource } from '@/types';
import { useTenant } from '@/hooks/use-tenant';
import { PageLayout } from '@/components/layout/page-layout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { useToast } from '@/hooks/use-toast';
import { RolePermissionsTab } from '@/components/roles/role-permissions-tab';
import { RoleUsersTab } from '@/components/roles/role-users-tab';
import {
  ArrowLeftIcon,
  PencilIcon,
  TrashIcon,
  UserGroupIcon,
  ShieldCheckIcon,
  ExclamationTriangleIcon,
  KeyIcon,
  CheckCircleIcon
} from '@heroicons/react/24/outline';

interface Role {
  id: string;
  name: string;
  description: string;
  isSystemRole: boolean;
  parentRoleId?: string;
  permissions: Permission[];
  childRoles: Role[];
  userCount?: number;
}

interface Permission {
  id: string;
  name: string;
  resource: string;
  action: string;
  category: string;
  description: string;
}

export default function RoleDetailsPage() {
  const { currentTenant } = useTenant();
  const router = useRouter();
  const params = useParams();
  const { toast } = useToast();
  const roleId = params.id as string;

  const [role, setRole] = useState<Role | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);
  const [activeTab, setActiveTab] = useState('permissions');

  useEffect(() => {
    if (currentTenant && roleId) {
      fetchRoleDetails();
    }
  }, [currentTenant, roleId]);

  const fetchRoleDetails = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch(`/api/roles/${roleId}`);
      if (response.ok) {
        const data = await response.json();
        setRole(data.data.role);
      } else {
        const errorData = await response.json();
        setError(errorData.error || 'Failed to fetch role details');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch role details');
    } finally {
      setIsLoading(false);
    }
  };

  const handleBackToRoles = () => {
    router.push('/settings/roles');
  };

  const handleEditRole = () => {
    router.push(`/settings/roles/${roleId}/edit`);
  };

  const handleDeleteRole = async () => {
    if (!role || !confirm(`Are you sure you want to delete the role "${role.name}"? This action cannot be undone.`)) {
      return;
    }

    try {
      setIsDeleting(true);
      const response = await fetch(`/api/roles?roleId=${roleId}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        toast({
          title: 'Success',
          description: 'Role deleted successfully',
        });
        router.push('/settings/roles');
      } else {
        const errorData = await response.json();
        toast({
          title: 'Error',
          description: errorData.error || 'Failed to delete role',
          variant: 'destructive',
        });
      }
    } catch (err) {
      toast({
        title: 'Error',
        description: err instanceof Error ? err.message : 'Failed to delete role',
        variant: 'destructive',
      });
    } finally {
      setIsDeleting(false);
    }
  };

  if (!currentTenant) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <ExclamationTriangleIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-500">No tenant context available</p>
        </div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner />
      </div>
    );
  }

  if (error || !role) {
    return (
      <PageLayout
        title="Role Details"
        description="View role information and permissions"
      >
        <Card className="border-red-200 bg-red-50">
          <CardContent className="p-12 text-center">
            <ExclamationTriangleIcon className="w-12 h-12 text-red-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Error Loading Role</h3>
            <p className="text-gray-500 mb-4">{error || 'Role not found'}</p>
            <Button onClick={handleBackToRoles}>
              <ArrowLeftIcon className="w-4 h-4 mr-2" />
              Back to Roles
            </Button>
          </CardContent>
        </Card>
      </PageLayout>
    );
  }

  const actions = (
    <div className="flex items-center gap-2">
      <Button variant="outline" onClick={handleBackToRoles}>
        <ArrowLeftIcon className="w-4 h-4 mr-2" />
        Back to Roles
      </Button>

      {/* Regular role editing */}
      <PermissionGate
        resource={PermissionResource.ROLES}
        action={PermissionAction.UPDATE}
      >
        <Button
          variant="outline"
          onClick={handleEditRole}
          disabled={role.isSystemRole}
        >
          <PencilIcon className="w-4 h-4 mr-2" />
          Edit Role
        </Button>
      </PermissionGate>

      {/* System role editing - requires special permission */}
      {role.isSystemRole && (
        <PermissionGate
          resource={PermissionResource.ROLES}
          action={PermissionAction.MANAGE_SYSTEM_ROLES}
        >
          <Button
            variant="outline"
            onClick={() => router.push(`/settings/roles/${roleId}/edit-system`)}
            className="border-orange-300 text-orange-700 hover:bg-orange-50"
          >
            <PencilIcon className="w-4 h-4 mr-2" />
            Edit System Role
          </Button>
        </PermissionGate>
      )}

      <PermissionGate
        resource={PermissionResource.ROLES}
        action={PermissionAction.DELETE}
      >
        <Button
          variant="destructive"
          onClick={handleDeleteRole}
          disabled={role.isSystemRole || isDeleting}
        >
          {isDeleting ? (
            <LoadingSpinner className="w-4 h-4 mr-2" />
          ) : (
            <TrashIcon className="w-4 h-4 mr-2" />
          )}
          Delete Role
        </Button>
      </PermissionGate>
    </div>
  );

  // Group permissions by category
  const groupedPermissions = role.permissions.reduce((acc, permission) => {
    const category = permission.category;
    if (!acc[category]) {
      acc[category] = [];
    }
    acc[category].push(permission);
    return acc;
  }, {} as Record<string, Permission[]>);

  return (
    <PermissionProvider>
      <PageLayout
        title={role.name}
        description={role.description || 'Role details and permissions'}
        actions={actions}
      >
        <PermissionGate
          resource={PermissionResource.ROLES}
          action={PermissionAction.READ}
          fallback={<AccessDeniedMessage />}
        >
          <div className="space-y-6">
            {/* Role Overview */}
            <Card>
              <CardHeader>
                <div className="flex items-center gap-3">
                  <div className={`p-3 rounded-lg ${role.isSystemRole ? 'bg-blue-100' : 'bg-gray-100'}`}>
                    {role.isSystemRole ? (
                      <ShieldCheckIcon className="w-6 h-6 text-blue-600" />
                    ) : (
                      <KeyIcon className="w-6 h-6 text-gray-600" />
                    )}
                  </div>
                  <div>
                    <CardTitle className="text-2xl">{role.name}</CardTitle>
                    {role.isSystemRole && (
                      <span className="inline-block bg-blue-100 text-blue-800 text-sm px-3 py-1 rounded-full mt-1">
                        System Role
                      </span>
                    )}
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Description</h4>
                    <p className="text-gray-600">
                      {role.description || 'No description provided'}
                    </p>
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Users Assigned</h4>
                    <div className="flex items-center gap-2">
                      <UserGroupIcon className="w-5 h-5 text-gray-400" />
                      <span className="text-lg font-semibold">{role.userCount || 0}</span>
                    </div>
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Permissions</h4>
                    <div className="flex items-center gap-2">
                      <KeyIcon className="w-5 h-5 text-gray-400" />
                      <span className="text-lg font-semibold">{role.permissions.length}</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Tabbed Content */}
            <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="permissions" className="flex items-center space-x-2">
                  <KeyIcon className="h-4 w-4" />
                  <span>Permissions ({role.permissions.length})</span>
                </TabsTrigger>
                <TabsTrigger value="users" className="flex items-center space-x-2">
                  <UserGroupIcon className="h-4 w-4" />
                  <span>Assigned Users ({role.userCount || 0})</span>
                </TabsTrigger>
              </TabsList>

              <TabsContent value="permissions">
                <RolePermissionsTab
                  roleId={role.id}
                  roleName={role.name}
                  isSystemRole={role.isSystemRole}
                  initialPermissions={role.permissions}
                />
              </TabsContent>

              <TabsContent value="users">
                <RoleUsersTab
                  roleId={role.id}
                  roleName={role.name}
                  isSystemRole={role.isSystemRole}
                />
              </TabsContent>
            </Tabs>

            {/* Child Roles */}
            {role.childRoles.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle>Child Roles</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {role.childRoles.map((childRole) => (
                      <div
                        key={childRole.id}
                        className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer"
                        onClick={() => router.push(`/settings/roles/${childRole.id}`)}
                      >
                        <div className="flex items-center gap-2">
                          <KeyIcon className="w-4 h-4 text-gray-400" />
                          <h5 className="font-medium text-gray-900">{childRole.name}</h5>
                        </div>
                        {childRole.description && (
                          <p className="text-sm text-gray-500 mt-1 line-clamp-2">
                            {childRole.description}
                          </p>
                        )}
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </PermissionGate>
      </PageLayout>
    </PermissionProvider>
  );
}

function AccessDeniedMessage() {
  return (
    <Card>
      <CardContent className="p-12 text-center">
        <ExclamationTriangleIcon className="w-12 h-12 text-red-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">Access Denied</h3>
        <p className="text-gray-500">
          You don't have permission to view role details. Contact your administrator for access.
        </p>
      </CardContent>
    </Card>
  );
}
