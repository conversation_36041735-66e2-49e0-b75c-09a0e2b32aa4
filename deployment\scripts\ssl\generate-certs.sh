#!/bin/bash
# Script to generate self-signed SSL certificates for development

# Create directory for certificates if it doesn't exist
mkdir -p ./config/nginx/ssl

# Generate a private key
openssl genrsa -out ./config/nginx/ssl/server.key 2048

# Generate a CSR (Certificate Signing Request)
openssl req -new -key ./config/nginx/ssl/server.key -out ./config/nginx/ssl/server.csr -subj "/C=US/ST=State/L=City/O=Organization/CN=localhost"

# Generate a self-signed certificate
openssl x509 -req -days 365 -in ./config/nginx/ssl/server.csr -signkey ./config/nginx/ssl/server.key -out ./config/nginx/ssl/server.crt

# Set permissions
chmod 600 ./config/nginx/ssl/server.key
chmod 600 ./config/nginx/ssl/server.crt

echo "Self-signed SSL certificates generated successfully!"
echo "Location: ./config/nginx/ssl/"
echo "Note: These certificates are for development only. Use proper certificates for production."
