import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { prisma } from '@/lib/prisma';
import { withPermission, AuthenticatedRequest } from '@/lib/permission-middleware';
import { PermissionResource, PermissionAction } from '@/types';

const analyticsQuerySchema = z.object({
  timeRange: z.enum(['1month', '3months', '6months', '1year', 'all']).default('6months'),
});

/**
 * GET /api/projects/analytics
 * Get comprehensive project analytics for the current tenant
 */
export const GET = withPermission({
  resource: PermissionResource.PROJECTS,
  action: PermissionAction.READ,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest) => {
  try {
    const { searchParams } = new URL(req.url);
    const queryParams = Object.fromEntries(searchParams.entries());
    
    const { timeRange } = analyticsQuerySchema.parse(queryParams);

    // Calculate date range
    const now = new Date();
    let startDate: Date;
    
    switch (timeRange) {
      case '1month':
        startDate = new Date(now.getFullYear(), now.getMonth() - 1, now.getDate());
        break;
      case '3months':
        startDate = new Date(now.getFullYear(), now.getMonth() - 3, now.getDate());
        break;
      case '6months':
        startDate = new Date(now.getFullYear(), now.getMonth() - 6, now.getDate());
        break;
      case '1year':
        startDate = new Date(now.getFullYear() - 1, now.getMonth(), now.getDate());
        break;
      default:
        startDate = new Date(2020, 0, 1); // Far back date for 'all'
    }

    // Base where clause for time filtering
    const timeFilter = timeRange === 'all' ? {} : {
      createdAt: {
        gte: startDate,
      },
    };

    // Get all projects for the tenant within time range
    const projects = await prisma.project.findMany({
      where: {
        tenantId: req.tenantId,
        ...timeFilter,
      },
      include: {
        tasks: {
          select: {
            status: true,
            actualHours: true,
            estimatedHours: true,
          },
        },
        resources: {
          select: {
            allocation: true,
            hoursAllocated: true,
            hoursLogged: true,
            isActive: true,
          },
        },
      },
    });

    // Calculate overview metrics
    const totalProjects = projects.length;
    const activeProjects = projects.filter(p => ['planning', 'in_progress'].includes(p.status)).length;
    const completedProjects = projects.filter(p => p.status === 'completed').length;
    const totalBudget = projects.reduce((sum, p) => sum + (Number(p.budget) || 0), 0);
    const totalEstimatedHours = projects.reduce((sum, p) => sum + (p.estimatedHours || 0), 0);
    const totalActualHours = projects.reduce((sum, p) => sum + (p.actualHours || 0), 0);
    const averageProgress = projects.length > 0 
      ? projects.reduce((sum, p) => sum + Number(p.progress), 0) / projects.length 
      : 0;

    // Calculate on-time delivery
    const completedOnTime = projects.filter(p => 
      p.status === 'completed' && 
      p.endDate && 
      p.updatedAt <= new Date(p.endDate)
    ).length;
    const onTimeDelivery = completedProjects > 0 ? (completedOnTime / completedProjects) * 100 : 0;

    // Calculate budget utilization
    const budgetUtilization = totalBudget > 0 ? (totalActualHours * 100) / totalEstimatedHours : 0; // Simplified calculation

    // Status distribution
    const statusCounts = projects.reduce((acc, project) => {
      acc[project.status] = (acc[project.status] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const statusDistribution = Object.entries(statusCounts).map(([status, count]) => ({
      name: status.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase()),
      value: count,
      color: getStatusColor(status),
    }));

    // Monthly progress (last 12 months)
    const monthlyProgress = [];
    for (let i = 11; i >= 0; i--) {
      const monthStart = new Date(now.getFullYear(), now.getMonth() - i, 1);
      const monthEnd = new Date(now.getFullYear(), now.getMonth() - i + 1, 0);
      
      const started = projects.filter(p => 
        p.createdAt >= monthStart && p.createdAt <= monthEnd
      ).length;
      
      const completed = projects.filter(p => 
        p.status === 'completed' && 
        p.updatedAt >= monthStart && 
        p.updatedAt <= monthEnd
      ).length;

      const monthBudget = projects
        .filter(p => p.createdAt >= monthStart && p.createdAt <= monthEnd)
        .reduce((sum, p) => sum + (Number(p.budget) || 0), 0);

      monthlyProgress.push({
        month: monthStart.toLocaleDateString('en-US', { month: 'short', year: '2-digit' }),
        started,
        completed,
        budget: monthBudget,
      });
    }

    // Resource utilization
    const resourceData = projects.flatMap(p => p.resources);
    const resourceUtilization = [
      {
        name: 'Developers',
        allocated: resourceData.filter(r => r.isActive && r.hoursAllocated).length,
        utilized: resourceData.filter(r => r.hoursLogged > 0).length,
        efficiency: calculateEfficiency(resourceData.filter(r => r.isActive)),
      },
      // Add more role-based analysis as needed
    ];

    // Budget analysis by project status
    const budgetAnalysis = [
      {
        category: 'Planning',
        budgeted: projects.filter(p => p.status === 'planning').reduce((sum, p) => sum + (Number(p.budget) || 0), 0),
        actual: projects.filter(p => p.status === 'planning').reduce((sum, p) => sum + (p.actualHours || 0) * 100, 0), // Simplified
        variance: 0,
      },
      {
        category: 'In Progress',
        budgeted: projects.filter(p => p.status === 'in_progress').reduce((sum, p) => sum + (Number(p.budget) || 0), 0),
        actual: projects.filter(p => p.status === 'in_progress').reduce((sum, p) => sum + (p.actualHours || 0) * 100, 0),
        variance: 0,
      },
      {
        category: 'Completed',
        budgeted: projects.filter(p => p.status === 'completed').reduce((sum, p) => sum + (Number(p.budget) || 0), 0),
        actual: projects.filter(p => p.status === 'completed').reduce((sum, p) => sum + (p.actualHours || 0) * 100, 0),
        variance: 0,
      },
    ];

    // Calculate variances
    budgetAnalysis.forEach(item => {
      item.variance = item.actual - item.budgeted;
    });

    // Risk analysis
    const overdue = projects.filter(p => 
      p.endDate && 
      new Date(p.endDate) < now && 
      !['completed', 'cancelled'].includes(p.status)
    ).length;

    const atRisk = projects.filter(p => {
      if (!p.endDate || ['completed', 'cancelled'].includes(p.status)) return false;
      const daysUntilDue = Math.ceil((new Date(p.endDate).getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
      return daysUntilDue <= 7 && daysUntilDue > 0 && Number(p.progress) < 75;
    }).length;

    const onTrack = projects.filter(p => 
      ['planning', 'in_progress'].includes(p.status) && 
      (!p.endDate || new Date(p.endDate) >= now) &&
      (Number(p.progress) >= 75 || !p.endDate)
    ).length;

    const riskAnalysis = {
      overdue,
      atRisk,
      onTrack,
      completed: completedProjects,
    };

    const analytics = {
      overview: {
        totalProjects,
        activeProjects,
        completedProjects,
        totalBudget,
        totalHours: totalActualHours,
        averageProgress,
        onTimeDelivery,
        budgetUtilization,
      },
      statusDistribution,
      monthlyProgress,
      resourceUtilization,
      budgetAnalysis,
      riskAnalysis,
    };

    return NextResponse.json({
      success: true,
      data: analytics,
      message: 'Project analytics retrieved successfully'
    });
  } catch (error) {
    console.error('Get project analytics error:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid query parameters', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to fetch project analytics' },
      { status: 500 }
    );
  }
});

function getStatusColor(status: string): string {
  const colors = {
    planning: '#3B82F6',
    in_progress: '#10B981',
    on_hold: '#F59E0B',
    completed: '#6B7280',
    cancelled: '#EF4444',
  };
  return colors[status as keyof typeof colors] || '#6B7280';
}

function calculateEfficiency(resources: any[]): number {
  if (resources.length === 0) return 0;
  
  const totalAllocated = resources.reduce((sum, r) => sum + (r.hoursAllocated || 0), 0);
  const totalLogged = resources.reduce((sum, r) => sum + (r.hoursLogged || 0), 0);
  
  return totalAllocated > 0 ? (totalLogged / totalAllocated) * 100 : 0;
}
