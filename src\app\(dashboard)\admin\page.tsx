'use client';

import React, { useState } from 'react';
import { PermissionProvider } from '@/components/providers/permission-provider';
import { PermissionDashboard } from '@/components/admin/permission-dashboard';
import { UserManagement } from '@/components/admin/user-management';
import { RoleManagement } from '@/components/admin/role-management';
import { TranslationManagement } from '@/components/admin/translation-management';
import { PermissionGate, TenantAdminGate } from '@/components/ui/permission-gate';
import { PermissionAction, PermissionResource } from '@/types';
import { useTenant } from '@/hooks/use-tenant';
import {
  ChartBarIcon,
  UserGroupIcon,
  ShieldCheckIcon,
  LanguageIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';

type AdminTab = 'dashboard' | 'users' | 'roles' | 'translations';

export default function AdminPage() {
  const [activeTab, setActiveTab] = useState<AdminTab>('dashboard');
  const { currentTenant } = useTenant();

  if (!currentTenant) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <ExclamationTriangleIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-500">No tenant context available</p>
        </div>
      </div>
    );
  }

  const tabs = [
    {
      id: 'dashboard' as AdminTab,
      name: 'Permission Dashboard',
      icon: ChartBarIcon,
      permission: { resource: PermissionResource.ROLES, action: PermissionAction.READ }
    },
    {
      id: 'users' as AdminTab,
      name: 'User Management',
      icon: UserGroupIcon,
      permission: { resource: PermissionResource.USERS, action: PermissionAction.READ }
    },
    {
      id: 'roles' as AdminTab,
      name: 'Role Management',
      icon: ShieldCheckIcon,
      permission: { resource: PermissionResource.ROLES, action: PermissionAction.READ }
    },
    {
      id: 'translations' as AdminTab,
      name: 'Translation Management',
      icon: LanguageIcon,
      permission: { resource: PermissionResource.SETTINGS, action: PermissionAction.MANAGE },
      adminOnly: true
    }
  ];

  return (
    <PermissionProvider>
      <TenantAdminGate
        fallback={
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <ExclamationTriangleIcon className="w-12 h-12 text-red-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">Access Denied</h3>
              <p className="text-gray-500">You need tenant admin privileges to access this area.</p>
            </div>
          </div>
        }
      >
        <div className="space-y-6">
          {/* Header */}
          <div className="border-b border-gray-200 pb-4">
            <h1 className="text-2xl font-bold text-gray-900">Administration</h1>
            <p className="text-gray-600 mt-1">
              Manage users, roles, and permissions for {currentTenant.name}
            </p>
          </div>

          {/* Tab Navigation */}
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8">
              {tabs.map((tab) => (
                <PermissionGate
                  key={tab.id}
                  resource={tab.permission.resource}
                  action={tab.permission.action}
                >
                  <button
                    onClick={() => setActiveTab(tab.id)}
                    className={`
                      group inline-flex items-center py-2 px-1 border-b-2 font-medium text-sm transition-colors
                      ${activeTab === tab.id
                        ? 'border-blue-500 text-blue-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                      }
                    `}
                  >
                    <tab.icon
                      className={`
                        -ml-0.5 mr-2 h-5 w-5 transition-colors
                        ${activeTab === tab.id
                          ? 'text-blue-500'
                          : 'text-gray-400 group-hover:text-gray-500'
                        }
                      `}
                    />
                    {tab.name}
                  </button>
                </PermissionGate>
              ))}
            </nav>
          </div>

          {/* Tab Content */}
          <div className="mt-6">
            {activeTab === 'dashboard' && (
              <PermissionGate
                resource={PermissionResource.ROLES}
                action={PermissionAction.READ}
                fallback={<AccessDeniedMessage />}
              >
                <PermissionDashboard tenantId={currentTenant.id} />
              </PermissionGate>
            )}

            {activeTab === 'users' && (
              <PermissionGate
                resource={PermissionResource.USERS}
                action={PermissionAction.READ}
                fallback={<AccessDeniedMessage />}
              >
                <UserManagement tenantId={currentTenant.id} />
              </PermissionGate>
            )}

            {activeTab === 'roles' && (
              <PermissionGate
                resource={PermissionResource.ROLES}
                action={PermissionAction.READ}
                fallback={<AccessDeniedMessage />}
              >
                <RoleManagement tenantId={currentTenant.id} />
              </PermissionGate>
            )}

            {activeTab === 'translations' && (
              <TenantAdminGate
                fallback={<AccessDeniedMessage message="Translation management is only available to tenant administrators." />}
              >
                <TranslationManagement tenantId={currentTenant.id} />
              </TenantAdminGate>
            )}


          </div>
        </div>
      </TenantAdminGate>
    </PermissionProvider>
  );
}

function AccessDeniedMessage({ message }: { message?: string }) {
  return (
    <div className="text-center py-12">
      <ExclamationTriangleIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
      <h3 className="text-lg font-medium text-gray-900 mb-2">Access Denied</h3>
      <p className="text-gray-500 max-w-md mx-auto">
        {message || "You don't have permission to access this section."}
      </p>
    </div>
  );
}
