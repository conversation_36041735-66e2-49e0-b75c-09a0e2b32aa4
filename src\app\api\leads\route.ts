import { NextResponse } from 'next/server';
import { withPermission } from '@/lib/permission-middleware';
import {
  leadManagementService,
  createLeadSchema,
  getLeadsQuerySchema,
  CreateLeadData
} from '@/services/lead-management';
import { PermissionAction, PermissionResource } from '@/types';
import { AuthenticatedRequest } from '@/lib/permission-middleware';
import { prisma } from '@/lib/prisma';

/**
 * GET /api/leads
 * Get leads with filtering and pagination
 */
export const GET = withPermission({
  resource: PermissionResource.LEADS,
  action: PermissionAction.READ,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest) => {
  try {
    const { searchParams } = new URL(req.url);
    const queryParams = Object.fromEntries(searchParams.entries());

    console.log('Leads API - Raw query params:', queryParams);

    const validatedQuery = getLeadsQuerySchema.parse(queryParams);

    console.log('Leads API - Validated query:', validatedQuery);
    console.log('Leads API - Tenant ID:', req.tenantId);
    console.log('Leads API - User ID:', req.userId);

    // Debug: Check if user has access to this tenant
    const tenantUser = await prisma.tenantUser.findFirst({
      where: {
        userId: req.userId,
        tenantId: req.tenantId,
        status: 'active'
      }
    });
    console.log('Leads API - Tenant user relationship:', tenantUser ? 'Found' : 'Not found');

    // Debug: Check total leads in this tenant
    const totalLeadsInTenant = await prisma.lead.count({
      where: { tenantId: req.tenantId }
    });
    console.log('Leads API - Total leads in tenant:', totalLeadsInTenant);

    const result = await leadManagementService.getLeads(
      req.tenantId,
      validatedQuery,
      req.userId
    );

    console.log('Leads API - Service result:', {
      totalLeads: result.leads.length,
      total: result.total,
      page: result.page,
      totalPages: result.totalPages
    });

    return NextResponse.json({
      success: true,
      data: result,
      message: 'Leads retrieved successfully'
    });

  } catch (error) {
    console.error('Get leads API error:', error);

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to fetch leads' },
      { status: 500 }
    );
  }
});

/**
 * POST /api/leads
 * Create a new lead
 */
export const POST = withPermission({
  resource: PermissionResource.LEADS,
  action: PermissionAction.CREATE,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest) => {
  try {
    const body = await req.json();
    const validatedData = createLeadSchema.parse(body);

    const lead = await leadManagementService.createLead(
      req.tenantId,
      validatedData,
      req.userId
    );

    return NextResponse.json({
      success: true,
      data: { lead },
      message: 'Lead created successfully'
    }, { status: 201 });

  } catch (error) {
    console.error('Create lead API error:', error);
    
    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      { error: 'Failed to create lead' },
      { status: 500 }
    );
  }
});

/**
 * DELETE /api/leads
 * Bulk delete leads
 */
export const DELETE = withPermission({
  resource: PermissionResource.LEADS,
  action: PermissionAction.DELETE,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest) => {
  try {
    const body = await req.json();
    const { leadIds } = body;

    if (!Array.isArray(leadIds) || leadIds.length === 0) {
      return NextResponse.json(
        { error: 'Lead IDs array is required' },
        { status: 400 }
      );
    }

    // Limit bulk operations to prevent abuse
    if (leadIds.length > 100) {
      return NextResponse.json(
        { error: 'Maximum 100 leads can be deleted at once' },
        { status: 400 }
      );
    }

    const deletedCount = await leadManagementService.bulkDeleteLeads(
      leadIds,
      req.tenantId
    );

    return NextResponse.json({
      success: true,
      data: { deletedCount },
      message: `${deletedCount} lead(s) deleted successfully`
    });

  } catch (error) {
    console.error('Bulk delete leads API error:', error);
    
    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      { error: 'Failed to delete leads' },
      { status: 500 }
    );
  }
});
