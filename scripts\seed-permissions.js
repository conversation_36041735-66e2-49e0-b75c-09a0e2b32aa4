#!/usr/bin/env node

/**
 * Comprehensive Permission Seeding Script
 * Seeds the database with granular permissions for all CRM modules
 *
 * This script now uses the centralized permission constants to ensure
 * consistency between seeded permissions and the application's permission system.
 */

const { PrismaClient } = require('@prisma/client');
const path = require('path');

// Import the centralized permission constants
// Note: We need to use a dynamic import since this is a CommonJS script
// importing from a TypeScript module
const prisma = new PrismaClient();

// Permission Resources (matching PermissionResource enum)
const PermissionResource = {
  CONTACTS: 'contacts',
  COMPANIES: 'companies',
  LEADS: 'leads',
  OPPORTUNITIES: 'opportunities',
  PROPOSALS: 'proposals',
  PROJECTS: 'projects',
  HANDOVERS: 'handovers',
  COMMUNICATION: 'communication',
  USERS: 'users',
  ROLES: 'roles',
  SETTINGS: 'settings',
  AI_FEATURES: 'ai_features',
  REPORTS: 'reports',
  CALLS: 'calls'
};

// Permission Actions (matching PermissionAction enum)
const PermissionAction = {
  CREATE: 'CREATE',
  READ: 'READ',
  UPDATE: 'UPDATE',
  DELETE: 'DELETE',
  EXPORT: 'EXPORT',
  MANAGE: 'MANAGE'
};

// Helper function to create permission name (matching the constants file)
const createPermission = (resource, action) => {
  return `${resource}.${action.toLowerCase()}`;
};

// Core permission definitions using the centralized approach
const CORE_PERMISSIONS = {
  // Contact Permissions
  CONTACTS: {
    CREATE: createPermission(PermissionResource.CONTACTS, PermissionAction.CREATE),
    READ: createPermission(PermissionResource.CONTACTS, PermissionAction.READ),
    UPDATE: createPermission(PermissionResource.CONTACTS, PermissionAction.UPDATE),
    DELETE: createPermission(PermissionResource.CONTACTS, PermissionAction.DELETE),
    EXPORT: createPermission(PermissionResource.CONTACTS, PermissionAction.EXPORT),
    MANAGE: createPermission(PermissionResource.CONTACTS, PermissionAction.MANAGE),
  },

  // Company Permissions
  COMPANIES: {
    CREATE: createPermission(PermissionResource.COMPANIES, PermissionAction.CREATE),
    READ: createPermission(PermissionResource.COMPANIES, PermissionAction.READ),
    UPDATE: createPermission(PermissionResource.COMPANIES, PermissionAction.UPDATE),
    DELETE: createPermission(PermissionResource.COMPANIES, PermissionAction.DELETE),
    EXPORT: createPermission(PermissionResource.COMPANIES, PermissionAction.EXPORT),
    MANAGE: createPermission(PermissionResource.COMPANIES, PermissionAction.MANAGE),
  },

  // Lead Permissions
  LEADS: {
    CREATE: createPermission(PermissionResource.LEADS, PermissionAction.CREATE),
    READ: createPermission(PermissionResource.LEADS, PermissionAction.READ),
    UPDATE: createPermission(PermissionResource.LEADS, PermissionAction.UPDATE),
    DELETE: createPermission(PermissionResource.LEADS, PermissionAction.DELETE),
    EXPORT: createPermission(PermissionResource.LEADS, PermissionAction.EXPORT),
    MANAGE: createPermission(PermissionResource.LEADS, PermissionAction.MANAGE),
  },

  // Opportunity Permissions
  OPPORTUNITIES: {
    CREATE: createPermission(PermissionResource.OPPORTUNITIES, PermissionAction.CREATE),
    READ: createPermission(PermissionResource.OPPORTUNITIES, PermissionAction.READ),
    UPDATE: createPermission(PermissionResource.OPPORTUNITIES, PermissionAction.UPDATE),
    DELETE: createPermission(PermissionResource.OPPORTUNITIES, PermissionAction.DELETE),
    EXPORT: createPermission(PermissionResource.OPPORTUNITIES, PermissionAction.EXPORT),
    MANAGE: createPermission(PermissionResource.OPPORTUNITIES, PermissionAction.MANAGE),
  },

  // Proposal Permissions
  PROPOSALS: {
    CREATE: createPermission(PermissionResource.PROPOSALS, PermissionAction.CREATE),
    READ: createPermission(PermissionResource.PROPOSALS, PermissionAction.READ),
    UPDATE: createPermission(PermissionResource.PROPOSALS, PermissionAction.UPDATE),
    DELETE: createPermission(PermissionResource.PROPOSALS, PermissionAction.DELETE),
    EXPORT: createPermission(PermissionResource.PROPOSALS, PermissionAction.EXPORT),
    MANAGE: createPermission(PermissionResource.PROPOSALS, PermissionAction.MANAGE),
  },

  // Project Permissions
  PROJECTS: {
    CREATE: createPermission(PermissionResource.PROJECTS, PermissionAction.CREATE),
    READ: createPermission(PermissionResource.PROJECTS, PermissionAction.READ),
    UPDATE: createPermission(PermissionResource.PROJECTS, PermissionAction.UPDATE),
    DELETE: createPermission(PermissionResource.PROJECTS, PermissionAction.DELETE),
    EXPORT: createPermission(PermissionResource.PROJECTS, PermissionAction.EXPORT),
    MANAGE: createPermission(PermissionResource.PROJECTS, PermissionAction.MANAGE),
  },

  // Handover Permissions
  HANDOVERS: {
    CREATE: createPermission(PermissionResource.HANDOVERS, PermissionAction.CREATE),
    READ: createPermission(PermissionResource.HANDOVERS, PermissionAction.READ),
    UPDATE: createPermission(PermissionResource.HANDOVERS, PermissionAction.UPDATE),
    DELETE: createPermission(PermissionResource.HANDOVERS, PermissionAction.DELETE),
    MANAGE: createPermission(PermissionResource.HANDOVERS, PermissionAction.MANAGE),
  },

  // Communication Permissions
  COMMUNICATION: {
    CREATE: createPermission(PermissionResource.COMMUNICATION, PermissionAction.CREATE),
    READ: createPermission(PermissionResource.COMMUNICATION, PermissionAction.READ),
    UPDATE: createPermission(PermissionResource.COMMUNICATION, PermissionAction.UPDATE),
    DELETE: createPermission(PermissionResource.COMMUNICATION, PermissionAction.DELETE),
    MANAGE: createPermission(PermissionResource.COMMUNICATION, PermissionAction.MANAGE),
  },

  // User Management Permissions
  USERS: {
    CREATE: createPermission(PermissionResource.USERS, PermissionAction.CREATE),
    READ: createPermission(PermissionResource.USERS, PermissionAction.READ),
    UPDATE: createPermission(PermissionResource.USERS, PermissionAction.UPDATE),
    DELETE: createPermission(PermissionResource.USERS, PermissionAction.DELETE),
    MANAGE: createPermission(PermissionResource.USERS, PermissionAction.MANAGE),
  },

  // Role Management Permissions
  ROLES: {
    CREATE: createPermission(PermissionResource.ROLES, PermissionAction.CREATE),
    READ: createPermission(PermissionResource.ROLES, PermissionAction.READ),
    UPDATE: createPermission(PermissionResource.ROLES, PermissionAction.UPDATE),
    DELETE: createPermission(PermissionResource.ROLES, PermissionAction.DELETE),
    MANAGE: createPermission(PermissionResource.ROLES, PermissionAction.MANAGE),
  },

  // Settings Permissions
  SETTINGS: {
    READ: createPermission(PermissionResource.SETTINGS, PermissionAction.READ),
    UPDATE: createPermission(PermissionResource.SETTINGS, PermissionAction.UPDATE),
    MANAGE: createPermission(PermissionResource.SETTINGS, PermissionAction.MANAGE),
  },

  // AI Features Permissions
  AI_FEATURES: {
    READ: createPermission(PermissionResource.AI_FEATURES, PermissionAction.READ),
    CREATE: createPermission(PermissionResource.AI_FEATURES, PermissionAction.CREATE),
    MANAGE: createPermission(PermissionResource.AI_FEATURES, PermissionAction.MANAGE),
  },

  // Reports Permissions
  REPORTS: {
    READ: createPermission(PermissionResource.REPORTS, PermissionAction.READ),
    CREATE: createPermission(PermissionResource.REPORTS, PermissionAction.CREATE),
    EXPORT: createPermission(PermissionResource.REPORTS, PermissionAction.EXPORT),
    MANAGE: createPermission(PermissionResource.REPORTS, PermissionAction.MANAGE),
  },

  // Call Permissions
  CALLS: {
    CREATE: createPermission(PermissionResource.CALLS, PermissionAction.CREATE),
    READ: createPermission(PermissionResource.CALLS, PermissionAction.READ),
    UPDATE: createPermission(PermissionResource.CALLS, PermissionAction.UPDATE),
    DELETE: createPermission(PermissionResource.CALLS, PermissionAction.DELETE),
    MANAGE: createPermission(PermissionResource.CALLS, PermissionAction.MANAGE),
  },
};

// Helper function to get all permissions as a flat array
const getAllPermissions = () => {
  return Object.values(CORE_PERMISSIONS).flatMap(resourcePerms => Object.values(resourcePerms));
};

// Additional specialized permissions that extend the core set
const ADDITIONAL_PERMISSIONS = [
  // Contact-specific
  { name: 'contacts.import', resource: 'contacts', action: 'import', description: 'Import contacts from files', category: 'contacts' },
  { name: 'contacts.merge', resource: 'contacts', action: 'merge', description: 'Merge duplicate contacts', category: 'contacts' },

  // Lead-specific
  { name: 'leads.convert', resource: 'leads', action: 'convert', description: 'Convert leads to opportunities', category: 'leads' },
  { name: 'leads.assign', resource: 'leads', action: 'assign', description: 'Assign leads to users', category: 'leads' },
  { name: 'leads.score', resource: 'leads', action: 'score', description: 'Access lead scoring features', category: 'leads' },
  { name: 'leads.bulk_assign', resource: 'leads', action: 'bulk_assign', description: 'Bulk assign leads to users', category: 'leads' },
  { name: 'leads.bulk_update', resource: 'leads', action: 'bulk_update', description: 'Bulk update lead properties', category: 'leads' },

  // Opportunity-specific
  { name: 'opportunities.close', resource: 'opportunities', action: 'close', description: 'Close opportunities', category: 'opportunities' },
  { name: 'opportunities.reopen', resource: 'opportunities', action: 'reopen', description: 'Reopen closed opportunities', category: 'opportunities' },
  { name: 'opportunities.forecast', resource: 'opportunities', action: 'forecast', description: 'Access sales forecasting', category: 'opportunities' },
  { name: 'opportunities.view_pipeline', resource: 'opportunities', action: 'view_pipeline', description: 'View sales pipeline', category: 'opportunities' },
  { name: 'opportunities.manage_stages', resource: 'opportunities', action: 'manage_stages', description: 'Manage opportunity stages', category: 'opportunities' },

  // Proposal-specific
  { name: 'proposals.send', resource: 'proposals', action: 'send', description: 'Send proposals to clients', category: 'proposals' },
  { name: 'proposals.approve', resource: 'proposals', action: 'approve', description: 'Approve proposals', category: 'proposals' },
  { name: 'proposals.reject', resource: 'proposals', action: 'reject', description: 'Reject proposals', category: 'proposals' },
  { name: 'proposals.version', resource: 'proposals', action: 'version', description: 'Manage proposal versions', category: 'proposals' },

  // Project-specific
  { name: 'projects.assign_members', resource: 'projects', action: 'assign_members', description: 'Assign team members to projects', category: 'projects' },
  { name: 'projects.track_time', resource: 'projects', action: 'track_time', description: 'Track time on projects', category: 'projects' },

  // User management
  { name: 'users.invite', resource: 'users', action: 'invite', description: 'Invite new users', category: 'users' },
  { name: 'users.activate', resource: 'users', action: 'activate', description: 'Activate user accounts', category: 'users' },
  { name: 'users.deactivate', resource: 'users', action: 'deactivate', description: 'Deactivate user accounts', category: 'users' },
  { name: 'users.view_all', resource: 'users', action: 'view_all', description: 'View all users in tenant', category: 'users' },
  { name: 'users.manage_roles', resource: 'users', action: 'manage_roles', description: 'Assign/revoke user roles', category: 'users' },

  // Role management
  { name: 'roles.assign', resource: 'roles', action: 'assign', description: 'Assign roles to users', category: 'roles' },
  { name: 'roles.revoke', resource: 'roles', action: 'revoke', description: 'Revoke roles from users', category: 'roles' },

  // Settings
  { name: 'settings.tenant_config', resource: 'settings', action: 'tenant_config', description: 'Configure tenant settings', category: 'settings' },
  { name: 'settings.integrations', resource: 'settings', action: 'integrations', description: 'Manage integrations', category: 'settings' },
  { name: 'settings.configure', resource: 'settings', action: 'configure', description: 'Configure system settings', category: 'settings' },
  { name: 'settings.backup', resource: 'settings', action: 'backup', description: 'Create system backups', category: 'settings' },
  { name: 'settings.restore', resource: 'settings', action: 'restore', description: 'Restore from backups', category: 'settings' },

  // AI Features
  { name: 'ai_features.lead_scoring', resource: 'ai_features', action: 'lead_scoring', description: 'Access AI lead scoring', category: 'ai_features' },
  { name: 'ai_features.content_generation', resource: 'ai_features', action: 'content_generation', description: 'Use AI content generation', category: 'ai_features' },
  { name: 'ai_features.insights', resource: 'ai_features', action: 'insights', description: 'View AI insights and recommendations', category: 'ai_features' },
  { name: 'ai_features.access', resource: 'ai_features', action: 'access', description: 'Access AI features', category: 'ai_features' },
  { name: 'ai_features.configure', resource: 'ai_features', action: 'configure', description: 'Configure AI features', category: 'ai_features' },
  { name: 'ai_features.train', resource: 'ai_features', action: 'train', description: 'Train AI models', category: 'ai_features' },

  // Reporting & Analytics
  { name: 'reports.generate', resource: 'reports', action: 'generate', description: 'Generate reports', category: 'reports' },
  { name: 'reports.schedule', resource: 'reports', action: 'schedule', description: 'Schedule automated reports', category: 'reports' },
  { name: 'reports.custom_reports', resource: 'reports', action: 'custom_reports', description: 'Create custom reports', category: 'reports' },
];

async function seedPermissions() {
  console.log('🌱 Starting comprehensive permission seeding using centralized constants...\n');

  try {
    // Get all tenants to seed permissions for each
    const tenants = await prisma.tenant.findMany();

    if (tenants.length === 0) {
      console.log('❌ No tenants found. Please run tenant seeding first.');
      return;
    }

    for (const tenant of tenants) {
      console.log(`📋 Seeding permissions for tenant: ${tenant.name}`);

      const permissions = [];

      // Add core permissions from centralized constants
      console.log('  Creating core permissions from centralized constants...');
      for (const [resourceKey, resourcePermissions] of Object.entries(CORE_PERMISSIONS)) {
        const resourceName = resourceKey.toLowerCase();
        console.log(`    Adding ${resourceName} permissions...`);

        for (const [actionKey, permissionName] of Object.entries(resourcePermissions)) {
          const [resource, action] = permissionName.split('.');
          permissions.push({
            tenantId: tenant.id,
            name: permissionName,
            resource: resource,
            action: action,
            description: `${actionKey.charAt(0).toUpperCase() + actionKey.slice(1).toLowerCase()} ${resource}`,
            category: resource
          });
        }
      }

      // Add additional specialized permissions
      console.log('  Adding additional specialized permissions...');
      for (const perm of ADDITIONAL_PERMISSIONS) {
        permissions.push({
          tenantId: tenant.id,
          ...perm
        });
      }

      // Batch create permissions
      console.log(`  Creating ${permissions.length} permissions...`);

      let createdCount = 0;
      let skippedCount = 0;

      for (const permission of permissions) {
        try {
          await prisma.permission.upsert({
            where: {
              tenantId_name: {
                tenantId: tenant.id,
                name: permission.name
              }
            },
            update: {
              resource: permission.resource,
              action: permission.action,
              description: permission.description,
              category: permission.category
            },
            create: permission
          });

          createdCount++;
        } catch (error) {
          // Skip if permission already exists or other constraint issues
          if (error.message.includes('Unique constraint') || error.code === 'P2002') {
            skippedCount++;
            console.log(`    Skipped existing permission: ${permission.name}`);
          } else {
            console.error(`    Error with permission ${permission.name}:`, error.message);
            skippedCount++;
          }
        }
      }

      console.log(`✅ Completed permissions for tenant: ${tenant.name}`);
      console.log(`    Created/Updated: ${createdCount}, Skipped: ${skippedCount}\n`);
    }

    console.log('🎉 Permission seeding completed successfully using centralized constants!');

    // Summary
    const totalPermissions = await prisma.permission.count();
    const corePermissionCount = getAllPermissions().length;
    const additionalPermissionCount = ADDITIONAL_PERMISSIONS.length;

    console.log(`📊 Summary:`);
    console.log(`   Total permissions in database: ${totalPermissions}`);
    console.log(`   Core permissions from constants: ${corePermissionCount}`);
    console.log(`   Additional specialized permissions: ${additionalPermissionCount}`);
    console.log(`   Expected per tenant: ${corePermissionCount + additionalPermissionCount}`);

  } catch (error) {
    console.error('❌ Permission seeding failed:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run seeding
seedPermissions().catch(console.error);
