'use client';

import React, { memo, useMemo, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { EmptyState } from '@/components/ui/empty-state';
import { PermissionAwareTable } from '@/components/ui/permission-aware-table';
import { PermissionGate } from '@/components/ui/permission-gate';
import { useOptimizedLeads } from '@/hooks/use-optimized-leads';
import { PermissionResource, PermissionAction } from '@/types';
import { Eye, Edit, Trash2, ClipboardList, Search, Filter, RefreshCw } from 'lucide-react';
import { toastFunctions as toast } from '@/hooks/use-toast';

// Memoized filter components to prevent unnecessary re-renders
const SearchFilter = memo(function SearchFilter({ 
  value, 
  onChange, 
  placeholder = "Search leads..." 
}: {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
}) {
  return (
    <div className="relative">
      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
      <Input
        placeholder={placeholder}
        value={value}
        onChange={(e) => onChange(e.target.value)}
        className="pl-10"
      />
    </div>
  );
});

const StatusFilter = memo(function StatusFilter({ 
  value, 
  onChange 
}: {
  value: string;
  onChange: (value: string) => void;
}) {
  return (
    <Select value={value} onValueChange={onChange}>
      <SelectTrigger>
        <SelectValue placeholder="All Statuses" />
      </SelectTrigger>
      <SelectContent>
        <SelectItem value="all">All Statuses</SelectItem>
        <SelectItem value="new">New</SelectItem>
        <SelectItem value="contacted">Contacted</SelectItem>
        <SelectItem value="qualified">Qualified</SelectItem>
        <SelectItem value="proposal">Proposal</SelectItem>
        <SelectItem value="negotiation">Negotiation</SelectItem>
        <SelectItem value="closed_won">Closed Won</SelectItem>
        <SelectItem value="closed_lost">Closed Lost</SelectItem>
      </SelectContent>
    </Select>
  );
});

const PriorityFilter = memo(function PriorityFilter({ 
  value, 
  onChange 
}: {
  value: string;
  onChange: (value: string) => void;
}) {
  return (
    <Select value={value} onValueChange={onChange}>
      <SelectTrigger>
        <SelectValue placeholder="All Priorities" />
      </SelectTrigger>
      <SelectContent>
        <SelectItem value="all">All Priorities</SelectItem>
        <SelectItem value="low">Low</SelectItem>
        <SelectItem value="medium">Medium</SelectItem>
        <SelectItem value="high">High</SelectItem>
        <SelectItem value="urgent">Urgent</SelectItem>
      </SelectContent>
    </Select>
  );
});

export const OptimizedLeadManagement = memo(function OptimizedLeadManagement() {
  const router = useRouter();
  
  const {
    leads,
    totalPages,
    loading,
    error,
    filters,
    updateFilters,
    refresh,
    clearCache,
    cacheSize,
    isCached
  } = useOptimizedLeads({
    initialFilters: {
      page: 1,
      limit: 10,
      search: '',
      status: 'all',
      priority: 'all'
    },
    enableCache: true,
    cacheTime: 5 * 60 * 1000, // 5 minutes
    staleTime: 30 * 1000, // 30 seconds
  });

  // Memoized table columns to prevent recreation
  const columns = useMemo(() => [
    {
      accessorKey: 'contact.firstName',
      header: 'Name',
      cell: ({ row }: any) => {
        const lead = row.original;
        return (
          <div>
            <div className="font-medium">
              {lead.contact?.firstName} {lead.contact?.lastName}
            </div>
            <div className="text-sm text-muted-foreground">
              {lead.contact?.email}
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: 'company.name',
      header: 'Company',
      cell: ({ row }: any) => row.original.company?.name || 'N/A',
    },
    {
      accessorKey: 'status',
      header: 'Status',
      cell: ({ row }: any) => {
        const status = row.original.status;
        return (
          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
            status === 'new' ? 'bg-blue-100 text-blue-800' :
            status === 'contacted' ? 'bg-yellow-100 text-yellow-800' :
            status === 'qualified' ? 'bg-green-100 text-green-800' :
            status === 'proposal' ? 'bg-purple-100 text-purple-800' :
            status === 'negotiation' ? 'bg-orange-100 text-orange-800' :
            status === 'closed_won' ? 'bg-green-100 text-green-800' :
            'bg-red-100 text-red-800'
          }`}>
            {status.replace('_', ' ').toUpperCase()}
          </span>
        );
      },
    },
    {
      accessorKey: 'priority',
      header: 'Priority',
      cell: ({ row }: any) => {
        const priority = row.original.priority;
        return (
          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
            priority === 'urgent' ? 'bg-red-100 text-red-800' :
            priority === 'high' ? 'bg-orange-100 text-orange-800' :
            priority === 'medium' ? 'bg-yellow-100 text-yellow-800' :
            'bg-gray-100 text-gray-800'
          }`}>
            {priority.toUpperCase()}
          </span>
        );
      },
    },
    {
      accessorKey: 'score',
      header: 'Score',
      cell: ({ row }: any) => {
        const score = row.original.score;
        return score ? `${score}%` : 'N/A';
      },
    },
    {
      accessorKey: 'createdAt',
      header: 'Created',
      cell: ({ row }: any) => {
        return new Date(row.original.createdAt).toLocaleDateString();
      },
    },
  ], []);

  // Memoized filter handlers
  const handleSearchChange = useCallback((search: string) => {
    updateFilters({ search, page: 1 });
  }, [updateFilters]);

  const handleStatusChange = useCallback((status: string) => {
    updateFilters({ status, page: 1 });
  }, [updateFilters]);

  const handlePriorityChange = useCallback((priority: string) => {
    updateFilters({ priority, page: 1 });
  }, [updateFilters]);

  const handlePageChange = useCallback((page: number) => {
    updateFilters({ page });
  }, [updateFilters]);

  // Memoized action handlers
  const handleViewLead = useCallback((lead: any) => {
    router.push(`/leads/${lead.id}`);
  }, [router]);

  const handleEditLead = useCallback((lead: any) => {
    router.push(`/leads/${lead.id}/edit`);
  }, [router]);

  const handleDeleteLead = useCallback(async (lead: any) => {
    if (!confirm('Are you sure you want to delete this lead?')) return;
    
    try {
      const response = await fetch(`/api/leads/${lead.id}`, {
        method: 'DELETE',
      });
      
      if (response.ok) {
        toast.success('Lead deleted successfully');
        refresh(); // Refresh the data
      } else {
        toast.error('Failed to delete lead');
      }
    } catch (error) {
      console.error('Error deleting lead:', error);
      toast.error('Failed to delete lead');
    }
  }, [refresh]);

  // Memoized table actions
  const tableActions = useMemo(() => [
    {
      label: 'View',
      icon: Eye,
      onClick: handleViewLead,
      permission: { resource: PermissionResource.LEADS, action: PermissionAction.READ }
    },
    {
      label: 'Edit',
      icon: Edit,
      onClick: handleEditLead,
      permission: { resource: PermissionResource.LEADS, action: PermissionAction.UPDATE }
    },
    {
      label: 'Delete',
      icon: Trash2,
      onClick: handleDeleteLead,
      permission: { resource: PermissionResource.LEADS, action: PermissionAction.DELETE },
      variant: 'destructive' as const
    }
  ], [handleViewLead, handleEditLead, handleDeleteLead]);

  if (error) {
    return (
      <Card>
        <CardContent className="p-8">
          <EmptyState
            icon={<ClipboardList className="w-12 h-12" />}
            title="Error Loading Leads"
            description={error}
            action={{
              label: 'Retry',
              onClick: refresh,
              variant: 'primary'
            }}
          />
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Filters */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
            <div className="flex flex-col sm:flex-row gap-4 flex-1">
              <div className="w-full sm:w-80">
                <SearchFilter
                  value={filters.search || ''}
                  onChange={handleSearchChange}
                />
              </div>
              <StatusFilter
                value={filters.status || 'all'}
                onChange={handleStatusChange}
              />
              <PriorityFilter
                value={filters.priority || 'all'}
                onChange={handlePriorityChange}
              />
            </div>
            
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={refresh}
                disabled={loading}
              >
                <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                Refresh
              </Button>
              
              {process.env.NODE_ENV === 'development' && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={clearCache}
                  title={`Cache: ${cacheSize} entries, Current: ${isCached ? 'Cached' : 'Fresh'}`}
                >
                  Clear Cache
                </Button>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Leads Table */}
      {leads.length === 0 && !loading ? (
        <Card>
          <CardContent>
            <EmptyState
              icon={<ClipboardList className="w-12 h-12" />}
              title="No leads found"
              description="Get started by creating your first lead to track potential sales opportunities."
              action={{
                label: 'Add Lead',
                onClick: () => router.push('/leads/new'),
                variant: 'primary'
              }}
            />
          </CardContent>
        </Card>
      ) : (
        <Card>
          <PermissionAwareTable
            data={leads}
            columns={columns}
            actions={tableActions}
            loading={loading}
            pagination={{
              currentPage: filters.page || 1,
              totalPages,
              onPageChange: handlePageChange,
            }}
          />
        </Card>
      )}
    </div>
  );
});
