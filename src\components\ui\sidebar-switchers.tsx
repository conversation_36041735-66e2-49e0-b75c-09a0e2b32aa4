'use client';

import React from 'react';
import { cn } from '@/lib/utils';
import { ThemeToggle } from '@/components/ui/theme-toggle';
import { LanguageSwitcher } from '@/components/layout/language-switcher';
import { useSidebar } from '@/components/ui/sidebar';

interface SidebarSwitchersProps {
  className?: string;
  showSeparator?: boolean;
}

export function SidebarSwitchers({
  className,
  showSeparator = false
}: SidebarSwitchersProps) {
  const { state } = useSidebar();
  const isCollapsed = state === "collapsed";

  // Hide switchers when sidebar is collapsed
  if (isCollapsed) {
    return null;
  }

  return (
    <div className={cn(
      "flex items-center gap-1",
      showSeparator && "border-t border-sidebar-border pt-2",
      className
    )}>
      <ThemeToggle variant="sidebar" />
      <LanguageSwitcher variant="sidebar" />
    </div>
  );
}

// Individual components for more granular control
export function SidebarThemeToggle({ className }: { className?: string }) {
  const { state } = useSidebar();
  const isCollapsed = state === "collapsed";

  // Hide when sidebar is collapsed
  if (isCollapsed) {
    return null;
  }

  return <ThemeToggle variant="sidebar" className={className} />;
}

export function SidebarLanguageSwitcher({ className }: { className?: string }) {
  const { state } = useSidebar();
  const isCollapsed = state === "collapsed";

  // Hide when sidebar is collapsed
  if (isCollapsed) {
    return null;
  }

  return <LanguageSwitcher variant="sidebar" className={className} />;
}
