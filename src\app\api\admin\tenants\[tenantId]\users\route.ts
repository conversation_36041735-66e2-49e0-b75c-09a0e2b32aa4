import { NextRequest, NextResponse } from 'next/server';
import { withSuperAdminAuth } from '@/lib/tenant-context';
import { prisma } from '@/lib/prisma';
import { permissionService } from '@/services/permission-service';
import { UserManagementService } from '@/services/user-management';
import { z } from 'zod';

const updateUserRoleSchema = z.object({
  userId: z.string(),
  role: z.string(),
  isTenantAdmin: z.boolean().optional()
});

const removeUserSchema = z.object({
  userId: z.string()
});

/**
 * @swagger
 * /api/admin/tenants/{tenantId}/users:
 *   get:
 *     summary: Get users in specific tenant (Super Admin only)
 *     description: Retrieve all users belonging to a specific tenant
 *     tags:
 *       - Super Admin - Tenant Users
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: tenantId
 *         required: true
 *         schema:
 *           type: string
 *         description: Tenant ID
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 20
 *         description: Number of items per page
 *     responses:
 *       200:
 *         description: Tenant users retrieved successfully
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Super Admin access required
 *       404:
 *         description: Tenant not found
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ tenantId: string }> }
) {
  return withSuperAdminAuth(async (req: NextRequest) => {
    try {
      const { tenantId } = await params;
      const { searchParams } = new URL(request.url);
      const page = parseInt(searchParams.get('page') || '1');
      const limit = parseInt(searchParams.get('limit') || '20');
      const offset = (page - 1) * limit;

      // Verify tenant exists
      const tenant = await prisma.tenant.findUnique({
        where: { id: tenantId }
      });

      if (!tenant) {
        return NextResponse.json(
          { error: 'Tenant not found' },
          { status: 404 }
        );
      }

      const [tenantUsers, total] = await Promise.all([
        prisma.tenantUser.findMany({
          where: {
            tenantId,
            user: {
              // Exclude platform admins from tenant user lists
              isPlatformAdmin: false
            }
          },
          skip: offset,
          take: limit,
          include: {
            user: true,
            tenant: true
          },
          orderBy: { joinedAt: 'desc' }
        }),
        prisma.tenantUser.count({
          where: {
            tenantId,
            user: {
              // Exclude platform admins from tenant user lists
              isPlatformAdmin: false
            }
          }
        })
      ]);

      const users = tenantUsers.map(tu => ({
        id: tu.user.id,
        email: tu.user.email,
        firstName: tu.user.firstName,
        lastName: tu.user.lastName,
        avatarUrl: tu.user.avatarUrl,
        status: tu.user.status,
        role: tu.role,
        isTenantAdmin: tu.isTenantAdmin,
        joinedAt: tu.joinedAt,
        lastLogin: tu.user.lastLogin,
        tenantStatus: tu.status
      }));

      return NextResponse.json({
        success: true,
        data: {
          users,
          pagination: {
            page,
            limit,
            total,
            totalPages: Math.ceil(total / limit)
          },
          tenant: {
            id: tenant.id,
            name: tenant.name,
            slug: tenant.slug
          }
        }
      });

    } catch (error) {
      console.error('Get tenant users error:', error);
      return NextResponse.json(
        { error: 'Internal server error' },
        { status: 500 }
      );
    }
  })(request);
}

/**
 * @swagger
 * /api/admin/tenants/{tenantId}/users:
 *   put:
 *     summary: Update user role in tenant (Super Admin only)
 *     description: Update a user's role and permissions within a specific tenant
 *     tags:
 *       - Super Admin - Tenant Users
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: tenantId
 *         required: true
 *         schema:
 *           type: string
 *         description: Tenant ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               userId:
 *                 type: string
 *               role:
 *                 type: string
 *               isTenantAdmin:
 *                 type: boolean
 *             required:
 *               - userId
 *               - role
 *     responses:
 *       200:
 *         description: User role updated successfully
 *       400:
 *         description: Validation error
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Super Admin access required
 *       404:
 *         description: Tenant or user not found
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ tenantId: string }> }
) {
  return withSuperAdminAuth(async (req: NextRequest) => {
    try {
      const { tenantId } = await params;
      const body = await request.json();
      const validatedData = updateUserRoleSchema.parse(body);

      // Verify tenant exists
      const tenant = await prisma.tenant.findUnique({
        where: { id: tenantId }
      });

      if (!tenant) {
        return NextResponse.json(
          { error: 'Tenant not found' },
          { status: 404 }
        );
      }

      // Update user role in tenant using proper service method
      const userService = new UserManagementService();
      let updatedTenantUser;

      try {
        // Use the proper updateUserRole method that updates both tenantUser.role AND userRole table
        await userService.updateUserRole(
          tenantId,
          validatedData.userId,
          validatedData.role,
          req.userId // Super admin user ID
        );

        // Also update isTenantAdmin if provided
        if (validatedData.isTenantAdmin !== undefined) {
          await prisma.tenantUser.update({
            where: {
              tenantId_userId: {
                tenantId,
                userId: validatedData.userId
              }
            },
            data: {
              isTenantAdmin: validatedData.isTenantAdmin
            }
          });
        }

        // Get updated user data for response
        updatedTenantUser = await prisma.tenantUser.findUnique({
          where: {
            tenantId_userId: {
              tenantId,
              userId: validatedData.userId
            }
          },
          include: {
            user: true
          }
        });

        if (!updatedTenantUser) {
          throw new Error('User not found after update');
        }

      } catch (error: any) {
        console.error('Error updating user role:', error);
        return NextResponse.json(
          { error: error.message || 'Failed to update user role in tenant' },
          { status: 500 }
        );
      }

      return NextResponse.json({
        success: true,
        data: {
          id: updatedTenantUser.user.id,
          email: updatedTenantUser.user.email,
          firstName: updatedTenantUser.user.firstName,
          lastName: updatedTenantUser.user.lastName,
          role: updatedTenantUser.role,
          isTenantAdmin: updatedTenantUser.isTenantAdmin,
          status: updatedTenantUser.status
        },
        message: 'User role updated successfully'
      });

    } catch (error) {
      if (error instanceof z.ZodError) {
        return NextResponse.json(
          { 
            error: 'Validation failed',
            details: error.errors.map(e => ({
              field: e.path.join('.'),
              message: e.message
            }))
          },
          { status: 400 }
        );
      }

      console.error('Update user role error:', error);
      return NextResponse.json(
        { error: 'Internal server error' },
        { status: 500 }
      );
    }
  })(request);
}

/**
 * @swagger
 * /api/admin/tenants/{tenantId}/users:
 *   delete:
 *     summary: Remove user from tenant (Super Admin only)
 *     description: Remove a user from a specific tenant
 *     tags:
 *       - Super Admin - Tenant Users
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: tenantId
 *         required: true
 *         schema:
 *           type: string
 *         description: Tenant ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               userId:
 *                 type: string
 *             required:
 *               - userId
 *     responses:
 *       200:
 *         description: User removed from tenant successfully
 *       400:
 *         description: Validation error
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Super Admin access required
 *       404:
 *         description: Tenant or user not found
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ tenantId: string }> }
) {
  return withSuperAdminAuth(async (req: NextRequest) => {
    try {
      const { tenantId } = await params;
      const body = await request.json();
      const validatedData = removeUserSchema.parse(body);

      // Verify tenant exists
      const tenant = await prisma.tenant.findUnique({
        where: { id: tenantId }
      });

      if (!tenant) {
        return NextResponse.json(
          { error: 'Tenant not found' },
          { status: 404 }
        );
      }

      // Remove user from tenant
      try {
        await prisma.tenantUser.delete({
          where: {
            tenantId_userId: {
              tenantId,
              userId: validatedData.userId
            }
          }
        });
      } catch (prismaError: any) {
        // Handle not found or foreign key constraint errors
        if (prismaError.code === 'P2025') {
          return NextResponse.json(
            { error: 'Tenant-user relationship not found' },
            { status: 404 }
          );
        }
        // Log and return generic error for other Prisma errors
        console.error('Prisma error during tenantUser.delete:', prismaError);
        return NextResponse.json(
          { error: 'Failed to remove user from tenant' },
          { status: 500 }
        );
      }

      // Invalidate user permission cache after removal
      try {
        await permissionService.invalidateUserPermissions(validatedData.userId, tenantId);
      } catch (cacheError) {
        console.error('Failed to invalidate permission cache:', cacheError);
        // Don't fail the request if cache invalidation fails, but log it
      }

      return NextResponse.json({
        success: true,
        message: 'User removed from tenant successfully'
      });

    } catch (error) {
      if (error instanceof z.ZodError) {
        return NextResponse.json(
          { 
            error: 'Validation failed',
            details: error.errors.map(e => ({
              field: e.path.join('.'),
              message: e.message
            }))
          },
          { status: 400 }
        );
      }

      console.error('Remove user from tenant error:', error);
      return NextResponse.json(
        { error: 'Internal server error' },
        { status: 500 }
      );
    }
  })(request);
}
