import { NextResponse } from 'next/server';
import { z } from 'zod';
import { withPermission, AuthenticatedRequest } from '@/lib/permission-middleware';
import { scheduledMeetingService } from '@/services/scheduled-meeting-service';
import { PermissionAction, PermissionResource } from '@/types';

const completeMeetingSchema = z.object({
  meetingNotes: z.string().optional(),
  followUpRequired: z.boolean().default(false),
  nextMeetingDate: z.string().datetime().optional(),
});

/**
 * POST /api/meetings/[meetingId]/complete
 * Mark a meeting as completed
 */
export const POST = withPermission({
  resource: PermissionResource.MEETINGS,
  action: PermissionAction.UPDATE,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest) => {
  try {
    const url = new URL(req.url);
    const pathSegments = url.pathname.split('/');
    const meetingId = pathSegments[pathSegments.indexOf('meetings') + 1];
    
    const body = await req.json();
    const validatedData = completeMeetingSchema.parse(body);

    const scheduledMeeting = await scheduledMeetingService.completeMeeting(
      meetingId,
      req.tenantId,
      validatedData.meetingNotes,
      validatedData.followUpRequired,
      validatedData.nextMeetingDate
    );

    return NextResponse.json({
      success: true,
      data: { scheduledMeeting },
      message: 'Meeting marked as completed successfully'
    });

  } catch (error) {
    console.error('Complete meeting error:', error);

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
});
