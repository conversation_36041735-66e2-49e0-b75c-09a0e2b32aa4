import { NextRequest, NextResponse } from 'next/server';
import { withSuperAdminAuth } from '@/lib/tenant-context';
import { TenantImpersonationService } from '@/services/tenant-impersonation';

/**
 * @swagger
 * /api/admin/impersonation/tenants/{tenantId}/users:
 *   get:
 *     summary: Get users available for impersonation in a tenant (Super Admin only)
 *     description: Retrieve all users in a tenant that can be impersonated
 *     tags:
 *       - Super Admin - Impersonation
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: tenantId
 *         required: true
 *         schema:
 *           type: string
 *         description: Tenant ID
 *     responses:
 *       200:
 *         description: Users retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: string
 *                       email:
 *                         type: string
 *                       firstName:
 *                         type: string
 *                       lastName:
 *                         type: string
 *                       role:
 *                         type: string
 *                       isBeingImpersonated:
 *                         type: boolean
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Super Admin access required
 *       404:
 *         description: Tenant not found
 */
export const GET = withSuperAdminAuth(async (
  request: NextRequest,
  { params }: { params: { tenantId: string } }
) => {
  try {
    const impersonationService = new TenantImpersonationService();
    const users = await impersonationService.getTenantUsersForImpersonation(params.tenantId);

    return NextResponse.json({
      success: true,
      data: users
    });

  } catch (error) {
    console.error('Get tenant users for impersonation error:', error);
    
    if (error instanceof Error && error.message.includes('not found')) {
      return NextResponse.json(
        { error: 'Tenant not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
});
