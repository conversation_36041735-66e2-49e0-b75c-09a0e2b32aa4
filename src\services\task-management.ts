import { prisma } from '@/lib/prisma';
import { z } from 'zod';
import { Decimal } from '@prisma/client/runtime/library';
import { UnifiedNotificationService } from './unified-notification-service';

// Helper function to parse date values (supports Date objects, strings in YYYY-MM-DD and ISO datetime formats)
const dateSchema = z.union([
  z.string(),
  z.date(),
  z.undefined(),
  z.null()
]).transform((val, ctx) => {
  if (!val || val === null) return undefined;

  // If it's already a Date object, return it
  if (val instanceof Date) {
    if (isNaN(val.getTime())) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Invalid date object',
      });
      return z.NEVER;
    }
    return val;
  }

  // If it's a string, try to parse it
  if (typeof val === 'string') {
    // Handle ISO datetime strings (e.g., "2024-01-15T10:30:00.000Z")
    if (val.includes('T') || val.includes('Z')) {
      const date = new Date(val);
      if (isNaN(date.getTime())) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Invalid ISO datetime string',
        });
        return z.NEVER;
      }
      return date;
    }

    // Handle YYYY-MM-DD format
    if (/^\d{4}-\d{2}-\d{2}$/.test(val)) {
      const date = new Date(`${val}T00:00:00.000Z`);
      if (isNaN(date.getTime())) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Invalid date string format',
        });
        return z.NEVER;
      }
      return date;
    }

    // Try parsing as a general date string
    const date = new Date(val);
    if (isNaN(date.getTime())) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Invalid date string',
      });
      return z.NEVER;
    }
    return date;
  }

  ctx.addIssue({
    code: z.ZodIssueCode.custom,
    message: 'Expected date or date string',
  });
  return z.NEVER;
}).optional();

// Validation schemas
export const createTaskSchema = z.object({
  title: z.string().min(1, 'Task title is required').max(255),
  description: z.string().optional(),
  status: z.enum(['todo', 'in_progress', 'review', 'done']).default('todo'),
  priority: z.enum(['low', 'medium', 'high', 'urgent']).default('medium'),
  assigneeId: z.string().optional(),
  milestoneId: z.string().optional(),
  startDate: dateSchema,
  dueDate: dateSchema,
  estimatedHours: z.number().positive().optional(),
  parentTaskId: z.string().optional(),
  dependsOnTasks: z.array(z.string()).default([]),
});

export const updateTaskSchema = createTaskSchema.partial();

export const bulkUpdateTasksSchema = z.object({
  taskIds: z.array(z.string()).min(1, 'At least one task ID is required'),
  updates: z.object({
    status: z.enum(['todo', 'in_progress', 'review', 'done']).optional(),
    priority: z.enum(['low', 'medium', 'high', 'urgent']).optional(),
    assigneeId: z.string().optional(),
    milestoneId: z.string().optional(),
    dueDate: dateSchema,
  }),
});

// Types
export type CreateTaskData = z.infer<typeof createTaskSchema>;
export type UpdateTaskData = z.infer<typeof updateTaskSchema>;
export type BulkUpdateTasksData = z.infer<typeof bulkUpdateTasksSchema>;

export interface TaskWithDetails {
  id: string;
  tenantId: string;
  projectId: string;
  title: string;
  description?: string;
  status: string;
  priority: string;
  assigneeId?: string;
  startDate?: Date;
  dueDate?: Date;
  completedAt?: Date;
  estimatedHours?: Decimal;
  actualHours?: Decimal;
  parentTaskId?: string;
  dependsOnTasks: string[];
  externalTaskId?: string;
  createdAt: Date;
  updatedAt: Date;
  
  // Relations
  project: {
    id: string;
    name: string;
    status: string;
  };
  assignee?: {
    id: string;
    firstName?: string;
    lastName?: string;
    email: string;
  };
  createdBy?: {
    id: string;
    firstName?: string;
    lastName?: string;
    email: string;
  };
  parentTask?: {
    id: string;
    title: string;
    status: string;
  };
  subTasks?: Array<{
    id: string;
    title: string;
    status: string;
    priority: string;
  }>;
}

export class TaskManagementService {
  private notificationService = new UnifiedNotificationService();

  private readonly includeOptions = {
    project: {
      select: {
        id: true,
        name: true,
        status: true,
      },
    },
    milestone: {
      select: {
        id: true,
        title: true,
        status: true,
        dueDate: true,
      },
    },
    assignee: {
      select: {
        id: true,
        firstName: true,
        lastName: true,
        email: true,
      },
    },
    createdBy: {
      select: {
        id: true,
        firstName: true,
        lastName: true,
        email: true,
      },
    },
    parentTask: {
      select: {
        id: true,
        title: true,
        status: true,
      },
    },
    subTasks: {
      select: {
        id: true,
        title: true,
        status: true,
        priority: true,
      },
      orderBy: {
        createdAt: 'asc' as const,
      },
    },
  };

  /**
   * Get all tasks for a project with pagination and filtering
   */
  async getProjectTasks(
    projectId: string,
    tenantId: string,
    options: {
      page?: number;
      limit?: number;
      status?: string;
      assigneeId?: string;
      priority?: string;
      search?: string;
      includeSubTasks?: boolean;
    } = {}
  ): Promise<{
    tasks: TaskWithDetails[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  }> {
    const {
      page = 1,
      limit = 20,
      status,
      assigneeId,
      priority,
      search,
      includeSubTasks = true,
    } = options;

    const skip = (page - 1) * limit;

    // Build where clause
    const where: any = {
      projectId,
      tenantId,
    };

    if (status) {
      where.status = status;
    }

    if (assigneeId) {
      where.assigneeId = assigneeId;
    }

    if (priority) {
      where.priority = priority;
    }

    if (search) {
      where.OR = [
        { title: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } },
      ];
    }

    // Optionally exclude sub-tasks from main list
    if (!includeSubTasks) {
      where.parentTaskId = null;
    }

    // Get total count
    const total = await prisma.projectTask.count({ where });

    // Get tasks
    const tasks = await prisma.projectTask.findMany({
      where,
      include: this.includeOptions,
      orderBy: [
        { priority: 'desc' },
        { dueDate: 'asc' },
        { createdAt: 'desc' },
      ],
      skip,
      take: limit,
    });

    const totalPages = Math.ceil(total / limit);

    return {
      tasks: tasks as TaskWithDetails[],
      total,
      page,
      limit,
      totalPages,
    };
  }

  /**
   * Get a single task by ID
   */
  async getTaskById(
    taskId: string,
    tenantId: string
  ): Promise<TaskWithDetails | null> {
    const task = await prisma.projectTask.findFirst({
      where: {
        id: taskId,
        tenantId,
      },
      include: this.includeOptions,
    });

    return task as TaskWithDetails | null;
  }

  /**
   * Create a new task
   */
  async createTask(
    projectId: string,
    tenantId: string,
    data: CreateTaskData,
    createdBy: string
  ): Promise<TaskWithDetails> {
    const validatedData = createTaskSchema.parse(data);

    return await prisma.$transaction(async (tx) => {
      // Verify project exists and belongs to tenant
      const project = await tx.project.findFirst({
        where: {
          id: projectId,
          tenantId,
        },
      });

      if (!project) {
        throw new Error('Project not found');
      }

      // Verify assignee belongs to tenant if provided
      if (validatedData.assigneeId) {
        const assignee = await tx.user.findFirst({
          where: {
            id: validatedData.assigneeId,
            tenantUsers: {
              some: { tenantId }
            }
          }
        });

        if (!assignee) {
          throw new Error('Assignee not found or not part of tenant');
        }
      }

      // Verify parent task exists and belongs to same project if provided
      if (validatedData.parentTaskId) {
        const parentTask = await tx.projectTask.findFirst({
          where: {
            id: validatedData.parentTaskId,
            projectId,
            tenantId,
          },
        });

        if (!parentTask) {
          throw new Error('Parent task not found or not in same project');
        }
      }

      // Verify dependency tasks exist and belong to same project
      if (validatedData.dependsOnTasks.length > 0) {
        const dependencyTasks = await tx.projectTask.findMany({
          where: {
            id: { in: validatedData.dependsOnTasks },
            projectId,
            tenantId,
          },
        });

        if (dependencyTasks.length !== validatedData.dependsOnTasks.length) {
          throw new Error('One or more dependency tasks not found or not in same project');
        }
      }

      // Verify milestone exists and belongs to same project if provided
      if (validatedData.milestoneId) {
        const milestone = await tx.projectMilestone.findFirst({
          where: {
            id: validatedData.milestoneId,
            projectId,
            tenantId,
          },
        });

        if (!milestone) {
          throw new Error('Milestone not found or not in same project');
        }
      }

      // Create the task
      const task = await tx.projectTask.create({
        data: {
          tenantId,
          projectId,
          title: validatedData.title,
          description: validatedData.description,
          status: validatedData.status,
          priority: validatedData.priority,
          assigneeId: validatedData.assigneeId,
          milestoneId: validatedData.milestoneId,
          startDate: validatedData.startDate,
          dueDate: validatedData.dueDate,
          estimatedHours: validatedData.estimatedHours,
          parentTaskId: validatedData.parentTaskId,
          dependsOnTasks: validatedData.dependsOnTasks,
          createdById: createdBy,
        },
        include: this.includeOptions,
      });

      // Trigger task creation notification
      await this.triggerTaskCreationNotification(task as TaskWithDetails, tenantId);

      return task as TaskWithDetails;
    });
  }

  /**
   * Update a task
   */
  async updateTask(
    taskId: string,
    tenantId: string,
    data: UpdateTaskData,
    updatedBy: string
  ): Promise<TaskWithDetails> {
    const validatedData = updateTaskSchema.parse(data);

    return await prisma.$transaction(async (tx) => {
      // Verify task exists and belongs to tenant
      const existingTask = await tx.projectTask.findFirst({
        where: {
          id: taskId,
          tenantId,
        },
      });

      if (!existingTask) {
        throw new Error('Task not found');
      }

      // Verify assignee belongs to tenant if provided
      if (validatedData.assigneeId) {
        const assignee = await tx.user.findFirst({
          where: {
            id: validatedData.assigneeId,
            tenantUsers: {
              some: { tenantId }
            }
          }
        });

        if (!assignee) {
          throw new Error('Assignee not found or not part of tenant');
        }
      }

      // Handle status change to 'done'
      const updateData: any = {
        ...validatedData,
        updatedAt: new Date(),
      };

      if (validatedData.status === 'done' && existingTask.status !== 'done') {
        updateData.completedAt = new Date();
      } else if (validatedData.status !== 'done' && existingTask.status === 'done') {
        updateData.completedAt = null;
      }

      // Update the task
      const task = await tx.projectTask.update({
        where: {
          id: taskId,
        },
        data: updateData,
        include: this.includeOptions,
      });

      // Update project progress if task status changed
      if (validatedData.status && validatedData.status !== existingTask.status) {
        await this.updateProjectProgressFromTasks(existingTask.projectId, tenantId, tx);
      }

      // Trigger task update notifications
      await this.handleTaskUpdateNotifications(
        task as TaskWithDetails,
        existingTask,
        tenantId
      );

      return task as TaskWithDetails;
    });
  }

  /**
   * Delete a task
   */
  async deleteTask(taskId: string, tenantId: string): Promise<void> {
    await prisma.$transaction(async (tx) => {
      // Verify task exists and belongs to tenant
      const task = await tx.projectTask.findFirst({
        where: {
          id: taskId,
          tenantId,
        },
      });

      if (!task) {
        throw new Error('Task not found');
      }

      // Check if task has sub-tasks
      const subTasks = await tx.projectTask.findMany({
        where: {
          parentTaskId: taskId,
          tenantId,
        },
      });

      if (subTasks.length > 0) {
        throw new Error('Cannot delete task with sub-tasks. Delete sub-tasks first.');
      }

      // Delete the task
      await tx.projectTask.delete({
        where: {
          id: taskId,
        },
      });

      // Update project progress
      await this.updateProjectProgressFromTasks(task.projectId, tenantId, tx);
    });
  }

  /**
   * Update project progress based on task completion
   */
  private async updateProjectProgressFromTasks(
    projectId: string,
    tenantId: string,
    tx: any
  ): Promise<void> {
    // Get all tasks for the project
    const tasks = await tx.projectTask.findMany({
      where: {
        projectId,
        tenantId,
      },
      select: {
        status: true,
      },
    });

    if (tasks.length === 0) {
      return; // No tasks, keep current progress
    }

    // Calculate progress based on completed tasks
    const completedTasks = tasks.filter(task => task.status === 'done').length;
    const progress = (completedTasks / tasks.length) * 100;

    // Update project progress
    await tx.project.update({
      where: {
        id: projectId,
      },
      data: {
        progress: new Decimal(progress),
      },
    });
  }

  /**
   * Trigger task creation notification
   */
  private async triggerTaskCreationNotification(
    task: TaskWithDetails,
    tenantId: string
  ): Promise<void> {
    try {
      const targetUsers = [];

      // Notify task assignee if assigned
      if (task.assigneeId) {
        targetUsers.push(task.assigneeId);
      }

      // Notify project manager
      // TODO: Add logic to get project manager from project

      // Create notification for each target user
      for (const userId of targetUsers) {
        await this.notificationService.createNotification({
          tenantId,
          userId,
          type: 'task_assigned',
          category: 'general',
          title: 'New Task Assigned',
          message: `Task "${task.title}" has been assigned to you`,
          data: {
            taskId: task.id,
            taskTitle: task.title,
            taskStatus: task.status,
            taskPriority: task.priority,
            projectId: task.projectId,
            projectName: task.project.name,
            dueDate: task.dueDate?.toISOString(),
            estimatedHours: task.estimatedHours?.toString(),
            parentTaskId: task.parentTaskId
          },
          actionUrl: `/projects/${task.projectId}/tasks/${task.id}`,
          actionLabel: 'View Task'
        });
      }
    } catch (error) {
      console.error('Failed to send task creation notification:', error);
      // Don't throw error to avoid breaking the main operation
    }
  }

  /**
   * Handle notifications for task updates
   */
  private async handleTaskUpdateNotifications(
    updatedTask: TaskWithDetails,
    previousTask: any,
    tenantId: string
  ): Promise<void> {
    try {
      // Check for assignee change
      if (updatedTask.assigneeId !== previousTask.assigneeId) {
        await this.triggerTaskAssignmentNotification(
          updatedTask,
          tenantId,
          previousTask.assigneeId
        );
      }

      // Check for status change
      if (updatedTask.status !== previousTask.status) {
        await this.triggerTaskStatusChangeNotification(
          updatedTask,
          tenantId,
          previousTask.status
        );
      }

      // Check for priority change to urgent
      if (updatedTask.priority === 'urgent' && previousTask.priority !== 'urgent') {
        await this.triggerUrgentTaskNotification(updatedTask, tenantId);
      }

      // Check for overdue tasks
      if (updatedTask.dueDate && new Date() > updatedTask.dueDate && updatedTask.status !== 'done') {
        await this.triggerOverdueTaskNotification(updatedTask, tenantId);
      }
    } catch (error) {
      console.error('Failed to handle task update notifications:', error);
    }
  }

  /**
   * Trigger task assignment notification
   */
  private async triggerTaskAssignmentNotification(
    task: TaskWithDetails,
    tenantId: string,
    previousAssigneeId?: string | null
  ): Promise<void> {
    try {
      // Notify new assignee
      if (task.assigneeId) {
        await this.notificationService.createNotification({
          tenantId,
          userId: task.assigneeId,
          type: 'task_assigned',
          category: 'general',
          title: 'Task Assigned to You',
          message: `Task "${task.title}" has been assigned to you`,
          data: {
            taskId: task.id,
            taskTitle: task.title,
            taskStatus: task.status,
            taskPriority: task.priority,
            projectId: task.projectId,
            projectName: task.project.name,
            dueDate: task.dueDate?.toISOString(),
            previousAssignee: previousAssigneeId
          },
          actionUrl: `/projects/${task.projectId}/tasks/${task.id}`,
          actionLabel: 'View Task'
        });
      }

      // Notify previous assignee if different
      if (previousAssigneeId && previousAssigneeId !== task.assigneeId) {
        await this.notificationService.createNotification({
          tenantId,
          userId: previousAssigneeId,
          type: 'task_reassigned',
          category: 'general',
          title: 'Task Reassigned',
          message: `Task "${task.title}" has been reassigned to another team member`,
          data: {
            taskId: task.id,
            taskTitle: task.title,
            newAssignee: task.assignee ? `${task.assignee.firstName} ${task.assignee.lastName}` : 'Unknown'
          },
          actionUrl: `/projects/${task.projectId}/tasks/${task.id}`,
          actionLabel: 'View Task'
        });
      }
    } catch (error) {
      console.error('Failed to send task assignment notification:', error);
    }
  }

  /**
   * Trigger task status change notification
   */
  private async triggerTaskStatusChangeNotification(
    task: TaskWithDetails,
    tenantId: string,
    previousStatus: string
  ): Promise<void> {
    try {
      const targetUsers = [];

      // Notify task assignee
      if (task.assigneeId) {
        targetUsers.push(task.assigneeId);
      }

      // Notify project manager
      // TODO: Add logic to get project manager

      // Determine notification type based on status change
      let notificationType = 'task_status_changed';
      let title = 'Task Status Updated';
      let message = `Task "${task.title}" status changed from ${previousStatus} to ${task.status}`;

      if (task.status === 'done') {
        notificationType = 'task_completed';
        title = 'Task Completed! ✅';
        message = `Task "${task.title}" has been completed`;
      } else if (task.status === 'in_progress' && previousStatus === 'todo') {
        notificationType = 'task_started';
        title = 'Task Started';
        message = `Work has started on task "${task.title}"`;
      }

      for (const userId of targetUsers) {
        await this.notificationService.createNotification({
          tenantId,
          userId,
          type: notificationType,
          category: 'general',
          title,
          message,
          data: {
            taskId: task.id,
            taskTitle: task.title,
            previousStatus,
            newStatus: task.status,
            taskPriority: task.priority,
            projectId: task.projectId,
            projectName: task.project.name,
            completedAt: task.completedAt?.toISOString()
          },
          actionUrl: `/projects/${task.projectId}/tasks/${task.id}`,
          actionLabel: 'View Task'
        });
      }
    } catch (error) {
      console.error('Failed to send task status change notification:', error);
    }
  }

  /**
   * Trigger urgent task notification
   */
  private async triggerUrgentTaskNotification(
    task: TaskWithDetails,
    tenantId: string
  ): Promise<void> {
    try {
      const targetUsers = [];
      if (task.assigneeId) {
        targetUsers.push(task.assigneeId);
      }

      // Notify project manager and team leads
      // TODO: Add logic to get project manager and team leads

      for (const userId of targetUsers) {
        await this.notificationService.createNotification({
          tenantId,
          userId,
          type: 'urgent_task_alert',
          category: 'general',
          title: 'Urgent Task Alert',
          message: `Urgent task "${task.title}" requires immediate attention`,
          data: {
            taskId: task.id,
            taskTitle: task.title,
            taskPriority: task.priority,
            taskStatus: task.status,
            projectId: task.projectId,
            projectName: task.project.name,
            dueDate: task.dueDate?.toISOString()
          },
          actionUrl: `/projects/${task.projectId}/tasks/${task.id}`,
          actionLabel: 'View Task'
        });
      }
    } catch (error) {
      console.error('Failed to send urgent task notification:', error);
    }
  }

  /**
   * Trigger overdue task notification
   */
  private async triggerOverdueTaskNotification(
    task: TaskWithDetails,
    tenantId: string
  ): Promise<void> {
    try {
      const targetUsers = [];
      if (task.assigneeId) {
        targetUsers.push(task.assigneeId);
      }

      // Notify project manager
      // TODO: Add logic to get project manager

      for (const userId of targetUsers) {
        await this.notificationService.createNotification({
          tenantId,
          userId,
          type: 'task_overdue',
          category: 'general',
          title: 'Task Overdue ⚠️',
          message: `Task "${task.title}" is overdue and needs attention`,
          data: {
            taskId: task.id,
            taskTitle: task.title,
            taskPriority: task.priority,
            taskStatus: task.status,
            projectId: task.projectId,
            projectName: task.project.name,
            dueDate: task.dueDate?.toISOString(),
            daysPastDue: Math.floor((new Date().getTime() - task.dueDate!.getTime()) / (1000 * 60 * 60 * 24))
          },
          actionUrl: `/projects/${task.projectId}/tasks/${task.id}`,
          actionLabel: 'View Task'
        });
      }
    } catch (error) {
      console.error('Failed to send overdue task notification:', error);
    }
  }
}
