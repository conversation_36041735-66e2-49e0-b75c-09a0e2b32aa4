import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { hasPermission } from '@/lib/permissions';
import { PermissionResource, PermissionAction } from '@/types';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';

// Validation schema for updates
const updateContentSectionSchema = z.object({
  title: z.string().min(1, 'Title is required').max(255).optional(),
  description: z.string().optional(),
  promptEn: z.string().min(1, 'English prompt is required').optional(),
  promptAr: z.string().optional(),
  systemPromptEn: z.string().min(1, 'English system prompt is required').optional(),
  systemPromptAr: z.string().optional(),
  orderIndex: z.number().min(0).optional(),
  isActive: z.boolean().optional(),
  isDefault: z.boolean().optional()
});

/**
 * GET /api/proposal-content-sections/[id]
 * Get a specific proposal content section
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user's current tenant
    const currentTenant = session.user.tenants?.[0];
    if (!currentTenant) {
      return NextResponse.json({ error: 'No tenant access' }, { status: 403 });
    }

    // Check permission
    const hasAccess = await hasPermission(
      PermissionResource.PROPOSALS,
      PermissionAction.READ,
      currentTenant.id
    );

    if (!hasAccess) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const contentSectionId = params.id;

    // Get content section
    const contentSection = await prisma.proposalContentSection.findFirst({
      where: {
        id: contentSectionId,
        tenantId: currentTenant.id
      },
      include: {
        createdBy: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true
          }
        }
      }
    });

    if (!contentSection) {
      return NextResponse.json(
        { error: 'Content section not found or access denied' },
        { status: 404 }
      );
    }

    return NextResponse.json(contentSection);

  } catch (error) {
    console.error('Error fetching proposal content section:', error);
    return NextResponse.json(
      { error: 'Failed to fetch proposal content section' },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/proposal-content-sections/[id]
 * Update a proposal content section
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user's current tenant
    const currentTenant = session.user.tenants?.[0];
    if (!currentTenant) {
      return NextResponse.json({ error: 'No tenant access' }, { status: 403 });
    }

    // Check permission
    const hasAccess = await hasPermission(
      PermissionResource.PROPOSALS,
      PermissionAction.UPDATE,
      currentTenant.id
    );

    if (!hasAccess) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const contentSectionId = params.id;

    // Verify content section exists and belongs to tenant
    const existingSection = await prisma.proposalContentSection.findFirst({
      where: {
        id: contentSectionId,
        tenantId: currentTenant.id
      }
    });

    if (!existingSection) {
      return NextResponse.json(
        { error: 'Content section not found or access denied' },
        { status: 404 }
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const validatedData = updateContentSectionSchema.parse(body);

    // Update content section
    const updatedSection = await prisma.proposalContentSection.update({
      where: { id: contentSectionId },
      data: {
        ...validatedData,
        updatedAt: new Date()
      },
      include: {
        createdBy: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true
          }
        }
      }
    });

    return NextResponse.json(updatedSection);

  } catch (error) {
    console.error('Error updating proposal content section:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to update proposal content section' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/proposal-content-sections/[id]
 * Delete a proposal content section
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user's current tenant
    const currentTenant = session.user.tenants?.[0];
    if (!currentTenant) {
      return NextResponse.json({ error: 'No tenant access' }, { status: 403 });
    }

    // Check permission
    const hasAccess = await hasPermission(
      PermissionResource.PROPOSALS,
      PermissionAction.DELETE,
      currentTenant.id
    );

    if (!hasAccess) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const contentSectionId = params.id;

    // Verify content section exists and belongs to tenant
    const existingSection = await prisma.proposalContentSection.findFirst({
      where: {
        id: contentSectionId,
        tenantId: currentTenant.id
      }
    });

    if (!existingSection) {
      return NextResponse.json(
        { error: 'Content section not found or access denied' },
        { status: 404 }
      );
    }

    // Prevent deletion of default sections
    if (existingSection.isDefault) {
      return NextResponse.json(
        { error: 'Cannot delete default content sections' },
        { status: 400 }
      );
    }

    // Delete content section
    await prisma.proposalContentSection.delete({
      where: { id: contentSectionId }
    });

    return NextResponse.json({ message: 'Content section deleted successfully' });

  } catch (error) {
    console.error('Error deleting proposal content section:', error);
    return NextResponse.json(
      { error: 'Failed to delete proposal content section' },
      { status: 500 }
    );
  }
}
