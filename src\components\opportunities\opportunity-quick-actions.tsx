'use client';

import React from 'react';
import { Menu } from '@headlessui/react';
import {
  ChevronDownIcon,
  PlusIcon,
  LightBulbIcon,
  PencilIcon,
  TrashIcon,
  ChartBarIcon
} from '@heroicons/react/24/outline';
import { Wand2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { PermissionGate } from '@/components/ui/permission-gate';
import { PermissionResource, PermissionAction } from '@/types';

interface OpportunityQuickActionsProps {
  opportunityId: string;
  onAIInsightsClick?: () => void;
  onCreateProposalClick?: () => void;
  onViewPipelineClick?: () => void;
  onEditClick?: () => void;
  onDeleteClick?: () => void;
}

export function OpportunityQuickActions({
  opportunityId,
  onAIInsightsClick,
  onCreateProposalClick,
  onViewPipelineClick,
  onEditClick,
  onDeleteClick
}: OpportunityQuickActionsProps) {
  const quickActions = [
    {
      name: 'AI Insights',
      description: 'View AI-powered opportunity insights',
      icon: LightBulbIcon,
      color: 'text-blue-600 dark:text-blue-400',
      bgColor: 'bg-blue-50 dark:bg-blue-900/20',
      onClick: onAIInsightsClick,
    },
    {
      name: 'Create AI Proposal',
      description: 'Generate AI-powered proposal',
      icon: Wand2,
      color: 'text-purple-600 dark:text-purple-400',
      bgColor: 'bg-purple-50 dark:bg-purple-900/20',
      onClick: onCreateProposalClick,
      permission: { resource: PermissionResource.PROPOSALS, action: PermissionAction.CREATE },
    },
    {
      name: 'View Pipeline',
      description: 'View opportunities pipeline',
      icon: ChartBarIcon,
      color: 'text-green-600 dark:text-green-400',
      bgColor: 'bg-green-50 dark:bg-green-900/20',
      onClick: onViewPipelineClick,
    },
    {
      name: 'Edit Opportunity',
      description: 'Edit opportunity information',
      icon: PencilIcon,
      color: 'text-indigo-600 dark:text-indigo-400',
      bgColor: 'bg-indigo-50 dark:bg-indigo-900/20',
      onClick: onEditClick,
      permission: { resource: PermissionResource.OPPORTUNITIES, action: PermissionAction.UPDATE },
    },
    {
      name: 'Delete Opportunity',
      description: 'Delete this opportunity',
      icon: TrashIcon,
      color: 'text-red-600 dark:text-red-400',
      bgColor: 'bg-red-50 dark:bg-red-900/20',
      onClick: onDeleteClick,
      permission: { resource: PermissionResource.OPPORTUNITIES, action: PermissionAction.DELETE },
    },
  ];

  return (
    <Menu as="div" className="relative inline-block text-left">
      <div>
        <Menu.Button as={Button} variant="outline" size="sm" className="flex items-center gap-1">
          <PlusIcon className="h-4 w-4" aria-hidden="true" />
          Quick Actions
          <ChevronDownIcon className="h-4 w-4" aria-hidden="true" />
        </Menu.Button>
      </div>

      <Menu.Items className="absolute right-0 z-10 mt-2 w-80 origin-top-right rounded-md bg-white dark:bg-gray-800 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
        <div className="py-1">
          {quickActions.map((action) => {
            const ActionButton = (
              <Menu.Item key={action.name}>
                {({ active }) => (
                  <button
                    onClick={action.onClick}
                    className={cn(
                      active
                        ? 'bg-gray-100 dark:bg-gray-700'
                        : 'bg-white dark:bg-gray-800',
                      'group flex w-full items-center px-4 py-3 text-sm transition-colors'
                    )}
                  >
                    <div className={cn(
                      'flex h-10 w-10 flex-shrink-0 items-center justify-center rounded-lg mr-3',
                      action.bgColor
                    )}>
                      <action.icon className={cn('h-5 w-5', action.color)} aria-hidden="true" />
                    </div>
                    <div className="flex-1 text-left">
                      <p className="font-medium text-gray-900 dark:text-white">
                        {action.name}
                      </p>
                      <p className="text-gray-500 dark:text-gray-400">
                        {action.description}
                      </p>
                    </div>
                  </button>
                )}
              </Menu.Item>
            );

            // Wrap actions that require permissions with PermissionGate
            if (action.permission) {
              return (
                <PermissionGate
                  key={action.name}
                  resource={action.permission.resource}
                  action={action.permission.action}
                >
                  {ActionButton}
                </PermissionGate>
              );
            }

            return ActionButton;
          })}
        </div>
      </Menu.Items>
    </Menu>
  );
}
