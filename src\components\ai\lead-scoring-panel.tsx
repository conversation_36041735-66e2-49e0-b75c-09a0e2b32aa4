'use client';

import React, { useState } from 'react';
import { usePermissions } from '@/components/providers/permission-provider';
import { PermissionGate } from '@/components/ui/permission-gate';
import { PermissionAction, PermissionResource } from '@/types';
import { useLocale, useTranslation } from '@/components/providers/locale-provider';
import { 
  SparklesIcon,
  ChartBarIcon,
  UserIcon,
  BoltIcon,
  HeartIcon,
  CheckCircleIcon,
  XCircleIcon,
  ClockIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';

interface LeadScore {
  overall: number;
  demographic: number;
  behavioral: number;
  engagement: number;
  intent: number;
  fit: number;
  reasoning: string;
  recommendations: string[];
  nextActions: string[];
  confidence: number;
}

interface LeadQualification {
  budget: 'qualified' | 'unqualified' | 'unknown';
  authority: 'qualified' | 'unqualified' | 'unknown';
  need: 'qualified' | 'unqualified' | 'unknown';
  timeline: 'qualified' | 'unqualified' | 'unknown';
  overall: 'qualified' | 'unqualified' | 'unknown';
  reasoning: string;
  questions: string[];
}

interface LeadScoringPanelProps {
  leadId: string;
  leadData: any;
  onScoreUpdate?: (score: LeadScore) => void;
}

export function LeadScoringPanel({ leadId, leadData, onScoreUpdate }: LeadScoringPanelProps) {
  const [loading, setLoading] = useState(false);
  const [fetchingData, setFetchingData] = useState(false);
  const [score, setScore] = useState<LeadScore | null>(null);
  const [qualification, setQualification] = useState<LeadQualification | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [includeQualification, setIncludeQualification] = useState(true);

  const { hasPermission } = usePermissions();
  const { isRTL } = useLocale();
  const { t } = useTranslation();

  const scoreLeadWithAI = async () => {
    setLoading(true);
    setFetchingData(true);
    setError(null);

    try {
      // First, fetch comprehensive lead data including activities, emails, and calls
      let leadDataToAnalyze = leadData;

      try {
        const comprehensiveDataResponse = await fetch(`/api/leads/${leadId}/comprehensive`);

        if (comprehensiveDataResponse.ok) {
          const comprehensiveData = await comprehensiveDataResponse.json();

          if (comprehensiveData.success) {
            leadDataToAnalyze = comprehensiveData.data.lead;
            console.log('Using comprehensive lead data for AI analysis');
          } else {
            console.warn('Failed to fetch comprehensive data, using basic lead data:', comprehensiveData.error);
          }
        } else {
          console.warn('Comprehensive data endpoint failed, using basic lead data');
        }
      } catch (comprehensiveError) {
        console.warn('Error fetching comprehensive data, falling back to basic lead data:', comprehensiveError);
      }

      setFetchingData(false);

      // Now send the lead data (comprehensive or basic) to AI for scoring
      const response = await fetch('/api/ai/lead-scoring', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          leadData: leadDataToAnalyze,
          includeQualification
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to score lead');
      }

      const result = await response.json();
      setScore(result.data.score);

      if (result.data.qualification) {
        setQualification(result.data.qualification);
      }

      if (onScoreUpdate) {
        onScoreUpdate(result.data.score);
      }

    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
      setFetchingData(false);
    }
  };

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600 bg-green-50';
    if (score >= 60) return 'text-yellow-600 bg-yellow-50';
    if (score >= 40) return 'text-orange-600 bg-orange-50';
    return 'text-red-600 bg-red-50';
  };

  const getScoreIcon = (score: number) => {
    if (score >= 80) return <CheckCircleIcon className="w-5 h-5 text-green-600" />;
    if (score >= 60) return <ClockIcon className="w-5 h-5 text-yellow-600" />;
    if (score >= 40) return <ExclamationTriangleIcon className="w-5 h-5 text-orange-600" />;
    return <XCircleIcon className="w-5 h-5 text-red-600" />;
  };

  const getQualificationIcon = (status: string) => {
    switch (status) {
      case 'qualified':
        return <CheckCircleIcon className="w-4 h-4 text-green-600" />;
      case 'unqualified':
        return <XCircleIcon className="w-4 h-4 text-red-600" />;
      default:
        return <ClockIcon className="w-4 h-4 text-gray-400" />;
    }
  };

  const getQualificationColor = (status: string) => {
    switch (status) {
      case 'qualified':
        return 'text-green-700 bg-green-50 border-green-200';
      case 'unqualified':
        return 'text-red-700 bg-red-50 border-red-200';
      default:
        return 'text-gray-700 bg-gray-50 border-gray-200';
    }
  };

  return (
    <PermissionGate
      resource={PermissionResource.LEADS}
      action={PermissionAction.READ}
      fallback={
        <div className="text-center py-4">
          <ExclamationTriangleIcon className="w-8 h-8 text-gray-400 mx-auto mb-2" />
          <p className="text-gray-500 text-sm">{t('common.noPermission')}</p>
        </div>
      }
    >
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-3">
            <SparklesIcon className="w-6 h-6 text-blue-600" />
            <div>
              <h3 className="text-lg font-semibold text-gray-900">
                {t('ai.leadScoring.title')}
              </h3>
              <p className="text-sm text-gray-500">
                {t('ai.leadScoring.description')}
              </p>
            </div>
          </div>

          <div className="flex items-center space-x-3">
            <label className="flex items-center space-x-2 text-sm">
              <input
                type="checkbox"
                checked={includeQualification}
                onChange={(e) => setIncludeQualification(e.target.checked)}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <span>{t('ai.leadScoring.includeQualification')}</span>
            </label>

            <button
              onClick={scoreLeadWithAI}
              disabled={loading || fetchingData}
              className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center space-x-2"
            >
              {loading || fetchingData ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  <span>
                    {fetchingData
                      ? t('ai.leadScoring.fetchingData') || 'Gathering data...'
                      : t('ai.leadScoring.analyzing')
                    }
                  </span>
                </>
              ) : (
                <>
                  <BoltIcon className="w-4 h-4" />
                  <span>{t('ai.leadScoring.analyze')}</span>
                </>
              )}
            </button>
          </div>
        </div>

        {/* Error Display */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-md p-4 mb-6">
            <div className="flex">
              <XCircleIcon className="w-5 h-5 text-red-400" />
              <div className="ml-3">
                <p className="text-sm text-red-800">{error}</p>
              </div>
            </div>
          </div>
        )}

        {/* Lead Score Display */}
        {score && (
          <div className="space-y-6">
            {/* Overall Score */}
            <div className="text-center">
              <div className={`inline-flex items-center px-6 py-3 rounded-full ${getScoreColor(score.overall)}`}>
                {getScoreIcon(score.overall)}
                <span className="ml-2 text-2xl font-bold">{score.overall}</span>
                <span className="ml-1 text-sm">/100</span>
              </div>
              <p className="text-sm text-gray-500 mt-2">
                {t('ai.leadScoring.overallScore')} • {t('ai.leadScoring.confidence')}: {Math.round(score.confidence * 100)}%
              </p>
            </div>

            {/* Score Breakdown - Vertical Layout */}
            <div className="space-y-4">
              <h4 className="font-medium text-gray-900 mb-4">{t('ai.leadScoring.scoreBreakdown') || 'Score Breakdown'}</h4>

              <div className="bg-gray-50 rounded-lg p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <UserIcon className="w-5 h-5 text-blue-500" />
                    <span className="text-sm font-medium text-gray-700">{t('ai.leadScoring.demographic')}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="text-lg font-bold text-gray-900">{score.demographic}</div>
                    <div className="text-sm text-gray-500">/100</div>
                  </div>
                </div>
              </div>

              <div className="bg-gray-50 rounded-lg p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <ChartBarIcon className="w-5 h-5 text-green-500" />
                    <span className="text-sm font-medium text-gray-700">{t('ai.leadScoring.behavioral')}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="text-lg font-bold text-gray-900">{score.behavioral}</div>
                    <div className="text-sm text-gray-500">/100</div>
                  </div>
                </div>
              </div>

              <div className="bg-gray-50 rounded-lg p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <HeartIcon className="w-5 h-5 text-red-500" />
                    <span className="text-sm font-medium text-gray-700">{t('ai.leadScoring.engagement')}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="text-lg font-bold text-gray-900">{score.engagement}</div>
                    <div className="text-sm text-gray-500">/100</div>
                  </div>
                </div>
              </div>

              <div className="bg-gray-50 rounded-lg p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <BoltIcon className="w-5 h-5 text-yellow-500" />
                    <span className="text-sm font-medium text-gray-700">{t('ai.leadScoring.intent')}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="text-lg font-bold text-gray-900">{score.intent}</div>
                    <div className="text-sm text-gray-500">/100</div>
                  </div>
                </div>
              </div>

              <div className="bg-gray-50 rounded-lg p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <CheckCircleIcon className="w-5 h-5 text-purple-500" />
                    <span className="text-sm font-medium text-gray-700">{t('ai.leadScoring.fit')}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="text-lg font-bold text-gray-900">{score.fit}</div>
                    <div className="text-sm text-gray-500">/100</div>
                  </div>
                </div>
              </div>
            </div>

            {/* Reasoning */}
            <div className="bg-gray-50 rounded-lg p-4">
              <h4 className="font-medium text-gray-900 mb-2">{t('ai.leadScoring.reasoning')}</h4>
              <p className="text-sm text-gray-700">{score.reasoning}</p>
            </div>

            {/* Recommendations */}
            {score.recommendations.length > 0 && (
              <div>
                <h4 className="font-medium text-gray-900 mb-3">{t('ai.leadScoring.recommendations')}</h4>
                <ul className="space-y-2">
                  {score.recommendations.map((recommendation, index) => (
                    <li key={index} className="flex items-start space-x-2">
                      <CheckCircleIcon className="w-4 h-4 text-green-600 mt-0.5 flex-shrink-0" />
                      <span className="text-sm text-gray-700">{recommendation}</span>
                    </li>
                  ))}
                </ul>
              </div>
            )}

            {/* Next Actions */}
            {score.nextActions.length > 0 && (
              <div>
                <h4 className="font-medium text-gray-900 mb-3">{t('ai.leadScoring.nextActions')}</h4>
                <ul className="space-y-2">
                  {score.nextActions.map((action, index) => (
                    <li key={index} className="flex items-start space-x-2">
                      <BoltIcon className="w-4 h-4 text-blue-600 mt-0.5 flex-shrink-0" />
                      <span className="text-sm text-gray-700">{action}</span>
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        )}

        {/* BANT Qualification */}
        {qualification && (
          <div className="mt-8 pt-6 border-t border-gray-200">
            <h4 className="font-medium text-gray-900 mb-4">{t('ai.leadScoring.bantQualification')}</h4>
            
            <div className="space-y-3 mb-4">
              <div className={`p-4 rounded-lg border ${getQualificationColor(qualification.budget)}`}>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    {getQualificationIcon(qualification.budget)}
                    <span className="text-sm font-medium">{t('ai.leadScoring.budget')}</span>
                  </div>
                  <div className="text-sm font-semibold capitalize">{qualification.budget}</div>
                </div>
              </div>

              <div className={`p-4 rounded-lg border ${getQualificationColor(qualification.authority)}`}>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    {getQualificationIcon(qualification.authority)}
                    <span className="text-sm font-medium">{t('ai.leadScoring.authority')}</span>
                  </div>
                  <div className="text-sm font-semibold capitalize">{qualification.authority}</div>
                </div>
              </div>

              <div className={`p-4 rounded-lg border ${getQualificationColor(qualification.need)}`}>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    {getQualificationIcon(qualification.need)}
                    <span className="text-sm font-medium">{t('ai.leadScoring.need')}</span>
                  </div>
                  <div className="text-sm font-semibold capitalize">{qualification.need}</div>
                </div>
              </div>

              <div className={`p-4 rounded-lg border ${getQualificationColor(qualification.timeline)}`}>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    {getQualificationIcon(qualification.timeline)}
                    <span className="text-sm font-medium">{t('ai.leadScoring.timeline')}</span>
                  </div>
                  <div className="text-sm font-semibold capitalize">{qualification.timeline}</div>
                </div>
              </div>
            </div>

            <div className="bg-blue-50 rounded-lg p-4">
              <h5 className="font-medium text-blue-900 mb-2">{t('ai.leadScoring.qualificationReasoning')}</h5>
              <p className="text-sm text-blue-800">{qualification.reasoning}</p>
            </div>

            {qualification.questions.length > 0 && (
              <div className="mt-4">
                <h5 className="font-medium text-gray-900 mb-2">{t('ai.leadScoring.suggestedQuestions')}</h5>
                <ul className="space-y-1">
                  {qualification.questions.map((question, index) => (
                    <li key={index} className="text-sm text-gray-700">
                      • {question}
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        )}
      </div>
    </PermissionGate>
  );
}
