import { NextResponse } from 'next/server';
import { z } from 'zod';
import { CompanyManagementService, createCompanySchema, CompanyFilters } from '@/services/company-management';
import { withPermission, AuthenticatedRequest } from '@/lib/permission-middleware';
import { PermissionResource, PermissionAction } from '@/types';

const companyService = new CompanyManagementService();

// Validation schema for query parameters
const getCompaniesQuerySchema = z.object({
  search: z.string().optional(),
  industry: z.string().optional(),
  size: z.string().optional(),
  ownerId: z.string().optional(),
  hasWebsite: z.string().transform(val => val === 'true').optional(),
  page: z.string().transform(val => parseInt(val) || 1).optional(),
  limit: z.string().transform(val => Math.min(parseInt(val) || 20, 100)).optional(),
  sortBy: z.enum(['name', 'industry', 'size', 'createdAt', 'updatedAt']).optional(),
  sortOrder: z.enum(['asc', 'desc']).optional(),
});

/**
 * GET /api/companies
 * Get companies with filtering and pagination
 */
export const GET = withPermission({
  resource: PermissionResource.COMPANIES,
  action: PermissionAction.READ,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest) => {
  try {
    const { searchParams } = new URL(req.url);
    const queryParams = Object.fromEntries(searchParams.entries());
    
    const validatedQuery = getCompaniesQuerySchema.parse(queryParams);
    
    const filters: CompanyFilters = {
      search: validatedQuery.search,
      industry: validatedQuery.industry,
      size: validatedQuery.size,
      ownerId: validatedQuery.ownerId,
      hasWebsite: validatedQuery.hasWebsite,
      page: validatedQuery.page,
      limit: validatedQuery.limit,
      sortBy: validatedQuery.sortBy,
      sortOrder: validatedQuery.sortOrder,
    };

    const result = await companyService.getCompanies(req.tenantId, filters);

    return NextResponse.json({
      success: true,
      data: result,
      message: 'Companies retrieved successfully'
    });

  } catch (error) {
    console.error('Get companies error:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          error: 'Invalid query parameters',
          details: error.errors.map(e => ({
            field: e.path.join('.'),
            message: e.message
          }))
        },
        { status: 400 }
      );
    }

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
});

/**
 * POST /api/companies
 * Create a new company
 */
export const POST = withPermission({
  resource: PermissionResource.COMPANIES,
  action: PermissionAction.CREATE,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest) => {
  try {
    const body = await req.json();
    const validatedData = createCompanySchema.parse(body);

    const company = await companyService.createCompany(
      req.tenantId,
      validatedData,
      req.userId
    );

    return NextResponse.json({
      success: true,
      data: { company },
      message: 'Company created successfully'
    }, { status: 201 });

  } catch (error) {
    console.error('Create company error:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          error: 'Validation failed',
          details: error.errors.map(e => ({
            field: e.path.join('.'),
            message: e.message
          }))
        },
        { status: 400 }
      );
    }

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
});

/**
 * PUT /api/companies
 * Update an existing company
 */
export const PUT = withPermission({
  resource: PermissionResource.COMPANIES,
  action: PermissionAction.UPDATE,
  requireOwnership: true,
  getResourceOwnerId: async (req) => {
    const body = await req.json();
    return body.companyId;
  },
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest) => {
  try {
    const body = await req.json();
    const { companyId, ...updateData } = body;

    if (!companyId) {
      return NextResponse.json(
        { error: 'Company ID is required' },
        { status: 400 }
      );
    }

    const company = await companyService.updateCompany(
      companyId,
      req.tenantId,
      updateData,
      req.userId
    );

    return NextResponse.json({
      success: true,
      data: { company },
      message: 'Company updated successfully'
    });

  } catch (error) {
    console.error('Update company error:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          error: 'Validation failed',
          details: error.errors.map(e => ({
            field: e.path.join('.'),
            message: e.message
          }))
        },
        { status: 400 }
      );
    }

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
});

/**
 * DELETE /api/companies
 * Delete a company
 */
export const DELETE = withPermission({
  resource: PermissionResource.COMPANIES,
  action: PermissionAction.DELETE,
  requireOwnership: true,
  getResourceOwnerId: async (req) => {
    const { searchParams } = new URL(req.url);
    return searchParams.get('companyId');
  },
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest) => {
  try {
    const { searchParams } = new URL(req.url);
    const companyId = searchParams.get('companyId');

    if (!companyId) {
      return NextResponse.json(
        { error: 'Company ID is required' },
        { status: 400 }
      );
    }

    await companyService.deleteCompany(companyId, req.tenantId);

    return NextResponse.json({
      success: true,
      message: 'Company deleted successfully'
    });

  } catch (error) {
    console.error('Delete company error:', error);

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
});
