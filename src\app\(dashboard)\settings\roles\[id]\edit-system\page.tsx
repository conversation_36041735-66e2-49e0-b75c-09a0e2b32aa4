'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { PermissionProvider } from '@/components/providers/permission-provider';
import { PermissionGate } from '@/components/ui/permission-gate';
import { PermissionAction, PermissionResource } from '@/types';
import { useTenant } from '@/hooks/use-tenant';
import { PageLayout } from '@/components/layout/page-layout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { useToast } from '@/hooks/use-toast';
import {
  ArrowLeftIcon,
  ExclamationTriangleIcon,
  KeyIcon,
  CheckIcon,
  ShieldCheckIcon,
  ExclamationCircleIcon,
  InformationCircleIcon
} from '@heroicons/react/24/outline';

interface Role {
  id: string;
  name: string;
  description: string;
  isSystemRole: boolean;
  parentRoleId?: string;
  permissions: Permission[];
  childRoles: Role[];
  userCount?: number;
}

interface Permission {
  id: string;
  name: string;
  resource: string;
  action: string;
  category: string;
  description: string;
}

interface SystemRoleFormData {
  name: string;
  description: string;
  permissions: string[];
}

export default function EditSystemRolePage() {
  const { currentTenant } = useTenant();
  const router = useRouter();
  const params = useParams();
  const { toast } = useToast();
  const roleId = params.id as string;

  const [role, setRole] = useState<Role | null>(null);
  const [formData, setFormData] = useState<SystemRoleFormData>({
    name: '',
    description: '',
    permissions: []
  });

  const [permissions, setPermissions] = useState<Permission[]>([]);
  const [groupedPermissions, setGroupedPermissions] = useState<Record<string, Permission[]>>({});
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [showWarning, setShowWarning] = useState(true);

  useEffect(() => {
    if (currentTenant && roleId) {
      fetchData();
    }
  }, [currentTenant, roleId]);

  useEffect(() => {
    // Check for unsaved changes
    if (role) {
      const originalPermissions = role.permissions.map(p => p.id).sort();
      const currentPermissions = formData.permissions.sort();
      const permissionsChanged = JSON.stringify(originalPermissions) !== JSON.stringify(currentPermissions);
      const nameChanged = formData.name !== role.name;
      const descriptionChanged = formData.description !== (role.description || '');
      
      setHasUnsavedChanges(permissionsChanged || nameChanged || descriptionChanged);
    }
  }, [formData, role]);

  const fetchData = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const [roleResponse, permissionsResponse] = await Promise.all([
        fetch(`/api/roles/${roleId}`),
        fetch('/api/permissions')
      ]);

      if (roleResponse.ok && permissionsResponse.ok) {
        const roleData = await roleResponse.json();
        const permissionsData = await permissionsResponse.json();

        const roleInfo = roleData.data.role;
        
        if (!roleInfo.isSystemRole) {
          setError('This role is not a system role');
          return;
        }

        setRole(roleInfo);
        setFormData({
          name: roleInfo.name,
          description: roleInfo.description || '',
          permissions: roleInfo.permissions.map((p: Permission) => p.id)
        });

        setPermissions(permissionsData.data.permissions || []);
        setGroupedPermissions(permissionsData.data.groupedPermissions || {});
      } else {
        setError('Failed to load required data');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load data');
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (field: keyof SystemRoleFormData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handlePermissionToggle = (permissionId: string) => {
    setFormData(prev => ({
      ...prev,
      permissions: prev.permissions.includes(permissionId)
        ? prev.permissions.filter(id => id !== permissionId)
        : [...prev.permissions, permissionId]
    }));
  };

  const handleSelectAllCategory = (category: string) => {
    const categoryPermissions = groupedPermissions[category] || [];
    const categoryPermissionIds = categoryPermissions.map(p => p.id);
    const allSelected = categoryPermissionIds.every(id => formData.permissions.includes(id));

    if (allSelected) {
      setFormData(prev => ({
        ...prev,
        permissions: prev.permissions.filter(id => !categoryPermissionIds.includes(id))
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        permissions: [...new Set([...prev.permissions, ...categoryPermissionIds])]
      }));
    }
  };

  const handleSavePermissions = async () => {
    try {
      setIsSaving(true);

      const response = await fetch(`/api/roles/${roleId}/system`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          updateType: 'permissions',
          permissions: formData.permissions
        }),
      });

      if (response.ok) {
        toast({
          title: 'Success',
          description: 'System role permissions updated successfully',
        });
        await fetchData(); // Refresh data
      } else {
        const errorData = await response.json();
        toast({
          title: 'Error',
          description: errorData.error || 'Failed to update permissions',
          variant: 'destructive',
        });
      }
    } catch (err) {
      toast({
        title: 'Error',
        description: err instanceof Error ? err.message : 'Failed to update permissions',
        variant: 'destructive',
      });
    } finally {
      setIsSaving(false);
    }
  };

  const handleSaveRole = async () => {
    if (!formData.name.trim()) {
      toast({
        title: 'Validation Error',
        description: 'Role name is required',
        variant: 'destructive',
      });
      return;
    }

    try {
      setIsSaving(true);

      const response = await fetch(`/api/roles/${roleId}/system`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          updateType: 'role',
          name: formData.name.trim(),
          description: formData.description.trim() || undefined,
          permissions: formData.permissions
        }),
      });

      if (response.ok) {
        toast({
          title: 'Success',
          description: 'System role updated successfully',
        });
        router.push(`/settings/roles/${roleId}`);
      } else {
        const errorData = await response.json();
        toast({
          title: 'Error',
          description: errorData.error || 'Failed to update system role',
          variant: 'destructive',
        });
      }
    } catch (err) {
      toast({
        title: 'Error',
        description: err instanceof Error ? err.message : 'Failed to update system role',
        variant: 'destructive',
      });
    } finally {
      setIsSaving(false);
    }
  };

  const handleCancel = () => {
    if (hasUnsavedChanges) {
      if (confirm('You have unsaved changes. Are you sure you want to leave?')) {
        router.push(`/settings/roles/${roleId}`);
      }
    } else {
      router.push(`/settings/roles/${roleId}`);
    }
  };

  if (!currentTenant) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <ExclamationTriangleIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-500">No tenant context available</p>
        </div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner />
      </div>
    );
  }

  if (error || !role) {
    return (
      <PageLayout
        title="Edit System Role"
        description="Update system role information and permissions"
      >
        <Card className="border-red-200 bg-red-50">
          <CardContent className="p-12 text-center">
            <ExclamationTriangleIcon className="w-12 h-12 text-red-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Error Loading System Role</h3>
            <p className="text-gray-500 mb-4">{error || 'System role not found'}</p>
            <Button onClick={() => router.push('/settings/roles')}>
              <ArrowLeftIcon className="w-4 h-4 mr-2" />
              Back to Roles
            </Button>
          </CardContent>
        </Card>
      </PageLayout>
    );
  }

  const actions = (
    <div className="flex items-center gap-2">
      <Button variant="outline" onClick={handleCancel}>
        <ArrowLeftIcon className="w-4 h-4 mr-2" />
        Cancel
      </Button>
      <Button
        variant="outline"
        onClick={handleSavePermissions}
        disabled={isSaving || !hasUnsavedChanges}
        className="border-blue-300 text-blue-700 hover:bg-blue-50"
      >
        {isSaving ? (
          <LoadingSpinner className="w-4 h-4 mr-2" />
        ) : (
          <KeyIcon className="w-4 h-4 mr-2" />
        )}
        Save Permissions Only
      </Button>
      <Button
        onClick={handleSaveRole}
        disabled={isSaving || !formData.name.trim()}
        className="bg-orange-600 hover:bg-orange-700"
      >
        {isSaving ? (
          <LoadingSpinner className="w-4 h-4 mr-2" />
        ) : (
          <CheckIcon className="w-4 h-4 mr-2" />
        )}
        Save All Changes
      </Button>
    </div>
  );

  return (
    <PermissionProvider>
      <PageLayout
        title={`Edit System Role: ${role.name}`}
        description="Modify system role information and permissions"
        actions={actions}
      >
        <PermissionGate
          resource={PermissionResource.ROLES}
          action={PermissionAction.MANAGE_SYSTEM_ROLES}
          fallback={<AccessDeniedMessage />}
        >
          <div className="space-y-6">
            {/* Warning Banner */}
            {showWarning && (
              <Card className="border-orange-200 bg-orange-50">
                <CardContent className="p-4">
                  <div className="flex items-start gap-3">
                    <ExclamationCircleIcon className="w-6 h-6 text-orange-600 flex-shrink-0 mt-0.5" />
                    <div className="flex-1">
                      <h4 className="font-medium text-orange-900 mb-1">System Role Warning</h4>
                      <p className="text-sm text-orange-800 mb-3">
                        You are editing a system role. Changes to system roles affect core platform functionality 
                        and all users with this role. Please proceed with caution and ensure you understand 
                        the implications of your changes.
                      </p>
                      <div className="flex items-center gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setShowWarning(false)}
                          className="text-orange-700 border-orange-300 hover:bg-orange-100"
                        >
                          I Understand
                        </Button>
                        <span className="text-xs text-orange-700">
                          Users affected: {role.userCount || 0}
                        </span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Unsaved Changes Indicator */}
            {hasUnsavedChanges && (
              <Card className="border-blue-200 bg-blue-50">
                <CardContent className="p-3">
                  <div className="flex items-center gap-2 text-blue-800">
                    <InformationCircleIcon className="w-5 h-5" />
                    <p className="text-sm">You have unsaved changes</p>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Basic Information */}
            <Card>
              <CardHeader>
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-orange-100 rounded-lg">
                    <ShieldCheckIcon className="w-6 h-6 text-orange-600" />
                  </div>
                  <div>
                    <CardTitle>System Role Information</CardTitle>
                    <p className="text-sm text-gray-600">
                      Basic information for this system role
                    </p>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                    Role Name *
                  </label>
                  <input
                    type="text"
                    id="name"
                    value={formData.name}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                    placeholder="Enter role name"
                    required
                  />
                </div>

                <div>
                  <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
                    Description
                  </label>
                  <textarea
                    id="description"
                    value={formData.description}
                    onChange={(e) => handleInputChange('description', e.target.value)}
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                    placeholder="Enter role description"
                  />
                </div>

                <div className="bg-gray-50 p-3 rounded-lg">
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-600">Users with this role:</span>
                    <span className="font-medium">{role.userCount || 0}</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Permissions */}
            <Card>
              <CardHeader>
                <CardTitle>System Role Permissions</CardTitle>
                <p className="text-sm text-gray-600">
                  Manage permissions for this system role. Selected: {formData.permissions.length} of {permissions.length}
                </p>
              </CardHeader>
              <CardContent>
                {Object.keys(groupedPermissions).length === 0 ? (
                  <div className="text-center py-8">
                    <KeyIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-500">No permissions available</p>
                  </div>
                ) : (
                  <div className="space-y-6">
                    {Object.entries(groupedPermissions).map(([category, categoryPermissions]) => {
                      const categoryPermissionIds = categoryPermissions.map(p => p.id);
                      const selectedInCategory = categoryPermissionIds.filter(id => 
                        formData.permissions.includes(id)
                      ).length;
                      const allSelected = selectedInCategory === categoryPermissionIds.length;

                      return (
                        <div key={category} className="border border-gray-200 rounded-lg p-4">
                          <div className="flex items-center justify-between mb-3">
                            <h4 className="font-medium text-gray-900 capitalize">
                              {category.replace('_', ' ')} ({selectedInCategory}/{categoryPermissionIds.length})
                            </h4>
                            <Button
                              type="button"
                              variant="outline"
                              size="sm"
                              onClick={() => handleSelectAllCategory(category)}
                              className="border-orange-300 text-orange-700 hover:bg-orange-50"
                            >
                              {allSelected ? 'Deselect All' : 'Select All'}
                            </Button>
                          </div>
                          
                          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                            {categoryPermissions.map((permission) => (
                              <label
                                key={permission.id}
                                className="flex items-start gap-3 p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer"
                              >
                                <input
                                  type="checkbox"
                                  checked={formData.permissions.includes(permission.id)}
                                  onChange={() => handlePermissionToggle(permission.id)}
                                  className="mt-0.5 h-4 w-4 text-orange-600 focus:ring-orange-500 border-gray-300 rounded"
                                />
                                <div className="min-w-0">
                                  <p className="font-medium text-sm text-gray-900">
                                    {permission.name}
                                  </p>
                                  {permission.description && (
                                    <p className="text-xs text-gray-500 mt-1">
                                      {permission.description}
                                    </p>
                                  )}
                                </div>
                              </label>
                            ))}
                          </div>
                        </div>
                      );
                    })}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </PermissionGate>
      </PageLayout>
    </PermissionProvider>
  );
}

function AccessDeniedMessage() {
  return (
    <Card>
      <CardContent className="p-12 text-center">
        <ExclamationTriangleIcon className="w-12 h-12 text-red-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">Access Denied</h3>
        <p className="text-gray-500">
          You don't have permission to edit system roles. This requires special administrative privileges.
        </p>
      </CardContent>
    </Card>
  );
}
