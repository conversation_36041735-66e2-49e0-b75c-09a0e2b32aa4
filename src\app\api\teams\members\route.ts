import { NextResponse } from 'next/server';
import { z } from 'zod';
import { withPermission, AuthenticatedRequest } from '@/lib/permission-middleware';
import { TeamManagementService } from '@/services/team-management';
import { PermissionAction, PermissionResource } from '@/types';

const addMemberSchema = z.object({
  teamId: z.string().min(1, 'Team ID is required'),
  userId: z.string().min(1, 'User ID is required'),
  role: z.string().optional()
});

const removeMemberSchema = z.object({
  teamId: z.string().min(1, 'Team ID is required'),
  userId: z.string().min(1, 'User ID is required')
});

const bulkAddMembersSchema = z.object({
  teamId: z.string().min(1, 'Team ID is required'),
  userIds: z.array(z.string()).min(1, 'At least one user ID is required'),
  role: z.string().optional()
});

/**
 * GET /api/teams/members
 * Get team members
 */
export const GET = withPermission({
  resource: PermissionResource.USERS,
  action: PermissionAction.READ,
  allowSuperAdmin: true
})(async (req) => {
  try {
    const { searchParams } = new URL(req.url);
    const teamId = searchParams.get('teamId');

    if (!teamId) {
      return NextResponse.json(
        { error: 'Team ID is required' },
        { status: 400 }
      );
    }

    const teamService = new TeamManagementService();
    const members = await teamService.getTeamMembers(teamId, req.tenantId);

    return NextResponse.json({
      success: true,
      data: {
        teamId,
        members,
        count: members.length
      }
    });

  } catch (error) {
    console.error('Get team members error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch team members' },
      { status: 500 }
    );
  }
});

/**
 * POST /api/teams/members
 * Add member to team
 */
export const POST = withPermission({
  resource: PermissionResource.USERS,
  action: PermissionAction.MANAGE,
  allowSuperAdmin: true
})(async (req) => {
  try {
    const body = await req.json();
    
    // Check if this is a bulk operation
    if (body.userIds && Array.isArray(body.userIds)) {
      const validatedData = bulkAddMembersSchema.parse(body);
      
      const teamService = new TeamManagementService();
      const results = await teamService.addMembersToTeam(
        validatedData.teamId,
        req.tenantId,
        validatedData.userIds,
        req.userId,
        validatedData.role
      );

      return NextResponse.json({
        success: true,
        data: { results },
        message: `${results.successful.length} members added successfully`
      });
    } else {
      const validatedData = addMemberSchema.parse(body);

      const teamService = new TeamManagementService();
      await teamService.addMemberToTeam(
        validatedData.teamId,
        req.tenantId,
        validatedData.userId,
        req.userId,
        validatedData.role
      );

      return NextResponse.json({
        success: true,
        message: 'Member added to team successfully'
      });
    }

  } catch (error) {
    console.error('Add team member error:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          error: 'Validation failed',
          details: error.errors.map(e => ({
            field: e.path.join('.'),
            message: e.message
          }))
        },
        { status: 400 }
      );
    }

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to add member to team' },
      { status: 500 }
    );
  }
});

/**
 * DELETE /api/teams/members
 * Remove member from team
 */
export const DELETE = withPermission({
  resource: PermissionResource.USERS,
  action: PermissionAction.MANAGE,
  allowSuperAdmin: true
})(async (req) => {
  try {
    const body = await req.json();
    const validatedData = removeMemberSchema.parse(body);

    const teamService = new TeamManagementService();
    await teamService.removeMemberFromTeam(
      validatedData.teamId,
      req.tenantId,
      validatedData.userId,
      req.userId
    );

    return NextResponse.json({
      success: true,
      message: 'Member removed from team successfully'
    });

  } catch (error) {
    console.error('Remove team member error:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          error: 'Validation failed',
          details: error.errors.map(e => ({
            field: e.path.join('.'),
            message: e.message
          }))
        },
        { status: 400 }
      );
    }

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to remove member from team' },
      { status: 500 }
    );
  }
});
