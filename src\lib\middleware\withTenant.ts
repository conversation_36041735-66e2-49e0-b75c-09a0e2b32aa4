import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth';
import { prisma } from '../prisma';

// Extended user type for session
interface ExtendedUser {
  id: string;
  name?: string | null;
  email?: string | null;
  image?: string | null;
  isPlatformAdmin?: boolean;
}

interface ExtendedSession {
  user: ExtendedUser;
}

export interface TenantApiRequest extends NextApiRequest {
  tenantId: string;
  userId: string;
  session: ExtendedSession;
  isSuperAdmin: boolean;
}

/**
 * Middleware to add tenant context to Pages API routes
 * This is specifically for Pages API routes (not App Router)
 */
export function withTenant(
  handler: (req: TenantApiRequest, res: NextApiResponse) => Promise<void>
) {
  return async function (req: NextApiRequest, res: NextApiResponse) {
    try {
      // Get user session
      const session = await getServerSession(req, res, authOptions) as ExtendedSession | null;

      if (!session?.user) {
        return res.status(401).json({ error: 'Authentication required' });
      }

      const userId = session.user.id;
      const isSuperAdmin = session.user.isPlatformAdmin || false;

      // Super Admin bypass: Skip tenant context for platform admins
      if (isSuperAdmin) {
        const tenantReq = req as TenantApiRequest;
        tenantReq.userId = userId;
        tenantReq.session = session;
        tenantReq.isSuperAdmin = true;
        tenantReq.tenantId = ''; // No tenant context for Super Admin
        return handler(tenantReq, res);
      }

      // Get tenant ID from headers
      let tenantId = req.headers['x-tenant-id'] as string;

      // If no tenant ID in headers, get user's tenant
      if (!tenantId) {
        const userTenant = await prisma.tenantUser.findFirst({
          where: { userId },
          select: { tenantId: true }
        });
        tenantId = userTenant?.tenantId || '';
      }

      if (!tenantId) {
        return res.status(400).json({ error: 'Tenant context required' });
      }

      // Verify user has access to this tenant
      const tenantUser = await prisma.tenantUser.findUnique({
        where: {
          tenantId_userId: {
            tenantId,
            userId
          }
        }
      });

      if (!tenantUser) {
        return res.status(403).json({ error: 'Access denied to tenant' });
      }

      // Add tenant context to request
      const tenantReq = req as TenantApiRequest;
      tenantReq.tenantId = tenantId;
      tenantReq.userId = userId;
      tenantReq.session = session;
      tenantReq.isSuperAdmin = false;

      return handler(tenantReq, res);
    } catch (error) {
      console.error('Tenant middleware error:', error);
      return res.status(500).json({ error: 'Internal server error' });
    }
  };
}
