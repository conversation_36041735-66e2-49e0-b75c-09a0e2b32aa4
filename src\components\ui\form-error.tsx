'use client';

import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { AlertCircle, CheckCircle, AlertTriangle, Info } from 'lucide-react';
import { cn } from '@/lib/utils';

interface FormErrorProps {
  error?: string;
  type?: 'error' | 'warning' | 'success' | 'info';
  className?: string;
  showIcon?: boolean;
  animate?: boolean;
}

export function FormError({ 
  error, 
  type = 'error', 
  className, 
  showIcon = true,
  animate = true 
}: FormErrorProps) {
  if (!error) return null;

  const icons = {
    error: AlertCircle,
    warning: AlertTriangle,
    success: CheckCircle,
    info: Info,
  };

  const Icon = icons[type];

  const variants = {
    error: 'text-destructive',
    warning: 'text-yellow-600',
    success: 'text-green-600',
    info: 'text-blue-600',
  };

  const bgVariants = {
    error: 'bg-destructive/10 border-destructive/20',
    warning: 'bg-yellow-50 border-yellow-200 dark:bg-yellow-900/20 dark:border-yellow-800',
    success: 'bg-green-50 border-green-200 dark:bg-green-900/20 dark:border-green-800',
    info: 'bg-blue-50 border-blue-200 dark:bg-blue-900/20 dark:border-blue-800',
  };

  const content = (
    <div className={cn(
      'flex items-start gap-2 p-3 rounded-md border text-sm',
      bgVariants[type],
      className
    )}>
      {showIcon && (
        <Icon className={cn('h-4 w-4 mt-0.5 flex-shrink-0', variants[type])} />
      )}
      <span className={cn('leading-relaxed', variants[type])}>
        {error}
      </span>
    </div>
  );

  if (!animate) {
    return content;
  }

  return (
    <AnimatePresence mode="wait">
      <motion.div
        initial={{ opacity: 0, height: 0, marginTop: 0 }}
        animate={{ opacity: 1, height: 'auto', marginTop: 8 }}
        exit={{ opacity: 0, height: 0, marginTop: 0 }}
        transition={{ duration: 0.2, ease: 'easeInOut' }}
      >
        {content}
      </motion.div>
    </AnimatePresence>
  );
}

// Inline error component for simple text errors
export function InlineFormError({ 
  error, 
  className,
  animate = true 
}: { 
  error?: string; 
  className?: string;
  animate?: boolean;
}) {
  if (!error) return null;

  const content = (
    <p className={cn('text-sm text-destructive flex items-center gap-1', className)}>
      <AlertCircle className="h-3 w-3 flex-shrink-0" />
      {error}
    </p>
  );

  if (!animate) {
    return content;
  }

  return (
    <AnimatePresence mode="wait">
      <motion.div
        initial={{ opacity: 0, y: -10 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -10 }}
        transition={{ duration: 0.15, ease: 'easeOut' }}
      >
        {content}
      </motion.div>
    </AnimatePresence>
  );
}

// Success message component
export function FormSuccess({ 
  message, 
  className,
  animate = true 
}: { 
  message?: string; 
  className?: string;
  animate?: boolean;
}) {
  if (!message) return null;

  const content = (
    <div className={cn(
      'flex items-center gap-2 p-3 rounded-md border bg-green-50 border-green-200 text-green-700 text-sm dark:bg-green-900/20 dark:border-green-800 dark:text-green-400',
      className
    )}>
      <CheckCircle className="h-4 w-4 flex-shrink-0" />
      <span>{message}</span>
    </div>
  );

  if (!animate) {
    return content;
  }

  return (
    <AnimatePresence mode="wait">
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.95 }}
        transition={{ duration: 0.2, ease: 'easeOut' }}
      >
        {content}
      </motion.div>
    </AnimatePresence>
  );
}

// Form field wrapper with enhanced error display
export function FormFieldWrapper({ 
  children, 
  error, 
  success,
  className 
}: { 
  children: React.ReactNode; 
  error?: string;
  success?: string;
  className?: string;
}) {
  return (
    <div className={cn('space-y-2', className)}>
      {children}
      <FormError error={error} />
      <FormSuccess message={success} />
    </div>
  );
}
