import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { hasPermission } from '@/lib/permissions';
import { PermissionResource, PermissionAction } from '@/types';
import { documentGenerator } from '@/services/document-generation';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';

// Request validation schema
const generateDocumentSchema = z.object({
  format: z.enum(['docx', 'pdf']).default('docx'),
  includeCharts: z.boolean().default(true),
  includeBranding: z.boolean().default(true),
  customStyling: z.object({
    // Legacy options for backward compatibility
    fontFamily: z.string().optional(),
    fontSize: z.number().min(8).max(24).optional(),
    headerColor: z.string().optional(),
    accentColor: z.string().optional(),
    logoUrl: z.string().optional(),
    companyName: z.string().optional(),

    // Enhanced options for md-to-docx library
    documentType: z.enum(['document', 'report']).optional(),
    titleSize: z.number().min(12).max(48).optional(),
    headingSpacing: z.number().min(100).max(500).optional(),
    paragraphSpacing: z.number().min(100).max(500).optional(),
    lineSpacing: z.number().min(1).max(3).optional(),
    heading1Size: z.number().min(12).max(48).optional(),
    heading2Size: z.number().min(12).max(48).optional(),
    heading3Size: z.number().min(12).max(48).optional(),
    heading4Size: z.number().min(12).max(48).optional(),
    heading5Size: z.number().min(12).max(48).optional(),
    paragraphSize: z.number().min(8).max(24).optional(),
    listItemSize: z.number().min(8).max(24).optional(),
    codeBlockSize: z.number().min(8).max(24).optional(),
    blockquoteSize: z.number().min(8).max(24).optional(),
    tocFontSize: z.number().min(8).max(24).optional(),
    paragraphAlignment: z.enum(['LEFT', 'RIGHT', 'CENTER', 'JUSTIFIED']).optional(),
    headingAlignment: z.enum(['LEFT', 'RIGHT', 'CENTER', 'JUSTIFIED']).optional(),
    heading1Alignment: z.enum(['LEFT', 'RIGHT', 'CENTER', 'JUSTIFIED']).optional(),
    heading2Alignment: z.enum(['LEFT', 'RIGHT', 'CENTER', 'JUSTIFIED']).optional(),
    heading3Alignment: z.enum(['LEFT', 'RIGHT', 'CENTER', 'JUSTIFIED']).optional(),
    heading4Alignment: z.enum(['LEFT', 'RIGHT', 'CENTER', 'JUSTIFIED']).optional(),
    heading5Alignment: z.enum(['LEFT', 'RIGHT', 'CENTER', 'JUSTIFIED']).optional(),
    blockquoteAlignment: z.enum(['LEFT', 'RIGHT', 'CENTER', 'JUSTIFIED']).optional(),
  }).optional()
});

/**
 * POST /api/proposals/[id]/generate-document
 * Generate Word/PDF document from proposal
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user's current tenant
    const currentTenant = session.user.tenants?.[0];
    if (!currentTenant) {
      return NextResponse.json({ error: 'No tenant access' }, { status: 403 });
    }

    // Check permission for proposal reading
    const hasAccess = await hasPermission(
      PermissionResource.PROPOSALS,
      PermissionAction.READ,
      currentTenant.id
    );

    if (!hasAccess) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const proposalId = params.id;
    const body = await request.json().catch(() => ({}));
    
    // Validate request data
    const validatedData = generateDocumentSchema.parse(body);

    // Verify proposal exists and belongs to tenant
    const proposal = await prisma.proposal.findFirst({
      where: {
        id: proposalId,
        tenantId: currentTenant.id
      },
      include: {
        sections: true,
        charts: true
      }
    });

    if (!proposal) {
      return NextResponse.json(
        { error: 'Proposal not found or access denied' },
        { status: 404 }
      );
    }

    // Check if proposal has content to generate document from
    if (proposal.sections.length === 0) {
      return NextResponse.json(
        { error: 'Proposal has no sections to generate document from' },
        { status: 400 }
      );
    }

    // Check if proposal generation is complete
    if (proposal.generationStatus === 'generating') {
      return NextResponse.json(
        { error: 'Cannot generate document while proposal is being generated' },
        { status: 409 }
      );
    }

    if (proposal.generationStatus === 'failed') {
      return NextResponse.json(
        { error: 'Cannot generate document from failed proposal generation' },
        { status: 400 }
      );
    }

    // Generate the document
    const result = await documentGenerator.generateDocument(
      proposalId,
      currentTenant.id,
      validatedData
    );

    return NextResponse.json({
      success: true,
      data: result,
      message: 'Document generated successfully'
    });

  } catch (error) {
    console.error('Error generating document:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          error: 'Invalid request data', 
          details: error.errors.map(e => ({
            field: e.path.join('.'),
            message: e.message
          }))
        },
        { status: 400 }
      );
    }

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * @swagger
 * /api/proposals/{id}/generate-document:
 *   post:
 *     summary: Generate document from proposal
 *     description: Create a Word or PDF document from the proposal content including sections and charts
 *     tags: [Proposals]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Proposal ID
 *     requestBody:
 *       required: false
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               format:
 *                 type: string
 *                 enum: [docx, pdf]
 *                 default: docx
 *                 description: Output document format
 *               includeCharts:
 *                 type: boolean
 *                 default: true
 *                 description: Whether to include Mermaid charts in the document
 *               includeBranding:
 *                 type: boolean
 *                 default: true
 *                 description: Whether to include tenant branding
 *               customStyling:
 *                 type: object
 *                 description: Custom styling options for the document
 *                 properties:
 *                   fontFamily:
 *                     type: string
 *                     description: Font family for the document
 *                   fontSize:
 *                     type: number
 *                     minimum: 8
 *                     maximum: 24
 *                     description: Base font size in points
 *                   headerColor:
 *                     type: string
 *                     description: Color for headers (hex format)
 *                   accentColor:
 *                     type: string
 *                     description: Accent color (hex format)
 *                   logoUrl:
 *                     type: string
 *                     description: URL to company logo
 *                   companyName:
 *                     type: string
 *                     description: Company name for branding
 *     responses:
 *       200:
 *         description: Document generated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     documentPath:
 *                       type: string
 *                       description: Relative path to the generated document
 *                     fileName:
 *                       type: string
 *                       description: Name of the generated file
 *                     fileSize:
 *                       type: number
 *                       description: Size of the generated file in bytes
 *                     downloadUrl:
 *                       type: string
 *                       description: URL to download the document
 *                 message:
 *                   type: string
 *       400:
 *         description: Invalid request data or proposal has no content
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - insufficient permissions
 *       404:
 *         description: Proposal not found
 *       409:
 *         description: Conflict - proposal is being generated
 *       500:
 *         description: Internal server error
 */
