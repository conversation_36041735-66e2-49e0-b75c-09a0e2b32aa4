'use client';

import { useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { ProjectForm } from '@/components/projects/ProjectForm';
import { PageLayout } from '@/components/layout/page-layout';
import { PermissionGate } from '@/components/ui/permission-gate';
import { PermissionResource, PermissionAction } from '@/types';
import { Card, CardContent } from '@/components/ui/card';
import { EmptyState } from '@/components/ui/empty-state';
import { ExclamationTriangleIcon } from '@heroicons/react/24/outline';

export default function NewProjectPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const opportunityId = searchParams.get('opportunityId');
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = async (data: any) => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/projects', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create project');
      }

      const result = await response.json();
      
      // Redirect to the new project page
      router.push(`/projects/${result.data.project.id}`);
    } catch (error) {
      console.error('Error creating project:', error);
      throw error; // Re-throw to let ProjectForm handle the error display
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    router.push('/projects');
  };

  return (
    <PermissionGate
      resource={PermissionResource.PROJECTS}
      action={PermissionAction.CREATE}
      fallback={
        <PageLayout>
          <Card>
            <CardContent className="p-8">
              <EmptyState
                icon={<ExclamationTriangleIcon className="w-12 h-12 text-muted-foreground" />}
                title="Access Denied"
                description="You don't have permission to create projects. Please contact your administrator for access."
                action={{
                  label: 'Back to Projects',
                  onClick: () => router.push('/projects'),
                  variant: 'primary'
                }}
              />
            </CardContent>
          </Card>
        </PageLayout>
      }
    >
      <PageLayout
        title="Create New Project"
        description="Set up a new project with timeline, budget, and team assignments"
      >
        <div className="max-w-5xl mx-auto py-6">
          <ProjectForm
            initialData={opportunityId ? { opportunityId } : undefined}
            onSubmit={handleSubmit}
            onCancel={handleCancel}
            isLoading={isLoading}
            submitLabel="Create Project"
          />
        </div>
      </PageLayout>
    </PermissionGate>
  );
}
