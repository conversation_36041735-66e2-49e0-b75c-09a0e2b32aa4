import { prisma } from '@/lib/prisma';
import { z } from 'zod';
import { UnifiedNotificationService } from './unified-notification-service';

// Workflow and automation types
export type WorkflowEventType = 
  | 'workflow_started'
  | 'workflow_completed'
  | 'workflow_failed'
  | 'workflow_paused'
  | 'workflow_resumed'
  | 'step_completed'
  | 'step_failed'
  | 'approval_required'
  | 'automation_triggered'
  | 'automation_error'
  | 'schedule_executed'
  | 'condition_met'
  | 'escalation_triggered';

// Validation schemas
export const workflowEventSchema = z.object({
  workflowId: z.string(),
  workflowName: z.string(),
  tenantId: z.string(),
  eventType: z.enum(['workflow_started', 'workflow_completed', 'workflow_failed', 'workflow_paused', 'workflow_resumed', 'step_completed', 'step_failed', 'approval_required', 'automation_triggered', 'automation_error', 'schedule_executed', 'condition_met', 'escalation_triggered']),
  triggeredBy: z.string(),
  stepId: z.string().optional(),
  stepName: z.string().optional(),
  entityType: z.string().optional(), // 'lead', 'opportunity', 'project', etc.
  entityId: z.string().optional(),
  data: z.record(z.any()).optional(),
  duration: z.number().optional(), // in milliseconds
  errorMessage: z.string().optional(),
  approvalRequired: z.boolean().default(false),
  approvers: z.array(z.string()).optional(),
  priority: z.enum(['low', 'medium', 'high', 'urgent']).default('medium'),
});

export type WorkflowEvent = z.infer<typeof workflowEventSchema>;

export interface WorkflowExecutionResult {
  workflowId: string;
  executionId: string;
  status: 'completed' | 'failed' | 'paused' | 'running';
  startTime: Date;
  endTime?: Date;
  duration?: number;
  stepsCompleted: number;
  totalSteps: number;
  errors: string[];
  outputs: Record<string, any>;
  triggeredBy: string;
}

export class ProcessAutomationNotificationService {
  private notificationService = new UnifiedNotificationService();

  /**
   * Process workflow event and trigger notifications
   */
  async processWorkflowEvent(event: WorkflowEvent): Promise<void> {
    const validatedEvent = workflowEventSchema.parse(event);

    try {
      // Log the workflow event
      await this.logWorkflowEvent(validatedEvent);

      // Trigger appropriate notifications based on event type
      await this.triggerWorkflowNotification(validatedEvent);

    } catch (error) {
      console.error('Error processing workflow event:', error);
      throw error;
    }
  }

  /**
   * Process workflow completion with detailed results
   */
  async processWorkflowCompletion(
    workflowId: string,
    tenantId: string,
    result: WorkflowExecutionResult
  ): Promise<void> {
    try {
      // Create completion event
      const completionEvent: WorkflowEvent = {
        workflowId,
        workflowName: await this.getWorkflowName(workflowId, tenantId),
        tenantId,
        eventType: result.status === 'completed' ? 'workflow_completed' : 'workflow_failed',
        triggeredBy: result.triggeredBy,
        duration: result.duration,
        data: {
          executionId: result.executionId,
          stepsCompleted: result.stepsCompleted,
          totalSteps: result.totalSteps,
          outputs: result.outputs,
          errors: result.errors
        },
        errorMessage: result.errors.length > 0 ? result.errors.join('; ') : undefined,
        priority: result.status === 'failed' ? 'high' : 'medium'
      };

      await this.processWorkflowEvent(completionEvent);

      // Send detailed completion notification
      await this.triggerWorkflowCompletionNotification(completionEvent, result);

    } catch (error) {
      console.error('Error processing workflow completion:', error);
    }
  }

  /**
   * Log workflow event to database
   */
  private async logWorkflowEvent(event: WorkflowEvent): Promise<void> {
    try {
      await prisma.workflowLog.create({
        data: {
          workflowId: event.workflowId,
          tenantId: event.tenantId,
          eventType: event.eventType,
          stepId: event.stepId,
          stepName: event.stepName,
          entityType: event.entityType,
          entityId: event.entityId,
          triggeredBy: event.triggeredBy,
          data: event.data || {},
          duration: event.duration,
          errorMessage: event.errorMessage,
          priority: event.priority,
        }
      });
    } catch (error) {
      console.error('Failed to log workflow event:', error);
      // Don't throw error to avoid breaking the main operation
    }
  }

  /**
   * Trigger workflow notifications based on event type
   */
  private async triggerWorkflowNotification(event: WorkflowEvent): Promise<void> {
    try {
      // Only notify on significant events
      const notifiableEvents: WorkflowEventType[] = [
        'workflow_completed', 'workflow_failed', 'approval_required', 
        'automation_error', 'escalation_triggered'
      ];

      if (!notifiableEvents.includes(event.eventType)) {
        return;
      }

      // Get target users based on event type and workflow
      const targetUsers = await this.getWorkflowNotificationTargets(event);

      // Determine notification details based on event type
      const notificationDetails = this.getWorkflowNotificationDetails(event);

      for (const userId of targetUsers) {
        await this.notificationService.createNotification({
          tenantId: event.tenantId,
          userId,
          type: notificationDetails.type,
          category: 'system',
          title: notificationDetails.title,
          message: notificationDetails.message,
          data: {
            workflowId: event.workflowId,
            workflowName: event.workflowName,
            eventType: event.eventType,
            stepId: event.stepId,
            stepName: event.stepName,
            entityType: event.entityType,
            entityId: event.entityId,
            duration: event.duration,
            errorMessage: event.errorMessage,
            triggeredBy: event.triggeredBy,
            priority: event.priority,
            eventData: event.data
          },
          actionUrl: `/workflows/${event.workflowId}/executions`,
          actionLabel: 'View Workflow'
        });
      }

      // Handle approval required notifications
      if (event.eventType === 'approval_required' && event.approvers) {
        await this.triggerApprovalNotifications(event);
      }

      // Handle escalation notifications
      if (event.eventType === 'escalation_triggered') {
        await this.triggerEscalationNotifications(event);
      }

    } catch (error) {
      console.error('Failed to send workflow notification:', error);
    }
  }

  /**
   * Get notification targets for workflow events
   */
  private async getWorkflowNotificationTargets(event: WorkflowEvent): Promise<string[]> {
    const targetUsers = [];

    // Always notify the person who triggered the workflow
    targetUsers.push(event.triggeredBy);

    // Get workflow owners and administrators
    const workflowUsers = await prisma.tenantUser.findMany({
      where: {
        tenantId: event.tenantId,
        OR: [
          { role: { in: ['Admin', 'Workflow Manager', 'Process Manager'] } },
          { isTenantAdmin: true }
        ],
        status: 'active'
      },
      select: { userId: true }
    });

    targetUsers.push(...workflowUsers.map(user => user.userId));

    // For entity-specific workflows, notify entity owners
    if (event.entityType && event.entityId) {
      const entityOwners = await this.getEntityOwners(event.entityType, event.entityId, event.tenantId);
      targetUsers.push(...entityOwners);
    }

    return [...new Set(targetUsers)];
  }

  /**
   * Get notification details based on event type
   */
  private getWorkflowNotificationDetails(event: WorkflowEvent): { type: string; title: string; message: string } {
    switch (event.eventType) {
      case 'workflow_completed':
        return {
          type: 'workflow_completed',
          title: 'Workflow Completed Successfully',
          message: `Workflow "${event.workflowName}" has completed successfully${event.duration ? ` in ${Math.round(event.duration / 1000)}s` : ''}`
        };

      case 'workflow_failed':
        return {
          type: 'workflow_failed',
          title: 'Workflow Failed',
          message: `Workflow "${event.workflowName}" failed: ${event.errorMessage || 'Unknown error'}`
        };

      case 'approval_required':
        return {
          type: 'workflow_approval_required',
          title: 'Workflow Approval Required',
          message: `Workflow "${event.workflowName}" is waiting for approval at step "${event.stepName || 'Unknown'}"`
        };

      case 'automation_error':
        return {
          type: 'automation_error',
          title: 'Automation Error',
          message: `Automation error in workflow "${event.workflowName}": ${event.errorMessage || 'Unknown error'}`
        };

      case 'escalation_triggered':
        return {
          type: 'workflow_escalation',
          title: 'Workflow Escalation Triggered',
          message: `Workflow "${event.workflowName}" has been escalated due to timeout or failure conditions`
        };

      default:
        return {
          type: 'workflow_event',
          title: 'Workflow Event',
          message: `Workflow "${event.workflowName}" event: ${event.eventType}`
        };
    }
  }

  /**
   * Trigger approval notifications
   */
  private async triggerApprovalNotifications(event: WorkflowEvent): Promise<void> {
    if (!event.approvers || event.approvers.length === 0) return;

    try {
      for (const approverId of event.approvers) {
        await this.notificationService.createNotification({
          tenantId: event.tenantId,
          userId: approverId,
          type: 'workflow_approval_request',
          category: 'system',
          title: 'Workflow Approval Required',
          message: `Your approval is required for workflow "${event.workflowName}" at step "${event.stepName || 'Unknown'}"`,
          data: {
            workflowId: event.workflowId,
            workflowName: event.workflowName,
            stepId: event.stepId,
            stepName: event.stepName,
            requestedBy: event.triggeredBy,
            entityType: event.entityType,
            entityId: event.entityId,
            approvalData: event.data
          },
          actionUrl: `/workflows/${event.workflowId}/approve/${event.stepId}`,
          actionLabel: 'Review & Approve'
        });
      }
    } catch (error) {
      console.error('Failed to send approval notifications:', error);
    }
  }

  /**
   * Trigger escalation notifications
   */
  private async triggerEscalationNotifications(event: WorkflowEvent): Promise<void> {
    try {
      // Get senior management for escalations
      const seniorManagement = await prisma.tenantUser.findMany({
        where: {
          tenantId: event.tenantId,
          OR: [
            { role: { in: ['CEO', 'CTO', 'VP', 'Director', 'Senior Manager'] } },
            { isTenantAdmin: true }
          ],
          status: 'active'
        },
        select: { userId: true }
      });

      for (const user of seniorManagement) {
        await this.notificationService.createNotification({
          tenantId: event.tenantId,
          userId: user.userId,
          type: 'workflow_escalation_alert',
          category: 'system',
          title: 'Workflow Escalation Alert',
          message: `Workflow "${event.workflowName}" has been escalated and requires management attention`,
          data: {
            workflowId: event.workflowId,
            workflowName: event.workflowName,
            escalationReason: event.errorMessage || 'Timeout or failure conditions met',
            originalTriggeredBy: event.triggeredBy,
            entityType: event.entityType,
            entityId: event.entityId
          },
          actionUrl: `/workflows/${event.workflowId}`,
          actionLabel: 'Review Workflow'
        });
      }
    } catch (error) {
      console.error('Failed to send escalation notifications:', error);
    }
  }

  /**
   * Trigger detailed workflow completion notification
   */
  private async triggerWorkflowCompletionNotification(
    event: WorkflowEvent,
    result: WorkflowExecutionResult
  ): Promise<void> {
    try {
      const targetUsers = await this.getWorkflowNotificationTargets(event);

      for (const userId of targetUsers) {
        await this.notificationService.createNotification({
          tenantId: event.tenantId,
          userId,
          type: result.status === 'completed' ? 'workflow_execution_complete' : 'workflow_execution_failed',
          category: 'system',
          title: result.status === 'completed' ? 'Workflow Execution Complete' : 'Workflow Execution Failed',
          message: result.status === 'completed' 
            ? `Workflow "${event.workflowName}" completed successfully with ${result.stepsCompleted}/${result.totalSteps} steps`
            : `Workflow "${event.workflowName}" failed after ${result.stepsCompleted}/${result.totalSteps} steps`,
          data: {
            workflowId: event.workflowId,
            workflowName: event.workflowName,
            executionId: result.executionId,
            status: result.status,
            duration: result.duration,
            stepsCompleted: result.stepsCompleted,
            totalSteps: result.totalSteps,
            successRate: Math.round((result.stepsCompleted / result.totalSteps) * 100),
            outputs: result.outputs,
            errors: result.errors,
            triggeredBy: result.triggeredBy
          },
          actionUrl: `/workflows/${event.workflowId}/executions/${result.executionId}`,
          actionLabel: 'View Execution Details'
        });
      }
    } catch (error) {
      console.error('Failed to send workflow completion notification:', error);
    }
  }

  /**
   * Get entity owners for entity-specific workflows
   */
  private async getEntityOwners(entityType: string, entityId: string, tenantId: string): Promise<string[]> {
    try {
      let owners: string[] = [];

      switch (entityType.toLowerCase()) {
        case 'lead':
          const lead = await prisma.lead.findUnique({
            where: { id: entityId, tenantId },
            select: { ownerId: true }
          });
          if (lead?.ownerId) owners.push(lead.ownerId);
          break;

        case 'opportunity':
          const opportunity = await prisma.opportunity.findUnique({
            where: { id: entityId, tenantId },
            select: { ownerId: true }
          });
          if (opportunity?.ownerId) owners.push(opportunity.ownerId);
          break;

        case 'project':
          const project = await prisma.project.findUnique({
            where: { id: entityId, tenantId },
            select: { managerId: true }
          });
          if (project?.managerId) owners.push(project.managerId);
          break;

        default:
          // For unknown entity types, return empty array
          break;
      }

      return owners;
    } catch (error) {
      console.error('Failed to get entity owners:', error);
      return [];
    }
  }

  /**
   * Get workflow name (placeholder - would integrate with actual workflow service)
   */
  private async getWorkflowName(workflowId: string, tenantId: string): Promise<string> {
    try {
      // This would typically fetch from a workflow definition service
      const workflow = await prisma.workflow.findUnique({
        where: { id: workflowId, tenantId },
        select: { name: true }
      });
      return workflow?.name || `Workflow ${workflowId}`;
    } catch (error) {
      return `Workflow ${workflowId}`;
    }
  }

  /**
   * Get workflow statistics for a tenant
   */
  async getWorkflowStats(
    tenantId: string,
    options: {
      startDate?: Date;
      endDate?: Date;
      workflowId?: string;
    } = {}
  ): Promise<{
    totalExecutions: number;
    successfulExecutions: number;
    failedExecutions: number;
    averageDuration: number;
    successRate: number;
    mostActiveWorkflows: Array<{ workflowId: string; executions: number }>;
  }> {
    const { startDate, endDate, workflowId } = options;

    const where: any = {
      tenantId,
      eventType: { in: ['workflow_completed', 'workflow_failed'] },
      ...(startDate && { createdAt: { gte: startDate } }),
      ...(endDate && { createdAt: { lte: endDate } }),
      ...(workflowId && { workflowId }),
    };

    const [
      totalExecutions,
      successfulExecutions,
      failedExecutions,
      avgDurationResult,
      mostActiveWorkflows
    ] = await Promise.all([
      prisma.workflowLog.count({ where }),
      prisma.workflowLog.count({ where: { ...where, eventType: 'workflow_completed' } }),
      prisma.workflowLog.count({ where: { ...where, eventType: 'workflow_failed' } }),
      prisma.workflowLog.aggregate({
        where: { ...where, duration: { not: null } },
        _avg: { duration: true }
      }),
      prisma.workflowLog.groupBy({
        by: ['workflowId'],
        where,
        _count: { workflowId: true },
        orderBy: { _count: { workflowId: 'desc' } },
        take: 5
      })
    ]);

    const successRate = totalExecutions > 0 ? (successfulExecutions / totalExecutions) * 100 : 0;
    const averageDuration = avgDurationResult._avg.duration || 0;

    return {
      totalExecutions,
      successfulExecutions,
      failedExecutions,
      averageDuration: Math.round(averageDuration),
      successRate: Math.round(successRate * 100) / 100,
      mostActiveWorkflows: mostActiveWorkflows.map(item => ({
        workflowId: item.workflowId,
        executions: item._count.workflowId
      }))
    };
  }
}

// Export singleton instance
export const processAutomationNotificationService = new ProcessAutomationNotificationService();
