import { DocumentTemplateWithRelations } from './document-template-management';

export interface TemplateGenerationRequest {
  templateId: string;
  data: Record<string, any>;
  options?: {
    format?: 'html' | 'pdf';
    includeStyles?: boolean;
    responsive?: boolean;
  };
}

export interface TemplateGenerationResponse {
  html: string;
  metadata: {
    generatedAt: Date;
    templateId: string;
    placeholdersReplaced: number;
    totalPlaceholders: number;
  };
}

export class HTMLTemplateGeneratorService {
  /**
   * Generate HTML from template with data replacement
   */
  async generateHTML(
    template: DocumentTemplateWithRelations,
    data: Record<string, any>,
    options: TemplateGenerationRequest['options'] = {}
  ): Promise<TemplateGenerationResponse> {
    const startTime = Date.now();

    try {
      if (!template.htmlTemplate) {
        throw new Error('Template does not have HTML content');
      }

      // Replace placeholders with actual data
      const { html, replacedCount, totalCount } = this.replacePlaceholders(
        template.htmlTemplate,
        data
      );

      // Apply additional processing based on options
      const processedHtml = this.processHtmlOptions(html, options);

      return {
        html: processedHtml,
        metadata: {
          generatedAt: new Date(),
          templateId: template.id,
          placeholdersReplaced: replacedCount,
          totalPlaceholders: totalCount
        }
      };

    } catch (error) {
      console.error('HTML generation error:', error);
      throw new Error(`Failed to generate HTML: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Replace placeholders in HTML template with actual data
   */
  private replacePlaceholders(
    html: string,
    data: Record<string, any>
  ): { html: string; replacedCount: number; totalCount: number } {
    let processedHtml = html;
    let replacedCount = 0;

    // Find all placeholders in the format {{placeholder_name}}
    const placeholderRegex = /\{\{([^}]+)\}\}/g;
    const placeholders = [...html.matchAll(placeholderRegex)];
    const totalCount = placeholders.length;

    // Replace each placeholder
    for (const match of placeholders) {
      const fullMatch = match[0]; // {{placeholder_name}}
      const placeholderKey = match[1].trim(); // placeholder_name
      
      // Get the mapping info for this placeholder
      const mapping = mappings[fullMatch] || mappings[`{{${placeholderKey}}}`];
      
      // Get the replacement value
      const replacementValue = this.getReplacementValue(placeholderKey, data, mapping);
      
      if (replacementValue !== null) {
        processedHtml = processedHtml.replace(fullMatch, replacementValue);
        replacedCount++;
      }
    }

    return { html: processedHtml, replacedCount, totalCount };
  }

  /**
   * Get replacement value for a placeholder
   */
  private getReplacementValue(
    placeholderKey: string,
    data: Record<string, any>,
    mapping?: any
  ): string | null {
    // Handle nested object access (e.g., "company.name")
    const keys = placeholderKey.split('.');
    let value = data;

    for (const key of keys) {
      if (value && typeof value === 'object' && key in value) {
        value = value[key];
      } else {
        value = null;
        break;
      }
    }

    // Handle different data types based on mapping
    if (value !== null && mapping) {
      switch (mapping.type) {
        case 'image':
          return this.processImageValue(value, mapping);
        case 'table':
          return this.processTableValue(value, mapping);
        case 'list':
          return this.processListValue(value, mapping);
        case 'text':
        default:
          return this.processTextValue(value, mapping);
      }
    }

    // Default text processing
    return value !== null ? String(value) : null;
  }

  /**
   * Process image placeholder values
   */
  private processImageValue(value: any, mapping: any): string {
    if (typeof value === 'string') {
      // If it's a URL, use it directly
      if (value.startsWith('http') || value.startsWith('/')) {
        return `<img src="${value}" alt="${mapping.description || 'Image'}" class="max-w-full h-auto">`;
      }
      // Otherwise, treat as placeholder text for placehold.co
      const dimensions = mapping.dimensions || { width: 400, height: 300 };
      return `<img src="https://placehold.co/${dimensions.width}x${dimensions.height}/4f46e5/ffffff?text=${encodeURIComponent(value)}" alt="${mapping.description || 'Image'}" class="max-w-full h-auto">`;
    }
    
    // Fallback placeholder
    const dimensions = mapping.dimensions || { width: 400, height: 300 };
    return `<img src="https://placehold.co/${dimensions.width}x${dimensions.height}/e5e7eb/6b7280?text=Image+Placeholder" alt="${mapping.description || 'Image'}" class="max-w-full h-auto">`;
  }

  /**
   * Process table placeholder values
   */
  private processTableValue(value: any, mapping: any): string {
    if (!Array.isArray(value)) {
      return '<p class="text-gray-500 italic">No table data provided</p>';
    }

    const headers = mapping.headers || Object.keys(value[0] || {});
    
    let tableHtml = '<table class="w-full border-collapse border border-gray-300">';
    
    // Add headers
    if (headers.length > 0) {
      tableHtml += '<thead><tr>';
      headers.forEach((header: string) => {
        tableHtml += `<th class="border border-gray-300 px-4 py-2 bg-gray-50 font-semibold text-left">${header}</th>`;
      });
      tableHtml += '</tr></thead>';
    }
    
    // Add rows
    tableHtml += '<tbody>';
    value.forEach((row: any) => {
      tableHtml += '<tr>';
      headers.forEach((header: string) => {
        const cellValue = row[header] || row[header.toLowerCase()] || '';
        tableHtml += `<td class="border border-gray-300 px-4 py-2">${cellValue}</td>`;
      });
      tableHtml += '</tr>';
    });
    tableHtml += '</tbody></table>';
    
    return tableHtml;
  }

  /**
   * Process list placeholder values
   */
  private processListValue(value: any, mapping: any): string {
    if (!Array.isArray(value)) {
      return '<p class="text-gray-500 italic">No list data provided</p>';
    }

    let listHtml = '<ul class="list-disc list-inside space-y-2">';
    value.forEach((item: any) => {
      const itemText = typeof item === 'string' ? item : JSON.stringify(item);
      listHtml += `<li class="text-gray-700">${itemText}</li>`;
    });
    listHtml += '</ul>';
    
    return listHtml;
  }

  /**
   * Process text placeholder values
   */
  private processTextValue(value: any, mapping: any): string {
    const textValue = String(value);
    
    // Apply any text formatting based on mapping
    if (mapping.format) {
      switch (mapping.format) {
        case 'currency':
          return new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(Number(textValue) || 0);
        case 'date':
          return new Date(textValue).toLocaleDateString();
        case 'uppercase':
          return textValue.toUpperCase();
        case 'lowercase':
          return textValue.toLowerCase();
        case 'capitalize':
          return textValue.charAt(0).toUpperCase() + textValue.slice(1).toLowerCase();
        default:
          return textValue;
      }
    }
    
    return textValue;
  }

  /**
   * Apply additional processing options to HTML
   */
  private processHtmlOptions(html: string, options: TemplateGenerationRequest['options'] = {}): string {
    let processedHtml = html;

    // Add responsive meta tags if not present and responsive is enabled
    if (options.responsive !== false && !html.includes('viewport')) {
      processedHtml = processedHtml.replace(
        '<head>',
        '<head>\n    <meta name="viewport" content="width=device-width, initial-scale=1.0">'
      );
    }

    // Add print styles if not present
    if (options.includeStyles !== false && !html.includes('@media print')) {
      const printStyles = `
    <style>
        @media print {
            .page-break { page-break-after: always; }
            .no-print { display: none; }
            body { -webkit-print-color-adjust: exact; }
        }
    </style>`;
      
      processedHtml = processedHtml.replace('</head>', `${printStyles}\n</head>`);
    }

    return processedHtml;
  }

  /**
   * Validate template data against placeholder mappings
   */
  validateTemplateData(
    data: Record<string, any>,
    mappings: Record<string, any>
  ): { isValid: boolean; missingRequired: string[]; errors: string[] } {
    const missingRequired: string[] = [];
    const errors: string[] = [];

    for (const [placeholder, mapping] of Object.entries(mappings)) {
      if (mapping.required) {
        const placeholderKey = placeholder.replace(/[{}]/g, '');
        const keys = placeholderKey.split('.');
        let value = data;

        for (const key of keys) {
          if (value && typeof value === 'object' && key in value) {
            value = value[key];
          } else {
            value = null;
            break;
          }
        }

        if (value === null || value === undefined || value === '') {
          missingRequired.push(placeholder);
        }
      }
    }

    return {
      isValid: missingRequired.length === 0 && errors.length === 0,
      missingRequired,
      errors
    };
  }
}

export const htmlTemplateGeneratorService = new HTMLTemplateGeneratorService();
