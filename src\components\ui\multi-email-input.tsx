'use client';

import React, { useState, useRef, useEffect } from 'react';
import { X, Mail, User, Building } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';

interface Contact {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  company?: {
    name: string;
  };
}

interface MultiEmailInputProps {
  value: string[];
  onChange: (emails: string[]) => void;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
  error?: string;
}

export function MultiEmailInput({
  value = [],
  onChange,
  placeholder = "Enter email addresses...",
  className,
  disabled = false,
  error
}: MultiEmailInputProps) {
  const [inputValue, setInputValue] = useState('');
  const [suggestions, setSuggestions] = useState<Contact[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(-1);
  const [isSearching, setIsSearching] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);
  const suggestionsRef = useRef<HTMLDivElement>(null);

  // Search for contacts when input changes
  useEffect(() => {
    const searchContacts = async () => {
      if (inputValue.length < 2) {
        setSuggestions([]);
        setShowSuggestions(false);
        return;
      }

      setIsSearching(true);
      try {
        const response = await fetch(`/api/contacts/search?q=${encodeURIComponent(inputValue)}`);
        if (response.ok) {
          const data = await response.json();
          if (data.success && data.data?.contacts) {
            // Filter out contacts that are already selected
            const filteredContacts = data.data.contacts.filter((contact: Contact) => 
              contact.email && !value.includes(contact.email)
            );
            setSuggestions(filteredContacts);
            setShowSuggestions(filteredContacts.length > 0);
          }
        }
      } catch (error) {
        console.error('Error searching contacts:', error);
      } finally {
        setIsSearching(false);
      }
    };

    const debounceTimer = setTimeout(searchContacts, 300);
    return () => clearTimeout(debounceTimer);
  }, [inputValue, value]);

  // Handle keyboard navigation
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      if (selectedIndex >= 0 && suggestions[selectedIndex]) {
        addEmailFromContact(suggestions[selectedIndex]);
      } else if (inputValue.trim()) {
        addEmailFromInput();
      }
    } else if (e.key === 'ArrowDown') {
      e.preventDefault();
      setSelectedIndex(prev => 
        prev < suggestions.length - 1 ? prev + 1 : prev
      );
    } else if (e.key === 'ArrowUp') {
      e.preventDefault();
      setSelectedIndex(prev => prev > 0 ? prev - 1 : -1);
    } else if (e.key === 'Escape') {
      setShowSuggestions(false);
      setSelectedIndex(-1);
    } else if (e.key === 'Backspace' && !inputValue && value.length > 0) {
      // Remove last email when backspace is pressed on empty input
      removeEmail(value[value.length - 1]);
    } else if (e.key === ',' || e.key === ';' || e.key === ' ') {
      e.preventDefault();
      if (inputValue.trim()) {
        addEmailFromInput();
      }
    }
  };

  // Add email from contact selection
  const addEmailFromContact = (contact: Contact) => {
    if (contact.email && !value.includes(contact.email)) {
      onChange([...value, contact.email]);
      setInputValue('');
      setShowSuggestions(false);
      setSelectedIndex(-1);
      inputRef.current?.focus();
    }
  };

  // Add email from manual input
  const addEmailFromInput = () => {
    const email = inputValue.trim();
    if (email && isValidEmail(email) && !value.includes(email)) {
      onChange([...value, email]);
      setInputValue('');
      setShowSuggestions(false);
      setSelectedIndex(-1);
    }
  };

  // Remove email
  const removeEmail = (emailToRemove: string) => {
    onChange(value.filter(email => email !== emailToRemove));
  };

  // Email validation
  const isValidEmail = (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  // Handle input blur
  const handleBlur = (e: React.FocusEvent) => {
    // Delay hiding suggestions to allow for click events
    setTimeout(() => {
      if (!suggestionsRef.current?.contains(e.relatedTarget as Node)) {
        setShowSuggestions(false);
        setSelectedIndex(-1);
        
        // Add email if valid when losing focus
        if (inputValue.trim() && isValidEmail(inputValue.trim())) {
          addEmailFromInput();
        }
      }
    }, 150);
  };

  return (
    <div className={cn("relative", className)}>
      <div
        className={cn(
          "flex flex-wrap gap-1 p-2 border rounded-md bg-background min-h-[40px] cursor-text",
          error && "border-destructive",
          disabled && "opacity-50 cursor-not-allowed",
          "focus-within:ring-2 focus-within:ring-ring focus-within:ring-offset-2"
        )}
        onClick={() => inputRef.current?.focus()}
      >
        {/* Email badges */}
        {value.map((email) => (
          <Badge
            key={email}
            variant="secondary"
            className="flex items-center gap-1 px-2 py-1"
          >
            <Mail className="w-3 h-3" />
            <span className="text-xs">{email}</span>
            {!disabled && (
              <X
                className="w-3 h-3 cursor-pointer hover:text-destructive"
                onClick={(e) => {
                  e.stopPropagation();
                  removeEmail(email);
                }}
              />
            )}
          </Badge>
        ))}
        
        {/* Input field */}
        <input
          ref={inputRef}
          type="text"
          value={inputValue}
          onChange={(e) => setInputValue(e.target.value)}
          onKeyDown={handleKeyDown}
          onBlur={handleBlur}
          onFocus={() => {
            if (suggestions.length > 0) {
              setShowSuggestions(true);
            }
          }}
          placeholder={value.length === 0 ? placeholder : ""}
          disabled={disabled}
          className="flex-1 min-w-[120px] bg-transparent border-none outline-none text-sm placeholder:text-muted-foreground"
        />
      </div>

      {/* Error message */}
      {error && (
        <p className="text-sm text-destructive mt-1">{error}</p>
      )}

      {/* Suggestions dropdown */}
      {showSuggestions && suggestions.length > 0 && (
        <div
          ref={suggestionsRef}
          className="absolute z-50 w-full mt-1 bg-popover border rounded-md shadow-lg max-h-60 overflow-y-auto"
        >
          {suggestions.map((contact, index) => (
            <div
              key={contact.id}
              className={cn(
                "flex items-center gap-3 px-3 py-2 cursor-pointer hover:bg-accent",
                selectedIndex === index && "bg-accent"
              )}
              onClick={() => addEmailFromContact(contact)}
            >
              <div className="flex items-center gap-2 flex-1">
                <User className="w-4 h-4 text-muted-foreground" />
                <div className="flex-1">
                  <div className="text-sm font-medium">
                    {contact.firstName} {contact.lastName}
                  </div>
                  <div className="text-xs text-muted-foreground">
                    {contact.email}
                  </div>
                </div>
                {contact.company && (
                  <div className="flex items-center gap-1 text-xs text-muted-foreground">
                    <Building className="w-3 h-3" />
                    {contact.company.name}
                  </div>
                )}
              </div>
            </div>
          ))}
          
          {/* Add manual email option */}
          {inputValue.trim() && isValidEmail(inputValue.trim()) && !value.includes(inputValue.trim()) && (
            <div
              className={cn(
                "flex items-center gap-3 px-3 py-2 cursor-pointer hover:bg-accent border-t",
                selectedIndex === suggestions.length && "bg-accent"
              )}
              onClick={addEmailFromInput}
            >
              <Mail className="w-4 h-4 text-muted-foreground" />
              <div className="text-sm">
                Add "{inputValue.trim()}"
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
