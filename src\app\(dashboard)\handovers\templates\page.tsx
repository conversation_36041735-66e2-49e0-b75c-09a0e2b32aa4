'use client';

import HandoverTemplateManagement from '@/components/handovers/HandoverTemplateManagement';
import { PermissionGate } from '@/components/ui/permission-gate';
import { PermissionResource, PermissionAction } from '@/types';
import { Card, CardContent } from '@/components/ui/card';
import { EmptyState } from '@/components/ui/empty-state';
import { ExclamationTriangleIcon, PlusIcon, ArrowLeftIcon } from '@heroicons/react/24/outline';
import { PageLayout } from '@/components/layout/page-layout';
import { Button } from '@/components/ui/button';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import HandoverTemplateDialog from '@/components/handovers/HandoverTemplateDialog';

export default function HandoverTemplatesPage() {
  const router = useRouter();
  const [templateDialogOpen, setTemplateDialogOpen] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState(null);
  const [refreshKey, setRefreshKey] = useState(0);

  // Handle create template
  const handleCreateTemplate = () => {
    setSelectedTemplate(null);
    setTemplateDialogOpen(true);
  };

  // Handle edit template
  const handleEditTemplate = (template: any) => {
    setSelectedTemplate(template);
    setTemplateDialogOpen(true);
  };

  // Handle template dialog success
  const handleTemplateDialogSuccess = () => {
    setTemplateDialogOpen(false);
    setSelectedTemplate(null);
    // Trigger a refresh of the templates list
    setRefreshKey(prev => prev + 1);
  };

  // Page actions
  const actions = (
    <div className="flex items-center gap-2">
      <Button variant="outline" size="sm" onClick={() => router.push('/handovers')}>
        <ArrowLeftIcon className="h-4 w-4 mr-2" />
        Back to Handovers
      </Button>
      <Button size="sm" onClick={handleCreateTemplate}>
        <PlusIcon className="h-4 w-4 mr-2" />
        Create Template
      </Button>
    </div>
  );

  // TODO: Re-enable permission gate after running permission seeding
  // The HANDOVERS permissions need to be seeded in the database first
  // Run: node scripts/seed-permissions.js

  return (
    <>
      <PageLayout
        title="Handover Templates"
        description="Manage handover process templates and checklists"
        actions={actions}
      >
        <HandoverTemplateManagement
          key={refreshKey}
          onCreateTemplate={handleCreateTemplate}
          onEditTemplate={handleEditTemplate}
        />
      </PageLayout>

      {/* Template Dialog */}
      <HandoverTemplateDialog
        open={templateDialogOpen}
        onOpenChange={setTemplateDialogOpen}
        template={selectedTemplate}
        onSuccess={handleTemplateDialogSuccess}
      />
    </>
  );

  // Uncomment this after seeding handover permissions:
  /*
  return (
    <PermissionGate
      resource={PermissionResource.HANDOVERS}
      action={PermissionAction.READ}
      fallback={
        <PageLayout>
          <Card>
            <CardContent className="p-8">
              <EmptyState
                icon={<ExclamationTriangleIcon className="w-12 h-12 text-muted-foreground" />}
                title="Access Denied"
                description="You don't have permission to view handover templates. Please contact your administrator for access."
                action={{
                  label: 'Back to Handovers',
                  onClick: () => window.location.href = '/handovers',
                  variant: 'primary'
                }}
              />
            </CardContent>
          </Card>
        </PageLayout>
      }
    >
      <PageLayout
        title="Handover Templates"
        description="Manage handover process templates and checklists"
        actions={actions}
      >
        <HandoverTemplateManagement
          key={refreshKey}
          onCreateTemplate={handleCreateTemplate}
          onEditTemplate={handleEditTemplate}
        />
      </PageLayout>
    </PermissionGate>
  );
  */
}


