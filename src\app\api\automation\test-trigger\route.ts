import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { OpportunityTriggersService } from '@/services/opportunity-triggers';
import { z } from 'zod';

const testTriggerSchema = z.object({
  opportunityId: z.string().min(1, 'Opportunity ID is required'),
  newStatus: z.string().min(1, 'New status is required'),
  oldStatus: z.string().optional()
});

/**
 * POST /api/automation/test-trigger
 * Manually trigger automation for testing purposes
 */
export async function POST(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user's current tenant
    const currentTenant = session.user.tenants?.[0];
    if (!currentTenant) {
      return NextResponse.json({ error: 'No tenant access' }, { status: 403 });
    }

    const body = await req.json();
    const { opportunityId, newStatus, oldStatus } = testTriggerSchema.parse(body);

    // Get the opportunity to verify it exists and get current status
    const opportunity = await prisma.opportunity.findUnique({
      where: { 
        id: opportunityId,
        tenantId: currentTenant.id 
      },
      select: { id: true, title: true, stage: true, tenantId: true }
    });

    if (!opportunity) {
      return NextResponse.json(
        { error: 'Opportunity not found' },
        { status: 404 }
      );
    }

    const actualOldStatus = oldStatus || opportunity.stage;

    // Check if automation tables exist
    try {
      await prisma.automationTrigger.count({ where: { tenantId: currentTenant.id } });
    } catch (error) {
      return NextResponse.json(
        { 
          error: 'Automation tables not found',
          message: 'Please run database migration to create automation tables',
          details: 'Run: npm run db:push or npx prisma db push'
        },
        { status: 503 }
      );
    }

    // Check for active triggers
    const triggers = await prisma.automationTrigger.findMany({
      where: {
        tenantId: currentTenant.id,
        triggerType: 'opportunity_status_change',
        isActive: true
      }
    });

    if (triggers.length === 0) {
      return NextResponse.json(
        {
          error: 'No automation triggers found',
          message: 'No active automation triggers configured for this tenant',
          details: 'Run the setup script to create default triggers'
        },
        { status: 404 }
      );
    }

    // Execute the automation trigger
    const triggersService = new OpportunityTriggersService();
    
    const statusChange = {
      opportunityId: opportunity.id,
      tenantId: currentTenant.id,
      oldStatus: actualOldStatus,
      newStatus: newStatus,
      changedBy: session.user.id,
      changedAt: new Date(),
      metadata: {
        triggeredBy: 'manual_test',
        userAgent: req.headers.get('user-agent'),
        timestamp: new Date().toISOString()
      }
    };

    console.log(`🧪 Manual automation test: ${statusChange.oldStatus} → ${statusChange.newStatus} for opportunity ${opportunity.title}`);

    await triggersService.handleOpportunityStatusChange(statusChange);

    // Get execution results
    const executions = await prisma.automationExecution.findMany({
      where: {
        tenantId: currentTenant.id,
        entityType: 'opportunity',
        entityId: opportunity.id
      },
      include: { trigger: true },
      orderBy: { startedAt: 'desc' },
      take: 5
    });

    // Check for created handovers
    const handovers = await prisma.projectHandover.findMany({
      where: {
        tenantId: currentTenant.id,
        opportunityId: opportunity.id
      },
      select: { id: true, title: true, status: true, createdAt: true }
    });

    return NextResponse.json({
      success: true,
      message: 'Automation trigger executed successfully',
      data: {
        opportunity: {
          id: opportunity.id,
          title: opportunity.title,
          statusChange: `${actualOldStatus} → ${newStatus}`
        },
        triggers: {
          total: triggers.length,
          executed: triggers.filter(t => {
            const conditions = t.triggerConditions as any;
            return conditions.toStatus === newStatus || 
                   (conditions.fromStatus && conditions.fromStatus === actualOldStatus);
          }).length
        },
        executions: executions.map(exec => ({
          id: exec.id,
          triggerName: exec.trigger.name,
          status: exec.status,
          startedAt: exec.startedAt,
          completedAt: exec.completedAt,
          actionsExecuted: (exec.executedActions as any[])?.length || 0
        })),
        handovers: handovers.map(handover => ({
          id: handover.id,
          title: handover.title,
          status: handover.status,
          createdAt: handover.createdAt
        }))
      }
    });

  } catch (error) {
    console.error('Error testing automation trigger:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request data', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { 
        error: 'Failed to test automation trigger',
        details: process.env.NODE_ENV === 'development' ? error.message : undefined
      },
      { status: 500 }
    );
  }
}

/**
 * GET /api/automation/test-trigger
 * Get information about available test opportunities
 */
export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const currentTenant = session.user.tenants?.[0];
    if (!currentTenant) {
      return NextResponse.json({ error: 'No tenant access' }, { status: 403 });
    }

    // Get test opportunities
    const opportunities = await prisma.opportunity.findMany({
      where: { 
        tenantId: currentTenant.id,
        stage: { not: 'Closed Won' }
      },
      select: { 
        id: true, 
        title: true, 
        stage: true, 
        value: true,
        contact: { select: { firstName: true, lastName: true } },
        company: { select: { name: true } }
      },
      take: 10,
      orderBy: { updatedAt: 'desc' }
    });

    // Get available triggers
    const triggers = await prisma.automationTrigger.findMany({
      where: {
        tenantId: currentTenant.id,
        triggerType: 'opportunity_status_change',
        isActive: true
      },
      select: { 
        id: true, 
        name: true, 
        triggerConditions: true,
        actions: true
      }
    });

    return NextResponse.json({
      success: true,
      data: {
        opportunities: opportunities.map(opp => ({
          id: opp.id,
          title: opp.title,
          currentStage: opp.stage,
          value: opp.value,
          contact: opp.contact ? `${opp.contact.firstName} ${opp.contact.lastName}` : null,
          company: opp.company?.name || null
        })),
        triggers: triggers.map(trigger => ({
          id: trigger.id,
          name: trigger.name,
          conditions: trigger.triggerConditions,
          actionsCount: (trigger.actions as any[])?.length || 0
        })),
        suggestedTests: [
          { fromStatus: 'Negotiation', toStatus: 'Closed Won', description: 'Test handover creation' },
          { fromStatus: 'Qualification', toStatus: 'Proposal', description: 'Test proposal notifications' },
          { fromStatus: 'Proposal', toStatus: 'Negotiation', description: 'Test negotiation alerts' }
        ]
      }
    });

  } catch (error) {
    console.error('Error getting test trigger info:', error);
    return NextResponse.json(
      { error: 'Failed to get test information' },
      { status: 500 }
    );
  }
}
