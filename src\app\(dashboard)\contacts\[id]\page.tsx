'use client';

import React, { useState, useEffect, use } from 'react';
import { useRouter } from 'next/navigation';
import { motion } from 'framer-motion';
import {
  ArrowLeft,
  Edit,
  Trash2,
  Mail,
  Phone,
  Building,
  Calendar,
  TrendingUp,
  Globe,
  MapPin
} from 'lucide-react';
import { PermissionGate } from '@/components/ui/permission-gate';
import { PermissionResource, PermissionAction } from '@/types';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { EmptyState } from '@/components/ui/empty-state';
import { FormPageSkeleton } from '@/components/ui/skeleton-loaders';
import { ExclamationTriangleIcon } from '@heroicons/react/24/outline';
import { ContactAvatar, UserAvatar } from '@/components/ui/contact-avatar';
import { ContactWithRelations } from '@/services/contact-management';
import { PageLayout } from '@/components/layout/page-layout';
import { ActivityTimeline } from '@/components/contacts/activity-timeline';
import { useEnhancedDeleteDialog, deleteConfigurations, RelatedData } from '@/components/ui/enhanced-delete-dialog';
import { ContactQuickActions } from '@/components/contacts/contact-quick-actions';

interface ContactDetailPageProps {
  params: Promise<{
    id: string;
  }>;
}

export default function ContactDetailPage({ params }: ContactDetailPageProps) {
  const router = useRouter();
  const resolvedParams = use(params);
  const contactId = resolvedParams.id;
  
  const [contact, setContact] = useState<ContactWithRelations | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Enhanced delete dialog
  const { showDeleteDialog, DialogComponent } = useEnhancedDeleteDialog();

  // Fetch contact data
  useEffect(() => {
    const fetchContact = async () => {
      try {
        setLoading(true);
        const response = await fetch(`/api/contacts/${contactId}`);
        const data = await response.json();

        if (data.success) {
          setContact(data.data.contact);
        } else {
          setError(data.error || 'Contact not found');
        }
      } catch (err) {
        console.error('Error fetching contact:', err);
        setError('Failed to load contact details');
      } finally {
        setLoading(false);
      }
    };

    if (contactId) {
      fetchContact();
    }
  }, [contactId]);

  const handleDelete = async () => {
    if (!contact) return;

    const contactName = `${contact.firstName} ${contact.lastName}`;

    // Prepare related data information
    const relatedData: RelatedData[] = [];

    if (contact.company) {
      relatedData.push({
        type: 'companies',
        count: 1,
        label: 'company association',
        description: `will lose association with ${contact.company.name}`,
      });
    }

    await showDeleteDialog({
      ...deleteConfigurations.contact(contactName, relatedData),
      onConfirm: async () => {
        try {
          const response = await fetch(`/api/contacts?contactId=${contact.id}`, {
            method: 'DELETE',
          });

          const data = await response.json();

          if (data.success) {
            router.push('/contacts');
          } else {
            throw new Error(data.error || 'Failed to delete contact');
          }
        } catch (err) {
          console.error('Error deleting contact:', err);
          throw err;
        }
      },
    });
  };

  // Handle create lead
  const handleCreateLead = () => {
    if (!contact) return;

    // Navigate to create lead page with contact pre-filled
    const params = new URLSearchParams({
      contactId: contact.id,
      contactName: `${contact.firstName} ${contact.lastName}`,
      ...(contact.companyId && { companyId: contact.companyId }),
      ...(contact.email && { email: contact.email }),
      ...(contact.phone && { phone: contact.phone })
    });
    router.push(`/leads/new?${params.toString()}`);
  };

  // Page actions
  const actions = contact ? [
    <Button
      key="back"
      variant="outline"
      onClick={() => router.push('/contacts')}
      className="gap-2"
    >
      <ArrowLeft className="w-4 h-4" />
      Back to Contacts
    </Button>,
    <ContactQuickActions
      key="quick-actions"
      contactId={contact.id}
      contactData={contact}
      onCreateLeadClick={handleCreateLead}
      onEditClick={() => router.push(`/contacts/${contact.id}/edit`)}
      onDeleteClick={handleDelete}
    />
  ] : [
    <Button
      key="back"
      variant="outline"
      onClick={() => router.push('/contacts')}
      className="gap-2"
    >
      <ArrowLeft className="w-4 h-4" />
      Back to Contacts
    </Button>,
  ];

  if (loading) {
    return (
      <PageLayout
        title="Contact Details"
        description="View contact information and related data"
        actions={actions}
      >
        <FormPageSkeleton />
      </PageLayout>
    );
  }

  if (error || !contact) {
    return (
      <PageLayout
        title="Contact Details"
        description="View contact information and related data"
        actions={actions}
      >
        <Card>
          <CardContent className="p-8">
            <EmptyState
              icon={<ExclamationTriangleIcon className="w-12 h-12 text-muted-foreground" />}
              title="Contact Not Found"
              description={error || "The contact you're looking for doesn't exist or you don't have permission to view it."}
              action={{
                label: 'Back to Contacts',
                onClick: () => router.push('/contacts'),
                variant: 'primary'
              }}
            />
          </CardContent>
        </Card>
      </PageLayout>
    );
  }

  return (
    <PermissionGate
      resource={PermissionResource.CONTACTS}
      action={PermissionAction.READ}
      fallback={
        <PageLayout
          title="Contact Details"
          description="View contact information and related data"
          actions={actions}
        >
          <Card>
            <CardContent className="p-8">
              <EmptyState
                icon={<ExclamationTriangleIcon className="w-12 h-12 text-muted-foreground" />}
                title="Access Denied"
                description="You don't have permission to view this contact. Please contact your administrator for access."
                action={{
                  label: 'Back to Contacts',
                  onClick: () => router.push('/contacts'),
                  variant: 'primary'
                }}
              />
            </CardContent>
          </Card>
        </PageLayout>
      }
    >
      <PageLayout
        title={`${contact.firstName} ${contact.lastName}`}
        description="Contact details and activity history"
        actions={actions}
      >

        <motion.div
          className="space-y-6"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          {/* Contact Overview */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-3">
                <ContactAvatar
                  firstName={contact.firstName}
                  lastName={contact.lastName}
                  size="xl"
                />
                <div>
                  <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
                    {contact.firstName} {contact.lastName}
                  </h2>
                  <div className="space-y-1">
                    {contact.jobTitle && (
                      <p className="text-sm text-gray-600 dark:text-gray-300">
                        {contact.jobTitle}
                      </p>
                    )}
                    {contact.company && (
                      <p className="text-sm text-gray-500 dark:text-gray-400 flex items-center gap-1">
                        <Building className="w-4 h-4" />
                        {contact.company.name}
                      </p>
                    )}
                  </div>
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">

                  {contact.email && (
                    <div>
                      <label className="text-sm font-medium text-gray-500 dark:text-gray-400">
                        Email Address
                      </label>
                      <div className="flex items-center gap-2">
                        <Mail className="w-4 h-4 text-gray-400" />
                        <a
                          href={`mailto:${contact.email}`}
                          className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
                        >
                          {contact.email}
                        </a>
                      </div>
                    </div>
                  )}

                  {contact.phone && (
                    <div>
                      <label className="text-sm font-medium text-gray-500 dark:text-gray-400">
                        Phone Number
                      </label>
                      <div className="flex items-center gap-2">
                        <Phone className="w-4 h-4 text-gray-400" />
                        <a
                          href={`tel:${contact.phone}`}
                          className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
                        >
                          {contact.phone}
                        </a>
                      </div>
                    </div>
                  )}

                  {contact.website && (
                    <div>
                      <label className="text-sm font-medium text-gray-500 dark:text-gray-400">
                        Website
                      </label>
                      <div className="flex items-center gap-2">
                        <Globe className="w-4 h-4 text-gray-400" />
                        <a
                          href={contact.website}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
                        >
                          {contact.website}
                        </a>
                      </div>
                    </div>
                  )}

                  {contact.address && (
                    <div>
                      <label className="text-sm font-medium text-gray-500 dark:text-gray-400">
                        Address
                      </label>
                      <div className="flex items-start gap-2">
                        <MapPin className="w-4 h-4 text-gray-400 mt-0.5" />
                        <p className="text-gray-900 dark:text-gray-100">
                          {contact.address}
                        </p>
                      </div>
                    </div>
                  )}
                </div>

                <div className="space-y-4">
                  {contact.company && (
                    <div>
                      <label className="text-sm font-medium text-gray-500 dark:text-gray-400">
                        Company
                      </label>
                      <div className="flex items-center gap-2">
                        <Building className="w-4 h-4 text-gray-400" />
                        <p className="text-gray-900 dark:text-gray-100">
                          {contact.company.name}
                        </p>
                      </div>
                    </div>
                  )}

                  {contact.owner && (
                    <div>
                      <label className="text-sm font-medium text-gray-500 dark:text-gray-400">
                        Owner
                      </label>
                      <div className="mt-2">
                        <UserAvatar
                          user={contact.owner}
                          size="md"
                          showEmail
                        />
                      </div>
                    </div>
                  )}

                  <div>
                    <label className="text-sm font-medium text-gray-500 dark:text-gray-400">
                      Created
                    </label>
                    <div className="flex items-center gap-2">
                      <Calendar className="w-4 h-4 text-gray-400" />
                      <p className="text-gray-900 dark:text-gray-100">
                        {new Date(contact.createdAt).toLocaleDateString()}
                      </p>
                    </div>
                  </div>

                  <div>
                    <label className="text-sm font-medium text-gray-500 dark:text-gray-400">
                      Last Updated
                    </label>
                    <div className="flex items-center gap-2">
                      <Calendar className="w-4 h-4 text-gray-400" />
                      <p className="text-gray-900 dark:text-gray-100">
                        {new Date(contact.updatedAt).toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>



          {/* Activity Timeline */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-3">
                <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                  <TrendingUp className="w-5 h-5 text-green-600" />
                </div>
                Activity Timeline
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ActivityTimeline
                relatedToType="contact"
                relatedToId={contact.id}
              />
            </CardContent>
          </Card>
        </motion.div>

        {/* Enhanced Delete Dialog */}
        <DialogComponent />
      </PageLayout>
    </PermissionGate>
  );
}
