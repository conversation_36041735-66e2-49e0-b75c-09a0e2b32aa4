import { NextResponse } from 'next/server';
import { DocumentManagementService } from '@/services/document-management';
import { FileStorageService } from '@/lib/file-storage';
import { withPermission, AuthenticatedRequest } from '@/lib/permission-middleware';
import { PermissionResource, PermissionAction } from '@/types';

const documentService = new DocumentManagementService();

/**
 * GET /api/documents/[documentId]/download
 * Download a document file
 */
export const GET = withPermission({
  resource: PermissionResource.DOCUMENTS,
  action: PermissionAction.READ,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest) => {
  try {
    const url = new URL(req.url);
    const pathSegments = url.pathname.split('/');
    const documentId = pathSegments[pathSegments.indexOf('documents') + 1];

    const document = await documentService.getDocument(documentId, req.tenantId);

    if (!document) {
      return NextResponse.json(
        { error: 'Document not found' },
        { status: 404 }
      );
    }

    // Check if file exists before trying to read it
    const fileExists = await FileStorageService.fileExists(document.filePath);
    if (!fileExists) {
      return NextResponse.json(
        { error: 'File not found on disk' },
        { status: 404 }
      );
    }

    const stream = await FileStorageService.readFile(document.filePath);

    // Set appropriate headers for file download
    const headers = new Headers();
    headers.set('Content-Type', document.mimeType || 'application/octet-stream');

    // Properly encode filename to handle Unicode characters
    const encodedFilename = encodeURIComponent(document.name);
    const asciiFilename = document.name.replace(/[^\x00-\x7F]/g, "_"); // Replace non-ASCII with underscore
    headers.set('Content-Disposition', `attachment; filename="${asciiFilename}"; filename*=UTF-8''${encodedFilename}`);
    headers.set('Content-Length', stream.length.toString());

    return new NextResponse(stream, {
      status: 200,
      headers,
    });

  } catch (error) {
    console.error('Download document error:', error);

    if (error instanceof Error) {
      // Handle specific error types
      if (error.message.includes('ENOENT') || error.message.includes('no such file')) {
        return NextResponse.json(
          { error: 'File not found on disk' },
          { status: 404 }
        );
      }

      if (error.message.includes('EACCES') || error.message.includes('permission denied')) {
        return NextResponse.json(
          { error: 'File access denied' },
          { status: 403 }
        );
      }

      if (error.message.includes('ByteString') || error.message.includes('encoding')) {
        return NextResponse.json(
          { error: 'File encoding error' },
          { status: 400 }
        );
      }

      return NextResponse.json(
        { error: error.message },
        { status: error.message === 'Document not found' ? 404 : 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
});
