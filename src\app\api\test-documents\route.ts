import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { permissionService } from '@/services/permission-service';
import { PermissionResource, PermissionAction } from '@/types';
import { prisma } from '@/lib/prisma';

export async function GET() {
  try {
    console.log('🔍 Testing document permissions...');

    // Check session
    const session = await getServerSession(authOptions);
    console.log('Session:', session ? 'Found' : 'Not found');
    
    if (!session?.user) {
      return NextResponse.json({ error: 'No session found' }, { status: 401 });
    }

    console.log('User ID:', session.user.id);
    console.log('User email:', session.user.email);

    // Get tenant context
    const tenantUser = await prisma.tenantUser.findFirst({
      where: {
        userId: session.user.id,
        status: 'active'
      },
      include: {
        tenant: true
      }
    });

    console.log('Tenant user:', tenantUser ? 'Found' : 'Not found');
    
    if (!tenantUser) {
      return NextResponse.json({ error: 'No active tenant found' }, { status: 403 });
    }

    console.log('Tenant ID:', tenantUser.tenantId);
    console.log('Tenant name:', tenantUser.tenant.name);

    // Check permission
    const hasPermission = await permissionService.hasPermission(
      session.user.id,
      tenantUser.tenantId,
      PermissionResource.DOCUMENTS,
      PermissionAction.READ
    );

    console.log('Has permission:', hasPermission);

    // Get user permissions for debugging
    const userPermissions = await permissionService.getUserEffectivePermissions(
      session.user.id,
      tenantUser.tenantId
    );

    const documentPermissions = userPermissions.effectivePermissions.filter(p => 
      p.name.startsWith('documents.')
    );

    return NextResponse.json({
      success: true,
      data: {
        userId: session.user.id,
        userEmail: session.user.email,
        tenantId: tenantUser.tenantId,
        tenantName: tenantUser.tenant.name,
        hasDocumentReadPermission: hasPermission,
        documentPermissions: documentPermissions.map(p => p.name),
        totalPermissions: userPermissions.effectivePermissions.length,
        roles: userPermissions.roles.map(r => r.name)
      }
    });

  } catch (error) {
    console.error('Test documents error:', error);
    return NextResponse.json({
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
