'use client';

import React from 'react';
import { usePermissions } from '@/components/providers/permission-provider';
import { PermissionResource, PermissionAction } from '@/types';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

/**
 * Test component to demonstrate dashboard permission filtering
 * This component shows which dashboard cards and components are visible
 * based on the current user's permissions
 */
export function DashboardPermissionTest() {
  const { canAccessResource, user } = usePermissions();

  // Define all dashboard components and their permission requirements
  const dashboardComponents = [
    {
      name: 'Revenue Card',
      type: 'card',
      permission: { resource: PermissionResource.REPORTS, action: PermissionAction.READ },
      description: 'Shows total revenue and growth metrics'
    },
    {
      name: 'Customers Card',
      type: 'card',
      permission: { resource: PermissionResource.CONTACTS, action: PermissionAction.READ },
      description: 'Shows new customer count and growth'
    },
    {
      name: 'Accounts Card',
      type: 'card',
      permission: { resource: PermissionResource.COMPANIES, action: PermissionAction.READ },
      description: 'Shows active accounts and growth'
    },
    {
      name: 'Growth Rate Card',
      type: 'card',
      permission: { resource: PermissionResource.REPORTS, action: PermissionAction.READ },
      description: 'Shows overall growth rate metrics'
    },
    {
      name: 'Analytics Chart',
      type: 'component',
      permission: { resource: PermissionResource.REPORTS, action: PermissionAction.READ },
      description: 'Displays trend charts for various metrics'
    },
    {
      name: 'Meetings Overview',
      type: 'component',
      permission: { resource: PermissionResource.MEETINGS, action: PermissionAction.READ },
      description: 'Shows upcoming and recent meetings'
    },
    {
      name: 'Communication Widget',
      type: 'component',
      permission: { resource: PermissionResource.COMMUNICATION, action: PermissionAction.READ },
      description: 'Communication tools and notifications'
    },
    {
      name: 'Activity Table',
      type: 'component',
      permission: { resource: PermissionResource.DASHBOARD, action: PermissionAction.READ },
      description: 'Shows recent tenant activities'
    }
  ];

  // Check permissions for each component
  const componentPermissions = dashboardComponents.map(component => ({
    ...component,
    hasAccess: canAccessResource(component.permission.resource, component.permission.action)
  }));

  const visibleCards = componentPermissions.filter(c => c.type === 'card' && c.hasAccess);
  const visibleComponents = componentPermissions.filter(c => c.type === 'component' && c.hasAccess);

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle>Dashboard Permission Test</CardTitle>
        <p className="text-sm text-muted-foreground">
          This shows which dashboard elements are visible based on your current permissions.
        </p>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* User Info */}
        <div className="p-4 bg-muted rounded-lg">
          <h3 className="font-semibold mb-2">Current User</h3>
          <p className="text-sm">
            <strong>Name:</strong> {user?.name || 'Unknown'}
          </p>
          <p className="text-sm">
            <strong>Email:</strong> {user?.email || 'Unknown'}
          </p>
          <p className="text-sm">
            <strong>Platform Admin:</strong> {user?.isPlatformAdmin ? 'Yes' : 'No'}
          </p>
        </div>

        {/* Dashboard Cards */}
        <div>
          <h3 className="font-semibold mb-3">Dashboard Cards ({visibleCards.length}/4 visible)</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            {componentPermissions
              .filter(c => c.type === 'card')
              .map((component, index) => (
                <div
                  key={index}
                  className={`p-3 rounded-lg border ${
                    component.hasAccess 
                      ? 'bg-green-50 border-green-200' 
                      : 'bg-red-50 border-red-200'
                  }`}
                >
                  <div className="flex items-center justify-between mb-1">
                    <span className="font-medium text-sm">{component.name}</span>
                    <Badge variant={component.hasAccess ? 'default' : 'destructive'}>
                      {component.hasAccess ? 'Visible' : 'Hidden'}
                    </Badge>
                  </div>
                  <p className="text-xs text-muted-foreground mb-2">
                    {component.description}
                  </p>
                  <p className="text-xs">
                    <strong>Required:</strong> {component.permission.resource}.{component.permission.action}
                  </p>
                </div>
              ))}
          </div>
        </div>

        {/* Dashboard Components */}
        <div>
          <h3 className="font-semibold mb-3">Dashboard Components ({visibleComponents.length}/4 visible)</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            {componentPermissions
              .filter(c => c.type === 'component')
              .map((component, index) => (
                <div
                  key={index}
                  className={`p-3 rounded-lg border ${
                    component.hasAccess 
                      ? 'bg-green-50 border-green-200' 
                      : 'bg-red-50 border-red-200'
                  }`}
                >
                  <div className="flex items-center justify-between mb-1">
                    <span className="font-medium text-sm">{component.name}</span>
                    <Badge variant={component.hasAccess ? 'default' : 'destructive'}>
                      {component.hasAccess ? 'Visible' : 'Hidden'}
                    </Badge>
                  </div>
                  <p className="text-xs text-muted-foreground mb-2">
                    {component.description}
                  </p>
                  <p className="text-xs">
                    <strong>Required:</strong> {component.permission.resource}.{component.permission.action}
                  </p>
                </div>
              ))}
          </div>
        </div>

        {/* Summary */}
        <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <h3 className="font-semibold mb-2">Summary</h3>
          <ul className="text-sm space-y-1">
            <li>• Dashboard cards will automatically hide if you don't have the required permissions</li>
            <li>• No error messages are shown - components simply don't appear</li>
            <li>• Grid layout adjusts dynamically based on visible components</li>
            <li>• Each card type requires specific resource permissions (contacts, companies, reports, etc.)</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  );
}
