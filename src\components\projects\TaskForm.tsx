'use client';

import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';

import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Loader2 } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { SearchableSelect, SearchableSelectOption } from '@/components/ui/searchable-select';
import { DateInput } from '@/components/ui/date-input';

const taskFormSchema = z.object({
  title: z.string().min(1, 'Task title is required').max(255),
  description: z.string().optional(),
  status: z.enum(['todo', 'in_progress', 'review', 'done']).default('todo'),
  priority: z.enum(['low', 'medium', 'high', 'urgent']).default('medium'),
  assigneeId: z.string().optional(),
  milestoneId: z.string().optional(),
  startDate: z.date().optional(),
  dueDate: z.date().optional(),
  estimatedHours: z.number().positive().optional(),
});

type TaskFormData = z.infer<typeof taskFormSchema>;

interface User {
  id: string;
  firstName?: string;
  lastName?: string;
  email: string;
}

interface Milestone {
  id: string;
  title: string;
  status: string;
  dueDate: string;
}

interface TaskFormProps {
  initialData?: Partial<TaskFormData & { id: string }>;
  onSubmit: (data: TaskFormData) => Promise<void>;
  onCancel: () => void;
  isLoading?: boolean;
  projectId: string;
}

export function TaskForm({
  initialData,
  onSubmit,
  onCancel,
  isLoading = false,
  projectId
}: TaskFormProps) {
  const [users, setUsers] = useState<User[]>([]);
  const [milestones, setMilestones] = useState<Milestone[]>([]);
  const [loadingUsers, setLoadingUsers] = useState(false);
  const [loadingMilestones, setLoadingMilestones] = useState(false);
  const { toast } = useToast();

  const form = useForm<TaskFormData>({
    resolver: zodResolver(taskFormSchema),
    defaultValues: {
      title: '',
      description: '',
      status: 'todo',
      priority: 'medium',
      assigneeId: '',
      milestoneId: '',
      startDate: undefined,
      dueDate: undefined,
      estimatedHours: undefined,
      ...initialData,
      // Convert string dates back to Date objects if they exist
      startDate: initialData?.startDate ? new Date(initialData.startDate as any) : undefined,
      dueDate: initialData?.dueDate ? new Date(initialData.dueDate as any) : undefined,
    },
  });

  useEffect(() => {
    fetchUsers();
    fetchMilestones();
  }, []);

  const fetchUsers = async () => {
    try {
      setLoadingUsers(true);
      const response = await fetch('/api/users');

      if (response.ok) {
        const data = await response.json();
        setUsers(data.data || []);
      }
    } catch (error) {
      console.error('Error fetching users:', error);
      toast({
        title: 'Warning',
        description: 'Failed to load users for assignment',
        variant: 'destructive',
      });
    } finally {
      setLoadingUsers(false);
    }
  };

  const fetchMilestones = async () => {
    try {
      setLoadingMilestones(true);
      const response = await fetch(`/api/projects/${projectId}/milestones`);

      if (response.ok) {
        const data = await response.json();
        setMilestones(data.data.milestones || []);
      }
    } catch (error) {
      console.error('Error fetching milestones:', error);
      toast({
        title: 'Warning',
        description: 'Failed to load milestones',
        variant: 'destructive',
      });
    } finally {
      setLoadingMilestones(false);
    }
  };

  const handleSubmit = async (data: TaskFormData) => {
    try {
      // Convert "unassigned" back to undefined for the API
      const submitData = {
        ...data,
        assigneeId: data.assigneeId === "unassigned" ? undefined : data.assigneeId,
        milestoneId: data.milestoneId === "none" ? undefined : data.milestoneId,
      };
      await onSubmit(submitData);
    } catch (error) {
      console.error('Error submitting task:', error);
      toast({
        title: 'Error',
        description: `Failed to ${initialData ? 'update' : 'create'} task`,
        variant: 'destructive',
      });
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
        {/* Task Title */}
        <FormField
          control={form.control}
          name="title"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Task Title *</FormLabel>
              <FormControl>
                <Input placeholder="Enter task title" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Description */}
        <FormField
          control={form.control}
          name="description"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Description</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="Enter task description"
                  rows={3}
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Status and Priority */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <FormField
            control={form.control}
            name="status"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Status</FormLabel>
                <FormControl>
                  <SearchableSelect
                    options={[
                      { value: "todo", label: "To Do", description: "Task not started" },
                      { value: "in_progress", label: "In Progress", description: "Task is being worked on" },
                      { value: "review", label: "Review", description: "Task ready for review" },
                      { value: "done", label: "Done", description: "Task completed" },
                    ]}
                    value={field.value}
                    onValueChange={field.onChange}
                    placeholder="Select status"
                    searchPlaceholder="Search status..."
                    emptyMessage="No status found"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="priority"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Priority</FormLabel>
                <FormControl>
                  <SearchableSelect
                    options={[
                      { value: "low", label: "Low", description: "Low priority task" },
                      { value: "medium", label: "Medium", description: "Medium priority task" },
                      { value: "high", label: "High", description: "High priority task" },
                      { value: "urgent", label: "Urgent", description: "Urgent priority task" },
                    ]}
                    value={field.value}
                    onValueChange={field.onChange}
                    placeholder="Select priority"
                    searchPlaceholder="Search priority..."
                    emptyMessage="No priority found"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        {/* Assignee */}
        <FormField
          control={form.control}
          name="assigneeId"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Assignee</FormLabel>
              <FormControl>
                <SearchableSelect
                  options={[
                    { value: "unassigned", label: "Unassigned" },
                    ...users.map((user) => ({
                      value: user.id,
                      label: `${user.firstName || ''} ${user.lastName || ''}`.trim() || user.email,
                      description: user.email,
                    }))
                  ]}
                  value={field.value || "unassigned"}
                  onValueChange={(value) => field.onChange(value === "unassigned" ? undefined : value)}
                  placeholder="Select assignee"
                  searchPlaceholder="Search users..."
                  emptyMessage="No users found"
                  allowClear
                  clearLabel="Unassign"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Milestone */}
        <FormField
          control={form.control}
          name="milestoneId"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Milestone</FormLabel>
              <FormControl>
                <SearchableSelect
                  options={[
                    { value: "none", label: "No Milestone" },
                    ...milestones.map((milestone) => ({
                      value: milestone.id,
                      label: milestone.title,
                      description: `Due: ${new Date(milestone.dueDate).toLocaleDateString()} • ${milestone.status}`,
                    }))
                  ]}
                  value={field.value || "none"}
                  onValueChange={(value) => field.onChange(value === "none" ? undefined : value)}
                  placeholder="Select milestone"
                  searchPlaceholder="Search milestones..."
                  emptyMessage="No milestones found"
                  allowClear
                  clearLabel="Remove from milestone"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Dates */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <FormField
            control={form.control}
            name="startDate"
            render={({ field }) => (
              <FormItem>
                <FormControl>
                  <DateInput
                    label="Start Date"
                    value={field.value}
                    onChange={field.onChange}
                    placeholder="Select start date"
                    min={new Date().toISOString().split('T')[0]}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="dueDate"
            render={({ field }) => (
              <FormItem>
                <FormControl>
                  <DateInput
                    label="Due Date"
                    value={field.value}
                    onChange={field.onChange}
                    placeholder="Select due date"
                    min={new Date().toISOString().split('T')[0]}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        {/* Estimated Hours */}
        <FormField
          control={form.control}
          name="estimatedHours"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Estimated Hours</FormLabel>
              <FormControl>
                <Input
                  type="number"
                  placeholder="Enter estimated hours"
                  {...field}
                  onChange={(e) => field.onChange(e.target.value ? parseFloat(e.target.value) : undefined)}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Form Actions */}
        <div className="flex items-center justify-end gap-3">
          <Button type="button" variant="outline" onClick={onCancel}>
            Cancel
          </Button>
          <Button type="submit" disabled={isLoading}>
            {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            {initialData ? 'Update Task' : 'Create Task'}
          </Button>
        </div>
      </form>
    </Form>
  );
}
