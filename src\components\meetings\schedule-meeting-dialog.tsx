'use client';

import { useState, useEffect, useRef } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { format } from 'date-fns';
import { CalendarIcon, Phone, Video, MapPin, Link, MapPinIcon } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Calendar } from '@/components/ui/calendar';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { ScheduledMeetingWithRelations } from '@/services/scheduled-meeting-service';

// Time slots for scheduling
const timeSlots = Array.from({ length: 48 }, (_, i) => {
  const hour = Math.floor(i / 2);
  const minute = i % 2 === 0 ? '00' : '30';
  const time24 = `${hour.toString().padStart(2, '0')}:${minute}`;
  const hour12 = hour === 0 ? 12 : hour > 12 ? hour - 12 : hour;
  const ampm = hour < 12 ? 'AM' : 'PM';
  const time12 = `${hour12}:${minute} ${ampm}`;
  return { value: time24, label: time12 };
});

const scheduleMeetingSchema = z.object({
  leadId: z.string().optional().transform(val => val === '' ? undefined : val),
  contactId: z.string().optional().transform(val => val === '' ? undefined : val),
  assignedTo: z.string().min(1, 'Assigned user is required'),
  title: z.string().min(1, 'Title is required').max(255),
  description: z.string().optional(),
  meetingType: z.enum(['phone', 'video', 'in-person']),
  meetingLink: z.string().url('Invalid meeting link').optional().or(z.literal('')),
  location: z.string().optional(),
  scheduledDate: z.date({
    required_error: 'Scheduled date is required',
  }),
  scheduledTime: z.string().min(1, 'Scheduled time is required'),
  duration: z.number().min(5).max(480),
  timezone: z.string(),
  followUpRequired: z.boolean(),
  nextMeetingDate: z.date().optional(),
}).refine((data) => {
  // Require meeting link for video meetings
  if (data.meetingType === 'video' && !data.meetingLink) {
    return false;
  }
  // Require location for in-person meetings
  if (data.meetingType === 'in-person' && !data.location) {
    return false;
  }
  return true;
}, {
  message: "Meeting link is required for video meetings and location is required for in-person meetings",
  path: ["meetingType"],
});

type ScheduleMeetingFormData = z.infer<typeof scheduleMeetingSchema>;

interface ScheduleMeetingDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (data: any) => Promise<void>;
  meeting?: ScheduledMeetingWithRelations | null;
  leadId?: string;
  contactId?: string;
  currentUserId?: string;
  users?: Array<{
    id: string;
    firstName: string | null;
    lastName: string | null;
    email: string;
  }>;
  contacts?: Array<{
    id: string;
    firstName: string;
    lastName: string;
    email: string | null;
  }>;
  leads?: Array<{
    id: string;
    title: string;
    status: string;
    contact?: {
      firstName: string;
      lastName: string;
    } | null;
  }>;
}

const meetingTypeOptions = [
  { value: 'phone', label: 'Phone Call', icon: Phone },
  { value: 'video', label: 'Video Meeting', icon: Video },
  { value: 'in-person', label: 'In-Person Meeting', icon: MapPin },
];

const durationOptions = [
  { value: 15, label: '15 minutes' },
  { value: 30, label: '30 minutes' },
  { value: 45, label: '45 minutes' },
  { value: 60, label: '1 hour' },
  { value: 90, label: '1.5 hours' },
  { value: 120, label: '2 hours' },
];

export function ScheduleMeetingDialog({
  open,
  onOpenChange,
  onSubmit,
  meeting,
  leadId,
  contactId,
  currentUserId,
  users = [],
  contacts = [],
  leads = [],
}: ScheduleMeetingDialogProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [selectedContact, setSelectedContact] = useState<string | null>(contactId || null);
  const [selectedLead, setSelectedLead] = useState<string | null>(leadId || null);
  const formInitialized = useRef(false);

  const isEditing = !!meeting;

  const form = useForm<ScheduleMeetingFormData>({
    resolver: zodResolver(scheduleMeetingSchema),
    defaultValues: {
      leadId: leadId || meeting?.leadId || '',
      contactId: contactId || meeting?.contactId || '',
      assignedTo: meeting?.assignedTo || currentUserId || '',
      title: meeting?.title || (leadId ? `Meeting regarding ${leads.find(l => l.id === leadId)?.title || 'Lead'}` : ''),
      description: meeting?.description || '',
      meetingType: meeting?.meetingType as 'phone' | 'video' | 'in-person' || 'phone',
      meetingLink: meeting?.meetingLink || '',
      location: meeting?.location || '',
      scheduledDate: meeting ? new Date(meeting.scheduledAt) : new Date(),
      scheduledTime: meeting ? format(new Date(meeting.scheduledAt), 'HH:mm') : '09:00',
      duration: meeting?.duration || 30,
      timezone: meeting?.timezone || 'UTC',
      followUpRequired: meeting?.followUpRequired || false,
      nextMeetingDate: meeting?.nextMeetingDate ? new Date(meeting.nextMeetingDate) : undefined,
    },
  });

  const watchedMeetingType = form.watch('meetingType');

  useEffect(() => {
    if (leadId) {
      setSelectedLead(leadId);
      form.setValue('leadId', leadId);
      // Auto-generate title if not editing
      if (!meeting && leads.length > 0) {
        const currentLead = leads.find(l => l.id === leadId);
        if (currentLead) {
          form.setValue('title', `Meeting regarding ${currentLead.title}`);
        }
      }
    }
    if (contactId) {
      setSelectedContact(contactId);
      form.setValue('contactId', contactId);
    }
    // Set current user as assigned user if available
    if (currentUserId && !meeting) {
      form.setValue('assignedTo', currentUserId);
    }
  }, [leadId, contactId, currentUserId, meeting, leads, form]);

  // Reset form when dialog opens
  useEffect(() => {
    if (open && !formInitialized.current) {
      if (!meeting) {
        // Reset for new meeting
        setSelectedContact(contactId || null);
        setSelectedLead(leadId || null);

        const currentLead = leads.find(l => l.id === leadId);
        const defaultTitle = currentLead ? `Meeting regarding ${currentLead.title}` : '';

        form.reset({
          leadId: leadId || '',
          contactId: contactId || '',
          assignedTo: currentUserId || '',
          title: defaultTitle,
          description: '',
          meetingType: 'phone',
          meetingLink: '',
          location: '',
          scheduledDate: new Date(),
          scheduledTime: '09:00',
          duration: 30,
          timezone: 'UTC',
          followUpRequired: false,
          nextMeetingDate: undefined,
        });
      } else {
        // Reset for editing existing meeting
        setSelectedContact(meeting.contactId || null);
        setSelectedLead(meeting.leadId || null);

        form.reset({
          leadId: meeting.leadId || '',
          contactId: meeting.contactId || '',
          assignedTo: meeting.assignedTo,
          title: meeting.title,
          description: meeting.description || '',
          meetingType: meeting.meetingType as 'phone' | 'video' | 'in-person',
          meetingLink: meeting.meetingLink || '',
          location: meeting.location || '',
          scheduledDate: new Date(meeting.scheduledAt),
          scheduledTime: format(new Date(meeting.scheduledAt), 'HH:mm'),
          duration: meeting.duration,
          timezone: meeting.timezone,
          followUpRequired: meeting.followUpRequired,
          nextMeetingDate: meeting.nextMeetingDate ? new Date(meeting.nextMeetingDate) : undefined,
        });
      }
      formInitialized.current = true;
    } else if (!open) {
      // Reset the flag when dialog closes
      formInitialized.current = false;
    }
  }, [open, meeting, leadId, contactId, leads, currentUserId, form]);

  const handleSubmit = async (data: ScheduleMeetingFormData) => {
    setIsLoading(true);
    try {
      // Combine date and time
      const scheduledDateTime = new Date(data.scheduledDate);
      const [hours, minutes] = data.scheduledTime.split(':').map(Number);
      scheduledDateTime.setHours(hours, minutes, 0, 0);

      const submitData = {
        ...data,
        leadId: data.leadId || undefined,
        contactId: data.contactId || undefined,
        meetingLink: data.meetingLink || undefined,
        location: data.location || undefined,
        scheduledAt: scheduledDateTime.toISOString(),
        nextMeetingDate: data.nextMeetingDate?.toISOString(),
      };

      await onSubmit(submitData);
      onOpenChange(false);
      // Reset form state
      setSelectedContact(null);
      setSelectedLead(null);
      form.reset();
    } catch (error) {
      console.error('Error submitting meeting:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleContactChange = (contactId: string) => {
    const actualContactId = contactId === 'none' ? null : contactId;
    setSelectedContact(actualContactId);
    form.setValue('contactId', actualContactId || '');
    // Only clear lead selection if contact is selected and we're not in a lead context
    if (actualContactId && !leadId) {
      setSelectedLead(null);
      form.setValue('leadId', '');
    }
  };

  const handleLeadChange = (leadId: string) => {
    const actualLeadId = leadId === 'none' ? null : leadId;
    setSelectedLead(actualLeadId);
    form.setValue('leadId', actualLeadId || '');
    // Only clear contact selection if lead is selected and we're not in a contact context
    if (actualLeadId && !contactId) {
      setSelectedContact(null);
      form.setValue('contactId', '');
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {isEditing ? 'Edit Scheduled Meeting' : 'Schedule New Meeting'}
          </DialogTitle>
          <DialogDescription>
            {isEditing 
              ? 'Update the details of your scheduled meeting.'
              : 'Schedule a new meeting with a lead or contact.'
            }
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
            {/* Contact/Lead Selection */}
            <div className="space-y-4">
                <FormField
                  control={form.control}
                  name="contactId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        Contact
                        {!!contactId && !isEditing && (
                          <span className="text-xs text-gray-500 ml-2">(from lead)</span>
                        )}
                      </FormLabel>
                      <Select
                        value={selectedContact || 'none'}
                        onValueChange={handleContactChange}
                        disabled={!!contactId && !isEditing}
                      >
                        <FormControl>
                          <SelectTrigger className={!!contactId && !isEditing ? 'opacity-60' : ''}>
                            <SelectValue placeholder="Select contact" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent
                          className="z-[9999] max-h-[200px] overflow-y-auto"
                          position="popper"
                          side="bottom"
                          align="start"
                          sideOffset={8}
                          avoidCollisions={true}
                          collisionPadding={20}
                          container={typeof window !== 'undefined' ? document.body : undefined}
                        >
                          <SelectItem value="none">No contact</SelectItem>
                          {contacts.map((contact) => (
                            <SelectItem key={contact.id} value={contact.id}>
                              {contact.firstName} {contact.lastName}
                              {contact.email && (
                                <span className="text-gray-500 ml-2">
                                  ({contact.email})
                                </span>
                              )}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="leadId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        Lead
                        {!!leadId && !isEditing && (
                          <span className="text-xs text-gray-500 ml-2">(current lead)</span>
                        )}
                      </FormLabel>
                      <Select
                        value={selectedLead || 'none'}
                        onValueChange={handleLeadChange}
                        disabled={!!leadId && !isEditing}
                      >
                        <FormControl>
                          <SelectTrigger className={!!leadId && !isEditing ? 'opacity-60' : ''}>
                            <SelectValue placeholder="Select lead" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent
                          className="z-[9999] max-h-[200px] overflow-y-auto"
                          position="popper"
                          side="bottom"
                          align="start"
                          sideOffset={8}
                          avoidCollisions={true}
                          collisionPadding={20}
                          container={typeof window !== 'undefined' ? document.body : undefined}
                        >
                          <SelectItem value="none">No lead</SelectItem>
                          {leads.map((lead) => (
                            <SelectItem key={lead.id} value={lead.id}>
                              {lead.title}
                              {lead.contact && (
                                <span className="text-gray-500 ml-2">
                                  ({lead.contact.firstName} {lead.contact.lastName})
                                </span>
                              )}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
            </div>

            {/* Basic Information */}
            <FormField
              control={form.control}
              name="title"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Meeting Title</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter meeting title" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Enter meeting description or agenda"
                      className="resize-none"
                      rows={3}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Meeting Type */}
            <FormField
              control={form.control}
              name="meetingType"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Meeting Type</FormLabel>
                  <FormControl>
                    <div className="grid grid-cols-3 gap-3">
                      {meetingTypeOptions.map((option) => {
                        const Icon = option.icon;
                        return (
                          <button
                            key={option.value}
                            type="button"
                            onClick={() => field.onChange(option.value)}
                            className={cn(
                              'flex flex-col items-center justify-center p-3 border rounded-lg transition-colors',
                              field.value === option.value
                                ? 'border-primary bg-primary/5 text-primary'
                                : 'border-gray-200 hover:border-gray-300'
                            )}
                          >
                            <Icon className="h-5 w-5 mb-1" />
                            <span className="text-sm font-medium">{option.label}</span>
                          </button>
                        );
                      })}
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Conditional Fields based on Meeting Type */}
            {watchedMeetingType === 'video' && (
              <FormField
                control={form.control}
                name="meetingLink"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="flex items-center gap-2">
                      <Link className="h-4 w-4" />
                      Meeting Link <span className="text-red-500">*</span>
                    </FormLabel>
                    <FormControl>
                      <Input 
                        placeholder="https://zoom.us/j/123456789 or https://meet.google.com/abc-defg-hij" 
                        {...field} 
                      />
                    </FormControl>
                    <FormDescription>
                      Enter the video meeting link (Zoom, Google Meet, Teams, etc.)
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}

            {watchedMeetingType === 'in-person' && (
              <FormField
                control={form.control}
                name="location"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="flex items-center gap-2">
                      <MapPinIcon className="h-4 w-4" />
                      Location <span className="text-red-500">*</span>
                    </FormLabel>
                    <FormControl>
                      <Input 
                        placeholder="Enter meeting location (address, office, etc.)" 
                        {...field} 
                      />
                    </FormControl>
                    <FormDescription>
                      Provide the full address or location details
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}

            {/* Scheduling */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <FormField
                control={form.control}
                name="scheduledDate"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>Date</FormLabel>
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant="outline"
                            className={cn(
                              'w-full pl-3 text-left font-normal',
                              !field.value && 'text-muted-foreground'
                            )}
                          >
                            {field.value ? (
                              format(field.value, 'PPP')
                            ) : (
                              <span>Pick a date</span>
                            )}
                            <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent
                        className="w-auto p-0 z-[1000]"
                        align="start"
                        side="bottom"
                        sideOffset={4}
                        avoidCollisions={true}
                        collisionPadding={20}
                      >
                        <Calendar
                          mode="single"
                          selected={field.value}
                          onSelect={field.onChange}
                          disabled={(date) =>
                            date < new Date(new Date().setHours(0, 0, 0, 0))
                          }
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="scheduledTime"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Time</FormLabel>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select time" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent className="max-h-60 z-[1000]" position="popper" side="bottom" sideOffset={4}>
                        {timeSlots.map((slot) => (
                          <SelectItem key={slot.value} value={slot.value}>
                            {slot.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="duration"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Duration</FormLabel>
                    <Select
                      onValueChange={(value) => field.onChange(Number(value))}
                      value={field.value?.toString()}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Duration" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent className="z-[1000]" position="popper" side="bottom" sideOffset={4}>
                        {durationOptions.map((option) => (
                          <SelectItem key={option.value} value={option.value.toString()}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Assigned To */}
            <FormField
              control={form.control}
              name="assignedTo"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Assigned To</FormLabel>
                  <Select onValueChange={field.onChange} value={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select user" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent className="z-[1000]" position="popper" side="bottom" sideOffset={4}>
                      {users.map((user) => (
                        <SelectItem key={user.id} value={user.id}>
                          {user.firstName} {user.lastName}
                          <span className="text-gray-500 ml-2">({user.email})</span>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Follow-up */}
            <FormField
              control={form.control}
              name="followUpRequired"
              render={({ field }) => (
                <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                  <FormControl>
                    <Checkbox
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                  <div className="space-y-1 leading-none">
                    <FormLabel>Follow-up required</FormLabel>
                    <FormDescription>
                      Mark this meeting as requiring a follow-up action
                    </FormDescription>
                  </div>
                </FormItem>
              )}
            />

            {form.watch('followUpRequired') && (
              <FormField
                control={form.control}
                name="nextMeetingDate"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>Next Meeting Date</FormLabel>
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant="outline"
                            className={cn(
                              'w-full pl-3 text-left font-normal',
                              !field.value && 'text-muted-foreground'
                            )}
                          >
                            {field.value ? (
                              format(field.value, 'PPP')
                            ) : (
                              <span>Pick a date for next meeting</span>
                            )}
                            <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent
                        className="w-auto p-0 z-[1000]"
                        align="start"
                        side="bottom"
                        sideOffset={4}
                        avoidCollisions={true}
                        collisionPadding={20}
                      >
                        <Calendar
                          mode="single"
                          selected={field.value}
                          onSelect={field.onChange}
                          disabled={(date) =>
                            date < new Date(new Date().setHours(0, 0, 0, 0))
                          }
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}

            {/* Submit Buttons */}
            <div className="flex justify-end space-x-2 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
                disabled={isLoading}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isLoading}>
                {isLoading ? 'Saving...' : isEditing ? 'Update Meeting' : 'Schedule Meeting'}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
