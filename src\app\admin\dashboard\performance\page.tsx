'use client';

import { useEffect, useState } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import {
  ChartBarIcon,
  ClockIcon,
  CircleStackIcon,
  CpuChipIcon,
  ServerIcon,
  ArrowTrendingUpIcon,
  ArrowTrendingDownIcon,
} from '@heroicons/react/24/outline';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { EnhancedMetricCard } from '@/components/dashboard/enhanced-metric-card';
import { PerformanceTrendChart } from '@/components/charts/performance-trend-chart';
import { DatabaseMetricsChart } from '@/components/charts/database-metrics-chart';
import { ApiMetricsChart } from '@/components/charts/api-metrics-chart';
import { PerformanceAlertsPanel } from '@/components/dashboard/performance-alerts-panel';
import { SuperAdminSiteHeader } from '@/components/super-admin-site-header';

interface PerformanceData {
  timestamp: number;
  timeWindow: string;
  systemHealth: any;
  performanceSummary: {
    slowQueries: number;
    cacheHitRate: number;
    averageResponseTime: number;
    errorRate: number;
    memoryUsage: number;
  };
  databaseMetrics: {
    queryStats: Record<string, any>;
    averageQueryTime: number;
    slowQueries: Array<{ name: string; avg: number; count: number; max: number; min: number }>;
  };
  cacheMetrics: {
    hitRate: number;
    memory: string;
    keys: number;
    hits: number;
    misses: number;
  };
  apiMetrics: {
    averageResponseTime: number;
    p95ResponseTime: number;
    p99ResponseTime: number;
    errorRate: number;
    requestsPerMinute: number;
  };
  memoryMetrics: {
    heapUsed: number;
    heapTotal: number;
    external: number;
    rss: number;
    heapUsedPercentage: number;
  };
}

interface PerformanceDashboardState {
  performanceData: PerformanceData | null;
  loading: boolean;
  error: string | null;
  timeWindow: string;
  autoRefresh: boolean;
  detailed: boolean;
}

export default function PerformanceDashboard() {
  const { data: session, status } = useSession();
  const router = useRouter();

  const [state, setState] = useState<PerformanceDashboardState>({
    performanceData: null,
    loading: true,
    error: null,
    timeWindow: '15m',
    autoRefresh: true,
    detailed: false,
  });

  useEffect(() => {
    if (status === 'loading') return;

    if (!session?.user?.isPlatformAdmin) {
      router.push('/dashboard');
      return;
    }

    fetchPerformanceData();
  }, [session, status, router, state.timeWindow, state.detailed]);

  useEffect(() => {
    if (!state.autoRefresh) return;

    const interval = setInterval(() => {
      fetchPerformanceData();
    }, 15000); // Refresh every 15 seconds

    return () => clearInterval(interval);
  }, [state.autoRefresh, state.timeWindow, state.detailed]);

  const fetchPerformanceData = async () => {
    try {
      setState(prev => ({ ...prev, error: null }));
      
      const response = await fetch(
        `/api/admin/performance?timeWindow=${state.timeWindow}&detailed=${state.detailed}`
      );

      if (!response.ok) {
        throw new Error('Failed to fetch performance data');
      }

      const data = await response.json();
      
      setState(prev => ({
        ...prev,
        performanceData: data.data,
        loading: false,
      }));
    } catch (err) {
      setState(prev => ({
        ...prev,
        error: err instanceof Error ? err.message : 'An error occurred',
        loading: false,
      }));
    }
  };

  const handleTimeWindowChange = (newTimeWindow: string) => {
    setState(prev => ({ ...prev, timeWindow: newTimeWindow, loading: true }));
  };

  const toggleAutoRefresh = () => {
    setState(prev => ({ ...prev, autoRefresh: !prev.autoRefresh }));
  };

  const toggleDetailed = () => {
    setState(prev => ({ ...prev, detailed: !prev.detailed, loading: true }));
  };

  const getPerformanceStatus = (data: PerformanceData | null) => {
    if (!data) return 'unknown';
    
    const { performanceSummary } = data;
    
    if (
      performanceSummary.averageResponseTime > 2000 ||
      performanceSummary.errorRate > 5 ||
      performanceSummary.memoryUsage > 90 ||
      performanceSummary.cacheHitRate < 70
    ) {
      return 'critical';
    }
    
    if (
      performanceSummary.averageResponseTime > 1000 ||
      performanceSummary.errorRate > 2 ||
      performanceSummary.memoryUsage > 80 ||
      performanceSummary.cacheHitRate < 85
    ) {
      return 'warning';
    }
    
    return 'good';
  };

  if (status === 'loading' || state.loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (state.error) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle className="text-destructive">Error</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground mb-4">{state.error}</p>
            <Button onClick={fetchPerformanceData} variant="outline" size="sm">
              Retry
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  const { performanceData } = state;
  const performanceStatus = getPerformanceStatus(performanceData);

  return (
    <>
      <SuperAdminSiteHeader />
      <div className="px-4 sm:px-6 lg:px-8 py-8 space-y-8">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
              Performance Dashboard
            </h1>
            <p className="mt-1 text-gray-600 dark:text-gray-400">
              Real-time system performance analytics and monitoring
            </p>
          </div>
          <div className="mt-4 sm:mt-0 flex items-center space-x-4">
            <Select value={state.timeWindow} onValueChange={handleTimeWindowChange}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="5m">5 minutes</SelectItem>
                <SelectItem value="15m">15 minutes</SelectItem>
                <SelectItem value="1h">1 hour</SelectItem>
                <SelectItem value="6h">6 hours</SelectItem>
                <SelectItem value="24h">24 hours</SelectItem>
              </SelectContent>
            </Select>

            <Button
              variant={state.detailed ? "default" : "outline"}
              size="sm"
              onClick={toggleDetailed}
            >
              {state.detailed ? "Detailed ON" : "Detailed OFF"}
            </Button>

            <Button
              variant={state.autoRefresh ? "default" : "outline"}
              size="sm"
              onClick={toggleAutoRefresh}
            >
              {state.autoRefresh ? "Auto-refresh ON" : "Auto-refresh OFF"}
            </Button>

            <Button onClick={fetchPerformanceData} variant="outline" size="sm">
              Refresh Now
            </Button>
          </div>
        </div>

      {/* Performance Summary */}
      {performanceData && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
          <EnhancedMetricCard
            title="Avg Response Time"
            value={Math.round(performanceData.performanceSummary.averageResponseTime)}
            subtitle="milliseconds"
            icon={ClockIcon}
            format="number"
            color={performanceData.performanceSummary.averageResponseTime > 1000 ? "warning" : "success"}
          />
          <EnhancedMetricCard
            title="Error Rate"
            value={performanceData.performanceSummary.errorRate}
            subtitle="percentage"
            icon={ArrowTrendingDownIcon}
            format="percentage"
            color={performanceData.performanceSummary.errorRate > 2 ? "danger" : "success"}
          />
          <EnhancedMetricCard
            title="Cache Hit Rate"
            value={performanceData.performanceSummary.cacheHitRate}
            subtitle="cache efficiency"
            icon={CircleStackIcon}
            format="percentage"
            color={performanceData.performanceSummary.cacheHitRate < 85 ? "warning" : "success"}
          />
          <EnhancedMetricCard
            title="Memory Usage"
            value={performanceData.performanceSummary.memoryUsage}
            subtitle="heap utilization"
            icon={CpuChipIcon}
            format="percentage"
            color={performanceData.performanceSummary.memoryUsage > 80 ? "warning" : "success"}
          />
          <EnhancedMetricCard
            title="Slow Queries"
            value={performanceData.performanceSummary.slowQueries}
            subtitle="queries > 1s"
            icon={ServerIcon}
            format="number"
            color={performanceData.performanceSummary.slowQueries > 5 ? "warning" : "success"}
          />
        </div>
      )}

      {/* Detailed Performance Metrics */}
      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="api">API Performance</TabsTrigger>
          <TabsTrigger value="database">Database</TabsTrigger>
          <TabsTrigger value="cache">Cache & Memory</TabsTrigger>
          <TabsTrigger value="alerts">Alerts</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {performanceData && (
            <PerformanceTrendChart
              title="Performance Overview"
              description={`System performance metrics over the last ${state.timeWindow}`}
              data={performanceData}
              timeWindow={state.timeWindow}
            />
          )}
        </TabsContent>

        <TabsContent value="api" className="space-y-6">
          {performanceData && (
            <ApiMetricsChart
              title="API Performance Metrics"
              description="Request response times and error rates"
              data={performanceData.apiMetrics}
              timeWindow={state.timeWindow}
            />
          )}
        </TabsContent>

        <TabsContent value="database" className="space-y-6">
          {performanceData && (
            <DatabaseMetricsChart
              title="Database Performance"
              description="Query performance and connection metrics"
              data={performanceData.databaseMetrics}
              timeWindow={state.timeWindow}
            />
          )}
        </TabsContent>

        <TabsContent value="cache" className="space-y-6">
          {performanceData && (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card className="bg-white dark:bg-gray-800 rounded-lg shadow-soft">
                <CardHeader>
                  <CardTitle>Cache Performance</CardTitle>
                  <CardDescription>Redis cache metrics and efficiency</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex justify-between">
                      <span className="text-sm font-medium">Hit Rate</span>
                      <span className="text-sm">{performanceData.cacheMetrics.hitRate.toFixed(1)}%</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm font-medium">Memory Usage</span>
                      <span className="text-sm">{performanceData.cacheMetrics.memory}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm font-medium">Total Keys</span>
                      <span className="text-sm">{performanceData.cacheMetrics.keys.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm font-medium">Cache Hits</span>
                      <span className="text-sm">{performanceData.cacheMetrics.hits.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm font-medium">Cache Misses</span>
                      <span className="text-sm">{performanceData.cacheMetrics.misses.toLocaleString()}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-white dark:bg-gray-800 rounded-lg shadow-soft">
                <CardHeader>
                  <CardTitle>Memory Metrics</CardTitle>
                  <CardDescription>Node.js memory usage breakdown</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex justify-between">
                      <span className="text-sm font-medium">Heap Used</span>
                      <span className="text-sm">{(performanceData.memoryMetrics.heapUsed / 1024 / 1024).toFixed(1)} MB</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm font-medium">Heap Total</span>
                      <span className="text-sm">{(performanceData.memoryMetrics.heapTotal / 1024 / 1024).toFixed(1)} MB</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm font-medium">Heap Usage</span>
                      <span className="text-sm">{performanceData.memoryMetrics.heapUsedPercentage.toFixed(1)}%</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm font-medium">External</span>
                      <span className="text-sm">{(performanceData.memoryMetrics.external / 1024 / 1024).toFixed(1)} MB</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm font-medium">RSS</span>
                      <span className="text-sm">{(performanceData.memoryMetrics.rss / 1024 / 1024).toFixed(1)} MB</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}
        </TabsContent>

        <TabsContent value="alerts" className="space-y-6">
          <PerformanceAlertsPanel 
            performanceStatus={performanceStatus} 
            performanceData={performanceData} 
          />
        </TabsContent>
      </Tabs>
      </div>
    </>
  );
}
