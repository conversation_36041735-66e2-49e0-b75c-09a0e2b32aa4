import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { processAutomationService } from '@/services/process-automation';
import { withPermission, AuthenticatedRequest } from '@/lib/permission-middleware';
import { PermissionResource, PermissionAction } from '@/types';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';

// Validation schemas
const createTriggerSchema = z.object({
  name: z.string().min(1, 'Name is required').max(255),
  description: z.string().optional(),
  triggerType: z.enum(['opportunity_status_change', 'handover_completion', 'project_milestone', 'time_based']),
  triggerConditions: z.record(z.any()),
  actions: z.array(z.object({
    type: z.enum(['create_handover', 'send_notification', 'create_project', 'assign_team', 'schedule_meeting']),
    config: z.record(z.any())
  })),
  isActive: z.boolean().default(true),
  priority: z.number().int().min(1).max(10).default(5)
});

const updateTriggerSchema = createTriggerSchema.partial();

const querySchema = z.object({
  page: z.string().optional().transform(val => val ? parseInt(val) : 1),
  limit: z.string().optional().transform(val => val ? parseInt(val) : 20),
  triggerType: z.enum(['opportunity_status_change', 'handover_completion', 'project_milestone', 'time_based']).optional(),
  isActive: z.string().optional().transform(val => val === 'true'),
  search: z.string().optional()
});

/**
 * GET /api/automation/triggers
 * Get automation triggers with filtering and pagination
 */
export const GET = withPermission({
  resource: PermissionResource.SETTINGS,
  action: PermissionAction.READ,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest) => {
  try {
    const { searchParams } = new URL(req.url);
    const query = querySchema.parse(Object.fromEntries(searchParams));

    // Build where clause
    const where: any = { tenantId: req.tenantId };
    
    if (query.triggerType) {
      where.triggerType = query.triggerType;
    }
    
    if (query.isActive !== undefined) {
      where.isActive = query.isActive;
    }
    
    if (query.search) {
      where.OR = [
        { name: { contains: query.search, mode: 'insensitive' } },
        { description: { contains: query.search, mode: 'insensitive' } }
      ];
    }

    // Get triggers with pagination
    const [triggers, total] = await Promise.all([
      prisma.automationTrigger.findMany({
        where,
        orderBy: [
          { priority: 'desc' },
          { createdAt: 'desc' }
        ],
        skip: (query.page - 1) * query.limit,
        take: query.limit,
        include: {
          createdBy: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true
            }
          },
          _count: {
            select: {
              executions: true
            }
          }
        }
      }),
      prisma.automationTrigger.count({ where })
    ]);

    return NextResponse.json({
      success: true,
      data: {
        triggers,
        pagination: {
          page: query.page,
          limit: query.limit,
          total,
          pages: Math.ceil(total / query.limit)
        }
      }
    });
  } catch (error) {
    console.error('Error fetching automation triggers:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid query parameters', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to fetch automation triggers' },
      { status: 500 }
    );
  }
});

/**
 * POST /api/automation/triggers
 * Create a new automation trigger
 */
export const POST = withPermission({
  resource: PermissionResource.SETTINGS,
  action: PermissionAction.CREATE,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest) => {
  try {
    const body = await req.json();
    const validatedData = createTriggerSchema.parse(body);

    const trigger = await processAutomationService.createAutomationTrigger(
      req.tenantId,
      validatedData,
      req.userId
    );

    return NextResponse.json({
      success: true,
      data: { trigger },
      message: 'Automation trigger created successfully'
    }, { status: 201 });
  } catch (error) {
    console.error('Error creating automation trigger:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request data', details: error.errors },
        { status: 400 }
      );
    }

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to create automation trigger' },
      { status: 500 }
    );
  }
});

/**
 * GET /api/automation/triggers/metrics
 * Get automation metrics and analytics
 */
export async function GET_METRICS(req: AuthenticatedRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(req.url);
    const dateFrom = searchParams.get('dateFrom');
    const dateTo = searchParams.get('dateTo');

    let dateRange;
    if (dateFrom && dateTo) {
      dateRange = {
        from: new Date(dateFrom),
        to: new Date(dateTo)
      };
    }

    const metrics = await processAutomationService.getProcessMetrics(
      req.tenantId,
      dateRange
    );

    return NextResponse.json({
      success: true,
      data: { metrics }
    });
  } catch (error) {
    console.error('Error fetching automation metrics:', error);
    return NextResponse.json(
      { error: 'Failed to fetch automation metrics' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/automation/triggers/test
 * Test an automation trigger
 */
export async function POST_TEST(req: AuthenticatedRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await req.json();
    const { triggerId, testData } = body;

    if (!triggerId) {
      return NextResponse.json(
        { error: 'Trigger ID is required' },
        { status: 400 }
      );
    }

    // Get the trigger
    const trigger = await prisma.automationTrigger.findUnique({
      where: { id: triggerId, tenantId: req.tenantId }
    });

    if (!trigger) {
      return NextResponse.json(
        { error: 'Trigger not found' },
        { status: 404 }
      );
    }

    // Execute test (this would be a dry run)
    const testResult = {
      triggerId,
      triggerName: trigger.name,
      testData,
      wouldExecute: trigger.isActive,
      actions: trigger.actions,
      timestamp: new Date().toISOString()
    };

    return NextResponse.json({
      success: true,
      data: { testResult },
      message: 'Trigger test completed'
    });
  } catch (error) {
    console.error('Error testing automation trigger:', error);
    return NextResponse.json(
      { error: 'Failed to test automation trigger' },
      { status: 500 }
    );
  }
}
