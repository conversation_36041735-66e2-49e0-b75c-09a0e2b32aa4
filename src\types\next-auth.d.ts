// import NextAuth from 'next-auth';

declare module 'next-auth' {
  interface Session {
    user: {
      id: string;
      email: string;
      name: string;
      image?: string;
      avatarUrl?: string;
      phone?: string;
      locale: string;
      isPlatformAdmin: boolean;
      tenants: Array<{
        id: string;
        name: string;
        slug: string;
        role: string;
        isTenantAdmin: boolean;
      }>;
    };
    accessToken?: string;
    error?: string;
  }

  interface User {
    id: string;
    email: string;
    name: string;
    image?: string;
    avatarUrl?: string;
    phone?: string;
    locale: string;
    isPlatformAdmin: boolean;
    tenants: Array<{
      id: string;
      name: string;
      slug: string;
      role: string;
      isTenantAdmin: boolean;
    }>;
  }
}

declare module 'next-auth/jwt' {
  interface JWT {
    id: string;
    email: string;
    name: string;
    avatarUrl?: string;
    phone?: string;
    locale: string;
    isPlatformAdmin: boolean;
    tenants: Array<{
      id: string;
      name: string;
      slug: string;
      role: string;
      isTenantAdmin: boolean;
    }>;
    accessToken?: string;
    accessTokenExpires?: number;
    refreshToken?: string;
    error?: string;
  }
}
