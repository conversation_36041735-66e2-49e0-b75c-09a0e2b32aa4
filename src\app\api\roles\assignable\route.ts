import { NextResponse } from 'next/server';
import { withPermission, AuthenticatedRequest } from '@/lib/permission-middleware';
import { PermissionAction, PermissionResource } from '@/types';
import { prisma } from '@/lib/prisma';

/**
 * GET /api/roles/assignable
 * Get roles that can be assigned to users (excludes super admin and other restricted roles)
 */
export const GET = withPermission({
  resource: PermissionResource.ROLES,
  action: PermissionAction.READ,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest) => {
  try {
    // Define roles that should not be assignable to regular users
    const restrictedRoleNames = [
      'Super Admin',
      'super_admin', // In case it's stored differently
      'Platform Admin'
    ];

    const roles = await prisma.role.findMany({
      where: {
        tenantId: req.tenantId,
        name: {
          notIn: restrictedRoleNames
        }
      },
      select: {
        id: true,
        name: true,
        description: true,
        isSystemRole: true
      },
      orderBy: [
        { isSystemRole: 'desc' }, // System roles first
        { name: 'asc' }
      ]
    });

    return NextResponse.json({
      success: true,
      data: roles
    });

  } catch (error) {
    console.error('Get assignable roles error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch assignable roles' },
      { status: 500 }
    );
  }
});
