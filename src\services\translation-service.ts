import { promises as fs } from 'fs';
import path from 'path';

export interface TranslationKey {
  key: string;
  en: string;
  ar: string;
  category: string;
  description?: string;
}

export interface TranslationFile {
  [key: string]: string | TranslationFile;
}

export class TranslationService {
  private static readonly LOCALES_PATH = path.join(process.cwd(), 'src/i18n/locales');

  /**
   * Load all translations from files
   */
  static async loadTranslations(): Promise<TranslationKey[]> {
    try {
      const enPath = path.join(this.LOCALES_PATH, 'en.json');
      const arPath = path.join(this.LOCALES_PATH, 'ar.json');

      const [enContent, arContent] = await Promise.all([
        fs.readFile(enPath, 'utf-8'),
        fs.readFile(arPath, 'utf-8')
      ]);

      const enTranslations = JSON.parse(enContent);
      const arTranslations = JSON.parse(arContent);

      return this.flattenTranslations(enTranslations, arTranslations);
    } catch (error) {
      console.error('Failed to load translations:', error);
      throw new Error('Failed to load translation files');
    }
  }

  /**
   * Save translations back to files
   */
  static async saveTranslations(translations: TranslationKey[]): Promise<void> {
    try {
      const enTranslations = this.buildNestedObject(translations, 'en');
      const arTranslations = this.buildNestedObject(translations, 'ar');

      const enPath = path.join(this.LOCALES_PATH, 'en.json');
      const arPath = path.join(this.LOCALES_PATH, 'ar.json');

      await Promise.all([
        fs.writeFile(enPath, JSON.stringify(enTranslations, null, 2), 'utf-8'),
        fs.writeFile(arPath, JSON.stringify(arTranslations, null, 2), 'utf-8')
      ]);
    } catch (error) {
      console.error('Failed to save translations:', error);
      throw new Error('Failed to save translation files');
    }
  }

  /**
   * Add a new translation
   */
  static async addTranslation(translation: TranslationKey): Promise<void> {
    const translations = await this.loadTranslations();
    
    // Check if key already exists
    if (translations.some(t => t.key === translation.key)) {
      throw new Error(`Translation key '${translation.key}' already exists`);
    }

    translations.push(translation);
    await this.saveTranslations(translations);
  }

  /**
   * Update an existing translation
   */
  static async updateTranslation(key: string, updates: Partial<TranslationKey>): Promise<void> {
    const translations = await this.loadTranslations();
    const index = translations.findIndex(t => t.key === key);
    
    if (index === -1) {
      throw new Error(`Translation key '${key}' not found`);
    }

    translations[index] = { ...translations[index], ...updates };
    await this.saveTranslations(translations);
  }

  /**
   * Delete a translation
   */
  static async deleteTranslation(key: string): Promise<void> {
    const translations = await this.loadTranslations();
    const filteredTranslations = translations.filter(t => t.key !== key);
    
    if (filteredTranslations.length === translations.length) {
      throw new Error(`Translation key '${key}' not found`);
    }

    await this.saveTranslations(filteredTranslations);
  }

  /**
   * Get translation statistics
   */
  static async getStatistics(): Promise<{
    totalKeys: number;
    categories: string[];
    missingTranslations: { key: string; language: string }[];
    duplicateKeys: string[];
  }> {
    const translations = await this.loadTranslations();
    
    const categories = Array.from(new Set(translations.map(t => t.category)));
    const missingTranslations: { key: string; language: string }[] = [];
    const duplicateKeys: string[] = [];
    
    // Check for missing translations
    translations.forEach(translation => {
      if (!translation.en || translation.en.trim() === '') {
        missingTranslations.push({ key: translation.key, language: 'en' });
      }
      if (!translation.ar || translation.ar.trim() === '') {
        missingTranslations.push({ key: translation.key, language: 'ar' });
      }
    });

    // Check for duplicate keys
    const keyCount = new Map<string, number>();
    translations.forEach(translation => {
      const count = keyCount.get(translation.key) || 0;
      keyCount.set(translation.key, count + 1);
    });
    
    keyCount.forEach((count, key) => {
      if (count > 1) {
        duplicateKeys.push(key);
      }
    });

    return {
      totalKeys: translations.length,
      categories,
      missingTranslations,
      duplicateKeys
    };
  }

  /**
   * Validate translation key format
   */
  static validateKey(key: string): { valid: boolean; error?: string } {
    if (!key || key.trim() === '') {
      return { valid: false, error: 'Key cannot be empty' };
    }

    if (!/^[a-zA-Z][a-zA-Z0-9._-]*$/.test(key)) {
      return { 
        valid: false, 
        error: 'Key must start with a letter and contain only letters, numbers, dots, underscores, and hyphens' 
      };
    }

    if (key.includes('..') || key.startsWith('.') || key.endsWith('.')) {
      return { valid: false, error: 'Key cannot have consecutive dots or start/end with dots' };
    }

    return { valid: true };
  }

  /**
   * Search translations
   */
  static async searchTranslations(
    query: string,
    category?: string,
    language?: 'en' | 'ar'
  ): Promise<TranslationKey[]> {
    const translations = await this.loadTranslations();
    
    return translations.filter(translation => {
      const matchesQuery = !query || 
        translation.key.toLowerCase().includes(query.toLowerCase()) ||
        translation.en.toLowerCase().includes(query.toLowerCase()) ||
        translation.ar.toLowerCase().includes(query.toLowerCase());
      
      const matchesCategory = !category || translation.category === category;
      
      const matchesLanguage = !language || 
        (language === 'en' && translation.en) ||
        (language === 'ar' && translation.ar);

      return matchesQuery && matchesCategory && matchesLanguage;
    });
  }

  /**
   * Flatten nested translation object into array
   */
  private static flattenTranslations(
    enObj: TranslationFile, 
    arObj: TranslationFile, 
    prefix = ''
  ): TranslationKey[] {
    const result: TranslationKey[] = [];
    
    const processObject = (
      enData: TranslationFile, 
      arData: TranslationFile, 
      currentPrefix: string, 
      category: string
    ) => {
      Object.keys(enData).forEach(key => {
        const fullKey = currentPrefix ? `${currentPrefix}.${key}` : key;
        const enValue = enData[key];
        const arValue = arData?.[key] || '';

        if (typeof enValue === 'object' && enValue !== null) {
          // Recursive call for nested objects
          processObject(
            enValue as TranslationFile, 
            (arValue as TranslationFile) || {}, 
            fullKey, 
            key
          );
        } else {
          // Leaf node - actual translation
          result.push({
            key: fullKey,
            en: String(enValue),
            ar: String(arValue),
            category: category || 'common'
          });
        }
      });
    };

    processObject(enObj, arObj, prefix, '');
    return result;
  }

  /**
   * Build nested object from flat translation array
   */
  private static buildNestedObject(
    translations: TranslationKey[], 
    language: 'en' | 'ar'
  ): TranslationFile {
    const result: TranslationFile = {};

    translations.forEach(translation => {
      const keys = translation.key.split('.');
      let current = result;

      for (let i = 0; i < keys.length - 1; i++) {
        const key = keys[i];
        if (!(key in current)) {
          current[key] = {};
        }
        current = current[key] as TranslationFile;
      }

      const lastKey = keys[keys.length - 1];
      current[lastKey] = translation[language];
    });

    return result;
  }

  /**
   * Export translations to different formats
   */
  static async exportTranslations(
    format: 'json' | 'csv' | 'xlsx',
    language?: 'en' | 'ar'
  ): Promise<string | Buffer> {
    const translations = await this.loadTranslations();
    
    switch (format) {
      case 'json':
        if (language) {
          const filtered = translations.reduce((acc, t) => {
            acc[t.key] = t[language];
            return acc;
          }, {} as Record<string, string>);
          return JSON.stringify(filtered, null, 2);
        }
        return JSON.stringify(translations, null, 2);
        
      case 'csv':
        const headers = language ? ['key', language] : ['key', 'en', 'ar', 'category'];
        const rows = translations.map(t =>
          language ? [t.key, t[language]] : [t.key, t.en, t.ar, t.category]
        );
        // Add UTF-8 BOM for proper Arabic character support
        const BOM = '\uFEFF';
        return BOM + [headers, ...rows].map(row => row.join(',')).join('\n');
        
      default:
        throw new Error(`Unsupported export format: ${format}`);
    }
  }

  /**
   * Import translations from different formats
   */
  static async importTranslations(
    data: string,
    format: 'json' | 'csv',
    merge = false
  ): Promise<void> {
    let importedTranslations: TranslationKey[] = [];

    switch (format) {
      case 'json':
        const jsonData = JSON.parse(data);
        if (Array.isArray(jsonData)) {
          importedTranslations = jsonData;
        } else {
          // Convert flat object to translation array
          importedTranslations = Object.entries(jsonData).map(([key, value]) => ({
            key,
            en: String(value),
            ar: '',
            category: key.split('.')[0] || 'imported'
          }));
        }
        break;
        
      case 'csv':
        const lines = data.split('\n').filter(line => line.trim());
        const headers = lines[0].split(',');
        
        for (let i = 1; i < lines.length; i++) {
          const values = lines[i].split(',');
          const translation: Partial<TranslationKey> = { category: 'imported' };
          
          headers.forEach((header, index) => {
            if (header === 'key') translation.key = values[index];
            else if (header === 'en') translation.en = values[index];
            else if (header === 'ar') translation.ar = values[index];
            else if (header === 'category') translation.category = values[index];
          });
          
          if (translation.key) {
            importedTranslations.push(translation as TranslationKey);
          }
        }
        break;
        
      default:
        throw new Error(`Unsupported import format: ${format}`);
    }

    if (merge) {
      const existingTranslations = await this.loadTranslations();
      const existingKeys = new Set(existingTranslations.map(t => t.key));
      
      // Only add new translations
      const newTranslations = importedTranslations.filter(t => !existingKeys.has(t.key));
      const mergedTranslations = [...existingTranslations, ...newTranslations];
      
      await this.saveTranslations(mergedTranslations);
    } else {
      await this.saveTranslations(importedTranslations);
    }
  }
}
