import { MermaidRenderRequest, MermaidRenderResponse } from '@/types/proposal';
import { prisma } from '@/lib/prisma';
import { enhancedMermaidRenderer } from './enhanced-mermaid-renderer';
import fs from 'fs/promises';
import path from 'path';

export class MermaidRendererService {
  private uploadsDir: string;

  constructor() {
    this.uploadsDir = path.join(process.cwd(), 'uploads');
  }

  /**
   * Render Mermaid chart to image using the enhanced mermaid renderer
   */
  async renderChart(
    chartId: string,
    tenantId: string,
    options: Partial<MermaidRenderRequest> = {}
  ): Promise<MermaidRenderResponse> {
    // Delegate to the enhanced mermaid renderer
    return enhancedMermaidRenderer.renderChart(chartId, tenantId, options);
  }

  /**
   * Validate Mermaid syntax
   */
  async validateMermaidSyntax(mermaidCode: string): Promise<{ valid: boolean; error?: string }> {
    return enhancedMermaidRenderer.validateMermaidSyntax(mermaidCode);
  }

  /**
   * Get rendered chart image
   */
  async getChartImage(chartId: string, tenantId: string): Promise<Buffer | null> {
    return enhancedMermaidRenderer.getChartImage(chartId, tenantId);
  }

  /**
   * Render all charts for a proposal
   */
  async renderProposalCharts(proposalId: string, tenantId: string): Promise<MermaidRenderResponse[]> {
    return enhancedMermaidRenderer.renderProposalCharts(proposalId, tenantId);
  }

  /**
   * Clean up old rendered images
   */
  async cleanupOldImages(tenantId: string, olderThanDays: number = 30): Promise<void> {
    try {
      const chartDir = path.join(this.uploadsDir, tenantId, 'charts');
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);

      // Get charts with old rendered images
      const oldCharts = await prisma.proposalChart.findMany({
        where: {
          tenantId,
          renderedImagePath: { not: null },
          updatedAt: { lt: cutoffDate }
        }
      });

      // Delete old image files
      for (const chart of oldCharts) {
        if (chart.renderedImagePath) {
          const imagePath = path.join(this.uploadsDir, chart.renderedImagePath);
          try {
            await fs.unlink(imagePath);
            
            // Clear the image path from database
            await prisma.proposalChart.update({
              where: { id: chart.id },
              data: { renderedImagePath: null }
            });
          } catch (error) {
            // File might already be deleted, continue
            console.warn(`Could not delete image file ${imagePath}:`, error);
          }
        }
      }

    } catch (error) {
      console.error('Error cleaning up old images:', error);
    }
  }
}

export const mermaidRenderer = new MermaidRendererService();
