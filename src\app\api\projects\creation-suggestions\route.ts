import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { projectCreationService } from '@/services/project-creation';
import { z } from 'zod';

// Request validation schemas
const getSuggestionsQuerySchema = z.object({
  limit: z.coerce.number().int().positive().max(50).default(10),
  includeWithHandovers: z.coerce.boolean().default(true),
});

/**
 * GET /api/projects/creation-suggestions - Get project creation suggestions
 */
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get tenantId from user's tenants array (users have one tenant in this system)
    let tenantId: string | null = null;
    if (session.user.tenants && session.user.tenants.length > 0) {
      tenantId = session.user.tenants[0].id;
    }

    if (!tenantId) {
      return NextResponse.json({ error: 'Tenant not found' }, { status: 400 });
    }

    // Parse and validate query parameters
    const url = new URL(request.url);
    const queryParams = Object.fromEntries(url.searchParams.entries());
    
    const validatedQuery = getSuggestionsQuerySchema.parse(queryParams);

    const suggestions = await projectCreationService.getProjectCreationSuggestions(
      tenantId,
      validatedQuery
    );

    return NextResponse.json({ suggestions });
  } catch (error) {
    console.error('Error fetching project creation suggestions:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid query parameters', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to fetch project creation suggestions' },
      { status: 500 }
    );
  }
}
