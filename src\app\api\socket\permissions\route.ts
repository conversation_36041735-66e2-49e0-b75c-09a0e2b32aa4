import { NextRequest, NextResponse } from 'next/server';
import { Server as SocketIOServer } from 'socket.io';
import { createServer } from 'http';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { permissionService } from '@/services/permission-service';
import { prisma } from '@/lib/prisma';

// Global socket server instance
let io: SocketIOServer | null = null;

// Initialize Socket.IO server
function initializeSocketServer() {
  if (io) return io;

  const httpServer = createServer();
  io = new SocketIOServer(httpServer, {
    path: '/api/socket/permissions',
    cors: {
      origin: process.env.NEXTAUTH_URL || 'http://localhost:3000',
      methods: ['GET', 'POST'],
      credentials: true
    },
    transports: ['websocket', 'polling']
  });

  // Authentication middleware
  io.use(async (socket, next) => {
    try {
      const token = socket.handshake.auth.token;
      
      // Validate the token (this is a simplified version)
      // In production, you'd want more robust token validation
      if (!token) {
        return next(new Error('Authentication required'));
      }

      // You can add more sophisticated auth validation here
      next();
    } catch (error) {
      next(new Error('Authentication failed'));
    }
  });

  // Handle connections
  io.on('connection', (socket) => {
    console.log('🔌 Permission WebSocket client connected:', socket.id);

    // Join user-specific room for permission updates
    socket.on('join_permission_room', async (userInfo: { userId: string; tenantId: string }) => {
      try {
        const roomName = `permissions:${userInfo.userId}:${userInfo.tenantId}`;
        await socket.join(roomName);
        console.log(`👤 User ${userInfo.userId} joined permission room: ${roomName}`);
        
        socket.emit('permission_room_joined', { room: roomName });
      } catch (error) {
        console.error('Error joining permission room:', error);
        socket.emit('permission_error', { message: 'Failed to join permission room' });
      }
    });

    // Handle permission refresh requests
    socket.on('request_permission_refresh', async (data: { userId: string; tenantId: string }) => {
      try {
        console.log(`🔄 Permission refresh requested for user ${data.userId} in tenant ${data.tenantId}`);
        
        // Invalidate cache and get fresh permissions
        await permissionService.invalidateUserPermissions(data.userId, data.tenantId);
        const freshPermissions = await permissionService.getUserEffectivePermissions(data.userId, data.tenantId);
        
        // Emit updated permissions to the user
        const roomName = `permissions:${data.userId}:${data.tenantId}`;
        io?.to(roomName).emit('permission_updated', {
          type: 'permission_update',
          userId: data.userId,
          tenantId: data.tenantId,
          permissions: freshPermissions.directPermissions,
          roles: freshPermissions.roles,
          effectivePermissions: freshPermissions.effectivePermissions,
          timestamp: new Date().toISOString(),
          reason: 'manual_refresh'
        });

        socket.emit('permission_refresh_complete', {
          userId: data.userId,
          tenantId: data.tenantId,
          success: true
        });
      } catch (error) {
        console.error('Error refreshing permissions:', error);
        socket.emit('permission_refresh_complete', {
          userId: data.userId,
          tenantId: data.tenantId,
          success: false
        });
      }
    });

    // Handle disconnection
    socket.on('disconnect', (reason) => {
      console.log('🔌 Permission WebSocket client disconnected:', socket.id, 'Reason:', reason);
    });

    // Handle errors
    socket.on('error', (error) => {
      console.error('❌ Permission WebSocket error:', error);
    });
  });

  // Start the server on a different port for WebSocket
  const port = process.env.WEBSOCKET_PORT || 3001;
  httpServer.listen(port, () => {
    console.log(`🚀 Permission WebSocket server running on port ${port}`);
  });

  return io;
}

// Broadcast permission updates to specific users
export async function broadcastPermissionUpdate(
  userId: string,
  tenantId: string,
  updateData: {
    type: 'permission_update' | 'role_update' | 'permission_revoke';
    permissions?: any[];
    roles?: any[];
    effectivePermissions?: any[];
    reason?: string;
  }
) {
  if (!io) {
    console.warn('WebSocket server not initialized, cannot broadcast permission update');
    return;
  }

  const roomName = `permissions:${userId}:${tenantId}`;
  const event = {
    ...updateData,
    userId,
    tenantId,
    timestamp: new Date().toISOString()
  };

  io.to(roomName).emit('permission_updated', event);
  console.log(`📡 Broadcasted permission update to room: ${roomName}`);
}

// Broadcast bulk permission updates
export async function broadcastBulkPermissionUpdate(
  updates: Array<{
    userId: string;
    tenantId: string;
    type: 'permission_update' | 'role_update' | 'permission_revoke';
    permissions?: any[];
    roles?: any[];
    effectivePermissions?: any[];
    reason?: string;
  }>
) {
  if (!io) {
    console.warn('WebSocket server not initialized, cannot broadcast bulk permission updates');
    return;
  }

  // Group updates by room
  const updatesByRoom: Record<string, any[]> = {};
  
  updates.forEach(update => {
    const roomName = `permissions:${update.userId}:${update.tenantId}`;
    if (!updatesByRoom[roomName]) {
      updatesByRoom[roomName] = [];
    }
    updatesByRoom[roomName].push({
      ...update,
      timestamp: new Date().toISOString()
    });
  });

  // Broadcast to each room
  Object.entries(updatesByRoom).forEach(([roomName, roomUpdates]) => {
    if (roomUpdates.length === 1) {
      io?.to(roomName).emit('permission_updated', roomUpdates[0]);
    } else {
      io?.to(roomName).emit('bulk_permission_update', roomUpdates);
    }
  });

  console.log(`📡 Broadcasted ${updates.length} permission updates to ${Object.keys(updatesByRoom).length} rooms`);
}

// HTTP endpoints for WebSocket management
export async function GET(request: NextRequest) {
  try {
    // Initialize WebSocket server if not already done
    if (!io) {
      initializeSocketServer();
    }

    return NextResponse.json({
      success: true,
      message: 'Permission WebSocket server is running',
      connectedClients: io?.engine.clientsCount || 0,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error in WebSocket GET endpoint:', error);
    return NextResponse.json(
      { error: 'Failed to initialize WebSocket server' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { action, userId, tenantId, updateData } = body;

    // Initialize WebSocket server if not already done
    if (!io) {
      initializeSocketServer();
    }

    switch (action) {
      case 'broadcast_update':
        await broadcastPermissionUpdate(userId, tenantId, updateData);
        break;
      
      case 'broadcast_bulk_update':
        await broadcastBulkPermissionUpdate(updateData);
        break;
      
      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        );
    }

    return NextResponse.json({
      success: true,
      message: 'Permission update broadcasted successfully'
    });
  } catch (error) {
    console.error('Error in WebSocket POST endpoint:', error);
    return NextResponse.json(
      { error: 'Failed to broadcast permission update' },
      { status: 500 }
    );
  }
}

// Export the socket server instance for use in other parts of the application
export { io as permissionSocketServer };

// Initialize the server when this module is loaded
if (process.env.NODE_ENV !== 'test') {
  initializeSocketServer();
}
