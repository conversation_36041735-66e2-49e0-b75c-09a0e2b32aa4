'use client';

import React from 'react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { cn } from '@/lib/utils';

interface DateInputProps {
  value?: Date;
  onChange?: (date: Date | undefined) => void;
  placeholder?: string;
  disabled?: boolean;
  className?: string;
  label?: string;
  error?: string;
  min?: string;
  max?: string;
  required?: boolean;
}

export function DateInput({
  value,
  onChange,
  placeholder,
  disabled,
  className,
  label,
  error,
  min,
  max,
  required,
}: DateInputProps) {
  // Convert Date to YYYY-MM-DD format for HTML input
  const formatDateForInput = (date: Date | undefined): string => {
    if (!date) return '';
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  };

  // Convert YYYY-MM-DD string to Date object
  const parseDateFromInput = (dateString: string): Date | undefined => {
    if (!dateString) return undefined;
    const date = new Date(dateString + 'T00:00:00');
    return isNaN(date.getTime()) ? undefined : date;
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const dateString = e.target.value;
    const date = parseDateFromInput(dateString);
    onChange?.(date);
  };

  return (
    <div className="space-y-2">
      {label && (
        <Label className={cn(required && "after:content-['*'] after:ml-0.5 after:text-red-500")}>
          {label}
        </Label>
      )}
      <Input
        type="date"
        value={formatDateForInput(value)}
        onChange={handleChange}
        placeholder={placeholder}
        disabled={disabled}
        className={cn(
          "w-full",
          error && "border-red-500 focus:border-red-500 focus:ring-red-500",
          className
        )}
        min={min}
        max={max}
        required={required}
      />
      {error && (
        <p className="text-sm text-red-600">{error}</p>
      )}
    </div>
  );
}

// For use with react-hook-form
interface FormDateInputProps extends DateInputProps {
  field?: {
    value: Date | undefined;
    onChange: (date: Date | undefined) => void;
    onBlur: () => void;
    name: string;
  };
}

export function FormDateInput({ field, ...props }: FormDateInputProps) {
  return (
    <DateInput
      value={field?.value}
      onChange={field?.onChange}
      {...props}
    />
  );
}
