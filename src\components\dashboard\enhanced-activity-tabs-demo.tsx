'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent } from '@/components/ui/tabs';
import {
  PlusIcon,
  EyeIcon,
  UserIcon,
  BuildingOfficeIcon,
  ChartBarIcon,
  DocumentIcon,
  RefreshCcwIcon
} from '@heroicons/react/24/outline';
import { cn } from '@/lib/utils';

interface MockActivity {
  id: string;
  type: 'lead' | 'opportunity' | 'contact' | 'company';
  action: string;
  description: string;
  timestamp: Date;
  userName: string;
}

/**
 * Enhanced Activity Tabs Demo Component
 * Showcases the improved tabs design for Recent Activities
 */
export function EnhancedActivityTabsDemo() {
  const [selectedTab, setSelectedTab] = useState("all");
  const [loading, setLoading] = useState(false);

  // Mock data for demonstration
  const mockActivities: MockActivity[] = [
    {
      id: '1',
      type: 'opportunity',
      action: 'updated',
      description: 'Opportunity updated: وزارة الدخل opportunity',
      timestamp: new Date(Date.now() - 46 * 60 * 1000), // 46 minutes ago
      userName: 'Ahmed Mohamed'
    },
    {
      id: '2',
      type: 'lead',
      action: 'created',
      description: 'New lead: Ahmed Mohamed',
      timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000), // 6 hours ago
      userName: 'Sarah Johnson'
    },
    {
      id: '3',
      type: 'contact',
      action: 'updated',
      description: 'Contact information updated for John Smith',
      timestamp: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000), // 2 days ago
      userName: 'Mike Wilson'
    },
    {
      id: '4',
      type: 'company',
      action: 'created',
      description: 'New company added: Tech Solutions Inc.',
      timestamp: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000), // 3 days ago
      userName: 'Lisa Chen'
    },
    {
      id: '5',
      type: 'lead',
      action: 'converted',
      description: 'Lead converted to opportunity: Digital Marketing Campaign',
      timestamp: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000), // 5 days ago
      userName: 'David Brown'
    }
  ];

  const tabs = [
    { 
      name: "All", 
      value: "all", 
      count: mockActivities.length,
      icon: DocumentIcon,
      color: "bg-gray-100 text-gray-600 dark:bg-gray-800 dark:text-gray-400"
    },
    { 
      name: "Leads", 
      value: "lead", 
      count: mockActivities.filter(a => a.type === 'lead').length,
      icon: UserIcon,
      color: "bg-blue-100 text-blue-600 dark:bg-blue-900/20 dark:text-blue-400"
    },
    { 
      name: "Opportunities", 
      value: "opportunity", 
      count: mockActivities.filter(a => a.type === 'opportunity').length,
      icon: ChartBarIcon,
      color: "bg-green-100 text-green-600 dark:bg-green-900/20 dark:text-green-400"
    },
    { 
      name: "Contacts", 
      value: "contact", 
      count: mockActivities.filter(a => a.type === 'contact').length,
      icon: UserIcon,
      color: "bg-purple-100 text-purple-600 dark:bg-purple-900/20 dark:text-purple-400"
    },
    { 
      name: "Companies", 
      value: "company", 
      count: mockActivities.filter(a => a.type === 'company').length,
      icon: BuildingOfficeIcon,
      color: "bg-orange-100 text-orange-600 dark:bg-orange-900/20 dark:text-orange-400"
    },
  ];

  const filteredActivities = selectedTab === "all" 
    ? mockActivities 
    : mockActivities.filter(activity => activity.type === selectedTab);

  const formatTimestamp = (timestamp: Date) => {
    const now = new Date();
    const diff = now.getTime() - timestamp.getTime();
    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));

    if (minutes < 60) {
      return `${minutes}m ago`;
    } else if (hours < 24) {
      return `${hours}h ago`;
    } else {
      return `${days}d ago`;
    }
  };

  const handleRefresh = () => {
    setLoading(true);
    setTimeout(() => setLoading(false), 1000);
  };

  const handleAddNew = (type: string) => {
    console.log(`Add new ${type}`);
  };

  const handleViewItem = (activity: MockActivity) => {
    console.log('View activity:', activity);
  };

  return (
    <Card className="w-full max-w-4xl">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Enhanced Recent Activities</CardTitle>
            <CardDescription>
              Latest updates and interactions across your CRM with improved tabs design
            </CardDescription>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" onClick={handleRefresh} disabled={loading}>
              <RefreshCcwIcon className={cn("h-4 w-4 mr-1", loading && "animate-spin")} />
              Refresh
            </Button>
            <Button size="sm" onClick={() => handleAddNew('lead')}>
              <PlusIcon className="h-4 w-4 mr-1" />
              Add New
            </Button>
          </div>
        </div>
      </CardHeader>
      
      <CardContent>
        <Tabs value={selectedTab} onValueChange={setSelectedTab} className="space-y-6">
          {/* Enhanced Tab Navigation */}
          <div className="flex flex-wrap gap-1 p-1 bg-muted/30 rounded-lg overflow-x-auto">
            {tabs.map((tab, index) => {
              const Icon = tab.icon;
              const isActive = selectedTab === tab.value;
              
              return (
                <button
                  key={tab.value}
                  onClick={() => setSelectedTab(tab.value)}
                  onKeyDown={(e) => {
                    if (e.key === 'ArrowRight' && index < tabs.length - 1) {
                      setSelectedTab(tabs[index + 1].value);
                    } else if (e.key === 'ArrowLeft' && index > 0) {
                      setSelectedTab(tabs[index - 1].value);
                    }
                  }}
                  className={cn(
                    "flex items-center gap-2 px-3 py-2 rounded-md text-sm font-medium transition-all duration-200",
                    "hover:bg-background/80 focus:outline-none focus:ring-2 focus:ring-primary/20",
                    "min-w-0 flex-shrink-0",
                    isActive 
                      ? "bg-background shadow-sm text-foreground border border-border/50" 
                      : "text-muted-foreground hover:text-foreground"
                  )}
                  role="tab"
                  aria-selected={isActive}
                  aria-controls={`tabpanel-${tab.value}`}
                  tabIndex={isActive ? 0 : -1}
                >
                  <div className={cn(
                    "p-1 rounded-full transition-colors flex-shrink-0",
                    isActive ? tab.color : "bg-muted/50 text-muted-foreground"
                  )}>
                    <Icon className="h-3 w-3" />
                  </div>
                  <span className="whitespace-nowrap hidden sm:inline">{tab.name}</span>
                  <span className="whitespace-nowrap sm:hidden">{tab.name.charAt(0)}</span>
                  {tab.count > 0 && (
                    <Badge 
                      variant={isActive ? "default" : "secondary"} 
                      className={cn(
                        "text-xs px-1.5 py-0.5 min-w-[1.25rem] h-5 flex items-center justify-center flex-shrink-0",
                        isActive ? "bg-primary/10 text-primary border-primary/20" : ""
                      )}
                    >
                      {tab.count}
                    </Badge>
                  )}
                </button>
              );
            })}
          </div>

          {/* Enhanced Tab Content */}
          <TabsContent 
            value={selectedTab} 
            className="space-y-4"
            id={`tabpanel-${selectedTab}`}
            role="tabpanel"
            aria-labelledby={`tab-${selectedTab}`}
          >
            {loading ? (
              <div className="space-y-3">
                {Array.from({ length: 3 }).map((_, i) => (
                  <div key={i} className="flex items-center space-x-3 p-4 rounded-lg border animate-pulse">
                    <div className="w-10 h-10 bg-muted rounded-full" />
                    <div className="flex-1 space-y-2">
                      <div className="h-4 bg-muted rounded w-3/4" />
                      <div className="h-3 bg-muted rounded w-1/2" />
                    </div>
                    <div className="w-8 h-8 bg-muted rounded" />
                  </div>
                ))}
              </div>
            ) : filteredActivities.length === 0 ? (
              <div className="text-center py-12">
                <div className="mx-auto w-16 h-16 bg-muted/30 rounded-full flex items-center justify-center mb-4">
                  {selectedTab !== 'all' ? (
                    <div className={cn("p-2 rounded-full", tabs.find(t => t.value === selectedTab)?.color)}>
                      {React.createElement(tabs.find(t => t.value === selectedTab)?.icon || DocumentIcon, { className: "h-6 w-6" })}
                    </div>
                  ) : (
                    <DocumentIcon className="h-6 w-6 text-muted-foreground" />
                  )}
                </div>
                <h3 className="text-sm font-medium text-foreground mb-2">
                  No {selectedTab === 'all' ? 'recent activities' : `${selectedTab} activities`} found
                </h3>
                <p className="text-sm text-muted-foreground mb-4">
                  {selectedTab === 'all' 
                    ? 'Get started by creating your first lead, contact, or opportunity.'
                    : `Create your first ${selectedTab} to see activities here.`
                  }
                </p>
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={() => handleAddNew(selectedTab === 'all' ? 'lead' : selectedTab)}
                  className="gap-2"
                >
                  <PlusIcon className="h-4 w-4" />
                  Create {selectedTab === 'all' ? 'New Item' : selectedTab}
                </Button>
              </div>
            ) : (
              <div className="space-y-2">
                {filteredActivities.map((activity) => {
                  const tabInfo = tabs.find(t => t.value === activity.type);
                  const Icon = tabInfo?.icon || DocumentIcon;
                  
                  return (
                    <div
                      key={activity.id}
                      className={cn(
                        "group flex items-center justify-between p-4 rounded-lg transition-all duration-200",
                        "hover:bg-muted/50 hover:shadow-sm border border-transparent hover:border-border/50",
                        "focus-within:ring-2 focus-within:ring-primary/20"
                      )}
                    >
                      <div className="flex items-start space-x-3 flex-1 min-w-0">
                        <div className={cn(
                          "flex-shrink-0 p-2.5 rounded-full transition-colors",
                          tabInfo?.color || "bg-muted text-muted-foreground"
                        )}>
                          <Icon className="h-4 w-4" />
                        </div>
                        
                        <div className="flex-1 min-w-0">
                          <div className="flex items-start justify-between gap-2">
                            <p className="text-sm font-medium text-foreground leading-5 line-clamp-2">
                              {activity.description}
                            </p>
                            <span className="text-xs text-muted-foreground whitespace-nowrap">
                              {formatTimestamp(activity.timestamp)}
                            </span>
                          </div>
                          
                          <div className="flex items-center gap-2 mt-2">
                            <Badge 
                              variant="outline" 
                              className={cn(
                                "text-xs px-2 py-0.5 border-0",
                                tabInfo?.color || "bg-muted text-muted-foreground"
                              )}
                            >
                              {activity.type}
                            </Badge>
                            
                            <span className="text-xs text-muted-foreground">•</span>
                            <span className="text-xs text-muted-foreground">
                              by {activity.userName}
                            </span>
                          </div>
                        </div>
                      </div>
                      
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleViewItem(activity)}
                        className="opacity-0 group-hover:opacity-100 transition-opacity ml-2 flex-shrink-0"
                      >
                        <EyeIcon className="h-4 w-4" />
                        <span className="sr-only">View details</span>
                      </Button>
                    </div>
                  );
                })}
              </div>
            )}
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
