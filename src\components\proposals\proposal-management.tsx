'use client';

import React, { useState, useEffect, useMemo } from 'react';
import { useRouter } from 'next/navigation';
import {
  Plus,
  Search,
  Filter,
  MoreHorizontal,
  Edit,
  Trash2,
  FileText,
  Eye,
  Copy,
  Download,
  Building,
  Calendar,
  User,
  TrendingUp
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { PermissionGate } from '@/components/ui/permission-gate';
import { PermissionAwareTable } from '@/components/ui/permission-aware-table';
import { EmptyState } from '@/components/ui/empty-state';
import { usePermissions } from '@/components/providers/permission-provider';
import { PermissionResource, PermissionAction } from '@/types';
import { toast } from 'sonner';
import { useSmoothNavigation } from '@/hooks/use-smooth-navigation';
import { useEnhancedDeleteDialog, deleteConfigurations } from '@/components/ui/enhanced-delete-dialog';

interface ProposalData {
  id: string;
  title: string;
  status: string;
  version: number;
  totalAmount: number | null;
  isAiGenerated: boolean;
  generationStatus: string;
  createdAt: string;
  updatedAt: string;
  opportunity?: {
    id: string;
    title: string;
    company?: {
      id: string;
      name: string;
    };
  };
  createdBy?: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
  };
  statistics: {
    totalSections: number;
    completedSections: number;
    totalCharts: number;
    completedCharts: number;
  };
}

interface ProposalFilters {
  search: string;
  status: string;
  dateRange: string;
  page: number;
}

export function ProposalManagement() {
  const router = useRouter();
  const { navigate } = useSmoothNavigation();
  const { hasPermission } = usePermissions();
  const { showDeleteDialog, DialogComponent } = useEnhancedDeleteDialog();

  const [proposals, setProposals] = useState<ProposalData[]>([]);
  const [loading, setLoading] = useState(true);
  const [totalPages, setTotalPages] = useState(1);
  const [totalCount, setTotalCount] = useState(0);
  const [filters, setFilters] = useState<ProposalFilters>({
    search: '',
    status: 'all',
    dateRange: 'all',
    page: 1
  });

  // Load proposals
  const loadProposals = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        search: filters.search,
        status: filters.status,
        dateRange: filters.dateRange,
        page: filters.page.toString(),
        limit: '20'
      });

      const response = await fetch(`/api/proposals?${params}`);
      
      if (response.ok) {
        const result = await response.json();
        setProposals(result.data.proposals);
        setTotalPages(result.data.pagination.totalPages);
        setTotalCount(result.data.pagination.totalCount);
      } else {
        toast.error('Failed to load proposals');
      }
    } catch (error) {
      console.error('Error loading proposals:', error);
      toast.error('Failed to load proposals');
    } finally {
      setLoading(false);
    }
  };

  // Handle filter changes
  const handleFilterChange = (key: keyof ProposalFilters, value: string | number) => {
    setFilters(prev => ({
      ...prev,
      [key]: value,
      page: key !== 'page' ? 1 : value // Reset page when other filters change
    }));
  };

  // Handle page change
  const handlePageChange = (page: number) => {
    handleFilterChange('page', page);
  };

  // Delete proposal
  const handleDelete = async (proposalId: string) => {
    try {
      const response = await fetch(`/api/proposals/${proposalId}`, {
        method: 'DELETE'
      });

      if (response.ok) {
        toast.success('Proposal deleted successfully');
        loadProposals();
      } else {
        toast.error('Failed to delete proposal');
      }
    } catch (error) {
      console.error('Error deleting proposal:', error);
      toast.error('Failed to delete proposal');
    }
  };

  // Duplicate proposal
  const handleDuplicate = async (proposalId: string) => {
    try {
      const response = await fetch(`/api/proposals/${proposalId}/duplicate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          title: `Copy of ${proposals.find(p => p.id === proposalId)?.title}`,
          versionType: 'copy',
          includeContent: true,
          includeCharts: true
        })
      });

      if (response.ok) {
        const result = await response.json();
        toast.success('Proposal duplicated successfully');
        navigate(`/proposals/${result.data.proposal.id}`);
      } else {
        toast.error('Failed to duplicate proposal');
      }
    } catch (error) {
      console.error('Error duplicating proposal:', error);
      toast.error('Failed to duplicate proposal');
    }
  };

  // Get status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'draft': return 'bg-gray-100 text-gray-800';
      case 'generating': return 'bg-blue-100 text-blue-800';
      case 'generated': return 'bg-green-100 text-green-800';
      case 'reviewed': return 'bg-purple-100 text-purple-800';
      case 'approved': return 'bg-emerald-100 text-emerald-800';
      case 'sent': return 'bg-orange-100 text-orange-800';
      case 'accepted': return 'bg-green-100 text-green-800';
      case 'rejected': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  // Table columns
  const columns = useMemo(() => [
    {
      key: 'title',
      label: 'Proposal Title',
      render: (proposal: ProposalData) => (
        <div className="flex flex-col">
          <button
            onClick={() => navigate(`/proposals/${proposal.id}`)}
            className="font-medium text-gray-900 hover:text-primary-600 dark:hover:text-primary-400 transition-colors hover:underline focus:outline-none focus:underline text-left"
          >
            {proposal.title}
          </button>
          <div className="flex items-center gap-2 mt-1">
            <Badge className={getStatusColor(proposal.status)} variant="secondary">
              {proposal.status}
            </Badge>
            {proposal.isAiGenerated && (
              <Badge variant="outline" className="text-xs">
                AI Generated
              </Badge>
            )}
          </div>
        </div>
      )
    },
    {
      key: 'opportunity',
      label: 'Opportunity',
      render: (proposal: ProposalData) => (
        <div className="flex flex-col">
          {proposal.opportunity ? (
            <>
              <button
                onClick={() => navigate(`/opportunities/${proposal.opportunity?.id}`)}
                className="font-medium text-gray-900 hover:text-primary-600 dark:hover:text-primary-400 transition-colors hover:underline focus:outline-none focus:underline text-left"
              >
                {proposal.opportunity.title}
              </button>
              {proposal.opportunity.company && (
                <span className="text-sm text-gray-500">
                  {proposal.opportunity.company.name}
                </span>
              )}
            </>
          ) : (
            <span className="text-gray-500">No opportunity</span>
          )}
        </div>
      )
    },
    {
      key: 'progress',
      label: 'Progress',
      render: (proposal: ProposalData) => (
        <div className="text-sm">
          <div>
            Sections: {proposal.statistics.completedSections}/{proposal.statistics.totalSections}
          </div>
          <div>
            Charts: {proposal.statistics.completedCharts}/{proposal.statistics.totalCharts}
          </div>
        </div>
      )
    },
    {
      key: 'createdBy',
      label: 'Created By',
      render: (proposal: ProposalData) => (
        <div className="text-sm">
          {proposal.createdBy ? (
            <div>
              <div className="font-medium">
                {proposal.createdBy.firstName} {proposal.createdBy.lastName}
              </div>
              <div className="text-gray-500">{proposal.createdBy.email}</div>
            </div>
          ) : (
            <span className="text-gray-500">Unknown</span>
          )}
        </div>
      )
    },
    {
      key: 'createdAt',
      label: 'Created',
      render: (proposal: ProposalData) => (
        <div className="text-sm text-gray-500">
          {new Date(proposal.createdAt).toLocaleDateString()}
        </div>
      )
    }
  ], [navigate]);

  // Table actions
  const tableActions = useMemo(() => [
    {
      label: 'View',
      icon: Eye,
      onClick: (proposal: ProposalData) => navigate(`/proposals/${proposal.id}`),
      permission: { resource: PermissionResource.PROPOSALS, action: PermissionAction.READ },
      tooltip: 'View proposal details',
      color: 'blue' as const
    },
    {
      label: 'Duplicate',
      icon: Copy,
      onClick: (proposal: ProposalData) => handleDuplicate(proposal.id),
      permission: { resource: PermissionResource.PROPOSALS, action: PermissionAction.CREATE },
      tooltip: 'Create a copy of this proposal',
      color: 'green' as const
    },
    {
      label: 'Delete',
      icon: Trash2,
      onClick: (proposal: ProposalData) => {
        showDeleteDialog({
          ...deleteConfigurations.proposal(proposal.title),
          onConfirm: async () => {
            await handleDelete(proposal.id);
          }
        });
      },
      permission: { resource: PermissionResource.PROPOSALS, action: PermissionAction.DELETE },
      tooltip: 'Delete this proposal',
      color: 'red' as const
    }
  ], [navigate, handleDuplicate, showDeleteDialog]);

  useEffect(() => {
    loadProposals();
  }, [filters]);

  return (
    <div className="space-y-6">
      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Search & Filters</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search proposals..."
                value={filters.search}
                onChange={(e) => handleFilterChange('search', e.target.value)}
                className="pl-8"
              />
            </div>
            <Select
              value={filters.status}
              onValueChange={(value) => handleFilterChange('status', value)}
            >
              <SelectTrigger className="w-full sm:w-[180px]">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="draft">Draft</SelectItem>
                <SelectItem value="generating">Generating</SelectItem>
                <SelectItem value="generated">Generated</SelectItem>
                <SelectItem value="reviewed">Reviewed</SelectItem>
                <SelectItem value="approved">Approved</SelectItem>
                <SelectItem value="sent">Sent</SelectItem>
                <SelectItem value="accepted">Accepted</SelectItem>
                <SelectItem value="rejected">Rejected</SelectItem>
              </SelectContent>
            </Select>
            <Select
              value={filters.dateRange}
              onValueChange={(value) => handleFilterChange('dateRange', value)}
            >
              <SelectTrigger className="w-full sm:w-[180px]">
                <SelectValue placeholder="Date Range" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Time</SelectItem>
                <SelectItem value="today">Today</SelectItem>
                <SelectItem value="week">This Week</SelectItem>
                <SelectItem value="month">This Month</SelectItem>
                <SelectItem value="quarter">This Quarter</SelectItem>
                <SelectItem value="year">This Year</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Proposals Table */}
      {proposals.length === 0 && !loading ? (
        <Card>
          <CardContent>
            <EmptyState
              icon={<FileText className="w-12 h-12" />}
              title="No proposals found"
              description="Get started by creating your first proposal from an opportunity."
              action={{
                label: 'View Opportunities',
                onClick: () => navigate('/opportunities'),
                variant: 'primary'
              }}
            />
          </CardContent>
        </Card>
      ) : (
        <Card>
          <PermissionAwareTable
            data={proposals}
            columns={columns}
            actions={tableActions}
            resource={PermissionResource.PROPOSALS}
            idField="id"
            ownershipField="createdBy.id"
            loading={loading}
            emptyMessage="No proposals found"
          />
        </Card>
      )}

      {/* Pagination */}
      {totalPages > 1 && (
        <Card>
          <CardContent>
            <div className="flex items-center justify-between">
              <div className="text-sm text-gray-700 dark:text-gray-300">
                Page {filters.page} of {totalPages} ({totalCount} total proposals)
              </div>
              <div className="flex space-x-2">
                <Button
                  variant="outline"
                  onClick={() => handlePageChange(filters.page - 1)}
                  disabled={filters.page === 1}
                  size="sm"
                >
                  Previous
                </Button>
                <Button
                  variant="outline"
                  onClick={() => handlePageChange(filters.page + 1)}
                  disabled={filters.page === totalPages}
                  size="sm"
                >
                  Next
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      <DialogComponent />
    </div>
  );
}
