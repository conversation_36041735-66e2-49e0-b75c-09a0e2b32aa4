import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { PermissionResource, PermissionAction } from '@/types';
import { permissionService } from '@/services/permission-service';
import { cacheService, CACHE_PREFIXES, CACHE_TTL } from '@/lib/redis';

export interface PermissionMiddlewareOptions {
  resource: PermissionResource;
  action: PermissionAction;
  allowSuperAdmin?: boolean;
  requireOwnership?: boolean;
  getResourceOwnerId?: (req: NextRequest) => Promise<string>;
  skipAuditLog?: boolean; // New option to skip audit logging for performance
}

export interface AuthenticatedRequest extends NextRequest {
  userId: string;
  tenantId: string;
  session: any;
  isSuperAdmin: boolean;
}

// In-memory cache for permission checks (short-lived)
const permissionCache = new Map<string, { hasPermission: boolean; timestamp: number }>();
const PERMISSION_CACHE_TTL = 60 * 1000; // 1 minute

/**
 * Optimized middleware to check if user has required permission
 * Includes caching and reduced database calls
 */
export function withOptimizedPermission(options: PermissionMiddlewareOptions) {
  return function (handler: (req: AuthenticatedRequest, context?: any) => Promise<NextResponse>) {
    return async function (req: NextRequest, context?: any): Promise<NextResponse> {
      try {
        // Get user session with caching
        const session = await getServerSession(authOptions);
        
        if (!session?.user) {
          return NextResponse.json(
            { error: 'Authentication required' },
            { status: 401 }
          );
        }

        const userId = session.user.id;
        const isSuperAdmin = session.user.isPlatformAdmin || false;

        // Allow super admin if specified
        if (options.allowSuperAdmin && isSuperAdmin) {
          const authenticatedReq = req as AuthenticatedRequest;
          authenticatedReq.userId = userId;
          authenticatedReq.tenantId = ''; // Super admin has no tenant context
          authenticatedReq.session = session;
          authenticatedReq.isSuperAdmin = true;
          return handler(authenticatedReq, context);
        }

        // Get tenant context for regular users
        if (isSuperAdmin && !options.allowSuperAdmin) {
          return NextResponse.json(
            { error: 'This endpoint is not available for super admins' },
            { status: 403 }
          );
        }

        // Get tenant ID from session
        const tenantId = session.user.tenants?.[0]?.tenantId;
        if (!tenantId) {
          return NextResponse.json(
            { error: 'No tenant context found' },
            { status: 403 }
          );
        }

        // Check permission with caching
        const permissionKey = `${userId}:${tenantId}:${options.resource}:${options.action}`;
        const cachedPermission = permissionCache.get(permissionKey);
        
        let hasPermission = false;
        
        if (cachedPermission && Date.now() - cachedPermission.timestamp < PERMISSION_CACHE_TTL) {
          // Use cached permission
          hasPermission = cachedPermission.hasPermission;
        } else {
          // Check permission and cache result
          hasPermission = await permissionService.hasPermission(
            userId,
            tenantId,
            options.resource,
            options.action
          );
          
          // Cache the result
          permissionCache.set(permissionKey, {
            hasPermission,
            timestamp: Date.now()
          });
          
          // Clean up old cache entries periodically
          if (permissionCache.size > 1000) {
            const now = Date.now();
            for (const [key, value] of permissionCache.entries()) {
              if (now - value.timestamp > PERMISSION_CACHE_TTL) {
                permissionCache.delete(key);
              }
            }
          }
        }

        if (!hasPermission) {
          // Only log unauthorized access if not skipping audit logs
          if (!options.skipAuditLog) {
            // Use async logging to not block the response
            setImmediate(async () => {
              try {
                const { prisma } = await import('@/lib/prisma');
                await prisma.auditLog.create({
                  data: {
                    tenantId,
                    userId,
                    action: 'UNAUTHORIZED_ACCESS_ATTEMPT',
                    resourceType: options.resource,
                    newValues: {
                      requiredPermission: `${options.resource}.${options.action.toLowerCase()}`,
                      endpoint: req.url,
                      method: req.method
                    }
                  }
                });
              } catch (error) {
                console.error('Failed to log unauthorized access:', error);
              }
            });
          }

          return NextResponse.json(
            { 
              error: 'Insufficient permissions',
              required: `${options.resource}.${options.action.toLowerCase()}`
            },
            { status: 403 }
          );
        }

        // Check resource ownership if required
        if (options.requireOwnership && options.getResourceOwnerId) {
          const resourceOwnerId = await options.getResourceOwnerId(req);
          if (resourceOwnerId !== userId) {
            return NextResponse.json(
              { error: 'Access denied: You can only access your own resources' },
              { status: 403 }
            );
          }
        }

        // Add user context to request
        const authenticatedReq = req as AuthenticatedRequest;
        authenticatedReq.userId = userId;
        authenticatedReq.tenantId = tenantId;
        authenticatedReq.session = session;
        authenticatedReq.isSuperAdmin = false;

        return handler(authenticatedReq, context);
      } catch (error) {
        console.error('Permission middleware error:', error);
        return NextResponse.json(
          { error: 'Internal server error' },
          { status: 500 }
        );
      }
    };
  };
}

/**
 * Clear permission cache for a specific user or all users
 */
export function clearPermissionCache(userId?: string, tenantId?: string) {
  if (userId && tenantId) {
    // Clear cache for specific user
    const keysToDelete = Array.from(permissionCache.keys()).filter(key => 
      key.startsWith(`${userId}:${tenantId}:`)
    );
    keysToDelete.forEach(key => permissionCache.delete(key));
  } else {
    // Clear all cache
    permissionCache.clear();
  }
}

/**
 * Get permission cache statistics
 */
export function getPermissionCacheStats() {
  const now = Date.now();
  let validEntries = 0;
  let expiredEntries = 0;
  
  for (const [key, value] of permissionCache.entries()) {
    if (now - value.timestamp < PERMISSION_CACHE_TTL) {
      validEntries++;
    } else {
      expiredEntries++;
    }
  }
  
  return {
    totalEntries: permissionCache.size,
    validEntries,
    expiredEntries,
    hitRate: validEntries / (validEntries + expiredEntries) || 0
  };
}
