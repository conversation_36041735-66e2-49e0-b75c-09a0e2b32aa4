import { aiService, AIRequest } from './ai-service';
import { prisma } from '@/lib/prisma';

export interface EmailContext {
  recipientName: string;
  recipientEmail: string;
  recipientCompany?: string;
  recipientJobTitle?: string;
  senderName: string;
  senderCompany: string;
  senderJobTitle: string;
  relationship: 'new_lead' | 'existing_contact' | 'customer' | 'prospect';
  previousInteractions?: string[];
  leadScore?: number;
  industry?: string;
  locale: 'en' | 'ar';
}

export interface EmailTemplate {
  id: string;
  name: string;
  type: EmailType;
  subject: string;
  content: string;
  variables: string[];
  locale: 'en' | 'ar';
  tenantId: string;
}

export type EmailType = 
  | 'cold_outreach'
  | 'follow_up'
  | 'meeting_request'
  | 'proposal_follow_up'
  | 'thank_you'
  | 'nurturing'
  | 're_engagement'
  | 'introduction';

export interface GeneratedEmail {
  subject: string;
  content: string;
  tone: string;
  personalizations: string[];
  suggestions: string[];
  confidence: number;
  locale: 'en' | 'ar';
}

export class AIEmailGenerationService {
  private readonly EMAIL_GENERATION_PROMPTS = {
    en: {
      system: `You are an expert sales email writer. Generate professional, personalized, and effective sales emails that drive engagement and responses.

Key principles:
1. Keep emails concise and scannable
2. Personalize based on recipient context
3. Include clear value propositions
4. Use compelling subject lines
5. Include clear call-to-actions
6. Maintain professional but friendly tone
7. Avoid spam triggers and overly salesy language

Always provide:
- Compelling subject line
- Personalized email body
- Clear next steps
- Professional signature placeholder`,

      cold_outreach: `Write a cold outreach email that:
- Captures attention with a personalized opening
- Demonstrates research about their company/role
- Presents a clear value proposition
- Includes social proof if relevant
- Ends with a soft ask for a brief conversation`,

      follow_up: `Write a follow-up email that:
- References previous interaction
- Adds new value or information
- Addresses any concerns raised
- Maintains momentum without being pushy
- Includes a clear next step`,

      meeting_request: `Write a meeting request email that:
- Clearly states the purpose
- Suggests specific time options
- Explains the value of the meeting
- Keeps it brief and professional
- Makes it easy to accept or propose alternatives`
    },
    ar: {
      system: `أنت كاتب بريد إلكتروني مبيعات خبير. اكتب رسائل بريد إلكتروني مهنية ومخصصة وفعالة تحفز التفاعل والردود.

المبادئ الأساسية:
1. اجعل الرسائل موجزة وسهلة القراءة
2. خصص الرسالة حسب سياق المستقبل
3. اشمل عروض قيمة واضحة
4. استخدم عناوين جذابة
5. اشمل دعوات واضحة للعمل
6. حافظ على نبرة مهنية ولكن ودية
7. تجنب محفزات الرسائل المزعجة واللغة المبيعية المفرطة

قدم دائماً:
- عنوان جذاب
- نص رسالة مخصص
- خطوات تالية واضحة
- توقيع مهني`,

      cold_outreach: `اكتب رسالة تواصل أولى تتضمن:
- افتتاحية مخصصة تجذب الانتباه
- إظهار البحث عن شركتهم/دورهم
- تقديم عرض قيمة واضح
- تضمين دليل اجتماعي إن أمكن
- الانتهاء بطلب لطيف لمحادثة قصيرة`,

      follow_up: `اكتب رسالة متابعة تتضمن:
- الإشارة للتفاعل السابق
- إضافة قيمة أو معلومات جديدة
- معالجة أي مخاوف مثارة
- الحفاظ على الزخم دون إلحاح
- تضمين خطوة تالية واضحة`,

      meeting_request: `اكتب رسالة طلب اجتماع تتضمن:
- بيان واضح للغرض
- اقتراح خيارات وقت محددة
- شرح قيمة الاجتماع
- الإيجاز والمهنية
- تسهيل القبول أو اقتراح بدائل`
    }
  };

  async generateEmail(
    type: EmailType,
    context: EmailContext,
    userId: string,
    options?: {
      tone?: 'professional' | 'friendly' | 'casual';
      length?: 'short' | 'medium' | 'long';
      includeCallToAction?: boolean;
      customInstructions?: string;
    }
  ): Promise<GeneratedEmail> {
    try {
      const locale = context.locale || 'en';
      const prompts = this.EMAIL_GENERATION_PROMPTS[locale];
      
      const emailRequest: AIRequest = {
        prompt: this.buildEmailPrompt(type, context, options),
        systemPrompt: `${prompts.system}\n\n${(prompts as Record<string, string>)[type] || prompts.system}`,
        context: { emailContext: context, options },
        tenantId: context.senderCompany, // Using company as tenant identifier
        userId,
        feature: 'email_generation'
      };

      const response = await aiService.generateResponse(emailRequest, {
        temperature: 0.7, // Higher temperature for creativity
        maxTokens: 1000
      });

      const generatedEmail = this.parseEmailResponse(response.content, locale);

      // Store the generated email for analytics
      await this.storeEmailGeneration(context, generatedEmail, response, userId);

      return generatedEmail;

    } catch (error) {
      console.error('AI email generation failed:', error);
      throw new Error('Failed to generate email');
    }
  }

  private buildEmailPrompt(
    type: EmailType,
    context: EmailContext,
    options?: Record<string, unknown>
  ): string {
    const locale = context.locale || 'en';
    
    const contextInfo = locale === 'ar' ? `
معلومات السياق:
- اسم المستقبل: ${context.recipientName}
- شركة المستقبل: ${context.recipientCompany || 'غير محدد'}
- منصب المستقبل: ${context.recipientJobTitle || 'غير محدد'}
- اسم المرسل: ${context.senderName}
- شركة المرسل: ${context.senderCompany}
- منصب المرسل: ${context.senderJobTitle}
- نوع العلاقة: ${context.relationship}
- الصناعة: ${context.industry || 'غير محدد'}
- نقاط الاهتمام: ${context.leadScore || 'غير محدد'}
` : `
Context Information:
- Recipient Name: ${context.recipientName}
- Recipient Company: ${context.recipientCompany || 'Not specified'}
- Recipient Job Title: ${context.recipientJobTitle || 'Not specified'}
- Sender Name: ${context.senderName}
- Sender Company: ${context.senderCompany}
- Sender Job Title: ${context.senderJobTitle}
- Relationship Type: ${context.relationship}
- Industry: ${context.industry || 'Not specified'}
- Lead Score: ${context.leadScore || 'Not specified'}
`;

    const previousInteractions = context.previousInteractions?.length ? 
      (locale === 'ar' ? 
        `\nالتفاعلات السابقة:\n${context.previousInteractions.join('\n')}` :
        `\nPrevious Interactions:\n${context.previousInteractions.join('\n')}`
      ) : '';

    const additionalInstructions = options?.customInstructions ? 
      (locale === 'ar' ? 
        `\nتعليمات إضافية: ${options.customInstructions}` :
        `\nAdditional Instructions: ${options.customInstructions}`
      ) : '';

    const toneInstruction = options?.tone ? 
      (locale === 'ar' ? 
        `\nالنبرة المطلوبة: ${options.tone}` :
        `\nDesired Tone: ${options.tone}`
      ) : '';

    const lengthInstruction = options?.length ? 
      (locale === 'ar' ? 
        `\nطول الرسالة: ${options.length}` :
        `\nEmail Length: ${options.length}`
      ) : '';

    const formatInstruction = locale === 'ar' ? `
أرجع الرد بتنسيق JSON التالي:
{
  "subject": "عنوان الرسالة",
  "content": "محتوى الرسالة",
  "tone": "النبرة المستخدمة",
  "personalizations": ["التخصيصات المستخدمة"],
  "suggestions": ["اقتراحات للتحسين"],
  "confidence": رقم بين 0 و 1
}
` : `
Return your response in this JSON format:
{
  "subject": "Email subject line",
  "content": "Email body content",
  "tone": "Tone used",
  "personalizations": ["Personalizations used"],
  "suggestions": ["Suggestions for improvement"],
  "confidence": number between 0 and 1
}
`;

    return `${contextInfo}${previousInteractions}${additionalInstructions}${toneInstruction}${lengthInstruction}

${formatInstruction}`;
  }

  private parseEmailResponse(aiResponse: string, locale: 'en' | 'ar'): GeneratedEmail {
    try {
      const jsonMatch = aiResponse.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No JSON found in AI response');
      }

      const parsed = JSON.parse(jsonMatch[0]);
      
      return {
        subject: parsed.subject || (locale === 'ar' ? 'موضوع الرسالة' : 'Email Subject'),
        content: parsed.content || (locale === 'ar' ? 'محتوى الرسالة' : 'Email content'),
        tone: parsed.tone || 'professional',
        personalizations: Array.isArray(parsed.personalizations) ? parsed.personalizations : [],
        suggestions: Array.isArray(parsed.suggestions) ? parsed.suggestions : [],
        confidence: Math.max(0, Math.min(1, parsed.confidence || 0.7)),
        locale
      };
    } catch (error) {
      console.error('Failed to parse email response:', error);
      return {
        subject: locale === 'ar' ? 'موضوع الرسالة' : 'Email Subject',
        content: locale === 'ar' ? 'فشل في إنشاء محتوى الرسالة' : 'Failed to generate email content',
        tone: 'professional',
        personalizations: [],
        suggestions: [locale === 'ar' ? 'راجع البيانات يدوياً' : 'Review data manually'],
        confidence: 0.1,
        locale
      };
    }
  }

  async generateEmailVariations(
    type: EmailType,
    context: EmailContext,
    userId: string,
    count: number = 3
  ): Promise<GeneratedEmail[]> {
    const variations: GeneratedEmail[] = [];
    
    const tones = ['professional', 'friendly', 'casual'] as const;
    const lengths = ['short', 'medium', 'long'] as const;
    
    for (let i = 0; i < count; i++) {
      try {
        const options = {
          tone: tones[i % tones.length],
          length: lengths[i % lengths.length],
          customInstructions: `This is variation ${i + 1} of ${count}. Make it distinct from other variations.`
        };
        
        const email = await this.generateEmail(type, context, userId, options);
        variations.push(email);
        
        // Add delay to avoid rate limiting
        if (i < count - 1) {
          await new Promise(resolve => setTimeout(resolve, 500));
        }
      } catch (error) {
        console.error(`Failed to generate email variation ${i + 1}:`, error);
      }
    }
    
    return variations;
  }

  async improveEmail(
    originalEmail: string,
    improvementGoals: string[],
    context: EmailContext,
    userId: string
  ): Promise<GeneratedEmail> {
    const locale = context.locale || 'en';
    
    const improvementPrompt = locale === 'ar' ? 
      `حسن هذه الرسالة الإلكترونية بناءً على الأهداف التالية:
${improvementGoals.join('\n')}

الرسالة الأصلية:
${originalEmail}

قدم نسخة محسنة مع شرح التحسينات المطبقة.` :
      `Improve this email based on the following goals:
${improvementGoals.join('\n')}

Original Email:
${originalEmail}

Provide an improved version with explanation of improvements made.`;

    const request: AIRequest = {
      prompt: improvementPrompt,
      systemPrompt: this.EMAIL_GENERATION_PROMPTS[locale].system,
      context: { originalEmail, improvementGoals, emailContext: context },
      tenantId: context.senderCompany,
      userId,
      feature: 'email_improvement'
    };

    const response = await aiService.generateResponse(request, {
      temperature: 0.6,
      maxTokens: 1200
    });

    return this.parseEmailResponse(response.content, locale);
  }

  private async storeEmailGeneration(
    context: EmailContext,
    generatedEmail: GeneratedEmail,
    aiResponse: Record<string, unknown>,
    userId: string
  ): Promise<void> {
    try {
      await prisma.aiInsight.create({
        data: {
          tenantId: context.senderCompany, // Using company as tenant
          relatedToType: 'email',
          relatedToId: context.recipientEmail,
          insightType: 'email_generation',
          data: {
            context,
            generatedEmail,
            aiResponse: {
              provider: aiResponse.provider,
              model: aiResponse.model,
              tokensUsed: aiResponse.tokensUsed,
              cost: aiResponse.cost
            }
          } as Record<string, unknown>,
          confidenceScore: generatedEmail.confidence,
          createdAt: new Date()
        }
      });
    } catch (error) {
      console.error('Failed to store email generation data:', error);
    }
  }

  async getEmailTemplates(tenantId: string, locale: 'en' | 'ar' = 'en'): Promise<EmailTemplate[]> {
    // This would typically fetch from database
    // For now, return some default templates
    const templates: EmailTemplate[] = [
      {
        id: '1',
        name: locale === 'ar' ? 'تواصل أولي' : 'Cold Outreach',
        type: 'cold_outreach',
        subject: locale === 'ar' ? 'فرصة تعاون مع {{recipientCompany}}' : 'Partnership opportunity with {{recipientCompany}}',
        content: locale === 'ar' ? 'مرحباً {{recipientName}}،\n\nأتمنى أن تكون بخير...' : 'Hi {{recipientName}},\n\nI hope this email finds you well...',
        variables: ['recipientName', 'recipientCompany', 'senderName', 'senderCompany'],
        locale,
        tenantId
      }
    ];
    
    return templates;
  }
}

export const aiEmailGenerationService = new AIEmailGenerationService();
