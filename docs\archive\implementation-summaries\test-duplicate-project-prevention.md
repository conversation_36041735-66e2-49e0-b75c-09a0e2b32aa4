# Test Plan: Duplicate Project Prevention

## Overview
This document outlines the test plan for the duplicate project prevention feature.

## Test Cases

### 1. UI Feedback Test
**Scenario**: User selects an opportunity that already has a project
**Expected Result**: 
- Warning alert appears showing existing project details
- Link to view existing project is provided
- User can still proceed with creation if needed

### 2. API Validation Test
**Scenario**: API call to create project with existing opportunity
**Expected Result**:
- API returns 400 error with descriptive message
- Error message includes link to existing project

### 3. Database Constraint Test
**Scenario**: Direct database insertion of duplicate project
**Expected Result**:
- Database constraint prevents insertion
- Unique constraint error is thrown

### 4. URL Parameter Test
**Scenario**: User navigates to /projects/new?opportunityId=123
**Expected Result**:
- Opportunity field is pre-populated
- Duplicate check runs automatically
- Warning appears if project exists

## Manual Testing Steps

1. **Setup**:
   - Create an opportunity in the system
   - Create a project linked to that opportunity

2. **Test UI Feedback**:
   - Navigate to /projects/new?opportunityId=[opportunity-id]
   - Verify opportunity is pre-selected
   - Verify warning alert appears
   - Click link to view existing project

3. **Test API Protection**:
   - Use browser dev tools or Postman
   - Send POST to /api/projects with existing opportunityId
   - Verify error response

4. **Test Direct Creation Flow**:
   - Navigate to /opportunities/[id]/create-project
   - Click "Create Project Now"
   - Verify redirect to form with pre-populated opportunity
   - Verify warning appears

## Success Criteria
- ✅ No duplicate projects can be created for same opportunity
- ✅ User receives clear feedback about existing projects
- ✅ User can easily navigate to existing project
- ✅ Database integrity is maintained
- ✅ Error messages are user-friendly
