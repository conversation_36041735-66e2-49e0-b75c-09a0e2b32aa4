'use client';

import React, { useState, useEffect, useMemo } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Textarea } from '@/components/ui/textarea';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  FileText,
  BarChart3,
  Settings,
  Layers,
  Search,
  Eye,
  Grid3X3,
  List,
  Wand2
} from 'lucide-react';
import { ProposalContentSection } from '@/types/proposal';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';

interface EnhancedContentSectionSelectorProps {
  selectedSections: string[];
  onSelectionChange: (sections: string[]) => void;
  onSectionCustomize?: (sectionType: string, customPrompt: string) => void;
  customPrompts?: Record<string, string>;
  className?: string;
}

interface SectionCategory {
  id: string;
  name: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
  color: string;
}

const SECTION_CATEGORIES: SectionCategory[] = [
  {
    id: 'overview',
    name: 'Overview',
    description: 'High-level summaries and introductions',
    icon: FileText,
    color: 'bg-blue-500'
  },
  {
    id: 'technical',
    name: 'Technical',
    description: 'Architecture and implementation details',
    icon: Settings,
    color: 'bg-green-500'
  },
  {
    id: 'project',
    name: 'Project',
    description: 'Planning, scope, and management',
    icon: Layers,
    color: 'bg-purple-500'
  },
  {
    id: 'business',
    name: 'Business',
    description: 'Commercial and strategic aspects',
    icon: BarChart3,
    color: 'bg-orange-500'
  }
];

export function EnhancedContentSectionSelector({
  selectedSections,
  onSelectionChange,
  onSectionCustomize,
  customPrompts = {},
  className
}: EnhancedContentSectionSelectorProps) {
  const [contentSections, setContentSections] = useState<ProposalContentSection[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [previewSection, setPreviewSection] = useState<ProposalContentSection | null>(null);
  const [customizeSection, setCustomizeSection] = useState<ProposalContentSection | null>(null);

  useEffect(() => {
    loadContentSections();
  }, []);

  const loadContentSections = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/proposal-content-sections?limit=100&isActive=true');

      if (!response.ok) {
        throw new Error('Failed to load content sections');
      }

      const result = await response.json();
      setContentSections(result.data || []);
    } catch (error) {
      console.error('Error loading content sections:', error);
      toast.error('Failed to load content sections');
    } finally {
      setLoading(false);
    }
  };

  // Categorize sections based on their type
  const categorizeSection = (sectionType: string): string => {
    const typeMap: { [key: string]: string } = {
      'executive_summary': 'overview',
      'scope_of_work': 'project',
      'architecture_high_level': 'technical',
      'architecture_low_level': 'technical',
      'out_of_scope': 'project',
      'assumptions': 'project',
      'project_plan': 'project',
      'resource_estimation': 'business',
      'cost_breakdown': 'business',
      'timeline': 'project',
      'risk_assessment': 'business',
      'technical_requirements': 'technical',
      'implementation_approach': 'technical'
    };
    return typeMap[sectionType] || 'project';
  };

  // Filter and search sections
  const filteredSections = useMemo(() => {
    let filtered = contentSections;

    // Filter by category
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(section => 
        categorizeSection(section.sectionType) === selectedCategory
      );
    }

    // Filter by search query
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(section =>
        section.title.toLowerCase().includes(query) ||
        section.description?.toLowerCase().includes(query) ||
        section.sectionType.toLowerCase().includes(query)
      );
    }

    // Sort by order index
    return filtered.sort((a, b) => a.orderIndex - b.orderIndex);
  }, [contentSections, selectedCategory, searchQuery]);

  // Group sections by category for display
  const sectionsByCategory = useMemo(() => {
    const grouped: { [key: string]: ProposalContentSection[] } = {};
    
    SECTION_CATEGORIES.forEach(category => {
      grouped[category.id] = filteredSections.filter(section =>
        categorizeSection(section.sectionType) === category.id
      );
    });

    return grouped;
  }, [filteredSections]);

  const handleSectionToggle = (sectionType: string, checked: boolean) => {
    const newSelection = checked
      ? [...selectedSections, sectionType]
      : selectedSections.filter(s => s !== sectionType);
    
    onSelectionChange(newSelection);
  };

  const handleSelectAll = () => {
    const allSectionTypes = filteredSections.map(s => s.sectionType);
    onSelectionChange([...new Set([...selectedSections, ...allSectionTypes])]);
  };

  const handleDeselectAll = () => {
    const filteredSectionTypes = filteredSections.map(s => s.sectionType);
    onSelectionChange(selectedSections.filter(s => !filteredSectionTypes.includes(s)));
  };

  const getCategoryColor = (categoryId: string) => {
    const category = SECTION_CATEGORIES.find(c => c.id === categoryId);
    return category?.color || 'bg-gray-500';
  };

  if (loading) {
    return (
      <div className={cn("space-y-4", className)}>
        <div className="flex items-center justify-center py-12">
          <div className="text-center space-y-3">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
            <p className="text-sm text-muted-foreground">Loading content sections...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={cn("space-y-6", className)}>
      {/* Header with Search and Controls */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold">Content Sections</h3>
            <p className="text-sm text-muted-foreground">
              Choose which sections to include in your proposal
            </p>
          </div>
          <Badge variant="secondary" className="text-sm">
            {selectedSections.length} selected
          </Badge>
        </div>

        {/* Search and Filter Controls */}
        <div className="flex items-center space-x-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search sections..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
          
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setViewMode(viewMode === 'grid' ? 'list' : 'grid')}
            >
              {viewMode === 'grid' ? <List className="h-4 w-4" /> : <Grid3X3 className="h-4 w-4" />}
            </Button>
            
            <Button variant="outline" size="sm" onClick={handleSelectAll}>
              Select All
            </Button>
            
            <Button variant="outline" size="sm" onClick={handleDeselectAll}>
              Clear
            </Button>
          </div>
        </div>

        {/* Category Tabs */}
        <Tabs value={selectedCategory} onValueChange={setSelectedCategory}>
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="all">All</TabsTrigger>
            {SECTION_CATEGORIES.map((category) => {
              const IconComponent = category.icon;
              return (
                <TabsTrigger key={category.id} value={category.id} className="flex items-center space-x-2">
                  <IconComponent className="h-4 w-4" />
                  <span className="hidden sm:inline">{category.name}</span>
                </TabsTrigger>
              );
            })}
          </TabsList>
        </Tabs>
      </div>

      {/* Content Sections Display */}
      <ScrollArea className="h-[400px]">
        {selectedCategory === 'all' ? (
          // Show all categories
          <div className="space-y-6">
            {SECTION_CATEGORIES.map((category) => {
              const sections = sectionsByCategory[category.id];
              if (sections.length === 0) return null;

              return (
                <div key={category.id} className="space-y-3">
                  <div className="flex items-center space-x-2">
                    <div className={cn("w-2 h-2 rounded-full", category.color)} />
                    <h4 className="font-medium text-sm">{category.name}</h4>
                    <Badge variant="outline" className="text-xs">
                      {sections.filter(s => selectedSections.includes(s.sectionType)).length}/{sections.length}
                    </Badge>
                  </div>
                  
                  <div className={cn(
                    "grid gap-3",
                    viewMode === 'grid' ? "grid-cols-1 md:grid-cols-2" : "grid-cols-1"
                  )}>
                    {sections.map((section) => (
                      <SectionCard
                        key={section.id}
                        section={section}
                        isSelected={selectedSections.includes(section.sectionType)}
                        onToggle={handleSectionToggle}
                        onPreview={setPreviewSection}
                        onCustomize={onSectionCustomize ? setCustomizeSection : undefined}
                        hasCustomPrompt={!!customPrompts[section.sectionType]}
                        viewMode={viewMode}
                        categoryColor={category.color}
                      />
                    ))}
                  </div>
                </div>
              );
            })}
          </div>
        ) : (
          // Show single category
          <div className={cn(
            "grid gap-3",
            viewMode === 'grid' ? "grid-cols-1 md:grid-cols-2 lg:grid-cols-3" : "grid-cols-1"
          )}>
            {filteredSections.map((section) => (
              <SectionCard
                key={section.id}
                section={section}
                isSelected={selectedSections.includes(section.sectionType)}
                onToggle={handleSectionToggle}
                onPreview={setPreviewSection}
                onCustomize={onSectionCustomize ? setCustomizeSection : undefined}
                hasCustomPrompt={!!customPrompts[section.sectionType]}
                viewMode={viewMode}
                categoryColor={getCategoryColor(categorizeSection(section.sectionType))}
              />
            ))}
          </div>
        )}

        {filteredSections.length === 0 && (
          <div className="text-center py-12">
            <FileText className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
            <h4 className="font-medium text-muted-foreground mb-2">No sections found</h4>
            <p className="text-sm text-muted-foreground">
              Try adjusting your search or category filter
            </p>
          </div>
        )}
      </ScrollArea>

      {/* Section Preview Dialog */}
      {previewSection && (
        <SectionPreviewDialog
          section={previewSection}
          open={!!previewSection}
          onOpenChange={(open) => !open && setPreviewSection(null)}
        />
      )}

      {/* Section Customization Dialog */}
      {customizeSection && onSectionCustomize && (
        <SectionCustomizationDialog
          section={customizeSection}
          currentCustomPrompt={customPrompts[customizeSection.sectionType] || ''}
          open={!!customizeSection}
          onOpenChange={(open) => !open && setCustomizeSection(null)}
          onSave={(customPrompt) => {
            onSectionCustomize(customizeSection.sectionType, customPrompt);
            setCustomizeSection(null);
          }}
        />
      )}
    </div>
  );
}

// Section Card Component
interface SectionCardProps {
  section: ProposalContentSection;
  isSelected: boolean;
  onToggle: (sectionType: string, checked: boolean) => void;
  onPreview: (section: ProposalContentSection) => void;
  onCustomize?: (section: ProposalContentSection) => void;
  hasCustomPrompt?: boolean;
  viewMode: 'grid' | 'list';
  categoryColor: string;
}

function SectionCard({
  section,
  isSelected,
  onToggle,
  onPreview,
  onCustomize,
  hasCustomPrompt = false,
  viewMode,
  categoryColor
}: SectionCardProps) {
  return (
    <Card
      className={cn(
        "transition-all duration-200 hover:shadow-sm cursor-pointer border",
        isSelected
          ? 'ring-2 ring-primary bg-primary/5 border-primary/20'
          : 'hover:bg-muted/50',
        viewMode === 'list' ? 'p-3' : 'p-4'
      )}
      onClick={() => onToggle(section.sectionType, !isSelected)}
    >
      <CardContent className={cn("p-0", viewMode === 'list' ? 'flex items-center space-x-4' : 'space-y-3')}>
        <div className={cn("flex items-center space-x-3", viewMode === 'list' ? 'flex-1' : '')}>
          <Checkbox
            checked={isSelected}
            onCheckedChange={(checked) => onToggle(section.sectionType, checked as boolean)}
            onClick={(e) => e.stopPropagation()}
          />

          <div className={cn("w-3 h-3 rounded-full flex-shrink-0", categoryColor)} />

          <div className="flex-1 min-w-0">
            <h4 className={cn("font-medium truncate", viewMode === 'list' ? 'text-sm' : 'text-base')}>
              {section.title}
            </h4>
            {section.description && (
              <p className={cn("text-muted-foreground truncate", viewMode === 'list' ? 'text-xs' : 'text-sm')}>
                {section.description}
              </p>
            )}
          </div>
        </div>

        <div className={cn("flex items-center space-x-2", viewMode === 'list' ? 'flex-shrink-0' : 'justify-between')}>
          <div className="flex items-center space-x-2">
            {section.isDefault && (
              <Badge variant="secondary" className="text-xs">
                Default
              </Badge>
            )}

            {hasCustomPrompt && (
              <Badge variant="outline" className="text-xs bg-orange-50 text-orange-700 border-orange-200">
                Custom
              </Badge>
            )}

            <Badge variant="outline" className="text-xs">
              {section.sectionType.replace(/_/g, ' ')}
            </Badge>
          </div>

          <div className="flex items-center space-x-1">
            {onCustomize && (
              <Button
                variant="ghost"
                size="sm"
                onClick={(e) => {
                  e.stopPropagation();
                  onCustomize(section);
                }}
                className="h-8 w-8 p-0"
                title="Customize prompts"
              >
                <Wand2 className="h-4 w-4" />
              </Button>
            )}

            <Button
              variant="ghost"
              size="sm"
              onClick={(e) => {
                e.stopPropagation();
                onPreview(section);
              }}
              className="h-8 w-8 p-0"
              title="Preview prompts"
            >
              <Eye className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

// Section Preview Dialog Component
interface SectionPreviewDialogProps {
  section: ProposalContentSection;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

function SectionPreviewDialog({
  section,
  open,
  onOpenChange
}: SectionPreviewDialogProps) {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-hidden flex flex-col">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <FileText className="h-5 w-5" />
            <span>{section.title}</span>
          </DialogTitle>
          <DialogDescription>
            Preview the prompts and configuration for this content section
          </DialogDescription>
        </DialogHeader>

        <ScrollArea className="flex-1">
          <div className="space-y-6 p-1">
            {/* Section Info */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">Section Information</Label>
              <div className="grid grid-cols-2 gap-4 p-4 bg-muted/50 rounded-lg">
                <div>
                  <Label className="text-xs text-muted-foreground">Type</Label>
                  <p className="text-sm font-mono">{section.sectionType}</p>
                </div>
                <div>
                  <Label className="text-xs text-muted-foreground">Order</Label>
                  <p className="text-sm">{section.orderIndex}</p>
                </div>
                {section.description && (
                  <div className="col-span-2">
                    <Label className="text-xs text-muted-foreground">Description</Label>
                    <p className="text-sm">{section.description}</p>
                  </div>
                )}
              </div>
            </div>

            {/* English Prompts */}
            <div className="space-y-4">
              <Label className="text-sm font-medium">English Prompts</Label>

              <div className="space-y-3">
                <div>
                  <Label className="text-xs text-muted-foreground">User Prompt</Label>
                  <div className="mt-1 p-3 bg-muted/50 rounded-md">
                    <pre className="text-sm whitespace-pre-wrap font-mono">
                      {section.promptEn}
                    </pre>
                  </div>
                </div>

                <div>
                  <Label className="text-xs text-muted-foreground">System Prompt</Label>
                  <div className="mt-1 p-3 bg-muted/50 rounded-md">
                    <pre className="text-sm whitespace-pre-wrap font-mono">
                      {section.systemPromptEn}
                    </pre>
                  </div>
                </div>
              </div>
            </div>

            {/* Arabic Prompts (if available) */}
            {(section.promptAr || section.systemPromptAr) && (
              <div className="space-y-4">
                <Label className="text-sm font-medium">Arabic Prompts</Label>

                <div className="space-y-3">
                  {section.promptAr && (
                    <div>
                      <Label className="text-xs text-muted-foreground">User Prompt (Arabic)</Label>
                      <div className="mt-1 p-3 bg-muted/50 rounded-md" dir="rtl">
                        <pre className="text-sm whitespace-pre-wrap font-mono">
                          {section.promptAr}
                        </pre>
                      </div>
                    </div>
                  )}

                  {section.systemPromptAr && (
                    <div>
                      <Label className="text-xs text-muted-foreground">System Prompt (Arabic)</Label>
                      <div className="mt-1 p-3 bg-muted/50 rounded-md" dir="rtl">
                        <pre className="text-sm whitespace-pre-wrap font-mono">
                          {section.systemPromptAr}
                        </pre>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        </ScrollArea>
      </DialogContent>
    </Dialog>
  );
}

// Section Customization Dialog Component
interface SectionCustomizationDialogProps {
  section: ProposalContentSection;
  currentCustomPrompt: string;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSave: (customPrompt: string) => void;
}

function SectionCustomizationDialog({
  section,
  currentCustomPrompt,
  open,
  onOpenChange,
  onSave
}: SectionCustomizationDialogProps) {
  const [customPrompt, setCustomPrompt] = useState(currentCustomPrompt);

  useEffect(() => {
    setCustomPrompt(currentCustomPrompt);
  }, [currentCustomPrompt, open]);

  const handleSave = () => {
    onSave(customPrompt.trim());
  };

  const handleReset = () => {
    setCustomPrompt('');
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-hidden flex flex-col">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <Wand2 className="h-5 w-5" />
            <span>Customize: {section.title}</span>
          </DialogTitle>
          <DialogDescription>
            Add custom instructions to modify how this section is generated
          </DialogDescription>
        </DialogHeader>

        <div className="flex-1 overflow-hidden flex flex-col space-y-6">
          {/* Original Prompts Preview */}
          <div className="flex-shrink-0">
            <Label className="text-sm font-medium">Original Prompts</Label>
            <div className="mt-2 p-3 bg-muted/50 rounded-md max-h-32 overflow-y-auto">
              <div className="text-sm text-muted-foreground">
                <strong>User:</strong> {section.promptEn.substring(0, 200)}...
              </div>
            </div>
          </div>

          {/* Custom Instructions */}
          <div className="flex-1 flex flex-col space-y-2">
            <Label htmlFor="customPrompt" className="text-sm font-medium">
              Custom Instructions
            </Label>
            <p className="text-xs text-muted-foreground">
              Add specific requirements or modifications for this section. These will be appended to the original prompts.
            </p>
            <Textarea
              id="customPrompt"
              value={customPrompt}
              onChange={(e) => setCustomPrompt(e.target.value)}
              placeholder="For example: 'Include specific technology stack requirements', 'Focus on cost optimization', 'Add compliance considerations', etc."
              className="flex-1 min-h-[200px] resize-none"
            />
          </div>

          {/* Preview of Combined Prompt */}
          {customPrompt.trim() && (
            <div className="flex-shrink-0">
              <Label className="text-sm font-medium">Combined Prompt Preview</Label>
              <div className="mt-2 p-3 bg-blue-50 border border-blue-200 rounded-md max-h-32 overflow-y-auto">
                <div className="text-sm">
                  <div className="text-gray-700 mb-2">{section.promptEn}</div>
                  <div className="text-blue-700 font-medium">
                    Additional Instructions: {customPrompt}
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        <DialogFooter className="flex items-center justify-between">
          <Button
            variant="outline"
            onClick={handleReset}
            disabled={!customPrompt.trim()}
          >
            Reset
          </Button>

          <div className="flex space-x-2">
            <Button
              variant="outline"
              onClick={() => onOpenChange(false)}
            >
              Cancel
            </Button>
            <Button
              onClick={handleSave}
            >
              Save Custom Instructions
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
