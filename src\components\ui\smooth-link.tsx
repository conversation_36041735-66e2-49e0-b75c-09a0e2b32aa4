"use client"

import React, { memo } from 'react'
import Link from 'next/link'
import { cn } from '@/lib/utils'

interface SmoothLinkProps {
  href: string
  children: React.ReactNode
  className?: string
  replace?: boolean
  onClick?: (e: React.MouseEvent) => void
  prefetch?: boolean
  [key: string]: any
}

export const SmoothLink = memo(function SmoothLink({
  href,
  children,
  className,
  replace = false,
  onClick,
  prefetch = true,
  ...props
}: SmoothLinkProps) {
  const handleClick = (e: React.MouseEvent<HTMLAnchorElement>) => {
    // Call custom onClick if provided
    if (onClick) {
      onClick(e)
    }

    // Don't prevent default for external links or special keys
    if (
      href.startsWith('http') ||
      href.startsWith('mailto:') ||
      href.startsWith('tel:') ||
      e.metaKey ||
      e.ctrlKey ||
      e.shiftKey ||
      e.alt<PERSON><PERSON>
    ) {
      return
    }

    // Let Next.js handle navigation naturally - no interference
  }

  return (
    <Link
      href={href}
      className={cn(className)}
      onClick={handleClick}
      prefetch={prefetch}
      replace={replace}
      {...props}
    >
      {children}
    </Link>
  )
})
