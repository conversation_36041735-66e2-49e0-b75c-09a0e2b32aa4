'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { SearchableSelect } from '@/components/ui/searchable-select';
import { Card, CardContent } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  FileText,
  Search,
  Loader2,
  AlertCircle,
  Check,
  X,
  Filter,
  Download,
} from 'lucide-react';
import { DocumentWithRelations } from '@/services/document-management';
import { useToast } from '@/hooks/use-toast';
import { cn } from '@/lib/utils';
import { format } from 'date-fns';
import { downloadFile } from '@/lib/download-utils';

const linkFormSchema = z.object({
  documentIds: z.array(z.string()).min(1, 'Please select at least one document'),
  category: z.enum(['requirements', 'contracts', 'technical', 'legal', 'financial']).default('requirements'),
  isRequired: z.boolean().default(false),
  isClientVisible: z.boolean().default(false),
  accessLevel: z.enum(['internal', 'delivery_team', 'client']).default('internal'),
  notes: z.string().optional(),
});

type LinkFormData = z.infer<typeof linkFormSchema>;

interface DocumentLinkDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onLinkComplete?: (linkedDocuments: any[]) => void;
  onLinkError?: (error: string) => void;
  relatedToType: string;
  relatedToId: string;
  opportunityTitle?: string;
  companyName?: string;
}

interface HandoverDocumentData {
  handover: {
    id: string;
    title: string;
    opportunityId: string;
    opportunityTitle: string;
    companyName?: string;
  };
  documents: {
    opportunity: DocumentWithRelations[];
    proposals: (DocumentWithRelations & { proposalTitle: string; proposalId: string })[];
    total: number;
  };
  metadata: {
    totalAvailable: number;
    alreadyLinked: number;
    proposalCount: number;
    searchTerm?: string;
    mimeTypeFilter?: string;
  };
}

export function DocumentLinkDialog({
  open,
  onOpenChange,
  onLinkComplete,
  onLinkError,
  relatedToType,
  relatedToId,
  opportunityTitle,
  companyName,
}: DocumentLinkDialogProps) {
  const { toast } = useToast();
  const [searchTerm, setSearchTerm] = useState('');
  const [documents, setDocuments] = useState<DocumentWithRelations[]>([]);
  const [handoverData, setHandoverData] = useState<HandoverDocumentData | null>(null);
  const [selectedDocuments, setSelectedDocuments] = useState<Set<string>>(new Set());
  const [loading, setLoading] = useState(false);
  const [searching, setSearching] = useState(false);
  const [linking, setLinking] = useState(false);
  const [mimeTypeFilter, setMimeTypeFilter] = useState<string>('all');
  const [hasSearched, setHasSearched] = useState(false);
  const [showAllDocuments, setShowAllDocuments] = useState(false);

  const form = useForm<LinkFormData>({
    resolver: zodResolver(linkFormSchema),
    defaultValues: {
      documentIds: [],
      category: 'requirements',
      isRequired: false,
      isClientVisible: false,
      accessLevel: 'internal',
      notes: '',
    },
  });

  // Load handover-specific documents (opportunity and proposals)
  const loadHandoverDocuments = useCallback(async () => {
    if (relatedToType !== 'handover') return;

    try {
      setLoading(true);
      const params = new URLSearchParams({
        ...(mimeTypeFilter && mimeTypeFilter !== 'all' && { mimeType: mimeTypeFilter }),
      });

      const response = await fetch(`/api/handovers/${relatedToId}/available-documents?${params}`);
      const data = await response.json();

      if (response.ok) {
        setHandoverData(data);
        // Combine opportunity and proposal documents
        const allDocs = [...data.documents.opportunity, ...data.documents.proposals];
        setDocuments(allDocs);
        setHasSearched(true);
      } else {
        throw new Error(data.error || 'Failed to load handover documents');
      }
    } catch (error) {
      console.error('Error loading handover documents:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to load handover documents';
      toast({ description: errorMessage, variant: 'destructive' });
      setDocuments([]);
    } finally {
      setLoading(false);
    }
  }, [relatedToType, relatedToId, mimeTypeFilter, toast]);

  // Search documents (for general search or non-handover entities)
  const searchDocuments = useCallback(async (query: string) => {
    if (!query.trim() && !showAllDocuments) {
      if (relatedToType === 'handover') {
        // For handovers, load opportunity/proposal documents by default
        loadHandoverDocuments();
      } else {
        setDocuments([]);
        setHasSearched(false);
      }
      return;
    }

    try {
      setSearching(true);

      if (relatedToType === 'handover' && !showAllDocuments) {
        // Search within handover-specific documents
        const params = new URLSearchParams({
          ...(query && { search: query }),
          ...(mimeTypeFilter && mimeTypeFilter !== 'all' && { mimeType: mimeTypeFilter }),
        });

        const response = await fetch(`/api/handovers/${relatedToId}/available-documents?${params}`);
        const data = await response.json();

        if (response.ok) {
          setHandoverData(data);
          const allDocs = [...data.documents.opportunity, ...data.documents.proposals];
          setDocuments(allDocs);
          setHasSearched(true);
        } else {
          throw new Error(data.error || 'Failed to search handover documents');
        }
      } else {
        // General document search
        const params = new URLSearchParams({
          limit: '50',
          excludeRelatedToType: relatedToType,
          excludeRelatedToId: relatedToId,
          includeProposalDocs: relatedToType === 'opportunity' ? 'true' : 'false',
          ...(mimeTypeFilter && mimeTypeFilter !== 'all' && { mimeType: mimeTypeFilter }),
        });

        // Only add query parameter if it's not empty
        if (query && query.trim()) {
          params.set('q', query.trim());
        }

        const response = await fetch(`/api/documents/search?${params}`);
        const data = await response.json();

        if (response.ok && data.success) {
          setDocuments(data.data.documents || []);
          setHasSearched(true);
        } else {
          throw new Error(data.error || 'Failed to search documents');
        }
      }
    } catch (error) {
      console.error('Error searching documents:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to search documents';
      toast({ description: errorMessage, variant: 'destructive' });
      setDocuments([]);
    } finally {
      setSearching(false);
    }
  }, [relatedToType, relatedToId, mimeTypeFilter, toast, showAllDocuments, loadHandoverDocuments]);

  // Load initial documents when dialog opens
  useEffect(() => {
    if (open && relatedToType === 'handover') {
      loadHandoverDocuments();
    }
  }, [open, loadHandoverDocuments]);

  // Debounced search
  useEffect(() => {
    const timer = setTimeout(() => {
      searchDocuments(searchTerm);
    }, 300);

    return () => clearTimeout(timer);
  }, [searchTerm, searchDocuments]);

  // Handle filter changes
  useEffect(() => {
    if (hasSearched) {
      searchDocuments(searchTerm);
    }
  }, [mimeTypeFilter, showAllDocuments]);

  // Handle document selection
  const toggleDocumentSelection = (documentId: string) => {
    const newSelection = new Set(selectedDocuments);
    if (newSelection.has(documentId)) {
      newSelection.delete(documentId);
    } else {
      newSelection.add(documentId);
    }
    setSelectedDocuments(newSelection);
    form.setValue('documentIds', Array.from(newSelection));
  };

  // Handle form submission
  const onSubmit = async (data: LinkFormData) => {
    try {
      setLinking(true);
      const linkedDocuments = [];

      // Link each selected document
      for (const documentId of data.documentIds) {
        const linkData = {
          documentId,
          category: data.category,
          isRequired: data.isRequired,
          isClientVisible: data.isClientVisible,
          accessLevel: data.accessLevel,
          notes: data.notes,
        };

        const response = await fetch(`/api/handovers/${relatedToId}/documents`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(linkData),
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to link document');
        }

        const linkedDocument = await response.json();
        linkedDocuments.push(linkedDocument);
      }

      toast({
        description: `Successfully linked ${linkedDocuments.length} document(s)`,
        variant: 'default',
      });

      onLinkComplete?.(linkedDocuments);
      onOpenChange(false);
      
      // Reset form and state
      form.reset();
      setSelectedDocuments(new Set());
      setSearchTerm('');
      setDocuments([]);
      setHasSearched(false);

    } catch (error) {
      console.error('Error linking documents:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to link documents';
      toast({ description: errorMessage, variant: 'destructive' });
      onLinkError?.(errorMessage);
    } finally {
      setLinking(false);
    }
  };

  // Format file size
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // Safe date formatting helper
  const formatSafeDate = (dateValue: string | Date | null | undefined): string => {
    if (!dateValue) return 'Unknown date';
    try {
      const date = new Date(dateValue);
      if (isNaN(date.getTime())) return 'Invalid date';
      return format(date, 'MMM d, yyyy');
    } catch (error) {
      console.warn('Date formatting error:', error);
      return 'Invalid date';
    }
  };

  // Get file icon based on mime type
  const getFileIcon = (mimeType: string) => {
    return <FileText className="w-5 h-5 text-blue-600" />;
  };

  // Filter options
  const getMimeTypeOptions = () => [
    { value: 'all', label: 'All file types' },
    { value: 'application/pdf', label: 'PDF files' },
    { value: 'application/msword', label: 'Word documents' },
    { value: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', label: 'Word documents (new)' },
    { value: 'application/vnd.ms-excel', label: 'Excel files' },
    { value: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', label: 'Excel files (new)' },
    { value: 'text/plain', label: 'Text files' },
    { value: 'image/', label: 'Images' },
  ];

  const getCategoryOptions = () => [
    { value: 'requirements', label: 'Requirements' },
    { value: 'contracts', label: 'Contracts' },
    { value: 'technical', label: 'Technical' },
    { value: 'legal', label: 'Legal' },
    { value: 'financial', label: 'Financial' },
  ];

  const getAccessLevelOptions = () => [
    { value: 'internal', label: 'Internal Only' },
    { value: 'delivery_team', label: 'Delivery Team' },
    { value: 'client', label: 'Client Access' },
  ];

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden flex flex-col">
        <DialogHeader>
          <DialogTitle>Link Existing Documents</DialogTitle>
          <DialogDescription>
            {relatedToType === 'handover' ? (
              <>
                Select documents from the related opportunity and proposals to link to this handover.
                {handoverData && (
                  <span className="block mt-1 text-sm font-medium text-blue-600">
                    Opportunity: {handoverData.handover.opportunityTitle}
                    {handoverData.handover.companyName && ` (${handoverData.handover.companyName})`}
                  </span>
                )}
              </>
            ) : (
              <>
                Search and select existing documents to link to this handover.
                {opportunityTitle && (
                  <span className="block mt-1 text-sm font-medium text-blue-600">
                    Related to: {opportunityTitle} {companyName && `(${companyName})`}
                  </span>
                )}
              </>
            )}
          </DialogDescription>
        </DialogHeader>

        <div className="flex-1 overflow-hidden flex flex-col space-y-4">
          {/* Search and Filters */}
          <div className="space-y-4">
            <div className="flex gap-4">
              <div className="flex-1 relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder={
                    relatedToType === 'handover' && !showAllDocuments
                      ? "Search opportunity and proposal documents..."
                      : "Search documents by name or description..."
                  }
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
                {searching && (
                  <Loader2 className="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 animate-spin" />
                )}
              </div>
              <div className="w-48">
                <SearchableSelect
                  options={getMimeTypeOptions()}
                  value={mimeTypeFilter}
                  onValueChange={setMimeTypeFilter}
                  placeholder="All file types"
                  searchPlaceholder="Search file types..."
                  emptyMessage="No file types found"
                />
              </div>
            </div>

            {/* Toggle for handover documents */}
            {relatedToType === 'handover' && (
              <div className="flex items-center gap-4">
                <Button
                  type="button"
                  variant={!showAllDocuments ? "default" : "outline"}
                  size="sm"
                  onClick={() => setShowAllDocuments(false)}
                >
                  Opportunity Documents
                </Button>
                <Button
                  type="button"
                  variant={showAllDocuments ? "default" : "outline"}
                  size="sm"
                  onClick={() => setShowAllDocuments(true)}
                >
                  All Documents
                </Button>
                {handoverData && !showAllDocuments && (
                  <span className="text-sm text-gray-600">
                    {handoverData.documents.total} available from opportunity & proposals
                  </span>
                )}
              </div>
            )}
          </div>

          {/* Document List */}
          <div className="flex-1 overflow-y-auto border rounded-lg">
            {!hasSearched && !searching && !loading && (
              <div className="flex items-center justify-center h-32 text-gray-500">
                <div className="text-center">
                  <Search className="w-8 h-8 mx-auto mb-2 text-gray-400" />
                  <p>
                    {relatedToType === 'handover'
                      ? 'Loading opportunity documents...'
                      : 'Enter a search term to find documents'
                    }
                  </p>
                </div>
              </div>
            )}

            {(searching || loading) && (
              <div className="flex items-center justify-center h-32">
                <div className="text-center">
                  <Loader2 className="w-8 h-8 mx-auto mb-2 animate-spin text-blue-600" />
                  <p className="text-gray-600">
                    {loading ? 'Loading documents...' : 'Searching documents...'}
                  </p>
                </div>
              </div>
            )}

            {hasSearched && !searching && !loading && documents.length === 0 && (
              <div className="flex items-center justify-center h-32 text-gray-500">
                <div className="text-center">
                  <FileText className="w-8 h-8 mx-auto mb-2 text-gray-400" />
                  <p>No documents found</p>
                  <p className="text-sm mt-1">
                    {relatedToType === 'handover' && !showAllDocuments
                      ? 'No documents available from the related opportunity or proposals'
                      : 'Try different keywords or adjust filters'
                    }
                  </p>
                </div>
              </div>
            )}

            {documents.length > 0 && (
              <div className="p-4 space-y-4">
                {/* Show categorized documents for handovers */}
                {relatedToType === 'handover' && handoverData && !showAllDocuments && (
                  <>
                    {/* Opportunity Documents */}
                    {handoverData.documents.opportunity.length > 0 && (
                      <div className="space-y-3">
                        <div className="flex items-center gap-2">
                          <h3 className="font-medium text-sm text-gray-900">Opportunity Documents</h3>
                          <Badge variant="outline" className="text-xs">
                            {handoverData.documents.opportunity.length}
                          </Badge>
                        </div>
                        {handoverData.documents.opportunity.map((document) => (
                          <DocumentCard
                            key={document.id}
                            document={document}
                            isSelected={selectedDocuments.has(document.id)}
                            onToggle={() => toggleDocumentSelection(document.id)}
                            source="opportunity"
                          />
                        ))}
                      </div>
                    )}

                    {/* Proposal Documents */}
                    {handoverData.documents.proposals.length > 0 && (
                      <div className="space-y-3">
                        <div className="flex items-center gap-2">
                          <h3 className="font-medium text-sm text-gray-900">Proposal Documents</h3>
                          <Badge variant="outline" className="text-xs">
                            {handoverData.documents.proposals.length}
                          </Badge>
                        </div>
                        {handoverData.documents.proposals.map((document) => (
                          <DocumentCard
                            key={document.id}
                            document={document}
                            isSelected={selectedDocuments.has(document.id)}
                            onToggle={() => toggleDocumentSelection(document.id)}
                            source="proposal"
                            proposalTitle={(document as any).proposalTitle}
                            proposalId={(document as any).proposalId}
                          />
                        ))}
                      </div>
                    )}
                  </>
                )}

                {/* Regular document list for other cases */}
                {(relatedToType !== 'handover' || showAllDocuments) && (
                  <div className="space-y-3">
                    {documents.map((document) => (
                      <DocumentCard
                        key={document.id}
                        document={document}
                        isSelected={selectedDocuments.has(document.id)}
                        onToggle={() => toggleDocumentSelection(document.id)}
                      />
                    ))}
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Selected Documents Summary */}
          {selectedDocuments.size > 0 && (
            <Alert>
              <Check className="h-4 w-4" />
              <AlertDescription>
                {selectedDocuments.size} document(s) selected for linking
              </AlertDescription>
            </Alert>
          )}
        </div>

        {/* Link Configuration Form */}
        {selectedDocuments.size > 0 && (
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4 border-t pt-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <FormField
                  control={form.control}
                  name="category"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Category</FormLabel>
                      <FormControl>
                        <SearchableSelect
                          options={getCategoryOptions()}
                          value={field.value}
                          onValueChange={field.onChange}
                          placeholder="Select category"
                          searchPlaceholder="Search categories..."
                          emptyMessage="No categories found"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="accessLevel"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Access Level</FormLabel>
                      <FormControl>
                        <SearchableSelect
                          options={getAccessLevelOptions()}
                          value={field.value}
                          onValueChange={field.onChange}
                          placeholder="Select access level"
                          searchPlaceholder="Search access levels..."
                          emptyMessage="No access levels found"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="space-y-3">
                  <FormField
                    control={form.control}
                    name="isRequired"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center space-x-3 space-y-0">
                        <FormControl>
                          <Checkbox
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                        <FormLabel className="text-sm font-normal">
                          Required document
                        </FormLabel>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="isClientVisible"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center space-x-3 space-y-0">
                        <FormControl>
                          <Checkbox
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                        <FormLabel className="text-sm font-normal">
                          Visible to client
                        </FormLabel>
                      </FormItem>
                    )}
                  />
                </div>
              </div>

              <FormField
                control={form.control}
                name="notes"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Notes (Optional)</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Add any notes about these documents..."
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <DialogFooter>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => onOpenChange(false)}
                  disabled={linking}
                >
                  Cancel
                </Button>
                <Button type="submit" disabled={linking || selectedDocuments.size === 0}>
                  {linking && <Loader2 className="w-4 h-4 mr-2 animate-spin" />}
                  Link {selectedDocuments.size} Document{selectedDocuments.size !== 1 ? 's' : ''}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        )}

        {selectedDocuments.size === 0 && (
          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
            >
              Cancel
            </Button>
          </DialogFooter>
        )}
      </DialogContent>
    </Dialog>
  );
}

// Document Card Component
interface DocumentCardProps {
  document: DocumentWithRelations;
  isSelected: boolean;
  onToggle: () => void;
  source?: 'opportunity' | 'proposal';
  proposalTitle?: string;
  proposalId?: string;
}

function DocumentCard({ document, isSelected, onToggle, source, proposalTitle, proposalId }: DocumentCardProps) {
  const { toast } = useToast();
  const [isDownloading, setIsDownloading] = useState(false);
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // Safe date formatting helper
  const formatSafeDate = (dateValue: string | Date | null | undefined): string => {
    if (!dateValue) return 'Unknown date';
    try {
      const date = new Date(dateValue);
      if (isNaN(date.getTime())) return 'Invalid date';
      return format(date, 'MMM d, yyyy');
    } catch (error) {
      console.warn('Date formatting error:', error);
      return 'Invalid date';
    }
  };

  const getFileIcon = (mimeType: string) => {
    return <FileText className="w-5 h-5 text-blue-600" />;
  };

  // Handle download for proposal documents
  const handleDownloadProposal = async (e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent card selection

    // Get proposal ID from either the prop or the document's relatedToId
    const downloadProposalId = proposalId || (document.relatedToType === 'proposal' ? document.relatedToId : null);
    if (!downloadProposalId) {
      toast({
        description: 'Unable to download proposal: Proposal ID not found',
        variant: 'destructive'
      });
      return;
    }

    setIsDownloading(true);
    try {
      // For proposal documents, use the proposal download API
      const response = await fetch(`/api/proposals/${downloadProposalId}/download`);

      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `${proposalTitle || 'proposal'}.docx`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
        toast({
          description: 'Proposal downloaded successfully',
          variant: 'default'
        });
      } else {
        const errorData = await response.json().catch(() => ({ error: 'Failed to download proposal' }));
        throw new Error(errorData.error || 'Failed to download proposal');
      }
    } catch (error) {
      console.error('Error downloading proposal:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to download proposal';
      toast({
        description: errorMessage,
        variant: 'destructive'
      });
    } finally {
      setIsDownloading(false);
    }
  };

  // Handle download for regular documents
  const handleDownloadDocument = async (e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent card selection

    setIsDownloading(true);
    try {
      await downloadFile(`/api/documents/${document.id}/download`, document.name);
      toast({
        description: 'Document downloaded successfully',
        variant: 'default'
      });
    } catch (error) {
      console.error('Error downloading document:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to download document';
      toast({
        description: errorMessage,
        variant: 'destructive'
      });
    } finally {
      setIsDownloading(false);
    }
  };

  const getSourceBadge = () => {
    if (source === 'proposal') {
      return (
        <Badge variant="outline" className="text-xs bg-purple-50 text-purple-700 border-purple-200">
          Proposal: {proposalTitle}
        </Badge>
      );
    }
    if (source === 'opportunity') {
      return (
        <Badge variant="outline" className="text-xs bg-blue-50 text-blue-700 border-blue-200">
          Opportunity
        </Badge>
      );
    }
    return null;
  };

  return (
    <Card
      className={cn(
        'cursor-pointer transition-all hover:shadow-md',
        isSelected && 'ring-2 ring-blue-500 bg-blue-50'
      )}
      onClick={onToggle}
    >
      <CardContent className="p-4">
        <div className="flex items-start space-x-3">
          <Checkbox
            checked={isSelected}
            onChange={onToggle}
            className="mt-1"
          />
          <div className="flex-shrink-0">
            {getFileIcon(document.mimeType || '')}
          </div>
          <div className="flex-1 min-w-0">
            <div className="flex items-start justify-between">
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2 mb-1">
                  <h4 className="font-medium text-sm text-gray-900 truncate">
                    {document.name}
                  </h4>
                  {getSourceBadge()}
                </div>
                {document.description && (
                  <p className="text-xs text-gray-600 mt-1 line-clamp-2">
                    {document.description}
                  </p>
                )}
                <div className="flex items-center gap-2 mt-2 text-xs text-gray-500">
                  <span>{document.fileSize ? formatFileSize(document.fileSize) : 'Unknown size'}</span>
                  <span>•</span>
                  <span>{formatSafeDate(document.createdAt)}</span>
                  {document.uploadedBy && (
                    <>
                      <span>•</span>
                      <span>
                        {document.uploadedBy.firstName && document.uploadedBy.lastName
                          ? `${document.uploadedBy.firstName} ${document.uploadedBy.lastName}`
                          : document.uploadedBy.email}
                      </span>
                    </>
                  )}
                </div>
              </div>
              <div className="flex items-start gap-2 ml-2">
                {document.tags && document.tags.length > 0 && (
                  <div className="flex flex-wrap gap-1">
                    {(document.tags as string[]).slice(0, 2).map((tag, index) => (
                      <Badge key={index} variant="outline" className="text-xs">
                        {tag}
                      </Badge>
                    ))}
                    {(document.tags as string[]).length > 2 && (
                      <Badge variant="outline" className="text-xs">
                        +{(document.tags as string[]).length - 2}
                      </Badge>
                    )}
                  </div>
                )}
                {/* Download button */}
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={document.relatedToType === 'proposal' ? handleDownloadProposal : handleDownloadDocument}
                  disabled={isDownloading}
                  className="h-6 w-6 p-0 text-gray-500 hover:text-blue-600 hover:bg-blue-50"
                  title={document.relatedToType === 'proposal' ? 'Download proposal document' : 'Download document'}
                >
                  {isDownloading ? (
                    <Loader2 className="h-3 w-3 animate-spin" />
                  ) : (
                    <Download className="h-3 w-3" />
                  )}
                </Button>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
