'use client';

import { useState } from 'react';
import { 
  ChartBarIcon,
  EyeIcon,
  PlusIcon
} from '@heroicons/react/24/outline';
import { cn, formatCurrency } from '@/lib/utils';

interface PipelineStage {
  id: string;
  name: string;
  count: number;
  value: number;
  color: string;
  deals: Deal[];
}

interface Deal {
  id: string;
  title: string;
  company: string;
  value: number;
  probability: number;
  expectedCloseDate: Date;
  contact: string;
}

// Mock data
const mockPipelineStages: PipelineStage[] = [
  {
    id: '1',
    name: 'Prospecting',
    count: 12,
    value: 145000,
    color: 'bg-gray-500',
    deals: [
      {
        id: '1',
        title: 'Website Redesign',
        company: 'ABC Corp',
        value: 25000,
        probability: 20,
        expectedCloseDate: new Date('2024-02-15'),
        contact: '<PERSON>',
      },
      {
        id: '2',
        title: 'CRM Implementation',
        company: 'XYZ Inc',
        value: 45000,
        probability: 15,
        expectedCloseDate: new Date('2024-02-20'),
        contact: '<PERSON>',
      },
    ],
  },
  {
    id: '2',
    name: 'Qualification',
    count: 8,
    value: 320000,
    color: 'bg-blue-500',
    deals: [
      {
        id: '3',
        title: 'ERP System',
        company: 'Tech Solutions',
        value: 120000,
        probability: 40,
        expectedCloseDate: new Date('2024-02-10'),
        contact: 'Mike Wilson',
      },
    ],
  },
  {
    id: '3',
    name: 'Proposal',
    count: 5,
    value: 180000,
    color: 'bg-yellow-500',
    deals: [
      {
        id: '4',
        title: 'Mobile App Development',
        company: 'StartupCo',
        value: 80000,
        probability: 60,
        expectedCloseDate: new Date('2024-01-30'),
        contact: 'Lisa Chen',
      },
    ],
  },
  {
    id: '4',
    name: 'Negotiation',
    count: 3,
    value: 95000,
    color: 'bg-orange-500',
    deals: [
      {
        id: '5',
        title: 'Cloud Migration',
        company: 'Enterprise Ltd',
        value: 65000,
        probability: 80,
        expectedCloseDate: new Date('2024-01-25'),
        contact: 'Robert Brown',
      },
    ],
  },
  {
    id: '5',
    name: 'Closed Won',
    count: 7,
    value: 420000,
    color: 'bg-green-500',
    deals: [
      {
        id: '6',
        title: 'Security Audit',
        company: 'FinTech Corp',
        value: 35000,
        probability: 100,
        expectedCloseDate: new Date('2024-01-15'),
        contact: 'Emily Davis',
      },
    ],
  },
];

export function PipelineOverview() {
  const [selectedStage, setSelectedStage] = useState<string | null>(null);

  const totalValue = mockPipelineStages.reduce((sum, stage) => sum + stage.value, 0);
  const totalDeals = mockPipelineStages.reduce((sum, stage) => sum + stage.count, 0);

  return (
    <div className="bg-white dark:bg-gray-800 shadow rounded-lg">
      <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <ChartBarIcon className="h-6 w-6 text-gray-400" />
            <div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                Sales Pipeline
              </h3>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {totalDeals} deals • {formatCurrency(totalValue)} total value
              </p>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <button className="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors">
              <EyeIcon className="h-4 w-4 mr-2" />
              View Details
            </button>
            <button className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors">
              <PlusIcon className="h-4 w-4 mr-2" />
              Add Deal
            </button>
          </div>
        </div>
      </div>

      <div className="p-6">
        {/* Pipeline Stages */}
        <div className="grid grid-cols-1 md:grid-cols-5 gap-4 mb-6">
          {mockPipelineStages.map((stage) => (
            <div
              key={stage.id}
              className={cn(
                'p-4 rounded-lg border-2 cursor-pointer transition-all',
                selectedStage === stage.id
                  ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20'
                  : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
              )}
              onClick={() => setSelectedStage(selectedStage === stage.id ? null : stage.id)}
            >
              <div className="flex items-center justify-between mb-2">
                <div className={cn('w-3 h-3 rounded-full', stage.color)}></div>
                <span className="text-sm font-medium text-gray-500 dark:text-gray-400">
                  {stage.count}
                </span>
              </div>
              <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-1">
                {stage.name}
              </h4>
              <p className="text-lg font-semibold text-gray-900 dark:text-white">
                {formatCurrency(stage.value)}
              </p>
            </div>
          ))}
        </div>

        {/* Pipeline Visualization */}
        <div className="mb-6">
          <div className="flex rounded-lg overflow-hidden h-4">
            {mockPipelineStages.map((stage) => {
              const percentage = (stage.value / totalValue) * 100;
              return (
                <div
                  key={stage.id}
                  className={cn(stage.color, 'transition-all duration-300')}
                  style={{ width: `${percentage}%` }}
                  title={`${stage.name}: ${formatCurrency(stage.value)} (${percentage.toFixed(1)}%)`}
                />
              );
            })}
          </div>
          <div className="flex justify-between mt-2 text-xs text-gray-500 dark:text-gray-400">
            <span>0%</span>
            <span>50%</span>
            <span>100%</span>
          </div>
        </div>

        {/* Selected Stage Details */}
        {selectedStage && (
          <div className="border-t border-gray-200 dark:border-gray-700 pt-6">
            {(() => {
              const stage = mockPipelineStages.find(s => s.id === selectedStage);
              if (!stage) return null;

              return (
                <div>
                  <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                    {stage.name} Deals
                  </h4>
                  <div className="space-y-3">
                    {stage.deals.map((deal) => (
                      <div
                        key={deal.id}
                        className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg"
                      >
                        <div className="flex-1">
                          <div className="flex items-center justify-between">
                            <h5 className="text-sm font-medium text-gray-900 dark:text-white">
                              {deal.title}
                            </h5>
                            <span className="text-sm font-semibold text-gray-900 dark:text-white">
                              {formatCurrency(deal.value)}
                            </span>
                          </div>
                          <div className="flex items-center justify-between mt-1">
                            <p className="text-xs text-gray-500 dark:text-gray-400">
                              {deal.company} • {deal.contact}
                            </p>
                            <div className="flex items-center space-x-2">
                              <span className="text-xs text-gray-500 dark:text-gray-400">
                                {deal.probability}% probability
                              </span>
                              <span className="text-xs text-gray-500 dark:text-gray-400">
                                Close: {deal.expectedCloseDate.toLocaleDateString()}
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              );
            })()}
          </div>
        )}
      </div>
    </div>
  );
}
