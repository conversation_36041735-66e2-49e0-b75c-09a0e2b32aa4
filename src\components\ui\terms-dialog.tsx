'use client';

import React from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { FileText, Shield, Eye, Database, Users, AlertTriangle } from 'lucide-react';

interface TermsDialogProps {
  children: React.ReactNode;
}

export function TermsDialog({ children }: TermsDialogProps) {
  return (
    <Dialog>
      <DialogTrigger asChild>
        {children}
      </DialogTrigger>
      <DialogContent className="max-w-4xl max-h-[80vh]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Terms and Conditions
          </DialogTitle>
          <DialogDescription>
            Please read our terms and conditions carefully before proceeding.
          </DialogDescription>
        </DialogHeader>
        
        <ScrollArea className="h-[60vh] pr-4">
          <div className="space-y-6">
            {/* Introduction */}
            <section>
              <h3 className="text-lg font-semibold mb-3">1. Introduction</h3>
              <p className="text-sm text-muted-foreground leading-relaxed">
                Welcome to our Customer Relationship Management (CRM) platform. These Terms and Conditions 
                ("Terms") govern your use of our service. By creating an account and using our platform, 
                you agree to be bound by these Terms.
              </p>
            </section>

            <Separator />

            {/* Service Description */}
            <section>
              <h3 className="text-lg font-semibold mb-3 flex items-center gap-2">
                <Users className="h-4 w-4" />
                2. Service Description
              </h3>
              <p className="text-sm text-muted-foreground leading-relaxed mb-3">
                Our CRM platform provides tools for managing customer relationships, including:
              </p>
              <ul className="text-sm text-muted-foreground space-y-1 ml-4">
                <li>• Contact and lead management</li>
                <li>• Sales pipeline tracking</li>
                <li>• Communication tools</li>
                <li>• Analytics and reporting</li>
                <li>• Team collaboration features</li>
              </ul>
            </section>

            <Separator />

            {/* User Responsibilities */}
            <section>
              <h3 className="text-lg font-semibold mb-3 flex items-center gap-2">
                <Shield className="h-4 w-4" />
                3. User Responsibilities
              </h3>
              <div className="space-y-3">
                <p className="text-sm text-muted-foreground leading-relaxed">
                  You are responsible for:
                </p>
                <ul className="text-sm text-muted-foreground space-y-1 ml-4">
                  <li>• Maintaining the confidentiality of your account credentials</li>
                  <li>• Ensuring all information provided is accurate and up-to-date</li>
                  <li>• Using the service in compliance with applicable laws</li>
                  <li>• Not sharing your account with unauthorized users</li>
                  <li>• Reporting any security breaches immediately</li>
                </ul>
              </div>
            </section>

            <Separator />

            {/* Privacy and Data Protection */}
            <section>
              <h3 className="text-lg font-semibold mb-3 flex items-center gap-2">
                <Database className="h-4 w-4" />
                4. Privacy and Data Protection
              </h3>
              <div className="space-y-3">
                <p className="text-sm text-muted-foreground leading-relaxed">
                  We take your privacy seriously and are committed to protecting your data:
                </p>
                <ul className="text-sm text-muted-foreground space-y-1 ml-4">
                  <li>• Your data is encrypted both in transit and at rest</li>
                  <li>• We comply with GDPR and other applicable privacy regulations</li>
                  <li>• You retain ownership of your customer data</li>
                  <li>• We will never sell your data to third parties</li>
                  <li>• You can export or delete your data at any time</li>
                </ul>
              </div>
            </section>

            <Separator />

            {/* Service Availability */}
            <section>
              <h3 className="text-lg font-semibold mb-3 flex items-center gap-2">
                <Eye className="h-4 w-4" />
                5. Service Availability
              </h3>
              <p className="text-sm text-muted-foreground leading-relaxed">
                While we strive to maintain 99.9% uptime, we cannot guarantee uninterrupted service. 
                We may perform scheduled maintenance with advance notice. We are not liable for any 
                downtime or service interruptions beyond our reasonable control.
              </p>
            </section>

            <Separator />

            {/* Limitation of Liability */}
            <section>
              <h3 className="text-lg font-semibold mb-3 flex items-center gap-2">
                <AlertTriangle className="h-4 w-4" />
                6. Limitation of Liability
              </h3>
              <p className="text-sm text-muted-foreground leading-relaxed">
                Our liability is limited to the amount you have paid for the service in the 12 months 
                preceding any claim. We are not liable for indirect, incidental, or consequential damages. 
                This limitation applies to the fullest extent permitted by law.
              </p>
            </section>

            <Separator />

            {/* Termination */}
            <section>
              <h3 className="text-lg font-semibold mb-3">7. Termination</h3>
              <p className="text-sm text-muted-foreground leading-relaxed">
                Either party may terminate this agreement at any time. Upon termination, you will lose 
                access to the service, but you may export your data within 30 days. We reserve the right 
                to terminate accounts that violate these Terms.
              </p>
            </section>

            <Separator />

            {/* Changes to Terms */}
            <section>
              <h3 className="text-lg font-semibold mb-3">8. Changes to Terms</h3>
              <p className="text-sm text-muted-foreground leading-relaxed">
                We may update these Terms from time to time. We will notify you of significant changes 
                via email or through the platform. Continued use of the service after changes constitutes 
                acceptance of the new Terms.
              </p>
            </section>

            <Separator />

            {/* Contact Information */}
            <section>
              <h3 className="text-lg font-semibold mb-3">9. Contact Information</h3>
              <p className="text-sm text-muted-foreground leading-relaxed">
                If you have any questions about these Terms, please contact us at:
              </p>
              <div className="mt-2 text-sm text-muted-foreground">
                <p>Email: <EMAIL></p>
                <p>Address: 123 Business Street, Suite 100, City, State 12345</p>
              </div>
            </section>

            <div className="mt-6 p-4 bg-muted/50 rounded-lg">
              <p className="text-xs text-muted-foreground">
                Last updated: {new Date().toLocaleDateString()}
              </p>
            </div>
          </div>
        </ScrollArea>
      </DialogContent>
    </Dialog>
  );
}
