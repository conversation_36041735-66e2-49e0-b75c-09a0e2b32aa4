'use client';

import React, { useState, useEffect, use } from 'react';
import { useRouter } from 'next/navigation';
import { OpportunityWithRelations } from '@/services/opportunity-management';
import { PermissionGate } from '@/components/ui/permission-gate';
import { PermissionResource, PermissionAction } from '@/types';
import { DocumentManagement } from '@/components/documents/document-management';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { EmptyState } from '@/components/ui/empty-state';
import {
  ArrowLeftIcon,
  PencilIcon,
  TrashIcon,
  CurrencyDollarIcon,
  CalendarIcon,
  UserIcon,
  BuildingOfficeIcon,
  ChartBarIcon,
  LightBulbIcon,
  ClockIcon,
  TagIcon
} from '@heroicons/react/24/outline';
import { Wand2 } from 'lucide-react';
import { PageLayout } from '@/components/layout/page-layout';
import { toastFunctions as toast } from '@/hooks/use-toast';
import { formatDateSafe } from '@/lib/utils';
import { useEnhancedDeleteDialog } from '@/components/ui/enhanced-delete-dialog';

import { OpportunityProposalManagement } from '@/components/proposals/opportunity-proposal-management';
import { AutomationStatusIndicator } from '@/components/automation/automation-status-indicator';
import { OpportunityQuickActions } from '@/components/opportunities/opportunity-quick-actions';

interface OpportunityDetailPageProps {
  params: Promise<{
    id: string;
  }>;
}

export default function OpportunityDetailPage({ params }: OpportunityDetailPageProps) {
  const router = useRouter();
  const resolvedParams = use(params);
  const [opportunity, setOpportunity] = useState<OpportunityWithRelations | null>(null);
  const [loading, setLoading] = useState(true);

  const { showDeleteDialog } = useEnhancedDeleteDialog();

  // Handle proposal wizard event
  useEffect(() => {
    const handleOpenProposalWizard = () => {
      if (opportunity) {
        router.push(`/opportunities/${opportunity.id}/generate-proposal`);
      }
    };

    window.addEventListener('openProposalWizard', handleOpenProposalWizard);
    return () => {
      window.removeEventListener('openProposalWizard', handleOpenProposalWizard);
    };
  }, [opportunity, router]);

  // Load opportunity data
  useEffect(() => {
    const loadOpportunity = async () => {
      try {
        setLoading(true);
        const response = await fetch(`/api/opportunities/${resolvedParams.id}`);

        if (response.ok) {
          const data = await response.json();
          setOpportunity(data);
        } else if (response.status === 404) {
          toast.error('Opportunity not found');
          router.push('/opportunities');
        } else {
          toast.error('Failed to load opportunity');
        }
      } catch (error) {
        console.error('Error loading opportunity:', error);
        toast.error('Failed to load opportunity');
      } finally {
        setLoading(false);
      }
    };

    loadOpportunity();
  }, [resolvedParams.id, router]);



  // Handle delete
  const handleDelete = async () => {
    if (!opportunity) return;

    const confirmed = await showDeleteDialog({
      title: 'Delete Opportunity',
      description: `Are you sure you want to delete "${opportunity.title}"? This action cannot be undone.`,
      itemName: opportunity.title,
      itemType: 'opportunity',
      onConfirm: async () => {
        // This will be handled by the showDeleteDialog return value
      }
    });

    if (confirmed) {
      try {
        const response = await fetch(`/api/opportunities/${opportunity.id}`, {
          method: 'DELETE'
        });

        if (response.ok) {
          toast.success('Opportunity deleted successfully');
          router.push('/opportunities');
        } else {
          toast.error('Failed to delete opportunity');
        }
      } catch (error) {
        console.error('Error deleting opportunity:', error);
        toast.error('Failed to delete opportunity');
      }
    }
  };

  // Format currency
  const formatCurrency = (amount: number | null) => {
    if (!amount) return 'No value set';
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };



  // Get initials for avatar
  const getInitials = (firstName?: string | null, lastName?: string | null) => {
    if (!firstName && !lastName) return 'U';
    return `${firstName?.[0] || ''}${lastName?.[0] || ''}`.toUpperCase();
  };

  // Priority badge component
  const PriorityBadge = ({ priority }: { priority: string }) => {
    const priorityConfig = {
      low: { color: 'bg-gray-100 text-gray-800', label: 'Low' },
      medium: { color: 'bg-blue-100 text-blue-800', label: 'Medium' },
      high: { color: 'bg-orange-100 text-orange-800', label: 'High' },
      urgent: { color: 'bg-red-100 text-red-800', label: 'Urgent' }
    };

    const config = priorityConfig[priority as keyof typeof priorityConfig] || priorityConfig.medium;
    
    return (
      <Badge className={config.color}>
        {config.label}
      </Badge>
    );
  };

  if (loading) {
    return (
      <PageLayout>
        <div className="flex justify-center items-center h-96">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </PageLayout>
    );
  }

  if (!opportunity) {
    return (
      <PageLayout>
        <Card>
          <CardContent className="p-8">
            <EmptyState
              icon={<ChartBarIcon className="w-12 h-12 text-muted-foreground" />}
              title="Opportunity Not Found"
              description="The opportunity you're looking for doesn't exist or you don't have permission to view it."
              action={{
                label: 'Back to Opportunities',
                onClick: () => router.push('/opportunities'),
                variant: 'primary'
              }}
            />
          </CardContent>
        </Card>
      </PageLayout>
    );
  }

  const actions = (
    <div className="flex items-center gap-2">
      <Button variant="outline" size="sm" onClick={() => router.back()}>
        <ArrowLeftIcon className="h-4 w-4 mr-2" />
        Back
      </Button>
      <OpportunityQuickActions
        opportunityId={opportunity.id}
        onAIInsightsClick={() => router.push(`/opportunities/${opportunity.id}/insights`)}
        onCreateProposalClick={() => router.push(`/opportunities/${opportunity.id}/generate-proposal`)}
        onViewPipelineClick={() => router.push('/opportunities/pipeline')}
        onEditClick={() => router.push(`/opportunities/${opportunity.id}/edit`)}
        onDeleteClick={handleDelete}
      />
    </div>
  );

  return (
    <PermissionGate
      resource={PermissionResource.OPPORTUNITIES}
      action={PermissionAction.READ}
      fallback={
        <PageLayout>
          <Card>
            <CardContent className="p-8">
              <EmptyState
                icon={<ChartBarIcon className="w-12 h-12 text-muted-foreground" />}
                title="Access Denied"
                description="You don't have permission to view this opportunity."
                action={{
                  label: 'Back to Opportunities',
                  onClick: () => router.push('/opportunities'),
                  variant: 'primary'
                }}
              />
            </CardContent>
          </Card>
        </PageLayout>
      }
    >
      <PageLayout
        title={opportunity.title}
        description="Opportunity details and management"
        actions={actions}
      >
        <div className="space-y-6">
          {/* Overview & Details */}
          <Card>
            <CardHeader>
              <CardTitle>Overview</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Description */}
              {opportunity.description && (
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Description</h4>
                  <p className="text-gray-600">{opportunity.description}</p>
                </div>
              )}

              {/* Key Metrics Grid */}
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                  <CurrencyDollarIcon className="h-5 w-5 text-green-500 flex-shrink-0" />
                  <div className="min-w-0">
                    <p className="text-sm text-gray-500">Value</p>
                    <p className="font-medium truncate">{formatCurrency(opportunity.value ? Number(opportunity.value) : null)}</p>
                  </div>
                </div>

                <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                  <ChartBarIcon className="h-5 w-5 text-blue-500 flex-shrink-0" />
                  <div className="min-w-0">
                    <p className="text-sm text-gray-500">Probability</p>
                    <p className="font-medium">{opportunity.probability}%</p>
                  </div>
                </div>

                {opportunity.expectedCloseDate && (
                  <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                    <CalendarIcon className="h-5 w-5 text-purple-500 flex-shrink-0" />
                    <div className="min-w-0">
                      <p className="text-sm text-gray-500">Expected Close</p>
                      <p className="font-medium truncate">
                        {formatDateSafe(opportunity.expectedCloseDate)}
                      </p>
                    </div>
                  </div>
                )}

                <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                  <ClockIcon className="h-5 w-5 text-orange-500 flex-shrink-0" />
                  <div className="min-w-0">
                    <p className="text-sm text-gray-500">Priority</p>
                    <PriorityBadge priority={(opportunity as any).priority || 'medium'} />
                  </div>
                </div>
              </div>

              <Separator />

              {/* Details Section */}
              <div>
                <h4 className="font-medium text-gray-900 mb-4">Details</h4>
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                  <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                    <div className="w-2 h-2 bg-blue-500 rounded-full flex-shrink-0"></div>
                    <div className="min-w-0">
                      <p className="text-sm text-gray-500">Current Stage</p>
                      <Badge variant="outline" className="text-sm">
                        {opportunity.stage}
                      </Badge>
                    </div>
                  </div>

                  <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                    <UserIcon className="h-5 w-5 text-gray-500 flex-shrink-0" />
                    <div className="min-w-0">
                      <p className="text-sm text-gray-500">Owner</p>
                      {opportunity.owner ? (
                        <div className="flex items-center space-x-2">
                          <Avatar className="h-6 w-6">
                            <AvatarImage src="" />
                            <AvatarFallback className="text-xs">
                              {getInitials(opportunity.owner.firstName, opportunity.owner.lastName)}
                            </AvatarFallback>
                          </Avatar>
                          <div className="min-w-0">
                            <p className="font-medium text-sm truncate">
                              {opportunity.owner.firstName} {opportunity.owner.lastName}
                            </p>
                          </div>
                        </div>
                      ) : (
                        <p className="text-gray-400 text-sm">Unassigned</p>
                      )}
                    </div>
                  </div>

                  <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                    <div className="w-2 h-2 bg-gray-500 rounded-full flex-shrink-0"></div>
                    <div className="min-w-0">
                      <p className="text-sm text-gray-500">Source</p>
                      <p className="text-sm font-medium truncate">{(opportunity as any).source || 'Not specified'}</p>
                    </div>
                  </div>

                  <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                    <CalendarIcon className="h-5 w-5 text-gray-500 flex-shrink-0" />
                    <div className="min-w-0">
                      <p className="text-sm text-gray-500">Created</p>
                      <p className="text-sm font-medium truncate">{formatDateSafe(opportunity.createdAt)}</p>
                    </div>
                  </div>

                  <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                    <ClockIcon className="h-5 w-5 text-gray-500 flex-shrink-0" />
                    <div className="min-w-0">
                      <p className="text-sm text-gray-500">Last Updated</p>
                      <p className="text-sm font-medium truncate">{formatDateSafe(opportunity.updatedAt)}</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Tags */}
              {Array.isArray((opportunity as any).tags) && (opportunity as any).tags.length > 0 && (
                <>
                  <Separator />
                  <div>
                    <div className="flex items-center space-x-2 mb-3">
                      <TagIcon className="h-4 w-4 text-gray-500" />
                      <h4 className="font-medium text-gray-900">Tags</h4>
                    </div>
                    <div className="flex flex-wrap gap-2">
                      {(opportunity as any).tags.map((tag: any, index: number) => (
                        <Badge key={index} variant="secondary">
                          {String(tag)}
                        </Badge>
                      ))}
                    </div>
                  </div>
                </>
              )}
            </CardContent>
          </Card>

          {/* Automation Status */}
          <Card>
            <CardHeader>
              <CardTitle>Automation Status</CardTitle>
            </CardHeader>
            <CardContent>
              <AutomationStatusIndicator opportunityId={opportunity.id} />
            </CardContent>
          </Card>

          {/* Related Information */}
          <Card>
            <CardHeader>
              <CardTitle>Related Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {/* Contact */}
                {opportunity.contact && (
                  <div className="flex items-center space-x-3 p-4 bg-gray-50 rounded-lg border border-gray-200 hover:border-gray-300 transition-colors">
                    <UserIcon className="h-6 w-6 text-blue-500 flex-shrink-0" />
                    <div className="flex-1 min-w-0">
                      <p className="font-medium text-sm truncate">
                        {opportunity.contact.firstName} {opportunity.contact.lastName}
                      </p>
                      <p className="text-xs text-gray-500 truncate">{opportunity.contact.email}</p>
                      <Button
                        variant="outline"
                        size="sm"
                        className="mt-2 w-full"
                        onClick={() => router.push(`/contacts/${opportunity.contact?.id}`)}
                      >
                        View Contact
                      </Button>
                    </div>
                  </div>
                )}

                {/* Company */}
                {opportunity.company && (
                  <div className="flex items-center space-x-3 p-4 bg-gray-50 rounded-lg border border-gray-200 hover:border-gray-300 transition-colors">
                    <BuildingOfficeIcon className="h-6 w-6 text-green-500 flex-shrink-0" />
                    <div className="flex-1 min-w-0">
                      <p className="font-medium text-sm truncate">{opportunity.company.name}</p>
                      {opportunity.company.industry && (
                        <p className="text-xs text-gray-500 truncate">{opportunity.company.industry}</p>
                      )}
                      <Button
                        variant="outline"
                        size="sm"
                        className="mt-2 w-full"
                        onClick={() => router.push(`/companies/${opportunity.company?.id}`)}
                      >
                        View Company
                      </Button>
                    </div>
                  </div>
                )}

                {/* Lead Source */}
                {opportunity.lead && (
                  <div className="flex items-center space-x-3 p-4 bg-gray-50 rounded-lg border border-gray-200 hover:border-gray-300 transition-colors">
                    <ChartBarIcon className="h-6 w-6 text-purple-500 flex-shrink-0" />
                    <div className="flex-1 min-w-0">
                      <p className="font-medium text-sm">Converted from Lead</p>
                      <p className="text-xs text-gray-500 truncate">{opportunity.lead.title}</p>
                      <Button
                        variant="outline"
                        size="sm"
                        className="mt-2 w-full"
                        onClick={() => router.push(`/leads/${opportunity.lead?.id}`)}
                      >
                        View Lead
                      </Button>
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Documents */}
          <DocumentManagement
            relatedToType="opportunity"
            relatedToId={opportunity.id}
            title="Opportunity Documents"
            showStats={false}
            opportunityTitle={opportunity.title}
            companyName={opportunity.company?.name}
          />

          {/* Proposal Management */}
          <OpportunityProposalManagement
            opportunityId={opportunity.id}
            opportunityTitle={opportunity.title}
          />
        </div>
      </PageLayout>


    </PermissionGate>
  );
}
