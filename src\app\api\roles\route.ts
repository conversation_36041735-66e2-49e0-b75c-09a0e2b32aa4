import { NextResponse } from 'next/server';
import { z } from 'zod';
import { withPermission, AuthenticatedRequest } from '@/lib/permission-middleware';
import { roleManagementService } from '@/services/role-management';
import { PermissionAction, PermissionResource } from '@/types';

const createRoleSchema = z.object({
  name: z.string().min(1, 'Role name is required').max(100, 'Role name too long'),
  description: z.string().optional(),
  parentRoleId: z.string().optional(),
  permissions: z.array(z.string()).default([])
});

const updateRoleSchema = z.object({
  name: z.string().min(1, 'Role name is required').max(100, 'Role name too long').optional(),
  description: z.string().optional(),
  parentRoleId: z.string().optional(),
  permissions: z.array(z.string()).optional()
});

/**
 * GET /api/roles
 * Get all roles for the current tenant
 */
export const GET = withPermission({
  resource: PermissionResource.ROLES,
  action: PermissionAction.READ,
  allowSuperAdmin: true
})(async (req) => {
  try {
    const { searchParams } = new URL(req.url);
    const includePermissions = searchParams.get('includePermissions') === 'true';
    const includeUserCount = searchParams.get('includeUserCount') === 'true';
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '50');
    const search = searchParams.get('search') || undefined;

    // Use getRoles method if any advanced options are requested
    if (includePermissions || includeUserCount || search || page > 1) {
      const result = await roleManagementService.getRoles(req.tenantId, {
        includePermissions,
        includeUserCount,
        page,
        limit,
        search
      });

      return NextResponse.json({
        success: true,
        data: {
          roles: result.roles,
          count: result.roles.length,
          pagination: result.pagination
        }
      });
    }

    // Use simple getTenantRoles for basic requests
    const roles = await roleManagementService.getTenantRoles(req.tenantId);

    return NextResponse.json({
      success: true,
      data: {
        roles,
        count: roles.length
      }
    });
  } catch (error) {
    console.error('Get roles error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch roles' },
      { status: 500 }
    );
  }
});

/**
 * POST /api/roles
 * Create a new role
 */
export const POST = withPermission({
  resource: PermissionResource.ROLES,
  action: PermissionAction.CREATE,
  allowSuperAdmin: true
})(async (req) => {
  try {
    const body = await req.json();
    const validatedData = createRoleSchema.parse(body);

    const role = await roleManagementService.createRole(
      req.tenantId,
      validatedData,
      req.userId
    );

    return NextResponse.json({
      success: true,
      data: { role },
      message: 'Role created successfully'
    }, { status: 201 });

  } catch (error) {
    console.error('Create role error:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          error: 'Validation failed',
          details: error.errors.map(e => ({
            field: e.path.join('.'),
            message: e.message
          }))
        },
        { status: 400 }
      );
    }

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to create role' },
      { status: 500 }
    );
  }
});

/**
 * PUT /api/roles
 * Update an existing role
 */
export const PUT = withPermission({
  resource: PermissionResource.ROLES,
  action: PermissionAction.UPDATE,
  allowSuperAdmin: true
})(async (req) => {
  try {
    const body = await req.json();
    const { roleId, ...updateData } = body;

    if (!roleId) {
      return NextResponse.json(
        { error: 'Role ID is required' },
        { status: 400 }
      );
    }

    const validatedData = updateRoleSchema.parse(updateData);

    const role = await roleManagementService.updateRole(
      roleId,
      req.tenantId,
      validatedData,
      req.userId
    );

    return NextResponse.json({
      success: true,
      data: { role },
      message: 'Role updated successfully'
    });

  } catch (error) {
    console.error('Update role error:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          error: 'Validation failed',
          details: error.errors.map(e => ({
            field: e.path.join('.'),
            message: e.message
          }))
        },
        { status: 400 }
      );
    }

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to update role' },
      { status: 500 }
    );
  }
});

/**
 * DELETE /api/roles
 * Delete a role
 */
export const DELETE = withPermission({
  resource: PermissionResource.ROLES,
  action: PermissionAction.DELETE,
  allowSuperAdmin: true
})(async (req) => {
  try {
    const { searchParams } = new URL(req.url);
    const roleId = searchParams.get('roleId');

    if (!roleId) {
      return NextResponse.json(
        { error: 'Role ID is required' },
        { status: 400 }
      );
    }

    await roleManagementService.deleteRole(roleId, req.tenantId, req.userId);

    return NextResponse.json({
      success: true,
      message: 'Role deleted successfully'
    });

  } catch (error) {
    console.error('Delete role error:', error);

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to delete role' },
      { status: 500 }
    );
  }
});
