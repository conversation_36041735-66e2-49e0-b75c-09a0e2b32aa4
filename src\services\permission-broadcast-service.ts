import { permissionService } from './permission-service';
import { prisma } from '@/lib/prisma';

export interface PermissionUpdateEvent {
  type: 'permission_update' | 'role_update' | 'permission_revoke';
  userId: string;
  tenantId: string;
  permissions?: any[];
  roles?: any[];
  effectivePermissions?: any[];
  reason?: string;
}

export class PermissionBroadcastService {
  /**
   * Broadcast permission updates to WebSocket clients
   */
  private static async broadcastToWebSocket(
    userId: string,
    tenantId: string,
    updateData: Omit<PermissionUpdateEvent, 'userId' | 'tenantId'>
  ): Promise<void> {
    try {
      // Call the WebSocket API endpoint to broadcast the update
      const response = await fetch(`${process.env.NEXTAUTH_URL || 'http://localhost:3000'}/api/socket/permissions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'broadcast_update',
          userId,
          tenantId,
          updateData
        })
      });

      if (!response.ok) {
        console.error('Failed to broadcast permission update via WebSocket:', await response.text());
      }
    } catch (error) {
      console.error('Error broadcasting permission update:', error);
    }
  }

  /**
   * Notify users when their permissions are updated
   */
  static async notifyPermissionUpdate(
    userId: string,
    tenantId: string,
    reason: string = 'permission_change'
  ): Promise<void> {
    try {
      // Get fresh permissions for the user
      const userPermissions = await permissionService.getUserEffectivePermissions(userId, tenantId);

      // Invalidate cache
      await permissionService.invalidateUserPermissions(userId, tenantId);

      // Broadcast the update
      await this.broadcastToWebSocket(userId, tenantId, {
        type: 'permission_update',
        permissions: userPermissions.directPermissions,
        roles: userPermissions.roles,
        effectivePermissions: userPermissions.effectivePermissions,
        reason
      });

      console.log(`📡 Permission update notification sent for user ${userId} in tenant ${tenantId}`);
    } catch (error) {
      console.error('Error notifying permission update:', error);
    }
  }

  /**
   * Notify users when their roles are updated
   */
  static async notifyRoleUpdate(
    userId: string,
    tenantId: string,
    reason: string = 'role_change'
  ): Promise<void> {
    try {
      // Get fresh permissions for the user (roles affect effective permissions)
      const userPermissions = await permissionService.getUserEffectivePermissions(userId, tenantId);

      // Invalidate cache
      await permissionService.invalidateUserPermissions(userId, tenantId);

      // Broadcast the update
      await this.broadcastToWebSocket(userId, tenantId, {
        type: 'role_update',
        permissions: userPermissions.directPermissions,
        roles: userPermissions.roles,
        effectivePermissions: userPermissions.effectivePermissions,
        reason
      });

      console.log(`📡 Role update notification sent for user ${userId} in tenant ${tenantId}`);
    } catch (error) {
      console.error('Error notifying role update:', error);
    }
  }

  /**
   * Notify users when permissions are revoked
   */
  static async notifyPermissionRevoke(
    userId: string,
    tenantId: string,
    reason: string = 'permission_revoked'
  ): Promise<void> {
    try {
      // Get fresh permissions for the user
      const userPermissions = await permissionService.getUserEffectivePermissions(userId, tenantId);

      // Invalidate cache
      await permissionService.invalidateUserPermissions(userId, tenantId);

      // Broadcast the update
      await this.broadcastToWebSocket(userId, tenantId, {
        type: 'permission_revoke',
        permissions: userPermissions.directPermissions,
        roles: userPermissions.roles,
        effectivePermissions: userPermissions.effectivePermissions,
        reason
      });

      console.log(`📡 Permission revoke notification sent for user ${userId} in tenant ${tenantId}`);
    } catch (error) {
      console.error('Error notifying permission revoke:', error);
    }
  }

  /**
   * Notify all users in a tenant when tenant-wide permission changes occur
   */
  static async notifyTenantPermissionUpdate(
    tenantId: string,
    reason: string = 'tenant_permission_change'
  ): Promise<void> {
    try {
      // Get all active users in the tenant
      const tenantUsers = await prisma.tenantUser.findMany({
        where: {
          tenantId,
          status: 'active'
        },
        select: {
          userId: true
        }
      });

      // Notify each user
      const notifications = tenantUsers.map(tenantUser =>
        this.notifyPermissionUpdate(tenantUser.userId, tenantId, reason)
      );

      await Promise.allSettled(notifications);

      console.log(`📡 Tenant-wide permission update notifications sent for tenant ${tenantId} (${tenantUsers.length} users)`);
    } catch (error) {
      console.error('Error notifying tenant permission update:', error);
    }
  }

  /**
   * Notify all users with a specific role when role permissions change
   */
  static async notifyRolePermissionUpdate(
    roleId: string,
    tenantId: string,
    reason: string = 'role_permission_change'
  ): Promise<void> {
    try {
      // Get all users with this role
      const usersWithRole = await prisma.userRole.findMany({
        where: {
          roleId,
          role: {
            tenantId
          }
        },
        select: {
          userId: true
        }
      });

      // Notify each user
      const notifications = usersWithRole.map(userRole =>
        this.notifyRoleUpdate(userRole.userId, tenantId, reason)
      );

      await Promise.allSettled(notifications);

      console.log(`📡 Role permission update notifications sent for role ${roleId} (${usersWithRole.length} users)`);
    } catch (error) {
      console.error('Error notifying role permission update:', error);
    }
  }

  /**
   * Bulk notify multiple users with different update types
   */
  static async notifyBulkPermissionUpdates(
    updates: Array<{
      userId: string;
      tenantId: string;
      type: 'permission_update' | 'role_update' | 'permission_revoke';
      reason?: string;
    }>
  ): Promise<void> {
    try {
      // Process updates in batches to avoid overwhelming the system
      const batchSize = 10;
      const batches = [];
      
      for (let i = 0; i < updates.length; i += batchSize) {
        batches.push(updates.slice(i, i + batchSize));
      }

      for (const batch of batches) {
        const batchPromises = batch.map(async (update) => {
          try {
            const userPermissions = await permissionService.getUserEffectivePermissions(
              update.userId,
              update.tenantId
            );

            await permissionService.invalidateUserPermissions(update.userId, update.tenantId);

            return {
              ...update,
              permissions: userPermissions.directPermissions,
              roles: userPermissions.roles,
              effectivePermissions: userPermissions.effectivePermissions
            };
          } catch (error) {
            console.error(`Error processing bulk update for user ${update.userId}:`, error);
            return null;
          }
        });

        const processedUpdates = (await Promise.allSettled(batchPromises))
          .map(result => result.status === 'fulfilled' ? result.value : null)
          .filter(update => update !== null);

        // Broadcast bulk updates
        if (processedUpdates.length > 0) {
          try {
            const response = await fetch(`${process.env.NEXTAUTH_URL || 'http://localhost:3000'}/api/socket/permissions`, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                action: 'broadcast_bulk_update',
                updateData: processedUpdates
              })
            });

            if (!response.ok) {
              console.error('Failed to broadcast bulk permission updates:', await response.text());
            }
          } catch (error) {
            console.error('Error broadcasting bulk permission updates:', error);
          }
        }
      }

      console.log(`📡 Bulk permission update notifications sent for ${updates.length} users`);
    } catch (error) {
      console.error('Error notifying bulk permission updates:', error);
    }
  }

  /**
   * Schedule periodic permission cache cleanup and validation
   */
  static async schedulePermissionMaintenance(): Promise<void> {
    try {
      // This could be called from a cron job or scheduled task
      console.log('🔧 Starting permission maintenance...');

      // Clean up expired permission caches
      const expiredCaches = await this.cleanupExpiredPermissionCaches();
      console.log(`🧹 Cleaned up ${expiredCaches} expired permission caches`);

      // Validate permission integrity for active users
      const validationResults = await this.validatePermissionIntegrity();
      console.log(`✅ Permission integrity validation completed: ${validationResults.valid} valid, ${validationResults.invalid} invalid`);

      console.log('🔧 Permission maintenance completed');
    } catch (error) {
      console.error('Error during permission maintenance:', error);
    }
  }

  /**
   * Clean up expired permission caches from Redis
   */
  private static async cleanupExpiredPermissionCaches(): Promise<number> {
    try {
      // This would integrate with your Redis cache service
      // For now, we'll return a placeholder
      return 0;
    } catch (error) {
      console.error('Error cleaning up expired permission caches:', error);
      return 0;
    }
  }

  /**
   * Validate permission integrity across the system
   */
  private static async validatePermissionIntegrity(): Promise<{ valid: number; invalid: number }> {
    try {
      // Get a sample of active users to validate
      const sampleUsers = await prisma.tenantUser.findMany({
        where: {
          status: 'active'
        },
        take: 100, // Validate a sample to avoid performance issues
        select: {
          userId: true,
          tenantId: true
        }
      });

      let valid = 0;
      let invalid = 0;

      for (const user of sampleUsers) {
        try {
          // Validate that the user's cached permissions match their actual permissions
          const freshPermissions = await permissionService.getUserEffectivePermissions(
            user.userId,
            user.tenantId
          );
          
          // If we get here without error, the permissions are valid
          valid++;
        } catch (error) {
          console.warn(`Permission validation failed for user ${user.userId}:`, error);
          invalid++;
          
          // Invalidate the user's cache to force a refresh
          await permissionService.invalidateUserPermissions(user.userId, user.tenantId);
        }
      }

      return { valid, invalid };
    } catch (error) {
      console.error('Error validating permission integrity:', error);
      return { valid: 0, invalid: 0 };
    }
  }
}

export const permissionBroadcastService = new PermissionBroadcastService();
