import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../../../../../lib/auth';
import { ChatMessageService } from '../../../../../services/communication';
import { withTenant } from '../../../../../lib/middleware/withTenant';
import { withRateLimit } from '../../../../../lib/middleware/withRateLimit';

async function handler(req: NextApiRequest, res: NextApiResponse) {
  const session = await getServerSession(req, res, authOptions);
  
  if (!session?.user?.id) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  const tenantId = req.headers['x-tenant-id'] as string;
  const userId = session.user.id;
  const messageId = req.query.messageId as string;

  if (!messageId) {
    return res.status(400).json({ error: 'Message ID is required' });
  }

  try {
    switch (req.method) {
      case 'POST':
        const { emoji } = req.body;
        
        if (!emoji) {
          return res.status(400).json({ error: 'Emoji is required' });
        }

        const updatedMessage = await ChatMessageService.addReaction(
          tenantId,
          messageId,
          userId,
          emoji
        );
        return res.status(200).json(updatedMessage);

      case 'DELETE':
        const { emoji: removeEmoji } = req.body;
        
        if (!removeEmoji) {
          return res.status(400).json({ error: 'Emoji is required' });
        }

        const message = await ChatMessageService.removeReaction(
          tenantId,
          messageId,
          userId,
          removeEmoji
        );
        return res.status(200).json(message);

      default:
        res.setHeader('Allow', ['POST', 'DELETE']);
        return res.status(405).json({ error: `Method ${req.method} not allowed` });
    }
  } catch (error) {
    console.error('Message reactions API error:', error);
    return res.status(500).json({ 
      error: error instanceof Error ? error.message : 'Internal server error' 
    });
  }
}

export default withRateLimit(withTenant(handler));
