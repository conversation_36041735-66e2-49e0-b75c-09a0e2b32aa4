import { NextResponse } from 'next/server';
import { z } from 'zod';
import { withPermission, AuthenticatedRequest } from '@/lib/permission-middleware';
import { TeamManagementService } from '@/services/team-management';
import { PermissionAction, PermissionResource } from '@/types';

const addMemberSchema = z.object({
  userId: z.string().min(1, 'User ID is required'),
  role: z.string().optional()
});

const addMembersSchema = z.object({
  userIds: z.array(z.string()).min(1, 'At least one user ID is required'),
  role: z.string().optional()
});

const removeMemberSchema = z.object({
  userId: z.string().min(1, 'User ID is required')
});

interface RouteParams {
  params: Promise<{
    id: string;
  }>;
}

/**
 * GET /api/teams/[id]/members
 * Get all members of a specific team
 */
export const GET = withPermission({
  resource: PermissionResource.USERS,
  action: PermissionAction.READ,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest, context: { params: Promise<{ id: string }> }) => {
  try {
    const { id: teamId } = await context.params;

    const teamService = new TeamManagementService();
    const members = await teamService.getTeamMembers(teamId, req.tenantId);

    return NextResponse.json({
      success: true,
      data: { members }
    });
  } catch (error) {
    console.error('Get team members error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch team members' },
      { status: 500 }
    );
  }
});

/**
 * POST /api/teams/[id]/members
 * Add member(s) to a team
 */
export const POST = withPermission({
  resource: PermissionResource.USERS,
  action: PermissionAction.MANAGE,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest, context: { params: Promise<{ id: string }> }) => {
  try {
    const { id: teamId } = await context.params;
    const body = await req.json();

    const teamService = new TeamManagementService();

    // Check if it's a single member or multiple members
    if (body.userIds && Array.isArray(body.userIds)) {
      // Multiple members
      const validatedData = addMembersSchema.parse(body);
      await teamService.addMembersToTeam(
        teamId,
        req.tenantId,
        validatedData.userIds,
        req.userId,
        validatedData.role
      );

      return NextResponse.json({
        success: true,
        message: `${validatedData.userIds.length} members added to team successfully`
      }, { status: 201 });
    } else {
      // Single member
      const validatedData = addMemberSchema.parse(body);
      await teamService.addMemberToTeam(
        teamId,
        req.tenantId,
        validatedData.userId,
        req.userId,
        validatedData.role
      );

      return NextResponse.json({
        success: true,
        message: 'Member added to team successfully'
      }, { status: 201 });
    }
  } catch (error) {
    console.error('Add team member error:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid input data', details: error.errors },
        { status: 400 }
      );
    }

    if (error instanceof Error) {
      if (error.message.includes('not found')) {
        return NextResponse.json(
          { error: 'Team or user not found' },
          { status: 404 }
        );
      }
      
      if (error.message.includes('already a member')) {
        return NextResponse.json(
          { error: 'User is already a member of this team' },
          { status: 409 }
        );
      }
    }

    return NextResponse.json(
      { error: 'Failed to add member to team' },
      { status: 500 }
    );
  }
});

/**
 * DELETE /api/teams/[id]/members
 * Remove a member from a team
 */
export const DELETE = withPermission({
  resource: PermissionResource.USERS,
  action: PermissionAction.MANAGE,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest, context: { params: Promise<{ id: string }> }) => {
  try {
    const { id: teamId } = await context.params;
    const body = await req.json();
    const validatedData = removeMemberSchema.parse(body);

    const teamService = new TeamManagementService();
    await teamService.removeMemberFromTeam(
      teamId,
      req.tenantId,
      validatedData.userId,
      req.userId
    );

    return NextResponse.json({
      success: true,
      message: 'Member removed from team successfully'
    });
  } catch (error) {
    console.error('Remove team member error:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid input data', details: error.errors },
        { status: 400 }
      );
    }

    if (error instanceof Error) {
      if (error.message.includes('not found')) {
        return NextResponse.json(
          { error: 'Team or user not found' },
          { status: 404 }
        );
      }
      
      if (error.message.includes('not a member')) {
        return NextResponse.json(
          { error: 'User is not a member of this team' },
          { status: 409 }
        );
      }
    }

    return NextResponse.json(
      { error: 'Failed to remove member from team' },
      { status: 500 }
    );
  }
});
