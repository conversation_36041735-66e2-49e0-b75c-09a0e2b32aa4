'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { PageLayout } from '@/components/layout/page-layout';
import { PermissionGate } from '@/components/ui/permission-gate';
import { PermissionResource, PermissionAction } from '@/types';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { EmptyState } from '@/components/ui/empty-state';
import { Skeleton } from '@/components/ui/skeleton';
import { useToast } from '@/hooks/use-toast';
import { NurturingCampaign } from '@/services/lead-nurturing';
import { 
  ExclamationTriangleIcon,
  PencilIcon,
  PlayIcon,
  PauseIcon,
  TrashIcon,
  EnvelopeIcon,
  UsersIcon,
  ChartBarIcon,
  ClockIcon
} from '@heroicons/react/24/outline';
import { formatDistanceToNow } from 'date-fns';

interface CampaignDetailsPageProps {
  params: {
    campaignId: string;
  };
}

export default function CampaignDetailsPage({ params }: CampaignDetailsPageProps) {
  const [campaign, setCampaign] = useState<NurturingCampaign | null>(null);
  const [loading, setLoading] = useState(true);
  const [executing, setExecuting] = useState(false);
  const { data: session } = useSession();
  const router = useRouter();
  const { toast } = useToast();
  const tenantId = session?.user?.tenants?.[0]?.id;

  useEffect(() => {
    if (tenantId) {
      fetchCampaign();
    }
  }, [tenantId, params.campaignId]);

  const fetchCampaign = async () => {
    try {
      const response = await fetch(`/api/leads/nurturing/campaigns/${params.campaignId}`, {
        headers: {
          'x-tenant-id': tenantId!
        }
      });

      if (!response.ok) {
        if (response.status === 404) {
          router.push('/leads/nurturing');
          return;
        }
        throw new Error('Failed to fetch campaign');
      }

      const data = await response.json();
      setCampaign(data.data);
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to fetch campaign details',
        variant: 'destructive'
      });
      router.push('/leads/nurturing');
    } finally {
      setLoading(false);
    }
  };

  const executeCampaign = async (dryRun: boolean = false) => {
    setExecuting(true);
    try {
      const response = await fetch(`/api/leads/nurturing/campaigns/${params.campaignId}/execute`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-tenant-id': tenantId!
        },
        body: JSON.stringify({ dryRun })
      });

      if (!response.ok) {
        throw new Error('Failed to execute campaign');
      }

      const data = await response.json();
      const result = data.data;

      toast({
        title: dryRun ? 'Dry Run Complete' : 'Campaign Executed',
        description: `Processed ${result.processed} leads, sent ${result.emailsSent} emails${result.errors.length > 0 ? `, ${result.errors.length} errors` : ''}`,
        variant: result.errors.length > 0 ? 'destructive' : 'default'
      });

      if (!dryRun) {
        fetchCampaign(); // Refresh campaign to update stats
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to execute campaign',
        variant: 'destructive'
      });
    } finally {
      setExecuting(false);
    }
  };

  const handleDelete = async () => {
    if (!confirm('Are you sure you want to delete this campaign? This action cannot be undone.')) {
      return;
    }

    try {
      const response = await fetch(`/api/leads/nurturing/campaigns/${params.campaignId}`, {
        method: 'DELETE',
        headers: {
          'x-tenant-id': tenantId!
        }
      });

      if (!response.ok) {
        throw new Error('Failed to delete campaign');
      }

      toast({
        title: 'Success',
        description: 'Campaign deleted successfully',
      });

      router.push('/leads/nurturing');
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to delete campaign',
        variant: 'destructive'
      });
    }
  };

  if (!tenantId) {
    return (
      <PageLayout>
        <div className="flex items-center justify-center h-64">
          <p className="text-muted-foreground">Loading...</p>
        </div>
      </PageLayout>
    );
  }

  if (loading) {
    return (
      <PageLayout>
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <div className="space-y-2">
              <Skeleton className="h-8 w-64" />
              <Skeleton className="h-4 w-96" />
            </div>
            <div className="flex gap-2">
              <Skeleton className="h-10 w-20" />
              <Skeleton className="h-10 w-20" />
              <Skeleton className="h-10 w-20" />
            </div>
          </div>
          
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
            {[1, 2, 3, 4].map((i) => (
              <Card key={i}>
                <CardContent className="p-6">
                  <div className="space-y-2">
                    <Skeleton className="h-4 w-16" />
                    <Skeleton className="h-8 w-12" />
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
          
          <Card>
            <CardHeader>
              <Skeleton className="h-6 w-32" />
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {[1, 2, 3].map((i) => (
                  <div key={i} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="space-y-2">
                      <Skeleton className="h-4 w-24" />
                      <Skeleton className="h-3 w-32" />
                    </div>
                    <Skeleton className="h-6 w-16" />
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </PageLayout>
    );
  }

  if (!campaign) {
    return (
      <PageLayout>
        <Card>
          <CardContent className="p-8">
            <EmptyState
              icon={<ExclamationTriangleIcon className="w-12 h-12 text-muted-foreground" />}
              title="Campaign Not Found"
              description="The campaign you're looking for doesn't exist or has been deleted."
              action={{
                label: 'Back to Campaigns',
                onClick: () => router.push('/leads/nurturing'),
                variant: 'primary'
              }}
            />
          </CardContent>
        </Card>
      </PageLayout>
    );
  }

  const actions = (
    <div className="flex items-center gap-3">
      <Button
        variant="outline"
        onClick={() => executeCampaign(true)}
        disabled={executing}
      >
        Test Run
      </Button>
      <Button
        onClick={() => executeCampaign(false)}
        disabled={executing || !campaign.isActive}
      >
        {executing ? (
          <>
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
            Running...
          </>
        ) : (
          <>
            <PlayIcon className="h-4 w-4 mr-2" />
            Execute
          </>
        )}
      </Button>
      <Button
        variant="outline"
        onClick={() => router.push(`/leads/nurturing/${campaign.id}/edit`)}
      >
        <PencilIcon className="h-4 w-4 mr-2" />
        Edit
      </Button>
      <Button
        variant="outline"
        onClick={handleDelete}
        className="text-destructive hover:text-destructive"
      >
        <TrashIcon className="h-4 w-4 mr-2" />
        Delete
      </Button>
    </div>
  );

  return (
    <PermissionGate
      resource={PermissionResource.LEADS}
      action={PermissionAction.READ}
      fallback={
        <PageLayout>
          <Card>
            <CardContent className="p-8">
              <EmptyState
                icon={<ExclamationTriangleIcon className="w-12 h-12 text-muted-foreground" />}
                title="Access Denied"
                description="You don't have permission to view this campaign. Please contact your administrator for access."
                action={{
                  label: 'Back to Campaigns',
                  onClick: () => router.push('/leads/nurturing'),
                  variant: 'primary'
                }}
              />
            </CardContent>
          </Card>
        </PageLayout>
      }
    >
      <PageLayout
        title={campaign.name}
        description={campaign.description || 'No description provided'}
        actions={actions}
      >
        <div className="space-y-6">
          {/* Campaign Status */}
          <div className="flex items-center gap-4">
            <Badge variant={campaign.isActive ? 'default' : 'secondary'} className="text-sm">
              {campaign.isActive ? 'Active' : 'Inactive'}
            </Badge>
            <span className="text-sm text-muted-foreground">
              Created {formatDistanceToNow(new Date(campaign.createdAt), { addSuffix: true })}
            </span>
            <span className="text-sm text-muted-foreground">
              Updated {formatDistanceToNow(new Date(campaign.updatedAt), { addSuffix: true })}
            </span>
          </div>

          {/* Campaign Progress */}
          {campaign.stats.totalLeads > 0 && (
            <Card>
              <CardContent className="p-6">
                <div className="space-y-2">
                  <div className="flex items-center justify-between text-sm">
                    <span className="font-medium">Campaign Progress</span>
                    <span className="text-muted-foreground">
                      {campaign.stats.processedLeads} of {campaign.stats.totalLeads} leads processed
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                      style={{
                        width: `${Math.min(100, (campaign.stats.processedLeads / campaign.stats.totalLeads) * 100)}%`
                      }}
                    ></div>
                  </div>
                  <div className="flex items-center justify-between text-xs text-muted-foreground">
                    <span>
                      {campaign.stats.totalLeads - campaign.stats.processedLeads} leads remaining
                    </span>
                    <span>
                      {Math.round((campaign.stats.processedLeads / campaign.stats.totalLeads) * 100)}% complete
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Campaign Stats */}
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-5">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Eligible Leads</p>
                    <p className="text-2xl font-bold">{campaign.stats.totalLeads}</p>
                    <p className="text-xs text-muted-foreground">Match criteria</p>
                  </div>
                  <UsersIcon className="h-8 w-8 text-muted-foreground" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Processed</p>
                    <p className="text-2xl font-bold">{campaign.stats.processedLeads}</p>
                    <p className="text-xs text-muted-foreground">In campaign</p>
                  </div>
                  <div className="h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center">
                    <div className="h-4 w-4 rounded-full bg-blue-600"></div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Emails Sent</p>
                    <p className="text-2xl font-bold">{campaign.stats.emailsSent}</p>
                    <p className="text-xs text-muted-foreground">Total sent</p>
                  </div>
                  <EnvelopeIcon className="h-8 w-8 text-muted-foreground" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Responses</p>
                    <p className="text-2xl font-bold">{campaign.stats.responses}</p>
                    <p className="text-xs text-muted-foreground">Replied</p>
                  </div>
                  <ChartBarIcon className="h-8 w-8 text-muted-foreground" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Conversions</p>
                    <p className="text-2xl font-bold text-green-600">{campaign.stats.conversions}</p>
                    <p className="text-xs text-green-600">Qualified+</p>
                  </div>
                  <div className="text-green-600">✓</div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Campaign Configuration */}
          <div className="grid gap-6 md:grid-cols-2">
            {/* Target Criteria */}
            <Card>
              <CardHeader>
                <CardTitle>Target Criteria</CardTitle>
                <CardDescription>
                  Leads matching these criteria will be included in the campaign
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <p className="text-sm font-medium mb-2">Target Status</p>
                  <div className="flex flex-wrap gap-2">
                    {campaign.targetStatus.map((status) => (
                      <Badge key={status} variant="outline">
                        {status}
                      </Badge>
                    ))}
                  </div>
                </div>
                
                {campaign.targetPriority && campaign.targetPriority.length > 0 && (
                  <div>
                    <p className="text-sm font-medium mb-2">Target Priority</p>
                    <div className="flex flex-wrap gap-2">
                      {campaign.targetPriority.map((priority) => (
                        <Badge key={priority} variant="outline">
                          {priority}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}

                <div>
                  <p className="text-sm font-medium">Max Leads Per Day</p>
                  <p className="text-lg font-semibold">{campaign.maxLeadsPerDay}</p>
                </div>
              </CardContent>
            </Card>

            {/* Email Sequence */}
            <Card>
              <CardHeader>
                <CardTitle>Email Sequence</CardTitle>
                <CardDescription>
                  {campaign.emailSequence.length} email{campaign.emailSequence.length !== 1 ? 's' : ''} in the sequence
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {campaign.emailSequence.map((step, index) => (
                    <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center space-x-3">
                        <div className="flex items-center justify-center w-8 h-8 bg-primary/10 rounded-full">
                          <span className="text-sm font-medium">{index + 1}</span>
                        </div>
                        <div>
                          <p className="font-medium capitalize">
                            {step.emailType.replace('_', ' ')}
                          </p>
                          <div className="flex items-center text-sm text-muted-foreground">
                            <ClockIcon className="h-4 w-4 mr-1" />
                            {step.delayDays === 0 ? 'Immediate' : `${step.delayDays} day${step.delayDays !== 1 ? 's' : ''} delay`}
                          </div>
                        </div>
                      </div>
                      <Badge variant="outline">{step.emailType}</Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </PageLayout>
    </PermissionGate>
  );
}
