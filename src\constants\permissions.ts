/**
 * Unified Permission Constants
 * 
 * This file centralizes all permission names to prevent typos and provide
 * type safety throughout the application. All permission strings should
 * be imported from here rather than hardcoded.
 */

import { PermissionResource, PermissionAction } from '@/types';

// Helper function to create permission name
const createPermission = (resource: string, action: string): string => {
  return `${resource}.${action.toLowerCase()}`;
};

// Contact Permissions
export const CONTACT_PERMISSIONS = {
  CREATE: createPermission(PermissionResource.CONTACTS, PermissionAction.CREATE),
  READ: createPermission(PermissionResource.CONTACTS, PermissionAction.READ),
  UPDATE: createPermission(PermissionResource.CONTACTS, PermissionAction.UPDATE),
  DELETE: createPermission(PermissionResource.CONTACTS, PermissionAction.DELETE),
  EXPORT: createPermission(PermissionResource.CONTACTS, PermissionAction.EXPORT),
  MANAGE: createPermission(PermissionResource.CONTACTS, PermissionAction.MANAGE),
} as const;

// Company Permissions
export const COMPANY_PERMISSIONS = {
  CREATE: createPermission(PermissionResource.COMPANIES, PermissionAction.CREATE),
  READ: createPermission(PermissionResource.COMPANIES, PermissionAction.READ),
  UPDATE: createPermission(PermissionResource.COMPANIES, PermissionAction.UPDATE),
  DELETE: createPermission(PermissionResource.COMPANIES, PermissionAction.DELETE),
  EXPORT: createPermission(PermissionResource.COMPANIES, PermissionAction.EXPORT),
  MANAGE: createPermission(PermissionResource.COMPANIES, PermissionAction.MANAGE),
} as const;

// Lead Permissions
export const LEAD_PERMISSIONS = {
  CREATE: createPermission(PermissionResource.LEADS, PermissionAction.CREATE),
  READ: createPermission(PermissionResource.LEADS, PermissionAction.READ),
  UPDATE: createPermission(PermissionResource.LEADS, PermissionAction.UPDATE),
  DELETE: createPermission(PermissionResource.LEADS, PermissionAction.DELETE),
  EXPORT: createPermission(PermissionResource.LEADS, PermissionAction.EXPORT),
  MANAGE: createPermission(PermissionResource.LEADS, PermissionAction.MANAGE),
} as const;

// Opportunity Permissions
export const OPPORTUNITY_PERMISSIONS = {
  CREATE: createPermission(PermissionResource.OPPORTUNITIES, PermissionAction.CREATE),
  READ: createPermission(PermissionResource.OPPORTUNITIES, PermissionAction.READ),
  UPDATE: createPermission(PermissionResource.OPPORTUNITIES, PermissionAction.UPDATE),
  DELETE: createPermission(PermissionResource.OPPORTUNITIES, PermissionAction.DELETE),
  EXPORT: createPermission(PermissionResource.OPPORTUNITIES, PermissionAction.EXPORT),
  MANAGE: createPermission(PermissionResource.OPPORTUNITIES, PermissionAction.MANAGE),
} as const;

// Proposal Permissions
export const PROPOSAL_PERMISSIONS = {
  CREATE: createPermission(PermissionResource.PROPOSALS, PermissionAction.CREATE),
  READ: createPermission(PermissionResource.PROPOSALS, PermissionAction.READ),
  UPDATE: createPermission(PermissionResource.PROPOSALS, PermissionAction.UPDATE),
  DELETE: createPermission(PermissionResource.PROPOSALS, PermissionAction.DELETE),
  EXPORT: createPermission(PermissionResource.PROPOSALS, PermissionAction.EXPORT),
  MANAGE: createPermission(PermissionResource.PROPOSALS, PermissionAction.MANAGE),
} as const;

// Project Permissions
export const PROJECT_PERMISSIONS = {
  CREATE: createPermission(PermissionResource.PROJECTS, PermissionAction.CREATE),
  READ: createPermission(PermissionResource.PROJECTS, PermissionAction.READ),
  UPDATE: createPermission(PermissionResource.PROJECTS, PermissionAction.UPDATE),
  DELETE: createPermission(PermissionResource.PROJECTS, PermissionAction.DELETE),
  EXPORT: createPermission(PermissionResource.PROJECTS, PermissionAction.EXPORT),
  MANAGE: createPermission(PermissionResource.PROJECTS, PermissionAction.MANAGE),
} as const;

// Handover Permissions
export const HANDOVER_PERMISSIONS = {
  CREATE: createPermission(PermissionResource.HANDOVERS, PermissionAction.CREATE),
  READ: createPermission(PermissionResource.HANDOVERS, PermissionAction.READ),
  UPDATE: createPermission(PermissionResource.HANDOVERS, PermissionAction.UPDATE),
  DELETE: createPermission(PermissionResource.HANDOVERS, PermissionAction.DELETE),
  MANAGE: createPermission(PermissionResource.HANDOVERS, PermissionAction.MANAGE),
} as const;

// User Management Permissions
export const USER_PERMISSIONS = {
  CREATE: createPermission(PermissionResource.USERS, PermissionAction.CREATE),
  READ: createPermission(PermissionResource.USERS, PermissionAction.READ),
  UPDATE: createPermission(PermissionResource.USERS, PermissionAction.UPDATE),
  DELETE: createPermission(PermissionResource.USERS, PermissionAction.DELETE),
  MANAGE: createPermission(PermissionResource.USERS, PermissionAction.MANAGE),
} as const;

// Role Management Permissions
export const ROLE_PERMISSIONS = {
  CREATE: createPermission(PermissionResource.ROLES, PermissionAction.CREATE),
  READ: createPermission(PermissionResource.ROLES, PermissionAction.READ),
  UPDATE: createPermission(PermissionResource.ROLES, PermissionAction.UPDATE),
  DELETE: createPermission(PermissionResource.ROLES, PermissionAction.DELETE),
  MANAGE: createPermission(PermissionResource.ROLES, PermissionAction.MANAGE),
} as const;

// Settings Permissions
export const SETTINGS_PERMISSIONS = {
  READ: createPermission(PermissionResource.SETTINGS, PermissionAction.READ),
  UPDATE: createPermission(PermissionResource.SETTINGS, PermissionAction.UPDATE),
  MANAGE: createPermission(PermissionResource.SETTINGS, PermissionAction.MANAGE),
} as const;

// AI Features Permissions
export const AI_PERMISSIONS = {
  READ: createPermission(PermissionResource.AI_FEATURES, PermissionAction.READ),
  CREATE: createPermission(PermissionResource.AI_FEATURES, PermissionAction.CREATE),
  MANAGE: createPermission(PermissionResource.AI_FEATURES, PermissionAction.MANAGE),
} as const;

// Reports Permissions
export const REPORT_PERMISSIONS = {
  READ: createPermission(PermissionResource.REPORTS, PermissionAction.READ),
  CREATE: createPermission(PermissionResource.REPORTS, PermissionAction.CREATE),
  EXPORT: createPermission(PermissionResource.REPORTS, PermissionAction.EXPORT),
  MANAGE: createPermission(PermissionResource.REPORTS, PermissionAction.MANAGE),
} as const;

// Meeting Permissions
export const MEETING_PERMISSIONS = {
  CREATE: createPermission(PermissionResource.MEETINGS, PermissionAction.CREATE),
  READ: createPermission(PermissionResource.MEETINGS, PermissionAction.READ),
  UPDATE: createPermission(PermissionResource.MEETINGS, PermissionAction.UPDATE),
  DELETE: createPermission(PermissionResource.MEETINGS, PermissionAction.DELETE),
  MANAGE: createPermission(PermissionResource.MEETINGS, PermissionAction.MANAGE),
} as const;

// Document permissions
export const DOCUMENT_PERMISSIONS = {
  CREATE: createPermission(PermissionResource.DOCUMENTS, PermissionAction.CREATE),
  READ: createPermission(PermissionResource.DOCUMENTS, PermissionAction.READ),
  UPDATE: createPermission(PermissionResource.DOCUMENTS, PermissionAction.UPDATE),
  DELETE: createPermission(PermissionResource.DOCUMENTS, PermissionAction.DELETE),
  MANAGE: createPermission(PermissionResource.DOCUMENTS, PermissionAction.MANAGE),
} as const;

// Dashboard permissions
export const DASHBOARD_PERMISSIONS = {
  READ: createPermission(PermissionResource.DASHBOARD, PermissionAction.READ),
  MANAGE: createPermission(PermissionResource.DASHBOARD, PermissionAction.MANAGE),
} as const;

// Unified permissions object for easy access
export const PERMISSIONS = {
  CONTACTS: CONTACT_PERMISSIONS,
  COMPANIES: COMPANY_PERMISSIONS,
  LEADS: LEAD_PERMISSIONS,
  OPPORTUNITIES: OPPORTUNITY_PERMISSIONS,
  PROPOSALS: PROPOSAL_PERMISSIONS,
  PROJECTS: PROJECT_PERMISSIONS,
  HANDOVERS: HANDOVER_PERMISSIONS,
  USERS: USER_PERMISSIONS,
  ROLES: ROLE_PERMISSIONS,
  SETTINGS: SETTINGS_PERMISSIONS,
  AI_FEATURES: AI_PERMISSIONS,
  REPORTS: REPORT_PERMISSIONS,
  MEETINGS: MEETING_PERMISSIONS,
  DOCUMENTS: DOCUMENT_PERMISSIONS,
  DASHBOARD: DASHBOARD_PERMISSIONS,
} as const;

// Helper function to get all permissions for a resource
export const getResourcePermissions = (resource: keyof typeof PERMISSIONS): readonly string[] => {
  return Object.values(PERMISSIONS[resource]);
};

// Helper function to get all permissions as a flat array
export const getAllPermissions = (): string[] => {
  return Object.values(PERMISSIONS).flatMap(resourcePerms => Object.values(resourcePerms));
};

// Helper function to check if a permission exists
export const isValidPermission = (permission: string): boolean => {
  return getAllPermissions().includes(permission);
};

// Helper function to parse permission string
export const parsePermission = (permission: string): { resource: string; action: string } | null => {
  const parts = permission.split('.');
  if (parts.length !== 2) return null;
  
  return {
    resource: parts[0],
    action: parts[1].toUpperCase()
  };
};

// Type definitions for better TypeScript support
export type ContactPermission = typeof CONTACT_PERMISSIONS[keyof typeof CONTACT_PERMISSIONS];
export type CompanyPermission = typeof COMPANY_PERMISSIONS[keyof typeof COMPANY_PERMISSIONS];
export type LeadPermission = typeof LEAD_PERMISSIONS[keyof typeof LEAD_PERMISSIONS];
export type OpportunityPermission = typeof OPPORTUNITY_PERMISSIONS[keyof typeof OPPORTUNITY_PERMISSIONS];
export type ProposalPermission = typeof PROPOSAL_PERMISSIONS[keyof typeof PROPOSAL_PERMISSIONS];
export type ProjectPermission = typeof PROJECT_PERMISSIONS[keyof typeof PROJECT_PERMISSIONS];
export type HandoverPermission = typeof HANDOVER_PERMISSIONS[keyof typeof HANDOVER_PERMISSIONS];
export type UserPermission = typeof USER_PERMISSIONS[keyof typeof USER_PERMISSIONS];
export type RolePermission = typeof ROLE_PERMISSIONS[keyof typeof ROLE_PERMISSIONS];
export type SettingsPermission = typeof SETTINGS_PERMISSIONS[keyof typeof SETTINGS_PERMISSIONS];
export type AiPermission = typeof AI_PERMISSIONS[keyof typeof AI_PERMISSIONS];
export type ReportPermission = typeof REPORT_PERMISSIONS[keyof typeof REPORT_PERMISSIONS];
export type MeetingPermission = typeof MEETING_PERMISSIONS[keyof typeof MEETING_PERMISSIONS];
export type DocumentPermission = typeof DOCUMENT_PERMISSIONS[keyof typeof DOCUMENT_PERMISSIONS];
export type DashboardPermission = typeof DASHBOARD_PERMISSIONS[keyof typeof DASHBOARD_PERMISSIONS];

export type AnyPermission =
  | ContactPermission
  | CompanyPermission
  | LeadPermission
  | OpportunityPermission
  | ProposalPermission
  | ProjectPermission
  | HandoverPermission
  | UserPermission
  | RolePermission
  | SettingsPermission
  | AiPermission
  | ReportPermission
  | MeetingPermission
  | DocumentPermission
  | DashboardPermission;

// Export individual permission arrays for convenience
export const ALL_CONTACT_PERMISSIONS = Object.values(CONTACT_PERMISSIONS);
export const ALL_COMPANY_PERMISSIONS = Object.values(COMPANY_PERMISSIONS);
export const ALL_LEAD_PERMISSIONS = Object.values(LEAD_PERMISSIONS);
export const ALL_OPPORTUNITY_PERMISSIONS = Object.values(OPPORTUNITY_PERMISSIONS);
export const ALL_PROPOSAL_PERMISSIONS = Object.values(PROPOSAL_PERMISSIONS);
export const ALL_PROJECT_PERMISSIONS = Object.values(PROJECT_PERMISSIONS);
export const ALL_HANDOVER_PERMISSIONS = Object.values(HANDOVER_PERMISSIONS);
export const ALL_USER_PERMISSIONS = Object.values(USER_PERMISSIONS);
export const ALL_ROLE_PERMISSIONS = Object.values(ROLE_PERMISSIONS);
export const ALL_SETTINGS_PERMISSIONS = Object.values(SETTINGS_PERMISSIONS);
export const ALL_AI_PERMISSIONS = Object.values(AI_PERMISSIONS);
export const ALL_REPORT_PERMISSIONS = Object.values(REPORT_PERMISSIONS);
export const ALL_MEETING_PERMISSIONS = Object.values(MEETING_PERMISSIONS);
export const ALL_DOCUMENT_PERMISSIONS = Object.values(DOCUMENT_PERMISSIONS);
export const ALL_DASHBOARD_PERMISSIONS = Object.values(DASHBOARD_PERMISSIONS);

// Default permissions for different role types
export const DEFAULT_ROLE_PERMISSIONS = {
  TENANT_ADMIN: getAllPermissions(),
  MANAGER: [
    ...ALL_CONTACT_PERMISSIONS,
    ...ALL_COMPANY_PERMISSIONS,
    ...ALL_LEAD_PERMISSIONS,
    ...ALL_OPPORTUNITY_PERMISSIONS,
    ...ALL_PROPOSAL_PERMISSIONS,
    ...ALL_PROJECT_PERMISSIONS,
    ...ALL_HANDOVER_PERMISSIONS,
    USER_PERMISSIONS.READ,
    ROLE_PERMISSIONS.READ,
    SETTINGS_PERMISSIONS.READ,
    ...ALL_AI_PERMISSIONS,
    ...ALL_REPORT_PERMISSIONS,
    ...ALL_MEETING_PERMISSIONS,
    ...ALL_DOCUMENT_PERMISSIONS,
    ...ALL_DASHBOARD_PERMISSIONS,
  ],
  SALES_REP: [
    CONTACT_PERMISSIONS.CREATE,
    CONTACT_PERMISSIONS.READ,
    CONTACT_PERMISSIONS.UPDATE,
    CONTACT_PERMISSIONS.EXPORT,
    COMPANY_PERMISSIONS.CREATE,
    COMPANY_PERMISSIONS.READ,
    COMPANY_PERMISSIONS.UPDATE,
    COMPANY_PERMISSIONS.EXPORT,
    LEAD_PERMISSIONS.CREATE,
    LEAD_PERMISSIONS.READ,
    LEAD_PERMISSIONS.UPDATE,
    LEAD_PERMISSIONS.EXPORT,
    OPPORTUNITY_PERMISSIONS.CREATE,
    OPPORTUNITY_PERMISSIONS.READ,
    OPPORTUNITY_PERMISSIONS.UPDATE,
    OPPORTUNITY_PERMISSIONS.EXPORT,
    AI_PERMISSIONS.READ,
    REPORT_PERMISSIONS.READ,
    MEETING_PERMISSIONS.CREATE,
    MEETING_PERMISSIONS.READ,
    MEETING_PERMISSIONS.UPDATE,
    DASHBOARD_PERMISSIONS.READ,
  ],
  VIEWER: [
    CONTACT_PERMISSIONS.READ,
    COMPANY_PERMISSIONS.READ,
    LEAD_PERMISSIONS.READ,
    OPPORTUNITY_PERMISSIONS.READ,
    PROPOSAL_PERMISSIONS.READ,
    PROJECT_PERMISSIONS.READ,
    HANDOVER_PERMISSIONS.READ,
    REPORT_PERMISSIONS.READ,
    DASHBOARD_PERMISSIONS.READ,
  ],
} as const;
