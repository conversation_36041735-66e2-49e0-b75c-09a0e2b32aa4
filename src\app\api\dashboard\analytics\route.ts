import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { TenantDashboardAnalyticsService } from '@/services/tenant-dashboard-analytics';
import { DatabasePerformanceMonitor } from '@/lib/prisma';
import { withPermission, AuthenticatedRequest } from '@/lib/permission-middleware';
import { PermissionResource, PermissionAction } from '@/types';

const dashboardAnalyticsService = new TenantDashboardAnalyticsService();

/**
 * @swagger
 * /api/dashboard/analytics:
 *   get:
 *     summary: Get tenant dashboard analytics
 *     description: Retrieve comprehensive analytics and metrics for the tenant dashboard
 *     tags:
 *       - Dashboard
 *       - Analytics
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: type
 *         schema:
 *           type: string
 *           enum: [full, quick, trends]
 *           default: full
 *         description: Type of analytics to retrieve
 *       - in: query
 *         name: days
 *         schema:
 *           type: integer
 *           default: 30
 *         description: Number of days for trend data
 *     responses:
 *       200:
 *         description: Dashboard analytics retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     totalContacts:
 *                       type: number
 *                     totalLeads:
 *                       type: number
 *                     totalOpportunities:
 *                       type: number
 *                     totalCompanies:
 *                       type: number
 *                     contactsGrowth:
 *                       type: number
 *                     leadsGrowth:
 *                       type: number
 *                     opportunitiesGrowth:
 *                       type: number
 *                     companiesGrowth:
 *                       type: number
 *                     leadConversionRate:
 *                       type: number
 *                     opportunityWinRate:
 *                       type: number
 *                     averageDealSize:
 *                       type: number
 *                     totalRevenue:
 *                       type: number
 *                     recentActivities:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: string
 *                           type:
 *                             type: string
 *                           action:
 *                             type: string
 *                           description:
 *                             type: string
 *                           timestamp:
 *                             type: string
 *                             format: date-time
 *                     usage:
 *                       type: object
 *                       properties:
 *                         contacts:
 *                           type: number
 *                         leads:
 *                           type: number
 *                         users:
 *                           type: number
 *                         ai_requests:
 *                           type: number
 *                         storage_mb:
 *                           type: number
 *                     trends:
 *                       type: object
 *                       properties:
 *                         leads:
 *                           type: array
 *                           items:
 *                             type: object
 *                             properties:
 *                               date:
 *                                 type: string
 *                               count:
 *                                 type: number
 *                         opportunities:
 *                           type: array
 *                           items:
 *                             type: object
 *                             properties:
 *                               date:
 *                                 type: string
 *                               count:
 *                                 type: number
 *                               value:
 *                                 type: number
 *                         contacts:
 *                           type: array
 *                           items:
 *                             type: object
 *                             properties:
 *                               date:
 *                                 type: string
 *                               count:
 *                                 type: number
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden
 *       500:
 *         description: Internal server error
 */
export const GET = withPermission({
  resource: PermissionResource.DASHBOARD,
  action: PermissionAction.READ,
  allowSuperAdmin: false // Super admin should use their own dashboard
})(async (req: AuthenticatedRequest) => {
  try {
    const { searchParams } = new URL(req.url);
    const type = searchParams.get('type') || 'full';
    const days = parseInt(searchParams.get('days') || '30');

    // Validate days parameter
    if (days < 1 || days > 365) {
      return NextResponse.json(
        { error: 'Days parameter must be between 1 and 365' },
        { status: 400 }
      );
    }

    let data;

    switch (type) {
      case 'quick':
        data = await DatabasePerformanceMonitor.trackQuery(
          'dashboard_quick_stats',
          () => dashboardAnalyticsService.getTenantQuickStats(req.tenantId),
          { tenantId: req.tenantId, type: 'quick' }
        );
        break;
      case 'trends':
        const fullData = await DatabasePerformanceMonitor.trackQuery(
          'dashboard_trends',
          () => dashboardAnalyticsService.getTenantDashboardMetrics(req.tenantId),
          { tenantId: req.tenantId, type: 'trends' }
        );
        data = { trends: fullData.trends };
        break;
      case 'full':
      default:
        data = await DatabasePerformanceMonitor.trackQuery(
          'dashboard_full_metrics',
          () => dashboardAnalyticsService.getTenantDashboardMetrics(req.tenantId),
          { tenantId: req.tenantId, type: 'full' }
        );
        break;
    }

    return NextResponse.json({
      success: true,
      data,
      message: 'Dashboard analytics retrieved successfully'
    });

  } catch (error) {
    console.error('Dashboard analytics error:', error);
    
    if (error instanceof Error) {
      return NextResponse.json(
        { 
          success: false,
          error: error.message,
          details: 'Failed to fetch dashboard analytics'
        },
        { status: 500 }
      );
    }
    
    return NextResponse.json(
      { 
        success: false,
        error: 'Internal server error',
        details: 'An unexpected error occurred while fetching dashboard analytics'
      },
      { status: 500 }
    );
  }
});

/**
 * Alternative endpoint for quick stats only
 */
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user's current tenant
    const currentTenant = session.user.tenants?.[0];
    if (!currentTenant) {
      return NextResponse.json({ error: 'No tenant access' }, { status: 403 });
    }

    const body = await request.json();
    const { metrics } = body;

    if (!metrics || !Array.isArray(metrics)) {
      return NextResponse.json(
        { error: 'Invalid request body. Expected metrics array.' },
        { status: 400 }
      );
    }

    // Allow requesting specific metrics
    const availableMetrics = [
      'totalContacts',
      'totalLeads', 
      'totalOpportunities',
      'totalCompanies',
      'totalRevenue',
      'leadConversionRate',
      'opportunityWinRate'
    ];

    const requestedMetrics = metrics.filter(metric => availableMetrics.includes(metric));
    
    if (requestedMetrics.length === 0) {
      return NextResponse.json(
        { error: 'No valid metrics requested' },
        { status: 400 }
      );
    }

    // Get full data and filter to requested metrics
    const fullData = await dashboardAnalyticsService.getTenantDashboardMetrics(currentTenant.id);
    
    const filteredData = requestedMetrics.reduce((acc, metric) => {
      if (metric in fullData) {
        acc[metric] = fullData[metric as keyof typeof fullData];
      }
      return acc;
    }, {} as any);

    return NextResponse.json({
      success: true,
      data: filteredData,
      message: 'Specific metrics retrieved successfully'
    });

  } catch (error) {
    console.error('Dashboard specific metrics error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
