'use client';

import { useState } from 'react';
import { TenantSectionCards } from "@/components/dashboard/tenant-section-cards";
import { TenantAnalyticsChart } from "@/components/dashboard/tenant-analytics-chart";
import { TenantActivityTable } from "@/components/dashboard/tenant-activity-table";
import { MeetingsOverviewCard } from "@/components/dashboard/meetings-overview-card";
import { MeetingDetailsDialog } from "@/components/meetings/meeting-details-dialog";
import { SiteHeader } from "@/components/site-header";
import { ScheduledMeetingWithRelations } from "@/services/scheduled-meeting-service";
import { CommunicationWidget } from "@/components/communication";
import { PermissionGate } from "@/components/ui/permission-gate";
import { PermissionResource, PermissionAction } from "@/types";
import { usePermissions } from "@/components/providers/permission-provider";

export default function DashboardPage() {
  const [selectedMeeting, setSelectedMeeting] = useState<ScheduledMeetingWithRelations | null>(null);
  const [meetingDetailsOpen, setMeetingDetailsOpen] = useState(false);
  const { canAccessResource } = usePermissions();

  const handleCardClick = (cardType: string) => {
    // Handle navigation based on card type
    console.log(`Clicked on ${cardType} card`);
    // TODO: Implement navigation to specific sections
  };

  const handleMeetingClick = (meeting: ScheduledMeetingWithRelations) => {
    setSelectedMeeting(meeting);
    setMeetingDetailsOpen(true);
  };

  // Check permissions for dashboard components
  const canViewAnalytics = canAccessResource(PermissionResource.REPORTS, PermissionAction.READ);
  const canViewMeetings = canAccessResource(PermissionResource.MEETINGS, PermissionAction.READ);
  const canViewCommunication = canAccessResource(PermissionResource.COMMUNICATION, PermissionAction.READ);
  const canViewActivity = canAccessResource(PermissionResource.DASHBOARD, PermissionAction.READ);

  // Count visible sidebar components
  const visibleSidebarComponents = [canViewMeetings, canViewCommunication, canViewActivity].filter(Boolean).length;

  // Determine grid layout based on visible components
  const getGridLayout = () => {
    if (!canViewAnalytics && visibleSidebarComponents === 0) {
      return "hidden"; // No components visible
    }
    if (!canViewAnalytics) {
      return "grid gap-4 md:gap-8"; // Only sidebar components
    }
    if (visibleSidebarComponents === 0) {
      return "grid gap-4 md:gap-8"; // Only analytics chart
    }
    return "grid gap-4 md:gap-8 lg:grid-cols-2 xl:grid-cols-3"; // Both analytics and sidebar
  };

  return (
    <>
      <SiteHeader />
      <div className="flex flex-1 flex-col">
        <div className="@container/main flex flex-1 flex-col gap-2">
          <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
            <div className="px-4 lg:px-6">
              <div className="flex items-center justify-between space-y-2 mb-6">
                <h2 className="text-3xl font-bold tracking-tight">Dashboard</h2>
                <div className="flex items-center space-x-2">
                  <p className="text-sm text-muted-foreground">
                    Real-time data from your CRM
                  </p>
                </div>
              </div>
            </div>

            <TenantSectionCards onCardClick={handleCardClick} />

            <div className="px-4 lg:px-6">
              <div className={getGridLayout()}>
                {canViewAnalytics && (
                  <TenantAnalyticsChart className={visibleSidebarComponents > 0 ? "xl:col-span-2" : ""} />
                )}
                {visibleSidebarComponents > 0 && (
                  <div className="xl:col-span-1 space-y-4">
                    {canViewMeetings && (
                      <MeetingsOverviewCard onMeetingClick={handleMeetingClick} />
                    )}
                    {canViewCommunication && (
                      <CommunicationWidget />
                    )}
                    {canViewActivity && (
                      <TenantActivityTable />
                    )}
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Meeting Details Dialog */}
      <MeetingDetailsDialog
        open={meetingDetailsOpen}
        onOpenChange={(open) => {
          setMeetingDetailsOpen(open);
          if (!open) {
            setSelectedMeeting(null);
          }
        }}
        meeting={selectedMeeting}
      />
    </>
  );
}
