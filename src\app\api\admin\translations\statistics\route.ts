import { NextResponse } from 'next/server';
import { withTenantAdmin } from '@/lib/permission-middleware';
import { TranslationService } from '@/services/translation-service';
import { AuthenticatedRequest } from '@/lib/permission-middleware';

async function getHandler(req: AuthenticatedRequest) {
  try {
    const statistics = await TranslationService.getStatistics();
    return NextResponse.json(statistics);
  } catch (error) {
    return NextResponse.json(
      { error: 'Failed to get translation statistics' },
      { status: 500 }
    );
  }
}

export const GET = withTenantAdmin(getHandler);
