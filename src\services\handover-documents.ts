import { prisma } from '@/lib/prisma';
import { z } from 'zod';

// Validation schemas
export const linkDocumentToHandoverSchema = z.object({
  documentId: z.string().min(1, 'Document ID is required'),
  category: z.enum(['requirements', 'contracts', 'technical', 'legal', 'financial']).default('requirements'),
  isRequired: z.boolean().default(false),
  isClientVisible: z.boolean().default(false),
  accessLevel: z.enum(['internal', 'delivery_team', 'client']).default('internal'),
  orderIndex: z.number().int().min(0).default(0),
  notes: z.string().optional(),
});

export const updateHandoverDocumentSchema = z.object({
  category: z.enum(['requirements', 'contracts', 'technical', 'legal', 'financial']).optional(),
  isRequired: z.boolean().optional(),
  isApproved: z.boolean().optional(),
  isClientVisible: z.boolean().optional(),
  accessLevel: z.enum(['internal', 'delivery_team', 'client']).optional(),
  orderIndex: z.number().int().min(0).optional(),
  notes: z.string().optional(),
});

export const bulkUpdateDocumentsSchema = z.object({
  documentIds: z.array(z.string()).min(1, 'At least one document ID is required'),
  updates: z.object({
    category: z.enum(['requirements', 'contracts', 'technical', 'legal', 'financial']).optional(),
    isRequired: z.boolean().optional(),
    isApproved: z.boolean().optional(),
    isClientVisible: z.boolean().optional(),
    accessLevel: z.enum(['internal', 'delivery_team', 'client']).optional(),
  }),
});

// Types
export type LinkDocumentToHandoverData = z.infer<typeof linkDocumentToHandoverSchema>;
export type UpdateHandoverDocumentData = z.infer<typeof updateHandoverDocumentSchema>;
export type BulkUpdateDocumentsData = z.infer<typeof bulkUpdateDocumentsSchema>;

export interface HandoverDocumentWithDetails {
  id: string;
  tenantId: string;
  handoverId: string;
  documentId: string;
  category: string;
  isRequired: boolean;
  isApproved: boolean;
  approvedAt?: Date;
  approvedById?: string;
  isClientVisible: boolean;
  accessLevel: string;
  orderIndex: number;
  notes?: string;
  createdAt: Date;
  updatedAt: Date;
  
  // Relations
  document: {
    id: string;
    name: string;
    filePath: string;
    mimeType: string;
    fileSize: number;
    description?: string;
    tags: string[];
    createdAt: Date;
    uploadedBy?: {
      id: string;
      firstName?: string;
      lastName?: string;
      email: string;
    };
  };
  approvedBy?: {
    id: string;
    firstName?: string;
    lastName?: string;
    email: string;
  };
}

export class HandoverDocumentService {
  private includeOptions = {
    document: {
      select: {
        id: true,
        name: true,
        filePath: true,
        mimeType: true,
        fileSize: true,
        description: true,
        tags: true,
        createdAt: true,
        uploadedBy: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          }
        }
      }
    },
    approvedBy: {
      select: {
        id: true,
        firstName: true,
        lastName: true,
        email: true,
      }
    }
  };

  /**
   * Link an existing document to a handover
   */
  async linkDocumentToHandover(
    tenantId: string,
    handoverId: string,
    linkData: LinkDocumentToHandoverData
  ): Promise<HandoverDocumentWithDetails> {
    const validatedData = linkDocumentToHandoverSchema.parse(linkData);

    return await prisma.$transaction(async (tx) => {
      // Verify handover exists and belongs to tenant
      const handover = await tx.projectHandover.findUnique({
        where: {
          id: handoverId,
          tenantId
        }
      });

      if (!handover) {
        throw new Error('Handover not found or access denied');
      }

      // Verify document exists and belongs to tenant
      const document = await tx.document.findUnique({
        where: {
          id: validatedData.documentId,
          tenantId
        }
      });

      if (!document) {
        throw new Error('Document not found or access denied');
      }

      // Check if document is already linked to this handover
      const existingLink = await tx.handoverDocument.findFirst({
        where: {
          handoverId,
          documentId: validatedData.documentId,
          tenantId
        }
      });

      if (existingLink) {
        throw new Error('Document is already linked to this handover');
      }

      // Create the handover document link
      const handoverDocument = await tx.handoverDocument.create({
        data: {
          tenantId,
          handoverId,
          documentId: validatedData.documentId,
          category: validatedData.category,
          isRequired: validatedData.isRequired,
          isClientVisible: validatedData.isClientVisible,
          accessLevel: validatedData.accessLevel,
          orderIndex: validatedData.orderIndex,
          notes: validatedData.notes,
        },
        include: this.includeOptions,
      });

      // Update handover documents count
      await tx.projectHandover.update({
        where: {
          id: handoverId,
          tenantId
        },
        data: {
          documentsCount: {
            increment: 1
          }
        }
      });

      return handoverDocument as HandoverDocumentWithDetails;
    });
  }

  /**
   * Get documents for a handover
   */
  async getHandoverDocuments(
    tenantId: string,
    handoverId: string,
    options: {
      category?: string;
      isRequired?: boolean;
      isApproved?: boolean;
      accessLevel?: string;
      isClientVisible?: boolean;
    } = {}
  ): Promise<HandoverDocumentWithDetails[]> {
    const {
      category,
      isRequired,
      isApproved,
      accessLevel,
      isClientVisible
    } = options;

    const where: any = {
      tenantId,
      handoverId,
      ...(category && { category }),
      ...(typeof isRequired === 'boolean' && { isRequired }),
      ...(typeof isApproved === 'boolean' && { isApproved }),
      ...(accessLevel && { accessLevel }),
      ...(typeof isClientVisible === 'boolean' && { isClientVisible }),
    };

    const documents = await prisma.handoverDocument.findMany({
      where,
      include: this.includeOptions,
      orderBy: [
        { category: 'asc' },
        { orderIndex: 'asc' },
        { createdAt: 'asc' }
      ]
    });

    return documents as HandoverDocumentWithDetails[];
  }

  /**
   * Update a handover document
   */
  async updateHandoverDocument(
    tenantId: string,
    handoverDocumentId: string,
    updateData: UpdateHandoverDocumentData,
    approvedBy?: string
  ): Promise<HandoverDocumentWithDetails> {
    const validatedData = updateHandoverDocumentSchema.parse(updateData);

    return await prisma.$transaction(async (tx) => {
      // Verify handover document exists and belongs to tenant
      const existingDoc = await tx.handoverDocument.findUnique({
        where: {
          id: handoverDocumentId,
          tenantId
        }
      });

      if (!existingDoc) {
        throw new Error('Handover document not found or access denied');
      }

      // Handle approval status change
      const updateFields: any = { ...validatedData };
      
      if (validatedData.isApproved === true && !existingDoc.isApproved) {
        updateFields.approvedAt = new Date();
        updateFields.approvedById = approvedBy;
      } else if (validatedData.isApproved === false && existingDoc.isApproved) {
        updateFields.approvedAt = null;
        updateFields.approvedById = null;
      }

      const handoverDocument = await tx.handoverDocument.update({
        where: {
          id: handoverDocumentId,
          tenantId
        },
        data: updateFields,
        include: this.includeOptions,
      });

      return handoverDocument as HandoverDocumentWithDetails;
    });
  }

  /**
   * Remove a document from a handover
   */
  async removeDocumentFromHandover(
    tenantId: string,
    handoverDocumentId: string
  ): Promise<void> {
    return await prisma.$transaction(async (tx) => {
      // Verify handover document exists and belongs to tenant
      const handoverDocument = await tx.handoverDocument.findUnique({
        where: {
          id: handoverDocumentId,
          tenantId
        }
      });

      if (!handoverDocument) {
        throw new Error('Handover document not found or access denied');
      }

      // Remove the handover document link
      await tx.handoverDocument.delete({
        where: {
          id: handoverDocumentId,
          tenantId
        }
      });

      // Update handover documents count
      await tx.projectHandover.update({
        where: {
          id: handoverDocument.handoverId,
          tenantId
        },
        data: {
          documentsCount: {
            decrement: 1
          }
        }
      });
    });
  }

  /**
   * Bulk update multiple handover documents
   */
  async bulkUpdateDocuments(
    tenantId: string,
    handoverId: string,
    bulkData: BulkUpdateDocumentsData,
    approvedBy?: string
  ): Promise<HandoverDocumentWithDetails[]> {
    const validatedData = bulkUpdateDocumentsSchema.parse(bulkData);

    return await prisma.$transaction(async (tx) => {
      // Verify all documents belong to the handover and tenant
      const existingDocs = await tx.handoverDocument.findMany({
        where: {
          id: { in: validatedData.documentIds },
          handoverId,
          tenantId
        }
      });

      if (existingDocs.length !== validatedData.documentIds.length) {
        throw new Error('Some documents not found or access denied');
      }

      // Prepare update fields
      const updateFields: any = { ...validatedData.updates };
      
      if (validatedData.updates.isApproved === true) {
        updateFields.approvedAt = new Date();
        updateFields.approvedById = approvedBy;
      } else if (validatedData.updates.isApproved === false) {
        updateFields.approvedAt = null;
        updateFields.approvedById = null;
      }

      // Update all documents
      await tx.handoverDocument.updateMany({
        where: {
          id: { in: validatedData.documentIds },
          handoverId,
          tenantId
        },
        data: updateFields
      });

      // Return updated documents
      const updatedDocuments = await tx.handoverDocument.findMany({
        where: {
          id: { in: validatedData.documentIds },
          handoverId,
          tenantId
        },
        include: this.includeOptions,
        orderBy: [
          { category: 'asc' },
          { orderIndex: 'asc' },
          { createdAt: 'asc' }
        ]
      });

      return updatedDocuments as HandoverDocumentWithDetails[];
    });
  }

  /**
   * Get document statistics for a handover
   */
  async getDocumentStats(
    tenantId: string,
    handoverId: string
  ): Promise<{
    total: number;
    byCategory: Record<string, number>;
    required: number;
    approved: number;
    clientVisible: number;
  }> {
    const documents = await prisma.handoverDocument.findMany({
      where: {
        tenantId,
        handoverId
      },
      select: {
        category: true,
        isRequired: true,
        isApproved: true,
        isClientVisible: true,
      }
    });

    const stats = {
      total: documents.length,
      byCategory: {} as Record<string, number>,
      required: 0,
      approved: 0,
      clientVisible: 0,
    };

    documents.forEach(doc => {
      // Count by category
      stats.byCategory[doc.category] = (stats.byCategory[doc.category] || 0) + 1;
      
      // Count required, approved, and client visible
      if (doc.isRequired) stats.required++;
      if (doc.isApproved) stats.approved++;
      if (doc.isClientVisible) stats.clientVisible++;
    });

    return stats;
  }
}

// Export singleton instance
export const handoverDocumentService = new HandoverDocumentService();
