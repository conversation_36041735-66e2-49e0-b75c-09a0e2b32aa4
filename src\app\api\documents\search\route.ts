import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { DocumentManagementService } from '@/services/document-management';
import { withPermission, AuthenticatedRequest } from '@/lib/permission-middleware';
import { PermissionResource, PermissionAction } from '@/types';

const documentService = new DocumentManagementService();

const searchQuerySchema = z.object({
  q: z.string().optional().default(''),
  limit: z.coerce.number().int().min(1).max(50).default(20),
  relatedToType: z.string().optional(),
  relatedToId: z.string().optional(),
  excludeRelatedToType: z.string().optional(),
  excludeRelatedToId: z.string().optional(),
  mimeType: z.string().optional(),
  tags: z.string().optional(),
  includeProposalDocs: z.coerce.boolean().default(false),
});

/**
 * GET /api/documents/search
 * Search documents by name, description, and tags
 */
export const GET = withPermission({
  resource: PermissionResource.DOCUMENTS,
  action: PermissionAction.READ,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest) => {
  try {
    const { searchParams } = new URL(req.url);
    const queryParams = Object.fromEntries(searchParams.entries());
    
    const validatedQuery = searchQuerySchema.parse(queryParams);
    
    // Build search filters
    const filters: any = {
      limit: validatedQuery.limit,
      page: 1,
    };

    // Only add search filter if query is not empty
    if (validatedQuery.q && validatedQuery.q.trim()) {
      filters.search = validatedQuery.q.trim();
    }

    // Add optional filters
    if (validatedQuery.relatedToType) {
      filters.relatedToType = validatedQuery.relatedToType;
    }
    if (validatedQuery.relatedToId) {
      filters.relatedToId = validatedQuery.relatedToId;
    }
    if (validatedQuery.mimeType) {
      filters.mimeType = validatedQuery.mimeType;
    }
    if (validatedQuery.tags) {
      filters.tags = validatedQuery.tags.split(',');
    }

    // Get documents using the existing service
    const result = await documentService.getDocuments(req.tenantId, filters);
    
    let documents = result.documents;

    // Filter out documents that are already linked to the specified entity
    if (validatedQuery.excludeRelatedToType && validatedQuery.excludeRelatedToId) {
      documents = documents.filter(doc => 
        !(doc.relatedToType === validatedQuery.excludeRelatedToType && 
          doc.relatedToId === validatedQuery.excludeRelatedToId)
      );
    }

    // Include proposal documents if requested
    if (validatedQuery.includeProposalDocs && validatedQuery.relatedToType === 'opportunity') {
      try {
        // Get proposal documents for the opportunity
        const proposalFilters: any = {
          relatedToType: 'proposal',
          limit: 10,
          page: 1,
        };

        // Only add search filter if query is not empty
        if (validatedQuery.q && validatedQuery.q.trim()) {
          proposalFilters.search = validatedQuery.q.trim();
        }
        
        const proposalResult = await documentService.getDocuments(req.tenantId, proposalFilters);
        
        // Filter proposal documents that are related to the opportunity
        const opportunityProposalDocs = proposalResult.documents.filter(doc => {
          // This would need to be enhanced to properly link proposals to opportunities
          // For now, we'll include all proposal documents in search
          return true;
        });
        
        documents = [...documents, ...opportunityProposalDocs];
      } catch (error) {
        console.warn('Failed to fetch proposal documents:', error);
      }
    }

    // Remove duplicates based on document ID
    const uniqueDocuments = documents.filter((doc, index, self) => 
      index === self.findIndex(d => d.id === doc.id)
    );

    // Sort by relevance (name matches first, then description matches) or by date if no query
    const sortedDocuments = uniqueDocuments.sort((a, b) => {
      // If no search query, just sort by creation date (newest first)
      if (!validatedQuery.q || !validatedQuery.q.trim()) {
        return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
      }

      const query = validatedQuery.q.toLowerCase();
      const aNameMatch = a.name.toLowerCase().includes(query);
      const bNameMatch = b.name.toLowerCase().includes(query);

      if (aNameMatch && !bNameMatch) return -1;
      if (!aNameMatch && bNameMatch) return 1;

      // If both or neither match name, sort by creation date (newest first)
      return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
    });

    return NextResponse.json({
      success: true,
      data: {
        documents: sortedDocuments.slice(0, validatedQuery.limit),
        query: validatedQuery.q,
        count: sortedDocuments.length,
        hasMore: sortedDocuments.length > validatedQuery.limit,
      },
      message: 'Document search completed successfully'
    });

  } catch (error) {
    console.error('Error searching documents:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Invalid search parameters', 
          details: error.errors 
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to search documents' 
      },
      { status: 500 }
    );
  }
});
