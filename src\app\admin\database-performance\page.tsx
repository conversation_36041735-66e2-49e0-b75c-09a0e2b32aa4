'use client';

import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';
import DatabasePerformanceDashboard from '@/components/admin/DatabasePerformanceDashboard';

export default function DatabasePerformancePage() {
  const { data: session, status } = useSession();
  const router = useRouter();

  useEffect(() => {
    if (status === 'loading') return;

    if (!session) {
      router.push('/auth/signin');
      return;
    }

    if (!session.user.isPlatformAdmin) {
      router.push('/dashboard');
      return;
    }
  }, [session, status, router]);

  if (status === 'loading') {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  if (!session?.user?.isPlatformAdmin) {
    return null;
  }

  return (
    <div className="px-4 sm:px-6 lg:px-8 py-8 space-y-8">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            Database Performance
          </h1>
          <p className="mt-1 text-gray-600 dark:text-gray-400">
            Monitor and optimize database performance for the CRM platform
          </p>
        </div>
      </div>

      {/* Dashboard */}
      <DatabasePerformanceDashboard />
    </div>
  );
}
