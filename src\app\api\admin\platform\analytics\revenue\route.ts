import { NextRequest, NextResponse } from 'next/server';
import { withSuperAdminAuth } from '@/lib/tenant-context';
import { SuperAdminService } from '@/services/super-admin';

/**
 * @swagger
 * /api/admin/platform/analytics/revenue:
 *   get:
 *     summary: Get platform revenue analytics (Super Admin only)
 *     description: Retrieve comprehensive revenue and billing analytics
 *     tags:
 *       - Super Admin - Analytics
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Revenue analytics retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     totalRevenue:
 *                       type: number
 *                     monthlyRecurringRevenue:
 *                       type: number
 *                     averageRevenuePerUser:
 *                       type: number
 *                     revenueGrowthRate:
 *                       type: number
 *                     subscriptionBreakdown:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           planName:
 *                             type: string
 *                           count:
 *                             type: number
 *                           revenue:
 *                             type: number
 *                           percentage:
 *                             type: number
 *                     revenueByMonth:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           month:
 *                             type: string
 *                           revenue:
 *                             type: number
 *                           subscriptions:
 *                             type: number
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Super Admin access required
 */
export const GET = withSuperAdminAuth(async (request: NextRequest) => {
  try {
    const superAdminService = new SuperAdminService();
    const revenueAnalytics = await superAdminService.getRevenueAnalytics();

    return NextResponse.json({
      success: true,
      data: revenueAnalytics
    });

  } catch (error) {
    console.error('Get revenue analytics error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
});
