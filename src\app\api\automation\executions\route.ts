import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { withPermission, AuthenticatedRequest } from '@/lib/permission-middleware';
import { PermissionResource, PermissionAction } from '@/types';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';

// Validation schemas
const querySchema = z.object({
  page: z.string().optional().transform(val => val ? parseInt(val) : 1),
  limit: z.string().optional().transform(val => val ? parseInt(val) : 20),
  entityType: z.enum(['opportunity', 'handover', 'project', 'contact', 'lead']).optional(),
  entityId: z.string().optional(),
  status: z.enum(['pending', 'completed', 'failed']).optional(),
  triggerId: z.string().optional()
});

/**
 * GET /api/automation/executions
 * Get automation executions with filtering and pagination
 */
export const GET = withPermission({
  resource: PermissionResource.SETTINGS,
  action: PermissionAction.READ,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest) => {
  try {
    const { searchParams } = new URL(req.url);
    const query = querySchema.parse(Object.fromEntries(searchParams));

    // Build where clause
    const where: any = { tenantId: req.tenantId };
    
    if (query.entityType) {
      where.entityType = query.entityType;
    }
    
    if (query.entityId) {
      where.entityId = query.entityId;
    }
    
    if (query.status) {
      where.status = query.status;
    }
    
    if (query.triggerId) {
      where.triggerId = query.triggerId;
    }

    // Get executions with pagination
    const [executions, total] = await Promise.all([
      prisma.automationExecution.findMany({
        where,
        orderBy: { startedAt: 'desc' },
        skip: (query.page - 1) * query.limit,
        take: query.limit,
        include: {
          trigger: {
            select: {
              id: true,
              name: true,
              description: true,
              triggerType: true
            }
          }
        }
      }),
      prisma.automationExecution.count({ where })
    ]);

    return NextResponse.json({
      success: true,
      data: {
        executions,
        pagination: {
          page: query.page,
          limit: query.limit,
          total,
          pages: Math.ceil(total / query.limit)
        }
      }
    });
  } catch (error) {
    console.error('Error fetching automation executions:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid query parameters', details: error.errors },
        { status: 400 }
      );
    }

    // Check if it's a database table not found error
    if (error instanceof Error && error.message.includes('automation_executions')) {
      return NextResponse.json(
        {
          error: 'Automation tables not found',
          message: 'Please run database migration to create automation tables',
          details: 'Run: npm run db:push or npm run db:migrate'
        },
        { status: 503 }
      );
    }

    return NextResponse.json(
      {
        error: 'Failed to fetch automation executions',
        details: process.env.NODE_ENV === 'development' ? error.message : undefined
      },
      { status: 500 }
    );
  }
});

/**
 * POST /api/automation/executions/test
 * Create a test execution for demonstration purposes
 */
export async function POST(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user's current tenant
    const currentTenant = session.user.tenants?.[0];
    if (!currentTenant) {
      return NextResponse.json({ error: 'No tenant access' }, { status: 403 });
    }

    const body = await req.json();
    const { entityType, entityId, triggerType = 'opportunity_status_change' } = body;

    if (!entityType || !entityId) {
      return NextResponse.json(
        { error: 'entityType and entityId are required' },
        { status: 400 }
      );
    }

    // Create a test trigger if none exists
    let trigger = await prisma.automationTrigger.findFirst({
      where: {
        tenantId: currentTenant.id,
        triggerType,
        isActive: true
      }
    });

    if (!trigger) {
      trigger = await prisma.automationTrigger.create({
        data: {
          tenantId: currentTenant.id,
          name: 'Test Automation Trigger',
          description: 'Test trigger for demonstration purposes',
          triggerType,
          triggerConditions: { test: true },
          actions: [
            {
              type: 'send_notification',
              config: { message: 'Test automation executed' }
            }
          ],
          isActive: true,
          priority: 1,
          createdById: session.user.id
        }
      });
    }

    // Create test execution
    const execution = await prisma.automationExecution.create({
      data: {
        tenantId: currentTenant.id,
        triggerId: trigger.id,
        entityType,
        entityId,
        triggerData: {
          test: true,
          timestamp: new Date().toISOString(),
          triggeredBy: session.user.id
        },
        status: 'completed',
        executedActions: [
          {
            actionType: 'send_notification',
            status: 'completed',
            result: { message: 'Test notification sent successfully' }
          }
        ],
        completedAt: new Date()
      },
      include: {
        trigger: {
          select: {
            id: true,
            name: true,
            description: true,
            triggerType: true
          }
        }
      }
    });

    return NextResponse.json({
      success: true,
      data: { execution },
      message: 'Test execution created successfully'
    });
  } catch (error) {
    console.error('Error creating test execution:', error);
    return NextResponse.json(
      { error: 'Failed to create test execution' },
      { status: 500 }
    );
  }
}
