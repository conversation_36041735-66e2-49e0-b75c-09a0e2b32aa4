import { NextResponse } from 'next/server';
import { z } from 'zod';
import { ContactManagementService, createContactSchema, ContactFilters } from '@/services/contact-management';
import { withPermission, AuthenticatedRequest } from '@/lib/permission-middleware';
import { PermissionResource, PermissionAction } from '@/types';

const contactService = new ContactManagementService();

// Validation schema for query parameters
const getContactsQuerySchema = z.object({
  search: z.string().optional(),
  companyId: z.string().optional(),
  ownerId: z.string().optional(),
  hasEmail: z.string().transform(val => val === 'true').optional(),
  hasPhone: z.string().transform(val => val === 'true').optional(),
  page: z.string().transform(val => parseInt(val) || 1).optional(),
  limit: z.string().transform(val => Math.min(parseInt(val) || 20, 100)).optional(),
  sortBy: z.enum(['firstName', 'lastName', 'email', 'createdAt', 'updatedAt']).optional(),
  sortOrder: z.enum(['asc', 'desc']).optional(),
});

/**
 * GET /api/contacts
 * Get contacts with filtering and pagination
 */
export const GET = withPermission({
  resource: PermissionResource.CONTACTS,
  action: PermissionAction.READ,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest) => {
  try {
    const { searchParams } = new URL(req.url);
    const queryParams = Object.fromEntries(searchParams.entries());
    
    const validatedQuery = getContactsQuerySchema.parse(queryParams);
    
    const filters: ContactFilters = {
      search: validatedQuery.search,
      companyId: validatedQuery.companyId,
      ownerId: validatedQuery.ownerId,
      hasEmail: validatedQuery.hasEmail,
      hasPhone: validatedQuery.hasPhone,
      page: validatedQuery.page,
      limit: validatedQuery.limit,
      sortBy: validatedQuery.sortBy,
      sortOrder: validatedQuery.sortOrder,
    };

    const result = await contactService.getContacts(req.tenantId, filters);

    return NextResponse.json({
      success: true,
      data: result,
      message: 'Contacts retrieved successfully'
    });

  } catch (error) {
    console.error('Get contacts error:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          error: 'Invalid query parameters',
          details: error.errors.map(e => ({
            field: e.path.join('.'),
            message: e.message
          }))
        },
        { status: 400 }
      );
    }

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
});

/**
 * POST /api/contacts
 * Create a new contact
 */
export const POST = withPermission({
  resource: PermissionResource.CONTACTS,
  action: PermissionAction.CREATE,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest) => {
  try {
    const body = await req.json();
    const validatedData = createContactSchema.parse(body);

    const contact = await contactService.createContact(
      req.tenantId,
      validatedData,
      req.userId
    );

    return NextResponse.json({
      success: true,
      data: { contact },
      message: 'Contact created successfully'
    }, { status: 201 });

  } catch (error) {
    console.error('Create contact error:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          error: 'Validation failed',
          details: error.errors.map(e => ({
            field: e.path.join('.'),
            message: e.message
          }))
        },
        { status: 400 }
      );
    }

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
});

/**
 * PUT /api/contacts
 * Update an existing contact
 */
export const PUT = withPermission({
  resource: PermissionResource.CONTACTS,
  action: PermissionAction.UPDATE,
  requireOwnership: true,
  getResourceOwnerId: async (req) => {
    const body = await req.json();
    return body.contactId;
  },
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest) => {
  try {
    const body = await req.json();
    const { contactId, ...updateData } = body;

    if (!contactId) {
      return NextResponse.json(
        { error: 'Contact ID is required' },
        { status: 400 }
      );
    }

    const contact = await contactService.updateContact(
      contactId,
      req.tenantId,
      updateData,
      req.userId
    );

    return NextResponse.json({
      success: true,
      data: { contact },
      message: 'Contact updated successfully'
    });

  } catch (error) {
    console.error('Update contact error:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          error: 'Validation failed',
          details: error.errors.map(e => ({
            field: e.path.join('.'),
            message: e.message
          }))
        },
        { status: 400 }
      );
    }

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
});

/**
 * DELETE /api/contacts
 * Delete a contact
 */
export const DELETE = withPermission({
  resource: PermissionResource.CONTACTS,
  action: PermissionAction.DELETE,
  requireOwnership: true,
  getResourceOwnerId: async (req) => {
    const { searchParams } = new URL(req.url);
    return searchParams.get('contactId');
  },
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest) => {
  try {
    const { searchParams } = new URL(req.url);
    const contactId = searchParams.get('contactId');

    if (!contactId) {
      return NextResponse.json(
        { error: 'Contact ID is required' },
        { status: 400 }
      );
    }

    await contactService.deleteContact(contactId, req.tenantId);

    return NextResponse.json({
      success: true,
      message: 'Contact deleted successfully'
    });

  } catch (error) {
    console.error('Delete contact error:', error);

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
});
