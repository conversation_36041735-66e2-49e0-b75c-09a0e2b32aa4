'use client';

import React from 'react';
import {
  ArrowTrendingUpIcon,
  ArrowTrendingDownIcon,
  MinusIcon
} from '@heroicons/react/24/outline';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { formatCurrency, formatNumber, formatPercentage } from '@/lib/formatters';

export interface EnhancedMetricCardProps {
  title: string;
  value: string | number;
  change?: number;
  changeLabel?: string;
  icon: React.ComponentType<{ className?: string }>;
  trend?: 'up' | 'down' | 'neutral';
  format?: 'number' | 'currency' | 'percentage';
  loading?: boolean;
  onClick?: () => void;
  subtitle?: string;
  color?: 'default' | 'success' | 'warning' | 'danger';
}

export function EnhancedMetricCard({
  title,
  value,
  change,
  changeLabel,
  icon: Icon,
  trend = 'neutral',
  format = 'number',
  loading = false,
  onClick,
  subtitle,
  color = 'default'
}: EnhancedMetricCardProps) {
  const formatValue = (val: string | number) => {
    if (typeof val === 'string') return val;

    switch (format) {
      case 'currency':
        return formatCurrency(val);
      case 'percentage':
        return formatPercentage(val);
      default:
        return formatNumber(val, 'en-US', 0, 0);
    }
  };

  const getTrendIcon = () => {
    switch (trend) {
      case 'up':
        return <ArrowTrendingUpIcon className="h-4 w-4" />;
      case 'down':
        return <ArrowTrendingDownIcon className="h-4 w-4" />;
      default:
        return <MinusIcon className="h-4 w-4" />;
    }
  };

  const getTrendColor = () => {
    switch (trend) {
      case 'up':
        return 'text-green-600 bg-green-50 dark:bg-green-900/20';
      case 'down':
        return 'text-red-600 bg-red-50 dark:bg-red-900/20';
      default:
        return 'text-gray-600 bg-gray-50 dark:bg-gray-800';
    }
  };

  const getCardColor = () => {
    switch (color) {
      case 'success':
        return 'border-green-200 dark:border-green-800';
      case 'warning':
        return 'border-yellow-200 dark:border-yellow-800';
      case 'danger':
        return 'border-red-200 dark:border-red-800';
      default:
        return 'border-gray-200 dark:border-gray-700';
    }
  };

  const CardComponent = onClick ? Button : 'div';
  const cardProps = onClick ? {
    variant: 'ghost' as const,
    className: cn(
      'h-auto p-0 justify-start hover:bg-muted/50 transition-colors',
      getCardColor()
    ),
    onClick
  } : {
    className: cn('transition-all duration-200 hover:shadow-md', getCardColor())
  };

  return (
    <Card className="bg-white dark:bg-gray-800 rounded-lg shadow-soft">
      <CardComponent {...cardProps}>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium text-muted-foreground">
            {title}
          </CardTitle>
          <Icon className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="space-y-2">
              <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
              <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse w-2/3"></div>
            </div>
          ) : (
            <>
              <div className="text-2xl font-bold text-foreground">
                {formatValue(value)}
              </div>
              
              {subtitle && (
                <p className="text-xs text-muted-foreground mt-1">
                  {subtitle}
                </p>
              )}
              
              {change !== undefined && (
                <div className="flex items-center space-x-1 mt-2">
                  <div className={cn(
                    'flex items-center space-x-1 px-2 py-1 rounded-full text-xs font-medium',
                    getTrendColor()
                  )}>
                    {getTrendIcon()}
                    <span>
                      {change > 0 ? '+' : ''}{change}%
                    </span>
                  </div>
                  {changeLabel && (
                    <span className="text-xs text-muted-foreground">
                      {changeLabel}
                    </span>
                  )}
                </div>
              )}
            </>
          )}
        </CardContent>
      </CardComponent>
    </Card>
  );
}
