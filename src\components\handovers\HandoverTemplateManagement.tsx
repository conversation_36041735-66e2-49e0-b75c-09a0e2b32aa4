'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';
import { PermissionAwareTable, TableColumn, TableAction } from '@/components/ui/permission-aware-table';
import { EmptyState } from '@/components/ui/empty-state';
import { PageLayout } from '@/components/layout/page-layout';
import { PermissionGate } from '@/components/ui/permission-gate';
import { useEnhancedDeleteDialog, deleteConfigurations } from '@/components/ui/enhanced-delete-dialog';
import { 
  Search, 
  Plus, 
  Filter, 
  FileText, 
  Clock, 
  Users,
  Edit,
  Trash2,
  Co<PERSON>,
  Star,
  StarOff
} from 'lucide-react';
import { PermissionResource, PermissionAction } from '@/types';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';

// Types
interface HandoverTemplate {
  id: string;
  name: string;
  description?: string;
  category: string;
  estimatedDuration?: number;
  isDefault: boolean;
  items: any[];
  createdAt: string;
  updatedAt: string;
  createdBy?: {
    id: string;
    firstName?: string;
    lastName?: string;
    email: string;
  };
}

interface HandoverTemplateManagementProps {
  className?: string;
  onCreateTemplate?: () => void;
  onEditTemplate?: (template: HandoverTemplate) => void;
}

const CATEGORIES = [
  { value: 'all', label: 'All Categories' },
  { value: 'general', label: 'General' },
  { value: 'technical', label: 'Technical' },
  { value: 'legal', label: 'Legal' },
  { value: 'financial', label: 'Financial' },
];

export default function HandoverTemplateManagement({
  className,
  onCreateTemplate,
  onEditTemplate
}: HandoverTemplateManagementProps) {
  const router = useRouter();
  const [templates, setTemplates] = useState<HandoverTemplate[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Filters and search
  const [searchQuery, setSearchQuery] = useState('');
  const [categoryFilter, setCategoryFilter] = useState('all');
  
  // Enhanced delete dialog
  const { showDeleteDialog } = useEnhancedDeleteDialog();

  // Fetch templates
  const fetchTemplates = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const params = new URLSearchParams();
      if (searchQuery) params.append('search', searchQuery);
      if (categoryFilter !== 'all') params.append('category', categoryFilter);
      
      const response = await fetch(`/api/handover-templates?${params.toString()}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch templates');
      }
      
      const data = await response.json();
      setTemplates(data.templates || []);
    } catch (err) {
      console.error('Error fetching templates:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch templates');
    } finally {
      setLoading(false);
    }
  };

  // Initial load
  useEffect(() => {
    fetchTemplates();
  }, []);

  // Debounced search
  useEffect(() => {
    const timer = setTimeout(() => {
      fetchTemplates();
    }, 300);
    
    return () => clearTimeout(timer);
  }, [searchQuery, categoryFilter]);

  // Handle create template
  const handleCreateTemplate = () => {
    onCreateTemplate?.();
  };

  // Handle edit template
  const handleEditTemplateInternal = (template: HandoverTemplate) => {
    onEditTemplate?.(template);
  };

  // Handle duplicate template
  const handleDuplicateTemplate = async (template: HandoverTemplate) => {
    try {
      const duplicateData = {
        name: `${template.name} (Copy)`,
        description: template.description,
        category: template.category,
        items: template.items,
        estimatedDuration: template.estimatedDuration,
        isDefault: false, // Duplicates are never default
      };

      const response = await fetch('/api/handover-templates', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(duplicateData),
      });

      if (!response.ok) {
        throw new Error('Failed to duplicate template');
      }

      await fetchTemplates();
    } catch (err) {
      console.error('Error duplicating template:', err);
      setError(err instanceof Error ? err.message : 'Failed to duplicate template');
    }
  };

  // Handle toggle default
  const handleToggleDefault = async (template: HandoverTemplate) => {
    try {
      const response = await fetch(`/api/handover-templates/${template.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          isDefault: !template.isDefault,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to update template');
      }

      await fetchTemplates();
    } catch (err) {
      console.error('Error updating template:', err);
      setError(err instanceof Error ? err.message : 'Failed to update template');
    }
  };

  // Handle delete template
  const handleDeleteTemplate = async (template: HandoverTemplate) => {
    const relatedData = [
      { 
        type: 'items', 
        count: template.items?.length || 0, 
        label: 'checklist items', 
        description: 'checklist items and configurations' 
      }
    ];

    const customWarnings = [
      'All checklist items and configurations will be permanently deleted.',
      'Any handovers using this template will no longer have access to the template structure.',
      'This action cannot be undone and the template cannot be recovered.',
    ];

    if (template.isDefault) {
      customWarnings.unshift('This is the default template. Deleting it may affect new handover creation.');
    }

    showDeleteDialog({
      title: 'Delete Handover Template',
      description: `Are you sure you want to delete the template "${template.name}"?`,
      itemName: template.name,
      itemType: 'handover template',
      relatedData,
      customWarnings,
      onConfirm: async () => {
        try {
          const response = await fetch(`/api/handover-templates/${template.id}`, {
            method: 'DELETE',
          });

          if (!response.ok) {
            throw new Error('Failed to delete template');
          }

          await fetchTemplates();
        } catch (err) {
          console.error('Error deleting template:', err);
          setError(err instanceof Error ? err.message : 'Failed to delete template');
        }
      },
    });
  };



  // Table columns
  const columns: TableColumn<HandoverTemplate>[] = [
    {
      key: 'name',
      label: 'Template Name',
      sortable: true,
      render: (template) => (
        <div className="flex items-center gap-3">
          <FileText className="w-4 h-4 text-muted-foreground" />
          <div>
            <div className="flex items-center gap-2">
              <span className="font-medium">{template.name}</span>
              {template.isDefault && (
                <Badge variant="secondary" className="text-xs">
                  Default
                </Badge>
              )}
            </div>
            {template.description && (
              <p className="text-sm text-muted-foreground mt-1">
                {template.description}
              </p>
            )}
          </div>
        </div>
      ),
    },
    {
      key: 'category',
      label: 'Category',
      sortable: true,
      render: (template) => (
        <div className="flex items-center gap-1">
          <Users className="w-4 h-4 text-muted-foreground" />
          <span className="capitalize">{template.category}</span>
        </div>
      ),
    },
    {
      key: 'items',
      label: 'Items',
      render: (template) => (
        <span className="text-sm">
          {template.items?.length || 0} items
        </span>
      ),
    },
    {
      key: 'estimatedDuration',
      label: 'Duration',
      render: (template) => (
        template.estimatedDuration ? (
          <div className="flex items-center gap-1">
            <Clock className="w-4 h-4 text-muted-foreground" />
            <span className="text-sm">{template.estimatedDuration}h</span>
          </div>
        ) : (
          <span className="text-sm text-muted-foreground">-</span>
        )
      ),
    },
    {
      key: 'createdAt',
      label: 'Created',
      sortable: true,
      render: (template) => (
        <div className="text-sm">
          <div>{format(new Date(template.createdAt), 'MMM d, yyyy')}</div>
          {template.createdBy && (
            <div className="text-muted-foreground">
              by {template.createdBy.firstName} {template.createdBy.lastName}
            </div>
          )}
        </div>
      ),
    },
  ];

  // Table actions
  // TODO: Re-enable permission checks after seeding handover permissions
  const actions: TableAction<HandoverTemplate>[] = [
    {
      label: 'Edit',
      icon: Edit,
      onClick: handleEditTemplateInternal,
    },
    {
      label: 'Duplicate',
      icon: Copy,
      onClick: handleDuplicateTemplate,
    },
    {
      label: 'Toggle Default',
      icon: Star,
      onClick: handleToggleDefault,
    },
    {
      label: 'Delete',
      icon: Trash2,
      onClick: handleDeleteTemplate,
      color: 'red',
    },
  ];

  // Uncomment after seeding permissions:
  /*
  const actions: TableAction<HandoverTemplate>[] = [
    {
      label: 'Edit',
      icon: Edit,
      onClick: handleEditTemplateInternal,
      permission: {
        resource: PermissionResource.HANDOVERS,
        action: PermissionAction.UPDATE,
      },
    },
    {
      label: 'Duplicate',
      icon: Copy,
      onClick: handleDuplicateTemplate,
      permission: {
        resource: PermissionResource.HANDOVERS,
        action: PermissionAction.CREATE,
      },
    },
    {
      label: 'Toggle Default',
      icon: Star,
      onClick: handleToggleDefault,
      permission: {
        resource: PermissionResource.HANDOVERS,
        action: PermissionAction.UPDATE,
      },
    },
    {
      label: 'Delete',
      icon: Trash2,
      onClick: handleDeleteTemplate,
      permission: {
        resource: PermissionResource.HANDOVERS,
        action: PermissionAction.DELETE,
      },
      color: 'red',
    },
  ];
  */



  return (
    <div className={cn('space-y-6', className)}>
      {/* Filters */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                <Input
                  placeholder="Search templates..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            <div className="flex gap-2">
              <Select value={categoryFilter} onValueChange={setCategoryFilter}>
                <SelectTrigger className="w-[180px]">
                  <Filter className="w-4 h-4 mr-2" />
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {CATEGORIES.map((category) => (
                    <SelectItem key={category.value} value={category.value}>
                      {category.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>


            </div>
          </div>
        </CardContent>
      </Card>

      {/* Templates Table */}
      {error && (
        <Card>
          <CardContent className="p-6">
            <div className="text-center text-red-600">
              <p>{error}</p>
              <Button
                variant="outline"
                onClick={fetchTemplates}
                className="mt-2"
              >
                Try Again
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {!error && templates.length === 0 && !loading ? (
        <Card>
          <CardContent>
            <EmptyState
              icon={<FileText className="w-12 h-12" />}
              title="No templates found"
              description="Create your first handover template to standardize your processes"
              action={onCreateTemplate ? {
                label: 'Create Template',
                onClick: handleCreateTemplate,
                variant: 'primary'
              } : undefined}
            />
          </CardContent>
        </Card>
      ) : (
        <Card>
          <PermissionAwareTable
            data={templates}
            columns={columns}
            actions={actions}
            resource={PermissionResource.HANDOVERS}
            loading={loading}
            emptyMessage="No templates found"
          />
        </Card>
      )}

    </div>
  );
}
