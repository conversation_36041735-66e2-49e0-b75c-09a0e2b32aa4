import { NextRequest, NextResponse } from 'next/server';
import { emailLeadCaptureService } from '@/services/email-lead-capture';
import { headers } from 'next/headers';
import { prisma } from '@/lib/prisma';

// Webhook endpoint for email providers (SendGrid, Mailgun, etc.)
export async function POST(request: NextRequest) {
  try {
    const headersList = await headers();
    const signature = headersList.get('x-webhook-signature');
    const provider = headersList.get('x-email-provider') || 'unknown';
    const tenantId = headersList.get('x-tenant-id') || headersList.get('tenant-id');

    // TODO: Verify webhook signature for security
    // This would depend on your email provider's signature verification method

    const body = await request.json();
    console.log(`📧 Received email webhook from ${provider}:`, body);

    // Parse email data based on provider
    const emailData = parseEmailWebhook(body, provider);

    if (!emailData) {
      return NextResponse.json({ error: 'Invalid email data' }, { status: 400 });
    }

    // Use provided tenant ID or try to extract from email
    let finalTenantId = tenantId;
    if (!finalTenantId) {
      finalTenantId = await extractTenantFromEmail(emailData.toEmail);
    }

    if (!finalTenantId) {
      return NextResponse.json({
        error: 'Tenant ID required in headers (x-tenant-id) or unable to determine from email'
      }, { status: 400 });
    }

    // Process the email
    const result = await emailLeadCaptureService.processIncomingEmail(
      finalTenantId,
      {
        fromEmail: emailData.fromEmail,
        fromName: emailData.fromName,
        subject: emailData.subject,
        content: emailData.content,
        receivedAt: emailData.receivedAt || new Date(),
        emailProvider: provider,
        messageId: emailData.messageId
      }
    );

    console.log(`✅ Email processed successfully:`, {
      emailCaptureId: result.emailCapture.id,
      leadCreated: !!result.lead,
      contactCreated: !!result.contact
    });

    return NextResponse.json({
      success: true,
      message: 'Email processed successfully',
      data: {
        emailCaptureId: result.emailCapture.id,
        leadCreated: !!result.lead,
        contactCreated: !!result.contact,
        processed: result.emailCapture.processed
      }
    });
  } catch (error) {
    console.error('Email webhook processing error:', error);
    return NextResponse.json(
      { error: 'Failed to process email webhook' },
      { status: 500 }
    );
  }
}

interface ParsedEmailData {
  fromEmail: string;
  fromName?: string;
  toEmail: string;
  subject: string;
  content: string;
  receivedAt?: Date;
  messageId?: string;
}

function parseEmailWebhook(body: any, provider: string): ParsedEmailData | null {
  try {
    switch (provider.toLowerCase()) {
      case 'sendgrid':
        return parseSendGridWebhook(body);
      case 'mailgun':
        return parseMailgunWebhook(body);
      case 'postmark':
        return parsePostmarkWebhook(body);
      default:
        return parseGenericWebhook(body);
    }
  } catch (error) {
    console.error('Error parsing email webhook:', error);
    return null;
  }
}

function parseSendGridWebhook(body: any): ParsedEmailData | null {
  // SendGrid inbound parse webhook format
  if (!body.from || !body.to || !body.subject) {
    return null;
  }

  return {
    fromEmail: extractEmail(body.from),
    fromName: extractName(body.from),
    toEmail: extractEmail(body.to),
    subject: body.subject,
    content: body.html || body.text || '',
    receivedAt: body.timestamp ? new Date(body.timestamp * 1000) : new Date(),
    messageId: body.message_id
  };
}

function parseMailgunWebhook(body: any): ParsedEmailData | null {
  // Mailgun webhook format
  if (!body.sender || !body.recipient || !body.subject) {
    return null;
  }

  return {
    fromEmail: extractEmail(body.sender),
    fromName: extractName(body.sender),
    toEmail: extractEmail(body.recipient),
    subject: body.subject,
    content: body['body-html'] || body['body-plain'] || '',
    receivedAt: body.timestamp ? new Date(body.timestamp * 1000) : new Date(),
    messageId: body['Message-Id']
  };
}

function parsePostmarkWebhook(body: any): ParsedEmailData | null {
  // Postmark webhook format
  if (!body.FromFull || !body.ToFull || !body.Subject) {
    return null;
  }

  return {
    fromEmail: body.FromFull.Email,
    fromName: body.FromFull.Name,
    toEmail: body.ToFull[0]?.Email,
    subject: body.Subject,
    content: body.HtmlBody || body.TextBody || '',
    receivedAt: body.Date ? new Date(body.Date) : new Date(),
    messageId: body.MessageID
  };
}

function parseGenericWebhook(body: any): ParsedEmailData | null {
  // Generic email webhook format
  return {
    fromEmail: extractEmail(body.from || body.fromEmail),
    fromName: extractName(body.from || body.fromName),
    toEmail: extractEmail(body.to || body.toEmail),
    subject: body.subject,
    content: body.content || body.html || body.text || '',
    receivedAt: body.receivedAt ? new Date(body.receivedAt) : new Date(),
    messageId: body.messageId
  };
}

function extractEmail(emailString: string): string {
  if (!emailString) return '';
  
  // Extract email from "Name <<EMAIL>>" format
  const match = emailString.match(/<([^>]+)>/);
  if (match) {
    return match[1];
  }
  
  // Return as-is if already just an email
  return emailString.trim();
}

function extractName(emailString: string): string | undefined {
  if (!emailString) return undefined;
  
  // Extract name from "Name <<EMAIL>>" format
  const match = emailString.match(/^([^<]+)</);
  if (match) {
    return match[1].trim().replace(/"/g, '');
  }
  
  return undefined;
}

async function extractTenantFromEmail(toEmail: string): Promise<string | null> {
  try {
    // Extract domain from email
    const domain = toEmail.split('@')[1];
    
    // Look up tenant by domain or email
    // This is a simplified approach - you might want to use a more sophisticated mapping
    const tenant = await prisma.tenant.findFirst({
      where: {
        OR: [
          { slug: domain.split('.')[0] }, // Use subdomain as tenant slug
          { 
            settings: {
              path: ['emailDomain'],
              equals: domain
            }
          }
        ]
      }
    });
    
    return tenant?.id || null;
  } catch (error) {
    console.error('Error extracting tenant from email:', error);
    return null;
  }
}

// Health check endpoint
export async function GET() {
  return NextResponse.json({
    status: 'ok',
    service: 'email-webhook',
    timestamp: new Date().toISOString()
  });
}
