import { NextResponse } from 'next/server';
import { CompanyManagementService, CompanyFilters } from '@/services/company-management';
import { withPermission, AuthenticatedRequest } from '@/lib/permission-middleware';
import { PermissionResource, PermissionAction } from '@/types';
import { z } from 'zod';

const companyService = new CompanyManagementService();

// Validation schema for export parameters
const exportQuerySchema = z.object({
  format: z.enum(['csv', 'json']).default('csv'),
  search: z.string().optional(),
  industry: z.string().optional(),
  size: z.string().optional(),
  ownerId: z.string().optional(),
  hasWebsite: z.string().transform(val => val === 'true').optional(),
});

/**
 * GET /api/companies/export
 * Export companies to CSV or JSON format
 */
export const GET = withPermission({
  resource: PermissionResource.COMPANIES,
  action: PermissionAction.READ,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest) => {
  try {
    const { searchParams } = new URL(req.url);
    const queryParams = Object.fromEntries(searchParams.entries());
    
    const validatedQuery = exportQuerySchema.parse(queryParams);
    
    const filters: CompanyFilters = {
      search: validatedQuery.search,
      industry: validatedQuery.industry,
      size: validatedQuery.size,
      ownerId: validatedQuery.ownerId,
      hasWebsite: validatedQuery.hasWebsite,
      limit: 10000, // Large limit for export
      sortBy: 'name',
      sortOrder: 'asc',
    };

    console.log('Export filters:', filters);

    const result = await companyService.getCompanies(req.tenantId, filters);
    const companies = result.companies;

    console.log(`Exporting ${companies.length} companies`);

    if (validatedQuery.format === 'json') {
      return NextResponse.json({
        success: true,
        data: companies,
        total: companies.length,
        exported: new Date().toISOString()
      });
    }

    // Generate CSV
    const headers = [
      'Name',
      'Website',
      'Industry',
      'Size',
      'Owner Name',
      'Owner Email',
      'Contacts Count',
      'Leads Count',
      'Opportunities Count',
      'Created At',
      'Updated At'
    ];

    const csvRows = [
      headers.join(','),
      ...companies.map(company => [
        `"${(company.name || '').replace(/"/g, '""')}"`,
        `"${(company.website || '').replace(/"/g, '""')}"`,
        `"${(company.industry || '').replace(/"/g, '""')}"`,
        `"${(company.size || '').replace(/"/g, '""')}"`,
        `"${company.owner ? `${company.owner.firstName || ''} ${company.owner.lastName || ''}`.trim() : ''}"`,
        `"${(company.owner?.email || '').replace(/"/g, '""')}"`,
        company._count?.contacts || 0,
        company._count?.leads || 0,
        company._count?.opportunities || 0,
        `"${company.createdAt.toISOString()}"`,
        `"${company.updatedAt.toISOString()}"`
      ].join(','))
    ];

    // Add UTF-8 BOM for proper Arabic character support
    const BOM = '\uFEFF';
    const csvContent = BOM + csvRows.join('\n');
    const filename = `companies-export-${new Date().toISOString().split('T')[0]}.csv`;

    return new NextResponse(csvContent, {
      status: 200,
      headers: {
        'Content-Type': 'text/csv; charset=utf-8',
        'Content-Disposition': `attachment; filename="${filename}"`,
        'Cache-Control': 'no-cache',
      },
    });

  } catch (error) {
    console.error('Export companies error:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid query parameters', details: error.errors },
        { status: 400 }
      );
    }

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
});
