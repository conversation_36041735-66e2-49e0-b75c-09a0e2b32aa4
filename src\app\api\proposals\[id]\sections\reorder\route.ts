import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { hasPermission } from '@/lib/permissions';
import { PermissionResource, PermissionAction } from '@/types';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';

// Request validation schema
const reorderSectionsSchema = z.object({
  sectionOrders: z.array(z.object({
    id: z.string().min(1, 'Section ID is required'),
    orderIndex: z.number().min(0, 'Order index must be non-negative')
  })).min(1, 'At least one section order is required')
});

/**
 * PUT /api/proposals/[id]/sections/reorder
 * Reorder proposal sections
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user's current tenant
    const currentTenant = session.user.tenants?.[0];
    if (!currentTenant) {
      return NextResponse.json({ error: 'No tenant access' }, { status: 403 });
    }

    // Check permission for proposal updating
    const hasAccess = await hasPermission(
      PermissionResource.PROPOSALS,
      PermissionAction.UPDATE,
      currentTenant.id
    );

    if (!hasAccess) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const proposalId = params.id;

    // Verify proposal exists and belongs to tenant
    const proposal = await prisma.proposal.findFirst({
      where: {
        id: proposalId,
        tenantId: currentTenant.id
      }
    });

    if (!proposal) {
      return NextResponse.json({ error: 'Proposal not found' }, { status: 404 });
    }

    // Parse and validate request body
    const body = await request.json();
    const validatedData = reorderSectionsSchema.parse(body);

    // Verify all sections belong to this proposal and tenant
    const sectionIds = validatedData.sectionOrders.map(s => s.id);
    const existingSections = await prisma.proposalSection.findMany({
      where: {
        id: { in: sectionIds },
        proposalId,
        tenantId: currentTenant.id
      }
    });

    if (existingSections.length !== sectionIds.length) {
      return NextResponse.json({ 
        error: 'One or more sections not found or do not belong to this proposal' 
      }, { status: 400 });
    }

    // Update order indexes in a transaction
    await prisma.$transaction(
      validatedData.sectionOrders.map(({ id, orderIndex }) =>
        prisma.proposalSection.update({
          where: { 
            id,
            proposalId,
            tenantId: currentTenant.id
          },
          data: { orderIndex }
        })
      )
    );

    // Return updated sections
    const updatedSections = await prisma.proposalSection.findMany({
      where: {
        proposalId,
        tenantId: currentTenant.id
      },
      orderBy: {
        orderIndex: 'asc'
      }
    });

    return NextResponse.json({
      message: 'Sections reordered successfully',
      sections: updatedSections
    });

  } catch (error) {
    console.error('Error reordering proposal sections:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json({
        error: 'Invalid request data',
        details: error.errors
      }, { status: 400 });
    }

    return NextResponse.json({
      error: 'Internal server error'
    }, { status: 500 });
  }
}
