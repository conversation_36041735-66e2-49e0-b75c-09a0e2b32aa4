import { prisma } from '@/lib/prisma';
import { z } from 'zod';
import { UnifiedNotificationService } from './unified-notification-service';

// Email delivery status types
export type EmailDeliveryStatus = 
  | 'sent' 
  | 'delivered' 
  | 'opened' 
  | 'clicked' 
  | 'bounced' 
  | 'failed' 
  | 'spam' 
  | 'unsubscribed';

// Validation schemas
export const emailDeliveryEventSchema = z.object({
  messageId: z.string(),
  tenantId: z.string(),
  status: z.enum(['sent', 'delivered', 'opened', 'clicked', 'bounced', 'failed', 'spam', 'unsubscribed']),
  timestamp: z.date().optional(),
  recipient: z.string().email(),
  reason: z.string().optional(),
  bounceType: z.enum(['hard', 'soft']).optional(),
  clickedUrl: z.string().optional(),
  userAgent: z.string().optional(),
  ipAddress: z.string().optional(),
  provider: z.string().optional(),
  metadata: z.record(z.any()).optional(),
});

export const bulkEmailDeliveryEventSchema = z.object({
  events: z.array(emailDeliveryEventSchema),
});

export type EmailDeliveryEvent = z.infer<typeof emailDeliveryEventSchema>;
export type BulkEmailDeliveryEvent = z.infer<typeof bulkEmailDeliveryEventSchema>;

export interface EmailLogWithDetails {
  id: string;
  tenantId: string;
  sentBy: string;
  leadId?: string;
  contactId?: string;
  campaignId?: string;
  templateId?: string;
  recipients: string[];
  subject: string;
  content: string;
  messageId?: string;
  provider: string;
  status: string;
  sentAt: Date;
  deliveredAt?: Date;
  openedAt?: Date;
  clickedAt?: Date;
  bouncedAt?: Date;
  failedAt?: Date;
  trackOpens: boolean;
  trackClicks: boolean;
  tags: string[];
  metadata: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
  
  // Relations
  sender?: {
    id: string;
    firstName?: string;
    lastName?: string;
    email: string;
  };
  lead?: {
    id: string;
    title: string;
    status: string;
  };
  contact?: {
    id: string;
    firstName?: string;
    lastName?: string;
    email: string;
  };
}

export class EmailDeliveryNotificationService {
  private notificationService = new UnifiedNotificationService();

  /**
   * Process email delivery webhook event
   */
  async processDeliveryEvent(event: EmailDeliveryEvent): Promise<void> {
    const validatedEvent = emailDeliveryEventSchema.parse(event);

    try {
      // Find the email log by message ID
      const emailLog = await prisma.emailLog.findFirst({
        where: {
          messageId: validatedEvent.messageId,
          tenantId: validatedEvent.tenantId,
        },
        include: {
          sender: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            }
          },
          lead: {
            select: {
              id: true,
              title: true,
              status: true,
            }
          },
          contact: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            }
          }
        }
      });

      if (!emailLog) {
        console.warn(`Email log not found for message ID: ${validatedEvent.messageId}`);
        return;
      }

      // Update email log status
      await this.updateEmailLogStatus(emailLog.id, validatedEvent);

      // Trigger appropriate notifications based on status
      await this.triggerDeliveryStatusNotification(
        emailLog as EmailLogWithDetails,
        validatedEvent
      );

    } catch (error) {
      console.error('Error processing email delivery event:', error);
      throw error;
    }
  }

  /**
   * Process bulk email delivery events
   */
  async processBulkDeliveryEvents(bulkEvent: BulkEmailDeliveryEvent): Promise<void> {
    const validatedBulkEvent = bulkEmailDeliveryEventSchema.parse(bulkEvent);

    const results = await Promise.allSettled(
      validatedBulkEvent.events.map(event => this.processDeliveryEvent(event))
    );

    // Log any failures
    results.forEach((result, index) => {
      if (result.status === 'rejected') {
        console.error(`Failed to process delivery event ${index}:`, result.reason);
      }
    });
  }

  /**
   * Update email log status based on delivery event
   */
  private async updateEmailLogStatus(
    emailLogId: string,
    event: EmailDeliveryEvent
  ): Promise<void> {
    const updateData: any = {
      status: event.status,
      updatedAt: new Date(),
    };

    // Set specific timestamp fields based on status
    switch (event.status) {
      case 'delivered':
        updateData.deliveredAt = event.timestamp || new Date();
        break;
      case 'opened':
        updateData.openedAt = event.timestamp || new Date();
        break;
      case 'clicked':
        updateData.clickedAt = event.timestamp || new Date();
        if (event.clickedUrl) {
          updateData.metadata = {
            clickedUrl: event.clickedUrl,
            userAgent: event.userAgent,
            ipAddress: event.ipAddress,
          };
        }
        break;
      case 'bounced':
        updateData.bouncedAt = event.timestamp || new Date();
        if (event.bounceType || event.reason) {
          updateData.metadata = {
            bounceType: event.bounceType,
            bounceReason: event.reason,
          };
        }
        break;
      case 'failed':
        updateData.failedAt = event.timestamp || new Date();
        if (event.reason) {
          updateData.metadata = {
            failureReason: event.reason,
          };
        }
        break;
    }

    await prisma.emailLog.update({
      where: { id: emailLogId },
      data: updateData,
    });
  }

  /**
   * Trigger delivery status notifications
   */
  private async triggerDeliveryStatusNotification(
    emailLog: EmailLogWithDetails,
    event: EmailDeliveryEvent
  ): Promise<void> {
    try {
      // Only notify on significant events
      const notifiableStatuses: EmailDeliveryStatus[] = ['bounced', 'failed', 'spam'];
      
      if (!notifiableStatuses.includes(event.status)) {
        return;
      }

      // Determine notification details based on status
      let notificationType = 'email_delivery_issue';
      let title = 'Email Delivery Issue';
      let message = `Email "${emailLog.subject}" has encountered a delivery issue`;

      switch (event.status) {
        case 'bounced':
          notificationType = 'email_bounced';
          title = 'Email Bounced';
          message = `Email "${emailLog.subject}" bounced for ${event.recipient}`;
          break;
        case 'failed':
          notificationType = 'email_failed';
          title = 'Email Delivery Failed';
          message = `Failed to deliver email "${emailLog.subject}" to ${event.recipient}`;
          break;
        case 'spam':
          notificationType = 'email_marked_spam';
          title = 'Email Marked as Spam';
          message = `Email "${emailLog.subject}" was marked as spam by ${event.recipient}`;
          break;
      }

      // Notify the sender
      await this.notificationService.createNotification({
        tenantId: emailLog.tenantId,
        userId: emailLog.sentBy,
        type: notificationType,
        category: 'communication',
        title,
        message,
        data: {
          emailLogId: emailLog.id,
          messageId: emailLog.messageId,
          subject: emailLog.subject,
          recipient: event.recipient,
          status: event.status,
          reason: event.reason,
          bounceType: event.bounceType,
          leadId: emailLog.leadId,
          contactId: emailLog.contactId,
          campaignId: emailLog.campaignId,
          sentAt: emailLog.sentAt.toISOString(),
          eventTimestamp: event.timestamp?.toISOString(),
        },
        actionUrl: emailLog.leadId ? `/leads/${emailLog.leadId}` : 
                  emailLog.contactId ? `/contacts/${emailLog.contactId}` : 
                  '/communications/emails',
        actionLabel: 'View Details'
      });

      // For hard bounces, also notify managers
      if (event.status === 'bounced' && event.bounceType === 'hard') {
        await this.triggerHardBounceNotification(emailLog, event);
      }

    } catch (error) {
      console.error('Failed to send email delivery notification:', error);
    }
  }

  /**
   * Trigger hard bounce notification to managers
   */
  private async triggerHardBounceNotification(
    emailLog: EmailLogWithDetails,
    event: EmailDeliveryEvent
  ): Promise<void> {
    try {
      // TODO: Get managers and team leads
      // For now, just notify the sender
      const targetUsers = [emailLog.sentBy];

      for (const userId of targetUsers) {
        await this.notificationService.createNotification({
          tenantId: emailLog.tenantId,
          userId,
          type: 'hard_bounce_alert',
          category: 'communication',
          title: 'Hard Bounce Alert',
          message: `Email to ${event.recipient} hard bounced - address may be invalid`,
          data: {
            emailLogId: emailLog.id,
            recipient: event.recipient,
            bounceReason: event.reason,
            leadId: emailLog.leadId,
            contactId: emailLog.contactId,
            subject: emailLog.subject,
          },
          actionUrl: emailLog.contactId ? `/contacts/${emailLog.contactId}` : '/communications/emails',
          actionLabel: 'Update Contact'
        });
      }
    } catch (error) {
      console.error('Failed to send hard bounce notification:', error);
    }
  }

  /**
   * Get email delivery statistics for a tenant
   */
  async getDeliveryStats(
    tenantId: string,
    options: {
      startDate?: Date;
      endDate?: Date;
      userId?: string;
      campaignId?: string;
    } = {}
  ): Promise<{
    sent: number;
    delivered: number;
    opened: number;
    clicked: number;
    bounced: number;
    failed: number;
    deliveryRate: number;
    openRate: number;
    clickRate: number;
    bounceRate: number;
  }> {
    const { startDate, endDate, userId, campaignId } = options;

    const where: any = {
      tenantId,
      ...(startDate && { sentAt: { gte: startDate } }),
      ...(endDate && { sentAt: { lte: endDate } }),
      ...(userId && { sentBy: userId }),
      ...(campaignId && { campaignId }),
    };

    const [
      sent,
      delivered,
      opened,
      clicked,
      bounced,
      failed
    ] = await Promise.all([
      prisma.emailLog.count({ where: { ...where, status: { not: null } } }),
      prisma.emailLog.count({ where: { ...where, deliveredAt: { not: null } } }),
      prisma.emailLog.count({ where: { ...where, openedAt: { not: null } } }),
      prisma.emailLog.count({ where: { ...where, clickedAt: { not: null } } }),
      prisma.emailLog.count({ where: { ...where, bouncedAt: { not: null } } }),
      prisma.emailLog.count({ where: { ...where, failedAt: { not: null } } }),
    ]);

    const deliveryRate = sent > 0 ? (delivered / sent) * 100 : 0;
    const openRate = delivered > 0 ? (opened / delivered) * 100 : 0;
    const clickRate = delivered > 0 ? (clicked / delivered) * 100 : 0;
    const bounceRate = sent > 0 ? (bounced / sent) * 100 : 0;

    return {
      sent,
      delivered,
      opened,
      clicked,
      bounced,
      failed,
      deliveryRate: Math.round(deliveryRate * 100) / 100,
      openRate: Math.round(openRate * 100) / 100,
      clickRate: Math.round(clickRate * 100) / 100,
      bounceRate: Math.round(bounceRate * 100) / 100,
    };
  }
}

// Export singleton instance
export const emailDeliveryNotificationService = new EmailDeliveryNotificationService();
