'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { PermissionProvider } from '@/components/providers/permission-provider';
import { PermissionGate } from '@/components/ui/permission-gate';
import { PermissionAction, PermissionResource } from '@/types';
import { useTenant } from '@/hooks/use-tenant';
import { PageLayout } from '@/components/layout/page-layout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { useToast } from '@/hooks/use-toast';
import {
  ArrowLeftIcon,
  ExclamationTriangleIcon,
  KeyIcon,
  CheckIcon
} from '@heroicons/react/24/outline';

interface Permission {
  id: string;
  name: string;
  resource: string;
  action: string;
  category: string;
  description: string;
}

interface CreateRoleFormData {
  name: string;
  description: string;
  parentRoleId: string;
  permissions: string[];
}

export default function CreateRolePage() {
  const { currentTenant } = useTenant();
  const router = useRouter();
  const { toast } = useToast();

  const [formData, setFormData] = useState<CreateRoleFormData>({
    name: '',
    description: '',
    parentRoleId: '',
    permissions: []
  });

  const [permissions, setPermissions] = useState<Permission[]>([]);
  const [groupedPermissions, setGroupedPermissions] = useState<Record<string, Permission[]>>({});
  const [roles, setRoles] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (currentTenant) {
      fetchData();
    }
  }, [currentTenant]);

  const fetchData = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const [permissionsResponse, rolesResponse] = await Promise.all([
        fetch('/api/permissions'),
        fetch('/api/roles')
      ]);

      if (permissionsResponse.ok && rolesResponse.ok) {
        const permissionsData = await permissionsResponse.json();
        const rolesData = await rolesResponse.json();

        setPermissions(permissionsData.data.permissions || []);
        setGroupedPermissions(permissionsData.data.groupedPermissions || {});
        setRoles(rolesData.data.roles || []);
      } else {
        setError('Failed to load required data');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load data');
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (field: keyof CreateRoleFormData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handlePermissionToggle = (permissionId: string) => {
    setFormData(prev => ({
      ...prev,
      permissions: prev.permissions.includes(permissionId)
        ? prev.permissions.filter(id => id !== permissionId)
        : [...prev.permissions, permissionId]
    }));
  };

  const handleSelectAllCategory = (category: string) => {
    const categoryPermissions = groupedPermissions[category] || [];
    const categoryPermissionIds = categoryPermissions.map(p => p.id);
    const allSelected = categoryPermissionIds.every(id => formData.permissions.includes(id));

    if (allSelected) {
      // Deselect all in category
      setFormData(prev => ({
        ...prev,
        permissions: prev.permissions.filter(id => !categoryPermissionIds.includes(id))
      }));
    } else {
      // Select all in category
      setFormData(prev => ({
        ...prev,
        permissions: [...new Set([...prev.permissions, ...categoryPermissionIds])]
      }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.name.trim()) {
      toast({
        title: 'Validation Error',
        description: 'Role name is required',
        variant: 'destructive',
      });
      return;
    }

    try {
      setIsSaving(true);

      const response = await fetch('/api/roles', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: formData.name.trim(),
          description: formData.description.trim() || undefined,
          parentRoleId: formData.parentRoleId || undefined,
          permissions: formData.permissions
        }),
      });

      if (response.ok) {
        toast({
          title: 'Success',
          description: 'Role created successfully',
        });
        router.push('/settings/roles');
      } else {
        const errorData = await response.json();
        toast({
          title: 'Error',
          description: errorData.error || 'Failed to create role',
          variant: 'destructive',
        });
      }
    } catch (err) {
      toast({
        title: 'Error',
        description: err instanceof Error ? err.message : 'Failed to create role',
        variant: 'destructive',
      });
    } finally {
      setIsSaving(false);
    }
  };

  const handleCancel = () => {
    router.push('/settings/roles');
  };

  if (!currentTenant) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <ExclamationTriangleIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-500">No tenant context available</p>
        </div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner />
      </div>
    );
  }

  const actions = (
    <div className="flex items-center gap-2">
      <Button variant="outline" onClick={handleCancel}>
        <ArrowLeftIcon className="w-4 h-4 mr-2" />
        Cancel
      </Button>
      <Button
        onClick={handleSubmit}
        disabled={isSaving || !formData.name.trim()}
      >
        {isSaving ? (
          <LoadingSpinner className="w-4 h-4 mr-2" />
        ) : (
          <CheckIcon className="w-4 h-4 mr-2" />
        )}
        Create Role
      </Button>
    </div>
  );

  return (
    <PermissionProvider>
      <PageLayout
        title="Create Role"
        description="Create a new role with custom permissions"
        actions={actions}
      >
        <PermissionGate
          resource={PermissionResource.ROLES}
          action={PermissionAction.CREATE}
          fallback={<AccessDeniedMessage />}
        >
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Error Display */}
            {error && (
              <Card className="border-red-200 bg-red-50">
                <CardContent className="p-4">
                  <div className="flex items-center gap-2 text-red-800">
                    <ExclamationTriangleIcon className="w-5 h-5" />
                    <p>{error}</p>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Basic Information */}
            <Card>
              <CardHeader>
                <CardTitle>Basic Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                    Role Name *
                  </label>
                  <input
                    type="text"
                    id="name"
                    value={formData.name}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Enter role name"
                    required
                  />
                </div>

                <div>
                  <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
                    Description
                  </label>
                  <textarea
                    id="description"
                    value={formData.description}
                    onChange={(e) => handleInputChange('description', e.target.value)}
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Enter role description"
                  />
                </div>

                <div>
                  <label htmlFor="parentRole" className="block text-sm font-medium text-gray-700 mb-1">
                    Parent Role (Optional)
                  </label>
                  <select
                    id="parentRole"
                    value={formData.parentRoleId}
                    onChange={(e) => handleInputChange('parentRoleId', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="">No parent role</option>
                    {roles.filter(role => !role.isSystemRole).map((role) => (
                      <option key={role.id} value={role.id}>
                        {role.name}
                      </option>
                    ))}
                  </select>
                </div>
              </CardContent>
            </Card>

            {/* Permissions */}
            <Card>
              <CardHeader>
                <CardTitle>Permissions</CardTitle>
                <p className="text-sm text-gray-600">
                  Select the permissions this role should have. Selected: {formData.permissions.length}
                </p>
              </CardHeader>
              <CardContent>
                {Object.keys(groupedPermissions).length === 0 ? (
                  <div className="text-center py-8">
                    <KeyIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-500">No permissions available</p>
                  </div>
                ) : (
                  <div className="space-y-6">
                    {Object.entries(groupedPermissions).map(([category, categoryPermissions]) => {
                      const categoryPermissionIds = categoryPermissions.map(p => p.id);
                      const selectedInCategory = categoryPermissionIds.filter(id => 
                        formData.permissions.includes(id)
                      ).length;
                      const allSelected = selectedInCategory === categoryPermissionIds.length;
                      const someSelected = selectedInCategory > 0 && selectedInCategory < categoryPermissionIds.length;

                      return (
                        <div key={category} className="border border-gray-200 rounded-lg p-4">
                          <div className="flex items-center justify-between mb-3">
                            <h4 className="font-medium text-gray-900 capitalize">
                              {category.replace('_', ' ')} ({selectedInCategory}/{categoryPermissionIds.length})
                            </h4>
                            <Button
                              type="button"
                              variant="outline"
                              size="sm"
                              onClick={() => handleSelectAllCategory(category)}
                            >
                              {allSelected ? 'Deselect All' : 'Select All'}
                            </Button>
                          </div>
                          
                          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                            {categoryPermissions.map((permission) => (
                              <label
                                key={permission.id}
                                className="flex items-start gap-3 p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer"
                              >
                                <input
                                  type="checkbox"
                                  checked={formData.permissions.includes(permission.id)}
                                  onChange={() => handlePermissionToggle(permission.id)}
                                  className="mt-0.5 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                />
                                <div className="min-w-0">
                                  <p className="font-medium text-sm text-gray-900">
                                    {permission.name}
                                  </p>
                                  {permission.description && (
                                    <p className="text-xs text-gray-500 mt-1">
                                      {permission.description}
                                    </p>
                                  )}
                                </div>
                              </label>
                            ))}
                          </div>
                        </div>
                      );
                    })}
                  </div>
                )}
              </CardContent>
            </Card>
          </form>
        </PermissionGate>
      </PageLayout>
    </PermissionProvider>
  );
}

function AccessDeniedMessage() {
  return (
    <Card>
      <CardContent className="p-12 text-center">
        <ExclamationTriangleIcon className="w-12 h-12 text-red-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">Access Denied</h3>
        <p className="text-gray-500">
          You don't have permission to create roles. Contact your administrator for access.
        </p>
      </CardContent>
    </Card>
  );
}
