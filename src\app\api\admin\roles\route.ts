import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { withTenantContext } from '@/lib/tenant-context';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';

/**
 * @swagger
 * /api/admin/roles:
 *   get:
 *     summary: Get all roles in tenant
 *     description: Retrieve all roles with user and permission counts for the current tenant
 *     tags:
 *       - Admin - Roles
 *     security:
 *       - bearerAuth: []
 *       - tenantHeader: []
 *     responses:
 *       200:
 *         description: List of roles with metadata
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: string
 *                       name:
 *                         type: string
 *                       description:
 *                         type: string
 *                       isSystemRole:
 *                         type: boolean
 *                       userCount:
 *                         type: integer
 *                       permissionCount:
 *                         type: integer
 *                       createdAt:
 *                         type: string
 *                         format: date-time
 *                       updatedAt:
 *                         type: string
 *                         format: date-time
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - Admin access required
 */
export const GET = withTenantContext(async (request: NextRequest) => {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const tenantId = (request as any).tenantId;

    // Check if user has admin access to this tenant
    const tenantUser = await prisma.tenantUser.findUnique({
      where: {
        tenantId_userId: {
          tenantId,
          userId: session.user.id
        }
      }
    });

    if (!tenantUser?.isTenantAdmin && !session.user.isPlatformAdmin) {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      );
    }

    // Get all roles with user and permission counts
    const roles = await prisma.role.findMany({
      where: { tenantId },
      include: {
        _count: {
          select: {
            userRoles: true,
            rolePermissions: true
          }
        }
      },
      orderBy: [
        { isSystemRole: 'desc' },
        { name: 'asc' }
      ]
    });

    const rolesWithCounts = roles.map(role => ({
      id: role.id,
      name: role.name,
      description: role.description,
      isSystemRole: role.isSystemRole,
      userCount: role._count.userRoles,
      permissionCount: role._count.rolePermissions,
      createdAt: role.createdAt,
      updatedAt: role.updatedAt
    }));

    return NextResponse.json({
      success: true,
      data: rolesWithCounts
    });

  } catch (error) {
    console.error('Get roles error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
});

const createRoleSchema = z.object({
  name: z.string().min(1, 'Role name is required').max(100, 'Role name too long'),
  description: z.string().optional(),
  permissions: z.array(z.string()).optional().default([])
});

/**
 * @swagger
 * /api/admin/roles:
 *   post:
 *     summary: Create a new role
 *     description: Create a new custom role with specified permissions
 *     tags:
 *       - Admin - Roles
 *     security:
 *       - bearerAuth: []
 *       - tenantHeader: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *             properties:
 *               name:
 *                 type: string
 *                 description: Role name
 *                 example: "Sales Manager"
 *               description:
 *                 type: string
 *                 description: Role description
 *                 example: "Manages sales team and processes"
 *               permissions:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: Array of permission IDs to assign
 *     responses:
 *       201:
 *         description: Role created successfully
 *       400:
 *         description: Validation error
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - Admin access required
 *       409:
 *         description: Role name already exists
 */
export const POST = withTenantContext(async (request: NextRequest) => {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const tenantId = (request as any).tenantId;

    // Check if user has admin access to this tenant
    const tenantUser = await prisma.tenantUser.findUnique({
      where: {
        tenantId_userId: {
          tenantId,
          userId: session.user.id
        }
      }
    });

    if (!tenantUser?.isTenantAdmin && !session.user.isPlatformAdmin) {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      );
    }

    const body = await request.json();
    const validatedData = createRoleSchema.parse(body);

    // Check if role name already exists
    const existingRole = await prisma.role.findUnique({
      where: {
        tenantId_name: {
          tenantId,
          name: validatedData.name
        }
      }
    });

    if (existingRole) {
      return NextResponse.json(
        { error: 'Role name already exists' },
        { status: 409 }
      );
    }

    // Create role with permissions in a transaction
    const result = await prisma.$transaction(async (tx) => {
      // Create the role
      const role = await tx.role.create({
        data: {
          tenantId,
          name: validatedData.name,
          description: validatedData.description,
          isSystemRole: false,
          createdById: session.user.id
        }
      });

      // Assign permissions if provided
      if (validatedData.permissions.length > 0) {
        const permissionAssignments = validatedData.permissions.map(permissionId => ({
          tenantId,
          roleId: role.id,
          permissionId,
          grantedBy: session.user.id
        }));

        await tx.rolePermission.createMany({
          data: permissionAssignments
        });
      }

      return role;
    });

    return NextResponse.json({
      success: true,
      data: result,
      message: 'Role created successfully'
    }, { status: 201 });

  } catch (error) {
    console.error('Create role error:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          error: 'Validation failed',
          details: error.errors.map(e => ({
            field: e.path.join('.'),
            message: e.message
          }))
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
});
