import { NextResponse } from 'next/server';
import { withPermission } from '@/lib/permission-middleware';
import { PermissionAction, PermissionResource } from '@/types';
import { AuthenticatedRequest } from '@/lib/permission-middleware';
import { prisma } from '@/lib/prisma';

// Common lead sources that can be used across tenants
const DEFAULT_LEAD_SOURCES = [
  {
    id: 'website',
    name: 'Website',
    description: 'Leads from website forms and landing pages',
    category: 'digital',
    icon: 'globe'
  },
  {
    id: 'referral',
    name: 'Referral',
    description: 'Leads from customer or partner referrals',
    category: 'word_of_mouth',
    icon: 'users'
  },
  {
    id: 'cold_call',
    name: 'Cold Call',
    description: 'Leads from outbound cold calling',
    category: 'outbound',
    icon: 'phone'
  },
  {
    id: 'cold_email',
    name: 'Cold Email',
    description: 'Leads from outbound email campaigns',
    category: 'outbound',
    icon: 'mail'
  },
  {
    id: 'social_media',
    name: 'Social Media',
    description: 'Leads from social media platforms',
    category: 'digital',
    icon: 'share-2'
  },
  {
    id: 'linkedin',
    name: 'LinkedIn',
    description: 'Leads from LinkedIn outreach and connections',
    category: 'digital',
    icon: 'linkedin'
  },
  {
    id: 'google_ads',
    name: 'Google Ads',
    description: 'Leads from Google advertising campaigns',
    category: 'paid_advertising',
    icon: 'search'
  },
  {
    id: 'facebook_ads',
    name: 'Facebook Ads',
    description: 'Leads from Facebook advertising campaigns',
    category: 'paid_advertising',
    icon: 'facebook'
  },
  {
    id: 'trade_show',
    name: 'Trade Show',
    description: 'Leads from trade shows and events',
    category: 'events',
    icon: 'calendar'
  },
  {
    id: 'webinar',
    name: 'Webinar',
    description: 'Leads from webinars and online events',
    category: 'events',
    icon: 'video'
  },
  {
    id: 'content_marketing',
    name: 'Content Marketing',
    description: 'Leads from blog posts, whitepapers, and content',
    category: 'digital',
    icon: 'file-text'
  },
  {
    id: 'email_marketing',
    name: 'Email Marketing',
    description: 'Leads from email marketing campaigns',
    category: 'digital',
    icon: 'mail'
  },
  {
    id: 'partner',
    name: 'Partner',
    description: 'Leads from business partners',
    category: 'partnerships',
    icon: 'handshake'
  },
  {
    id: 'direct_mail',
    name: 'Direct Mail',
    description: 'Leads from direct mail campaigns',
    category: 'traditional',
    icon: 'mail'
  },
  {
    id: 'word_of_mouth',
    name: 'Word of Mouth',
    description: 'Leads from word-of-mouth recommendations',
    category: 'word_of_mouth',
    icon: 'message-circle'
  },
  {
    id: 'existing_customer',
    name: 'Existing Customer',
    description: 'Leads from existing customers (upsell/cross-sell)',
    category: 'existing_relationships',
    icon: 'user-check'
  },
  {
    id: 'other',
    name: 'Other',
    description: 'Other lead sources not listed above',
    category: 'other',
    icon: 'more-horizontal'
  }
];

/**
 * GET /api/leads/sources
 * Get available lead sources for the tenant
 */
export const GET = withPermission({
  resource: PermissionResource.LEADS,
  action: PermissionAction.READ,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest) => {
  try {
    // Get custom lead sources from the database for this tenant
    const customSources = await prisma.lead.findMany({
      where: {
        tenantId: req.tenantId,
        source: {
          not: null
        }
      },
      select: {
        source: true
      },
      distinct: ['source']
    });

    // Extract unique custom sources
    const uniqueCustomSources = customSources
      .map(lead => lead.source)
      .filter((source): source is string => source !== null)
      .filter(source => !DEFAULT_LEAD_SOURCES.some(defaultSource => 
        defaultSource.id === source.toLowerCase().replace(/\s+/g, '_')
      ))
      .map(source => ({
        id: source.toLowerCase().replace(/\s+/g, '_'),
        name: source,
        description: `Custom lead source: ${source}`,
        category: 'custom',
        icon: 'tag',
        isCustom: true
      }));

    // Combine default and custom sources
    const allSources = [
      ...DEFAULT_LEAD_SOURCES,
      ...uniqueCustomSources
    ];

    // Get source usage statistics
    const sourceStats = await prisma.lead.groupBy({
      by: ['source'],
      where: {
        tenantId: req.tenantId,
        source: {
          not: null
        }
      },
      _count: {
        source: true
      }
    });

    // Add usage statistics to sources
    const sourcesWithStats = allSources.map(source => {
      const stats = sourceStats.find(stat => 
        stat.source?.toLowerCase().replace(/\s+/g, '_') === source.id
      );
      
      return {
        ...source,
        usage: stats?._count.source || 0
      };
    });

    // Sort by usage (most used first), then by name
    sourcesWithStats.sort((a, b) => {
      if (a.usage !== b.usage) {
        return b.usage - a.usage;
      }
      return a.name.localeCompare(b.name);
    });

    return NextResponse.json({
      success: true,
      data: {
        sources: sourcesWithStats,
        categories: [
          { id: 'digital', name: 'Digital Marketing', description: 'Online and digital channels' },
          { id: 'outbound', name: 'Outbound Sales', description: 'Proactive outreach efforts' },
          { id: 'paid_advertising', name: 'Paid Advertising', description: 'Paid marketing campaigns' },
          { id: 'events', name: 'Events & Webinars', description: 'In-person and virtual events' },
          { id: 'partnerships', name: 'Partnerships', description: 'Partner and referral channels' },
          { id: 'traditional', name: 'Traditional Marketing', description: 'Traditional marketing methods' },
          { id: 'word_of_mouth', name: 'Word of Mouth', description: 'Organic referrals and recommendations' },
          { id: 'existing_relationships', name: 'Existing Relationships', description: 'Current customers and contacts' },
          { id: 'custom', name: 'Custom Sources', description: 'Tenant-specific lead sources' },
          { id: 'other', name: 'Other', description: 'Miscellaneous sources' }
        ],
        totalSources: sourcesWithStats.length,
        customSourcesCount: uniqueCustomSources.length
      },
      message: 'Lead sources retrieved successfully'
    });

  } catch (error) {
    console.error('Error fetching lead sources:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch lead sources',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
});

/**
 * POST /api/leads/sources
 * Create a custom lead source for the tenant
 */
export const POST = withPermission({
  resource: PermissionResource.LEADS,
  action: PermissionAction.CREATE,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest) => {
  try {
    const body = await req.json();
    const { name, description, category = 'custom' } = body;

    if (!name || typeof name !== 'string') {
      return NextResponse.json(
        { success: false, error: 'Source name is required' },
        { status: 400 }
      );
    }

    // Create the source ID from the name
    const sourceId = name.toLowerCase().replace(/\s+/g, '_');

    // Check if source already exists
    const existingSource = DEFAULT_LEAD_SOURCES.find(source => source.id === sourceId);
    if (existingSource) {
      return NextResponse.json(
        { success: false, error: 'This source already exists as a default source' },
        { status: 409 }
      );
    }

    // For now, we'll just return the created source
    // In a full implementation, you might want to store custom sources in a separate table
    const customSource = {
      id: sourceId,
      name: name.trim(),
      description: description || `Custom lead source: ${name}`,
      category,
      icon: 'tag',
      isCustom: true,
      usage: 0
    };

    return NextResponse.json({
      success: true,
      data: { source: customSource },
      message: 'Custom lead source created successfully'
    }, { status: 201 });

  } catch (error) {
    console.error('Error creating custom lead source:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to create custom lead source',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
});
