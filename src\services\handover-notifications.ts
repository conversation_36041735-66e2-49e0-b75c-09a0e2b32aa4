import { prisma } from '@/lib/prisma';
import { z } from 'zod';

// Validation schemas
export const createHandoverNotificationSchema = z.object({
  type: z.enum([
    'handover_created',
    'handover_assigned',
    'checklist_completed',
    'checklist_item_completed',
    'document_uploaded',
    'document_approved',
    'qa_thread_created',
    'qa_response_received',
    'qa_thread_escalated',
    'handover_completed',
    'project_created',
    'handover_overdue'
  ]),
  title: z.string().min(1, 'Title is required').max(255),
  message: z.string().min(1, 'Message is required'),
  data: z.record(z.any()).default({}),
  actionUrl: z.string().optional(),
  actionLabel: z.string().optional(),
});

export const bulkCreateNotificationsSchema = z.object({
  userIds: z.array(z.string()).min(1, 'At least one user ID is required'),
  notification: createHandoverNotificationSchema,
});

// Types
export type CreateHandoverNotificationData = z.infer<typeof createHandoverNotificationSchema>;
export type BulkCreateNotificationsData = z.infer<typeof bulkCreateNotificationsSchema>;

export interface HandoverNotificationWithDetails {
  id: string;
  tenantId: string;
  handoverId: string;
  userId: string;
  type: string;
  title: string;
  message: string;
  data: any;
  isRead: boolean;
  readAt?: Date;
  actionUrl?: string;
  actionLabel?: string;
  createdAt: Date;
  
  // Relations
  user: {
    id: string;
    firstName?: string;
    lastName?: string;
    email: string;
  };
  handover: {
    id: string;
    title: string;
    status: string;
    opportunity: {
      id: string;
      title: string;
    };
  };
}

export class HandoverNotificationService {
  private includeOptions = {
    user: {
      select: {
        id: true,
        firstName: true,
        lastName: true,
        email: true,
      }
    },
    handover: {
      select: {
        id: true,
        title: true,
        status: true,
        opportunity: {
          select: {
            id: true,
            title: true,
          }
        }
      }
    }
  };

  /**
   * Create a notification for a specific user
   */
  async createNotification(
    tenantId: string,
    handoverId: string,
    userId: string,
    notificationData: CreateHandoverNotificationData
  ): Promise<HandoverNotificationWithDetails> {
    const validatedData = createHandoverNotificationSchema.parse(notificationData);

    // Verify handover exists and belongs to tenant
    const handover = await prisma.projectHandover.findUnique({
      where: {
        id: handoverId,
        tenantId
      }
    });

    if (!handover) {
      throw new Error('Handover not found or access denied');
    }

    // Verify user belongs to tenant
    const user = await prisma.user.findFirst({
      where: {
        id: userId,
        tenantUsers: {
          some: { tenantId }
        }
      }
    });

    if (!user) {
      throw new Error('User not found or not part of tenant');
    }

    const notification = await prisma.handoverNotification.create({
      data: {
        tenantId,
        handoverId,
        userId,
        type: validatedData.type,
        title: validatedData.title,
        message: validatedData.message,
        data: validatedData.data,
        actionUrl: validatedData.actionUrl,
        actionLabel: validatedData.actionLabel,
      },
      include: this.includeOptions,
    });

    return notification as HandoverNotificationWithDetails;
  }

  /**
   * Create notifications for multiple users
   */
  async createBulkNotifications(
    tenantId: string,
    handoverId: string,
    bulkData: BulkCreateNotificationsData
  ): Promise<HandoverNotificationWithDetails[]> {
    const validatedData = bulkCreateNotificationsSchema.parse(bulkData);

    return await prisma.$transaction(async (tx) => {
      // Verify handover exists and belongs to tenant
      const handover = await tx.projectHandover.findUnique({
        where: {
          id: handoverId,
          tenantId
        }
      });

      if (!handover) {
        throw new Error('Handover not found or access denied');
      }

      // Verify all users belong to tenant
      const users = await tx.user.findMany({
        where: {
          id: { in: validatedData.userIds },
          tenantUsers: {
            some: { tenantId }
          }
        }
      });

      if (users.length !== validatedData.userIds.length) {
        throw new Error('Some users not found or not part of tenant');
      }

      // Create notifications for all users
      const notifications = await Promise.all(
        validatedData.userIds.map(userId =>
          tx.handoverNotification.create({
            data: {
              tenantId,
              handoverId,
              userId,
              type: validatedData.notification.type,
              title: validatedData.notification.title,
              message: validatedData.notification.message,
              data: validatedData.notification.data,
              actionUrl: validatedData.notification.actionUrl,
              actionLabel: validatedData.notification.actionLabel,
            },
            include: this.includeOptions,
          })
        )
      );

      return notifications as HandoverNotificationWithDetails[];
    });
  }

  /**
   * Notify delivery team when handover is created
   */
  async notifyHandoverCreated(
    tenantId: string,
    handoverId: string,
    createdBy: string
  ): Promise<void> {
    const handover = await prisma.projectHandover.findUnique({
      where: {
        id: handoverId,
        tenantId
      },
      include: {
        opportunity: {
          select: {
            title: true,
            contact: true,
            company: true
          }
        },
        salesRep: true,
        deliveryManager: true
      }
    });

    if (!handover) return;

    const clientName = handover.opportunity.company?.name || 
      `${handover.opportunity.contact?.firstName} ${handover.opportunity.contact?.lastName}`;

    const notificationData: CreateHandoverNotificationData = {
      type: 'handover_created',
      title: 'New Handover Created',
      message: `A new handover "${handover.title}" has been created for ${clientName}`,
      data: {
        handoverId: handover.id,
        opportunityId: handover.opportunityId,
        clientName,
        createdBy
      },
      actionUrl: `/handovers/${handover.id}`,
      actionLabel: 'View Handover'
    };

    // Notify delivery manager and assigned team members
    const userIds = [
      handover.deliveryManagerId,
      ...handover.assignedTeamIds as string[]
    ].filter(Boolean);

    if (userIds.length > 0) {
      await this.createBulkNotifications(tenantId, handoverId, {
        userIds,
        notification: notificationData
      });
    }
  }

  /**
   * Notify when checklist is completed
   */
  async notifyChecklistCompleted(
    tenantId: string,
    handoverId: string,
    completedBy: string
  ): Promise<void> {
    const handover = await prisma.projectHandover.findUnique({
      where: {
        id: handoverId,
        tenantId
      },
      include: {
        opportunity: { select: { title: true } },
        salesRep: true,
        deliveryManager: true
      }
    });

    if (!handover) return;

    const notificationData: CreateHandoverNotificationData = {
      type: 'checklist_completed',
      title: 'Handover Checklist Completed',
      message: `The checklist for "${handover.title}" has been completed`,
      data: {
        handoverId: handover.id,
        completedBy,
        progress: handover.checklistProgress
      },
      actionUrl: `/handovers/${handover.id}`,
      actionLabel: 'View Handover'
    };

    // Notify sales rep and delivery manager
    const userIds = [handover.salesRepId, handover.deliveryManagerId].filter(Boolean);

    if (userIds.length > 0) {
      await this.createBulkNotifications(tenantId, handoverId, {
        userIds,
        notification: notificationData
      });
    }
  }

  /**
   * Notify when Q&A response is received
   */
  async notifyQAResponse(
    tenantId: string,
    handoverId: string,
    threadId: string,
    respondedBy: string
  ): Promise<void> {
    const thread = await prisma.handoverQAThread.findUnique({
      where: {
        id: threadId,
        tenantId
      },
      include: {
        handover: {
          select: {
            title: true,
            salesRepId: true,
            deliveryManagerId: true
          }
        },
        createdBy: true,
        assignedTo: true
      }
    });

    if (!thread) return;

    const notificationData: CreateHandoverNotificationData = {
      type: 'qa_response_received',
      title: 'Q&A Response Received',
      message: `New response in Q&A thread: "${thread.title}"`,
      data: {
        handoverId: thread.handoverId,
        threadId: thread.id,
        respondedBy
      },
      actionUrl: `/handovers/${thread.handoverId}/qa/${thread.id}`,
      actionLabel: 'View Thread'
    };

    // Notify thread creator and assigned user (excluding the responder)
    const userIds = [
      thread.createdById,
      thread.assignedToId,
      thread.handover.salesRepId,
      thread.handover.deliveryManagerId
    ].filter(Boolean).filter(id => id !== respondedBy);

    if (userIds.length > 0) {
      await this.createBulkNotifications(tenantId, handoverId, {
        userIds: [...new Set(userIds)], // Remove duplicates
        notification: notificationData
      });
    }
  }

  /**
   * Notify when handover is completed
   */
  async notifyHandoverCompleted(
    tenantId: string,
    handoverId: string,
    completedBy: string
  ): Promise<void> {
    const handover = await prisma.projectHandover.findUnique({
      where: {
        id: handoverId,
        tenantId
      },
      include: {
        opportunity: { select: { title: true } },
        salesRep: true,
        deliveryManager: true
      }
    });

    if (!handover) return;

    const notificationData: CreateHandoverNotificationData = {
      type: 'handover_completed',
      title: 'Handover Completed',
      message: `Handover "${handover.title}" has been completed and is ready for project creation`,
      data: {
        handoverId: handover.id,
        completedBy,
        completedAt: new Date().toISOString()
      },
      actionUrl: `/handovers/${handover.id}`,
      actionLabel: 'Create Project'
    };

    // Notify sales rep, delivery manager, and assigned team
    const userIds = [
      handover.salesRepId,
      handover.deliveryManagerId,
      ...handover.assignedTeamIds as string[]
    ].filter(Boolean);

    if (userIds.length > 0) {
      await this.createBulkNotifications(tenantId, handoverId, {
        userIds: [...new Set(userIds)], // Remove duplicates
        notification: notificationData
      });
    }
  }

  /**
   * Get notifications for a user
   */
  async getUserNotifications(
    tenantId: string,
    userId: string,
    options: {
      isRead?: boolean;
      handoverId?: string;
      type?: string;
      limit?: number;
      offset?: number;
    } = {}
  ): Promise<{
    notifications: HandoverNotificationWithDetails[];
    total: number;
    unreadCount: number;
  }> {
    const {
      isRead,
      handoverId,
      type,
      limit = 20,
      offset = 0
    } = options;

    const where: any = {
      tenantId,
      userId,
      ...(typeof isRead === 'boolean' && { isRead }),
      ...(handoverId && { handoverId }),
      ...(type && { type }),
    };

    const [notifications, total, unreadCount] = await Promise.all([
      prisma.handoverNotification.findMany({
        where,
        include: this.includeOptions,
        orderBy: { createdAt: 'desc' },
        take: limit,
        skip: offset,
      }),
      prisma.handoverNotification.count({ where }),
      prisma.handoverNotification.count({
        where: { ...where, isRead: false }
      })
    ]);

    return {
      notifications: notifications as HandoverNotificationWithDetails[],
      total,
      unreadCount
    };
  }

  /**
   * Mark notifications as read
   */
  async markNotificationsAsRead(
    tenantId: string,
    userId: string,
    notificationIds?: string[]
  ): Promise<number> {
    const where: any = {
      tenantId,
      userId,
      isRead: false,
    };

    if (notificationIds && notificationIds.length > 0) {
      where.id = { in: notificationIds };
    }

    const result = await prisma.handoverNotification.updateMany({
      where,
      data: {
        isRead: true,
        readAt: new Date()
      }
    });

    return result.count;
  }

  /**
   * Get notification statistics for a tenant
   */
  async getNotificationStats(
    tenantId: string,
    options: {
      handoverId?: string;
      dateFrom?: Date;
      dateTo?: Date;
    } = {}
  ): Promise<{
    total: number;
    byType: Record<string, number>;
    byStatus: { read: number; unread: number };
    recentActivity: number;
  }> {
    const { handoverId, dateFrom, dateTo } = options;

    const where: any = {
      tenantId,
      ...(handoverId && { handoverId }),
      ...(dateFrom && dateTo && {
        createdAt: {
          gte: dateFrom,
          lte: dateTo
        }
      })
    };

    const notifications = await prisma.handoverNotification.findMany({
      where,
      select: {
        type: true,
        isRead: true,
        createdAt: true,
      }
    });

    const stats = {
      total: notifications.length,
      byType: {} as Record<string, number>,
      byStatus: { read: 0, unread: 0 },
      recentActivity: 0
    };

    const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);

    notifications.forEach(notification => {
      // Count by type
      stats.byType[notification.type] = (stats.byType[notification.type] || 0) + 1;
      
      // Count by status
      if (notification.isRead) {
        stats.byStatus.read++;
      } else {
        stats.byStatus.unread++;
      }

      // Count recent activity (last 24 hours)
      if (notification.createdAt > oneDayAgo) {
        stats.recentActivity++;
      }
    });

    return stats;
  }
}

// Export singleton instance
export const handoverNotificationService = new HandoverNotificationService();
