import { NextRequest, NextResponse } from 'next/server';
import { withSuperAdminAuth } from '@/lib/tenant-context';
import { SuperAdminService } from '@/services/super-admin';

/**
 * @swagger
 * /api/admin/tenants/{tenantId}:
 *   get:
 *     summary: Get specific tenant details (Super Admin only)
 *     description: Retrieve detailed information about a specific tenant
 *     tags:
 *       - Super Admin - Tenants
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: tenantId
 *         required: true
 *         schema:
 *           type: string
 *         description: Tenant ID
 *     responses:
 *       200:
 *         description: Tenant details retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: string
 *                     name:
 *                       type: string
 *                     slug:
 *                       type: string
 *                     status:
 *                       type: string
 *                     userCount:
 *                       type: number
 *                     subscription:
 *                       type: object
 *                     usage:
 *                       type: object
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Super Admin access required
 *       404:
 *         description: Tenant not found
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ tenantId: string }> }
) {
  return withSuperAdminAuth(async (req: NextRequest) => {
    try {
      const { tenantId } = await params;

      const superAdminService = new SuperAdminService();
      const tenant = await superAdminService.getTenantById(tenantId);

      if (!tenant) {
        return NextResponse.json(
          { error: 'Tenant not found' },
          { status: 404 }
        );
      }

      return NextResponse.json({
        success: true,
        data: tenant
      });

    } catch (error) {
      console.error('Get tenant details error:', error);
      return NextResponse.json(
        { error: 'Internal server error' },
        { status: 500 }
      );
    }
  })(request);
}
