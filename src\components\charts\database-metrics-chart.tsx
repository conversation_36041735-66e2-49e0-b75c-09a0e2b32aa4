'use client';

import React from 'react';
import {
  Bar<PERSON>hart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Cell,
} from 'recharts';
import { BaseChart, BaseChartProps } from './base-chart';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

interface DatabaseMetrics {
  queryStats: Record<string, any>;
  averageQueryTime: number;
  slowQueries: Array<{ 
    name: string; 
    avg: number; 
    count: number; 
    max: number; 
    min: number; 
  }>;
}

interface DatabaseMetricsChartProps extends Omit<BaseChartProps, 'children'> {
  data: DatabaseMetrics;
  timeWindow: string;
}

const COLORS = [
  'hsl(var(--chart-1))',
  'hsl(var(--chart-2))',
  'hsl(var(--chart-3))',
  'hsl(var(--chart-4))',
  'hsl(var(--chart-5))'
];

export function DatabaseMetricsChart({
  data,
  timeWindow,
  ...baseProps
}: DatabaseMetricsChartProps) {
  // Transform slow queries data for visualization
  const slowQueriesData = data.slowQueries.map(query => ({
    name: query.name.length > 20 ? query.name.substring(0, 20) + '...' : query.name,
    fullName: query.name,
    avgTime: query.avg,
    count: query.count,
    maxTime: query.max,
    minTime: query.min,
  }));

  // Generate mock query type distribution
  const queryTypeData = [
    { name: 'SELECT', value: 65, count: 1250 },
    { name: 'INSERT', value: 20, count: 380 },
    { name: 'UPDATE', value: 10, count: 190 },
    { name: 'DELETE', value: 3, count: 57 },
    { name: 'OTHER', value: 2, count: 38 },
  ];

  // Generate mock connection pool data
  const connectionData = [
    { time: '5m ago', active: 8, idle: 12, waiting: 0 },
    { time: '4m ago', active: 10, idle: 10, waiting: 1 },
    { time: '3m ago', active: 12, idle: 8, waiting: 0 },
    { time: '2m ago', active: 9, idle: 11, waiting: 0 },
    { time: '1m ago', active: 11, idle: 9, waiting: 2 },
    { time: 'now', active: 10, idle: 10, waiting: 0 },
  ];

  const formatTooltipValue = (value: number, name: string) => {
    switch (name) {
      case 'avgTime':
        return [`${Math.round(value)}ms`, 'Avg Time'];
      case 'count':
        return [`${value}`, 'Query Count'];
      case 'maxTime':
        return [`${Math.round(value)}ms`, 'Max Time'];
      case 'minTime':
        return [`${Math.round(value)}ms`, 'Min Time'];
      case 'active':
        return [`${value}`, 'Active Connections'];
      case 'idle':
        return [`${value}`, 'Idle Connections'];
      case 'waiting':
        return [`${value}`, 'Waiting Connections'];
      default:
        return [value.toString(), name];
    }
  };

  const renderCustomLabel = (entry: any) => {
    return `${entry.name} (${entry.value}%)`;
  };

  return (
    <BaseChart {...baseProps}>
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Slow Queries Performance */}
        <Card className="bg-white dark:bg-gray-800 rounded-lg shadow-soft">
          <CardHeader>
            <CardTitle className="text-sm">Slow Query Performance</CardTitle>
            <CardDescription>Queries taking longer than 1 second</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={slowQueriesData} layout="horizontal">
                <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
                <XAxis 
                  type="number"
                  className="text-xs"
                  label={{ value: 'Average Time (ms)', position: 'insideBottom', offset: -5 }}
                />
                <YAxis 
                  type="category"
                  dataKey="name"
                  className="text-xs"
                  width={100}
                />
                <Tooltip
                  formatter={formatTooltipValue}
                  contentStyle={{
                    backgroundColor: 'hsl(var(--card))',
                    border: '1px solid hsl(var(--border))',
                    borderRadius: '6px',
                    fontSize: '12px'
                  }}
                />
                <Legend />
                
                <Bar
                  dataKey="avgTime"
                  fill="hsl(var(--primary))"
                  name="Avg Time"
                  radius={[0, 4, 4, 0]}
                />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Query Type Distribution */}
        <Card className="bg-white dark:bg-gray-800 rounded-lg shadow-soft">
          <CardHeader>
            <CardTitle className="text-sm">Query Type Distribution</CardTitle>
            <CardDescription>Breakdown of database operations</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={queryTypeData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={renderCustomLabel}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {queryTypeData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip 
                  formatter={(value, name, props) => [
                    `${value}% (${props.payload.count} queries)`,
                    'Distribution'
                  ]}
                />
                <Legend />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Connection Pool Status */}
        <Card className="bg-white dark:bg-gray-800 rounded-lg shadow-soft">
          <CardHeader>
            <CardTitle className="text-sm">Connection Pool Status</CardTitle>
            <CardDescription>Database connection utilization over time</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={connectionData}>
                <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
                <XAxis 
                  dataKey="time" 
                  className="text-xs"
                />
                <YAxis 
                  className="text-xs"
                  label={{ value: 'Connections', angle: -90, position: 'insideLeft' }}
                />
                <Tooltip
                  formatter={formatTooltipValue}
                  contentStyle={{
                    backgroundColor: 'hsl(var(--card))',
                    border: '1px solid hsl(var(--border))',
                    borderRadius: '6px',
                    fontSize: '12px'
                  }}
                />
                <Legend />
                
                <Bar
                  dataKey="active"
                  stackId="connections"
                  fill="hsl(var(--chart-1))"
                  name="Active"
                />
                <Bar
                  dataKey="idle"
                  stackId="connections"
                  fill="hsl(var(--chart-2))"
                  name="Idle"
                />
                <Bar
                  dataKey="waiting"
                  stackId="connections"
                  fill="hsl(var(--destructive))"
                  name="Waiting"
                />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Database Performance Summary */}
        <Card className="bg-white dark:bg-gray-800 rounded-lg shadow-soft">
          <CardHeader>
            <CardTitle className="text-sm">Performance Summary</CardTitle>
            <CardDescription>Key database performance indicators</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded">
                  <div className="text-lg font-bold text-primary">
                    {Math.round(data.averageQueryTime)}ms
                  </div>
                  <div className="text-xs text-muted-foreground">Avg Query Time</div>
                </div>
                <div className="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded">
                  <div className="text-lg font-bold text-chart-2">
                    {data.slowQueries.length}
                  </div>
                  <div className="text-xs text-muted-foreground">Slow Queries</div>
                </div>
              </div>
              
              <div className="space-y-2">
                <h5 className="text-xs font-semibold">Top Slow Queries</h5>
                {data.slowQueries.slice(0, 3).map((query, index) => (
                  <div key={index} className="flex justify-between items-center text-xs">
                    <span className="truncate flex-1 mr-2">
                      {query.name.length > 30 ? query.name.substring(0, 30) + '...' : query.name}
                    </span>
                    <span className="font-medium">{Math.round(query.avg)}ms</span>
                  </div>
                ))}
              </div>
              
              <div className="pt-2 border-t border-gray-200 dark:border-gray-700">
                <div className="grid grid-cols-3 gap-2 text-xs">
                  <div className="text-center">
                    <div className="font-semibold">20</div>
                    <div className="text-muted-foreground">Max Pool</div>
                  </div>
                  <div className="text-center">
                    <div className="font-semibold">10</div>
                    <div className="text-muted-foreground">Active</div>
                  </div>
                  <div className="text-center">
                    <div className="font-semibold">50%</div>
                    <div className="text-muted-foreground">Utilization</div>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </BaseChart>
  );
}
