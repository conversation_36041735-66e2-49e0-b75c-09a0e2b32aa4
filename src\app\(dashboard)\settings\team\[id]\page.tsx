'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { PermissionProvider } from '@/components/providers/permission-provider';
import { PermissionGate } from '@/components/ui/permission-gate';
import { PermissionAction, PermissionResource } from '@/types';
import { useTenant } from '@/hooks/use-tenant';
import { PageLayout } from '@/components/layout/page-layout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ContactAvatar } from '@/components/ui/contact-avatar';
import { TeamWithDetails, TeamMember } from '@/types';
import { TeamMembers } from '@/components/admin/team-members';
import { TeamAnalytics } from '@/components/admin/team-analytics';
import { EnhancedAddMemberModal } from '@/components/admin/enhanced-add-member-modal';
import {
  ArrowLeftIcon,
  UserGroupIcon,
  PencilIcon,
  TrashIcon,
  UserPlusIcon,
  ChartBarIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';

type TabType = 'overview' | 'members' | 'analytics';

export default function TeamDetailsPage() {
  const { currentTenant } = useTenant();
  const router = useRouter();
  const params = useParams();
  const teamId = params.id as string;

  const [team, setTeam] = useState<TeamWithDetails | null>(null);
  const [members, setMembers] = useState<TeamMember[]>([]);
  const [filteredMembers, setFilteredMembers] = useState<TeamMember[]>([]);
  const [activeTab, setActiveTab] = useState<TabType>('overview');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [roleFilter, setRoleFilter] = useState<string>('all');
  const [showAddMemberModal, setShowAddMemberModal] = useState(false);

  useEffect(() => {
    if (currentTenant && teamId) {
      fetchTeamDetails();
      fetchMembers();
    }
  }, [currentTenant, teamId]);

  useEffect(() => {
    // Filter members based on search and role filter
    let filtered = members;

    if (searchTerm) {
      filtered = filtered.filter(member =>
        `${member.user.firstName} ${member.user.lastName}`.toLowerCase().includes(searchTerm.toLowerCase()) ||
        member.user.email.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    if (roleFilter !== 'all') {
      filtered = filtered.filter(member => member.role === roleFilter);
    }

    setFilteredMembers(filtered);
  }, [members, searchTerm, roleFilter]);

  const fetchTeamDetails = async () => {
    try {
      const response = await fetch(`/api/teams/${teamId}`);
      if (response.ok) {
        const data = await response.json();
        setTeam(data.data.team);
      } else {
        setError('Failed to fetch team details');
      }
    } catch (error) {
      console.error('Failed to fetch team details:', error);
      setError('Failed to fetch team details');
    } finally {
      setIsLoading(false);
    }
  };

  const fetchMembers = async () => {
    try {
      const response = await fetch(`/api/teams/${teamId}/members`);
      if (response.ok) {
        const data = await response.json();
        setMembers(data.data.members || []);
      }
    } catch (error) {
      console.error('Failed to fetch team members:', error);
    }
  };

  const handleEditTeam = () => {
    router.push(`/settings/team/${teamId}/edit`);
  };

  const handleDeleteTeam = async () => {
    if (!confirm('Are you sure you want to delete this team? This action cannot be undone.')) {
      return;
    }

    try {
      const response = await fetch(`/api/teams/${teamId}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        router.push('/settings/team');
      } else {
        const errorData = await response.json();
        setError(errorData.error || 'Failed to delete team');
      }
    } catch (err) {
      setError('Failed to delete team');
    }
  };

  const handleAddMember = () => {
    setShowAddMemberModal(true);
  };

  const handleMemberAdded = () => {
    setShowAddMemberModal(false);
    fetchMembers(); // Refresh members list
  };

  const handleRemoveMember = async (userId: string) => {
    if (!confirm('Are you sure you want to remove this member from the team?')) {
      return;
    }

    try {
      const response = await fetch(`/api/teams/${teamId}/members`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ userId }),
      });

      if (response.ok) {
        fetchMembers(); // Refresh members list
      } else {
        const errorData = await response.json();
        setError(errorData.error || 'Failed to remove member');
      }
    } catch (err) {
      setError('Failed to remove member');
    }
  };

  const handleBackToTeams = () => {
    router.push('/settings/team');
  };

  if (!currentTenant) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <ExclamationTriangleIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-500">No tenant context available</p>
        </div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <PageLayout title="Loading..." description="Loading team details...">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </PageLayout>
    );
  }

  if (error || !team) {
    return (
      <PageLayout title="Error" description="Failed to load team details">
        <div className="text-center py-12">
          <ExclamationTriangleIcon className="w-12 h-12 text-red-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Error</h3>
          <p className="text-gray-500 mb-4">{error || 'Team not found'}</p>
          <Button onClick={handleBackToTeams}>
            <ArrowLeftIcon className="w-4 h-4 mr-2" />
            Back to Teams
          </Button>
        </div>
      </PageLayout>
    );
  }

  const actions = (
    <div className="flex items-center gap-2">
      <Button variant="outline" onClick={handleBackToTeams}>
        <ArrowLeftIcon className="w-4 h-4 mr-2" />
        Back to Teams
      </Button>
      <PermissionGate
        resource={PermissionResource.USERS}
        action={PermissionAction.MANAGE}
      >
        <Button variant="outline" onClick={handleEditTeam}>
          <PencilIcon className="w-4 h-4 mr-2" />
          Edit Team
        </Button>
        <Button variant="destructive" onClick={handleDeleteTeam}>
          <TrashIcon className="w-4 h-4 mr-2" />
          Delete Team
        </Button>
      </PermissionGate>
    </div>
  );

  const tabs = [
    { id: 'overview' as TabType, name: 'Overview', icon: UserGroupIcon },
    { id: 'members' as TabType, name: 'Members', icon: UserPlusIcon },
    { id: 'analytics' as TabType, name: 'Analytics', icon: ChartBarIcon },
  ];

  return (
    <PermissionProvider>
      <PageLayout
        title={team.name}
        description={team.description || 'Team details and management'}
        actions={actions}
      >
        <PermissionGate
          resource={PermissionResource.USERS}
          action={PermissionAction.READ}
          fallback={<AccessDeniedMessage />}
        >
          <div className="space-y-6">
            {error && (
              <div className="bg-red-50 border border-red-200 rounded-md p-4">
                <p className="text-red-600">{error}</p>
                <button
                  onClick={() => setError(null)}
                  className="text-red-600 hover:text-red-800 underline mt-2"
                >
                  Dismiss
                </button>
              </div>
            )}

            {/* Tab Navigation */}
            <div className="border-b border-gray-200">
              <nav className="-mb-px flex space-x-8">
                {tabs.map((tab) => {
                  const Icon = tab.icon;
                  return (
                    <button
                      key={tab.id}
                      onClick={() => setActiveTab(tab.id)}
                      className={`flex items-center gap-2 py-2 px-1 border-b-2 font-medium text-sm ${
                        activeTab === tab.id
                          ? 'border-blue-500 text-blue-600'
                          : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                      }`}
                    >
                      <Icon className="w-4 h-4" />
                      {tab.name}
                    </button>
                  );
                })}
              </nav>
            </div>

            {/* Tab Content */}
            {activeTab === 'overview' && (
              <TeamOverview team={team} />
            )}

            {activeTab === 'members' && (
              <TeamMembers
                members={filteredMembers}
                isLoading={false}
                searchTerm={searchTerm}
                roleFilter={roleFilter}
                onSearchChange={setSearchTerm}
                onRoleFilterChange={setRoleFilter}
                onAddMember={handleAddMember}
                onRemoveMember={handleRemoveMember}
              />
            )}

            {activeTab === 'analytics' && (
              <TeamAnalytics team={team} members={members} />
            )}
          </div>
        </PermissionGate>

        {/* Enhanced Add Member Modal */}
        {showAddMemberModal && (
          <EnhancedAddMemberModal
            teamId={teamId}
            onClose={() => setShowAddMemberModal(false)}
            onMemberAdded={handleMemberAdded}
          />
        )}
      </PageLayout>
    </PermissionProvider>
  );
}

function TeamOverview({ team }: { team: TeamWithDetails }) {
  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <Card>
        <CardHeader>
          <CardTitle>Team Information</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <label className="text-sm font-medium text-gray-500">Team Name</label>
            <p className="text-gray-900">{team.name}</p>
          </div>
          {team.description && (
            <div>
              <label className="text-sm font-medium text-gray-500">Description</label>
              <p className="text-gray-900">{team.description}</p>
            </div>
          )}
          <div>
            <label className="text-sm font-medium text-gray-500">Team Color</label>
            <div className="flex items-center gap-2">
              <div 
                className="w-4 h-4 rounded-full border border-gray-300"
                style={{ backgroundColor: team.color || '#6B7280' }}
              />
              <span className="text-gray-900">{team.color || '#6B7280'}</span>
            </div>
          </div>
          <div>
            <label className="text-sm font-medium text-gray-500">Status</label>
            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
              team.isActive 
                ? 'bg-green-100 text-green-800' 
                : 'bg-red-100 text-red-800'
            }`}>
              {team.isActive ? 'Active' : 'Inactive'}
            </span>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Team Structure</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {team.manager && (
            <div>
              <label className="text-sm font-medium text-gray-500">Team Manager</label>
              <div className="flex items-center gap-2 mt-1">
                <ContactAvatar 
                  contact={{
                    firstName: team.manager.firstName,
                    lastName: team.manager.lastName,
                    email: team.manager.email
                  }}
                  size="sm"
                />
                <span className="text-gray-900">
                  {team.manager.firstName} {team.manager.lastName}
                </span>
              </div>
            </div>
          )}
          {team.parentTeam && (
            <div>
              <label className="text-sm font-medium text-gray-500">Parent Team</label>
              <p className="text-gray-900">{team.parentTeam.name}</p>
            </div>
          )}
          <div>
            <label className="text-sm font-medium text-gray-500">Member Count</label>
            <p className="text-gray-900">{team._count?.members || 0} members</p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

function AccessDeniedMessage() {
  return (
    <div className="text-center py-12">
      <ExclamationTriangleIcon className="w-12 h-12 text-red-400 mx-auto mb-4" />
      <h3 className="text-lg font-medium text-gray-900 mb-2">Access Denied</h3>
      <p className="text-gray-500">
        You don't have permission to view team details. Contact your administrator for access.
      </p>
    </div>
  );
}
