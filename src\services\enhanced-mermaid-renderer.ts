import { MermaidRenderRequest, MermaidRenderResponse } from '@/types/proposal';
import { prisma } from '@/lib/prisma';
import fs from 'fs/promises';
import path from 'path';
import { mermaidPngConverter, MermaidToPngOptions } from './mermaid-png-converter';

/**
 * Enhanced Mermaid Renderer Service using the mermaid npm package
 * This service provides server-side rendering of Mermaid diagrams to images
 */
export class EnhancedMermaidRendererService {
  private uploadsDir: string;
  private mermaidInitialized = false;

  constructor() {
    this.uploadsDir = path.join(process.cwd(), 'uploads');
  }

  /**
   * Initialize Mermaid for server-side rendering
   */
  private async initializeMermaid() {
    if (this.mermaidInitialized) return;

    try {
      // Dynamic import of mermaid for server-side usage
      const { default: mermaid } = await import('mermaid');
      
      // Initialize mermaid with server-side configuration
      mermaid.initialize({
        startOnLoad: false,
        theme: 'default',
        securityLevel: 'loose',
        fontFamily: 'Arial, sans-serif',
        fontSize: 14,
        flowchart: {
          useMaxWidth: true,
          htmlLabels: true,
          curve: 'basis'
        },
        sequence: {
          useMaxWidth: true,
          wrap: true
        },
        gantt: {
          useMaxWidth: true
        },
        journey: {
          useMaxWidth: true
        },
        timeline: {
          useMaxWidth: true
        }
      });

      this.mermaidInitialized = true;
    } catch (error) {
      console.error('Failed to initialize Mermaid:', error);
      throw new Error('Mermaid initialization failed');
    }
  }

  /**
   * Render Mermaid chart to SVG using the mermaid library
   */
  async renderChart(
    chartId: string,
    tenantId: string,
    options: Partial<MermaidRenderRequest> = {}
  ): Promise<MermaidRenderResponse> {
    try {
      // Initialize Mermaid if not already done
      await this.initializeMermaid();

      // Get chart from database
      const chart = await prisma.proposalChart.findFirst({
        where: {
          id: chartId,
          tenantId
        }
      });

      if (!chart) {
        throw new Error('Chart not found');
      }

      // Prepare render request
      const renderRequest: MermaidRenderRequest = {
        mermaidCode: chart.mermaidCode,
        title: chart.title,
        format: options.format || 'svg',
        theme: options.theme || 'default',
        width: options.width || 800,
        height: options.height || 600
      };

      // Render the Mermaid diagram based on format
      let renderedData: Buffer;
      if (renderRequest.format === 'png') {
        // Use PNG converter for PNG format
        const pngOptions: MermaidToPngOptions = {
          theme: renderRequest.theme as any,
          width: renderRequest.width,
          height: renderRequest.height,
          backgroundColor: 'white'
        };
        const result = await mermaidPngConverter.convertToPngWithFallback(chart.mermaidCode, pngOptions);
        renderedData = result.pngBuffer;
      } else {
        // Use SVG rendering for SVG format
        renderedData = await this.renderMermaidToSvg(renderRequest);
      }

      // Generate file path for the rendered image
      const fileName = `chart_${chartId}_${Date.now()}.${renderRequest.format}`;
      const chartDir = path.join(this.uploadsDir, tenantId, 'charts');
      const imagePath = path.join(chartDir, fileName);
      const relativeImagePath = path.join(tenantId, 'charts', fileName);

      // Ensure directory exists
      await fs.mkdir(chartDir, { recursive: true });

      // Save the rendered image
      await fs.writeFile(imagePath, renderedData);

      // Update chart record with rendered image path
      await prisma.proposalChart.update({
        where: { id: chartId },
        data: {
          renderedImagePath: relativeImagePath,
          status: 'rendered'
        }
      });

      const response: MermaidRenderResponse = {
        imagePath: relativeImagePath,
        imageUrl: `/api/charts/${chartId}/image`,
        width: renderRequest.width!,
        height: renderRequest.height!,
        format: renderRequest.format
      };

      return response;

    } catch (error) {
      console.error('Error rendering Mermaid chart:', error);
      
      // Update chart status to failed
      await prisma.proposalChart.update({
        where: { id: chartId },
        data: {
          status: 'failed'
        }
      }).catch(console.error);

      throw new Error(`Failed to render chart: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Render Mermaid code to SVG using the mermaid library
   */
  private async renderMermaidToSvg(request: MermaidRenderRequest): Promise<Buffer> {
    try {
      // Dynamic import of mermaid
      const { default: mermaid } = await import('mermaid');

      // Set theme if different from default
      if (request.theme && request.theme !== 'default') {
        mermaid.initialize({
          theme: request.theme as any,
          startOnLoad: false,
          securityLevel: 'loose'
        });
      }

      // Generate unique ID for this render
      const elementId = `mermaid-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;

      // Render the diagram
      const { svg } = await mermaid.render(elementId, request.mermaidCode);

      // Create enhanced SVG with title and styling
      const enhancedSvg = this.enhanceSvg(svg, request);

      return Buffer.from(enhancedSvg, 'utf-8');

    } catch (error) {
      console.error('Error rendering Mermaid to SVG:', error);
      
      // Fallback to placeholder SVG
      const placeholderSvg = this.createPlaceholderSvg(request, error as Error);
      return Buffer.from(placeholderSvg, 'utf-8');
    }
  }

  /**
   * Enhance SVG with title, styling, and metadata
   */
  private enhanceSvg(svg: string, request: MermaidRenderRequest): string {
    // Extract the SVG content (remove any existing XML declaration)
    const svgContent = svg.replace(/^<\?xml[^>]*\?>/, '').trim();
    
    // Add title and enhanced styling
    const enhancedSvg = `<?xml version="1.0" encoding="UTF-8"?>
${svgContent.replace(
  /<svg([^>]*)>/,
  `<svg$1>
  <title>${request.title}</title>
  <style>
    .mermaid-title {
      font-family: Arial, sans-serif;
      font-size: 16px;
      font-weight: bold;
      text-anchor: middle;
      fill: #333;
    }
    .mermaid-subtitle {
      font-family: Arial, sans-serif;
      font-size: 12px;
      text-anchor: middle;
      fill: #666;
    }
  </style>`
)}`;

    return enhancedSvg;
  }

  /**
   * Create placeholder SVG when rendering fails
   */
  private createPlaceholderSvg(request: MermaidRenderRequest, error: Error): string {
    return `<?xml version="1.0" encoding="UTF-8"?>
<svg width="${request.width}" height="${request.height}" xmlns="http://www.w3.org/2000/svg">
  <rect width="100%" height="100%" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2"/>
  <text x="50%" y="20%" text-anchor="middle" font-family="Arial" font-size="18" font-weight="bold" fill="#333">
    ${request.title}
  </text>
  <text x="50%" y="35%" text-anchor="middle" font-family="Arial" font-size="14" fill="#e74c3c">
    Rendering Error
  </text>
  <text x="50%" y="50%" text-anchor="middle" font-family="Arial" font-size="12" fill="#666">
    ${error.message.substring(0, 60)}${error.message.length > 60 ? '...' : ''}
  </text>
  <text x="50%" y="65%" text-anchor="middle" font-family="Arial" font-size="10" fill="#999">
    Mermaid Code Preview:
  </text>
  <foreignObject x="10%" y="70%" width="80%" height="25%">
    <div xmlns="http://www.w3.org/1999/xhtml" style="font-family: monospace; font-size: 8px; color: #666; overflow: hidden; text-overflow: ellipsis;">
      ${request.mermaidCode.substring(0, 200)}${request.mermaidCode.length > 200 ? '...' : ''}
    </div>
  </foreignObject>
</svg>`;
  }

  /**
   * Get rendered chart image
   */
  async getChartImage(chartId: string, tenantId: string): Promise<Buffer | null> {
    try {
      const chart = await prisma.proposalChart.findFirst({
        where: {
          id: chartId,
          tenantId
        }
      });

      if (!chart || !chart.renderedImagePath) {
        return null;
      }

      const imagePath = path.join(this.uploadsDir, chart.renderedImagePath);
      const imageData = await fs.readFile(imagePath);
      
      return imageData;

    } catch (error) {
      console.error('Error getting chart image:', error);
      return null;
    }
  }

  /**
   * Render all charts for a proposal
   */
  async renderProposalCharts(proposalId: string, tenantId: string): Promise<MermaidRenderResponse[]> {
    try {
      // Get all charts for the proposal
      const charts = await prisma.proposalChart.findMany({
        where: {
          proposalId,
          tenantId,
          status: 'generated'
        },
        orderBy: {
          orderIndex: 'asc'
        }
      });

      // Render all charts in parallel
      const renderPromises = charts.map(chart =>
        this.renderChart(chart.id, tenantId)
      );

      const results = await Promise.allSettled(renderPromises);
      
      // Return successful renders and log failures
      const successfulRenders: MermaidRenderResponse[] = [];
      results.forEach((result, index) => {
        if (result.status === 'fulfilled') {
          successfulRenders.push(result.value);
        } else {
          console.error(`Failed to render chart ${charts[index].id}:`, result.reason);
        }
      });

      return successfulRenders;

    } catch (error) {
      console.error('Error rendering proposal charts:', error);
      throw error;
    }
  }

  /**
   * Render chart as PNG for document embedding
   */
  async renderChartAsPng(
    chartId: string,
    tenantId: string,
    options: MermaidToPngOptions = {}
  ): Promise<{ pngBuffer: Buffer; imagePath: string }> {
    try {
      // Get chart from database
      const chart = await prisma.proposalChart.findFirst({
        where: {
          id: chartId,
          tenantId
        }
      });

      if (!chart) {
        throw new Error('Chart not found');
      }

      // Convert to PNG
      const pngOptions: MermaidToPngOptions = {
        theme: 'default',
        width: 800,
        height: 600,
        backgroundColor: 'white',
        ...options
      };

      const result = await mermaidPngConverter.convertToPngWithFallback(chart.mermaidCode, pngOptions);

      // Generate file path for the PNG image
      const fileName = `chart_${chartId}_${Date.now()}.png`;
      const chartDir = path.join(this.uploadsDir, tenantId, 'charts');
      const imagePath = path.join(chartDir, fileName);
      const relativeImagePath = path.join(tenantId, 'charts', fileName);

      // Ensure directory exists
      await fs.mkdir(chartDir, { recursive: true });

      // Save the PNG image
      await fs.writeFile(imagePath, result.pngBuffer);

      // Update chart record with PNG image path
      await prisma.proposalChart.update({
        where: { id: chartId },
        data: {
          renderedImagePath: relativeImagePath,
          status: 'rendered'
        }
      });

      return {
        pngBuffer: result.pngBuffer,
        imagePath: imagePath
      };

    } catch (error) {
      console.error('Error rendering chart as PNG:', error);
      throw new Error(`Failed to render chart as PNG: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Validate Mermaid syntax
   */
  async validateMermaidSyntax(mermaidCode: string): Promise<{ valid: boolean; error?: string }> {
    try {
      await this.initializeMermaid();
      const { default: mermaid } = await import('mermaid');

      // Try to parse the mermaid code
      const elementId = `validation-${Date.now()}`;
      await mermaid.render(elementId, mermaidCode);

      return { valid: true };
    } catch (error) {
      return {
        valid: false,
        error: error instanceof Error ? error.message : 'Invalid Mermaid syntax'
      };
    }
  }
}

export const enhancedMermaidRenderer = new EnhancedMermaidRendererService();
