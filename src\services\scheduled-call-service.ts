import { prisma } from '@/lib/prisma';
import { ScheduledCall } from '@prisma/client';
import { z } from 'zod';

// Validation schemas
export const createScheduledCallSchema = z.object({
  leadId: z.string().optional().nullable(),
  contactId: z.string().optional().nullable(),
  assignedTo: z.string().min(1, 'Assigned user is required'),
  title: z.string().min(1, 'Title is required').max(255),
  description: z.string().optional().nullable(),
  callType: z.enum(['phone', 'video', 'in-person']).default('phone'),
  scheduledAt: z.string().datetime('Invalid date format'),
  duration: z.number().min(5).max(480).default(30), // 5 minutes to 8 hours
  timezone: z.string().default('UTC'),
  followUpRequired: z.boolean().default(false),
  nextCallDate: z.string().datetime().optional().nullable(),
});

export const updateScheduledCallSchema = z.object({
  leadId: z.string().optional().nullable(),
  contactId: z.string().optional().nullable(),
  assignedTo: z.string().optional(),
  title: z.string().min(1, 'Title is required').max(255).optional(),
  description: z.string().optional().nullable(),
  callType: z.enum(['phone', 'video', 'in-person']).optional(),
  scheduledAt: z.union([z.string().datetime('Invalid date format'), z.date()]).optional(),
  duration: z.number().min(5).max(480).optional(),
  timezone: z.string().optional(),
  status: z.enum(['scheduled', 'completed', 'cancelled', 'rescheduled']).optional(),
  completedAt: z.union([z.string().datetime(), z.date()]).optional().nullable(),
  callNotes: z.string().optional().nullable(),
  followUpRequired: z.boolean().optional(),
  nextCallDate: z.union([z.string().datetime(), z.date()]).optional().nullable(),
  reminderSent: z.boolean().optional(),
});

export const getScheduledCallsQuerySchema = z.object({
  leadId: z.string().optional(),
  contactId: z.string().optional(),
  assignedTo: z.string().optional(),
  status: z.enum(['scheduled', 'completed', 'cancelled', 'rescheduled']).optional(),
  callType: z.enum(['phone', 'video', 'in-person']).optional(),
  dateFrom: z.string().datetime().optional(),
  dateTo: z.string().datetime().optional(),
  page: z.string().transform(val => parseInt(val) || 1).optional(),
  limit: z.string().transform(val => Math.min(parseInt(val) || 10, 100)).optional(),
  sortBy: z.enum(['scheduledAt', 'createdAt', 'title']).default('scheduledAt'),
  sortOrder: z.enum(['asc', 'desc']).default('asc'),
});

export type CreateScheduledCallData = z.infer<typeof createScheduledCallSchema>;
export type UpdateScheduledCallData = z.infer<typeof updateScheduledCallSchema>;
export type GetScheduledCallsQuery = z.infer<typeof getScheduledCallsQuerySchema>;

export interface ScheduledCallWithRelations extends ScheduledCall {
  lead?: {
    id: string;
    title: string;
    status: string;
    contact?: {
      id: string;
      firstName: string;
      lastName: string;
      email: string | null;
    } | null;
  } | null;
  contact?: {
    id: string;
    firstName: string;
    lastName: string;
    email: string | null;
    phone: string | null;
  } | null;
  scheduledByUser: {
    id: string;
    firstName: string | null;
    lastName: string | null;
    email: string;
  };
  assignedToUser: {
    id: string;
    firstName: string | null;
    lastName: string | null;
    email: string;
  };
}

export class ScheduledCallService {
  /**
   * Create a new scheduled call
   */
  async createScheduledCall(
    tenantId: string,
    data: CreateScheduledCallData,
    scheduledBy: string
  ): Promise<ScheduledCallWithRelations> {
    const validatedData = createScheduledCallSchema.parse(data);

    // Verify that either leadId or contactId is provided
    if (!validatedData.leadId && !validatedData.contactId) {
      throw new Error('Either leadId or contactId must be provided');
    }

    // Verify related entities exist and belong to tenant
    if (validatedData.leadId) {
      await this.verifyLeadExists(validatedData.leadId, tenantId);
    }

    if (validatedData.contactId) {
      await this.verifyContactExists(validatedData.contactId, tenantId);
    }

    // Verify assigned user exists and belongs to tenant
    await this.verifyUserBelongsToTenant(validatedData.assignedTo, tenantId);

    const scheduledCall = await prisma.scheduledCall.create({
      data: {
        ...validatedData,
        scheduledAt: new Date(validatedData.scheduledAt),
        nextCallDate: validatedData.nextCallDate ? new Date(validatedData.nextCallDate) : null,
        tenantId,
        scheduledBy,
      },
      include: this.getIncludeOptions(),
    });

    return scheduledCall as ScheduledCallWithRelations;
  }

  /**
   * Get scheduled calls with filtering and pagination
   */
  async getScheduledCalls(
    tenantId: string,
    query: GetScheduledCallsQuery
  ): Promise<{
    calls: ScheduledCallWithRelations[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  }> {
    const validatedQuery = getScheduledCallsQuerySchema.parse(query);
    const { page = 1, limit = 10, sortBy = 'scheduledAt', sortOrder = 'asc' } = validatedQuery;

    const where: any = {
      tenantId,
    };

    // Apply filters
    if (validatedQuery.leadId) {
      where.leadId = validatedQuery.leadId;
    }

    if (validatedQuery.contactId) {
      where.contactId = validatedQuery.contactId;
    }

    if (validatedQuery.assignedTo) {
      where.assignedTo = validatedQuery.assignedTo;
    }

    if (validatedQuery.status) {
      where.status = validatedQuery.status;
    }

    if (validatedQuery.callType) {
      where.callType = validatedQuery.callType;
    }

    if (validatedQuery.dateFrom || validatedQuery.dateTo) {
      where.scheduledAt = {};
      if (validatedQuery.dateFrom) {
        where.scheduledAt.gte = new Date(validatedQuery.dateFrom);
      }
      if (validatedQuery.dateTo) {
        where.scheduledAt.lte = new Date(validatedQuery.dateTo);
      }
    }

    const [calls, total] = await Promise.all([
      prisma.scheduledCall.findMany({
        where,
        include: this.getIncludeOptions(),
        orderBy: { [sortBy]: sortOrder },
        skip: (page - 1) * limit,
        take: limit,
      }),
      prisma.scheduledCall.count({ where }),
    ]);

    return {
      calls: calls as ScheduledCallWithRelations[],
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  /**
   * Get a specific scheduled call by ID
   */
  async getScheduledCall(
    callId: string,
    tenantId: string
  ): Promise<ScheduledCallWithRelations | null> {
    const call = await prisma.scheduledCall.findFirst({
      where: {
        id: callId,
        tenantId,
      },
      include: this.getIncludeOptions(),
    });

    return call as ScheduledCallWithRelations | null;
  }

  /**
   * Update a scheduled call
   */
  async updateScheduledCall(
    callId: string,
    tenantId: string,
    data: UpdateScheduledCallData,
    updatedBy: string
  ): Promise<ScheduledCallWithRelations> {
    const validatedData = updateScheduledCallSchema.parse(data);

    // Verify call exists and belongs to tenant
    const existingCall = await this.getScheduledCall(callId, tenantId);
    if (!existingCall) {
      throw new Error('Scheduled call not found');
    }

    // Verify related entities if being updated
    if (validatedData.leadId) {
      await this.verifyLeadExists(validatedData.leadId, tenantId);
    }

    if (validatedData.contactId) {
      await this.verifyContactExists(validatedData.contactId, tenantId);
    }

    if (validatedData.assignedTo) {
      await this.verifyUserBelongsToTenant(validatedData.assignedTo, tenantId);
    }

    const updateData: any = { ...validatedData };

    // Convert date strings to Date objects
    if (validatedData.scheduledAt) {
      updateData.scheduledAt = new Date(validatedData.scheduledAt);
    }

    if (validatedData.completedAt) {
      updateData.completedAt = new Date(validatedData.completedAt);
    }

    if (validatedData.nextCallDate) {
      updateData.nextCallDate = new Date(validatedData.nextCallDate);
    }

    const updatedCall = await prisma.scheduledCall.update({
      where: { id: callId },
      data: updateData,
      include: this.getIncludeOptions(),
    });

    return updatedCall as ScheduledCallWithRelations;
  }

  /**
   * Delete a scheduled call
   */
  async deleteScheduledCall(callId: string, tenantId: string): Promise<void> {
    // Verify call exists and belongs to tenant
    const existingCall = await this.getScheduledCall(callId, tenantId);
    if (!existingCall) {
      throw new Error('Scheduled call not found');
    }

    await prisma.scheduledCall.delete({
      where: { id: callId },
    });
  }

  /**
   * Mark call as completed
   */
  async completeCall(
    callId: string,
    tenantId: string,
    callNotes?: string,
    followUpRequired?: boolean,
    nextCallDate?: string
  ): Promise<ScheduledCallWithRelations> {
    // Verify call exists and belongs to tenant
    const existingCall = await this.getScheduledCall(callId, tenantId);
    if (!existingCall) {
      throw new Error('Scheduled call not found');
    }

    const updateData: any = {
      status: 'completed',
      completedAt: new Date(),
      callNotes,
      followUpRequired: followUpRequired || false,
    };

    if (nextCallDate) {
      updateData.nextCallDate = new Date(nextCallDate);
    }

    // Convert dates to ISO strings for validation
    const validationData = {
      ...updateData,
      completedAt: updateData.completedAt.toISOString(),
      nextCallDate: updateData.nextCallDate?.toISOString(),
    };

    // Validate the data
    const validatedData = updateScheduledCallSchema.parse(validationData);

    // Convert back to Date objects for database update
    const finalUpdateData: any = { ...validatedData };
    if (validatedData.completedAt) {
      finalUpdateData.completedAt = new Date(validatedData.completedAt);
    }
    if (validatedData.nextCallDate) {
      finalUpdateData.nextCallDate = new Date(validatedData.nextCallDate);
    }

    // Use raw SQL update until Prisma client is regenerated
    await prisma.$executeRaw`
      UPDATE scheduled_calls
      SET
        status = ${finalUpdateData.status},
        completed_at = ${finalUpdateData.completedAt},
        call_notes = ${finalUpdateData.callNotes || null},
        follow_up_required = ${finalUpdateData.followUpRequired},
        next_call_date = ${finalUpdateData.nextCallDate || null},
        updated_at = NOW()
      WHERE id = ${callId} AND tenant_id = ${tenantId}
    `;

    // Fetch the updated call
    const updatedCall = await this.getScheduledCall(callId, tenantId);
    if (!updatedCall) {
      throw new Error('Failed to retrieve updated call');
    }

    return updatedCall;
  }

  /**
   * Get upcoming calls for a user
   */
  async getUpcomingCalls(
    tenantId: string,
    userId: string,
    limit: number = 5
  ): Promise<ScheduledCallWithRelations[]> {
    const calls = await prisma.scheduledCall.findMany({
      where: {
        tenantId,
        assignedTo: userId,
        status: 'scheduled',
        scheduledAt: {
          gte: new Date(),
        },
      },
      include: this.getIncludeOptions(),
      orderBy: { scheduledAt: 'asc' },
      take: limit,
    });

    return calls as ScheduledCallWithRelations[];
  }

  // Private helper methods
  private getIncludeOptions() {
    return {
      lead: {
        select: {
          id: true,
          title: true,
          status: true,
          contact: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
        },
      },
      contact: {
        select: {
          id: true,
          firstName: true,
          lastName: true,
          email: true,
          phone: true,
        },
      },
      scheduledByUser: {
        select: {
          id: true,
          firstName: true,
          lastName: true,
          email: true,
        },
      },
      assignedToUser: {
        select: {
          id: true,
          firstName: true,
          lastName: true,
          email: true,
        },
      },
    };
  }

  private async verifyLeadExists(leadId: string, tenantId: string): Promise<void> {
    const lead = await prisma.lead.findFirst({
      where: { id: leadId, tenantId },
      select: { id: true },
    });

    if (!lead) {
      throw new Error('Lead not found or does not belong to tenant');
    }
  }

  private async verifyContactExists(contactId: string, tenantId: string): Promise<void> {
    const contact = await prisma.contact.findFirst({
      where: { id: contactId, tenantId },
      select: { id: true },
    });

    if (!contact) {
      throw new Error('Contact not found or does not belong to tenant');
    }
  }

  private async verifyUserBelongsToTenant(userId: string, tenantId: string): Promise<void> {
    const tenantUser = await prisma.tenantUser.findFirst({
      where: { userId, tenantId },
      select: { id: true },
    });

    if (!tenantUser) {
      throw new Error('User does not belong to tenant');
    }
  }
}

export const scheduledCallService = new ScheduledCallService();
