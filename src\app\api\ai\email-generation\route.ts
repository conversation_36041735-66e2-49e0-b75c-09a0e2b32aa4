import { NextResponse } from 'next/server';
import { withPermission } from '@/lib/permission-middleware';
import { aiEmailGenerationService, EmailType, EmailContext } from '@/services/ai-email-generation';
import { PermissionAction, PermissionResource } from '@/types';
import { AuthenticatedRequest } from '@/lib/permission-middleware';

async function postHandler(req: AuthenticatedRequest) {
  try {
    const body = await req.json();
    const { 
      type, 
      context, 
      options = {},
      generateVariations = false,
      variationCount = 3 
    } = body;

    // Validate required fields
    if (!type || !context) {
      return NextResponse.json(
        { error: 'Email type and context are required' },
        { status: 400 }
      );
    }

    // Validate email type
    const validTypes: EmailType[] = [
      'cold_outreach', 'follow_up', 'meeting_request', 
      'proposal_follow_up', 'thank_you', 'nurturing', 
      're_engagement', 'introduction'
    ];
    
    if (!validTypes.includes(type)) {
      return NextResponse.json(
        { error: 'Invalid email type' },
        { status: 400 }
      );
    }

    // Validate context
    if (!context.recipientName || !context.senderName) {
      return NextResponse.json(
        { error: 'Recipient name and sender name are required' },
        { status: 400 }
      );
    }

    // Set default locale if not provided
    const emailContext: EmailContext = {
      ...context,
      locale: context.locale || 'en'
    };

    let result;

    if (generateVariations) {
      const count = Math.min(Math.max(1, variationCount), 5); // Limit to 1-5 variations
      const variations = await aiEmailGenerationService.generateEmailVariations(
        type,
        emailContext,
        req.userId,
        count
      );
      
      result = {
        variations,
        count: variations.length
      };
    } else {
      const email = await aiEmailGenerationService.generateEmail(
        type,
        emailContext,
        req.userId,
        options
      );
      
      result = { email };
    }

    return NextResponse.json({
      success: true,
      data: result
    });

  } catch (error) {
    console.error('Email generation API error:', error);
    
    if (error instanceof Error) {
      if (error.message.includes('AI service')) {
        return NextResponse.json(
          { error: 'AI service temporarily unavailable' },
          { status: 503 }
        );
      }
      
      if (error.message.includes('rate limit')) {
        return NextResponse.json(
          { error: 'Rate limit exceeded. Please try again later.' },
          { status: 429 }
        );
      }
    }

    return NextResponse.json(
      { error: 'Failed to generate email' },
      { status: 500 }
    );
  }
}

async function putHandler(req: AuthenticatedRequest) {
  try {
    const body = await req.json();
    const { originalEmail, improvementGoals, context } = body;

    // Validate required fields
    if (!originalEmail || !improvementGoals || !context) {
      return NextResponse.json(
        { error: 'Original email, improvement goals, and context are required' },
        { status: 400 }
      );
    }

    if (!Array.isArray(improvementGoals) || improvementGoals.length === 0) {
      return NextResponse.json(
        { error: 'Improvement goals must be a non-empty array' },
        { status: 400 }
      );
    }

    // Set default locale if not provided
    const emailContext: EmailContext = {
      ...context,
      locale: context.locale || 'en'
    };

    const improvedEmail = await aiEmailGenerationService.improveEmail(
      originalEmail,
      improvementGoals,
      emailContext,
      req.userId
    );

    return NextResponse.json({
      success: true,
      data: { improvedEmail }
    });

  } catch (error) {
    console.error('Email improvement API error:', error);
    return NextResponse.json(
      { error: 'Failed to improve email' },
      { status: 500 }
    );
  }
}

async function getHandler(req: AuthenticatedRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const locale = (searchParams.get('locale') as 'en' | 'ar') || 'en';

    const templates = await aiEmailGenerationService.getEmailTemplates(
      req.tenantId,
      locale
    );

    return NextResponse.json({
      success: true,
      data: { templates }
    });

  } catch (error) {
    console.error('Email templates API error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch email templates' },
      { status: 500 }
    );
  }
}

// Export with permission middleware
export const POST = withPermission({
  resource: PermissionResource.CONTACTS,
  action: PermissionAction.CREATE
})(postHandler);

export const PUT = withPermission({
  resource: PermissionResource.CONTACTS,
  action: PermissionAction.UPDATE
})(putHandler);

export const GET = withPermission({
  resource: PermissionResource.CONTACTS,
  action: PermissionAction.READ
})(getHandler);
