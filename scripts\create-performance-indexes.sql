-- Performance Optimization Indexes Creation Script
-- Run this script to create performance indexes for the CRM system
-- This script can be run safely multiple times (uses IF NOT EXISTS)

-- ============================================================================
-- TENANT-SCOPED COMPOSITE INDEXES FOR COMMON QUERY PATTERNS
-- ============================================================================

-- Contacts optimization
CREATE INDEX IF NOT EXISTS idx_contacts_tenant_status_created 
ON contacts(tenant_id, status, created_at);

CREATE INDEX IF NOT EXISTS idx_contacts_tenant_owner_status 
ON contacts(tenant_id, owner_id, status) WHERE owner_id IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_contacts_tenant_company 
ON contacts(tenant_id, company_id) WHERE company_id IS NOT NULL;

-- Leads optimization (with soft delete consideration)
CREATE INDEX IF NOT EXISTS idx_leads_tenant_status_created 
ON leads(tenant_id, status, created_at) WHERE deleted_at IS NULL;

CREATE INDEX IF NOT EXISTS idx_leads_tenant_owner_status 
ON leads(tenant_id, owner_id, status) WHERE deleted_at IS NULL AND owner_id IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_leads_tenant_source 
ON leads(tenant_id, lead_source_id) WHERE deleted_at IS NULL AND lead_source_id IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_leads_tenant_priority 
ON leads(tenant_id, priority, created_at) WHERE deleted_at IS NULL;

-- Opportunities optimization
CREATE INDEX IF NOT EXISTS idx_opportunities_tenant_stage_created 
ON opportunities(tenant_id, stage, created_at);

CREATE INDEX IF NOT EXISTS idx_opportunities_tenant_owner_stage 
ON opportunities(tenant_id, owner_id, stage) WHERE owner_id IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_opportunities_tenant_value 
ON opportunities(tenant_id, value) WHERE value IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_opportunities_tenant_close_date 
ON opportunities(tenant_id, expected_close_date) WHERE expected_close_date IS NOT NULL;

-- Companies optimization
CREATE INDEX IF NOT EXISTS idx_companies_tenant_industry 
ON companies(tenant_id, industry) WHERE industry IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_companies_tenant_size 
ON companies(tenant_id, size) WHERE size IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_companies_tenant_owner 
ON companies(tenant_id, owner_id) WHERE owner_id IS NOT NULL;

-- ============================================================================
-- DASHBOARD ANALYTICS OPTIMIZATION INDEXES
-- ============================================================================

-- Monthly aggregation indexes for dashboard metrics
CREATE INDEX IF NOT EXISTS idx_leads_tenant_created_month 
ON leads(tenant_id, date_trunc('month', created_at)) WHERE deleted_at IS NULL;

CREATE INDEX IF NOT EXISTS idx_opportunities_tenant_created_month 
ON opportunities(tenant_id, date_trunc('month', created_at));

CREATE INDEX IF NOT EXISTS idx_contacts_tenant_created_month 
ON contacts(tenant_id, date_trunc('month', created_at));

CREATE INDEX IF NOT EXISTS idx_companies_tenant_created_month 
ON companies(tenant_id, date_trunc('month', created_at));

-- Weekly aggregation indexes
CREATE INDEX IF NOT EXISTS idx_leads_tenant_created_week 
ON leads(tenant_id, date_trunc('week', created_at)) WHERE deleted_at IS NULL;

CREATE INDEX IF NOT EXISTS idx_opportunities_tenant_created_week 
ON opportunities(tenant_id, date_trunc('week', created_at));

-- Conversion tracking indexes
CREATE INDEX IF NOT EXISTS idx_leads_tenant_converted 
ON leads(tenant_id, is_converted, converted_at) WHERE is_converted = true;

-- ============================================================================
-- SEARCH AND FILTERING OPTIMIZATION INDEXES
-- ============================================================================

-- Full-text search indexes for leads
CREATE INDEX IF NOT EXISTS idx_leads_search_gin
ON leads USING gin(to_tsvector('english', title || ' ' || COALESCE(description, '')))
WHERE deleted_at IS NULL;

-- Email search optimization
CREATE INDEX IF NOT EXISTS idx_contacts_email_search
ON contacts(tenant_id, email) WHERE deleted_at IS NULL AND email IS NOT NULL;

-- Name search optimization
CREATE INDEX IF NOT EXISTS idx_contacts_name_search
ON contacts(tenant_id, first_name, last_name) WHERE deleted_at IS NULL;

-- Company name search
CREATE INDEX IF NOT EXISTS idx_companies_name_search
ON companies(tenant_id, name) WHERE deleted_at IS NULL;

-- Lead score optimization for high-value leads
CREATE INDEX IF NOT EXISTS idx_leads_high_score
ON leads(tenant_id, score DESC) WHERE deleted_at IS NULL AND score >= 70;

-- ============================================================================
-- PERMISSION AND SECURITY OPTIMIZATION INDEXES
-- ============================================================================

-- User permission lookups
CREATE INDEX IF NOT EXISTS idx_user_permissions_lookup
ON user_permissions(user_id, tenant_id, resource, action);

-- Role permission lookups
CREATE INDEX IF NOT EXISTS idx_role_permissions_lookup
ON role_permissions(role_id, resource, action);

-- Tenant user relationships
CREATE INDEX IF NOT EXISTS idx_tenant_users_active
ON tenant_users(tenant_id, user_id, is_active) WHERE is_active = true;

-- ============================================================================
-- AUDIT AND MONITORING OPTIMIZATION INDEXES
-- ============================================================================

-- Audit log performance indexes
CREATE INDEX IF NOT EXISTS idx_audit_logs_tenant_resource
ON audit_logs(tenant_id, resource_type, created_at DESC);

CREATE INDEX IF NOT EXISTS idx_audit_logs_user_recent
ON audit_logs(user_id, created_at DESC) WHERE created_at >= NOW() - INTERVAL '30 days';

-- Performance metrics indexes
CREATE INDEX IF NOT EXISTS idx_performance_metrics_recent
ON performance_metrics(metric_type, timestamp DESC) WHERE timestamp >= NOW() - INTERVAL '7 days';

-- ============================================================================
-- FOREIGN KEY PERFORMANCE INDEXES
-- ============================================================================

-- Activities optimization
CREATE INDEX IF NOT EXISTS idx_activities_tenant_user_date 
ON activities(tenant_id, user_id, created_at) WHERE user_id IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_activities_tenant_type_date 
ON activities(tenant_id, activity_type, created_at) WHERE activity_type IS NOT NULL;

-- Documents optimization
CREATE INDEX IF NOT EXISTS idx_documents_tenant_type 
ON documents(tenant_id, mime_type) WHERE mime_type IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_documents_tenant_public 
ON documents(tenant_id, is_public);

-- Audit logs optimization
CREATE INDEX IF NOT EXISTS idx_audit_logs_tenant_created 
ON audit_logs(tenant_id, created_at);

CREATE INDEX IF NOT EXISTS idx_audit_logs_tenant_user_action 
ON audit_logs(tenant_id, user_id, action) WHERE user_id IS NOT NULL;

-- ============================================================================
-- USER AND PERMISSION OPTIMIZATION INDEXES
-- ============================================================================

-- User roles optimization
CREATE INDEX IF NOT EXISTS idx_user_roles_tenant_user 
ON user_roles(tenant_id, user_id);

CREATE INDEX IF NOT EXISTS idx_user_roles_tenant_role 
ON user_roles(tenant_id, role_id);

-- Role permissions optimization
CREATE INDEX IF NOT EXISTS idx_role_permissions_tenant_role 
ON role_permissions(tenant_id, role_id);

CREATE INDEX IF NOT EXISTS idx_role_permissions_tenant_permission 
ON role_permissions(tenant_id, permission_id);

-- Tenant users optimization
CREATE INDEX IF NOT EXISTS idx_tenant_users_user_status 
ON tenant_users(user_id, status);

CREATE INDEX IF NOT EXISTS idx_tenant_users_tenant_role 
ON tenant_users(tenant_id, role);

-- ============================================================================
-- PARTIAL INDEXES FOR FILTERED QUERIES
-- ============================================================================

-- Active records only indexes
CREATE INDEX IF NOT EXISTS idx_leads_active_tenant_owner 
ON leads(tenant_id, owner_id) 
WHERE deleted_at IS NULL AND status != 'converted' AND owner_id IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_opportunities_active_tenant_stage 
ON opportunities(tenant_id, stage) 
WHERE stage NOT IN ('won', 'lost');

-- Recent activity indexes (last 30 days)
CREATE INDEX IF NOT EXISTS idx_activities_recent_tenant 
ON activities(tenant_id, created_at) 
WHERE created_at > NOW() - INTERVAL '30 days';

-- Unread notifications
CREATE INDEX IF NOT EXISTS idx_notifications_unread_tenant 
ON notifications(tenant_id, created_at) 
WHERE read_at IS NULL;

-- High-value opportunities
CREATE INDEX IF NOT EXISTS idx_opportunities_high_value_tenant 
ON opportunities(tenant_id, value, stage) 
WHERE value > 10000;

-- ============================================================================
-- SPECIALIZED INDEXES FOR REPORTING AND ANALYTICS
-- ============================================================================

-- Lead source effectiveness
CREATE INDEX IF NOT EXISTS idx_leads_source_conversion 
ON leads(tenant_id, lead_source_id, is_converted, created_at) 
WHERE lead_source_id IS NOT NULL;

-- Opportunity stage progression
CREATE INDEX IF NOT EXISTS idx_opportunity_stage_history_progression 
ON opportunity_stage_history(tenant_id, opportunity_id, changed_at);

-- Usage metrics optimization
CREATE INDEX IF NOT EXISTS idx_usage_metrics_tenant_period 
ON usage_metrics(tenant_id, metric_name, period_start);

-- ============================================================================
-- MAINTENANCE AND MONITORING INDEXES
-- ============================================================================

-- Email logs optimization
CREATE INDEX IF NOT EXISTS idx_email_logs_tenant_status_date 
ON email_logs(tenant_id, status, sent_at) WHERE sent_at IS NOT NULL;

-- Scheduled meetings optimization
CREATE INDEX IF NOT EXISTS idx_scheduled_meetings_tenant_date 
ON scheduled_meetings(tenant_id, meeting_date) WHERE meeting_date IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_scheduled_meetings_tenant_assigned 
ON scheduled_meetings(tenant_id, assigned_to_id, meeting_date) 
WHERE assigned_to_id IS NOT NULL AND meeting_date IS NOT NULL;

-- Team management optimization
CREATE INDEX IF NOT EXISTS idx_team_members_tenant_team 
ON team_members(tenant_id, team_id);

CREATE INDEX IF NOT EXISTS idx_team_members_tenant_user 
ON team_members(tenant_id, user_id);

-- ============================================================================
-- VERIFICATION AND COMPLETION
-- ============================================================================

-- Check created indexes
SELECT 
    schemaname,
    tablename,
    indexname,
    indexdef
FROM pg_indexes 
WHERE schemaname = 'public' 
    AND indexname LIKE 'idx_%'
    AND indexname NOT LIKE '%_pkey'
ORDER BY tablename, indexname;

-- Performance optimization completion message
DO $$
BEGIN
    RAISE NOTICE '✅ Performance optimization indexes created successfully!';
    RAISE NOTICE '📊 Database performance should be significantly improved.';
    RAISE NOTICE '🔄 Consider running ANALYZE to update table statistics.';
    RAISE NOTICE '📈 Monitor query performance using the admin dashboard.';
END $$;
