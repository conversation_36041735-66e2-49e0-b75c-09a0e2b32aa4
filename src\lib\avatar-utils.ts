/**
 * Utility functions for avatar generation and styling
 */

// Predefined gradient combinations for consistent avatar colors
const AVATAR_GRADIENTS = [
  'from-blue-500 to-purple-600',
  'from-purple-500 to-pink-600', 
  'from-pink-500 to-rose-600',
  'from-rose-500 to-orange-600',
  'from-orange-500 to-yellow-600',
  'from-yellow-500 to-green-600',
  'from-green-500 to-emerald-600',
  'from-emerald-500 to-teal-600',
  'from-teal-500 to-cyan-600',
  'from-cyan-500 to-blue-600',
  'from-indigo-500 to-purple-600',
  'from-violet-500 to-purple-600',
] as const;

/**
 * Generate a consistent gradient class based on a string (usually name)
 * This ensures the same person always gets the same color
 */
export function getAvatarGradient(input: string): string {
  if (!input) return AVATAR_GRADIENTS[0];
  
  // Create a simple hash from the input string
  let hash = 0;
  for (let i = 0; i < input.length; i++) {
    const char = input.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32-bit integer
  }
  
  // Use absolute value and modulo to get a consistent index
  const index = Math.abs(hash) % AVATAR_GRADIENTS.length;
  return AVATAR_GRADIENTS[index];
}

/**
 * Generate initials from a full name
 */
export function getInitials(firstName: string, lastName?: string): string {
  if (!firstName) return '?';
  
  const first = firstName.charAt(0).toUpperCase();
  const last = lastName ? lastName.charAt(0).toUpperCase() : '';
  
  return first + last;
}

/**
 * Generate initials from a single name string
 */
export function getInitialsFromName(name: string): string {
  if (!name) return '?';
  
  const parts = name.trim().split(/\s+/);
  if (parts.length === 1) {
    return parts[0].charAt(0).toUpperCase();
  }
  
  return parts[0].charAt(0).toUpperCase() + parts[parts.length - 1].charAt(0).toUpperCase();
}

/**
 * Get avatar props for consistent styling
 */
export interface AvatarProps {
  src?: string;
  alt: string;
  fallbackText: string;
  gradientClass: string;
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl';
}

export function getAvatarProps(
  firstName: string,
  lastName?: string,
  avatarUrl?: string,
  size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl' = 'md'
): AvatarProps {
  const fullName = lastName ? `${firstName} ${lastName}` : firstName;
  const initials = getInitials(firstName, lastName);
  const gradient = getAvatarGradient(fullName);
  
  return {
    src: avatarUrl,
    alt: fullName,
    fallbackText: initials,
    gradientClass: `bg-gradient-to-br ${gradient}`,
    size
  };
}

/**
 * Size classes for different avatar sizes
 */
export const AVATAR_SIZES = {
  xs: 'h-5 w-5 text-xs',
  sm: 'h-6 w-6 text-xs',
  md: 'h-8 w-8 text-sm',
  lg: 'h-10 w-10 text-sm',
  xl: 'h-12 w-12 text-base',
  '2xl': 'h-16 w-16 text-lg'
} as const;

/**
 * Get size classes for avatar
 */
export function getAvatarSizeClasses(size: keyof typeof AVATAR_SIZES): string {
  return AVATAR_SIZES[size];
}
