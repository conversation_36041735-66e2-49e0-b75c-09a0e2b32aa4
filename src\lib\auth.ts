import { NextAuthOptions } from 'next-auth';
import { PrismaAdapter } from '@next-auth/prisma-adapter';
import CredentialsProvider from 'next-auth/providers/credentials';
import bcrypt from 'bcryptjs';
import { prisma } from './prisma';
import { JWT } from 'next-auth/jwt';

// Refresh token function
async function refreshAccessToken(token: JWT): Promise<JWT> {
  try {
    // In a real implementation, you would call your refresh token endpoint
    // For now, we'll just extend the token if it's still valid
    const user = await prisma.user.findUnique({
      where: { id: token.id as string },
      include: {
        tenantUsers: {
          include: {
            tenant: true
          }
        }
      }
    });

    if (!user) {
      throw new Error('User not found');
    }

    return {
      ...token,
      accessTokenExpires: Date.now() + 24 * 60 * 60 * 1000, // 24 hours
      error: undefined,
    };
  } catch (error) {
    console.error('Error refreshing access token:', error);
    return {
      ...token,
      error: 'RefreshAccessTokenError',
    };
  }
}

export const authOptions: NextAuthOptions = {
  adapter: PrismaAdapter(prisma),
  providers: [
    CredentialsProvider({
      name: 'credentials',
      credentials: {
        email: { label: 'Email', type: 'email' },
        password: { label: 'Password', type: 'password' }
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          return null;
        }

        const user = await prisma.user.findUnique({
          where: {
            email: credentials.email
          },
          include: {
            tenantUsers: {
              include: {
                tenant: true
              }
            }
          }
        });

        if (!user || !user.passwordHash) {
          return null;
        }

        const isPasswordValid = await bcrypt.compare(
          credentials.password,
          user.passwordHash
        );

        if (!isPasswordValid) {
          return null;
        }

        // Update last login
        await prisma.user.update({
          where: { id: user.id },
          data: { lastLogin: new Date() }
        });

        return {
          id: user.id,
          email: user.email,
          name: `${user.firstName} ${user.lastName}`,
          image: user.avatarUrl || undefined,
          avatarUrl: user.avatarUrl || undefined,
          phone: user.phone || undefined,
          locale: user.locale,
          isPlatformAdmin: user.isPlatformAdmin,
          tenants: user.tenantUsers.map(tu => ({
            id: tu.tenant.id,
            name: tu.tenant.name,
            slug: tu.tenant.slug,
            role: tu.role,
            isTenantAdmin: tu.isTenantAdmin
          }))
        };
      }
    })
  ],
  session: {
    strategy: 'jwt',
    maxAge: 24 * 60 * 60, // 24 hours
  },
  jwt: {
    maxAge: 24 * 60 * 60, // 24 hours
  },
  callbacks: {
    async jwt({ token, user, account }) {
      // Initial sign in
      if (user) {
        token.id = user.id;
        token.email = user.email;
        token.name = user.name;
        token.avatarUrl = user.avatarUrl;
        token.phone = user.phone;
        token.locale = user.locale;
        token.isPlatformAdmin = user.isPlatformAdmin;
        token.tenants = user.tenants;
        token.accessTokenExpires = Date.now() + 24 * 60 * 60 * 1000; // 24 hours
        token.refreshToken = account?.refresh_token;
      }

      // Return previous token if the access token has not expired yet
      if (Date.now() < (token.accessTokenExpires as number)) {
        return token;
      }

      // Access token has expired, try to update it
      return await refreshAccessToken(token);
    },
    async session({ session, token }) {
      if (token) {
        session.user.id = token.id as string;
        session.user.email = token.email as string;
        session.user.name = token.name as string;
        session.user.avatarUrl = token.avatarUrl as string;
        session.user.phone = token.phone as string;
        session.user.locale = token.locale as string;
        session.user.isPlatformAdmin = token.isPlatformAdmin as boolean;
        session.user.tenants = token.tenants as any[];
        // Set a simple access token for API identification
        session.accessToken = `nextauth-${token.id}`;
      }
      return session;
    },
  },
  pages: {
    signIn: '/auth/signin',
    error: '/auth/error',
  },
  secret: process.env.NEXTAUTH_SECRET,
};

// Utility function to get user's tenants
export async function getUserTenants(userId: string) {
  return await prisma.tenantUser.findMany({
    where: { userId },
    include: {
      tenant: {
        include: {
          subscriptions: {
            include: {
              plan: true
            }
          }
        }
      }
    }
  });
}

// Utility function to check if user has access to tenant
export async function hasAccessToTenant(userId: string, tenantId: string): Promise<boolean> {
  const tenantUser = await prisma.tenantUser.findUnique({
    where: {
      tenantId_userId: {
        tenantId,
        userId
      }
    }
  });

  return !!tenantUser && tenantUser.status === 'active';
}
