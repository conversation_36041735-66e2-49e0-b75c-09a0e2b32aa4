import { prisma } from '@/lib/prisma';
import { PermissionAction, PermissionResource, PermissionCategory, Permission, Role } from '@/types';
import { redis } from '@/lib/redis';

export interface UserPermissions {
  userId: string;
  tenantId: string;
  roles: Role[];
  directPermissions: Permission[];
  effectivePermissions: Permission[];
  lastUpdated: Date;
}

export interface PermissionCheckResult {
  allowed: boolean;
  reason?: string;
  requiredPermission?: string;
}

export class PermissionService {
  private static readonly CACHE_TTL = 300; // 5 minutes
  private static readonly CACHE_PREFIX = 'permissions:';

  /**
   * Check if user has specific permission
   */
  async hasPermission(
    userId: string,
    tenantId: string,
    resource: PermissionResource,
    action: PermissionAction
  ): Promise<boolean> {
    try {
      const userPermissions = await this.getUserEffectivePermissions(userId, tenantId);
      const permissionName = `${resource}.${action.toLowerCase()}`;
      
      return userPermissions.effectivePermissions.some(p => 
        p.name === permissionName || 
        p.name === `${resource}.*` || 
        p.name === '*'
      );
    } catch (error) {
      console.error('Permission check failed:', error);
      return false;
    }
  }

  /**
   * Check if user can access specific resource (ownership-based)
   */
  async canAccessResource(
    userId: string,
    tenantId: string,
    resource: PermissionResource,
    action: PermissionAction,
    resourceOwnerId?: string
  ): Promise<PermissionCheckResult> {
    try {
      // Check basic permission
      const hasBasicPermission = await this.hasPermission(userId, tenantId, resource, action);
      
      if (!hasBasicPermission) {
        return {
          allowed: false,
          reason: 'Insufficient permissions',
          requiredPermission: `${resource}.${action.toLowerCase()}`
        };
      }

      // If no resource owner specified, basic permission is enough
      if (!resourceOwnerId) {
        return { allowed: true };
      }

      // Check if user has manage permission (can access all resources)
      const canManageAll = await this.hasPermission(userId, tenantId, resource, PermissionAction.MANAGE);
      
      if (canManageAll) {
        return { allowed: true };
      }

      // Check if user owns the resource
      if (userId === resourceOwnerId) {
        return { allowed: true };
      }

      return {
        allowed: false,
        reason: 'Access denied: You can only access your own resources',
        requiredPermission: `${resource}.manage`
      };
    } catch (error) {
      console.error('Resource access check failed:', error);
      return {
        allowed: false,
        reason: 'Permission check failed'
      };
    }
  }

  /**
   * Get user's effective permissions (including role inheritance)
   */
  async getUserEffectivePermissions(userId: string, tenantId: string): Promise<UserPermissions> {
    const cacheKey = `${PermissionService.CACHE_PREFIX}${userId}:${tenantId}`;

    try {
      // Try to get from cache first
      try {
        const cached = await redis.get(cacheKey);
        if (cached) {
          return JSON.parse(cached);
        }
      } catch (redisError) {
        console.warn('Redis cache error, proceeding without cache:', redisError);
      }

      // Get user roles with permissions
      const userRoles = await prisma.userRole.findMany({
        where: {
          userId,
          tenantId,
          OR: [
            { expiresAt: null },
            { expiresAt: { gt: new Date() } }
          ]
        },
        include: {
          role: {
            include: {
              rolePermissions: {
                include: {
                  permission: true
                }
              },
              parentRole: {
                include: {
                  rolePermissions: {
                    include: {
                      permission: true
                    }
                  }
                }
              }
            }
          }
        }
      });



      // Collect all permissions from roles and role hierarchy
      const allPermissions = new Map<string, Permission>();
      const roles: Role[] = [];

      for (const userRole of userRoles) {
        const role = userRole.role;
        roles.push({
          id: role.id,
          name: role.name,
          description: role.description || '',
          isSystemRole: role.isSystemRole,
          parentRoleId: role.parentRoleId || undefined,
          permissions: [],
          childRoles: []
        });

        // Add permissions from current role
        for (const rolePermission of role.rolePermissions) {
          const permission = rolePermission.permission;
          allPermissions.set(permission.id, {
            id: permission.id,
            name: permission.name,
            resource: permission.resource as PermissionResource,
            action: permission.action as PermissionAction,
            category: permission.category as PermissionCategory,
            description: permission.description || ''
          });
        }

        // Add permissions from parent role (inheritance)
        if (role.parentRole) {
          for (const parentPermission of role.parentRole.rolePermissions) {
            const permission = parentPermission.permission;
            allPermissions.set(permission.id, {
              id: permission.id,
              name: permission.name,
              resource: permission.resource as PermissionResource,
              action: permission.action as PermissionAction,
              category: permission.category as PermissionCategory,
              description: permission.description || ''
            });
          }
        }
      }

      const effectivePermissions = Array.from(allPermissions.values());



      const userPermissions: UserPermissions = {
        userId,
        tenantId,
        roles,
        directPermissions: [], // TODO: Implement direct user permissions if needed
        effectivePermissions,
        lastUpdated: new Date()
      };

      // Cache the result
      try {
        await redis.setex(cacheKey, PermissionService.CACHE_TTL, JSON.stringify(userPermissions));
      } catch (cacheError) {
        console.warn('Failed to cache permissions:', cacheError);
        // Continue without caching
      }

      return userPermissions;
    } catch (error) {
      console.error('Failed to get user permissions:', error);
      throw error;
    }
  }

  /**
   * Invalidate user permission cache
   */
  async invalidateUserPermissions(userId: string, tenantId?: string): Promise<void> {
    try {
      if (tenantId) {
        const cacheKey = `${PermissionService.CACHE_PREFIX}${userId}:${tenantId}`;
        await redis.del(cacheKey);
      } else {
        // Invalidate all tenant permissions for user
        const pattern = `${PermissionService.CACHE_PREFIX}${userId}:*`;
        const keys = await redis.keys(pattern);
        if (keys.length > 0) {
          await redis.del(...keys);
        }
      }

      // Also invalidate session cache
      await this.invalidateUserSession(userId);
    } catch (error) {
      console.error('Failed to invalidate permission cache:', error);
    }
  }

  /**
   * Invalidate user session cache to force session refresh
   */
  async invalidateUserSession(userId: string): Promise<void> {
    try {
      // Invalidate NextAuth session cache
      const sessionKeys = await redis.keys(`next-auth.session-token:*`);
      for (const key of sessionKeys) {
        const session = await redis.get(key);
        if (session) {
          const sessionData = JSON.parse(session);
          if (sessionData.user?.id === userId) {
            await redis.del(key);
          }
        }
      }

      // Invalidate our custom session cache
      await redis.del(`session:${userId}`);
    } catch (error) {
      console.error('Failed to invalidate session cache:', error);
    }
  }

  /**
   * Invalidate all caches for users with a specific role
   */
  async invalidateRoleUserCaches(roleId: string, tenantId: string): Promise<void> {
    try {
      // Get all users with this role
      const userRoles = await prisma.userRole.findMany({
        where: { roleId, tenantId },
        select: { userId: true }
      });

      // Invalidate cache for each user
      for (const userRole of userRoles) {
        await this.invalidateUserPermissions(userRole.userId, tenantId);
      }
    } catch (error) {
      console.error('Failed to invalidate role user caches:', error);
    }
  }

  /**
   * Check multiple permissions at once
   */
  async hasAnyPermission(
    userId: string,
    tenantId: string,
    permissions: Array<{ resource: PermissionResource; action: PermissionAction }>
  ): Promise<boolean> {
    try {
      const userPermissions = await this.getUserEffectivePermissions(userId, tenantId);
      
      return permissions.some(({ resource, action }) => {
        const permissionName = `${resource}.${action.toLowerCase()}`;
        return userPermissions.effectivePermissions.some(p => 
          p.name === permissionName || 
          p.name === `${resource}.*` || 
          p.name === '*'
        );
      });
    } catch (error) {
      console.error('Multiple permission check failed:', error);
      return false;
    }
  }

  /**
   * Check if user has all specified permissions
   */
  async hasAllPermissions(
    userId: string,
    tenantId: string,
    permissions: Array<{ resource: PermissionResource; action: PermissionAction }>
  ): Promise<boolean> {
    try {
      const userPermissions = await this.getUserEffectivePermissions(userId, tenantId);
      
      return permissions.every(({ resource, action }) => {
        const permissionName = `${resource}.${action.toLowerCase()}`;
        return userPermissions.effectivePermissions.some(p => 
          p.name === permissionName || 
          p.name === `${resource}.*` || 
          p.name === '*'
        );
      });
    } catch (error) {
      console.error('All permissions check failed:', error);
      return false;
    }
  }

  /**
   * Get permissions for a specific role
   */
  async getRolePermissions(roleId: string, tenantId: string): Promise<Permission[]> {
    try {
      const rolePermissions = await prisma.rolePermission.findMany({
        where: {
          roleId,
          tenantId
        },
        include: {
          permission: true
        }
      });

      return rolePermissions.map(rp => ({
        id: rp.permission.id,
        name: rp.permission.name,
        resource: rp.permission.resource as PermissionResource,
        action: rp.permission.action as PermissionAction,
        category: rp.permission.category as PermissionCategory,
        description: rp.permission.description || ''
      }));
    } catch (error) {
      console.error('Failed to get role permissions:', error);
      throw error;
    }
  }

  /**
   * Check if user is tenant admin
   */
  async isTenantAdmin(userId: string, tenantId: string): Promise<boolean> {
    try {
      const tenantUser = await prisma.tenantUser.findUnique({
        where: {
          tenantId_userId: {
            tenantId,
            userId
          }
        }
      });

      return tenantUser?.isTenantAdmin || false;
    } catch (error) {
      console.error('Failed to check tenant admin status:', error);
      return false;
    }
  }
}

// Export singleton instance
export const permissionService = new PermissionService();
