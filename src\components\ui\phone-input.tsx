'use client';

import React, { forwardRef } from 'react';
import PhoneInput from 'react-phone-number-input';
import 'react-phone-number-input/style.css';
import { cn } from '@/lib/utils';
import { Label } from '@/components/ui/label';

interface PhoneInputProps {
  value?: string;
  onChange?: (value: string | undefined) => void;
  placeholder?: string;
  disabled?: boolean;
  error?: string;
  label?: string;
  required?: boolean;
  className?: string;
  id?: string;
}

export const CustomPhoneInput = forwardRef<HTMLInputElement, PhoneInputProps>(
  ({
    value,
    onChange,
    placeholder = "Enter phone number",
    disabled = false,
    error,
    label,
    required = false,
    className,
    id,
    ...props
  }, ref) => {
    const inputId = id || `phone-input-${Math.random().toString(36).substr(2, 9)}`;

    return (
      <div className="space-y-2">
        {label && (
          <Label htmlFor={inputId} className="text-sm font-medium leading-none">
            {label}
            {required && <span className="text-red-500 ml-1">*</span>}
          </Label>
        )}
        
        <div className="relative">
          <PhoneInput
            id={inputId}
            value={value}
            onChange={onChange}
            placeholder={placeholder}
            disabled={disabled}
            defaultCountry="US"
            international
            countryCallingCodeEditable={false}
            className={cn(
              'phone-input-container',
              error && 'phone-input-error',
              disabled && 'phone-input-disabled',
              className
            )}
            {...props}
          />
        </div>

        {error && (
          <p className="text-sm text-destructive">
            {error}
          </p>
        )}

        <style jsx global>{`
          .phone-input-container {
            position: relative;
          }

          .phone-input-container .PhoneInputInput {
            flex: 1;
            height: 2.5rem;
            width: 100%;
            border-radius: 0.375rem;
            border: 1px solid hsl(var(--border));
            background-color: hsl(var(--background));
            padding: 0.5rem 0.75rem;
            font-size: 0.875rem;
            line-height: 1.25rem;
            color: hsl(var(--foreground));
            transition: all 0.2s ease-in-out;
            outline: none;
          }

          .phone-input-container .PhoneInputInput:focus {
            outline: 2px solid transparent;
            outline-offset: 2px;
            border-color: hsl(var(--ring));
            box-shadow: 0 0 0 2px hsl(var(--ring) / 0.2);
          }

          .phone-input-container .PhoneInputInput::placeholder {
            color: hsl(var(--muted-foreground));
          }

          .phone-input-container .PhoneInputInput:disabled {
            cursor: not-allowed;
            opacity: 0.5;
          }

          .phone-input-container .PhoneInputCountrySelect {
            margin-right: 0.5rem;
            border: none;
            background: transparent;
            font-size: 0.875rem;
            color: hsl(var(--foreground));
            cursor: pointer;
            outline: none;
          }

          .phone-input-container .PhoneInputCountrySelect:focus {
            outline: 2px solid transparent;
            outline-offset: 2px;
            border-radius: 0.25rem;
            box-shadow: 0 0 0 2px hsl(var(--ring) / 0.2);
          }

          .phone-input-container .PhoneInputCountrySelectArrow {
            color: hsl(var(--muted-foreground));
            margin-left: 0.25rem;
          }

          .phone-input-container .PhoneInputCountryIcon {
            width: 1.25rem;
            height: 1rem;
            margin-right: 0.5rem;
          }

          .phone-input-error .PhoneInputInput {
            border-color: hsl(var(--destructive));
          }

          .phone-input-error .PhoneInputInput:focus {
            border-color: hsl(var(--destructive));
            box-shadow: 0 0 0 2px hsl(var(--destructive) / 0.2);
          }

          .phone-input-disabled .PhoneInputInput,
          .phone-input-disabled .PhoneInputCountrySelect {
            cursor: not-allowed;
            opacity: 0.5;
          }

          /* Dark mode support */
          .dark .phone-input-container .PhoneInputInput {
            background-color: hsl(var(--background));
            border-color: hsl(var(--border));
            color: hsl(var(--foreground));
          }

          .dark .phone-input-container .PhoneInputInput:focus {
            border-color: hsl(var(--ring));
            box-shadow: 0 0 0 2px hsl(var(--ring) / 0.2);
          }

          .dark .phone-input-container .PhoneInputCountrySelect {
            color: hsl(var(--foreground));
          }

          .dark .phone-input-container .PhoneInputCountrySelectArrow {
            color: hsl(var(--muted-foreground));
          }

          /* Responsive design */
          @media (max-width: 640px) {
            .phone-input-container .PhoneInputInput {
              font-size: 1rem;
            }
          }
        `}</style>
      </div>
    );
  }
);

CustomPhoneInput.displayName = 'CustomPhoneInput';

export { CustomPhoneInput as PhoneInput };
