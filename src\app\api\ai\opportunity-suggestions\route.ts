import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { hasPermission } from '@/lib/permissions';
import { PermissionResource, PermissionAction } from '@/types';
import { aiOpportunityService } from '@/services/ai-opportunity-service';
import { z } from 'zod';

// Request validation schema
const suggestionRequestSchema = z.object({
  title: z.string().optional(),
  description: z.string().optional(),
  companyId: z.string().optional(),
  contactId: z.string().optional(),
  industry: z.string().optional(),
  companySize: z.string().optional(),
});

/**
 * POST /api/ai/opportunity-suggestions
 * Generate AI suggestions for opportunity creation
 */
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user's current tenant
    const currentTenant = session.user.tenants?.[0];
    if (!currentTenant) {
      return NextResponse.json({ error: 'No tenant access' }, { status: 403 });
    }

    // Check permission
    const hasAccess = await hasPermission(
      PermissionResource.OPPORTUNITIES,
      PermissionAction.CREATE,
      currentTenant.id
    );

    if (!hasAccess) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const body = await request.json();
    const validatedData = suggestionRequestSchema.parse(body);

    // Get additional context if company or contact is provided
    let companyContext = null;
    let contactContext = null;

    if (validatedData.companyId) {
      try {
        const companyResponse = await fetch(`${process.env.NEXTAUTH_URL}/api/companies/${validatedData.companyId}`, {
          headers: {
            'Cookie': request.headers.get('cookie') || '',
          },
        });
        if (companyResponse.ok) {
          companyContext = await companyResponse.json();
        }
      } catch (error) {
        console.warn('Failed to fetch company context:', error);
      }
    }

    if (validatedData.contactId) {
      try {
        const contactResponse = await fetch(`${process.env.NEXTAUTH_URL}/api/contacts/${validatedData.contactId}`, {
          headers: {
            'Cookie': request.headers.get('cookie') || '',
          },
        });
        if (contactResponse.ok) {
          contactContext = await contactResponse.json();
        }
      } catch (error) {
        console.warn('Failed to fetch contact context:', error);
      }
    }

    // Generate AI suggestions
    const suggestions = await generateOpportunitySuggestions({
      title: validatedData.title,
      description: validatedData.description,
      company: companyContext,
      contact: contactContext,
      industry: validatedData.industry || companyContext?.industry,
      companySize: validatedData.companySize || companyContext?.size,
      tenantId: currentTenant.id,
    });

    return NextResponse.json(suggestions);
  } catch (error) {
    console.error('Error generating opportunity suggestions:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request data', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to generate suggestions' },
      { status: 500 }
    );
  }
}

/**
 * Generate opportunity suggestions using AI
 */
async function generateOpportunitySuggestions(context: {
  title?: string;
  description?: string;
  company?: any;
  contact?: any;
  industry?: string;
  companySize?: string;
  tenantId: string;
}) {
  try {
    // Base suggestions without AI (fallback)
    const baseSuggestions = {
      suggestedValue: null,
      suggestedCloseDate: null,
      suggestedTags: [],
      suggestedPriority: 'medium',
      confidence: 0.5,
      reasoning: 'Based on general industry standards',
    };

    // Industry-based value suggestions
    const industryValueMap: Record<string, { min: number; max: number }> = {
      'technology': { min: 10000, max: 500000 },
      'healthcare': { min: 25000, max: 1000000 },
      'finance': { min: 50000, max: 2000000 },
      'manufacturing': { min: 15000, max: 750000 },
      'retail': { min: 5000, max: 200000 },
      'education': { min: 10000, max: 300000 },
      'real-estate': { min: 20000, max: 1500000 },
      'consulting': { min: 15000, max: 500000 },
    };

    // Company size multipliers
    const sizeMultipliers: Record<string, number> = {
      'startup': 0.5,
      'small': 0.7,
      'medium': 1.0,
      'large': 1.5,
      'enterprise': 2.5,
    };

    // Generate value suggestion
    if (context.industry && industryValueMap[context.industry.toLowerCase()]) {
      const baseRange = industryValueMap[context.industry.toLowerCase()];
      const sizeMultiplier = context.companySize ? (sizeMultipliers[context.companySize.toLowerCase()] || 1.0) : 1.0;
      
      const minValue = Math.round(baseRange.min * sizeMultiplier);
      const maxValue = Math.round(baseRange.max * sizeMultiplier);
      
      // Suggest a value in the middle-upper range
      baseSuggestions.suggestedValue = Math.round(minValue + (maxValue - minValue) * 0.7);
      baseSuggestions.confidence = 0.8;
      baseSuggestions.reasoning = `Based on ${context.industry} industry standards and ${context.companySize || 'typical'} company size`;
    }

    // Generate close date suggestion (30-90 days from now)
    const daysToClose = context.companySize === 'enterprise' ? 90 : 
                       context.companySize === 'large' ? 60 : 45;
    
    const suggestedCloseDate = new Date();
    suggestedCloseDate.setDate(suggestedCloseDate.getDate() + daysToClose);
    baseSuggestions.suggestedCloseDate = suggestedCloseDate.toISOString();

    // Generate tags based on context
    const suggestedTags = [];
    
    if (context.industry) {
      suggestedTags.push(context.industry.toLowerCase());
    }
    
    if (context.companySize) {
      suggestedTags.push(`${context.companySize}-business`);
    }
    
    if (context.title) {
      // Extract potential tags from title
      const titleWords = context.title.toLowerCase().split(' ');
      const commonBusinessTerms = ['software', 'consulting', 'implementation', 'migration', 'upgrade', 'integration'];
      titleWords.forEach(word => {
        if (commonBusinessTerms.includes(word) && !suggestedTags.includes(word)) {
          suggestedTags.push(word);
        }
      });
    }

    baseSuggestions.suggestedTags = suggestedTags.slice(0, 5); // Limit to 5 tags

    // Determine priority based on company size and value
    if (baseSuggestions.suggestedValue) {
      if (baseSuggestions.suggestedValue > 500000) {
        baseSuggestions.suggestedPriority = 'high';
      } else if (baseSuggestions.suggestedValue > 100000) {
        baseSuggestions.suggestedPriority = 'medium';
      } else {
        baseSuggestions.suggestedPriority = 'low';
      }
    }

    // Try to enhance with AI if available
    try {
      const aiSuggestions = await aiOpportunityService.generateOpportunitySuggestions(
        context,
        context.tenantId
      );
      
      // Merge AI suggestions with base suggestions
      return {
        ...baseSuggestions,
        ...aiSuggestions,
        confidence: Math.max(baseSuggestions.confidence, aiSuggestions?.confidence || 0),
      };
    } catch (aiError) {
      console.warn('AI suggestions failed, using base suggestions:', aiError);
      return baseSuggestions;
    }

  } catch (error) {
    console.error('Error in generateOpportunitySuggestions:', error);
    
    // Return minimal fallback suggestions
    return {
      suggestedValue: 50000,
      suggestedCloseDate: new Date(Date.now() + 45 * 24 * 60 * 60 * 1000).toISOString(),
      suggestedTags: ['new-opportunity'],
      suggestedPriority: 'medium',
      confidence: 0.3,
      reasoning: 'Default suggestions due to processing error',
    };
  }
}
