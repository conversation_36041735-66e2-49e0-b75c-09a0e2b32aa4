import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { handoverChecklistService } from '@/services/handover-checklist';
import { z } from 'zod';

// Request validation schemas
const updateTemplateSchema = z.object({
  name: z.string().min(1, 'Name is required').max(255).optional(),
  description: z.string().optional(),
  category: z.enum(['general', 'technical', 'legal', 'financial']).optional(),
  items: z.array(z.object({
    title: z.string().min(1, 'Title is required'),
    description: z.string().optional(),
    category: z.enum(['general', 'technical', 'legal', 'financial', 'client']).default('general'),
    orderIndex: z.number().int().min(0),
    isRequired: z.boolean().default(true),
    dependsOnIds: z.array(z.string()).default([]),
  })).optional(),
  estimatedDuration: z.number().int().positive().optional(),
  isDefault: z.boolean().optional(),
});

/**
 * GET /api/handover-templates/[id] - Get a specific handover template
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get tenantId from user's tenants array
    let tenantId: string | null = null;
    if (session.user.tenants && session.user.tenants.length > 0) {
      tenantId = session.user.tenants[0].id;
    }

    if (!tenantId) {
      return NextResponse.json({ error: 'Tenant not found' }, { status: 400 });
    }

    const resolvedParams = await params;
    const template = await handoverChecklistService.getTemplateById(tenantId, resolvedParams.id);

    if (!template) {
      return NextResponse.json({ error: 'Template not found' }, { status: 404 });
    }

    return NextResponse.json(template);
  } catch (error) {
    console.error('Error fetching handover template:', error);
    return NextResponse.json(
      { error: 'Failed to fetch handover template' },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/handover-templates/[id] - Update a handover template
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get tenantId from user's tenants array
    let tenantId: string | null = null;
    if (session.user.tenants && session.user.tenants.length > 0) {
      tenantId = session.user.tenants[0].id;
    }

    if (!tenantId) {
      return NextResponse.json({ error: 'Tenant not found' }, { status: 400 });
    }

    const body = await request.json();
    const validatedData = updateTemplateSchema.parse(body);

    const template = await handoverChecklistService.updateTemplate(
      tenantId,
      params.id,
      validatedData,
      session.user.id
    );

    return NextResponse.json(template);
  } catch (error) {
    console.error('Error updating handover template:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request data', details: error.errors },
        { status: 400 }
      );
    }

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to update handover template' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/handover-templates/[id] - Delete a handover template
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get tenantId from user's tenants array
    let tenantId: string | null = null;
    if (session.user.tenants && session.user.tenants.length > 0) {
      tenantId = session.user.tenants[0].id;
    }

    if (!tenantId) {
      return NextResponse.json({ error: 'Tenant not found' }, { status: 400 });
    }

    await handoverChecklistService.deleteTemplate(tenantId, params.id);

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting handover template:', error);
    
    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to delete handover template' },
      { status: 500 }
    );
  }
}
