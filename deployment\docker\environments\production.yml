# Production configuration for CRM Platform
# This file contains production-specific overrides for docker-compose.yml

services:
  # Production configuration for Next.js app
  app:
    build:
      target: runner
    restart: always
    environment:
      - NODE_ENV=production
      # Mermaid PNG generation environment variables for production
      - PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true
      - PUPPETEER_EXECUTABLE_PATH=/usr/bin/chromium-browser
      - PUPPETEER_ARGS=--no-sandbox --disable-setuid-sandbox --disable-dev-shm-usage --disable-accelerated-2d-canvas --no-first-run --no-zygote --disable-gpu --disable-background-timer-throttling --disable-backgrounding-occluded-windows --disable-renderer-backgrounding
      - MERMAID_TEMP_DIR=/tmp/mermaid
      - CHROME_BIN=/usr/bin/chromium-browser
    volumes:
      - mermaid-temp:/tmp/mermaid
    # Increased shared memory for Chrome in production
    shm_size: 4gb
    # Security options for Chrome in container
    security_opt:
      - seccomp:unconfined
    deploy:
      replicas: 2
      update_config:
        parallelism: 1
        delay: 10s
        order: start-first
      restart_policy:
        condition: on-failure
        max_attempts: 3
        window: 120s
      resources:
        limits:
          cpus: '2'
          memory: 2G
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    # Remove ports in production if using a reverse proxy
    # ports:
    #   - "3000:3000"

  # Production configuration for PostgreSQL
  postgres:
    restart: always
    environment:
      - POSTGRES_PASSWORD=${DB_PASSWORD:-admin}  # Use environment variable in production
    volumes:
      - postgres-data:/var/lib/postgresql/data
      # Don't mount init scripts in production
    deploy:
      resources:
        limits:
          cpus: '2'
          memory: 2G
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    # Don't expose ports in production
    ports: []

  # Production configuration for Redis
  redis:
    restart: always
    command: redis-server /usr/local/etc/redis/redis.conf --appendonly yes
    environment:
      - REDIS_PASSWORD=${REDIS_PASSWORD:-}  # Use environment variable in production
    deploy:
      resources:
        limits:
          cpus: '1'
          memory: 1G
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    # Don't expose ports in production
    ports: []

  # Production Nginx reverse proxy
  nginx:
    image: nginx:stable-alpine
    container_name: crm-nginx
    restart: always
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./config/nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./config/nginx/conf.d:/etc/nginx/conf.d:ro
      - ./config/nginx/ssl:/etc/nginx/ssl:ro
      - ./public:/var/www/html:ro
    depends_on:
      - app
    networks:
      - crm-network
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 256M
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    security_opt:
      - no-new-privileges:true

networks:
  crm-network:
    driver: bridge
    ipam:
      driver: default
      config:
        - subnet: **********/16

volumes:
  postgres-data:
    driver: local
  redis-data:
    driver: local
  mermaid-temp:
    driver: local
