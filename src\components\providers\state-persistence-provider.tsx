"use client"

import React, { createContext, useContext, useEffect, useRef } from 'react'
import { usePathname } from 'next/navigation'

interface StatePersistenceContextType {
  saveScrollPosition: (path: string, position: number) => void
  restoreScrollPosition: (path: string) => number | null
  saveFormData: (path: string, data: any) => void
  restoreFormData: (path: string) => any | null
  clearFormData: (path: string) => void
}

const StatePersistenceContext = createContext<StatePersistenceContextType | undefined>(undefined)

export function useStatePersistence() {
  const context = useContext(StatePersistenceContext)
  if (!context) {
    throw new Error('useStatePersistence must be used within a StatePersistenceProvider')
  }
  return context
}

interface StatePersistenceProviderProps {
  children: React.ReactNode
}

export function StatePersistenceProvider({ children }: StatePersistenceProviderProps) {
  const pathname = usePathname()
  const scrollPositions = useRef<Map<string, number>>(new Map())
  const formData = useRef<Map<string, any>>(new Map())

  // Save scroll position when leaving a page
  useEffect(() => {
    const handleScroll = () => {
      const position = window.scrollY
      scrollPositions.current.set(pathname, position)
    }

    const handleBeforeUnload = () => {
      const position = window.scrollY
      scrollPositions.current.set(pathname, position)
      // Save to sessionStorage for persistence across page reloads
      try {
        sessionStorage.setItem('scrollPositions', JSON.stringify(Array.from(scrollPositions.current.entries())))
        sessionStorage.setItem('formData', JSON.stringify(Array.from(formData.current.entries())))
      } catch (e) {
        // Ignore storage errors (e.g., in private browsing mode)
      }
    }

    // Load from sessionStorage on mount (only once)
    const loadSavedData = () => {
      try {
        const savedScrollPositions = sessionStorage.getItem('scrollPositions')
        const savedFormData = sessionStorage.getItem('formData')

        if (savedScrollPositions) {
          const positions = JSON.parse(savedScrollPositions)
          scrollPositions.current = new Map(positions)
        }

        if (savedFormData) {
          const data = JSON.parse(savedFormData)
          formData.current = new Map(data)
        }
      } catch (e) {
        // Ignore parsing errors
      }
    }

    // Only load saved data on initial mount
    if (scrollPositions.current.size === 0) {
      loadSavedData()
    }

    window.addEventListener('scroll', handleScroll, { passive: true })
    window.addEventListener('beforeunload', handleBeforeUnload)

    return () => {
      window.removeEventListener('scroll', handleScroll)
      window.removeEventListener('beforeunload', handleBeforeUnload)
    }
  }, [pathname])

  // Restore scroll position when entering a page
  useEffect(() => {
    // Only restore scroll position if we have a saved position and it's not the initial load
    const savedPosition = scrollPositions.current.get(pathname)
    if (savedPosition !== undefined && savedPosition > 0) {
      const timer = setTimeout(() => {
        window.scrollTo({ top: savedPosition, behavior: 'instant' })
      }, 100) // Small delay to ensure content is rendered

      return () => clearTimeout(timer)
    }
  }, [pathname])

  const saveScrollPosition = (path: string, position: number) => {
    scrollPositions.current.set(path, position)
  }

  const restoreScrollPosition = (path: string) => {
    return scrollPositions.current.get(path) || null
  }

  const saveFormData = (path: string, data: any) => {
    formData.current.set(path, data)
    // Also save to sessionStorage for persistence
    sessionStorage.setItem('formData', JSON.stringify(Array.from(formData.current.entries())))
  }

  const restoreFormData = (path: string) => {
    return formData.current.get(path) || null
  }

  const clearFormData = (path: string) => {
    formData.current.delete(path)
    sessionStorage.setItem('formData', JSON.stringify(Array.from(formData.current.entries())))
  }

  const contextValue = {
    saveScrollPosition,
    restoreScrollPosition,
    saveFormData,
    restoreFormData,
    clearFormData,
  }

  return (
    <StatePersistenceContext.Provider value={contextValue}>
      {children}
    </StatePersistenceContext.Provider>
  )
}
