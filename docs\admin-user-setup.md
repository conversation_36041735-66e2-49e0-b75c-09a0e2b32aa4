# Enhanced Admin User Setup

This document describes the enhanced admin user creation system that uses configurable environment variables and the same bcryptjs hashing system as the main application.

## Overview

The enhanced admin user system provides:
- **Configurable credentials** via environment variables
- **Secure password hashing** using bcryptjs with salt rounds 12
- **Password strength validation** 
- **Idempotent operations** (safe to run multiple times)
- **Comprehensive error handling**
- **Secure password generation** option

## Environment Variables

Configure the initial admin user credentials in your `.env` file:

```bash
# Initial Admin User Configuration
ADMIN_EMAIL="<EMAIL>"
ADMIN_PASSWORD="Admin123!"
ADMIN_FIRST_NAME="Admin"
ADMIN_LAST_NAME="User"
```

### Environment Variable Details

| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `ADMIN_EMAIL` | Admin user email address | `<EMAIL>` | No |
| `ADMIN_PASSWORD` | Admin user password | `Admin123!` | No |
| `ADMIN_FIRST_NAME` | Admin user first name | `Admin` | No |
| `ADMIN_LAST_NAME` | Admin user last name | `User` | No |

## Password Requirements

The system enforces the same password strength requirements as the main application:

- **Minimum 8 characters** long
- **At least one uppercase** letter (A-Z)
- **At least one lowercase** letter (a-z)
- **At least one number** (0-9)
- **At least one special character** (!@#$%^&*()_+-=[]{}|;:,.<>?)

### Password Strength Scoring

The system provides a strength score from 0-5:
- **1 point** for meeting minimum length
- **1 point** for uppercase letters
- **1 point** for lowercase letters
- **1 point** for numbers
- **1 point** for special characters
- **1 bonus point** for 12+ characters
- **1 bonus point** for 16+ characters

## Usage

### 1. Automatic Creation (Recommended)

The admin user is automatically created when running the master seed script:

```bash
npm run db:seed
# or
node scripts/master-seed.js
```

### 2. Standalone Admin User Creation

Use the dedicated admin user creation script:

```bash
# Create admin user with environment variables
node scripts/create-admin-user.js

# Force update existing admin user
node scripts/create-admin-user.js --force

# Generate a secure random password
node scripts/create-admin-user.js --generate

# Only validate password strength
node scripts/create-admin-user.js --validate-only
```

### 3. Command Line Options

| Option | Description |
|--------|-------------|
| `--force` | Force update existing admin user |
| `--generate` | Generate a secure random password |
| `--validate-only` | Only validate password without creating user |

## Security Features

### Password Hashing

- **Algorithm**: bcryptjs
- **Salt Rounds**: 12 (same as main application)
- **Hash Format**: `$2a$12$...` (bcrypt format)

### Secure Password Generation

When using `--generate` flag, the system creates passwords with:
- **16 characters** length
- **Mixed character types** (uppercase, lowercase, numbers, symbols)
- **Cryptographically secure** random generation
- **Shuffled output** to prevent patterns

### Example Generated Password
```
K#9mP2$vL8qR5@Nt
```

## Examples

### Basic Setup

1. **Configure environment variables** in `.env`:
```bash
ADMIN_EMAIL="<EMAIL>"
ADMIN_PASSWORD="MySecurePassword123!"
ADMIN_FIRST_NAME="System"
ADMIN_LAST_NAME="Administrator"
```

2. **Run the seed script**:
```bash
npm run db:seed
```

### Generate Secure Password

```bash
node scripts/create-admin-user.js --generate
```

Output:
```
🔐 Generated secure password: K#9mP2$vL8qR5@Nt
⚠️  Please save this password securely!

✅ Password validation passed (strength score: 5/5)
✅ Admin user created successfully:
   ID: user_admin
   Email: <EMAIL>
   Name: Admin User
   Platform Admin: true
   Status: active

📝 Login Credentials:
   Email: <EMAIL>
   Password: K#9mP2$vL8qR5@Nt (generated)

⚠️  Please change the default password after first login!
```

### Update Existing Admin

```bash
node scripts/create-admin-user.js --force
```

### Validate Password Only

```bash
node scripts/create-admin-user.js --validate-only
```

## Integration with Main Application

The enhanced admin user system integrates seamlessly with the main application:

### Authentication
- Uses the same **NextAuth.js** configuration
- Compatible with **bcryptjs** password verification
- Supports **platform admin** privileges

### Database Schema
- Creates user in the **users** table
- Sets **isPlatformAdmin** to `true`
- Links to tenant via **tenantUsers** table

### Password Management
- Uses the same **password utilities** (`src/lib/password-utils.ts`)
- Compatible with **password change** functionality
- Supports **password reset** flows

## Troubleshooting

### Common Issues

1. **Database Connection Failed**
   ```
   ❌ Failed to initialize Prisma client
   ```
   **Solution**: Ensure database is running and `DATABASE_URL` is correct

2. **Password Validation Failed**
   ```
   ❌ Password validation failed:
   • Password must contain at least one uppercase letter
   ```
   **Solution**: Update password to meet strength requirements

3. **Admin Already Exists**
   ```
   ⚠️  Admin user already exists
   ```
   **Solution**: Use `--force` flag to update existing admin

### Debug Mode

For detailed logging, set environment variable:
```bash
DEBUG=true node scripts/create-admin-user.js
```

## Security Best Practices

1. **Change Default Password**: Always change the default password after first login
2. **Use Strong Passwords**: Follow the password strength requirements
3. **Secure Environment Variables**: Keep `.env` file secure and never commit to version control
4. **Regular Password Updates**: Update admin passwords regularly
5. **Monitor Admin Access**: Log and monitor platform admin activities

## Migration from Old System

If upgrading from the old hardcoded admin system:

1. **Update environment variables** in `.env`
2. **Run the enhanced script** with `--force` flag:
   ```bash
   node scripts/create-admin-user.js --force
   ```
3. **Verify the update** by checking the database or attempting login

The system will automatically update the existing admin user with the new password hash and configuration.
