'use client';

import React, { useState, useEffect } from 'react';
import {
  Mail,
  Clock,
  CheckCircle,
  XCircle,
  Eye,
  MousePointer,
  Calendar,
  User,
  Building,
  Tag,
  Send,
  RefreshCw
} from 'lucide-react';
import { <PERSON>, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { EmptyState } from '@/components/ui/empty-state';
import { Skeleton } from '@/components/ui/skeleton';
import { toastFunctions as toast } from '@/hooks/use-toast';
import { cn } from '@/lib/utils';

interface EmailLog {
  id: string;
  subject: string;
  recipients: string[];
  status: string;
  sentAt: string;
  deliveredAt?: string;
  bouncedAt?: string;
  provider: string;
  tags: string[];
  metadata: Record<string, any>;
  lead?: {
    id: string;
    title: string;
  };
  contact?: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
  };
}

interface EmailHistoryProps {
  leadId?: string;
  contactId?: string;
  className?: string;
}

export function EmailHistory({ leadId, contactId, className }: EmailHistoryProps) {
  const [emails, setEmails] = useState<EmailLog[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadEmailHistory();
  }, [leadId, contactId]);

  const loadEmailHistory = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams();
      if (leadId) params.append('leadId', leadId);
      if (contactId) params.append('contactId', contactId);

      const response = await fetch(`/api/email/history?${params}`);
      const data = await response.json();

      if (data.success) {
        setEmails(data.data.emails);
      } else {
        toast.error('Failed to load email history');
      }
    } catch (error) {
      console.error('Error loading email history:', error);
      toast.error('Failed to load email history');
    } finally {
      setLoading(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'sent':
        return <Mail className="w-4 h-4 text-blue-500" />;
      case 'delivered':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'opened':
        return <Eye className="w-4 h-4 text-purple-500" />;
      case 'clicked':
        return <MousePointer className="w-4 h-4 text-orange-500" />;
      case 'bounced':
        return <XCircle className="w-4 h-4 text-red-500" />;
      case 'failed':
        return <XCircle className="w-4 h-4 text-red-500" />;
      default:
        return <Clock className="w-4 h-4 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'sent':
        return 'bg-blue-100 text-blue-800';
      case 'delivered':
        return 'bg-green-100 text-green-800';
      case 'opened':
        return 'bg-purple-100 text-purple-800';
      case 'clicked':
        return 'bg-orange-100 text-orange-800';
      case 'bounced':
        return 'bg-red-100 text-red-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  // Calculate email statistics
  const totalEmails = emails.length;
  const deliveredEmails = emails.filter(email => email.status === 'delivered').length;

  if (loading) {
    return (
      <Card className={cn("bg-white dark:bg-gray-800 shadow-soft border-0 ring-1 ring-gray-200 dark:ring-gray-700", className)}>
        <CardHeader className="pb-4">
          <CardTitle className="flex items-center space-x-2 text-lg font-semibold">
            <div className="p-2 bg-green-100 dark:bg-green-900/20 rounded-lg">
              <Mail className="w-5 h-5 text-green-600 dark:text-green-400" />
            </div>
            <span>Email History</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            {/* Loading Statistics */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {Array.from({ length: 3 }).map((_, i) => (
                <div key={i} className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4">
                  <Skeleton className="h-4 w-16 mb-2" />
                  <Skeleton className="h-6 w-8" />
                </div>
              ))}
            </div>
            {/* Loading Email Cards */}
            <div className="space-y-4">
              {Array.from({ length: 3 }).map((_, i) => (
                <div key={i} className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4">
                  <div className="space-y-3">
                    <div className="flex items-center space-x-3">
                      <Skeleton className="h-10 w-10 rounded-lg" />
                      <div className="flex-1">
                        <Skeleton className="h-4 w-3/4 mb-2" />
                        <Skeleton className="h-3 w-1/2" />
                      </div>
                    </div>
                    <Skeleton className="h-4 w-full" />
                  </div>
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (emails.length === 0) {
    return (
      <Card className={cn("bg-white dark:bg-gray-800 shadow-soft border-0 ring-1 ring-gray-200 dark:ring-gray-700", className)}>
        <CardHeader className="pb-4">
          <CardTitle className="flex items-center space-x-2 text-lg font-semibold">
            <div className="p-2 bg-green-100 dark:bg-green-900/20 rounded-lg">
              <Mail className="w-5 h-5 text-green-600 dark:text-green-400" />
            </div>
            <span>Email History</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-12">
            <div className="bg-gray-100 dark:bg-gray-700 rounded-full w-20 h-20 flex items-center justify-center mx-auto mb-6">
              <Mail className="h-10 w-10 text-gray-400" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
              No emails sent
            </h3>
            <p className="text-gray-500 dark:text-gray-400 mb-6 max-w-sm mx-auto">
              No emails have been sent to this lead or contact yet. Start engaging with email communication.
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={cn("bg-white dark:bg-gray-800 shadow-soft border-0 ring-1 ring-gray-200 dark:ring-gray-700", className)}>
      <CardHeader className="pb-4">
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center space-x-2 text-lg font-semibold">
            <div className="p-2 bg-green-100 dark:bg-green-900/20 rounded-lg">
              <Mail className="w-5 h-5 text-green-600 dark:text-green-400" />
            </div>
            <span>Email History</span>
            {totalEmails > 0 && (
              <Badge variant="secondary" className="ml-2">
                {totalEmails} total
              </Badge>
            )}
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={loadEmailHistory}
            className="flex items-center gap-2"
          >
            <RefreshCw className="w-4 h-4" />
            Refresh
          </Button>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Email Statistics */}
        {totalEmails > 0 && (
          <>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg p-4">
                <div className="flex items-center space-x-3">
                  <div className="p-2 bg-blue-100 dark:bg-blue-800 rounded-lg">
                    <Send className="w-5 h-5 text-blue-600 dark:text-blue-300" />
                  </div>
                  <div>
                    <p className="text-sm font-medium text-blue-900 dark:text-blue-100">Total Sent</p>
                    <p className="text-2xl font-bold text-blue-700 dark:text-blue-300">{totalEmails}</p>
                  </div>
                </div>
              </div>

              <div className="bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-lg p-4">
                <div className="flex items-center space-x-3">
                  <div className="p-2 bg-green-100 dark:bg-green-800 rounded-lg">
                    <CheckCircle className="w-5 h-5 text-green-600 dark:text-green-300" />
                  </div>
                  <div>
                    <p className="text-sm font-medium text-green-900 dark:text-green-100">Delivered</p>
                    <p className="text-2xl font-bold text-green-700 dark:text-green-300">{deliveredEmails}</p>
                  </div>
                </div>
              </div>
            </div>
          </>
        )}
        {/* Email List */}
        <div className="space-y-4">
          {emails.map((email, index) => (
            <div key={email.id} className={cn(
              "bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4 transition-all duration-200 hover:shadow-md border-l-4",
              email.status === 'delivered' ? 'border-l-green-500' :
              email.status === 'opened' ? 'border-l-purple-500' :
              email.status === 'clicked' ? 'border-l-orange-500' :
              email.status === 'bounced' || email.status === 'failed' ? 'border-l-red-500' :
              'border-l-blue-500'
            )}>
              <div className="flex items-start space-x-4">
                <div className={cn(
                  "flex-shrink-0 p-2 rounded-lg",
                  email.status === 'delivered' ? 'bg-green-100 dark:bg-green-900/30' :
                  email.status === 'opened' ? 'bg-purple-100 dark:bg-purple-900/30' :
                  email.status === 'clicked' ? 'bg-orange-100 dark:bg-orange-900/30' :
                  email.status === 'bounced' || email.status === 'failed' ? 'bg-red-100 dark:bg-red-900/30' :
                  'bg-blue-100 dark:bg-blue-900/30'
                )}>
                  {getStatusIcon(email.status)}
                </div>

                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between mb-3">
                    <h4 className="text-base font-semibold text-gray-900 dark:text-gray-100 truncate">
                      {email.subject}
                    </h4>
                    <Badge className={cn("text-xs", getStatusColor(email.status))}>
                      {email.status}
                    </Badge>
                  </div>

                  {/* Email Details Grid */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3 mb-4">
                    <div className="bg-white dark:bg-gray-600 rounded-lg p-3">
                      <div className="flex items-center space-x-2 mb-2">
                        <User className="w-4 h-4 text-gray-500" />
                        <span className="text-xs font-medium text-gray-600 dark:text-gray-300 uppercase tracking-wide">Recipients</span>
                      </div>
                      <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                        {email.recipients.join(', ')}
                      </p>
                    </div>

                    <div className="bg-white dark:bg-gray-600 rounded-lg p-3">
                      <div className="flex items-center space-x-2 mb-2">
                        <Calendar className="w-4 h-4 text-gray-500" />
                        <span className="text-xs font-medium text-gray-600 dark:text-gray-300 uppercase tracking-wide">Sent Date</span>
                      </div>
                      <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                        {formatDate(email.sentAt)}
                      </p>
                    </div>
                  </div>

                  {/* Lead/Contact Information */}
                  {(email.lead || email.contact) && (
                    <div className="bg-indigo-50 dark:bg-indigo-900/20 rounded-lg p-3 mb-4">
                      <div className="flex items-center space-x-2 mb-2">
                        {email.lead ? <Building className="w-4 h-4 text-indigo-600 dark:text-indigo-400" /> : <User className="w-4 h-4 text-indigo-600 dark:text-indigo-400" />}
                        <span className="text-xs font-medium text-indigo-900 dark:text-indigo-100 uppercase tracking-wide">
                          {email.lead ? 'Lead' : 'Contact'}
                        </span>
                      </div>
                      <p className="font-semibold text-indigo-900 dark:text-indigo-100">
                        {email.lead ? email.lead.title : `${email.contact?.firstName} ${email.contact?.lastName}`}
                      </p>
                    </div>
                  )}

                  {/* Tags and Provider Info */}
                  <div className="flex items-center justify-between pt-3 border-t border-gray-200 dark:border-gray-600">
                    <div className="flex items-center space-x-3">
                      {email.tags.length > 0 && (
                        <div className="flex items-center space-x-2">
                          <Tag className="w-4 h-4 text-gray-400" />
                          <div className="flex flex-wrap gap-1">
                            {email.tags.map((tag, tagIndex) => (
                              <Badge key={tagIndex} variant="secondary" className="text-xs">
                                {tag}
                              </Badge>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>

                    <div className="flex items-center space-x-2">
                      <span className="text-xs text-gray-500 dark:text-gray-400">
                        via {email.provider}
                      </span>
                      {email.metadata.generatedByAI && (
                        <Badge variant="outline" className="text-xs bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300">
                          AI Generated
                        </Badge>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
