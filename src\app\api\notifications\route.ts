import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { 
  unifiedNotificationService, 
  notificationQuerySchema, 
  markAsReadSchema 
} from '@/services/unified-notification-service';
import { z } from 'zod';

/**
 * GET /api/notifications - Get all notifications for current user
 */
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get tenantId from user's tenants array (users have one tenant in this system)
    let tenantId: string | null = null;
    if (session.user.tenants && session.user.tenants.length > 0) {
      tenantId = session.user.tenants[0].id;
    }

    if (!tenantId) {
      return NextResponse.json({ error: 'Tenant not found' }, { status: 400 });
    }

    // Parse and validate query parameters
    const url = new URL(request.url);
    const queryParams = Object.fromEntries(url.searchParams.entries());
    
    const validatedQuery = notificationQuerySchema.parse(queryParams);

    const result = await unifiedNotificationService.getUserNotifications(
      session.user.id,
      tenantId,
      validatedQuery
    );

    return NextResponse.json({
      success: true,
      data: {
        notifications: result.notifications,
        pagination: {
          total: result.total,
          limit: validatedQuery.limit,
          offset: validatedQuery.offset,
          hasMore: result.total > validatedQuery.offset + validatedQuery.limit
        },
        unreadCount: result.unreadCount
      }
    });
  } catch (error) {
    console.error('Error fetching notifications:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid query parameters', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to fetch notifications' },
      { status: 500 }
    );
  }
}

/**
 * PATCH /api/notifications - Mark notifications as read
 */
export async function PATCH(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get tenantId from user's tenants array
    let tenantId: string | null = null;
    if (session.user.tenants && session.user.tenants.length > 0) {
      tenantId = session.user.tenants[0].id;
    }

    if (!tenantId) {
      return NextResponse.json({ error: 'Tenant not found' }, { status: 400 });
    }

    const body = await request.json();
    const validatedData = markAsReadSchema.parse(body);

    const result = await unifiedNotificationService.markAsRead(
      session.user.id,
      tenantId,
      validatedData
    );

    return NextResponse.json({
      success: true,
      data: {
        updated: result.updated
      },
      message: `${result.updated} notification(s) marked as read`
    });
  } catch (error) {
    console.error('Error marking notifications as read:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request data', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to mark notifications as read' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/notifications - Create a new notification (for system use)
 */
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Only allow system/admin users to create notifications
    if (!session.user.isPlatformAdmin) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const body = await request.json();
    
    const createNotificationSchema = z.object({
      userId: z.string(),
      tenantId: z.string(),
      type: z.string(),
      title: z.string().min(1).max(255),
      message: z.string().min(1),
      data: z.record(z.any()).default({}),
      actionUrl: z.string().optional(),
      actionLabel: z.string().optional(),
    });

    const validatedData = createNotificationSchema.parse(body);

    // Create notification using the user notification service
    const { UserNotificationService } = await import('@/services/user-notification-service');
    const userNotificationService = new UserNotificationService();

    await userNotificationService.createPortalNotification({
      userId: validatedData.userId,
      tenantId: validatedData.tenantId,
      type: validatedData.type as any,
      title: validatedData.title,
      message: validatedData.message,
      data: {
        ...validatedData.data,
        actionUrl: validatedData.actionUrl,
        actionLabel: validatedData.actionLabel,
      },
    });

    return NextResponse.json({
      success: true,
      message: 'Notification created successfully'
    }, { status: 201 });
  } catch (error) {
    console.error('Error creating notification:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request data', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to create notification' },
      { status: 500 }
    );
  }
}
