'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { PermissionGate } from '@/components/ui/permission-gate';
import { PermissionResource, PermissionAction } from '@/types';
import { ModernContactForm } from '@/components/contacts/modern-contact-form';
import { Card, CardContent } from '@/components/ui/card';
import { EmptyState } from '@/components/ui/empty-state';
import { FormPageSkeleton } from '@/components/ui/skeleton-loaders';
import { ExclamationTriangleIcon, ArrowLeftIcon } from '@heroicons/react/24/outline';
import { ContactWithRelations } from '@/services/contact-management';
import { PageLayout } from '@/components/layout/page-layout';
import { Button } from '@/components/ui/button';

export default function EditContactPage() {
  const router = useRouter();
  const params = useParams();
  const contactId = params.id as string;

  const [initialLoading, setInitialLoading] = useState(true);
  const [contact, setContact] = useState<ContactWithRelations | null>(null);
  const [error, setError] = useState<string | null>(null);

  // Fetch contact data
  useEffect(() => {
    const fetchContact = async () => {
      try {
        setInitialLoading(true);
        const response = await fetch(`/api/contacts/${contactId}`);
        const data = await response.json();

        if (data.success) {
          setContact(data.data.contact);
        } else {
          setError(data.error || 'Contact not found');
        }
      } catch (err) {
        console.error('Failed to fetch contact:', err);
        setError('Failed to load contact data');
      } finally {
        setInitialLoading(false);
      }
    };

    if (contactId) {
      fetchContact();
    }
  }, [contactId]);

  const handleSave = async (contactData: Record<string, unknown>) => {
    try {
      const response = await fetch(`/api/contacts/${contactId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...contactData,
          companyId: contactData.companyId || null,
          ownerId: contactData.ownerId || null,
        }),
      });

      const data = await response.json();

      if (data.success) {
        router.push(`/contacts/${contactId}`);
      } else {
        throw new Error(data.error || 'Failed to update contact');
      }
    } catch (err) {
      console.error('Error updating contact:', err);
      alert(err instanceof Error ? err.message : 'Failed to update contact. Please try again.');
      throw err;
    }
  };

  const actions = (
    <Button variant="outline" size="sm" onClick={() => router.push('/contacts')}>
      <ArrowLeftIcon className="h-4 w-4 mr-2" />
      Back to Contacts
    </Button>
  );

  if (initialLoading) {
    return (
      <PageLayout
        title="Loading..."
        description="Please wait while we load the contact information"
        actions={actions}
      >
        <FormPageSkeleton />
      </PageLayout>
    );
  }

  if (error || !contact) {
    return (
      <PermissionGate
        resource={PermissionResource.CONTACTS}
        action={PermissionAction.UPDATE}
        fallback={
          <PageLayout>
            <Card>
              <CardContent className="p-8">
                <EmptyState
                  icon={<ExclamationTriangleIcon className="w-12 h-12 text-muted-foreground" />}
                  title="Access Denied"
                  description="You don't have permission to edit contacts. Please contact your administrator for access."
                  action={{
                    label: 'Back to Contacts',
                    onClick: () => router.push('/contacts'),
                    variant: 'primary'
                  }}
                />
              </CardContent>
            </Card>
          </PageLayout>
        }
      >
        <PageLayout
          title="Contact Not Found"
          description="The contact you're looking for doesn't exist or you don't have permission to edit it"
          actions={actions}
        >
          <Card>
            <CardContent className="p-8">
              <EmptyState
                icon={<ExclamationTriangleIcon className="w-12 h-12 text-muted-foreground" />}
                title="Contact Not Found"
                description={error || "The contact you're looking for doesn't exist or you don't have permission to edit it."}
                action={{
                  label: 'Back to Contacts',
                  onClick: () => router.push('/contacts'),
                  variant: 'primary'
                }}
              />
            </CardContent>
          </Card>
        </PageLayout>
      </PermissionGate>
    );
  }

  return (
    <PermissionGate
      resource={PermissionResource.CONTACTS}
      action={PermissionAction.UPDATE}
      fallback={
        <PageLayout>
          <Card>
            <CardContent className="p-8">
              <EmptyState
                icon={<ExclamationTriangleIcon className="w-12 h-12 text-muted-foreground" />}
                title="Access Denied"
                description="You don't have permission to edit contacts. Please contact your administrator for access."
                action={{
                  label: 'Back to Contacts',
                  onClick: () => router.push('/contacts'),
                  variant: 'primary'
                }}
              />
            </CardContent>
          </Card>
        </PageLayout>
      }
    >
      <PageLayout
        title={`Edit ${contact?.firstName || ''} ${contact?.lastName || ''}`}
        description="Update contact information in your CRM system"
        actions={actions}
      >
        <ModernContactForm
          key={contact?.id || 'new'}
          contact={contact}
          onSave={handleSave}
          onCancel={() => router.push('/contacts')}
        />
      </PageLayout>
    </PermissionGate>
  );
}
