'use client';

import React from 'react';
import { PageLayout } from '@/components/layout/page-layout';
import { LeadSourceManagement } from '@/components/lead-sources/lead-source-management';
import { PermissionGate } from '@/components/ui/permission-gate';
import { PermissionResource, PermissionAction } from '@/types';
import { Card, CardContent } from '@/components/ui/card';
import { EmptyState } from '@/components/ui/empty-state';
import { ExclamationTriangleIcon } from '@heroicons/react/24/outline';
import { Button } from '@/components/ui/button';
import { Plus } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useSmoothNavigation } from '@/hooks/use-smooth-navigation';

export default function LeadSourcesPage() {
  const router = useRouter();
  const { navigate } = useSmoothNavigation();

  const actions = (
    <Button onClick={() => router.push('/leads/sources/new')}>
      <Plus className="w-4 h-4 mr-2" />
      Add Lead Source
    </Button>
  );

  return (
    <PermissionGate
      resource={PermissionResource.LEADS}
      action={PermissionAction.READ}
      fallback={
        <PageLayout>
          <Card>
            <CardContent className="p-8">
              <EmptyState
                icon={<ExclamationTriangleIcon className="w-12 h-12 text-muted-foreground" />}
                title="Access Denied"
                description="You don't have permission to view lead sources. Please contact your administrator for access."
                action={{
                  label: 'Back to Dashboard',
                  onClick: () => navigate('/dashboard'),
                  variant: 'primary'
                }}
              />
            </CardContent>
          </Card>
        </PageLayout>
      }
    >
      <PageLayout
        title="Lead Sources"
        description="Manage and organize your lead sources to track where your leads are coming from"
        actions={actions}
      >
        <LeadSourceManagement />
      </PageLayout>
    </PermissionGate>
  );
}
