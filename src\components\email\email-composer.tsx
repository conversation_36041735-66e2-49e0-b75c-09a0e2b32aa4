'use client';

import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import {
  Send,
  Sparkles,
  X,
  Mail,
  Loader2,
  Co<PERSON>,
  Check,
  Wand2,
  Zap
} from 'lucide-react';
import { LeadWithRelations } from '@/services/lead-management';
import { ContactWithRelations } from '@/services/contact-management';
import { EmailContext } from '@/services/ai-email-generation';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { toastFunctions as toast } from '@/hooks/use-toast';
import { MultiEmailInput } from '@/components/ui/multi-email-input';

// Email composition schema
const emailComposerSchema = z.object({
  to: z.array(z.string().email('Valid email address is required')).min(1, 'At least one recipient is required'),
  cc: z.array(z.string().email('Valid email address is required')).optional(),
  bcc: z.array(z.string().email('Valid email address is required')).optional(),
  subject: z.string().min(1, 'Subject is required'),
  content: z.string().min(1, 'Email content is required'),
  emailType: z.enum([
    'cold_outreach',
    'follow_up',
    'meeting_request',
    'proposal_follow_up',
    'thank_you',
    'nurturing',
    're_engagement',
    'introduction'
  ]),
  tone: z.enum(['professional', 'friendly', 'formal', 'casual']),
  priority: z.enum(['high', 'normal', 'low']),
  trackOpens: z.boolean(),
  trackClicks: z.boolean()
});

type EmailComposerData = z.infer<typeof emailComposerSchema>;

interface EmailComposerProps {
  isOpen: boolean;
  onClose: () => void;
  lead?: LeadWithRelations;
  contact?: ContactWithRelations;
  meetingId?: string; // For meeting invitations
  onEmailSent?: (emailLogId: string) => void;
}

const EMAIL_TYPE_OPTIONS = [
  { value: 'cold_outreach', label: 'Cold Outreach' },
  { value: 'follow_up', label: 'Follow Up' },
  { value: 'meeting_request', label: 'Meeting Request' },
  { value: 'proposal_follow_up', label: 'Proposal Follow Up' },
  { value: 'thank_you', label: 'Thank You' },
  { value: 'nurturing', label: 'Nurturing' },
  { value: 're_engagement', label: 'Re-engagement' },
  { value: 'introduction', label: 'Introduction' }
];

const TONE_OPTIONS = [
  { value: 'professional', label: 'Professional' },
  { value: 'friendly', label: 'Friendly' },
  { value: 'formal', label: 'Formal' },
  { value: 'casual', label: 'Casual' }
];

export function EmailComposer({
  isOpen,
  onClose,
  lead,
  contact,
  meetingId,
  onEmailSent
}: EmailComposerProps) {
  const [loading, setLoading] = useState(false);
  const [aiGenerating, setAiGenerating] = useState(false);
  const [aiEnhancing, setAiEnhancing] = useState(false);
  const [contentEnhanced, setContentEnhanced] = useState(false);
  const [aiSuggestions, setAiSuggestions] = useState<string[]>([]);
  const [copiedSuggestion, setCopiedSuggestion] = useState<number | null>(null);
  const [meetingInvitationLoading, setMeetingInvitationLoading] = useState(false);

  const form = useForm<EmailComposerData>({
    resolver: zodResolver(emailComposerSchema),
    defaultValues: {
      to: [],
      cc: [],
      bcc: [],
      subject: '',
      content: '',
      emailType: 'follow_up',
      tone: 'professional',
      priority: 'normal',
      trackOpens: true,
      trackClicks: true
    }
  });

  // Pre-populate form when lead/contact changes
  useEffect(() => {
    if (lead || contact) {
      const recipient = lead?.contact || contact;
      if (recipient?.email) {
        form.setValue('to', [recipient.email]);
      }

      // Set default subject based on context
      if (lead) {
        form.setValue('subject', `Regarding: ${lead.title}`);
      } else if (contact) {
        form.setValue('subject', `Following up with ${contact.firstName} ${contact.lastName}`);
      }
    }
  }, [lead, contact, form]);

  // Load meeting invitation content when meetingId is provided
  useEffect(() => {
    if (meetingId && isOpen) {
      loadMeetingInvitationContent();
    }
  }, [meetingId, isOpen]);

  const loadMeetingInvitationContent = async () => {
    if (!meetingId) return;

    setMeetingInvitationLoading(true);
    try {
      // Get current user info for organizer details
      const currentUser = await getCurrentUser();
      const recipient = lead?.contact || contact;

      const params = new URLSearchParams({
        organizerName: `${currentUser?.firstName || ''} ${currentUser?.lastName || ''}`.trim() || 'Meeting Organizer',
        organizerEmail: currentUser?.email || '<EMAIL>',
        companyName: currentUser?.tenants?.[0]?.name || 'CRM Platform', // Use first tenant name
        recipientEmail: recipient?.email || ''
      });

      const response = await fetch(`/api/meetings/${meetingId}/send-invitation?${params}`);
      const data = await response.json();

      if (data.success) {
        // Pre-populate the form with meeting invitation content
        const recipientEmail = data.data.recipientEmail || recipient?.email || '';
        form.setValue('to', recipientEmail ? [recipientEmail] : []);
        form.setValue('subject', data.data.subject);
        form.setValue('content', data.data.content);
        form.setValue('emailType', 'meeting_request');
        form.setValue('tone', 'professional');

        // Trigger validation after setting values
        await form.trigger();

        // Debug log
        console.log('Form state after meeting content load:', {
          isValid: form.formState.isValid,
          errors: form.formState.errors,
          values: form.getValues()
        });

        toast.success('Meeting invitation content loaded');
      } else {
        throw new Error(data.error || 'Failed to load meeting invitation content');
      }
    } catch (error) {
      console.error('Error loading meeting invitation content:', error);
      toast.error('Failed to load meeting invitation content');
    } finally {
      setMeetingInvitationLoading(false);
    }
  };

  const getCurrentUser = async () => {
    try {
      const response = await fetch('/api/auth/me');
      const data = await response.json();
      return data.success ? data.data.user : null;
    } catch (error) {
      console.error('Error getting current user:', error);
      return null;
    }
  };

  const generateAIContent = async () => {
    if (!lead && !contact) {
      toast.error('Lead or contact information is required for AI generation');
      return;
    }

    setAiGenerating(true);
    try {
      const recipient = lead?.contact || contact;
      if (!recipient) {
        throw new Error('No recipient information available');
      }

      const emailContext: EmailContext = {
        recipientName: `${recipient.firstName} ${recipient.lastName}`,
        recipientEmail: recipient.email || '',
        recipientCompany: lead?.company?.name || 'Unknown Company',
        senderName: 'Sales Team', // This should come from user context
        senderCompany: 'CRM Platform', // This should come from tenant context
        senderJobTitle: 'Sales Representative',
        relationship: lead ? 'new_lead' : 'existing_contact',
        leadScore: lead?.score,
        locale: 'en'
      };

      const emailType = form.getValues('emailType');
      const tone = form.getValues('tone');

      const response = await fetch('/api/ai/email-generation', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          type: emailType,
          context: emailContext,
          options: { tone },
          generateVariations: true,
          variationCount: 3
        })
      });

      const data = await response.json();
      if (!data.success) {
        throw new Error(data.error || 'Failed to generate email content');
      }

      if (data.data.variations && data.data.variations.length > 0) {
        const firstVariation = data.data.variations[0];
        form.setValue('subject', firstVariation.subject);
        form.setValue('content', firstVariation.content);

        // Trigger validation after setting values
        await form.trigger();

        // Debug log
        console.log('Form state after AI generation:', {
          isValid: form.formState.isValid,
          errors: form.formState.errors,
          values: form.getValues()
        });

        // Store suggestions for other variations
        setAiSuggestions(data.data.variations.map((v: any) => v.content));
      }

      toast.success('AI content generated successfully');
    } catch (error) {
      console.error('AI generation error:', error);
      toast.error('Failed to generate AI content');
    } finally {
      setAiGenerating(false);
    }
  };

  const enhanceEmailContent = async () => {
    const currentContent = form.getValues('content');
    const currentSubject = form.getValues('subject');

    if (!currentContent.trim()) {
      toast.error('Please enter some content to enhance');
      return;
    }

    setAiEnhancing(true);
    try {
      const recipient = lead?.contact || contact;
      const emailType = form.getValues('emailType');
      const tone = form.getValues('tone');

      const response = await fetch('/api/ai/email-enhancement', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          content: currentContent,
          subject: currentSubject,
          emailType: emailType,
          tone: tone,
          recipientName: recipient ? `${recipient.firstName} ${recipient.lastName}` : '',
          recipientCompany: lead?.company?.name || '',
          isMeetingInvitation: !!meetingId
        })
      });

      const data = await response.json();
      if (!data.success) {
        throw new Error(data.error || 'Failed to enhance email content');
      }

      if (data.data.enhancedContent) {
        // Only update if the content is actually different
        if (data.data.enhancedContent !== currentContent) {
          form.setValue('content', data.data.enhancedContent);
          if (data.data.enhancedSubject && data.data.enhancedSubject !== currentSubject) {
            form.setValue('subject', data.data.enhancedSubject);
          }

          // Trigger validation after setting values
          await form.trigger();

          setContentEnhanced(true);
          setTimeout(() => setContentEnhanced(false), 3000); // Reset after 3 seconds

          // Show improvements if available
          if (data.data.improvements && data.data.improvements.length > 0) {
            const improvementsList = data.data.improvements.slice(0, 3).join(', ');
            toast.success(`Email enhanced: ${improvementsList}`);
          } else {
            toast.success('Email content enhanced successfully');
          }
        } else {
          toast.info('Content is already well-optimized');
        }
      } else {
        toast.error('No enhanced content received');
      }
    } catch (error) {
      console.error('AI enhancement error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to enhance email content';
      toast.error(`Enhancement failed: ${errorMessage}`);
    } finally {
      setAiEnhancing(false);
    }
  };

  const copySuggestion = async (suggestion: string, index: number) => {
    form.setValue('content', suggestion);

    // Trigger validation after setting value
    await form.trigger();

    setCopiedSuggestion(index);
    setTimeout(() => setCopiedSuggestion(null), 2000);
    toast.success('Content copied to email');
  };

  const sendEmail = async (data: EmailComposerData) => {
    setLoading(true);
    try {
      const emailPayload = {
        to: data.to,
        cc: data.cc && data.cc.length > 0 ? data.cc : undefined,
        bcc: data.bcc && data.bcc.length > 0 ? data.bcc : undefined,
        subject: data.subject,
        html: data.content.replace(/\n/g, '<br>'), // Simple HTML conversion
        text: data.content,
        priority: data.priority,
        leadId: lead?.id,
        contactId: contact?.id,
        trackOpens: data.trackOpens,
        trackClicks: data.trackClicks,
        tags: [data.emailType, data.tone],
        metadata: {
          emailType: data.emailType,
          tone: data.tone,
          generatedByAI: aiSuggestions.length > 0,
          meetingId: meetingId || undefined,
          isMeetingInvitation: !!meetingId
        }
      };

      console.log('📧 Sending email with payload:', emailPayload);

      const response = await fetch('/api/email/send', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(emailPayload)
      });

      console.log('📧 Email API response status:', response.status);

      const result = await response.json();
      console.log('📧 Email API response data:', result);

      if (!result.success) {
        const errorMessage = result.error || 'Failed to send email';
        const errorDetails = result.details || '';
        const debugInfo = result.debug || {};

        console.error('❌ Email sending failed:', {
          error: errorMessage,
          details: errorDetails,
          debug: debugInfo
        });

        // Provide more specific error messages
        if (response.status === 503) {
          throw new Error(`Email service not available: ${errorDetails}`);
        } else if (response.status === 400) {
          throw new Error(`Invalid email data: ${errorDetails}`);
        } else if (response.status === 403) {
          throw new Error('Permission denied: You do not have access to send emails');
        } else {
          throw new Error(errorMessage);
        }
      }

      toast.success('Email sent successfully');

      // If this was a meeting invitation, mark it as sent
      if (meetingId) {
        try {
          await fetch(`/api/meetings/${meetingId}`, {
            method: 'PATCH',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ invitationSent: true })
          });
        } catch (error) {
          console.error('Error updating meeting invitation status:', error);
        }
      }

      onEmailSent?.(result.data.emailLogId);
      onClose();
      form.reset();
    } catch (error) {
      console.error('❌ Email sending error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to send email';
      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    if (!loading && !meetingInvitationLoading && !aiEnhancing) {
      onClose();
      form.reset();
      setAiSuggestions([]);
      setMeetingInvitationLoading(false);
      setAiEnhancing(false);
      setContentEnhanced(false);
    }
  };

  if (!isOpen) return null;

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <Mail className="w-5 h-5" />
            <span>{meetingId ? 'Send Meeting Invitation' : 'Compose Email'}</span>
            {meetingId && (
              <Badge variant="outline" className="ml-2 bg-blue-50 text-blue-700">
                Meeting Invitation
              </Badge>
            )}
            {(lead || contact) && !meetingId && (
              <Badge variant="outline" className="ml-2">
                {lead ? `Lead: ${lead.title}` : `Contact: ${contact?.firstName} ${contact?.lastName}`}
              </Badge>
            )}
          </DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(sendEmail)} className="space-y-6">
            <Tabs defaultValue="compose" className="w-full">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="compose">Compose</TabsTrigger>
                <TabsTrigger value="ai-assist" disabled={!!meetingId}>AI Assist</TabsTrigger>
                <TabsTrigger value="preview">Preview</TabsTrigger>
              </TabsList>

              <TabsContent value="compose" className="space-y-4">
                {/* Email Recipients */}
                <div className="space-y-4">
                  <FormField
                    control={form.control}
                    name="to"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>To *</FormLabel>
                        <FormControl>
                          <MultiEmailInput
                            value={field.value}
                            onChange={field.onChange}
                            placeholder="Enter recipient email addresses..."
                            error={form.formState.errors.to?.message}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="cc"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>CC</FormLabel>
                          <FormControl>
                            <MultiEmailInput
                              value={field.value || []}
                              onChange={field.onChange}
                              placeholder="Enter CC email addresses..."
                              error={form.formState.errors.cc?.message}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="bcc"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>BCC</FormLabel>
                          <FormControl>
                            <MultiEmailInput
                              value={field.value || []}
                              onChange={field.onChange}
                              placeholder="Enter BCC email addresses..."
                              error={form.formState.errors.bcc?.message}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>

                {/* Email Settings */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <FormField
                    control={form.control}
                    name="emailType"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Email Type</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select email type" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {EMAIL_TYPE_OPTIONS.map((option) => (
                              <SelectItem key={option.value} value={option.value}>
                                {option.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="tone"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Tone</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select tone" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {TONE_OPTIONS.map((option) => (
                              <SelectItem key={option.value} value={option.value}>
                                {option.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="priority"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Priority</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select priority" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="low">Low</SelectItem>
                            <SelectItem value="normal">Normal</SelectItem>
                            <SelectItem value="high">High</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                {/* Subject */}
                <FormField
                  control={form.control}
                  name="subject"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Subject *</FormLabel>
                      <FormControl>
                        <Input {...field} placeholder="Email subject" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Content */}
                <FormField
                  control={form.control}
                  name="content"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="flex items-center justify-between">
                        <span>Content *</span>
                        <div className="flex items-center gap-2">
                          {!meetingId && (
                            <Button
                              type="button"
                              variant="outline"
                              size="sm"
                              onClick={generateAIContent}
                              disabled={aiGenerating}
                            >
                              {aiGenerating ? (
                                <Loader2 className="w-4 h-4 animate-spin mr-2" />
                              ) : (
                                <Sparkles className="w-4 h-4 mr-2" />
                              )}
                              Generate with AI
                            </Button>
                          )}
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Button
                                  type="button"
                                  variant="outline"
                                  size="sm"
                                  onClick={enhanceEmailContent}
                                  disabled={aiEnhancing || !field.value?.trim()}
                                  className="bg-gradient-to-r from-purple-50 to-blue-50 hover:from-purple-100 hover:to-blue-100 border-purple-200 text-purple-700 hover:text-purple-800"
                                >
                                  {aiEnhancing ? (
                                    <Loader2 className="w-4 h-4 animate-spin mr-2" />
                                  ) : (
                                    <Zap className="w-4 h-4 mr-2" />
                                  )}
                                  Enhance with AI
                                </Button>
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>Improve clarity, tone, and professionalism of your email content using AI</p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                          {meetingId && meetingInvitationLoading && (
                            <div className="flex items-center text-sm text-muted-foreground">
                              <Loader2 className="w-4 h-4 animate-spin mr-2" />
                              Loading meeting invitation content...
                            </div>
                          )}
                          {meetingId && !meetingInvitationLoading && (
                            <div className="text-sm text-green-600 dark:text-green-400">
                              ✓ Meeting invitation content loaded
                            </div>
                          )}
                          {contentEnhanced && (
                            <div className="text-sm text-purple-600 dark:text-purple-400 animate-pulse">
                              ✨ Content enhanced with AI
                            </div>
                          )}
                        </div>
                      </FormLabel>
                      <FormControl>
                        <Textarea
                          {...field}
                          placeholder="Write your email content here..."
                          className="min-h-[200px]"
                          onChange={(e) => {
                            field.onChange(e);
                            if (contentEnhanced) {
                              setContentEnhanced(false);
                            }
                          }}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </TabsContent>

              <TabsContent value="ai-assist" className="space-y-4">
                <div className="text-center py-8">
                  <Wand2 className="w-12 h-12 mx-auto text-muted-foreground mb-4" />
                  <h3 className="text-lg font-semibold mb-2">AI Email Assistant</h3>
                  <p className="text-muted-foreground mb-4">
                    Generate professional email content using AI based on your lead/contact information
                  </p>
                  
                  <Button
                    type="button"
                    onClick={generateAIContent}
                    disabled={aiGenerating || (!lead && !contact)}
                    className="mb-6"
                  >
                    {aiGenerating ? (
                      <Loader2 className="w-4 h-4 animate-spin mr-2" />
                    ) : (
                      <Sparkles className="w-4 h-4 mr-2" />
                    )}
                    Generate Email Content
                  </Button>

                  {aiSuggestions.length > 0 && (
                    <div className="space-y-4">
                      <Separator />
                      <h4 className="font-medium text-left">AI Suggestions:</h4>
                      {aiSuggestions.map((suggestion, index) => (
                        <div key={index} className="border rounded-lg p-4 text-left">
                          <div className="flex justify-between items-start mb-2">
                            <Badge variant="outline">Variation {index + 1}</Badge>
                            <Button
                              type="button"
                              variant="ghost"
                              size="sm"
                              onClick={() => copySuggestion(suggestion, index)}
                            >
                              {copiedSuggestion === index ? (
                                <Check className="w-4 h-4" />
                              ) : (
                                <Copy className="w-4 h-4" />
                              )}
                            </Button>
                          </div>
                          <p className="text-sm text-muted-foreground whitespace-pre-wrap">
                            {suggestion}
                          </p>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </TabsContent>

              <TabsContent value="preview" className="space-y-4">
                <div className="border rounded-lg p-6 bg-muted/50">
                  <div className="space-y-4">
                    <div className="space-y-2 text-sm">
                      <div><strong>To:</strong> {form.watch('to')?.join(', ') || 'No recipients'}</div>
                      {form.watch('cc') && form.watch('cc')?.length > 0 && (
                        <div><strong>CC:</strong> {form.watch('cc')?.join(', ')}</div>
                      )}
                      {form.watch('bcc') && form.watch('bcc')?.length > 0 && (
                        <div><strong>BCC:</strong> {form.watch('bcc')?.join(', ')}</div>
                      )}
                    </div>
                    <div className="text-sm">
                      <strong>Subject:</strong> {form.watch('subject')}
                    </div>
                    <Separator />
                    <div className="whitespace-pre-wrap text-sm">
                      {form.watch('content')}
                    </div>
                  </div>
                </div>
              </TabsContent>
            </Tabs>

            {/* Action Buttons */}
            <div className="flex justify-between items-center pt-4 border-t">
              <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                <label className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    {...form.register('trackOpens')}
                    className="rounded"
                  />
                  <span>Track Opens</span>
                </label>
                <label className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    {...form.register('trackClicks')}
                    className="rounded"
                  />
                  <span>Track Clicks</span>
                </label>
              </div>
              
              <div className="flex space-x-2">
                <Button
                  type="button"
                  variant="outline"
                  onClick={handleClose}
                  disabled={loading}
                >
                  <X className="w-4 h-4 mr-2" />
                  Cancel
                </Button>
                
                <Button
                  type="submit"
                  disabled={loading || !form.formState.isValid}
                >
                  {loading ? (
                    <Loader2 className="w-4 h-4 animate-spin mr-2" />
                  ) : (
                    <Send className="w-4 h-4 mr-2" />
                  )}
                  Send Email
                </Button>
              </div>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
