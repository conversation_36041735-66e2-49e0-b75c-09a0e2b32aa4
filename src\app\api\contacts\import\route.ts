import { NextRequest, NextResponse } from 'next/server';
import { ContactManagementService, createContactSchema } from '@/services/contact-management';
import { CompanyManagementService } from '@/services/company-management';
import { withPermission, AuthenticatedRequest } from '@/lib/permission-middleware';
import { PermissionResource, PermissionAction } from '@/types';

const contactService = new ContactManagementService();
const companyService = new CompanyManagementService();

interface ImportError {
  row: number;
  field: string;
  message: string;
}

/**
 * POST /api/contacts/import
 * Import contacts from CSV file
 */
export const POST = withPermission({
  resource: PermissionResource.CONTACTS,
  action: PermissionAction.CREATE,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest) => {
  try {
    console.log('Import API called');
    const formData = await req.formData();
    const file = formData.get('file') as File;

    console.log('File received:', file?.name, file?.type, file?.size);

    if (!file) {
      return NextResponse.json(
        { error: 'No file provided' },
        { status: 400 }
      );
    }

    if (!file.name.endsWith('.csv') && file.type !== 'text/csv') {
      return NextResponse.json(
        { error: 'Only CSV files are supported' },
        { status: 400 }
      );
    }

    // Read and parse CSV
    const text = await file.text();
    console.log('CSV text length:', text.length);
    const lines = text.split('\n').filter(line => line.trim());
    console.log('CSV lines count:', lines.length);

    if (lines.length < 2) {
      return NextResponse.json(
        { error: 'CSV file must have at least a header row and one data row' },
        { status: 400 }
      );
    }

    // Parse headers
    const headers = lines[0].split(',').map(h => h.trim().replace(/"/g, '').toLowerCase());
    console.log('CSV headers:', headers);

    // Validate required headers
    const requiredHeaders = ['firstname', 'lastname'];
    const missingHeaders = requiredHeaders.filter(header => !headers.includes(header));

    if (missingHeaders.length > 0) {
      return NextResponse.json(
        { error: `Missing required columns: ${missingHeaders.join(', ')}. Found headers: ${headers.join(', ')}` },
        { status: 400 }
      );
    }

    // Get header indices
    const getHeaderIndex = (headerName: string) => headers.indexOf(headerName.toLowerCase());
    
    const firstNameIndex = getHeaderIndex('firstname');
    const lastNameIndex = getHeaderIndex('lastname');
    const emailIndex = getHeaderIndex('email');
    const phoneIndex = getHeaderIndex('phone');
    const companyIndex = getHeaderIndex('company');

    // Process data rows
    const errors: ImportError[] = [];
    const imported: any[] = [];
    let importedCount = 0;

    // Cache for companies to avoid duplicate lookups
    const companyCache = new Map<string, string>();

    for (let i = 1; i < lines.length; i++) {
      const rowNumber = i + 1;
      const values = lines[i].split(',').map(v => v.trim().replace(/"/g, ''));
      
      try {
        // Extract values
        const firstName = values[firstNameIndex]?.trim();
        const lastName = values[lastNameIndex]?.trim();
        const email = values[emailIndex]?.trim() || null;
        const phone = values[phoneIndex]?.trim() || null;
        const companyName = values[companyIndex]?.trim();

        // Validate required fields
        if (!firstName) {
          errors.push({
            row: rowNumber,
            field: 'firstName',
            message: 'First name is required'
          });
          continue;
        }

        if (!lastName) {
          errors.push({
            row: rowNumber,
            field: 'lastName',
            message: 'Last name is required'
          });
          continue;
        }

        // Validate email format if provided
        if (email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
          errors.push({
            row: rowNumber,
            field: 'email',
            message: 'Invalid email format'
          });
          continue;
        }

        // Check for duplicate email in current tenant
        if (email) {
          const existingContact = await contactService.findContactByEmail(email, req.tenantId);
          if (existingContact) {
            errors.push({
              row: rowNumber,
              field: 'email',
              message: 'Contact with this email already exists'
            });
            continue;
          }
        }

        // Handle company
        let companyId: string | null = null;
        if (companyName) {
          // Check cache first
          if (companyCache.has(companyName)) {
            companyId = companyCache.get(companyName)!;
          } else {
            // Search for existing company
            const companies = await companyService.searchCompaniesByName(companyName, req.tenantId, 1);
            
            if (companies.length > 0) {
              companyId = companies[0].id;
              companyCache.set(companyName, companyId);
            } else {
              // Create new company
              try {
                const newCompany = await companyService.createCompany(
                  req.tenantId,
                  { name: companyName },
                  req.userId
                );
                companyId = newCompany.id;
                companyCache.set(companyName, companyId);
              } catch (companyError) {
                errors.push({
                  row: rowNumber,
                  field: 'company',
                  message: 'Failed to create company'
                });
                continue;
              }
            }
          }
        }

        // Validate contact data
        const contactData = {
          firstName,
          lastName,
          email,
          phone,
          companyId,
        };

        try {
          createContactSchema.parse(contactData);
        } catch (validationError: any) {
          errors.push({
            row: rowNumber,
            field: 'validation',
            message: validationError.errors?.[0]?.message || 'Validation failed'
          });
          continue;
        }

        // Create contact
        try {
          const contact = await contactService.createContact(
            req.tenantId,
            contactData,
            req.userId
          );
          imported.push(contact);
          importedCount++;
        } catch (createError: any) {
          errors.push({
            row: rowNumber,
            field: 'creation',
            message: createError.message || 'Failed to create contact'
          });
        }

      } catch (rowError: any) {
        errors.push({
          row: rowNumber,
          field: 'parsing',
          message: 'Failed to parse row data'
        });
      }
    }

    // Return results
    const success = importedCount > 0 && errors.length === 0;
    
    return NextResponse.json({
      success,
      imported: importedCount,
      errors: errors.slice(0, 100), // Limit errors to prevent large responses
      message: success 
        ? `Successfully imported ${importedCount} contacts`
        : `Imported ${importedCount} contacts with ${errors.length} errors`
    });

  } catch (error) {
    console.error('Import contacts error:', error);

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
});
