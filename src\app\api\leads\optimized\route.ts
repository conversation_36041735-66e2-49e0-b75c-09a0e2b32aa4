import { NextRequest, NextResponse } from 'next/server';
import { withOptimizedPermission } from '@/lib/optimized-permission-middleware';
import { PermissionResource, PermissionAction } from '@/types';
import { LeadManagementService } from '@/services/lead-management';
import { z } from 'zod';

// Validation schema for query parameters
const LeadQuerySchema = z.object({
  page: z.string().transform(val => parseInt(val, 10)).default('1'),
  limit: z.string().transform(val => Math.min(parseInt(val, 10), 100)).default('10'), // Max 100 items
  search: z.string().optional(),
  status: z.enum(['new', 'contacted', 'qualified', 'proposal', 'negotiation', 'closed_won', 'closed_lost', 'all']).default('all'),
  priority: z.enum(['low', 'medium', 'high', 'urgent', 'all']).default('all'),
  source: z.string().optional(),
  ownerId: z.string().optional(),
  sortBy: z.enum(['createdAt', 'updatedAt', 'score', 'value', 'status']).default('createdAt'),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
  includeArchived: z.string().transform(val => val === 'true').default('false'),
  includeConverted: z.string().transform(val => val === 'true').default('false'),
});

/**
 * Optimized GET endpoint for leads with advanced caching and performance optimizations
 */
async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    
    // Parse and validate query parameters
    const queryParams = Object.fromEntries(searchParams.entries());
    const validatedQuery = LeadQuerySchema.parse(queryParams);

    const leadService = new LeadManagementService();
    
    // Convert validated query to service format
    const serviceQuery = {
      page: validatedQuery.page,
      limit: validatedQuery.limit,
      search: validatedQuery.search,
      status: validatedQuery.status === 'all' ? undefined : validatedQuery.status,
      priority: validatedQuery.priority === 'all' ? undefined : validatedQuery.priority,
      leadSourceId: validatedQuery.source,
      ownerId: validatedQuery.ownerId,
      sortBy: validatedQuery.sortBy,
      sortOrder: validatedQuery.sortOrder,
      includeArchived: validatedQuery.includeArchived,
      includeConverted: validatedQuery.includeConverted,
    };

    // Get leads with optimized caching
    const result = await leadService.getLeads(
      (req as any).tenantId,
      serviceQuery,
      (req as any).userId
    );

    // Add performance headers
    const response = NextResponse.json({
      success: true,
      data: result,
      meta: {
        cached: false, // This would be set by the service if data was cached
        queryTime: Date.now(), // Could be actual query time from service
        optimized: true
      }
    });

    // Set cache headers for browser caching
    response.headers.set('Cache-Control', 'public, max-age=30, stale-while-revalidate=60');
    response.headers.set('X-Performance-Optimized', 'true');
    
    return response;

  } catch (error) {
    console.error('Optimized leads API error:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid query parameters',
          details: error.errors
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error'
      },
      { status: 500 }
    );
  }
}

/**
 * Optimized POST endpoint for creating leads
 */
async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    
    // Basic validation
    if (!body.title || !body.status) {
      return NextResponse.json(
        {
          success: false,
          error: 'Title and status are required'
        },
        { status: 400 }
      );
    }

    const leadService = new LeadManagementService();
    
    const lead = await leadService.createLead(
      (req as any).tenantId,
      body,
      (req as any).userId
    );

    return NextResponse.json({
      success: true,
      data: lead,
      meta: {
        created: true,
        optimized: true
      }
    }, { status: 201 });

  } catch (error) {
    console.error('Optimized lead creation error:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create lead'
      },
      { status: 500 }
    );
  }
}

// Apply optimized permission middleware with reduced audit logging for performance
export const GET_WITH_PERMISSION = withOptimizedPermission({
  resource: PermissionResource.LEADS,
  action: PermissionAction.READ,
  skipAuditLog: true // Skip audit logging for read operations to improve performance
})(GET);

export const POST_WITH_PERMISSION = withOptimizedPermission({
  resource: PermissionResource.LEADS,
  action: PermissionAction.CREATE,
  skipAuditLog: false // Keep audit logging for create operations
})(POST);

export { GET_WITH_PERMISSION as GET, POST_WITH_PERMISSION as POST };
