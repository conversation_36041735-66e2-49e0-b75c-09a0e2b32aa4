'use client';

import { useSession, signIn, signOut } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { useCallback, useEffect } from 'react';
import { api } from '@/lib/api-client';

export interface AuthUser {
  id: string;
  email: string;
  name: string;
  image?: string;
  locale: string;
  isPlatformAdmin: boolean;
  tenants: Array<{
    id: string;
    name: string;
    slug: string;
    role: string;
    isTenantAdmin: boolean;
  }>;
}

export interface UseAuthReturn {
  user: AuthUser | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  error: string | null;
  signIn: (email: string, password: string) => Promise<{ success: boolean; error?: string }>;
  signOut: () => Promise<void>;
  refreshSession: () => Promise<void>;
  hasPermission: (permission: string) => boolean;
  isTenantAdmin: () => boolean;
  isPlatformAdmin: () => boolean;
}

export function useAuth(): UseAuthReturn {
  const { data: session, status, update } = useSession();
  const router = useRouter();

  const user = session?.user || null;
  const isLoading = status === 'loading';
  const isAuthenticated = !!session && !!user;
  const error = session?.error || null;

  // Sign in function
  const handleSignIn = useCallback(async (email: string, password: string) => {
    try {
      const result = await signIn('credentials', {
        email,
        password,
        redirect: false,
      });

      if (result?.error) {
        return { success: false, error: 'Invalid email or password' };
      }

      if (result?.ok) {
        // Refresh session to get updated user data
        await update();
        return { success: true };
      }

      return { success: false, error: 'Sign in failed' };
    } catch (error) {
      console.error('Sign in error:', error);
      return { success: false, error: 'An error occurred during sign in' };
    }
  }, [update]);

  // Sign out function
  const handleSignOut = useCallback(async () => {
    try {
      await signOut({ 
        callbackUrl: '/auth/signin',
        redirect: true 
      });
    } catch (error) {
      console.error('Sign out error:', error);
      // Force redirect even if signOut fails
      router.push('/auth/signin');
    }
  }, [router]);

  // Refresh session function
  const refreshSession = useCallback(async () => {
    try {
      await update();
    } catch (error) {
      console.error('Session refresh error:', error);
    }
  }, [update]);

  // Permission checking functions
  const hasPermission = useCallback((permission: string): boolean => {
    if (!user) return false;
    
    // Platform admins have all permissions
    if (user.isPlatformAdmin) return true;
    
    // Check tenant-specific permissions
    const currentTenant = user.tenants?.[0];
    if (!currentTenant) return false;
    
    // Basic permission logic - can be extended based on your needs
    switch (permission) {
      case 'admin':
        return currentTenant.isTenantAdmin;
      case 'read':
        return true; // All authenticated users can read
      case 'write':
        return currentTenant.role !== 'viewer';
      case 'delete':
        return currentTenant.isTenantAdmin || currentTenant.role === 'admin';
      default:
        return false;
    }
  }, [user]);

  const isTenantAdmin = useCallback((): boolean => {
    return user?.tenants?.[0]?.isTenantAdmin || false;
  }, [user]);

  const isPlatformAdmin = useCallback((): boolean => {
    return user?.isPlatformAdmin || false;
  }, [user]);

  // Auto-refresh session on token expiry
  useEffect(() => {
    if (error === 'RefreshAccessTokenError') {
      // Token refresh failed, sign out user
      handleSignOut();
    }
  }, [error, handleSignOut]);

  // Periodic session refresh (every 30 minutes)
  useEffect(() => {
    if (!isAuthenticated) return;

    const interval = setInterval(() => {
      refreshSession();
    }, 30 * 60 * 1000); // 30 minutes

    return () => clearInterval(interval);
  }, [isAuthenticated, refreshSession]);

  return {
    user,
    isLoading,
    isAuthenticated,
    error,
    signIn: handleSignIn,
    signOut: handleSignOut,
    refreshSession,
    hasPermission,
    isTenantAdmin,
    isPlatformAdmin,
  };
}

// Hook for protecting routes
export function useRequireAuth(redirectTo: string = '/auth/signin') {
  const { isAuthenticated, isLoading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push(redirectTo);
    }
  }, [isAuthenticated, isLoading, router, redirectTo]);

  return { isAuthenticated, isLoading };
}

// Hook for redirecting authenticated users
export function useRedirectIfAuthenticated(redirectTo: string = '/dashboard') {
  const { isAuthenticated, isLoading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!isLoading && isAuthenticated) {
      router.push(redirectTo);
    }
  }, [isAuthenticated, isLoading, router, redirectTo]);

  return { isAuthenticated, isLoading };
}
