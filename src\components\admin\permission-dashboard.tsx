'use client';

import React, { useState, useEffect } from 'react';
import { usePermissions } from '@/components/providers/permission-provider';
import { PermissionGate } from '@/components/ui/permission-gate';
import { PermissionAction, PermissionResource, Permission, Role, User } from '@/types';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { 
  ShieldCheckIcon, 
  UserGroupIcon, 
  KeyIcon, 
  ExclamationTriangleIcon,
  ChartBarIcon,
  ClockIcon
} from '@heroicons/react/24/outline';

interface PermissionDashboardProps {
  tenantId: string;
}

export function PermissionDashboard({ tenantId }: PermissionDashboardProps) {
  const [stats, setStats] = useState<PermissionStats | null>(null);
  const [recentActivity, setRecentActivity] = useState<PermissionActivity[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const { isTenantAdmin, getPermissionsByCategory, getUserRoles } = usePermissions();

  useEffect(() => {
    fetchDashboardData();
  }, [tenantId]);

  const fetchDashboardData = async () => {
    try {
      setIsLoading(true);
      
      // Fetch permission statistics
      const statsResponse = await fetch(`/api/admin/permissions/stats?tenantId=${tenantId}`);
      if (statsResponse.ok) {
        const statsData = await statsResponse.json();
        setStats(statsData.data);
      }

      // Fetch recent permission activity
      const activityResponse = await fetch(`/api/admin/permissions/activity?tenantId=${tenantId}&limit=10`);
      if (activityResponse.ok) {
        const activityData = await activityResponse.json();
        setRecentActivity(activityData.data || []);
      }

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch dashboard data');
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <LoadingSpinner />
      </div>
    );
  }

  return (
    <PermissionGate
      resource={PermissionResource.ROLES}
      action={PermissionAction.READ}
      fallback={
        <div className="text-center py-8">
          <ExclamationTriangleIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-500">You don't have permission to view the permission dashboard.</p>
        </div>
      }
    >
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">Permission Dashboard</h2>
            <p className="text-gray-600">Monitor and manage permissions across your organization</p>
          </div>
          
          <div className="flex space-x-3">
            <PermissionGate resource={PermissionResource.ROLES} action={PermissionAction.CREATE}>
              <button className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors">
                Create Role
              </button>
            </PermissionGate>
            
            <button 
              onClick={fetchDashboardData}
              className="bg-gray-100 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-200 transition-colors"
            >
              Refresh
            </button>
          </div>
        </div>

        {/* Error Display */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-md p-4">
            <p className="text-red-800">{error}</p>
          </div>
        )}

        {/* Statistics Cards */}
        {stats && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <StatCard
              title="Total Users"
              value={stats.totalUsers}
              icon={UserGroupIcon}
              color="blue"
              change={stats.userGrowth}
            />
            <StatCard
              title="Active Roles"
              value={stats.totalRoles}
              icon={ShieldCheckIcon}
              color="green"
              change={stats.roleGrowth}
            />
            <StatCard
              title="Permissions"
              value={stats.totalPermissions}
              icon={KeyIcon}
              color="purple"
            />
            <StatCard
              title="Security Score"
              value={`${stats.securityScore}%`}
              icon={ChartBarIcon}
              color={stats.securityScore >= 80 ? 'green' : stats.securityScore >= 60 ? 'yellow' : 'red'}
            />
          </div>
        )}

        {/* Permission Overview */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Permission Categories */}
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Permission Categories</h3>
            <PermissionCategoryChart />
          </div>

          {/* User Roles Distribution */}
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Role Distribution</h3>
            <RoleDistributionChart roles={getUserRoles()} />
          </div>
        </div>

        {/* Recent Activity */}
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-semibold text-gray-900">Recent Permission Activity</h3>
            <PermissionGate resource={PermissionResource.SETTINGS} action={PermissionAction.READ}>
              <button className="text-blue-600 hover:text-blue-800 text-sm">
                View All Activity
              </button>
            </PermissionGate>
          </div>
          
          <RecentActivityList activities={recentActivity} />
        </div>

        {/* Quick Actions */}
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
          <QuickActions />
        </div>
      </div>
    </PermissionGate>
  );
}

interface StatCardProps {
  title: string;
  value: string | number;
  icon: React.ComponentType<{ className?: string }>;
  color: 'blue' | 'green' | 'purple' | 'yellow' | 'red';
  change?: number;
}

function StatCard({ title, value, icon: Icon, color, change }: StatCardProps) {
  const colorClasses = {
    blue: 'bg-blue-50 text-blue-600',
    green: 'bg-green-50 text-green-600',
    purple: 'bg-purple-50 text-purple-600',
    yellow: 'bg-yellow-50 text-yellow-600',
    red: 'bg-red-50 text-red-600',
  };

  return (
    <div className="bg-white rounded-lg border border-gray-200 p-6">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <p className="text-2xl font-bold text-gray-900">{value}</p>
          {change !== undefined && (
            <p className={`text-sm ${change >= 0 ? 'text-green-600' : 'text-red-600'}`}>
              {change >= 0 ? '+' : ''}{change}% from last month
            </p>
          )}
        </div>
        <div className={`p-3 rounded-full ${colorClasses[color]}`}>
          <Icon className="w-6 h-6" />
        </div>
      </div>
    </div>
  );
}

function PermissionCategoryChart() {
  const { getPermissionsByCategory } = usePermissions();
  const categories = getPermissionsByCategory();

  return (
    <div className="space-y-3">
      {Object.entries(categories).map(([category, permissions]) => (
        <div key={category} className="flex items-center justify-between">
          <span className="text-sm font-medium text-gray-700 capitalize">{category}</span>
          <div className="flex items-center space-x-2">
            <div className="w-24 bg-gray-200 rounded-full h-2">
              <div 
                className="bg-blue-600 h-2 rounded-full" 
                style={{ width: `${Math.min(100, (permissions.length / 10) * 100)}%` }}
              />
            </div>
            <span className="text-sm text-gray-500">{permissions.length}</span>
          </div>
        </div>
      ))}
    </div>
  );
}

function RoleDistributionChart({ roles }: { roles: Role[] }) {
  const roleCounts = roles.reduce((acc, role) => {
    acc[role.name] = (acc[role.name] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  return (
    <div className="space-y-3">
      {Object.entries(roleCounts).map(([roleName, count]) => (
        <div key={roleName} className="flex items-center justify-between">
          <span className="text-sm font-medium text-gray-700">{roleName}</span>
          <div className="flex items-center space-x-2">
            <div className="w-24 bg-gray-200 rounded-full h-2">
              <div 
                className="bg-green-600 h-2 rounded-full" 
                style={{ width: `${Math.min(100, (count / Math.max(...Object.values(roleCounts))) * 100)}%` }}
              />
            </div>
            <span className="text-sm text-gray-500">{count}</span>
          </div>
        </div>
      ))}
    </div>
  );
}

function RecentActivityList({ activities }: { activities: PermissionActivity[] }) {
  if (activities.length === 0) {
    return (
      <div className="text-center py-8">
        <ClockIcon className="w-8 h-8 text-gray-400 mx-auto mb-2" />
        <p className="text-gray-500">No recent activity</p>
      </div>
    );
  }

  return (
    <div className="space-y-3">
      {activities.map((activity, index) => (
        <div key={index} className="flex items-center space-x-3 p-3 bg-gray-50 rounded-md">
          <div className="flex-shrink-0">
            <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
              <KeyIcon className="w-4 h-4 text-blue-600" />
            </div>
          </div>
          <div className="flex-1 min-w-0">
            <p className="text-sm font-medium text-gray-900">{activity.action}</p>
            <p className="text-sm text-gray-500">{activity.description}</p>
          </div>
          <div className="flex-shrink-0 text-sm text-gray-500">
            {new Date(activity.timestamp).toLocaleDateString()}
          </div>
        </div>
      ))}
    </div>
  );
}

function QuickActions() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
      <PermissionGate resource={PermissionResource.ROLES} action={PermissionAction.CREATE}>
        <button className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left">
          <ShieldCheckIcon className="w-6 h-6 text-blue-600 mb-2" />
          <h4 className="font-medium text-gray-900">Create Role</h4>
          <p className="text-sm text-gray-500">Add a new role with custom permissions</p>
        </button>
      </PermissionGate>

      <PermissionGate resource={PermissionResource.USERS} action={PermissionAction.CREATE}>
        <button className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left">
          <UserGroupIcon className="w-6 h-6 text-green-600 mb-2" />
          <h4 className="font-medium text-gray-900">Invite User</h4>
          <p className="text-sm text-gray-500">Invite a new user to your organization</p>
        </button>
      </PermissionGate>

      <PermissionGate resource={PermissionResource.SETTINGS} action={PermissionAction.READ}>
        <button className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left">
          <ChartBarIcon className="w-6 h-6 text-purple-600 mb-2" />
          <h4 className="font-medium text-gray-900">Security Audit</h4>
          <p className="text-sm text-gray-500">Review security and permission logs</p>
        </button>
      </PermissionGate>
    </div>
  );
}

// Types for dashboard data
interface PermissionStats {
  totalUsers: number;
  totalRoles: number;
  totalPermissions: number;
  securityScore: number;
  userGrowth?: number;
  roleGrowth?: number;
}

interface PermissionActivity {
  action: string;
  description: string;
  timestamp: string;
  userId?: string;
  userName?: string;
}
