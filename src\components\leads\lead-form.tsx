'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useSession } from 'next-auth/react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import {
  Save,
  X,
  User,
  Building,
  Calendar,
  DollarSign,
  Star,
  Flag,
  Tag,
  FileText,
  TrendingUp,
  Sparkles
} from 'lucide-react';
import { LeadWithRelations } from '@/services/lead-management';
import { ContactWithRelations } from '@/services/contact-management';
import { CompanyWithRelations } from '@/services/company-management';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { SearchableSelect, SearchableSelectOption } from '@/components/ui/searchable-select';
import { 
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form';
import { toastFunctions as toast } from '@/hooks/use-toast';

// Form validation schema
const leadFormSchema = z.object({
  title: z.string().min(1, 'Title is required').max(255, 'Title too long'),
  description: z.string().optional(),
  contactId: z.string().optional(),
  companyId: z.string().optional(),
  status: z.enum(['new', 'contacted', 'qualified', 'proposal', 'negotiation', 'closed_won', 'closed_lost']).default('new'),
  leadSourceId: z.string().optional(),
  score: z.number().int().min(0).max(100).default(0),
  value: z.number().optional(),
  expectedCloseDate: z.string().optional(),
  priority: z.enum(['low', 'medium', 'high', 'urgent']).default('medium'),
  ownerId: z.string().optional(),
  tags: z.array(z.string()).default([])
});

type LeadFormData = z.infer<typeof leadFormSchema>;

interface LeadSource {
  id: string;
  name: string;
  description: string | null;
  category: string;
  icon: string | null;
  color: string | null;
  isActive: boolean;
}

interface LeadFormProps {
  lead?: LeadWithRelations;
  initialData?: {
    contactId?: string;
    companyId?: string;
    title?: string;
    _contactInfo?: {
      name?: string;
      email?: string;
      phone?: string;
    };
  };
  onSuccess?: () => void;
  onCancel?: () => void;
  className?: string;
}

export function LeadForm({ lead, initialData, onSuccess, onCancel, className }: LeadFormProps) {
  const router = useRouter();
  const { data: session } = useSession();
  const [loading, setLoading] = useState(false);
  const [dataLoading, setDataLoading] = useState(true);
  const [contacts, setContacts] = useState<ContactWithRelations[]>([]);
  const [companies, setCompanies] = useState<CompanyWithRelations[]>([]);
  const [leadSources, setLeadSources] = useState<LeadSource[]>([]);
  const [users, setUsers] = useState<any[]>([]);
  const [tagInput, setTagInput] = useState('');

  // Helper functions to convert data to SearchableSelectOption format
  const getLeadSourceOptions = (): SearchableSelectOption[] => {
    const options: SearchableSelectOption[] = [
      { value: 'none', label: 'No source', description: 'No lead source selected' }
    ];

    leadSources.forEach(source => {
      options.push({
        value: source.id,
        label: source.name,
        description: source.description || undefined,
        icon: source.icon && source.color ? (
          <div
            className="w-4 h-4 rounded flex items-center justify-center text-white text-xs"
            style={{ backgroundColor: source.color }}
          >
            {source.icon}
          </div>
        ) : undefined
      });
    });

    return options;
  };

  const getContactOptions = (): SearchableSelectOption[] => {
    const options: SearchableSelectOption[] = [
      { value: 'none', label: 'No contact', description: 'No contact selected' }
    ];

    contacts.forEach(contact => {
      options.push({
        value: contact.id,
        label: `${contact.firstName} ${contact.lastName}`,
        description: contact.email
      });
    });

    return options;
  };

  const getCompanyOptions = (): SearchableSelectOption[] => {
    const options: SearchableSelectOption[] = [
      { value: 'none', label: 'No company', description: 'No company selected' }
    ];

    companies.forEach(company => {
      options.push({
        value: company.id,
        label: company.name,
        description: (company as any).website || undefined
      });
    });

    return options;
  };

  const getStatusOptions = (): SearchableSelectOption[] => {
    return [
      { value: 'new', label: 'New', description: 'Newly created lead' },
      { value: 'contacted', label: 'Contacted', description: 'Initial contact made' },
      { value: 'qualified', label: 'Qualified', description: 'Lead has been qualified' },
      { value: 'proposal', label: 'Proposal', description: 'Proposal sent to lead' },
      { value: 'negotiation', label: 'Negotiation', description: 'In negotiation phase' },
      { value: 'closed_won', label: 'Won', description: 'Successfully closed' },
      { value: 'closed_lost', label: 'Lost', description: 'Lead was lost' }
    ];
  };

  const getPriorityOptions = (): SearchableSelectOption[] => {
    return [
      { value: 'low', label: 'Low', description: 'Low priority lead' },
      { value: 'medium', label: 'Medium', description: 'Medium priority lead' },
      { value: 'high', label: 'High', description: 'High priority lead' },
      { value: 'urgent', label: 'Urgent', description: 'Urgent priority lead' }
    ];
  };

  const getUserOptions = (): SearchableSelectOption[] => {
    const options: SearchableSelectOption[] = [
      { value: 'none', label: 'Unassigned', description: 'No owner assigned' }
    ];

    users.forEach(user => {
      options.push({
        value: user.id,
        label: `${user.firstName} ${user.lastName}`,
        description: user.email
      });
    });

    return options;
  };

  const form = useForm({
    resolver: zodResolver(leadFormSchema),
    defaultValues: {
      title: lead?.title || initialData?.title || '',
      description: (lead as any)?.description || '',
      contactId: lead?.contactId || initialData?.contactId || 'none',
      companyId: lead?.companyId || initialData?.companyId || 'none',
      status: (lead?.status as any) || 'new',
      leadSourceId: (lead as any)?.leadSourceId || 'none',
      score: lead?.score || 0,
      value: (lead as any)?.value ? Number((lead as any).value) : 0,
      expectedCloseDate: (lead as any)?.expectedCloseDate ? new Date((lead as any).expectedCloseDate).toISOString().split('T')[0] : '',
      priority: (lead as any)?.priority || 'medium',
      ownerId: lead?.ownerId || (lead ? 'none' : session?.user?.id || 'none'),
      tags: Array.isArray((lead as any)?.tags) ? (lead as any).tags : []
    }
  }) as any;

  // Load related data
  useEffect(() => {
    const loadData = async () => {
      try {
        setDataLoading(true);

        // Load contacts
        const contactsResponse = await fetch('/api/contacts?limit=100');
        const contactsData = await contactsResponse.json();
        if (contactsData.success) {
          setContacts(contactsData.data.contacts || []);
        }

        // Load companies
        const companiesResponse = await fetch('/api/companies?limit=100');
        const companiesData = await companiesResponse.json();
        if (companiesData.success) {
          setCompanies(companiesData.data.companies || []);
        }

        // Load lead sources
        const leadSourcesResponse = await fetch('/api/lead-sources?limit=100&isActive=true');
        const leadSourcesData = await leadSourcesResponse.json();
        if (leadSourcesData.success) {
          setLeadSources(leadSourcesData.data.leadSources || []);
        }

        // Load users (current tenant only)
        const usersResponse = await fetch('/api/users');
        const usersData = await usersResponse.json();
        if (usersData.success) {
          setUsers(usersData.data || []);
        }
      } catch (error) {
        console.error('Error loading form data:', error);
        // Set empty arrays as fallback
        setContacts([]);
        setCompanies([]);
        setLeadSources([]);
        setUsers([]);
      } finally {
        setDataLoading(false);
      }
    };

    loadData();
  }, []);

  // Update form values when data is loaded and we have initial data
  useEffect(() => {
    if (!dataLoading && initialData) {
      // Set contact if provided and exists in loaded contacts
      if (initialData.contactId && contacts.some(c => c.id === initialData.contactId)) {
        form.setValue('contactId', initialData.contactId);
      }

      // Set company if provided and exists in loaded companies
      if (initialData.companyId && companies.some(c => c.id === initialData.companyId)) {
        form.setValue('companyId', initialData.companyId);
      }

      // Set title if provided and form title is empty
      if (initialData.title && !form.getValues('title')) {
        form.setValue('title', initialData.title);
      }
    }
  }, [dataLoading, initialData, contacts, companies, form]);

  // Handle form submission
  const onSubmit = async (data: LeadFormData) => {
    try {
      setLoading(true);

      const payload = {
        ...data,
        contactId: data.contactId === 'none' ? undefined : data.contactId,
        companyId: data.companyId === 'none' ? undefined : data.companyId,
        leadSourceId: data.leadSourceId === 'none' ? undefined : data.leadSourceId,
        ownerId: data.ownerId === 'none' ? undefined : data.ownerId,
        value: data.value && data.value > 0 ? data.value : undefined,
        expectedCloseDate: data.expectedCloseDate || undefined
      };

      const url = lead ? `/api/leads/${lead.id}` : '/api/leads';
      const method = lead ? 'PUT' : 'POST';

      const response = await fetch(url, {
        method,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload)
      });

      const result = await response.json();

      if (result.success) {
        toast.success(lead ? 'Lead updated successfully' : 'Lead created successfully');
        if (onSuccess) {
          onSuccess();
        } else {
          router.push('/leads');
        }
      } else {
        toast.error(result.error || 'Failed to save lead');
      }
    } catch (error) {
      console.error('Error saving lead:', error);
      toast.error('Failed to save lead');
    } finally {
      setLoading(false);
    }
  };

  // Handle tag addition
  const addTag = () => {
    if (tagInput.trim() && !form.getValues('tags').includes(tagInput.trim())) {
      const currentTags = form.getValues('tags');
      form.setValue('tags', [...currentTags, tagInput.trim()]);
      setTagInput('');
    }
  };

  // Handle tag removal
  const removeTag = (tagToRemove: string) => {
    const currentTags = form.getValues('tags');
    form.setValue('tags', currentTags.filter((tag: string) => tag !== tagToRemove));
  };

  if (dataLoading) {
    return (
      <div className={`max-w-4xl mx-auto ${className}`}>
        <Card>
          <CardContent className="p-6">
            <div className="animate-pulse space-y-4">
              <div className="h-4 bg-gray-200 rounded w-1/4"></div>
              <div className="space-y-3">
                <div className="h-4 bg-gray-200 rounded"></div>
                <div className="h-4 bg-gray-200 rounded w-5/6"></div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className={className}>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <FileText className="w-5 h-5" />
            <span>{lead ? 'Edit Lead' : 'Create New Lead'}</span>
          </CardTitle>
        </CardHeader>

        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              {/* Basic Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <FormField
                  control={form.control}
                  name="title"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Lead Title *</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter lead title" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="leadSourceId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="flex items-center space-x-2">
                        <TrendingUp className="w-4 h-4" />
                        <span>Lead Source</span>
                      </FormLabel>
                      <FormControl>
                        <SearchableSelect
                          options={getLeadSourceOptions()}
                          value={field.value}
                          onValueChange={field.onChange}
                          placeholder="Select a lead source"
                          searchPlaceholder="Search lead sources..."
                          emptyMessage="No lead sources found"
                          allowClear={true}
                          clearLabel="Clear lead source"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description</FormLabel>
                    <FormControl>
                      <Textarea 
                        placeholder="Enter lead description and notes"
                        rows={3}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Contact and Company */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <FormField
                  control={form.control}
                  name="contactId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="flex items-center gap-2">
                        Contact
                        {initialData?.contactId && field.value === initialData.contactId && (
                          <span className="inline-flex items-center gap-1 px-2 py-1 text-xs bg-green-100 text-green-700 rounded-full">
                            <Sparkles className="w-3 h-3" />
                            Auto-selected
                          </span>
                        )}
                      </FormLabel>
                      <FormControl>
                        <SearchableSelect
                          options={getContactOptions()}
                          value={field.value}
                          onValueChange={field.onChange}
                          placeholder="Select a contact"
                          searchPlaceholder="Search contacts..."
                          emptyMessage="No contacts found"
                          allowClear={true}
                          clearLabel="Clear contact"
                        />
                      </FormControl>
                      <FormMessage />
                      {initialData?._contactInfo?.name && field.value === initialData.contactId && (
                        <p className="text-xs text-green-600">
                          Auto-selected from contact: {initialData._contactInfo.name}
                        </p>
                      )}
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="companyId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="flex items-center gap-2">
                        Company
                        {initialData?.companyId && field.value === initialData.companyId && (
                          <span className="inline-flex items-center gap-1 px-2 py-1 text-xs bg-green-100 text-green-700 rounded-full">
                            <Sparkles className="w-3 h-3" />
                            Auto-selected
                          </span>
                        )}
                      </FormLabel>
                      <FormControl>
                        <SearchableSelect
                          options={getCompanyOptions()}
                          value={field.value}
                          onValueChange={field.onChange}
                          placeholder="Select a company"
                          searchPlaceholder="Search companies..."
                          emptyMessage="No companies found"
                          allowClear={true}
                          clearLabel="Clear company"
                        />
                      </FormControl>
                      <FormMessage />
                      {initialData?.companyId && field.value === initialData.companyId && (
                        <p className="text-xs text-green-600">
                          Auto-selected from contact's company
                        </p>
                      )}
                    </FormItem>
                  )}
                />
              </div>

              {/* Status and Priority */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <FormField
                  control={form.control}
                  name="status"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Status</FormLabel>
                      <FormControl>
                        <SearchableSelect
                          options={getStatusOptions()}
                          value={field.value}
                          onValueChange={field.onChange}
                          placeholder="Select status"
                          searchPlaceholder="Search status..."
                          emptyMessage="No status options found"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="priority"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Priority</FormLabel>
                      <FormControl>
                        <SearchableSelect
                          options={getPriorityOptions()}
                          value={field.value}
                          onValueChange={field.onChange}
                          placeholder="Select priority"
                          searchPlaceholder="Search priority..."
                          emptyMessage="No priority options found"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="score"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Score (0-100)</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          min="0"
                          max="100"
                          placeholder="0"
                          {...field}
                          value={field.value || 0}
                          onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Value and Date */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <FormField
                  control={form.control}
                  name="value"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Estimated Value ($)</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          min="0"
                          step="0.01"
                          placeholder="0.00"
                          {...field}
                          value={field.value || ''}
                          onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="expectedCloseDate"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Expected Close Date</FormLabel>
                      <FormControl>
                        <Input type="date" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Owner */}
              <FormField
                control={form.control}
                name="ownerId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Owner</FormLabel>
                    <FormControl>
                      <SearchableSelect
                        options={getUserOptions()}
                        value={field.value}
                        onValueChange={field.onChange}
                        placeholder="Select an owner"
                        searchPlaceholder="Search users..."
                        emptyMessage="No users found"
                        allowClear={true}
                        clearLabel="Clear owner"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Form Actions */}
              <div className="flex justify-end space-x-4 pt-6 border-t">
                <Button
                  type="button"
                  variant="outline"
                  onClick={onCancel || (() => router.back())}
                  disabled={loading}
                >
                  <X className="w-4 h-4 mr-2" />
                  Cancel
                </Button>
                
                <Button type="submit" disabled={loading}>
                  <Save className="w-4 h-4 mr-2" />
                  {loading ? 'Saving...' : (lead ? 'Update Lead' : 'Create Lead')}
                </Button>
              </div>
            </form>
          </Form>
        </CardContent>
      </Card>
    </div>
  );
}
