'use client';

import React, { useState, useEffect } from 'react';
import { Plus, Search, Filter, MoreHorizontal, Edit, Trash2, Mail, Phone, Building, Download, Upload, Users, TrendingUp } from 'lucide-react';
import { ContactWithRelations } from '@/services/contact-management';
import { usePermissions } from '@/components/providers/permission-provider';
import { PermissionResource, PermissionAction } from '@/types';
import { PermissionGate } from '@/components/ui/permission-gate';
import { PermissionAwareTable } from '@/components/ui/permission-aware-table';
import { EmptyState } from '@/components/ui/empty-state';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { LegacySelect as Select } from '@/components/ui/select';
import { useEnhancedDeleteDialog, deleteConfigurations, RelatedData } from '@/components/ui/enhanced-delete-dialog';
import { ContactAvatar, UserAvatar } from '@/components/ui/contact-avatar';
import { useRouter } from 'next/navigation';


interface ContactManagementProps {
  className?: string;
}

export function ContactManagement({ className }: ContactManagementProps) {
  const router = useRouter();
  const [contacts, setContacts] = useState<ContactWithRelations[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [showAdvancedSearch, setShowAdvancedSearch] = useState(false);
  const [filters, setFilters] = useState({
    companyId: '',
    ownerId: '',
    hasEmail: '',
    hasPhone: '',
  });

  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalContacts, setTotalContacts] = useState(0);
  const [companies, setCompanies] = useState<any[]>([]);
  const [users, setUsers] = useState<any[]>([]);

  // Enhanced delete dialog
  const { showDeleteDialog, DialogComponent } = useEnhancedDeleteDialog();

  const { hasPermission, canAccessResource } = usePermissions();

  // Handle export
  const handleExport = async (format: 'csv' | 'json') => {
    try {
      console.log('Starting export...', format);
      const params = new URLSearchParams({
        format,
        ...(searchTerm && { search: searchTerm }),
        ...(filters.companyId && { companyId: filters.companyId }),
        ...(filters.ownerId && { ownerId: filters.ownerId }),
        ...(filters.hasEmail && { hasEmail: filters.hasEmail }),
        ...(filters.hasPhone && { hasPhone: filters.hasPhone }),
      });

      console.log('Export params:', params.toString());
      const response = await fetch(`/api/contacts/export?${params}`);
      console.log('Export response status:', response.status);

      if (response.ok) {
        const blob = await response.blob();
        const filename = `contacts-export-${new Date().toISOString().split('T')[0]}.${format}`;

        if (typeof window !== 'undefined') {
          const url = window.URL.createObjectURL(blob);
          const a = window.document.createElement('a');
          a.href = url;
          a.download = filename;
          window.document.body.appendChild(a);
          a.click();
          window.URL.revokeObjectURL(url);
          window.document.body.removeChild(a);
        }
        console.log('Export completed successfully');
      } else {
        const data = await response.json();
        console.error('Export failed:', data);
        setError(data.error || 'Failed to export contacts');
      }
    } catch (err) {
      console.error('Export error:', err);
      setError('Failed to export contacts. Please try again.');
    }
  };

  // Fetch contacts with advanced filters
  const fetchContacts = async (page: number = 1, search: string = '', advancedFilters: any = {}) => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        page: page.toString(),
        limit: '20',
        ...(search && { search }),
        ...(advancedFilters.companyId && { companyId: advancedFilters.companyId }),
        ...(advancedFilters.ownerId && { ownerId: advancedFilters.ownerId }),
        ...(advancedFilters.hasEmail && { hasEmail: advancedFilters.hasEmail }),
        ...(advancedFilters.hasPhone && { hasPhone: advancedFilters.hasPhone }),
      });

      const response = await fetch(`/api/contacts?${params}`);
      const data = await response.json();

      if (data.success) {
        setContacts(data.data.contacts);
        setTotalPages(data.data.totalPages);
        setCurrentPage(data.data.page);
        setTotalContacts(data.data.total || data.data.contacts.length);
      } else {
        setError(data.error || 'Failed to fetch contacts');
      }
    } catch (err) {
      setError('Failed to fetch contacts');
      console.error('Fetch contacts error:', err);
    } finally {
      setLoading(false);
    }
  };

  // Fetch companies and users for filters
  const fetchFilterData = async () => {
    try {
      const [companiesRes, usersRes] = await Promise.all([
        fetch('/api/companies?limit=100'),
        fetch('/api/users?limit=100')
      ]);

      const companiesData = await companiesRes.json();
      const usersData = await usersRes.json();

      if (companiesData.success) {
        setCompanies(companiesData.data.companies || []);
      }
      if (usersData.success) {
        setUsers(usersData.data.users || []);
      }
    } catch (err) {
      console.error('Failed to fetch filter data:', err);
    }
  };

  // Load contacts and filter data on component mount
  useEffect(() => {
    fetchContacts();
    fetchFilterData();
  }, []);

  // Handle search
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    fetchContacts(1, searchTerm, filters);
  };

  // Handle advanced search
  const handleAdvancedSearch = () => {
    fetchContacts(1, searchTerm, filters);
    setShowAdvancedSearch(false);
  };

  // Clear filters
  const clearFilters = () => {
    setFilters({
      companyId: '',
      ownerId: '',
      hasEmail: '',
      hasPhone: '',
    });
    setSearchTerm('');
    fetchContacts(1, '', {});
  };



  // Handle bulk export
  const handleBulkExport = async (contactIds: string[]) => {
    try {
      const params = new URLSearchParams({
        format: 'csv',
        ids: contactIds.join(',')
      });

      const response = await fetch(`/api/contacts/export?${params}`);

      if (response.ok) {
        const blob = await response.blob();
        const filename = `contacts-export-${new Date().toISOString().split('T')[0]}.csv`;

        if (typeof window !== 'undefined') {
          const url = window.URL.createObjectURL(blob);
          const a = window.document.createElement('a');
          a.href = url;
          a.download = filename;
          window.document.body.appendChild(a);
          a.click();
          window.URL.revokeObjectURL(url);
          window.document.body.removeChild(a);
        }
      } else {
        setError('Failed to export selected contacts');
      }
    } catch (err) {
      console.error('Bulk export error:', err);
      setError('Failed to export selected contacts');
    }
  };



  // Handle delete contact
  const handleDeleteContact = async (contact: ContactWithRelations) => {
    const contactName = `${contact.firstName} ${contact.lastName}`;

    // Prepare related data information
    const relatedData: RelatedData[] = [];

    // Check if contact has activities (this would need to be fetched from API in real implementation)
    // For now, we'll show a placeholder
    if (contact.company) {
      relatedData.push({
        type: 'companies',
        count: 1,
        label: 'company association',
        description: `will lose association with ${contact.company.name}`,
      });
    }

    const confirmed = await showDeleteDialog({
      ...deleteConfigurations.contact(contactName, relatedData),
      onConfirm: async () => {
        try {
          const response = await fetch(`/api/contacts?contactId=${contact.id}`, {
            method: 'DELETE',
          });

          const data = await response.json();

          if (data.success) {
            fetchContacts(currentPage, searchTerm, filters);
          } else {
            setError(data.error || 'Failed to delete contact');
            throw new Error(data.error || 'Failed to delete contact');
          }
        } catch (err) {
          setError('Failed to delete contact');
          console.error('Delete contact error:', err);
          throw err;
        }
      },
    });
  };





  // Table columns configuration
  const columns = [
    {
      key: 'name',
      label: 'Contact',
      render: (contact: ContactWithRelations) => (
        <div className="flex items-center space-x-3">
          <ContactAvatar
            firstName={contact.firstName}
            lastName={contact.lastName}
            avatarUrl={contact.avatar}
            size="lg"
            onClick={() => router.push(`/contacts/${contact.id}`)}
          />
          <div className="min-w-0 flex-1">
            <div className="font-medium text-gray-900 dark:text-white">
              <button
                onClick={() => router.push(`/contacts/${contact.id}`)}
                className="hover:text-primary-600 dark:hover:text-primary-400 transition-colors text-left hover:underline focus:outline-none focus:underline"
              >
                {contact.firstName} {contact.lastName}
              </button>
            </div>
            {contact.email && (
              <div className="text-sm text-gray-500 dark:text-gray-400 flex items-center mt-1">
                <Mail className="w-3 h-3 mr-1 flex-shrink-0" />
                <span className="truncate">{contact.email}</span>
              </div>
            )}
          </div>
        </div>
      ),
      sortable: true,
    },
    {
      key: 'company',
      label: 'Company',
      render: (contact: ContactWithRelations) => (
        <div className="flex items-center">
          {contact.company ? (
            <div className="flex items-center text-gray-900 dark:text-white">
              <div className="w-8 h-8 bg-gray-100 dark:bg-gray-700 rounded-lg flex items-center justify-center mr-3">
                <Building className="w-4 h-4 text-gray-500 dark:text-gray-400" />
              </div>
              <button
                onClick={() => router.push(`/companies/${contact.company!.id}`)}
                className="font-medium hover:text-primary-600 dark:hover:text-primary-400 transition-colors hover:underline focus:outline-none focus:underline text-left"
              >
                {contact.company.name}
              </button>
            </div>
          ) : (
            <span className="text-gray-400 dark:text-gray-500 italic">No company</span>
          )}
        </div>
      ),
    },
    {
      key: 'jobTitle',
      label: 'Job Title',
      render: (contact: ContactWithRelations) => (
        contact.jobTitle ? (
          <div className="text-gray-900 dark:text-white">
            <span className="font-medium">{contact.jobTitle}</span>
          </div>
        ) : (
          <span className="text-gray-400 dark:text-gray-500 italic">No job title</span>
        )
      ),
    },
    {
      key: 'phone',
      label: 'Phone',
      render: (contact: ContactWithRelations) => (
        contact.phone ? (
          <div className="flex items-center text-gray-900 dark:text-white">
            <div className="w-8 h-8 bg-green-100 dark:bg-green-900/20 rounded-lg flex items-center justify-center mr-3">
              <Phone className="w-4 h-4 text-green-600 dark:text-green-400" />
            </div>
            <a
              href={`tel:${contact.phone}`}
              className="font-medium hover:text-primary-600 dark:hover:text-primary-400 transition-colors"
            >
              {contact.phone}
            </a>
          </div>
        ) : (
          <span className="text-gray-400 dark:text-gray-500 italic">No phone</span>
        )
      ),
    },
    {
      key: 'owner',
      label: 'Owner',
      render: (contact: ContactWithRelations) => (
        contact.owner ? (
          <UserAvatar
            user={contact.owner}
            size="md"
            showTooltip
          />
        ) : (
          <span className="text-gray-400 dark:text-gray-500 italic">Unassigned</span>
        )
      ),
    },
    {
      key: 'createdAt',
      label: 'Created',
      render: (contact: ContactWithRelations) => (
        <div className="text-sm">
          <div className="text-gray-900 dark:text-white font-medium">
            {new Date(contact.createdAt).toLocaleDateString()}
          </div>
          <div className="text-gray-500 dark:text-gray-400">
            {new Date(contact.createdAt).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
          </div>
        </div>
      ),
      sortable: true,
    },
  ];

  // Row actions
  const rowActions = [


    {
      label: 'Edit',
      icon: Edit,
      onClick: (contact: ContactWithRelations) => router.push(`/contacts/${contact.id}/edit`),
      permission: { resource: PermissionResource.CONTACTS, action: PermissionAction.UPDATE },
      requireOwnership: true,
      ownershipField: 'ownerId',
      tooltip: 'Edit contact information and details',
      color: 'blue' as const,
    },
    {
      label: 'Delete',
      icon: Trash2,
      onClick: (contact: ContactWithRelations) => handleDeleteContact(contact),
      permission: { resource: PermissionResource.CONTACTS, action: PermissionAction.DELETE },
      requireOwnership: true,
      ownershipField: 'ownerId',
      variant: 'destructive' as const,
      tooltip: 'Permanently delete this contact',
      color: 'red' as const,
    },
  ];

  // Bulk actions
  const bulkActions = [
    {
      label: 'Export Selected',
      icon: Download,
      onClick: (selectedContacts: ContactWithRelations[]) => {
        if (selectedContacts.length === 0) return;
        handleBulkExport(selectedContacts.map(c => c.id));
      },
      permission: { resource: PermissionResource.CONTACTS, action: PermissionAction.READ },
      tooltip: 'Export selected contacts to CSV file',
      color: 'green' as const,
    },

    {
      label: 'Delete Selected',
      icon: Trash2,
      onClick: async (selectedContacts: ContactWithRelations[]) => {
        if (selectedContacts.length === 0) return;

        // Calculate related data for bulk delete
        const companiesAffected = new Set(
          selectedContacts
            .filter(contact => contact.company)
            .map(contact => contact.company!.id)
        ).size;

        const relatedData: RelatedData[] = [];
        if (companiesAffected > 0) {
          relatedData.push({
            type: 'companies',
            count: companiesAffected,
            label: 'company associations',
            description: 'contacts will lose their company associations',
          });
        }

        const confirmed = await showDeleteDialog({
          ...deleteConfigurations.bulkDelete('Contact', selectedContacts.length, relatedData),
          onConfirm: async () => {
            try {
              const deletePromises = selectedContacts.map(contact =>
                fetch(`/api/contacts?contactId=${contact.id}`, { method: 'DELETE' })
              );

              await Promise.all(deletePromises);
              fetchContacts(currentPage, searchTerm, filters);
            } catch (err) {
              setError('Failed to delete contacts');
              console.error('Bulk delete error:', err);
              throw err;
            }
          },
        });
      },
      permission: { resource: PermissionResource.CONTACTS, action: PermissionAction.DELETE },
      requireOwnership: true,
      ownershipField: 'ownerId',
      variant: 'destructive' as const,
      tooltip: 'Permanently delete selected contacts',
      color: 'red' as const,
      confirmMessage: 'Are you sure you want to delete {count} contact(s)? This action cannot be undone.',
    },
  ];

  // Removed stats and quick actions for now

  if (loading && contacts.length === 0) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>

      {/* Search and Filters */}
      <Card>
        <CardContent className="p-6">
          <div className="space-y-4">
            <div className="flex items-center space-x-4">
              <form onSubmit={handleSearch} className="flex-1 max-w-md">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                  <input
                    type="text"
                    placeholder="Search contacts..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pl-10 pr-4 py-2 border border-input rounded-md bg-background text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
                  />
                </div>
              </form>
              <Button
                type="button"
                variant={showAdvancedSearch || Object.values(filters).some(v => v) ? "default" : "outline"}
                onClick={() => setShowAdvancedSearch(!showAdvancedSearch)}
                icon={<Filter className="w-4 h-4" />}
              >
                Advanced Filters
              </Button>
              {Object.values(filters).some(v => v) && (
                <Button
                  type="button"
                  variant="outline"
                  onClick={clearFilters}
                >
                  Clear Filters
                </Button>
              )}
            </div>

            {/* Advanced Search Panel */}
            {showAdvancedSearch && (
              <Card variant="outlined" className="mt-4">
                <CardHeader>
                  <CardTitle as="h3">Advanced Filters</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <Select
                      label="Company"
                      value={filters.companyId}
                      onChange={(e) => setFilters(prev => ({ ...prev, companyId: e.target.value }))}
                      options={[
                        { value: '', label: 'All companies' },
                        ...companies.map(company => ({
                          value: company.id,
                          label: company.name
                        }))
                      ]}
                    />

                    <Select
                      label="Owner"
                      value={filters.ownerId}
                      onChange={(e) => setFilters(prev => ({ ...prev, ownerId: e.target.value }))}
                      options={[
                        { value: '', label: 'All owners' },
                        ...users.map(user => ({
                          value: user.id,
                          label: `${user.firstName} ${user.lastName}`
                        }))
                      ]}
                    />

                    <Select
                      label="Has Email"
                      value={filters.hasEmail}
                      onChange={(e) => setFilters(prev => ({ ...prev, hasEmail: e.target.value }))}
                      options={[
                        { value: '', label: 'Any' },
                        { value: 'true', label: 'Yes' },
                        { value: 'false', label: 'No' }
                      ]}
                    />

                    <Select
                      label="Has Phone"
                      value={filters.hasPhone}
                      onChange={(e) => setFilters(prev => ({ ...prev, hasPhone: e.target.value }))}
                      options={[
                        { value: '', label: 'Any' },
                        { value: 'true', label: 'Yes' },
                        { value: 'false', label: 'No' }
                      ]}
                    />
                  </div>

                  <div className="flex items-center justify-end space-x-3 mt-6">
                    <Button
                      variant="outline"
                      onClick={() => setShowAdvancedSearch(false)}
                    >
                      Cancel
                    </Button>
                    <Button
                      variant="primary"
                      onClick={handleAdvancedSearch}
                    >
                      Apply Filters
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Error Message */}
      {error && (
        <Card variant="outlined" className="border-red-200 dark:border-red-800">
          <CardContent>
            <div className="flex items-center space-x-3">
              <div className="flex-shrink-0">
                <svg className="w-5 h-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="text-red-800 dark:text-red-200">{error}</div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Contacts Table */}
      {contacts.length === 0 && !loading ? (
        <Card>
          <CardContent>
            <EmptyState
              icon={<Users className="w-12 h-12" />}
              title="No contacts found"
              description="Get started by creating your first contact or importing from a CSV file."
              action={{
                label: 'Add Contact',
                onClick: () => router.push('/contacts/new'),
                variant: 'primary'
              }}
              secondaryAction={{
                label: 'Import CSV',
                onClick: () => router.push('/contacts/import')
              }}
            />
          </CardContent>
        </Card>
      ) : (
        <Card>
          <PermissionAwareTable
            data={contacts}
            columns={columns}
            actions={rowActions}
            bulkActions={bulkActions}
            resource={PermissionResource.CONTACTS}
            idField="id"
            ownershipField="ownerId"
            loading={loading}
            emptyMessage="No contacts found"
          />
        </Card>
      )}

      {/* Pagination */}
      {totalPages > 1 && (
        <Card>
          <CardContent>
            <div className="flex items-center justify-between">
              <div className="text-sm text-gray-700 dark:text-gray-300">
                Page {currentPage} of {totalPages}
              </div>
              <div className="flex space-x-2">
                <Button
                  variant="outline"
                  onClick={() => fetchContacts(currentPage - 1, searchTerm, filters)}
                  disabled={currentPage === 1}
                  size="sm"
                >
                  Previous
                </Button>
                <Button
                  variant="outline"
                  onClick={() => fetchContacts(currentPage + 1, searchTerm, filters)}
                  disabled={currentPage === totalPages}
                  size="sm"
                >
                  Next
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Enhanced Delete Dialog */}
      <DialogComponent />
    </div>
  );
}
