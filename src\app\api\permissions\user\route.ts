import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { permissionService } from '@/services/permission-service';
import { z } from 'zod';

const getUserPermissionsSchema = z.object({
  tenantId: z.string().optional(),
});

/**
 * GET /api/permissions/user
 * Get current user's effective permissions
 */
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const tenantId = searchParams.get('tenantId');

    // Super admin has all permissions
    if (session.user.isPlatformAdmin) {
      return NextResponse.json({
        success: true,
        data: {
          userId: session.user.id,
          tenantId: null,
          isSuperAdmin: true,
          permissions: [], // Super admin doesn't need explicit permissions
          roles: [],
          effectivePermissions: []
        }
      });
    }

    // Regular user needs tenant context
    if (!tenantId) {
      return NextResponse.json(
        { error: 'Tenant ID is required for regular users' },
        { status: 400 }
      );
    }

    // Validate input
    const validatedData = getUserPermissionsSchema.parse({ tenantId });

    // Get user permissions
    const userPermissions = await permissionService.getUserEffectivePermissions(
      session.user.id,
      validatedData.tenantId!
    );

    return NextResponse.json({
      success: true,
      data: {
        userId: userPermissions.userId,
        tenantId: userPermissions.tenantId,
        isSuperAdmin: false,
        permissions: userPermissions.directPermissions,
        roles: userPermissions.roles,
        effectivePermissions: userPermissions.effectivePermissions,
        lastUpdated: userPermissions.lastUpdated
      }
    });

  } catch (error) {
    console.error('Get user permissions error:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          error: 'Validation failed',
          details: error.errors.map(e => ({
            field: e.path.join('.'),
            message: e.message
          }))
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/permissions/user/check
 * Check if user has specific permission(s)
 */
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const body = await request.json();
    
    const checkPermissionSchema = z.object({
      tenantId: z.string().optional(),
      permissions: z.array(z.object({
        resource: z.string(),
        action: z.string(),
        resourceOwnerId: z.string().optional()
      })),
      requireAll: z.boolean().default(false)
    });

    const validatedData = checkPermissionSchema.parse(body);

    // Super admin has all permissions
    if (session.user.isPlatformAdmin) {
      return NextResponse.json({
        success: true,
        data: {
          hasPermission: true,
          results: validatedData.permissions.map(p => ({
            resource: p.resource,
            action: p.action,
            allowed: true,
            reason: 'Super admin access'
          }))
        }
      });
    }

    // Regular user needs tenant context
    if (!validatedData.tenantId) {
      return NextResponse.json(
        { error: 'Tenant ID is required for regular users' },
        { status: 400 }
      );
    }

    const results = [];
    let hasPermission = validatedData.requireAll;

    for (const permission of validatedData.permissions) {
      let allowed = false;
      let reason = '';

      try {
        if (permission.resourceOwnerId) {
          const accessResult = await permissionService.canAccessResource(
            session.user.id,
            validatedData.tenantId,
            permission.resource as any,
            permission.action as any,
            permission.resourceOwnerId
          );
          allowed = accessResult.allowed;
          reason = accessResult.reason || '';
        } else {
          allowed = await permissionService.hasPermission(
            session.user.id,
            validatedData.tenantId,
            permission.resource as any,
            permission.action as any
          );
          reason = allowed ? 'Permission granted' : 'Permission denied';
        }
      } catch (error) {
        console.error('Permission check failed:', error);
        allowed = false;
        reason = 'Permission check failed';
      }

      results.push({
        resource: permission.resource,
        action: permission.action,
        resourceOwnerId: permission.resourceOwnerId,
        allowed,
        reason
      });

      // Update overall permission result
      if (validatedData.requireAll) {
        hasPermission = hasPermission && allowed;
      } else {
        hasPermission = hasPermission || allowed;
      }
    }

    return NextResponse.json({
      success: true,
      data: {
        hasPermission,
        requireAll: validatedData.requireAll,
        results
      }
    });

  } catch (error) {
    console.error('Check permissions error:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          error: 'Validation failed',
          details: error.errors.map(e => ({
            field: e.path.join('.'),
            message: e.message
          }))
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
