import { NextRequest, NextResponse } from 'next/server';
import { headers } from 'next/headers';
import { emailDeliveryNotificationService } from '@/services/email-delivery-notifications';
import type { EmailDeliveryEvent } from '@/services/email-delivery-notifications';

/**
 * POST /api/webhooks/email-delivery
 * Handle email delivery status webhooks from email providers
 */
export async function POST(request: NextRequest) {
  try {
    const headersList = await headers();
    const signature = headersList.get('x-webhook-signature');
    const provider = headersList.get('x-email-provider') || 'unknown';
    const tenantId = headersList.get('x-tenant-id') || headersList.get('tenant-id');

    // TODO: Verify webhook signature for security
    // This would depend on your email provider's signature verification method

    const body = await request.json();
    console.log(`📧 Received email delivery webhook from ${provider}:`, body);

    if (!tenantId) {
      return NextResponse.json({
        error: 'Tenant ID required in headers (x-tenant-id)'
      }, { status: 400 });
    }

    // Parse delivery events based on provider
    const deliveryEvents = parseDeliveryWebhook(body, provider, tenantId);

    if (!deliveryEvents || deliveryEvents.length === 0) {
      return NextResponse.json({ error: 'No valid delivery events found' }, { status: 400 });
    }

    // Process delivery events
    if (deliveryEvents.length === 1) {
      await emailDeliveryNotificationService.processDeliveryEvent(deliveryEvents[0]);
    } else {
      await emailDeliveryNotificationService.processBulkDeliveryEvents({
        events: deliveryEvents
      });
    }

    console.log(`✅ Processed ${deliveryEvents.length} email delivery event(s) successfully`);

    return NextResponse.json({
      success: true,
      message: 'Email delivery events processed successfully',
      data: {
        eventsProcessed: deliveryEvents.length,
        provider
      }
    });

  } catch (error) {
    console.error('Email delivery webhook processing error:', error);
    return NextResponse.json(
      { error: 'Failed to process email delivery webhook' },
      { status: 500 }
    );
  }
}

/**
 * Parse delivery webhook based on provider format
 */
function parseDeliveryWebhook(body: any, provider: string, tenantId: string): EmailDeliveryEvent[] | null {
  try {
    switch (provider.toLowerCase()) {
      case 'sendgrid':
        return parseSendGridDeliveryWebhook(body, tenantId);
      case 'mailgun':
        return parseMailgunDeliveryWebhook(body, tenantId);
      case 'postmark':
        return parsePostmarkDeliveryWebhook(body, tenantId);
      case 'aws-ses':
        return parseAWSSESDeliveryWebhook(body, tenantId);
      default:
        return parseGenericDeliveryWebhook(body, tenantId);
    }
  } catch (error) {
    console.error('Error parsing delivery webhook:', error);
    return null;
  }
}

/**
 * Parse SendGrid delivery webhook
 */
function parseSendGridDeliveryWebhook(body: any, tenantId: string): EmailDeliveryEvent[] {
  // SendGrid sends an array of events
  if (!Array.isArray(body)) {
    return [];
  }

  return body.map((event: any) => ({
    messageId: event.sg_message_id,
    tenantId,
    status: mapSendGridStatus(event.event),
    timestamp: new Date(event.timestamp * 1000),
    recipient: event.email,
    reason: event.reason,
    bounceType: event.type === 'bounce' ? (event.bounce_classification === 'hard' ? 'hard' : 'soft') : undefined,
    clickedUrl: event.url,
    userAgent: event.useragent,
    ipAddress: event.ip,
    provider: 'sendgrid',
    metadata: {
      eventId: event.sg_event_id,
      category: event.category,
      asm_group_id: event.asm_group_id,
    }
  })).filter((event: any) => event.status); // Filter out unmapped statuses
}

/**
 * Parse Mailgun delivery webhook
 */
function parseMailgunDeliveryWebhook(body: any, tenantId: string): EmailDeliveryEvent[] {
  const eventData = body['event-data'];
  if (!eventData) {
    return [];
  }

  const event: EmailDeliveryEvent = {
    messageId: eventData.message?.headers?.['message-id'],
    tenantId,
    status: mapMailgunStatus(eventData.event),
    timestamp: new Date(eventData.timestamp * 1000),
    recipient: eventData.recipient,
    reason: eventData['delivery-status']?.description || eventData.reason,
    bounceType: eventData.severity === 'permanent' ? 'hard' : 'soft',
    clickedUrl: eventData.url,
    userAgent: eventData['user-agent'],
    ipAddress: eventData['client-info']?.['client-ip'],
    provider: 'mailgun',
    metadata: {
      eventId: eventData.id,
      tags: eventData.tags,
      campaigns: eventData.campaigns,
    }
  };

  return event.status ? [event] : [];
}

/**
 * Parse Postmark delivery webhook
 */
function parsePostmarkDeliveryWebhook(body: any, tenantId: string): EmailDeliveryEvent[] {
  const event: EmailDeliveryEvent = {
    messageId: body.MessageID,
    tenantId,
    status: mapPostmarkStatus(body.RecordType),
    timestamp: new Date(body.DeliveredAt || body.BouncedAt || Date.now()),
    recipient: body.Email,
    reason: body.Description || body.Details,
    bounceType: body.Type === 'HardBounce' ? 'hard' : 'soft',
    clickedUrl: body.OriginalLink,
    userAgent: body.UserAgent,
    ipAddress: body.IP,
    provider: 'postmark',
    metadata: {
      messageStream: body.MessageStream,
      tag: body.Tag,
      server: body.ServerID,
    }
  };

  return event.status ? [event] : [];
}

/**
 * Parse AWS SES delivery webhook
 */
function parseAWSSESDeliveryWebhook(body: any, tenantId: string): EmailDeliveryEvent[] {
  // AWS SES sends SNS notifications
  const message = typeof body.Message === 'string' ? JSON.parse(body.Message) : body.Message;
  
  if (!message) {
    return [];
  }

  const event: EmailDeliveryEvent = {
    messageId: message.mail?.messageId,
    tenantId,
    status: mapAWSSESStatus(message.eventType || message.notificationType),
    timestamp: new Date(message.mail?.timestamp || Date.now()),
    recipient: message.delivery?.recipients?.[0] || message.bounce?.bouncedRecipients?.[0]?.emailAddress,
    reason: message.bounce?.bouncedRecipients?.[0]?.diagnosticCode || message.complaint?.complaintFeedbackType,
    bounceType: message.bounce?.bounceType === 'Permanent' ? 'hard' : 'soft',
    provider: 'aws-ses',
    metadata: {
      source: message.mail?.source,
      destination: message.mail?.destination,
      tags: message.mail?.tags,
    }
  };

  return event.status ? [event] : [];
}

/**
 * Parse generic delivery webhook
 */
function parseGenericDeliveryWebhook(body: any, tenantId: string): EmailDeliveryEvent[] {
  // Try to parse a generic format
  const event: EmailDeliveryEvent = {
    messageId: body.messageId || body.message_id,
    tenantId,
    status: body.status || body.event,
    timestamp: body.timestamp ? new Date(body.timestamp) : new Date(),
    recipient: body.recipient || body.email,
    reason: body.reason || body.description,
    bounceType: body.bounceType || (body.type === 'hard' ? 'hard' : 'soft'),
    clickedUrl: body.url || body.clickedUrl,
    userAgent: body.userAgent || body.user_agent,
    ipAddress: body.ip || body.ipAddress,
    provider: 'generic',
    metadata: body.metadata || {}
  };

  return event.status ? [event] : [];
}

// Status mapping functions
function mapSendGridStatus(event: string): string | null {
  const statusMap: Record<string, string> = {
    'delivered': 'delivered',
    'open': 'opened',
    'click': 'clicked',
    'bounce': 'bounced',
    'dropped': 'failed',
    'deferred': 'failed',
    'spamreport': 'spam',
    'unsubscribe': 'unsubscribed',
  };
  return statusMap[event] || null;
}

function mapMailgunStatus(event: string): string | null {
  const statusMap: Record<string, string> = {
    'delivered': 'delivered',
    'opened': 'opened',
    'clicked': 'clicked',
    'bounced': 'bounced',
    'failed': 'failed',
    'complained': 'spam',
    'unsubscribed': 'unsubscribed',
  };
  return statusMap[event] || null;
}

function mapPostmarkStatus(recordType: string): string | null {
  const statusMap: Record<string, string> = {
    'Delivery': 'delivered',
    'Open': 'opened',
    'Click': 'clicked',
    'Bounce': 'bounced',
    'SpamComplaint': 'spam',
    'SubscriptionChange': 'unsubscribed',
  };
  return statusMap[recordType] || null;
}

function mapAWSSESStatus(eventType: string): string | null {
  const statusMap: Record<string, string> = {
    'delivery': 'delivered',
    'open': 'opened',
    'click': 'clicked',
    'bounce': 'bounced',
    'complaint': 'spam',
    'reject': 'failed',
  };
  return statusMap[eventType] || null;
}

// Health check endpoint
export async function GET() {
  return NextResponse.json({
    status: 'ok',
    service: 'email-delivery-webhook',
    timestamp: new Date().toISOString()
  });
}
