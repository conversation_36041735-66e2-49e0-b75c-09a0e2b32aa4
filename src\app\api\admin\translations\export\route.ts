import { NextResponse } from 'next/server';
import { withTenantAdmin } from '@/lib/permission-middleware';
import { TranslationService } from '@/services/translation-service';
import { AuthenticatedRequest } from '@/lib/permission-middleware';

async function getHandler(req: AuthenticatedRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const format = searchParams.get('format') as 'json' | 'csv' | 'xlsx';
    const language = searchParams.get('language') as 'en' | 'ar' | undefined;

    if (!format || !['json', 'csv', 'xlsx'].includes(format)) {
      return NextResponse.json(
        { error: 'Invalid format. Supported formats: json, csv, xlsx' },
        { status: 400 }
      );
    }

    const data = await TranslationService.exportTranslations(format, language);

    const headers: Record<string, string> = {};
    let filename = `translations${language ? `-${language}` : ''}`;

    switch (format) {
      case 'json':
        headers['Content-Type'] = 'application/json';
        filename += '.json';
        break;
      case 'csv':
        headers['Content-Type'] = 'text/csv; charset=utf-8';
        filename += '.csv';
        break;
      case 'xlsx':
        headers['Content-Type'] = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
        filename += '.xlsx';
        break;
    }

    headers['Content-Disposition'] = `attachment; filename="${filename}"`;

    return new NextResponse(data as string, { headers });
  } catch (error) {
    console.error('Failed to export translations:', error);
    return NextResponse.json(
      { error: 'Failed to export translations' },
      { status: 500 }
    );
  }
}

export const GET = withTenantAdmin(getHandler);
