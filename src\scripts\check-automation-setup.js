#!/usr/bin/env node

/**
 * Quick setup check for Process Automation system
 * This script checks if the automation system is properly configured
 */

import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';

console.log('🔍 Checking Process Automation Setup...\n');

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function checkFile(filePath, description) {
  const fullPath = path.join(process.cwd(), filePath);
  if (fs.existsSync(fullPath)) {
    log(`✅ ${description}`, 'green');
    return true;
  } else {
    log(`❌ ${description} - File not found: ${filePath}`, 'red');
    return false;
  }
}

function runCommand(command, description, optional = false) {
  try {
    log(`🔄 ${description}...`, 'blue');
    const output = execSync(command, { encoding: 'utf8', stdio: 'pipe' });
    log(`✅ ${description} - Success`, 'green');
    return { success: true, output };
  } catch (error) {
    if (optional) {
      log(`⚠️  ${description} - Skipped (${error.message})`, 'yellow');
      return { success: false, output: error.message, optional: true };
    } else {
      log(`❌ ${description} - Failed: ${error.message}`, 'red');
      return { success: false, output: error.message };
    }
  }
}

async function main() {
  let issues = [];
  let fixes = [];

  // 1. Check if required files exist
  log('1. Checking required files...', 'bold');
  
  const requiredFiles = [
    ['prisma/schema.prisma', 'Prisma schema file'],
    ['src/services/process-automation.ts', 'Process automation service'],
    ['src/components/automation/automation-dashboard.tsx', 'Automation dashboard component'],
    ['src/app/api/automation/executions/route.ts', 'Automation API routes'],
    ['src/app/(dashboard)/admin/automation/page.tsx', 'Automation admin page']
  ];

  let filesOk = true;
  for (const [file, desc] of requiredFiles) {
    if (!checkFile(file, desc)) {
      filesOk = false;
      issues.push(`Missing file: ${file}`);
    }
  }

  if (!filesOk) {
    log('\n❌ Some required files are missing. Please ensure all automation files are present.', 'red');
    return;
  }

  // 2. Check Prisma schema for automation tables
  log('\n2. Checking Prisma schema...', 'bold');
  
  try {
    const schemaContent = fs.readFileSync('prisma/schema.prisma', 'utf8');
    if (schemaContent.includes('model AutomationTrigger') && schemaContent.includes('model AutomationExecution')) {
      log('✅ Automation tables defined in schema', 'green');
    } else {
      log('❌ Automation tables not found in schema', 'red');
      issues.push('Automation tables missing from Prisma schema');
    }
  } catch (error) {
    log(`❌ Could not read Prisma schema: ${error.message}`, 'red');
    issues.push('Cannot read Prisma schema');
  }

  // 3. Check if database is accessible
  log('\n3. Checking database connection...', 'bold');
  
  const dbCheck = runCommand('npx prisma db execute --stdin <<< "SELECT 1;"', 'Database connection test', true);
  if (!dbCheck.success && !dbCheck.optional) {
    issues.push('Database connection failed');
    fixes.push('Check your DATABASE_URL in .env file');
  }

  // 4. Try to generate Prisma client
  log('\n4. Checking Prisma client...', 'bold');
  
  const generateResult = runCommand('npx prisma generate', 'Generate Prisma client');
  if (!generateResult.success) {
    issues.push('Prisma client generation failed');
    fixes.push('Fix Prisma schema errors and run: npx prisma generate');
  }

  // 5. Check if automation tables exist in database
  log('\n5. Checking database tables...', 'bold');
  
  const tableCheck = runCommand(
    'npx prisma db execute --stdin <<< "SELECT name FROM sqlite_master WHERE type=\'table\' AND name IN (\'automation_triggers\', \'automation_executions\');"',
    'Check automation tables',
    true
  );

  if (tableCheck.success && tableCheck.output.includes('automation_')) {
    log('✅ Automation tables exist in database', 'green');
  } else {
    log('⚠️  Automation tables may not exist in database', 'yellow');
    issues.push('Automation tables not found in database');
    fixes.push('Run: npm run db:push to create tables');
  }

  // 6. Summary and recommendations
  log('\n📋 Setup Summary', 'bold');
  
  if (issues.length === 0) {
    log('🎉 Process Automation setup looks good!', 'green');
    log('\n📝 Next steps:', 'blue');
    log('1. Visit /admin/automation to configure triggers');
    log('2. Test by changing an opportunity stage to "Closed Won"');
    log('3. Monitor automation activity in the dashboard');
  } else {
    log(`⚠️  Found ${issues.length} potential issues:`, 'yellow');
    issues.forEach((issue, i) => {
      log(`   ${i + 1}. ${issue}`, 'yellow');
    });

    if (fixes.length > 0) {
      log('\n🔧 Suggested fixes:', 'blue');
      fixes.forEach((fix, i) => {
        log(`   ${i + 1}. ${fix}`, 'blue');
      });
    }

    log('\n🚀 Quick setup commands:', 'bold');
    log('npm run db:push                    # Create database tables', 'blue');
    log('node src/scripts/setup-automation-system.ts  # Initialize triggers', 'blue');
  }

  // 7. Offer to run fixes
  if (fixes.length > 0) {
    log('\n❓ Would you like to run the database setup now? (This will run: npm run db:push)', 'yellow');
    log('   Press Ctrl+C to cancel, or wait 10 seconds to continue...', 'yellow');
    
    // Simple timeout for auto-execution
    setTimeout(() => {
      log('\n🔧 Running database setup...', 'blue');
      const setupResult = runCommand('npm run db:push', 'Create database tables');
      
      if (setupResult.success) {
        log('\n✅ Database setup completed!', 'green');
        log('🎯 You can now use the Process Automation system:', 'green');
        log('   • Visit /admin/automation to manage triggers', 'blue');
        log('   • Change opportunity stages to test automation', 'blue');
        log('   • Monitor executions in the dashboard', 'blue');
      } else {
        log('\n❌ Database setup failed. Please run manually:', 'red');
        log('   npm run db:push', 'blue');
      }
    }, 10000);
  }
}

// Handle Ctrl+C gracefully
process.on('SIGINT', () => {
  log('\n\n👋 Setup check cancelled by user', 'yellow');
  process.exit(0);
});

main().catch(error => {
  log(`\n❌ Setup check failed: ${error.message}`, 'red');
  process.exit(1);
});
