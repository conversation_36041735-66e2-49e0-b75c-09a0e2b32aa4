'use client';

import React, { useState, useEffect } from 'react';
import { usePermissions } from '@/components/providers/permission-provider';
import { PermissionGate } from '@/components/ui/permission-gate';
import { PermissionAction, PermissionResource } from '@/types';
import { useLocale, useTranslation } from '@/components/providers/locale-provider';
import { 
  LanguageIcon,
  PencilIcon,
  CheckIcon,
  XMarkIcon,
  PlusIcon,
  TrashIcon,
  DocumentDuplicateIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';

interface TranslationKey {
  key: string;
  en: string;
  ar: string;
  category: string;
  description?: string;
}

interface TranslationManagementProps {
  tenantId: string;
}

export function TranslationManagement({ tenantId }: TranslationManagementProps) {
  const [translations, setTranslations] = useState<TranslationKey[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [editingKey, setEditingKey] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [showAddForm, setShowAddForm] = useState(false);
  const [newTranslation, setNewTranslation] = useState<Partial<TranslationKey>>({});
  
  const { hasPermission } = usePermissions();
  const { locale } = useLocale();
  const { t } = useTranslation();

  // Load translations from files
  useEffect(() => {
    loadTranslations();
  }, []);

  const loadTranslations = async () => {
    try {
      setLoading(true);
      
      // Load both English and Arabic translation files
      const [enModule, arModule] = await Promise.all([
        import('@/i18n/locales/en.json'),
        import('@/i18n/locales/ar.json')
      ]);

      const enTranslations = enModule.default;
      const arTranslations = arModule.default;

      // Flatten nested translation objects
      const flattenedTranslations = flattenTranslations(enTranslations, arTranslations);
      setTranslations(flattenedTranslations);
    } catch (err) {
      setError('Failed to load translations');
      console.error('Translation loading error:', err);
    } finally {
      setLoading(false);
    }
  };

  const flattenTranslations = (en: any, ar: any, prefix = ''): TranslationKey[] => {
    const result: TranslationKey[] = [];
    
    const processObject = (enObj: any, arObj: any, currentPrefix: string, category: string) => {
      Object.keys(enObj).forEach(key => {
        const fullKey = currentPrefix ? `${currentPrefix}.${key}` : key;
        const enValue = enObj[key];
        const arValue = arObj?.[key] || '';

        if (typeof enValue === 'object' && enValue !== null) {
          // Recursive call for nested objects
          processObject(enValue, arValue, fullKey, key);
        } else {
          // Leaf node - actual translation
          result.push({
            key: fullKey,
            en: String(enValue),
            ar: String(arValue),
            category: category || 'common'
          });
        }
      });
    };

    processObject(en, ar, prefix, '');
    return result;
  };

  const categories = Array.from(new Set(translations.map(t => t.category)));

  const filteredTranslations = translations.filter(translation => {
    const matchesSearch = translation.key.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         translation.en.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         translation.ar.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || translation.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const handleSaveTranslation = async (key: string, enValue: string, arValue: string) => {
    try {
      // Update local state
      setTranslations(prev => prev.map(t => 
        t.key === key ? { ...t, en: enValue, ar: arValue } : t
      ));
      
      // Here you would typically save to your backend/file system
      // For now, we'll just update the local state
      setEditingKey(null);
      
      // Show success message
      console.log('Translation saved:', { key, en: enValue, ar: arValue });
    } catch (err) {
      setError('Failed to save translation');
      console.error('Save error:', err);
    }
  };

  const handleAddTranslation = async () => {
    if (!newTranslation.key || !newTranslation.en || !newTranslation.ar) {
      setError('All fields are required');
      return;
    }

    try {
      const translation: TranslationKey = {
        key: newTranslation.key,
        en: newTranslation.en,
        ar: newTranslation.ar,
        category: newTranslation.category || 'custom'
      };

      setTranslations(prev => [...prev, translation]);
      setNewTranslation({});
      setShowAddForm(false);
      
      console.log('Translation added:', translation);
    } catch (err) {
      setError('Failed to add translation');
      console.error('Add error:', err);
    }
  };

  const handleDeleteTranslation = async (key: string) => {
    if (!confirm('Are you sure you want to delete this translation?')) {
      return;
    }

    try {
      setTranslations(prev => prev.filter(t => t.key !== key));
      console.log('Translation deleted:', key);
    } catch (err) {
      setError('Failed to delete translation');
      console.error('Delete error:', err);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-2">Loading translations...</span>
      </div>
    );
  }

  return (
    <PermissionGate
      resource={PermissionResource.SETTINGS}
      action={PermissionAction.MANAGE}
      fallback={
        <div className="text-center py-8">
          <ExclamationTriangleIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-500">You don't have permission to manage translations</p>
        </div>
      }
    >
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">Translation Management</h2>
            <p className="text-gray-600">Manage multilingual content for your application</p>
          </div>
          
          <button
            onClick={() => setShowAddForm(true)}
            className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors flex items-center space-x-2"
          >
            <PlusIcon className="w-4 h-4" />
            <span>Add Translation</span>
          </button>
        </div>

        {/* Error Display */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-md p-4">
            <p className="text-red-800">{error}</p>
            <button
              onClick={() => setError(null)}
              className="text-red-600 hover:text-red-800 text-sm mt-2"
            >
              Dismiss
            </button>
          </div>
        )}

        {/* Filters */}
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <input
              type="text"
              placeholder="Search translations..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          
          <select
            value={selectedCategory}
            onChange={(e) => setSelectedCategory(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="all">All Categories</option>
            {categories.map(category => (
              <option key={category} value={category}>
                {category.charAt(0).toUpperCase() + category.slice(1)}
              </option>
            ))}
          </select>
        </div>

        {/* Statistics */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="bg-white p-4 rounded-lg border border-gray-200">
            <div className="flex items-center">
              <LanguageIcon className="w-8 h-8 text-blue-600" />
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-500">Total Translations</p>
                <p className="text-2xl font-bold text-gray-900">{translations.length}</p>
              </div>
            </div>
          </div>
          
          <div className="bg-white p-4 rounded-lg border border-gray-200">
            <div className="flex items-center">
              <DocumentDuplicateIcon className="w-8 h-8 text-green-600" />
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-500">Categories</p>
                <p className="text-2xl font-bold text-gray-900">{categories.length}</p>
              </div>
            </div>
          </div>
          
          <div className="bg-white p-4 rounded-lg border border-gray-200">
            <div className="flex items-center">
              <CheckIcon className="w-8 h-8 text-purple-600" />
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-500">Filtered Results</p>
                <p className="text-2xl font-bold text-gray-900">{filteredTranslations.length}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Add Translation Form */}
        {showAddForm && (
          <div className="bg-white p-6 rounded-lg border border-gray-200">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Add New Translation</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Translation Key
                </label>
                <input
                  type="text"
                  value={newTranslation.key || ''}
                  onChange={(e) => setNewTranslation(prev => ({ ...prev, key: e.target.value }))}
                  placeholder="e.g., common.save"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Category
                </label>
                <input
                  type="text"
                  value={newTranslation.category || ''}
                  onChange={(e) => setNewTranslation(prev => ({ ...prev, category: e.target.value }))}
                  placeholder="e.g., common"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  English Text
                </label>
                <input
                  type="text"
                  value={newTranslation.en || ''}
                  onChange={(e) => setNewTranslation(prev => ({ ...prev, en: e.target.value }))}
                  placeholder="English translation"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Arabic Text
                </label>
                <input
                  type="text"
                  value={newTranslation.ar || ''}
                  onChange={(e) => setNewTranslation(prev => ({ ...prev, ar: e.target.value }))}
                  placeholder="Arabic translation"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  dir="rtl"
                />
              </div>
            </div>
            
            <div className="flex justify-end space-x-3 mt-4">
              <button
                onClick={() => {
                  setShowAddForm(false);
                  setNewTranslation({});
                }}
                className="px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={handleAddTranslation}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
              >
                Add Translation
              </button>
            </div>
          </div>
        )}

        {/* Translations Table */}
        <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Key
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Category
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    English
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Arabic
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredTranslations.map((translation) => (
                  <TranslationRow
                    key={translation.key}
                    translation={translation}
                    isEditing={editingKey === translation.key}
                    onEdit={() => setEditingKey(translation.key)}
                    onSave={handleSaveTranslation}
                    onCancel={() => setEditingKey(null)}
                    onDelete={handleDeleteTranslation}
                  />
                ))}
              </tbody>
            </table>
          </div>
          
          {filteredTranslations.length === 0 && (
            <div className="text-center py-8">
              <LanguageIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">No translations found</p>
            </div>
          )}
        </div>
      </div>
    </PermissionGate>
  );
}

interface TranslationRowProps {
  translation: TranslationKey;
  isEditing: boolean;
  onEdit: () => void;
  onSave: (key: string, en: string, ar: string) => void;
  onCancel: () => void;
  onDelete: (key: string) => void;
}

function TranslationRow({ 
  translation, 
  isEditing, 
  onEdit, 
  onSave, 
  onCancel, 
  onDelete 
}: TranslationRowProps) {
  const [enValue, setEnValue] = useState(translation.en);
  const [arValue, setArValue] = useState(translation.ar);

  useEffect(() => {
    setEnValue(translation.en);
    setArValue(translation.ar);
  }, [translation.en, translation.ar, isEditing]);

  const handleSave = () => {
    onSave(translation.key, enValue, arValue);
  };

  return (
    <tr className="hover:bg-gray-50">
      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
        {translation.key}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
          {translation.category}
        </span>
      </td>
      <td className="px-6 py-4 text-sm text-gray-900">
        {isEditing ? (
          <input
            type="text"
            value={enValue}
            onChange={(e) => setEnValue(e.target.value)}
            className="w-full px-2 py-1 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        ) : (
          <span className="block max-w-xs truncate">{translation.en}</span>
        )}
      </td>
      <td className="px-6 py-4 text-sm text-gray-900">
        {isEditing ? (
          <input
            type="text"
            value={arValue}
            onChange={(e) => setArValue(e.target.value)}
            className="w-full px-2 py-1 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
            dir="rtl"
          />
        ) : (
          <span className="block max-w-xs truncate" dir="rtl">{translation.ar}</span>
        )}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
        {isEditing ? (
          <div className="flex justify-end space-x-2">
            <button
              onClick={handleSave}
              className="text-green-600 hover:text-green-900"
            >
              <CheckIcon className="w-4 h-4" />
            </button>
            <button
              onClick={onCancel}
              className="text-gray-600 hover:text-gray-900"
            >
              <XMarkIcon className="w-4 h-4" />
            </button>
          </div>
        ) : (
          <div className="flex justify-end space-x-2">
            <button
              onClick={onEdit}
              className="text-blue-600 hover:text-blue-900"
            >
              <PencilIcon className="w-4 h-4" />
            </button>
            <button
              onClick={() => onDelete(translation.key)}
              className="text-red-600 hover:text-red-900"
            >
              <TrashIcon className="w-4 h-4" />
            </button>
          </div>
        )}
      </td>
    </tr>
  );
}
