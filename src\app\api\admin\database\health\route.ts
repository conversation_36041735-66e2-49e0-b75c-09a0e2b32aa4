import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { DatabasePerformanceMonitor, prisma } from '@/lib/prisma';

/**
 * GET /api/admin/database/health
 * Get comprehensive database health status
 */
export async function GET(request: NextRequest) {
  try {
    // Check authentication and admin permissions
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Check if user is platform admin
    if (!session.user.isPlatformAdmin) {
      return NextResponse.json(
        { error: 'Platform admin access required' },
        { status: 403 }
      );
    }

    // Get basic database health status
    const healthStatus = await getBasicHealthStatus();

    // Get query performance summary
    const performanceSummary = DatabasePerformanceMonitor.getPerformanceSummary();

    // Get basic performance recommendations
    const recommendations = await getBasicRecommendations();

    return NextResponse.json({
      health: healthStatus,
      performance: performanceSummary,
      recommendations,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Database health check error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to get database health status',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}

/**
 * POST /api/admin/database/health
 * Trigger database health check and optimization
 */
export async function POST(request: NextRequest) {
  try {
    // Check authentication and admin permissions
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Check if user is platform admin
    if (!session.user.isPlatformAdmin) {
      return NextResponse.json(
        { error: 'Platform admin access required' },
        { status: 403 }
      );
    }

    const body = await request.json();
    const { action } = body;

    switch (action) {
      case 'clear_metrics':
        // Clear performance metrics
        DatabasePerformanceMonitor.getQueryStats(); // Reset internal state
        return NextResponse.json({
          success: true,
          message: 'Performance metrics cleared'
        });

      case 'analyze_performance':
        // Get detailed performance analysis
        const queryStats = DatabasePerformanceMonitor.getQueryStats();
        const healthStatus = await getBasicHealthStatus();

        return NextResponse.json({
          success: true,
          analysis: {
            queryStats,
            healthStatus,
            timestamp: new Date().toISOString()
          }
        });

      case 'vacuum_analyze':
        // This would trigger database maintenance (implement with caution)
        return NextResponse.json({
          success: true,
          message: 'Database maintenance scheduled (not implemented for safety)'
        });

      default:
        return NextResponse.json(
          { error: 'Invalid action specified' },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('Database health action error:', error);
    return NextResponse.json(
      {
        error: 'Failed to execute database health action',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}

/**
 * Get basic database health status
 */
async function getBasicHealthStatus() {
  const startTime = Date.now();

  try {
    // Test basic connectivity
    await prisma.$queryRaw`SELECT 1`;

    // Get active connections
    const connectionResult = await prisma.$queryRaw<[{ count: bigint }]>`
      SELECT count(*) as count
      FROM pg_stat_activity
      WHERE state = 'active' AND datname = current_database()
    `;

    // Get database size
    const sizeResult = await prisma.$queryRaw<[{ size: string }]>`
      SELECT pg_size_pretty(pg_database_size(current_database())) as size
    `;

    const responseTime = Date.now() - startTime;

    return {
      status: 'healthy',
      responseTime,
      activeConnections: Number(connectionResult[0].count),
      databaseSize: sizeResult[0].size,
      timestamp: new Date()
    };
  } catch (error) {
    return {
      status: 'error',
      responseTime: Date.now() - startTime,
      error: error instanceof Error ? error.message : String(error),
      timestamp: new Date()
    };
  }
}

/**
 * Get basic performance recommendations
 */
async function getBasicRecommendations(): Promise<string[]> {
  const recommendations: string[] = [];

  try {
    // Check for basic performance issues
    const performanceStats = DatabasePerformanceMonitor.getPerformanceSummary();

    if (performanceStats.slowQueryRate > 10) {
      recommendations.push(`High slow query rate detected: ${performanceStats.slowQueryRate}% of queries are slow`);
    }

    if (performanceStats.averageResponseTime > 500) {
      recommendations.push(`Average response time is high: ${performanceStats.averageResponseTime}ms`);
    }

    if (performanceStats.totalQueries > 1000) {
      recommendations.push('Consider implementing query result caching for frequently accessed data');
    }

    // Check database size
    const sizeResult = await prisma.$queryRaw<[{ size_bytes: bigint }]>`
      SELECT pg_database_size(current_database()) as size_bytes
    `;

    const sizeGB = Number(sizeResult[0].size_bytes) / (1024 * 1024 * 1024);
    if (sizeGB > 10) {
      recommendations.push(`Database size is ${sizeGB.toFixed(2)}GB - consider archiving old data`);
    }

  } catch (error) {
    recommendations.push('Unable to analyze database for recommendations');
  }

  return recommendations;
}
