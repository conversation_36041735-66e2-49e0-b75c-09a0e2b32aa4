import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { withTenantContext } from '@/lib/tenant-context';
import { prisma, DatabasePerformanceMonitor } from '@/lib/prisma';
import { tenantCache } from '@/lib/cache/tenant-cache';
import { cacheService, CACHE_PREFIXES, CACHE_TTL } from '@/lib/redis';
import { z } from 'zod';

const updateTenantSchema = z.object({
  name: z.string().min(2, 'Tenant name must be at least 2 characters').optional(),
  settings: z.record(z.any()).optional(),
  branding: z.object({
    logo: z.string().optional(),
    primaryColor: z.string().optional(),
    secondaryColor: z.string().optional()
  }).optional()
});

// Get user's tenant information
export async function GET(request: NextRequest) {
  const endTimer = DatabasePerformanceMonitor.startTimer('getUserTenant');

  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const userId = session.user.id;

    // Get user's tenant (should be only one)
    const userTenant = await prisma.tenantUser.findFirst({
      where: {
        userId,
        status: 'active'
      },
      include: {
        tenant: {
          include: {
            subscriptions: {
              where: { status: 'active' },
              include: { plan: true },
              take: 1,
              orderBy: { createdAt: 'desc' }
            }
          }
        }
      }
    });

    if (!userTenant) {
      return NextResponse.json({
        success: true,
        data: [],
        message: 'No tenant found for user'
      });
    }

    const tenant = userTenant.tenant;
    const subscription = tenant.subscriptions[0];

    // Get current month usage metrics
    const currentUsage = await prisma.usageMetric.findMany({
      where: {
        tenantId: tenant.id,
        periodStart: {
          gte: new Date(new Date().getFullYear(), new Date().getMonth(), 1)
        }
      },
      select: {
        metricName: true,
        metricValue: true
      }
    });

    // Calculate usage metrics efficiently
    const usage = currentUsage.reduce((acc, metric) => {
      acc[metric.metricName] = metric.metricValue;
      return acc;
    }, {} as Record<string, number>);

    // Get user count for this tenant
    const userCount = await prisma.tenantUser.count({
      where: { tenantId: tenant.id }
    });

    const responseData = {
      success: true,
      data: [{
        id: tenant.id,
        name: tenant.name,
        slug: tenant.slug,
        status: tenant.status,
        settings: tenant.settings,
        branding: tenant.branding,
        userRole: {
          role: userTenant.role,
          isTenantAdmin: userTenant.isTenantAdmin,
          joinedAt: userTenant.joinedAt
        },
        subscription: subscription ? {
          planId: subscription.planId,
          planName: subscription.plan.name,
          status: subscription.status,
          currentPeriodStart: subscription.currentPeriodStart,
          currentPeriodEnd: subscription.currentPeriodEnd,
          features: subscription.plan.features,
          limits: subscription.plan.limits
        } : null,
        usage,
        userCount,
        createdAt: tenant.createdAt,
        updatedAt: tenant.updatedAt
      }]
    };

    return NextResponse.json(responseData);

  } catch (error) {
    console.error('Get tenant error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  } finally {
    endTimer();
  }
}

// Update tenant information
export const PUT = withTenantContext(async (request: NextRequest) => {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const tenantId = (request as any).tenantId;
    const userId = session.user.id;

    // Check if user is tenant admin
    const tenantUser = await prisma.tenantUser.findUnique({
      where: {
        tenantId_userId: {
          tenantId,
          userId
        }
      }
    });

    if (!tenantUser?.isTenantAdmin) {
      return NextResponse.json(
        { error: 'Only tenant administrators can update tenant settings' },
        { status: 403 }
      );
    }

    const body = await request.json();
    const validatedData = updateTenantSchema.parse(body);

    // Get current tenant data for audit log
    const currentTenant = await prisma.tenant.findUnique({
      where: { id: tenantId }
    });

    // Update tenant
    const updatedTenant = await prisma.tenant.update({
      where: { id: tenantId },
      data: {
        ...(validatedData.name && { name: validatedData.name }),
        ...(validatedData.settings && { settings: validatedData.settings }),
        ...(validatedData.branding && { branding: validatedData.branding }),
        updatedAt: new Date()
      }
    });

    // Log the update
    await prisma.auditLog.create({
      data: {
        tenantId,
        userId,
        action: 'TENANT_UPDATED',
        resourceType: 'tenant',
        resourceId: tenantId,
        oldValues: {
          name: currentTenant?.name,
          settings: currentTenant?.settings,
          branding: currentTenant?.branding
        },
        newValues: validatedData
      }
    });

    // Invalidate related caches
    await Promise.all([
      // Invalidate tenant info cache
      cacheService.del(`tenant-info:${tenantId}`, CACHE_PREFIXES.API_RESPONSE),
      // Invalidate tenant data cache
      tenantCache.invalidateTenantDataType(tenantId, 'data'),
      // Invalidate tenant settings cache if settings were updated
      validatedData.settings && tenantCache.invalidateTenantDataType(tenantId, 'settings')
    ].filter(Boolean));

    return NextResponse.json({
      success: true,
      data: {
        id: updatedTenant.id,
        name: updatedTenant.name,
        slug: updatedTenant.slug,
        settings: updatedTenant.settings,
        branding: updatedTenant.branding,
        updatedAt: updatedTenant.updatedAt
      },
      message: 'Tenant updated successfully'
    });

  } catch (error) {
    console.error('Update tenant error:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          error: 'Validation failed',
          details: error.errors.map(e => ({
            field: e.path.join('.'),
            message: e.message
          }))
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
});

// Get tenant usage statistics with caching
export async function POST(request: NextRequest) {
  const endTimer = DatabasePerformanceMonitor.startTimer('getTenantUsage');

  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { action, tenantId } = await request.json();

    if (action === 'usage') {
      // Try to get usage from cache first
      const cachedUsage = await tenantCache.getTenantUsage(tenantId);

      if (cachedUsage) {
        console.log(`✅ Cache hit for tenant usage ${tenantId}`);
        return NextResponse.json({
          success: true,
          data: {
            contacts: cachedUsage.contacts,
            leads: cachedUsage.leads,
            users: cachedUsage.users,
            ai_requests: cachedUsage.ai_requests,
            storage_mb: cachedUsage.storage_mb
          }
        });
      }

      console.log(`🔍 Cache miss for tenant usage ${tenantId}, fetching from database`);

      // Fetch from database with optimized query
      const usage = await prisma.usageMetric.findMany({
        where: {
          tenantId,
          periodStart: {
            gte: new Date(new Date().getFullYear(), new Date().getMonth(), 1)
          }
        },
        select: {
          metricName: true,
          metricValue: true
        }
      });

      const usageData = usage.reduce((acc, metric) => {
        acc[metric.metricName] = metric.metricValue;
        return acc;
      }, {} as Record<string, number>);

      // Cache the usage data
      await tenantCache.setTenantUsage(tenantId, {
        contacts: usageData.contacts || 0,
        leads: usageData.leads || 0,
        users: usageData.users || 0,
        ai_requests: usageData.ai_requests || 0,
        storage_mb: usageData.storage_mb || 0,
        lastUpdated: Date.now()
      });

      return NextResponse.json({
        success: true,
        data: usageData
      });
    }

    return NextResponse.json(
      { error: 'Invalid action' },
      { status: 400 }
    );

  } catch (error) {
    console.error('Tenant action error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  } finally {
    endTimer();
  }
}
