'use client';

import { Metada<PERSON> } from 'next';
import { Suspense, use } from 'react';
import HandoverForm from '@/components/handovers/HandoverForm';
import { PermissionGate } from '@/components/ui/permission-gate';
import { PermissionResource, PermissionAction } from '@/types';
import { Card, CardContent } from '@/components/ui/card';
import { EmptyState } from '@/components/ui/empty-state';
import { ExclamationTriangleIcon } from '@heroicons/react/24/outline';
import { PageLayout } from '@/components/layout/page-layout';
import { FormPageSkeleton } from '@/components/ui/skeleton-loaders';

interface PageProps {
  searchParams: Promise<{
    opportunityId?: string;
  }>;
}

export default function NewHandoverPage({ searchParams }: PageProps) {
  const resolvedSearchParams = use(searchParams);

  // TODO: Re-enable permission gate after running permission seeding
  // Run: node scripts/seed-permissions.js

  return (
    <PageLayout
      title="Create Handover"
      description="Create a new sales-to-delivery handover"
    >
      <Suspense fallback={<FormPageSkeleton />}>
        <HandoverForm
          opportunityId={resolvedSearchParams.opportunityId}
        />
      </Suspense>
    </PageLayout>
  );

  // Uncomment this after seeding handover permissions:
  /*
  return (
    <PermissionGate
      resource={PermissionResource.HANDOVERS}
      action={PermissionAction.CREATE}
      fallback={
        <PageLayout>
          <Card>
            <CardContent className="p-8">
              <EmptyState
                icon={<ExclamationTriangleIcon className="w-12 h-12 text-muted-foreground" />}
                title="Access Denied"
                description="You don't have permission to create handovers. Please contact your administrator for access."
                action={{
                  label: 'Back to Handovers',
                  onClick: () => window.location.href = '/handovers',
                  variant: 'primary'
                }}
              />
            </CardContent>
          </Card>
        </PageLayout>
      }
    >
      <PageLayout
        title="Create Handover"
        description="Create a new sales-to-delivery handover"
      >
        <Suspense fallback={<FormPageSkeleton />}>
          <HandoverForm
            opportunityId={resolvedSearchParams.opportunityId}
          />
        </Suspense>
      </PageLayout>
    </PermissionGate>
  );
  */
}
