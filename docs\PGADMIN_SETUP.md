# pgAdmin Setup Guide

This guide explains how to use pgAdmin for database management in the CRM platform Docker environment.

## Overview

pgAdmin is a comprehensive PostgreSQL administration and development platform that provides a web-based interface for managing your PostgreSQL databases.

## Access Information

- **URL**: http://localhost:8082
- **Email**: <EMAIL>
- **Password**: admin

## Initial Setup

### 1. Access pgAdmin

1. Start your Docker containers:
   ```bash
   docker-compose -f docker-compose.yml -f docker-compose.dev.yml up -d
   ```

2. Open your browser and navigate to http://localhost:8082

3. Login with the credentials:
   - Email: `<EMAIL>`
   - Password: `admin`

### 2. Add Database Server

After logging in, you need to add the PostgreSQL server connection:

1. **Right-click on "Servers"** in the left sidebar
2. **Select "Register" > "Server..."**
3. **Fill in the connection details:**

   **General Tab:**
   - Name: `CRM Database`

   **Connection Tab:**
   - Host name/address: `postgres`
   - Port: `5432`
   - Maintenance database: `crm_platform`
   - Username: `admin`
   - Password: `admin`

4. **Click "Save"**

## Features

### Database Management
- View and edit table data
- Execute SQL queries
- Manage database schemas
- View database statistics

### Query Tool
- Write and execute SQL queries
- View query execution plans
- Export query results

### Data Management
- Import/export data
- Backup and restore databases
- View table relationships

## Common Tasks

### Viewing Table Data
1. Navigate to: `Servers > CRM Database > Databases > crm_platform > Schemas > public > Tables`
2. Right-click on any table
3. Select "View/Edit Data" > "All Rows"

### Running SQL Queries
1. Right-click on the database name
2. Select "Query Tool"
3. Write your SQL query
4. Click the "Execute" button (▶️)

### Database Backup
1. Right-click on the database name
2. Select "Backup..."
3. Configure backup options
4. Click "Backup"

## Troubleshooting

### Cannot Connect to Database
- Ensure the PostgreSQL container is running: `docker-compose ps postgres`
- Check that you're using `postgres` as the hostname (not `localhost`)
- Verify the database credentials match your Docker configuration

### pgAdmin Won't Load
- Check if the container is running: `docker-compose ps pgadmin`
- Verify port 8082 is not being used by another application
- Check container logs: `docker-compose logs pgadmin`

### Permission Issues
- Ensure the pgAdmin container has proper volume permissions
- Try restarting the containers: `docker-compose restart pgadmin`

## Security Notes

- The default credentials are for development only
- In production, change the default email and password
- Consider using environment variables for sensitive configuration
- Restrict network access to pgAdmin in production environments

## Comparison with Adminer

pgAdmin offers several advantages over Adminer:

- **PostgreSQL-specific**: Optimized specifically for PostgreSQL
- **Advanced features**: Query planner, performance monitoring, advanced backup options
- **Better UI**: More comprehensive and user-friendly interface
- **Extensibility**: Plugin support and customization options
- **Professional tools**: Advanced debugging and optimization features

## Additional Resources

- [pgAdmin Documentation](https://www.pgadmin.org/docs/)
- [PostgreSQL Documentation](https://www.postgresql.org/docs/)
- [Docker pgAdmin Image](https://hub.docker.com/r/dpage/pgadmin4/)
