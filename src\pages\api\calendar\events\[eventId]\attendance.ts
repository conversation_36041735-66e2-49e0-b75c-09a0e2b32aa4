import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../../../../../lib/auth';
import { CalendarEventService } from '../../../../../services/communication';
import { withTenant } from '../../../../../lib/middleware/withTenant';
import { withRateLimit } from '../../../../../lib/middleware/withRateLimit';

async function handler(req: NextApiRequest, res: NextApiResponse) {
  const session = await getServerSession(req, res, authOptions);
  
  if (!session?.user?.id) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  const tenantId = req.headers['x-tenant-id'] as string;
  const userId = session.user.id;
  const eventId = req.query.eventId as string;

  if (!eventId) {
    return res.status(400).json({ error: 'Event ID is required' });
  }

  try {
    switch (req.method) {
      case 'PUT':
        const { status, response } = req.body;
        
        if (!status || !['accepted', 'declined', 'tentative'].includes(status)) {
          return res.status(400).json({ error: 'Valid status is required (accepted, declined, tentative)' });
        }

        const updatedAttendance = await CalendarEventService.updateEventAttendance(
          tenantId,
          eventId,
          userId,
          status,
          response
        );
        return res.status(200).json(updatedAttendance);

      default:
        res.setHeader('Allow', ['PUT']);
        return res.status(405).json({ error: `Method ${req.method} not allowed` });
    }
  } catch (error) {
    console.error('Event attendance API error:', error);
    return res.status(500).json({ 
      error: error instanceof Error ? error.message : 'Internal server error' 
    });
  }
}

export default withRateLimit(withTenant(handler));
