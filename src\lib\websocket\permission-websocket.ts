'use client';

import { Socket } from 'socket.io-client';
import { Permission, Role } from '@/types';

export interface PermissionUpdateEvent {
  type: 'permission_update' | 'role_update' | 'permission_revoke';
  userId: string;
  tenantId: string;
  permissions?: Permission[];
  roles?: Role[];
  effectivePermissions?: Permission[];
  timestamp: string;
  reason?: string;
}

export interface WebSocketPermissionService {
  connect(): void;
  disconnect(): void;
  isConnected(): boolean;
  onPermissionUpdate(callback: (event: PermissionUpdateEvent) => void): void;
  offPermissionUpdate(callback: (event: PermissionUpdateEvent) => void): void;
  requestPermissionRefresh(userId: string, tenantId: string): void;
}

class PermissionWebSocketService implements WebSocketPermissionService {
  private socket: Socket | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;
  private permissionUpdateCallbacks: Set<(event: PermissionUpdateEvent) => void> = new Set();
  private isConnecting = false;

  constructor() {
    // Auto-connect when service is instantiated
    if (typeof window !== 'undefined') {
      this.connect();
    }
  }

  connect(): void {
    if (this.socket?.connected || this.isConnecting) {
      return;
    }

    this.isConnecting = true;

    try {
      // Check if WebSocket is available and enabled
      if (typeof window === 'undefined' || !window.WebSocket) {
        console.warn('WebSocket not available, skipping real-time updates');
        this.isConnecting = false;
        return;
      }

      // For now, we'll simulate WebSocket connection without actual server
      // This allows the caching system to work without requiring WebSocket setup
      console.log('🔌 WebSocket connection simulated (server not required for caching)');
      this.isConnecting = false;

      // Simulate connection success for demo purposes
      setTimeout(() => {
        console.log('✅ Permission WebSocket simulated connection ready');
      }, 1000);

      // TODO: Uncomment when WebSocket server is set up
      /*
      // Initialize Socket.IO connection
      this.socket = io(process.env.NEXT_PUBLIC_WEBSOCKET_URL || window.location.origin, {
        path: '/api/socket/permissions',
        transports: ['websocket', 'polling'],
        timeout: 10000,
        reconnection: true,
        reconnectionAttempts: this.maxReconnectAttempts,
        reconnectionDelay: this.reconnectDelay,
        auth: {
          // Include authentication token if available
          token: this.getAuthToken()
        }
      });

      this.setupEventHandlers();
      */
    } catch (error) {
      console.warn('WebSocket connection not available, continuing without real-time updates:', error);
      this.isConnecting = false;
    }
  }

  disconnect(): void {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
    this.isConnecting = false;
    this.reconnectAttempts = 0;
  }

  isConnected(): boolean {
    return this.socket?.connected || false;
  }

  onPermissionUpdate(callback: (event: PermissionUpdateEvent) => void): void {
    this.permissionUpdateCallbacks.add(callback);
  }

  offPermissionUpdate(callback: (event: PermissionUpdateEvent) => void): void {
    this.permissionUpdateCallbacks.delete(callback);
  }

  requestPermissionRefresh(userId: string, tenantId: string): void {
    if (this.socket?.connected) {
      this.socket.emit('request_permission_refresh', { userId, tenantId });
    }
  }

  private setupEventHandlers(): void {
    if (!this.socket) return;

    // Connection events
    this.socket.on('connect', () => {
      console.log('✅ Permission WebSocket connected');
      this.isConnecting = false;
      this.reconnectAttempts = 0;
      
      // Join user-specific room for permission updates
      const userInfo = this.getUserInfo();
      if (userInfo) {
        this.socket?.emit('join_permission_room', userInfo);
      }
    });

    this.socket.on('disconnect', (reason) => {
      console.log('❌ Permission WebSocket disconnected:', reason);
      this.isConnecting = false;
      
      // Attempt to reconnect if disconnection was unexpected
      if (reason === 'io server disconnect') {
        // Server initiated disconnect, don't reconnect automatically
        return;
      }
      
      this.handleReconnection();
    });

    this.socket.on('connect_error', (error) => {
      console.error('❌ Permission WebSocket connection error:', error);
      this.isConnecting = false;
      this.handleReconnection();
    });

    // Permission update events
    this.socket.on('permission_updated', (event: PermissionUpdateEvent) => {
      console.log('🔄 Permission update received:', event);
      this.notifyPermissionUpdate(event);
    });

    this.socket.on('role_updated', (event: PermissionUpdateEvent) => {
      console.log('🔄 Role update received:', event);
      this.notifyPermissionUpdate(event);
    });

    this.socket.on('permission_revoked', (event: PermissionUpdateEvent) => {
      console.log('🚫 Permission revoked:', event);
      this.notifyPermissionUpdate(event);
    });

    // Bulk permission updates (for efficiency)
    this.socket.on('bulk_permission_update', (events: PermissionUpdateEvent[]) => {
      console.log('🔄 Bulk permission update received:', events.length, 'updates');
      events.forEach(event => this.notifyPermissionUpdate(event));
    });

    // Permission refresh response
    this.socket.on('permission_refresh_complete', (data: { userId: string; tenantId: string; success: boolean }) => {
      console.log('✅ Permission refresh completed:', data);
    });

    // Error handling
    this.socket.on('permission_error', (error: { message: string; code?: string }) => {
      console.error('❌ Permission WebSocket error:', error);
    });
  }

  private handleReconnection(): void {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('❌ Max reconnection attempts reached for Permission WebSocket');
      return;
    }

    this.reconnectAttempts++;
    const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1); // Exponential backoff

    console.log(`🔄 Attempting to reconnect Permission WebSocket (attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts}) in ${delay}ms`);

    setTimeout(() => {
      if (!this.socket?.connected) {
        this.connect();
      }
    }, delay);
  }

  private notifyPermissionUpdate(event: PermissionUpdateEvent): void {
    this.permissionUpdateCallbacks.forEach(callback => {
      try {
        callback(event);
      } catch (error) {
        console.error('Error in permission update callback:', error);
      }
    });
  }

  private getAuthToken(): string | null {
    // Try to get auth token from various sources
    if (typeof window === 'undefined') return null;

    // Check for NextAuth session token
    const cookies = document.cookie.split(';');
    const sessionCookie = cookies.find(cookie => 
      cookie.trim().startsWith('next-auth.session-token=') ||
      cookie.trim().startsWith('__Secure-next-auth.session-token=')
    );

    if (sessionCookie) {
      return sessionCookie.split('=')[1];
    }

    // Fallback to localStorage or other auth mechanisms
    return localStorage.getItem('auth_token') || null;
  }

  private getUserInfo(): { userId: string; tenantId: string } | null {
    // This would typically come from your auth context or session
    // For now, we'll try to extract from localStorage or session
    try {
      const userInfo = localStorage.getItem('user_info');
      if (userInfo) {
        const parsed = JSON.parse(userInfo);
        if (parsed.userId && parsed.tenantId) {
          return { userId: parsed.userId, tenantId: parsed.tenantId };
        }
      }
    } catch (error) {
      console.error('Failed to get user info for WebSocket:', error);
    }
    return null;
  }
}

// Singleton instance
let permissionWebSocketService: PermissionWebSocketService | null = null;

export function getPermissionWebSocketService(): PermissionWebSocketService {
  if (!permissionWebSocketService) {
    permissionWebSocketService = new PermissionWebSocketService();
  }
  return permissionWebSocketService;
}

// Cross-tab communication using BroadcastChannel
export class CrossTabPermissionSync {
  private broadcastChannel: BroadcastChannel | null = null;
  private callbacks: Set<(event: PermissionUpdateEvent) => void> = new Set();

  constructor() {
    if (typeof window !== 'undefined' && 'BroadcastChannel' in window) {
      this.broadcastChannel = new BroadcastChannel('permission_updates');
      this.setupBroadcastHandlers();
    }
  }

  private setupBroadcastHandlers(): void {
    if (!this.broadcastChannel) return;

    this.broadcastChannel.addEventListener('message', (event) => {
      const permissionEvent: PermissionUpdateEvent = event.data;
      this.callbacks.forEach(callback => {
        try {
          callback(permissionEvent);
        } catch (error) {
          console.error('Error in cross-tab permission update callback:', error);
        }
      });
    });
  }

  broadcastPermissionUpdate(event: PermissionUpdateEvent): void {
    if (this.broadcastChannel) {
      this.broadcastChannel.postMessage(event);
    }
  }

  onPermissionUpdate(callback: (event: PermissionUpdateEvent) => void): void {
    this.callbacks.add(callback);
  }

  offPermissionUpdate(callback: (event: PermissionUpdateEvent) => void): void {
    this.callbacks.delete(callback);
  }

  close(): void {
    if (this.broadcastChannel) {
      this.broadcastChannel.close();
      this.broadcastChannel = null;
    }
    this.callbacks.clear();
  }
}

// Singleton for cross-tab sync
let crossTabSync: CrossTabPermissionSync | null = null;

export function getCrossTabPermissionSync(): CrossTabPermissionSync {
  if (!crossTabSync) {
    crossTabSync = new CrossTabPermissionSync();
  }
  return crossTabSync;
}
