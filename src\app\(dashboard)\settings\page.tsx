'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { PermissionProvider } from '@/components/providers/permission-provider';
import { PermissionGate } from '@/components/ui/permission-gate';
import { PermissionAction, PermissionResource } from '@/types';
import { useTenant } from '@/hooks/use-tenant';
import {
  Cog6ToothIcon,
  UserGroupIcon,
  BellIcon,
  ShieldCheckIcon,
  ExclamationTriangleIcon,
  RectangleGroupIcon
} from '@heroicons/react/24/outline';

type SettingsTab = 'general' | 'users' | 'teams' | 'notifications' | 'security';

export default function SettingsPage() {
  const [activeTab, setActiveTab] = useState<SettingsTab>('general');
  const { currentTenant } = useTenant();

  if (!currentTenant) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <ExclamationTriangleIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-500">No tenant context available</p>
        </div>
      </div>
    );
  }

  const tabs = [
    {
      id: 'general' as SettingsTab,
      name: 'General Settings',
      icon: Cog6ToothIcon,
      permission: { resource: PermissionResource.SETTINGS, action: PermissionAction.READ }
    },
    {
      id: 'users' as SettingsTab,
      name: 'User Management',
      icon: UserGroupIcon,
      permission: { resource: PermissionResource.USERS, action: PermissionAction.READ }
    },
    {
      id: 'teams' as SettingsTab,
      name: 'Team Management',
      icon: RectangleGroupIcon,
      permission: { resource: PermissionResource.USERS, action: PermissionAction.READ }
    },
    {
      id: 'notifications' as SettingsTab,
      name: 'Notifications',
      icon: BellIcon,
      permission: { resource: PermissionResource.SETTINGS, action: PermissionAction.READ }
    },
    {
      id: 'security' as SettingsTab,
      name: 'Security',
      icon: ShieldCheckIcon,
      permission: { resource: PermissionResource.SETTINGS, action: PermissionAction.READ }
    }
  ];

  return (
    <PermissionProvider>
      <div className="space-y-6">
        {/* Header */}
        <div className="border-b border-gray-200 pb-4">
          <div className="flex items-center space-x-3">
            <Cog6ToothIcon className="w-8 h-8 text-blue-600" />
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Settings</h1>
              <p className="text-gray-600 mt-1">
                Manage your organization settings for {currentTenant.name}
              </p>
            </div>
          </div>
        </div>

        {/* Tab Navigation */}
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            {tabs.map((tab) => (
              <PermissionGate
                key={tab.id}
                resource={tab.permission.resource}
                action={tab.permission.action}
              >
                <button
                  onClick={() => setActiveTab(tab.id)}
                  className={`
                    group inline-flex items-center py-2 px-1 border-b-2 font-medium text-sm transition-colors
                    ${activeTab === tab.id
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }
                  `}
                >
                  <tab.icon
                    className={`
                      -ml-0.5 mr-2 h-5 w-5 transition-colors
                      ${activeTab === tab.id
                        ? 'text-blue-500'
                        : 'text-gray-400 group-hover:text-gray-500'
                      }
                    `}
                  />
                  {tab.name}
                </button>
              </PermissionGate>
            ))}
          </nav>
        </div>

        {/* Tab Content */}
        <div className="mt-6">
          {activeTab === 'general' && (
            <PermissionGate
              resource={PermissionResource.SETTINGS}
              action={PermissionAction.READ}
              fallback={<AccessDeniedMessage />}
            >
              <GeneralSettings tenantId={currentTenant.id} />
            </PermissionGate>
          )}

          {activeTab === 'users' && (
            <PermissionGate
              resource={PermissionResource.USERS}
              action={PermissionAction.READ}
              fallback={<AccessDeniedMessage />}
            >
              <UsersSettings tenantId={currentTenant.id} />
            </PermissionGate>
          )}

          {activeTab === 'teams' && (
            <PermissionGate
              resource={PermissionResource.USERS}
              action={PermissionAction.READ}
              fallback={<AccessDeniedMessage />}
            >
              <TeamsSettings tenantId={currentTenant.id} />
            </PermissionGate>
          )}

          {activeTab === 'notifications' && (
            <PermissionGate
              resource={PermissionResource.SETTINGS}
              action={PermissionAction.READ}
              fallback={<AccessDeniedMessage />}
            >
              <NotificationSettings tenantId={currentTenant.id} />
            </PermissionGate>
          )}

          {activeTab === 'security' && (
            <PermissionGate
              resource={PermissionResource.SETTINGS}
              action={PermissionAction.READ}
              fallback={<AccessDeniedMessage />}
            >
              <SecuritySettings tenantId={currentTenant.id} />
            </PermissionGate>
          )}
        </div>
      </div>
    </PermissionProvider>
  );
}

function AccessDeniedMessage({ message }: { message?: string }) {
  return (
    <div className="text-center py-12">
      <ExclamationTriangleIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
      <h3 className="text-lg font-medium text-gray-900 mb-2">Access Denied</h3>
      <p className="text-gray-500 max-w-md mx-auto">
        {message || "You don't have permission to access this section."}
      </p>
    </div>
  );
}

// Placeholder components - these would be implemented separately
function GeneralSettings({ tenantId }: { tenantId: string }) {
  return (
    <div className="bg-white shadow rounded-lg p-6">
      <h3 className="text-lg font-medium text-gray-900 mb-4">General Settings</h3>
      <p className="text-gray-600">General tenant settings will be implemented here.</p>
    </div>
  );
}

function UsersSettings({ tenantId }: { tenantId: string }) {
  // Redirect to dedicated users page for better UX
  const router = useRouter();
  React.useEffect(() => {
    router.push('/settings/users');
  }, [router]);

  return (
    <div className="text-center py-8">
      <p className="text-gray-500">Redirecting to user management...</p>
    </div>
  );
}

function TeamsSettings({ tenantId }: { tenantId: string }) {
  // Redirect to dedicated team page for better UX
  const router = useRouter();
  React.useEffect(() => {
    router.push('/settings/team');
  }, [router]);

  return (
    <div className="text-center py-8">
      <p className="text-gray-500">Redirecting to team management...</p>
    </div>
  );
}

function NotificationSettings({ tenantId }: { tenantId: string }) {
  return (
    <div className="bg-white shadow rounded-lg p-6">
      <h3 className="text-lg font-medium text-gray-900 mb-4">Notification Settings</h3>
      <p className="text-gray-600">Notification preferences will be implemented here.</p>
    </div>
  );
}

function SecuritySettings({ tenantId }: { tenantId: string }) {
  return (
    <div className="bg-white shadow rounded-lg p-6">
      <h3 className="text-lg font-medium text-gray-900 mb-4">Security Settings</h3>
      <p className="text-gray-600">Security settings will be implemented here.</p>
    </div>
  );
}
