import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { opportunityManagementService } from '@/services/opportunity-management';
import { hasPermission } from '@/lib/permissions';
import { PermissionResource, PermissionAction } from '@/types';

/**
 * GET /api/opportunities/pipeline
 * Get pipeline data for visual pipeline interface
 */
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user's current tenant
    const currentTenant = session.user.tenants?.[0];
    if (!currentTenant) {
      return NextResponse.json({ error: 'No tenant access' }, { status: 403 });
    }

    // Check permission
    const hasAccess = await hasPermission(
      PermissionResource.OPPORTUNITIES,
      PermissionAction.READ,
      currentTenant.id
    );

    if (!hasAccess) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Parse query parameters for filtering
    const { searchParams } = new URL(request.url);
    
    const filters: any = {};
    
    if (searchParams.get('ownerId')) {
      filters.ownerId = searchParams.get('ownerId');
    }
    
    if (searchParams.get('priority')) {
      filters.priority = searchParams.get('priority');
    }
    
    if (searchParams.get('companyId')) {
      filters.companyId = searchParams.get('companyId');
    }
    
    if (searchParams.get('minValue')) {
      filters.value = { gte: Number(searchParams.get('minValue')) };
    }
    
    if (searchParams.get('maxValue')) {
      filters.value = { ...filters.value, lte: Number(searchParams.get('maxValue')) };
    }

    const pipelineData = await opportunityManagementService.getPipelineData(
      currentTenant.id,
      filters
    );

    return NextResponse.json(pipelineData);
  } catch (error) {
    console.error('Error fetching pipeline data:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
