-- CreateTable
CREATE TABLE "accounts" (
    "id" TEXT NOT NULL,
    "user_id" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "provider" TEXT NOT NULL,
    "provider_account_id" TEXT NOT NULL,
    "refresh_token" TEXT,
    "access_token" TEXT,
    "expires_at" INTEGER,
    "token_type" TEXT,
    "scope" TEXT,
    "id_token" TEXT,
    "session_state" TEXT,

    CONSTRAINT "accounts_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "sessions" (
    "id" TEXT NOT NULL,
    "session_token" TEXT NOT NULL,
    "user_id" TEXT NOT NULL,
    "expires" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "sessions_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "verificationtokens" (
    "identifier" TEXT NOT NULL,
    "token" TEXT NOT NULL,
    "expires" TIMESTAMP(3) NOT NULL
);

-- CreateTable
CREATE TABLE "tenants" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "slug" TEXT NOT NULL,
    "status" TEXT NOT NULL DEFAULT 'active',
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "settings" JSONB NOT NULL DEFAULT '{}',
    "branding" JSONB NOT NULL DEFAULT '{}',

    CONSTRAINT "tenants_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "users" (
    "id" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "password_hash" TEXT,
    "first_name" TEXT,
    "last_name" TEXT,
    "phone" TEXT,
    "avatar_url" TEXT,
    "locale" TEXT NOT NULL DEFAULT 'en',
    "timezone" TEXT NOT NULL DEFAULT 'UTC',
    "email_verified" BOOLEAN NOT NULL DEFAULT false,
    "is_platform_admin" BOOLEAN NOT NULL DEFAULT false,
    "status" TEXT NOT NULL DEFAULT 'active',
    "last_login" TIMESTAMP(3),
    "reset_token" TEXT,
    "reset_token_expiry" TIMESTAMP(3),
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "users_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "tenant_users" (
    "id" TEXT NOT NULL,
    "tenant_id" TEXT NOT NULL,
    "user_id" TEXT NOT NULL,
    "is_tenant_admin" BOOLEAN NOT NULL DEFAULT false,
    "role" TEXT NOT NULL DEFAULT 'user',
    "status" TEXT NOT NULL DEFAULT 'active',
    "joined_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "team_id" TEXT,
    "department" TEXT,
    "job_title" TEXT,
    "manager_id" TEXT,
    "last_active_at" TIMESTAMP(3),
    "invited_by" TEXT,
    "invited_at" TIMESTAMP(3),

    CONSTRAINT "tenant_users_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "teams" (
    "id" TEXT NOT NULL,
    "tenant_id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "color" TEXT DEFAULT '#6B7280',
    "manager_id" TEXT,
    "parent_team_id" TEXT,
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    "created_by" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "settings" JSONB NOT NULL DEFAULT '{}',
    "metadata" JSONB NOT NULL DEFAULT '{}',

    CONSTRAINT "teams_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "subscription_plans" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "price_monthly" DECIMAL(65,30) NOT NULL,
    "price_yearly" DECIMAL(65,30) NOT NULL,
    "features" JSONB NOT NULL DEFAULT '{}',
    "limits" JSONB NOT NULL DEFAULT '{}',
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "subscription_plans_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "subscriptions" (
    "id" TEXT NOT NULL,
    "tenant_id" TEXT NOT NULL,
    "plan_id" TEXT NOT NULL,
    "status" TEXT NOT NULL DEFAULT 'active',
    "current_period_start" TIMESTAMP(3) NOT NULL,
    "current_period_end" TIMESTAMP(3) NOT NULL,
    "stripe_subscription_id" TEXT,
    "stripe_customer_id" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "subscriptions_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "feature_flags" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "is_enabled" BOOLEAN NOT NULL DEFAULT true,
    "required_plan_level" INTEGER NOT NULL DEFAULT 1,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "feature_flags_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "usage_metrics" (
    "id" TEXT NOT NULL,
    "tenant_id" TEXT NOT NULL,
    "metric_name" TEXT NOT NULL,
    "metric_value" INTEGER NOT NULL,
    "period_start" TIMESTAMP(3) NOT NULL,
    "period_end" TIMESTAMP(3) NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "usage_metrics_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "billing_events" (
    "id" TEXT NOT NULL,
    "tenant_id" TEXT NOT NULL,
    "subscription_id" TEXT,
    "event_type" TEXT NOT NULL,
    "amount" DECIMAL(65,30),
    "currency" TEXT NOT NULL DEFAULT 'USD',
    "stripe_event_id" TEXT,
    "event_data" JSONB,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "billing_events_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "roles" (
    "id" TEXT NOT NULL,
    "tenant_id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "is_system_role" BOOLEAN NOT NULL DEFAULT false,
    "parent_role_id" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "created_by" TEXT,

    CONSTRAINT "roles_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "permissions" (
    "id" TEXT NOT NULL,
    "tenant_id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "resource" TEXT NOT NULL,
    "action" TEXT NOT NULL,
    "description" TEXT,
    "category" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "permissions_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "user_roles" (
    "id" TEXT NOT NULL,
    "tenant_id" TEXT NOT NULL,
    "user_id" TEXT NOT NULL,
    "role_id" TEXT NOT NULL,
    "assigned_by" TEXT,
    "assigned_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "expires_at" TIMESTAMP(3),

    CONSTRAINT "user_roles_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "role_permissions" (
    "id" TEXT NOT NULL,
    "tenant_id" TEXT NOT NULL,
    "role_id" TEXT NOT NULL,
    "permission_id" TEXT NOT NULL,
    "granted_by" TEXT,
    "granted_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "role_permissions_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "companies" (
    "id" TEXT NOT NULL,
    "tenant_id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "website" TEXT,
    "industry" TEXT,
    "size" TEXT,
    "owner_id" TEXT,
    "created_by" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "companies_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "contacts" (
    "id" TEXT NOT NULL,
    "tenant_id" TEXT NOT NULL,
    "first_name" TEXT NOT NULL,
    "last_name" TEXT NOT NULL,
    "email" TEXT,
    "phone" TEXT,
    "job_title" TEXT,
    "website" TEXT,
    "address" TEXT,
    "company_id" TEXT,
    "owner_id" TEXT,
    "created_by" TEXT,
    "last_contact_date" TIMESTAMP(3),
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "contacts_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "leads" (
    "id" TEXT NOT NULL,
    "tenant_id" TEXT NOT NULL,
    "contact_id" TEXT,
    "company_id" TEXT,
    "title" TEXT NOT NULL,
    "description" TEXT,
    "status" TEXT NOT NULL DEFAULT 'new',
    "lead_source_id" TEXT,
    "score" INTEGER NOT NULL DEFAULT 0,
    "value" DECIMAL(65,30),
    "expected_close_date" TIMESTAMP(3),
    "priority" TEXT NOT NULL DEFAULT 'medium',
    "tags" JSONB NOT NULL DEFAULT '[]',
    "custom_fields" JSONB NOT NULL DEFAULT '{}',
    "owner_id" TEXT,
    "created_by" TEXT,
    "last_contact_date" TIMESTAMP(3),
    "deleted_at" TIMESTAMP(3),
    "is_converted" BOOLEAN NOT NULL DEFAULT false,
    "converted_at" TIMESTAMP(3),
    "converted_by" TEXT,
    "conversion_notes" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "leads_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "lead_conversion_history" (
    "id" TEXT NOT NULL,
    "tenant_id" TEXT NOT NULL,
    "lead_id" TEXT NOT NULL,
    "opportunity_id" TEXT NOT NULL,
    "converted_by" TEXT NOT NULL,
    "conversion_type" TEXT NOT NULL DEFAULT 'manual',
    "original_data" JSONB NOT NULL,
    "conversion_data" JSONB NOT NULL,
    "notes" TEXT,
    "converted_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "lead_conversion_history_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "lead_sources" (
    "id" TEXT NOT NULL,
    "tenant_id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "category" TEXT NOT NULL DEFAULT 'custom',
    "icon" TEXT,
    "color" TEXT,
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    "sort_order" INTEGER NOT NULL DEFAULT 0,
    "created_by" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "lead_sources_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "nurturing_campaigns" (
    "id" TEXT NOT NULL,
    "tenant_id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "target_status" JSONB NOT NULL DEFAULT '[]',
    "target_priority" JSONB NOT NULL DEFAULT '[]',
    "email_sequence" JSONB NOT NULL DEFAULT '[]',
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    "max_leads_per_day" INTEGER NOT NULL DEFAULT 10,
    "created_by" TEXT,
    "deleted_at" TIMESTAMP(3),
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "nurturing_campaigns_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "nurturing_executions" (
    "id" TEXT NOT NULL,
    "campaign_id" TEXT NOT NULL,
    "lead_id" TEXT NOT NULL,
    "current_step" INTEGER NOT NULL DEFAULT 0,
    "next_execution_date" TIMESTAMP(3) NOT NULL,
    "status" TEXT NOT NULL DEFAULT 'active',
    "emails_sent" INTEGER NOT NULL DEFAULT 0,
    "last_email_sent_at" TIMESTAMP(3),
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "nurturing_executions_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "nurturing_email_logs" (
    "id" TEXT NOT NULL,
    "execution_id" TEXT NOT NULL,
    "lead_id" TEXT NOT NULL,
    "step_number" INTEGER NOT NULL,
    "email_type" TEXT NOT NULL,
    "subject" TEXT NOT NULL,
    "content" TEXT NOT NULL,
    "sent_at" TIMESTAMP(3) NOT NULL,
    "status" TEXT NOT NULL DEFAULT 'sent',
    "error_message" TEXT,

    CONSTRAINT "nurturing_email_logs_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "lead_notifications" (
    "id" TEXT NOT NULL,
    "tenant_id" TEXT NOT NULL,
    "user_id" TEXT NOT NULL,
    "lead_id" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "message" TEXT NOT NULL,
    "data" JSONB DEFAULT '{}',
    "is_read" BOOLEAN NOT NULL DEFAULT false,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "lead_notifications_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "notifications" (
    "id" TEXT NOT NULL,
    "tenant_id" TEXT NOT NULL,
    "user_id" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "message" TEXT NOT NULL,
    "data" JSONB DEFAULT '{}',
    "is_read" BOOLEAN NOT NULL DEFAULT false,
    "read_at" TIMESTAMP(3),
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "notifications_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "user_notification_preferences" (
    "id" TEXT NOT NULL,
    "user_id" TEXT NOT NULL,
    "preferences" JSONB NOT NULL DEFAULT '{}',
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "user_notification_preferences_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "email_lead_captures" (
    "id" TEXT NOT NULL,
    "tenant_id" TEXT NOT NULL,
    "from_email" TEXT NOT NULL,
    "from_name" TEXT,
    "subject" TEXT NOT NULL,
    "content" TEXT NOT NULL,
    "received_at" TIMESTAMP(3) NOT NULL,
    "processed" BOOLEAN NOT NULL DEFAULT false,
    "lead_id" TEXT,
    "contact_id" TEXT,
    "sentiment_analysis" JSONB,
    "extracted_data" JSONB,
    "processing_error" TEXT,
    "email_provider" TEXT,
    "message_id" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "email_lead_captures_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "social_media_captures" (
    "id" TEXT NOT NULL,
    "tenant_id" TEXT NOT NULL,
    "platform" TEXT NOT NULL,
    "post_id" TEXT NOT NULL,
    "author_id" TEXT NOT NULL,
    "author_name" TEXT NOT NULL,
    "author_handle" TEXT,
    "content" TEXT NOT NULL,
    "url" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL,
    "processed" BOOLEAN NOT NULL DEFAULT false,
    "lead_id" TEXT,
    "contact_id" TEXT,
    "analysis" JSONB,
    "engagement" JSONB,
    "metadata" JSONB,
    "processing_error" TEXT,
    "captured_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "social_media_captures_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "crm_integrations" (
    "id" TEXT NOT NULL,
    "tenant_id" TEXT NOT NULL,
    "provider" TEXT NOT NULL,
    "config" JSONB NOT NULL,
    "last_sync_at" TIMESTAMP(3),
    "syncStatus" TEXT NOT NULL DEFAULT 'active',
    "error_message" TEXT,
    "created_by" TEXT,
    "updated_by" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "crm_integrations_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "crm_mappings" (
    "id" TEXT NOT NULL,
    "tenant_id" TEXT NOT NULL,
    "entity_type" TEXT NOT NULL,
    "entity_id" TEXT NOT NULL,
    "provider" TEXT NOT NULL,
    "external_id" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "crm_mappings_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "crm_sync_logs" (
    "id" TEXT NOT NULL,
    "tenant_id" TEXT NOT NULL,
    "operation" TEXT NOT NULL,
    "entity_type" TEXT NOT NULL,
    "entity_id" TEXT NOT NULL,
    "external_id" TEXT,
    "status" TEXT NOT NULL,
    "error_message" TEXT,
    "data" JSONB,
    "synced_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "crm_sync_logs_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "sales_stages" (
    "id" TEXT NOT NULL,
    "tenant_id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "order_index" INTEGER NOT NULL,
    "color" TEXT NOT NULL DEFAULT '#6B7280',
    "probability_min" INTEGER NOT NULL DEFAULT 0,
    "probability_max" INTEGER NOT NULL DEFAULT 100,
    "is_closed" BOOLEAN NOT NULL DEFAULT false,
    "is_won" BOOLEAN NOT NULL DEFAULT false,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "sales_stages_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "opportunities" (
    "id" TEXT NOT NULL,
    "tenant_id" TEXT NOT NULL,
    "lead_id" TEXT,
    "contact_id" TEXT,
    "company_id" TEXT,
    "title" TEXT NOT NULL,
    "description" TEXT,
    "value" DECIMAL(65,30),
    "stage" TEXT NOT NULL DEFAULT 'qualification',
    "probability" INTEGER NOT NULL DEFAULT 0,
    "expected_close_date" TIMESTAMP(3),
    "tags" JSONB NOT NULL DEFAULT '[]',
    "source" TEXT,
    "priority" TEXT NOT NULL DEFAULT 'medium',
    "close_reason" TEXT,
    "competitor_info" JSONB NOT NULL DEFAULT '{}',
    "last_activity_date" TIMESTAMP(3),
    "next_followup_date" TIMESTAMP(3),
    "custom_fields" JSONB NOT NULL DEFAULT '{}',
    "owner_id" TEXT,
    "created_by" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "opportunities_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "opportunity_stage_history" (
    "id" TEXT NOT NULL,
    "tenant_id" TEXT NOT NULL,
    "opportunity_id" TEXT NOT NULL,
    "from_stage" TEXT,
    "to_stage" TEXT NOT NULL,
    "changed_by" TEXT,
    "changed_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "notes" TEXT,

    CONSTRAINT "opportunity_stage_history_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "proposals" (
    "id" TEXT NOT NULL,
    "tenant_id" TEXT NOT NULL,
    "opportunity_id" TEXT,
    "title" TEXT NOT NULL,
    "content" TEXT,
    "status" TEXT NOT NULL DEFAULT 'draft',
    "version" INTEGER NOT NULL DEFAULT 1,
    "total_amount" DECIMAL(65,30),
    "is_ai_generated" BOOLEAN NOT NULL DEFAULT false,
    "ai_provider" TEXT,
    "ai_model" TEXT,
    "generation_status" TEXT NOT NULL DEFAULT 'pending',
    "generation_error" TEXT,
    "selected_file_ids" JSONB NOT NULL DEFAULT '[]',
    "gemini_file_uris" JSONB NOT NULL DEFAULT '{}',
    "document_path" TEXT,
    "document_generated" BOOLEAN NOT NULL DEFAULT false,
    "parent_proposal_id" TEXT,
    "created_by" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "proposals_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "proposal_sections" (
    "id" TEXT NOT NULL,
    "tenant_id" TEXT NOT NULL,
    "proposal_id" TEXT NOT NULL,
    "section_type" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "content" TEXT,
    "order_index" INTEGER NOT NULL,
    "is_ai_generated" BOOLEAN NOT NULL DEFAULT false,
    "ai_prompt" TEXT,
    "ai_provider" TEXT,
    "ai_model" TEXT,
    "tokens_used" INTEGER,
    "generated_at" TIMESTAMP(3),
    "status" TEXT NOT NULL DEFAULT 'draft',
    "word_count" INTEGER,
    "confidence" DECIMAL(65,30),
    "created_by" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "proposal_sections_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "proposal_charts" (
    "id" TEXT NOT NULL,
    "tenant_id" TEXT NOT NULL,
    "proposal_id" TEXT NOT NULL,
    "chart_type" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "description" TEXT,
    "mermaid_code" TEXT NOT NULL,
    "rendered_image_path" TEXT,
    "is_ai_generated" BOOLEAN NOT NULL DEFAULT false,
    "ai_prompt" TEXT,
    "ai_provider" TEXT,
    "ai_model" TEXT,
    "tokens_used" INTEGER,
    "generated_at" TIMESTAMP(3),
    "status" TEXT NOT NULL DEFAULT 'draft',
    "order_index" INTEGER NOT NULL,
    "confidence" DECIMAL(65,30),
    "error_context" TEXT,
    "created_by" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "proposal_charts_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "projects" (
    "id" TEXT NOT NULL,
    "tenant_id" TEXT NOT NULL,
    "opportunity_id" TEXT,
    "handover_id" TEXT,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "status" TEXT NOT NULL DEFAULT 'planning',
    "start_date" TIMESTAMP(3),
    "end_date" TIMESTAMP(3),
    "budget" DECIMAL(65,30),
    "manager_id" TEXT,
    "is_from_handover" BOOLEAN NOT NULL DEFAULT false,
    "handover_completed_at" TIMESTAMP(3),
    "client_portal_enabled" BOOLEAN NOT NULL DEFAULT false,
    "priority" TEXT NOT NULL DEFAULT 'medium',
    "progress" DECIMAL(65,30) NOT NULL DEFAULT 0,
    "estimated_hours" INTEGER,
    "actual_hours" INTEGER,
    "client_portal_url" TEXT,
    "client_access_token" TEXT,
    "external_project_id" TEXT,
    "integration_provider" TEXT,
    "integration_config" JSONB,
    "last_sync_at" TIMESTAMP(3),
    "created_by" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "projects_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "project_tasks" (
    "id" TEXT NOT NULL,
    "tenant_id" TEXT NOT NULL,
    "project_id" TEXT NOT NULL,
    "milestone_id" TEXT,
    "title" TEXT NOT NULL,
    "description" TEXT,
    "status" TEXT NOT NULL DEFAULT 'todo',
    "priority" TEXT NOT NULL DEFAULT 'medium',
    "assignee_id" TEXT,
    "start_date" TIMESTAMP(3),
    "due_date" TIMESTAMP(3),
    "completed_at" TIMESTAMP(3),
    "estimated_hours" DECIMAL(65,30),
    "actual_hours" DECIMAL(65,30),
    "parent_task_id" TEXT,
    "depends_on_tasks" JSONB NOT NULL DEFAULT '[]',
    "external_task_id" TEXT,
    "created_by" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "project_tasks_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "project_milestones" (
    "id" TEXT NOT NULL,
    "tenant_id" TEXT NOT NULL,
    "project_id" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "description" TEXT,
    "status" TEXT NOT NULL DEFAULT 'pending',
    "due_date" TIMESTAMP(3) NOT NULL,
    "completed_at" TIMESTAMP(3),
    "progress" DECIMAL(65,30) NOT NULL DEFAULT 0,
    "depends_on_tasks" JSONB NOT NULL DEFAULT '[]',
    "external_milestone_id" TEXT,
    "created_by" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "project_milestones_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "project_resources" (
    "id" TEXT NOT NULL,
    "tenant_id" TEXT NOT NULL,
    "project_id" TEXT NOT NULL,
    "user_id" TEXT NOT NULL,
    "role" TEXT NOT NULL,
    "allocation" DECIMAL(65,30) NOT NULL DEFAULT 100,
    "hourly_rate" DECIMAL(65,30),
    "start_date" TIMESTAMP(3) NOT NULL,
    "end_date" TIMESTAMP(3),
    "hours_allocated" DECIMAL(65,30),
    "hours_logged" DECIMAL(65,30) NOT NULL DEFAULT 0,
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    "created_by" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "project_resources_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "project_client_access" (
    "id" TEXT NOT NULL,
    "tenant_id" TEXT NOT NULL,
    "project_id" TEXT NOT NULL,
    "contact_id" TEXT NOT NULL,
    "accessLevel" TEXT NOT NULL DEFAULT 'view',
    "can_view_tasks" BOOLEAN NOT NULL DEFAULT true,
    "can_view_milestones" BOOLEAN NOT NULL DEFAULT true,
    "can_view_documents" BOOLEAN NOT NULL DEFAULT true,
    "can_comment" BOOLEAN NOT NULL DEFAULT false,
    "access_token" TEXT NOT NULL,
    "token_expires_at" TIMESTAMP(3),
    "last_access_at" TIMESTAMP(3),
    "access_count" INTEGER NOT NULL DEFAULT 0,
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    "created_by" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "project_client_access_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "handover_checklist_templates" (
    "id" TEXT NOT NULL,
    "tenant_id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "is_default" BOOLEAN NOT NULL DEFAULT false,
    "category" TEXT NOT NULL DEFAULT 'general',
    "items" JSONB NOT NULL DEFAULT '[]',
    "estimated_duration" INTEGER,
    "created_by" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "handover_checklist_templates_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "project_handovers" (
    "id" TEXT NOT NULL,
    "tenant_id" TEXT NOT NULL,
    "opportunity_id" TEXT NOT NULL,
    "template_id" TEXT,
    "title" TEXT NOT NULL,
    "description" TEXT,
    "status" TEXT NOT NULL DEFAULT 'pending',
    "priority" TEXT NOT NULL DEFAULT 'medium',
    "sales_rep_id" TEXT,
    "delivery_manager_id" TEXT,
    "assigned_team_ids" JSONB NOT NULL DEFAULT '[]',
    "checklist_progress" DECIMAL(65,30) NOT NULL DEFAULT 0,
    "documents_count" INTEGER NOT NULL DEFAULT 0,
    "qa_threads_count" INTEGER NOT NULL DEFAULT 0,
    "scheduled_date" TIMESTAMP(3),
    "started_at" TIMESTAMP(3),
    "completed_at" TIMESTAMP(3),
    "estimated_duration" INTEGER,
    "actual_duration" INTEGER,
    "quality_score" DECIMAL(65,30),
    "client_satisfaction" DECIMAL(65,30),
    "delivery_feedback" TEXT,
    "created_by" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "project_handovers_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "handover_checklist_items" (
    "id" TEXT NOT NULL,
    "tenant_id" TEXT NOT NULL,
    "handover_id" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "description" TEXT,
    "category" TEXT NOT NULL DEFAULT 'general',
    "order_index" INTEGER NOT NULL,
    "is_required" BOOLEAN NOT NULL DEFAULT true,
    "is_completed" BOOLEAN NOT NULL DEFAULT false,
    "completed_at" TIMESTAMP(3),
    "completed_by" TEXT,
    "notes" TEXT,
    "depends_on_ids" JSONB NOT NULL DEFAULT '[]',
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "handover_checklist_items_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "handover_documents" (
    "id" TEXT NOT NULL,
    "tenant_id" TEXT NOT NULL,
    "handover_id" TEXT NOT NULL,
    "document_id" TEXT NOT NULL,
    "category" TEXT NOT NULL DEFAULT 'general',
    "is_required" BOOLEAN NOT NULL DEFAULT false,
    "is_approved" BOOLEAN NOT NULL DEFAULT false,
    "approved_at" TIMESTAMP(3),
    "approved_by" TEXT,
    "is_client_visible" BOOLEAN NOT NULL DEFAULT false,
    "accessLevel" TEXT NOT NULL DEFAULT 'internal',
    "order_index" INTEGER NOT NULL,
    "notes" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "handover_documents_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "handover_qa_threads" (
    "id" TEXT NOT NULL,
    "tenant_id" TEXT NOT NULL,
    "handover_id" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "category" TEXT NOT NULL DEFAULT 'general',
    "priority" TEXT NOT NULL DEFAULT 'medium',
    "status" TEXT NOT NULL DEFAULT 'open',
    "created_by" TEXT NOT NULL,
    "assigned_to" TEXT,
    "response_time" INTEGER,
    "is_escalated" BOOLEAN NOT NULL DEFAULT false,
    "escalated_at" TIMESTAMP(3),
    "resolved_at" TIMESTAMP(3),
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "handover_qa_threads_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "handover_qa_messages" (
    "id" TEXT NOT NULL,
    "tenant_id" TEXT NOT NULL,
    "thread_id" TEXT NOT NULL,
    "content" TEXT NOT NULL,
    "message_type" TEXT NOT NULL DEFAULT 'message',
    "attachments" JSONB NOT NULL DEFAULT '[]',
    "is_read" BOOLEAN NOT NULL DEFAULT false,
    "read_at" TIMESTAMP(3),
    "author_id" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "handover_qa_messages_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "handover_notifications" (
    "id" TEXT NOT NULL,
    "tenant_id" TEXT NOT NULL,
    "handover_id" TEXT NOT NULL,
    "user_id" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "message" TEXT NOT NULL,
    "data" JSONB NOT NULL DEFAULT '{}',
    "is_read" BOOLEAN NOT NULL DEFAULT false,
    "read_at" TIMESTAMP(3),
    "action_url" TEXT,
    "action_label" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "handover_notifications_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "activities" (
    "id" TEXT NOT NULL,
    "tenant_id" TEXT NOT NULL,
    "related_to_type" TEXT,
    "related_to_id" TEXT,
    "type" TEXT NOT NULL,
    "subject" TEXT,
    "description" TEXT,
    "scheduled_at" TIMESTAMP(3),
    "completed_at" TIMESTAMP(3),
    "owner_id" TEXT,
    "created_by" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "activities_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "scheduled_meetings" (
    "id" TEXT NOT NULL,
    "tenant_id" TEXT NOT NULL,
    "lead_id" TEXT,
    "contact_id" TEXT,
    "scheduled_by" TEXT NOT NULL,
    "assigned_to" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "description" TEXT,
    "meeting_type" TEXT NOT NULL DEFAULT 'phone',
    "meeting_link" TEXT,
    "location" TEXT,
    "scheduled_at" TIMESTAMP(3) NOT NULL,
    "duration" INTEGER NOT NULL DEFAULT 30,
    "timezone" TEXT NOT NULL DEFAULT 'UTC',
    "status" TEXT NOT NULL DEFAULT 'scheduled',
    "completed_at" TIMESTAMP(3),
    "meeting_notes" TEXT,
    "follow_up_required" BOOLEAN NOT NULL DEFAULT false,
    "next_meeting_date" TIMESTAMP(3),
    "reminder_sent" BOOLEAN NOT NULL DEFAULT false,
    "invitation_sent" BOOLEAN NOT NULL DEFAULT false,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "scheduled_meetings_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "documents" (
    "id" TEXT NOT NULL,
    "tenant_id" TEXT NOT NULL,
    "related_to_type" TEXT,
    "related_to_id" TEXT,
    "name" TEXT NOT NULL,
    "file_path" TEXT NOT NULL,
    "file_size" INTEGER,
    "mime_type" TEXT,
    "description" TEXT,
    "tags" JSONB NOT NULL DEFAULT '[]',
    "is_public" BOOLEAN NOT NULL DEFAULT false,
    "uploaded_by" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "documents_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ai_insights" (
    "id" TEXT NOT NULL,
    "tenant_id" TEXT NOT NULL,
    "related_to_type" TEXT,
    "related_to_id" TEXT,
    "insight_type" TEXT NOT NULL,
    "data" JSONB NOT NULL,
    "confidence_score" DECIMAL(65,30),
    "generated_by" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "ai_insights_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "tenant_settings" (
    "id" TEXT NOT NULL,
    "tenant_id" TEXT NOT NULL,
    "setting_key" TEXT NOT NULL,
    "setting_value" JSONB,
    "description" TEXT,
    "updated_by" TEXT,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "tenant_settings_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "audit_logs" (
    "id" TEXT NOT NULL,
    "tenant_id" TEXT,
    "user_id" TEXT,
    "action" TEXT NOT NULL,
    "resource_type" TEXT,
    "resource_id" TEXT,
    "old_values" JSONB,
    "new_values" JSONB,
    "ip_address" TEXT,
    "user_agent" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "audit_logs_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "email_logs" (
    "id" TEXT NOT NULL,
    "tenant_id" TEXT NOT NULL,
    "sent_by" TEXT NOT NULL,
    "lead_id" TEXT,
    "contact_id" TEXT,
    "campaign_id" TEXT,
    "template_id" TEXT,
    "recipients" JSONB NOT NULL DEFAULT '[]',
    "subject" TEXT NOT NULL,
    "content" TEXT NOT NULL,
    "message_id" TEXT,
    "provider" TEXT NOT NULL DEFAULT 'smtp',
    "status" TEXT NOT NULL DEFAULT 'sent',
    "sent_at" TIMESTAMP(3) NOT NULL,
    "delivered_at" TIMESTAMP(3),
    "opened_at" TIMESTAMP(3),
    "clicked_at" TIMESTAMP(3),
    "bounced_at" TIMESTAMP(3),
    "track_opens" BOOLEAN NOT NULL DEFAULT true,
    "track_clicks" BOOLEAN NOT NULL DEFAULT true,
    "tags" JSONB NOT NULL DEFAULT '[]',
    "metadata" JSONB NOT NULL DEFAULT '{}',
    "error_message" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "email_logs_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "impersonation_sessions" (
    "id" TEXT NOT NULL,
    "super_admin_id" TEXT NOT NULL,
    "tenant_id" TEXT NOT NULL,
    "user_id" TEXT NOT NULL,
    "started_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "ended_at" TIMESTAMP(3),
    "reason" TEXT,
    "ip_address" TEXT,
    "user_agent" TEXT,

    CONSTRAINT "impersonation_sessions_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "chat_channels" (
    "id" TEXT NOT NULL,
    "tenant_id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "type" TEXT NOT NULL DEFAULT 'general',
    "is_private" BOOLEAN NOT NULL DEFAULT false,
    "related_to_type" TEXT,
    "related_to_id" TEXT,
    "allow_file_sharing" BOOLEAN NOT NULL DEFAULT true,
    "allow_mentions" BOOLEAN NOT NULL DEFAULT true,
    "created_by" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "chat_channels_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "chat_channel_members" (
    "id" TEXT NOT NULL,
    "tenant_id" TEXT NOT NULL,
    "channel_id" TEXT NOT NULL,
    "user_id" TEXT NOT NULL,
    "role" TEXT NOT NULL DEFAULT 'member',
    "can_post" BOOLEAN NOT NULL DEFAULT true,
    "can_invite" BOOLEAN NOT NULL DEFAULT false,
    "can_moderate" BOOLEAN NOT NULL DEFAULT false,
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    "last_read_at" TIMESTAMP(3),
    "joined_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "left_at" TIMESTAMP(3),

    CONSTRAINT "chat_channel_members_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "chat_messages" (
    "id" TEXT NOT NULL,
    "tenant_id" TEXT NOT NULL,
    "channel_id" TEXT NOT NULL,
    "user_id" TEXT NOT NULL,
    "content" TEXT NOT NULL,
    "message_type" TEXT NOT NULL DEFAULT 'text',
    "attachments" JSONB DEFAULT '[]',
    "parent_message_id" TEXT,
    "thread_count" INTEGER NOT NULL DEFAULT 0,
    "is_edited" BOOLEAN NOT NULL DEFAULT false,
    "edited_at" TIMESTAMP(3),
    "is_deleted" BOOLEAN NOT NULL DEFAULT false,
    "deleted_at" TIMESTAMP(3),
    "mentions" JSONB DEFAULT '[]',
    "reactions" JSONB DEFAULT '{}',
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "chat_messages_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "calendar_events" (
    "id" TEXT NOT NULL,
    "tenant_id" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "description" TEXT,
    "start_time" TIMESTAMP(3) NOT NULL,
    "end_time" TIMESTAMP(3) NOT NULL,
    "timezone" TEXT NOT NULL DEFAULT 'UTC',
    "is_all_day" BOOLEAN NOT NULL DEFAULT false,
    "location" TEXT,
    "meeting_link" TEXT,
    "eventType" TEXT NOT NULL DEFAULT 'meeting',
    "status" TEXT NOT NULL DEFAULT 'confirmed',
    "visibility" TEXT NOT NULL DEFAULT 'public',
    "related_to_type" TEXT,
    "related_to_id" TEXT,
    "is_recurring" BOOLEAN NOT NULL DEFAULT false,
    "recurrence_rule" TEXT,
    "reminder_minutes" INTEGER,
    "created_by" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "calendar_events_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "calendar_event_attendees" (
    "id" TEXT NOT NULL,
    "tenant_id" TEXT NOT NULL,
    "event_id" TEXT NOT NULL,
    "user_id" TEXT NOT NULL,
    "status" TEXT NOT NULL DEFAULT 'pending',
    "response" TEXT,
    "can_edit" BOOLEAN NOT NULL DEFAULT false,
    "responded_at" TIMESTAMP(3),
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "calendar_event_attendees_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "email_templates" (
    "id" TEXT NOT NULL,
    "tenant_id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "subject" TEXT NOT NULL,
    "content" TEXT NOT NULL,
    "variables" JSONB NOT NULL DEFAULT '[]',
    "category" TEXT NOT NULL DEFAULT 'general',
    "type" TEXT NOT NULL,
    "locale" TEXT NOT NULL DEFAULT 'en',
    "use_custom_branding" BOOLEAN NOT NULL DEFAULT false,
    "branding_config" JSONB,
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    "is_default" BOOLEAN NOT NULL DEFAULT false,
    "usage_count" INTEGER NOT NULL DEFAULT 0,
    "last_used_at" TIMESTAMP(3),
    "created_by" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "email_templates_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "automation_triggers" (
    "id" TEXT NOT NULL,
    "tenant_id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "trigger_type" TEXT NOT NULL,
    "trigger_conditions" JSONB NOT NULL,
    "actions" JSONB NOT NULL,
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    "priority" INTEGER NOT NULL DEFAULT 5,
    "created_by" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "automation_triggers_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "automation_executions" (
    "id" TEXT NOT NULL,
    "tenant_id" TEXT NOT NULL,
    "trigger_id" TEXT NOT NULL,
    "entity_type" TEXT NOT NULL,
    "entity_id" TEXT NOT NULL,
    "trigger_data" JSONB NOT NULL,
    "executed_actions" JSONB NOT NULL DEFAULT '[]',
    "status" TEXT NOT NULL DEFAULT 'pending',
    "error" TEXT,
    "started_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "completed_at" TIMESTAMP(3),

    CONSTRAINT "automation_executions_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "accounts_provider_provider_account_id_key" ON "accounts"("provider", "provider_account_id");

-- CreateIndex
CREATE UNIQUE INDEX "sessions_session_token_key" ON "sessions"("session_token");

-- CreateIndex
CREATE UNIQUE INDEX "verificationtokens_token_key" ON "verificationtokens"("token");

-- CreateIndex
CREATE UNIQUE INDEX "verificationtokens_identifier_token_key" ON "verificationtokens"("identifier", "token");

-- CreateIndex
CREATE UNIQUE INDEX "tenants_slug_key" ON "tenants"("slug");

-- CreateIndex
CREATE UNIQUE INDEX "users_email_key" ON "users"("email");

-- CreateIndex
CREATE UNIQUE INDEX "tenant_users_tenant_id_user_id_key" ON "tenant_users"("tenant_id", "user_id");

-- CreateIndex
CREATE UNIQUE INDEX "teams_tenant_id_name_key" ON "teams"("tenant_id", "name");

-- CreateIndex
CREATE UNIQUE INDEX "feature_flags_name_key" ON "feature_flags"("name");

-- CreateIndex
CREATE UNIQUE INDEX "usage_metrics_tenant_id_metric_name_period_start_key" ON "usage_metrics"("tenant_id", "metric_name", "period_start");

-- CreateIndex
CREATE UNIQUE INDEX "roles_tenant_id_name_key" ON "roles"("tenant_id", "name");

-- CreateIndex
CREATE UNIQUE INDEX "permissions_tenant_id_name_key" ON "permissions"("tenant_id", "name");

-- CreateIndex
CREATE UNIQUE INDEX "user_roles_tenant_id_user_id_role_id_key" ON "user_roles"("tenant_id", "user_id", "role_id");

-- CreateIndex
CREATE UNIQUE INDEX "role_permissions_tenant_id_role_id_permission_id_key" ON "role_permissions"("tenant_id", "role_id", "permission_id");

-- CreateIndex
CREATE UNIQUE INDEX "lead_sources_tenant_id_name_key" ON "lead_sources"("tenant_id", "name");

-- CreateIndex
CREATE UNIQUE INDEX "nurturing_executions_campaign_id_lead_id_key" ON "nurturing_executions"("campaign_id", "lead_id");

-- CreateIndex
CREATE INDEX "notifications_tenant_id_user_id_idx" ON "notifications"("tenant_id", "user_id");

-- CreateIndex
CREATE INDEX "notifications_tenant_id_user_id_is_read_idx" ON "notifications"("tenant_id", "user_id", "is_read");

-- CreateIndex
CREATE UNIQUE INDEX "user_notification_preferences_user_id_key" ON "user_notification_preferences"("user_id");

-- CreateIndex
CREATE UNIQUE INDEX "social_media_captures_platform_post_id_key" ON "social_media_captures"("platform", "post_id");

-- CreateIndex
CREATE UNIQUE INDEX "crm_integrations_tenant_id_provider_key" ON "crm_integrations"("tenant_id", "provider");

-- CreateIndex
CREATE UNIQUE INDEX "crm_mappings_tenant_id_provider_external_id_key" ON "crm_mappings"("tenant_id", "provider", "external_id");

-- CreateIndex
CREATE UNIQUE INDEX "sales_stages_tenant_id_name_key" ON "sales_stages"("tenant_id", "name");

-- CreateIndex
CREATE UNIQUE INDEX "sales_stages_tenant_id_order_index_key" ON "sales_stages"("tenant_id", "order_index");

-- CreateIndex
CREATE UNIQUE INDEX "proposal_sections_proposal_id_section_type_key" ON "proposal_sections"("proposal_id", "section_type");

-- CreateIndex
CREATE UNIQUE INDEX "projects_tenant_id_opportunity_id_key" ON "projects"("tenant_id", "opportunity_id");

-- CreateIndex
CREATE UNIQUE INDEX "project_resources_tenant_id_project_id_user_id_role_key" ON "project_resources"("tenant_id", "project_id", "user_id", "role");

-- CreateIndex
CREATE UNIQUE INDEX "project_client_access_access_token_key" ON "project_client_access"("access_token");

-- CreateIndex
CREATE UNIQUE INDEX "project_client_access_tenant_id_project_id_contact_id_key" ON "project_client_access"("tenant_id", "project_id", "contact_id");

-- CreateIndex
CREATE UNIQUE INDEX "project_handovers_tenant_id_opportunity_id_key" ON "project_handovers"("tenant_id", "opportunity_id");

-- CreateIndex
CREATE UNIQUE INDEX "tenant_settings_tenant_id_setting_key_key" ON "tenant_settings"("tenant_id", "setting_key");

-- CreateIndex
CREATE INDEX "chat_channels_tenant_id_type_idx" ON "chat_channels"("tenant_id", "type");

-- CreateIndex
CREATE INDEX "chat_channels_tenant_id_related_to_type_related_to_id_idx" ON "chat_channels"("tenant_id", "related_to_type", "related_to_id");

-- CreateIndex
CREATE INDEX "chat_channel_members_tenant_id_user_id_idx" ON "chat_channel_members"("tenant_id", "user_id");

-- CreateIndex
CREATE UNIQUE INDEX "chat_channel_members_channel_id_user_id_key" ON "chat_channel_members"("channel_id", "user_id");

-- CreateIndex
CREATE INDEX "chat_messages_tenant_id_channel_id_created_at_idx" ON "chat_messages"("tenant_id", "channel_id", "created_at");

-- CreateIndex
CREATE INDEX "chat_messages_tenant_id_user_id_idx" ON "chat_messages"("tenant_id", "user_id");

-- CreateIndex
CREATE INDEX "chat_messages_parent_message_id_idx" ON "chat_messages"("parent_message_id");

-- CreateIndex
CREATE INDEX "calendar_events_tenant_id_start_time_idx" ON "calendar_events"("tenant_id", "start_time");

-- CreateIndex
CREATE INDEX "calendar_events_tenant_id_eventType_idx" ON "calendar_events"("tenant_id", "eventType");

-- CreateIndex
CREATE INDEX "calendar_events_tenant_id_related_to_type_related_to_id_idx" ON "calendar_events"("tenant_id", "related_to_type", "related_to_id");

-- CreateIndex
CREATE INDEX "calendar_event_attendees_tenant_id_user_id_idx" ON "calendar_event_attendees"("tenant_id", "user_id");

-- CreateIndex
CREATE UNIQUE INDEX "calendar_event_attendees_event_id_user_id_key" ON "calendar_event_attendees"("event_id", "user_id");

-- CreateIndex
CREATE INDEX "email_templates_tenant_id_category_idx" ON "email_templates"("tenant_id", "category");

-- CreateIndex
CREATE INDEX "email_templates_tenant_id_type_idx" ON "email_templates"("tenant_id", "type");

-- CreateIndex
CREATE INDEX "email_templates_tenant_id_locale_idx" ON "email_templates"("tenant_id", "locale");

-- AddForeignKey
ALTER TABLE "accounts" ADD CONSTRAINT "accounts_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "sessions" ADD CONSTRAINT "sessions_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "tenant_users" ADD CONSTRAINT "tenant_users_tenant_id_fkey" FOREIGN KEY ("tenant_id") REFERENCES "tenants"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "tenant_users" ADD CONSTRAINT "tenant_users_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "tenant_users" ADD CONSTRAINT "tenant_users_team_id_fkey" FOREIGN KEY ("team_id") REFERENCES "teams"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "teams" ADD CONSTRAINT "teams_tenant_id_fkey" FOREIGN KEY ("tenant_id") REFERENCES "tenants"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "teams" ADD CONSTRAINT "teams_manager_id_fkey" FOREIGN KEY ("manager_id") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "teams" ADD CONSTRAINT "teams_created_by_fkey" FOREIGN KEY ("created_by") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "teams" ADD CONSTRAINT "teams_parent_team_id_fkey" FOREIGN KEY ("parent_team_id") REFERENCES "teams"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "subscriptions" ADD CONSTRAINT "subscriptions_tenant_id_fkey" FOREIGN KEY ("tenant_id") REFERENCES "tenants"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "subscriptions" ADD CONSTRAINT "subscriptions_plan_id_fkey" FOREIGN KEY ("plan_id") REFERENCES "subscription_plans"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "usage_metrics" ADD CONSTRAINT "usage_metrics_tenant_id_fkey" FOREIGN KEY ("tenant_id") REFERENCES "tenants"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "billing_events" ADD CONSTRAINT "billing_events_tenant_id_fkey" FOREIGN KEY ("tenant_id") REFERENCES "tenants"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "billing_events" ADD CONSTRAINT "billing_events_subscription_id_fkey" FOREIGN KEY ("subscription_id") REFERENCES "subscriptions"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "roles" ADD CONSTRAINT "roles_tenant_id_fkey" FOREIGN KEY ("tenant_id") REFERENCES "tenants"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "roles" ADD CONSTRAINT "roles_created_by_fkey" FOREIGN KEY ("created_by") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "roles" ADD CONSTRAINT "roles_parent_role_id_fkey" FOREIGN KEY ("parent_role_id") REFERENCES "roles"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "permissions" ADD CONSTRAINT "permissions_tenant_id_fkey" FOREIGN KEY ("tenant_id") REFERENCES "tenants"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "user_roles" ADD CONSTRAINT "user_roles_tenant_id_fkey" FOREIGN KEY ("tenant_id") REFERENCES "tenants"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "user_roles" ADD CONSTRAINT "user_roles_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "user_roles" ADD CONSTRAINT "user_roles_role_id_fkey" FOREIGN KEY ("role_id") REFERENCES "roles"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "user_roles" ADD CONSTRAINT "user_roles_assigned_by_fkey" FOREIGN KEY ("assigned_by") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "role_permissions" ADD CONSTRAINT "role_permissions_tenant_id_fkey" FOREIGN KEY ("tenant_id") REFERENCES "tenants"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "role_permissions" ADD CONSTRAINT "role_permissions_role_id_fkey" FOREIGN KEY ("role_id") REFERENCES "roles"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "role_permissions" ADD CONSTRAINT "role_permissions_permission_id_fkey" FOREIGN KEY ("permission_id") REFERENCES "permissions"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "role_permissions" ADD CONSTRAINT "role_permissions_granted_by_fkey" FOREIGN KEY ("granted_by") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "companies" ADD CONSTRAINT "companies_tenant_id_fkey" FOREIGN KEY ("tenant_id") REFERENCES "tenants"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "companies" ADD CONSTRAINT "companies_owner_id_fkey" FOREIGN KEY ("owner_id") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "companies" ADD CONSTRAINT "companies_created_by_fkey" FOREIGN KEY ("created_by") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "contacts" ADD CONSTRAINT "contacts_tenant_id_fkey" FOREIGN KEY ("tenant_id") REFERENCES "tenants"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "contacts" ADD CONSTRAINT "contacts_company_id_fkey" FOREIGN KEY ("company_id") REFERENCES "companies"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "contacts" ADD CONSTRAINT "contacts_owner_id_fkey" FOREIGN KEY ("owner_id") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "contacts" ADD CONSTRAINT "contacts_created_by_fkey" FOREIGN KEY ("created_by") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "leads" ADD CONSTRAINT "leads_tenant_id_fkey" FOREIGN KEY ("tenant_id") REFERENCES "tenants"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "leads" ADD CONSTRAINT "leads_contact_id_fkey" FOREIGN KEY ("contact_id") REFERENCES "contacts"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "leads" ADD CONSTRAINT "leads_company_id_fkey" FOREIGN KEY ("company_id") REFERENCES "companies"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "leads" ADD CONSTRAINT "leads_lead_source_id_fkey" FOREIGN KEY ("lead_source_id") REFERENCES "lead_sources"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "leads" ADD CONSTRAINT "leads_owner_id_fkey" FOREIGN KEY ("owner_id") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "leads" ADD CONSTRAINT "leads_created_by_fkey" FOREIGN KEY ("created_by") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "leads" ADD CONSTRAINT "leads_converted_by_fkey" FOREIGN KEY ("converted_by") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "lead_conversion_history" ADD CONSTRAINT "lead_conversion_history_tenant_id_fkey" FOREIGN KEY ("tenant_id") REFERENCES "tenants"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "lead_conversion_history" ADD CONSTRAINT "lead_conversion_history_lead_id_fkey" FOREIGN KEY ("lead_id") REFERENCES "leads"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "lead_conversion_history" ADD CONSTRAINT "lead_conversion_history_opportunity_id_fkey" FOREIGN KEY ("opportunity_id") REFERENCES "opportunities"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "lead_conversion_history" ADD CONSTRAINT "lead_conversion_history_converted_by_fkey" FOREIGN KEY ("converted_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "lead_sources" ADD CONSTRAINT "lead_sources_tenant_id_fkey" FOREIGN KEY ("tenant_id") REFERENCES "tenants"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "lead_sources" ADD CONSTRAINT "lead_sources_created_by_fkey" FOREIGN KEY ("created_by") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "nurturing_campaigns" ADD CONSTRAINT "nurturing_campaigns_tenant_id_fkey" FOREIGN KEY ("tenant_id") REFERENCES "tenants"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "nurturing_campaigns" ADD CONSTRAINT "nurturing_campaigns_created_by_fkey" FOREIGN KEY ("created_by") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "nurturing_executions" ADD CONSTRAINT "nurturing_executions_campaign_id_fkey" FOREIGN KEY ("campaign_id") REFERENCES "nurturing_campaigns"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "nurturing_executions" ADD CONSTRAINT "nurturing_executions_lead_id_fkey" FOREIGN KEY ("lead_id") REFERENCES "leads"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "nurturing_email_logs" ADD CONSTRAINT "nurturing_email_logs_execution_id_fkey" FOREIGN KEY ("execution_id") REFERENCES "nurturing_executions"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "lead_notifications" ADD CONSTRAINT "lead_notifications_tenant_id_fkey" FOREIGN KEY ("tenant_id") REFERENCES "tenants"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "lead_notifications" ADD CONSTRAINT "lead_notifications_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "lead_notifications" ADD CONSTRAINT "lead_notifications_lead_id_fkey" FOREIGN KEY ("lead_id") REFERENCES "leads"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "notifications" ADD CONSTRAINT "notifications_tenant_id_fkey" FOREIGN KEY ("tenant_id") REFERENCES "tenants"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "notifications" ADD CONSTRAINT "notifications_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "user_notification_preferences" ADD CONSTRAINT "user_notification_preferences_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "email_lead_captures" ADD CONSTRAINT "email_lead_captures_tenant_id_fkey" FOREIGN KEY ("tenant_id") REFERENCES "tenants"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "email_lead_captures" ADD CONSTRAINT "email_lead_captures_lead_id_fkey" FOREIGN KEY ("lead_id") REFERENCES "leads"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "email_lead_captures" ADD CONSTRAINT "email_lead_captures_contact_id_fkey" FOREIGN KEY ("contact_id") REFERENCES "contacts"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "social_media_captures" ADD CONSTRAINT "social_media_captures_tenant_id_fkey" FOREIGN KEY ("tenant_id") REFERENCES "tenants"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "social_media_captures" ADD CONSTRAINT "social_media_captures_lead_id_fkey" FOREIGN KEY ("lead_id") REFERENCES "leads"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "social_media_captures" ADD CONSTRAINT "social_media_captures_contact_id_fkey" FOREIGN KEY ("contact_id") REFERENCES "contacts"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "crm_integrations" ADD CONSTRAINT "crm_integrations_tenant_id_fkey" FOREIGN KEY ("tenant_id") REFERENCES "tenants"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "crm_integrations" ADD CONSTRAINT "crm_integrations_created_by_fkey" FOREIGN KEY ("created_by") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "crm_integrations" ADD CONSTRAINT "crm_integrations_updated_by_fkey" FOREIGN KEY ("updated_by") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "crm_mappings" ADD CONSTRAINT "crm_mappings_tenant_id_fkey" FOREIGN KEY ("tenant_id") REFERENCES "tenants"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "crm_sync_logs" ADD CONSTRAINT "crm_sync_logs_tenant_id_fkey" FOREIGN KEY ("tenant_id") REFERENCES "tenants"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "sales_stages" ADD CONSTRAINT "sales_stages_tenant_id_fkey" FOREIGN KEY ("tenant_id") REFERENCES "tenants"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "opportunities" ADD CONSTRAINT "opportunities_tenant_id_fkey" FOREIGN KEY ("tenant_id") REFERENCES "tenants"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "opportunities" ADD CONSTRAINT "opportunities_lead_id_fkey" FOREIGN KEY ("lead_id") REFERENCES "leads"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "opportunities" ADD CONSTRAINT "opportunities_contact_id_fkey" FOREIGN KEY ("contact_id") REFERENCES "contacts"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "opportunities" ADD CONSTRAINT "opportunities_company_id_fkey" FOREIGN KEY ("company_id") REFERENCES "companies"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "opportunities" ADD CONSTRAINT "opportunities_owner_id_fkey" FOREIGN KEY ("owner_id") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "opportunities" ADD CONSTRAINT "opportunities_created_by_fkey" FOREIGN KEY ("created_by") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "opportunity_stage_history" ADD CONSTRAINT "opportunity_stage_history_tenant_id_fkey" FOREIGN KEY ("tenant_id") REFERENCES "tenants"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "opportunity_stage_history" ADD CONSTRAINT "opportunity_stage_history_opportunity_id_fkey" FOREIGN KEY ("opportunity_id") REFERENCES "opportunities"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "opportunity_stage_history" ADD CONSTRAINT "opportunity_stage_history_changed_by_fkey" FOREIGN KEY ("changed_by") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "proposals" ADD CONSTRAINT "proposals_tenant_id_fkey" FOREIGN KEY ("tenant_id") REFERENCES "tenants"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "proposals" ADD CONSTRAINT "proposals_opportunity_id_fkey" FOREIGN KEY ("opportunity_id") REFERENCES "opportunities"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "proposals" ADD CONSTRAINT "proposals_created_by_fkey" FOREIGN KEY ("created_by") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "proposals" ADD CONSTRAINT "proposals_parent_proposal_id_fkey" FOREIGN KEY ("parent_proposal_id") REFERENCES "proposals"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "proposal_sections" ADD CONSTRAINT "proposal_sections_tenant_id_fkey" FOREIGN KEY ("tenant_id") REFERENCES "tenants"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "proposal_sections" ADD CONSTRAINT "proposal_sections_proposal_id_fkey" FOREIGN KEY ("proposal_id") REFERENCES "proposals"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "proposal_sections" ADD CONSTRAINT "proposal_sections_created_by_fkey" FOREIGN KEY ("created_by") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "proposal_charts" ADD CONSTRAINT "proposal_charts_tenant_id_fkey" FOREIGN KEY ("tenant_id") REFERENCES "tenants"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "proposal_charts" ADD CONSTRAINT "proposal_charts_proposal_id_fkey" FOREIGN KEY ("proposal_id") REFERENCES "proposals"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "proposal_charts" ADD CONSTRAINT "proposal_charts_created_by_fkey" FOREIGN KEY ("created_by") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "projects" ADD CONSTRAINT "projects_tenant_id_fkey" FOREIGN KEY ("tenant_id") REFERENCES "tenants"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "projects" ADD CONSTRAINT "projects_opportunity_id_fkey" FOREIGN KEY ("opportunity_id") REFERENCES "opportunities"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "projects" ADD CONSTRAINT "projects_handover_id_fkey" FOREIGN KEY ("handover_id") REFERENCES "project_handovers"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "projects" ADD CONSTRAINT "projects_manager_id_fkey" FOREIGN KEY ("manager_id") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "projects" ADD CONSTRAINT "projects_created_by_fkey" FOREIGN KEY ("created_by") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "project_tasks" ADD CONSTRAINT "project_tasks_tenant_id_fkey" FOREIGN KEY ("tenant_id") REFERENCES "tenants"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "project_tasks" ADD CONSTRAINT "project_tasks_project_id_fkey" FOREIGN KEY ("project_id") REFERENCES "projects"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "project_tasks" ADD CONSTRAINT "project_tasks_milestone_id_fkey" FOREIGN KEY ("milestone_id") REFERENCES "project_milestones"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "project_tasks" ADD CONSTRAINT "project_tasks_assignee_id_fkey" FOREIGN KEY ("assignee_id") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "project_tasks" ADD CONSTRAINT "project_tasks_created_by_fkey" FOREIGN KEY ("created_by") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "project_tasks" ADD CONSTRAINT "project_tasks_parent_task_id_fkey" FOREIGN KEY ("parent_task_id") REFERENCES "project_tasks"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "project_milestones" ADD CONSTRAINT "project_milestones_tenant_id_fkey" FOREIGN KEY ("tenant_id") REFERENCES "tenants"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "project_milestones" ADD CONSTRAINT "project_milestones_project_id_fkey" FOREIGN KEY ("project_id") REFERENCES "projects"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "project_milestones" ADD CONSTRAINT "project_milestones_created_by_fkey" FOREIGN KEY ("created_by") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "project_resources" ADD CONSTRAINT "project_resources_tenant_id_fkey" FOREIGN KEY ("tenant_id") REFERENCES "tenants"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "project_resources" ADD CONSTRAINT "project_resources_project_id_fkey" FOREIGN KEY ("project_id") REFERENCES "projects"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "project_resources" ADD CONSTRAINT "project_resources_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "project_resources" ADD CONSTRAINT "project_resources_created_by_fkey" FOREIGN KEY ("created_by") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "project_client_access" ADD CONSTRAINT "project_client_access_tenant_id_fkey" FOREIGN KEY ("tenant_id") REFERENCES "tenants"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "project_client_access" ADD CONSTRAINT "project_client_access_project_id_fkey" FOREIGN KEY ("project_id") REFERENCES "projects"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "project_client_access" ADD CONSTRAINT "project_client_access_contact_id_fkey" FOREIGN KEY ("contact_id") REFERENCES "contacts"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "project_client_access" ADD CONSTRAINT "project_client_access_created_by_fkey" FOREIGN KEY ("created_by") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "handover_checklist_templates" ADD CONSTRAINT "handover_checklist_templates_tenant_id_fkey" FOREIGN KEY ("tenant_id") REFERENCES "tenants"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "handover_checklist_templates" ADD CONSTRAINT "handover_checklist_templates_created_by_fkey" FOREIGN KEY ("created_by") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "project_handovers" ADD CONSTRAINT "project_handovers_tenant_id_fkey" FOREIGN KEY ("tenant_id") REFERENCES "tenants"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "project_handovers" ADD CONSTRAINT "project_handovers_opportunity_id_fkey" FOREIGN KEY ("opportunity_id") REFERENCES "opportunities"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "project_handovers" ADD CONSTRAINT "project_handovers_template_id_fkey" FOREIGN KEY ("template_id") REFERENCES "handover_checklist_templates"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "project_handovers" ADD CONSTRAINT "project_handovers_sales_rep_id_fkey" FOREIGN KEY ("sales_rep_id") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "project_handovers" ADD CONSTRAINT "project_handovers_delivery_manager_id_fkey" FOREIGN KEY ("delivery_manager_id") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "project_handovers" ADD CONSTRAINT "project_handovers_created_by_fkey" FOREIGN KEY ("created_by") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "handover_checklist_items" ADD CONSTRAINT "handover_checklist_items_tenant_id_fkey" FOREIGN KEY ("tenant_id") REFERENCES "tenants"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "handover_checklist_items" ADD CONSTRAINT "handover_checklist_items_handover_id_fkey" FOREIGN KEY ("handover_id") REFERENCES "project_handovers"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "handover_checklist_items" ADD CONSTRAINT "handover_checklist_items_completed_by_fkey" FOREIGN KEY ("completed_by") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "handover_documents" ADD CONSTRAINT "handover_documents_tenant_id_fkey" FOREIGN KEY ("tenant_id") REFERENCES "tenants"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "handover_documents" ADD CONSTRAINT "handover_documents_handover_id_fkey" FOREIGN KEY ("handover_id") REFERENCES "project_handovers"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "handover_documents" ADD CONSTRAINT "handover_documents_document_id_fkey" FOREIGN KEY ("document_id") REFERENCES "documents"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "handover_documents" ADD CONSTRAINT "handover_documents_approved_by_fkey" FOREIGN KEY ("approved_by") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "handover_qa_threads" ADD CONSTRAINT "handover_qa_threads_tenant_id_fkey" FOREIGN KEY ("tenant_id") REFERENCES "tenants"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "handover_qa_threads" ADD CONSTRAINT "handover_qa_threads_handover_id_fkey" FOREIGN KEY ("handover_id") REFERENCES "project_handovers"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "handover_qa_threads" ADD CONSTRAINT "handover_qa_threads_created_by_fkey" FOREIGN KEY ("created_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "handover_qa_threads" ADD CONSTRAINT "handover_qa_threads_assigned_to_fkey" FOREIGN KEY ("assigned_to") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "handover_qa_messages" ADD CONSTRAINT "handover_qa_messages_tenant_id_fkey" FOREIGN KEY ("tenant_id") REFERENCES "tenants"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "handover_qa_messages" ADD CONSTRAINT "handover_qa_messages_thread_id_fkey" FOREIGN KEY ("thread_id") REFERENCES "handover_qa_threads"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "handover_qa_messages" ADD CONSTRAINT "handover_qa_messages_author_id_fkey" FOREIGN KEY ("author_id") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "handover_notifications" ADD CONSTRAINT "handover_notifications_tenant_id_fkey" FOREIGN KEY ("tenant_id") REFERENCES "tenants"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "handover_notifications" ADD CONSTRAINT "handover_notifications_handover_id_fkey" FOREIGN KEY ("handover_id") REFERENCES "project_handovers"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "handover_notifications" ADD CONSTRAINT "handover_notifications_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "activities" ADD CONSTRAINT "activities_tenant_id_fkey" FOREIGN KEY ("tenant_id") REFERENCES "tenants"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "activities" ADD CONSTRAINT "activities_owner_id_fkey" FOREIGN KEY ("owner_id") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "activities" ADD CONSTRAINT "activities_created_by_fkey" FOREIGN KEY ("created_by") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "scheduled_meetings" ADD CONSTRAINT "scheduled_meetings_tenant_id_fkey" FOREIGN KEY ("tenant_id") REFERENCES "tenants"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "scheduled_meetings" ADD CONSTRAINT "scheduled_meetings_lead_id_fkey" FOREIGN KEY ("lead_id") REFERENCES "leads"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "scheduled_meetings" ADD CONSTRAINT "scheduled_meetings_contact_id_fkey" FOREIGN KEY ("contact_id") REFERENCES "contacts"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "scheduled_meetings" ADD CONSTRAINT "scheduled_meetings_scheduled_by_fkey" FOREIGN KEY ("scheduled_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "scheduled_meetings" ADD CONSTRAINT "scheduled_meetings_assigned_to_fkey" FOREIGN KEY ("assigned_to") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "documents" ADD CONSTRAINT "documents_tenant_id_fkey" FOREIGN KEY ("tenant_id") REFERENCES "tenants"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "documents" ADD CONSTRAINT "documents_uploaded_by_fkey" FOREIGN KEY ("uploaded_by") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ai_insights" ADD CONSTRAINT "ai_insights_tenant_id_fkey" FOREIGN KEY ("tenant_id") REFERENCES "tenants"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "tenant_settings" ADD CONSTRAINT "tenant_settings_tenant_id_fkey" FOREIGN KEY ("tenant_id") REFERENCES "tenants"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "tenant_settings" ADD CONSTRAINT "tenant_settings_updated_by_fkey" FOREIGN KEY ("updated_by") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "audit_logs" ADD CONSTRAINT "audit_logs_tenant_id_fkey" FOREIGN KEY ("tenant_id") REFERENCES "tenants"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "audit_logs" ADD CONSTRAINT "audit_logs_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "email_logs" ADD CONSTRAINT "email_logs_tenant_id_fkey" FOREIGN KEY ("tenant_id") REFERENCES "tenants"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "email_logs" ADD CONSTRAINT "email_logs_sent_by_fkey" FOREIGN KEY ("sent_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "email_logs" ADD CONSTRAINT "email_logs_lead_id_fkey" FOREIGN KEY ("lead_id") REFERENCES "leads"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "email_logs" ADD CONSTRAINT "email_logs_contact_id_fkey" FOREIGN KEY ("contact_id") REFERENCES "contacts"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "impersonation_sessions" ADD CONSTRAINT "impersonation_sessions_super_admin_id_fkey" FOREIGN KEY ("super_admin_id") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "impersonation_sessions" ADD CONSTRAINT "impersonation_sessions_tenant_id_fkey" FOREIGN KEY ("tenant_id") REFERENCES "tenants"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "impersonation_sessions" ADD CONSTRAINT "impersonation_sessions_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "chat_channels" ADD CONSTRAINT "chat_channels_tenant_id_fkey" FOREIGN KEY ("tenant_id") REFERENCES "tenants"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "chat_channels" ADD CONSTRAINT "chat_channels_created_by_fkey" FOREIGN KEY ("created_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "chat_channel_members" ADD CONSTRAINT "chat_channel_members_tenant_id_fkey" FOREIGN KEY ("tenant_id") REFERENCES "tenants"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "chat_channel_members" ADD CONSTRAINT "chat_channel_members_channel_id_fkey" FOREIGN KEY ("channel_id") REFERENCES "chat_channels"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "chat_channel_members" ADD CONSTRAINT "chat_channel_members_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "chat_messages" ADD CONSTRAINT "chat_messages_tenant_id_fkey" FOREIGN KEY ("tenant_id") REFERENCES "tenants"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "chat_messages" ADD CONSTRAINT "chat_messages_channel_id_fkey" FOREIGN KEY ("channel_id") REFERENCES "chat_channels"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "chat_messages" ADD CONSTRAINT "chat_messages_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "chat_messages" ADD CONSTRAINT "chat_messages_parent_message_id_fkey" FOREIGN KEY ("parent_message_id") REFERENCES "chat_messages"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "calendar_events" ADD CONSTRAINT "calendar_events_tenant_id_fkey" FOREIGN KEY ("tenant_id") REFERENCES "tenants"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "calendar_events" ADD CONSTRAINT "calendar_events_created_by_fkey" FOREIGN KEY ("created_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "calendar_event_attendees" ADD CONSTRAINT "calendar_event_attendees_tenant_id_fkey" FOREIGN KEY ("tenant_id") REFERENCES "tenants"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "calendar_event_attendees" ADD CONSTRAINT "calendar_event_attendees_event_id_fkey" FOREIGN KEY ("event_id") REFERENCES "calendar_events"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "calendar_event_attendees" ADD CONSTRAINT "calendar_event_attendees_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "email_templates" ADD CONSTRAINT "email_templates_tenant_id_fkey" FOREIGN KEY ("tenant_id") REFERENCES "tenants"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "email_templates" ADD CONSTRAINT "email_templates_created_by_fkey" FOREIGN KEY ("created_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "automation_triggers" ADD CONSTRAINT "automation_triggers_tenant_id_fkey" FOREIGN KEY ("tenant_id") REFERENCES "tenants"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "automation_triggers" ADD CONSTRAINT "automation_triggers_created_by_fkey" FOREIGN KEY ("created_by") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "automation_executions" ADD CONSTRAINT "automation_executions_tenant_id_fkey" FOREIGN KEY ("tenant_id") REFERENCES "tenants"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "automation_executions" ADD CONSTRAINT "automation_executions_trigger_id_fkey" FOREIGN KEY ("trigger_id") REFERENCES "automation_triggers"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- ============================================================================
-- COMPREHENSIVE PERFORMANCE OPTIMIZATION INDEXES
-- Multi-Tenant CRM Platform - Database Best Practices Implementation
-- ============================================================================

-- ============================================================================
-- TENANT-SCOPED COMPOSITE INDEXES FOR COMMON QUERY PATTERNS
-- ============================================================================

-- Contacts optimization - Multi-tenant with performance focus
CREATE INDEX IF NOT EXISTS idx_contacts_tenant_created
ON contacts(tenant_id, created_at);

CREATE INDEX IF NOT EXISTS idx_contacts_tenant_owner
ON contacts(tenant_id, owner_id) WHERE owner_id IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_contacts_tenant_company
ON contacts(tenant_id, company_id) WHERE company_id IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_contacts_tenant_email
ON contacts(tenant_id, email) WHERE email IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_contacts_tenant_phone
ON contacts(tenant_id, phone) WHERE phone IS NOT NULL;

-- Leads optimization with soft delete consideration
CREATE INDEX IF NOT EXISTS idx_leads_tenant_status_created
ON leads(tenant_id, status, created_at) WHERE deleted_at IS NULL;

CREATE INDEX IF NOT EXISTS idx_leads_tenant_owner_status
ON leads(tenant_id, owner_id, status) WHERE deleted_at IS NULL AND owner_id IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_leads_tenant_source
ON leads(tenant_id, lead_source_id) WHERE deleted_at IS NULL AND lead_source_id IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_leads_tenant_priority
ON leads(tenant_id, priority, created_at) WHERE deleted_at IS NULL;

CREATE INDEX IF NOT EXISTS idx_leads_tenant_converted
ON leads(tenant_id, is_converted, converted_at) WHERE is_converted = true;

CREATE INDEX IF NOT EXISTS idx_leads_tenant_value
ON leads(tenant_id, value) WHERE deleted_at IS NULL AND value IS NOT NULL;

-- Opportunities optimization
CREATE INDEX IF NOT EXISTS idx_opportunities_tenant_stage_created
ON opportunities(tenant_id, stage, created_at);

CREATE INDEX IF NOT EXISTS idx_opportunities_tenant_owner_stage
ON opportunities(tenant_id, owner_id, stage) WHERE owner_id IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_opportunities_tenant_value
ON opportunities(tenant_id, value) WHERE value IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_opportunities_tenant_close_date
ON opportunities(tenant_id, expected_close_date) WHERE expected_close_date IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_opportunities_tenant_priority
ON opportunities(tenant_id, priority, created_at);

-- Companies optimization
CREATE INDEX IF NOT EXISTS idx_companies_tenant_industry
ON companies(tenant_id, industry) WHERE industry IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_companies_tenant_size
ON companies(tenant_id, size) WHERE size IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_companies_tenant_owner
ON companies(tenant_id, owner_id) WHERE owner_id IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_companies_tenant_name
ON companies(tenant_id, name);

-- ============================================================================
-- DASHBOARD ANALYTICS OPTIMIZATION INDEXES
-- ============================================================================

-- Monthly aggregation indexes for dashboard metrics (simplified for compatibility)
CREATE INDEX IF NOT EXISTS idx_leads_tenant_created_date
ON leads(tenant_id, created_at) WHERE deleted_at IS NULL;

CREATE INDEX IF NOT EXISTS idx_opportunities_tenant_created_date
ON opportunities(tenant_id, created_at);

CREATE INDEX IF NOT EXISTS idx_contacts_tenant_created_date
ON contacts(tenant_id, created_at);

CREATE INDEX IF NOT EXISTS idx_companies_tenant_created_date
ON companies(tenant_id, created_at);

-- Additional date-based indexes for analytics (simplified)
CREATE INDEX IF NOT EXISTS idx_leads_tenant_created_desc
ON leads(tenant_id, created_at DESC) WHERE deleted_at IS NULL;

CREATE INDEX IF NOT EXISTS idx_opportunities_tenant_created_desc
ON opportunities(tenant_id, created_at DESC);

-- Conversion tracking indexes
CREATE INDEX IF NOT EXISTS idx_lead_conversion_history_tenant_date
ON lead_conversion_history(tenant_id, converted_at);

-- ============================================================================
-- FULL-TEXT SEARCH OPTIMIZATION WITH GIN INDEXES
-- ============================================================================

-- Contacts search optimization
CREATE INDEX IF NOT EXISTS idx_contacts_search
ON contacts USING GIN(to_tsvector('english',
  coalesce(first_name, '') || ' ' ||
  coalesce(last_name, '') || ' ' ||
  coalesce(email, '') || ' ' ||
  coalesce(phone, '') || ' ' ||
  coalesce(job_title, '')
));

-- Companies search optimization
CREATE INDEX IF NOT EXISTS idx_companies_search
ON companies USING GIN(to_tsvector('english',
  coalesce(name, '') || ' ' ||
  coalesce(website, '') || ' ' ||
  coalesce(industry, '')
));

-- Leads search optimization
CREATE INDEX IF NOT EXISTS idx_leads_search
ON leads USING GIN(to_tsvector('english',
  coalesce(title, '') || ' ' ||
  coalesce(description, '')
)) WHERE deleted_at IS NULL;

-- Opportunities search optimization
CREATE INDEX IF NOT EXISTS idx_opportunities_search
ON opportunities USING GIN(to_tsvector('english',
  coalesce(title, '') || ' ' ||
  coalesce(description, '')
));

-- ============================================================================
-- FOREIGN KEY PERFORMANCE INDEXES
-- ============================================================================

-- Activities optimization
CREATE INDEX IF NOT EXISTS idx_activities_tenant_related
ON activities(tenant_id, related_to_type, related_to_id) WHERE related_to_type IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_activities_tenant_user_date
ON activities(tenant_id, owner_id, created_at) WHERE owner_id IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_activities_tenant_type_date
ON activities(tenant_id, type, created_at);

-- Documents optimization
CREATE INDEX IF NOT EXISTS idx_documents_tenant_related
ON documents(tenant_id, related_to_type, related_to_id) WHERE related_to_type IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_documents_tenant_type
ON documents(tenant_id, mime_type) WHERE mime_type IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_documents_tenant_public
ON documents(tenant_id, is_public);

-- Audit logs optimization
CREATE INDEX IF NOT EXISTS idx_audit_logs_tenant_created
ON audit_logs(tenant_id, created_at) WHERE tenant_id IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_audit_logs_tenant_user_action
ON audit_logs(tenant_id, user_id, action) WHERE tenant_id IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_audit_logs_tenant_resource
ON audit_logs(tenant_id, resource_type, resource_id) WHERE tenant_id IS NOT NULL;

-- ============================================================================
-- USER AND PERMISSION OPTIMIZATION INDEXES
-- ============================================================================

-- User roles optimization
CREATE INDEX IF NOT EXISTS idx_user_roles_tenant_user
ON user_roles(tenant_id, user_id);

CREATE INDEX IF NOT EXISTS idx_user_roles_tenant_role
ON user_roles(tenant_id, role_id);

CREATE INDEX IF NOT EXISTS idx_user_roles_expires
ON user_roles(tenant_id, expires_at) WHERE expires_at IS NOT NULL;

-- Role permissions optimization
CREATE INDEX IF NOT EXISTS idx_role_permissions_tenant_role
ON role_permissions(tenant_id, role_id);

CREATE INDEX IF NOT EXISTS idx_role_permissions_tenant_permission
ON role_permissions(tenant_id, permission_id);

-- Tenant users optimization
CREATE INDEX IF NOT EXISTS idx_tenant_users_user_status
ON tenant_users(user_id, status);

CREATE INDEX IF NOT EXISTS idx_tenant_users_tenant_role
ON tenant_users(tenant_id, role);

CREATE INDEX IF NOT EXISTS idx_tenant_users_tenant_team
ON tenant_users(tenant_id, team_id) WHERE team_id IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_tenant_users_tenant_manager
ON tenant_users(tenant_id, manager_id) WHERE manager_id IS NOT NULL;

-- ============================================================================
-- PARTIAL INDEXES FOR FILTERED QUERIES
-- ============================================================================

-- Active records only indexes
CREATE INDEX IF NOT EXISTS idx_leads_active_tenant_owner
ON leads(tenant_id, owner_id)
WHERE deleted_at IS NULL AND status != 'converted' AND owner_id IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_subscriptions_active_tenant
ON subscriptions(tenant_id, status, current_period_end)
WHERE status = 'active';

-- Recent activity indexes (simplified for compatibility)
CREATE INDEX IF NOT EXISTS idx_activities_recent_tenant
ON activities(tenant_id, created_at DESC);

-- Unread notifications
CREATE INDEX IF NOT EXISTS idx_notifications_unread_tenant
ON notifications(tenant_id, user_id, created_at)
WHERE is_read = false;

CREATE INDEX IF NOT EXISTS idx_lead_notifications_unread_tenant
ON lead_notifications(tenant_id, user_id, created_at)
WHERE is_read = false;

-- High-value opportunities
CREATE INDEX IF NOT EXISTS idx_opportunities_high_value_tenant
ON opportunities(tenant_id, value, stage)
WHERE value > 10000;

-- ============================================================================
-- SPECIALIZED INDEXES FOR REPORTING AND ANALYTICS
-- ============================================================================

-- Lead source effectiveness
CREATE INDEX IF NOT EXISTS idx_leads_source_conversion
ON leads(tenant_id, lead_source_id, is_converted, created_at)
WHERE lead_source_id IS NOT NULL;

-- Opportunity stage progression
CREATE INDEX IF NOT EXISTS idx_opportunity_stage_history_progression
ON opportunity_stage_history(tenant_id, opportunity_id, changed_at);

-- Usage metrics optimization
CREATE INDEX IF NOT EXISTS idx_usage_metrics_tenant_period
ON usage_metrics(tenant_id, metric_name, period_start);

-- AI insights optimization
CREATE INDEX IF NOT EXISTS idx_ai_insights_tenant_type_date
ON ai_insights(tenant_id, insight_type, created_at);

-- ============================================================================
-- MAINTENANCE AND MONITORING INDEXES
-- ============================================================================

-- Email logs optimization
CREATE INDEX IF NOT EXISTS idx_email_logs_tenant_status_date
ON email_logs(tenant_id, status, sent_at);

CREATE INDEX IF NOT EXISTS idx_email_logs_tenant_lead
ON email_logs(tenant_id, lead_id) WHERE lead_id IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_email_logs_tenant_contact
ON email_logs(tenant_id, contact_id) WHERE contact_id IS NOT NULL;

-- Scheduled meetings optimization
CREATE INDEX IF NOT EXISTS idx_scheduled_meetings_tenant_date
ON scheduled_meetings(tenant_id, scheduled_at);

CREATE INDEX IF NOT EXISTS idx_scheduled_meetings_tenant_assigned
ON scheduled_meetings(tenant_id, assigned_to, scheduled_at) WHERE assigned_to IS NOT NULL;

-- Billing events optimization
CREATE INDEX IF NOT EXISTS idx_billing_events_tenant_type_date
ON billing_events(tenant_id, event_type, created_at);

-- Team management optimization
CREATE INDEX IF NOT EXISTS idx_teams_tenant_active
ON teams(tenant_id, is_active) WHERE is_active = true;

CREATE INDEX IF NOT EXISTS idx_teams_tenant_manager
ON teams(tenant_id, manager_id) WHERE manager_id IS NOT NULL;

-- ============================================================================
-- PROJECT MANAGEMENT OPTIMIZATION INDEXES
-- ============================================================================

-- Project tasks optimization
CREATE INDEX IF NOT EXISTS idx_project_tasks_tenant_project_status
ON project_tasks(tenant_id, project_id, status);

CREATE INDEX IF NOT EXISTS idx_project_tasks_tenant_assignee_status
ON project_tasks(tenant_id, assignee_id, status) WHERE assignee_id IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_project_tasks_tenant_due_date
ON project_tasks(tenant_id, due_date) WHERE due_date IS NOT NULL;

-- Project milestones optimization
CREATE INDEX IF NOT EXISTS idx_project_milestones_tenant_project_status
ON project_milestones(tenant_id, project_id, status);

CREATE INDEX IF NOT EXISTS idx_project_milestones_tenant_due_date
ON project_milestones(tenant_id, due_date);

-- Project resources optimization
CREATE INDEX IF NOT EXISTS idx_project_resources_tenant_project_active
ON project_resources(tenant_id, project_id, is_active) WHERE is_active = true;

CREATE INDEX IF NOT EXISTS idx_project_resources_tenant_user_active
ON project_resources(tenant_id, user_id, is_active) WHERE is_active = true;

-- Projects optimization
CREATE INDEX IF NOT EXISTS idx_projects_tenant_status_priority
ON projects(tenant_id, status, priority);

CREATE INDEX IF NOT EXISTS idx_projects_tenant_manager_status
ON projects(tenant_id, manager_id, status) WHERE manager_id IS NOT NULL;

-- ============================================================================
-- HANDOVER SYSTEM OPTIMIZATION INDEXES
-- ============================================================================

-- Project handovers optimization
CREATE INDEX IF NOT EXISTS idx_project_handovers_tenant_status
ON project_handovers(tenant_id, status);

CREATE INDEX IF NOT EXISTS idx_project_handovers_tenant_sales_rep
ON project_handovers(tenant_id, sales_rep_id) WHERE sales_rep_id IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_project_handovers_tenant_delivery_manager
ON project_handovers(tenant_id, delivery_manager_id) WHERE delivery_manager_id IS NOT NULL;

-- Handover checklist items optimization
CREATE INDEX IF NOT EXISTS idx_handover_checklist_items_tenant_handover
ON handover_checklist_items(tenant_id, handover_id);

CREATE INDEX IF NOT EXISTS idx_handover_checklist_items_tenant_completed
ON handover_checklist_items(tenant_id, is_completed, completed_at);

-- ============================================================================
-- AUTOMATION SYSTEM OPTIMIZATION INDEXES
-- ============================================================================

-- Automation triggers optimization
CREATE INDEX IF NOT EXISTS idx_automation_triggers_tenant_active
ON automation_triggers(tenant_id, is_active) WHERE is_active = true;

CREATE INDEX IF NOT EXISTS idx_automation_triggers_tenant_trigger_type
ON automation_triggers(tenant_id, trigger_type);

-- Automation executions optimization
CREATE INDEX IF NOT EXISTS idx_automation_executions_tenant_status
ON automation_executions(tenant_id, status);

CREATE INDEX IF NOT EXISTS idx_automation_executions_tenant_trigger_date
ON automation_executions(tenant_id, trigger_id, started_at);

-- ============================================================================
-- COMMUNICATION SYSTEM OPTIMIZATION INDEXES
-- ============================================================================

-- Chat channels optimization
CREATE INDEX IF NOT EXISTS idx_chat_channels_tenant_type
ON chat_channels(tenant_id, type);

CREATE INDEX IF NOT EXISTS idx_chat_channels_tenant_related
ON chat_channels(tenant_id, related_to_type, related_to_id) WHERE related_to_type IS NOT NULL;

-- Chat messages optimization
CREATE INDEX IF NOT EXISTS idx_chat_messages_tenant_channel_date
ON chat_messages(tenant_id, channel_id, created_at);

CREATE INDEX IF NOT EXISTS idx_chat_messages_tenant_user
ON chat_messages(tenant_id, user_id);

-- Calendar events optimization
CREATE INDEX IF NOT EXISTS idx_calendar_events_tenant_start_time
ON calendar_events(tenant_id, start_time);

CREATE INDEX IF NOT EXISTS idx_calendar_events_tenant_type
ON calendar_events(tenant_id, "eventType");

-- Email templates optimization
CREATE INDEX IF NOT EXISTS idx_email_templates_tenant_type
ON email_templates(tenant_id, type);

CREATE INDEX IF NOT EXISTS idx_email_templates_tenant_category
ON email_templates(tenant_id, category);

-- ============================================================================
-- PROPOSAL SYSTEM OPTIMIZATION INDEXES
-- ============================================================================

-- Proposals optimization
CREATE INDEX IF NOT EXISTS idx_proposals_tenant_status
ON proposals(tenant_id, status);

CREATE INDEX IF NOT EXISTS idx_proposals_tenant_opportunity
ON proposals(tenant_id, opportunity_id) WHERE opportunity_id IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_proposals_tenant_ai_generated
ON proposals(tenant_id, is_ai_generated, generation_status) WHERE is_ai_generated = true;

-- Proposal sections optimization
CREATE INDEX IF NOT EXISTS idx_proposal_sections_tenant_proposal
ON proposal_sections(tenant_id, proposal_id);

CREATE INDEX IF NOT EXISTS idx_proposal_sections_tenant_type
ON proposal_sections(tenant_id, section_type);

-- Proposal charts optimization
CREATE INDEX IF NOT EXISTS idx_proposal_charts_tenant_proposal
ON proposal_charts(tenant_id, proposal_id);

CREATE INDEX IF NOT EXISTS idx_proposal_charts_tenant_type
ON proposal_charts(tenant_id, chart_type);

-- ============================================================================
-- LEAD CAPTURE SYSTEM OPTIMIZATION INDEXES
-- ============================================================================

-- Email lead captures optimization
CREATE INDEX IF NOT EXISTS idx_email_lead_captures_tenant_processed
ON email_lead_captures(tenant_id, processed, received_at);

CREATE INDEX IF NOT EXISTS idx_email_lead_captures_tenant_lead
ON email_lead_captures(tenant_id, lead_id) WHERE lead_id IS NOT NULL;

-- Social media captures optimization
CREATE INDEX IF NOT EXISTS idx_social_media_captures_tenant_platform
ON social_media_captures(tenant_id, platform);

CREATE INDEX IF NOT EXISTS idx_social_media_captures_tenant_processed
ON social_media_captures(tenant_id, processed, captured_at);

-- ============================================================================
-- NURTURING SYSTEM OPTIMIZATION INDEXES
-- ============================================================================

-- Nurturing campaigns optimization
CREATE INDEX IF NOT EXISTS idx_nurturing_campaigns_tenant_active
ON nurturing_campaigns(tenant_id, is_active) WHERE is_active = true AND deleted_at IS NULL;

-- Nurturing executions optimization
CREATE INDEX IF NOT EXISTS idx_nurturing_executions_campaign_status
ON nurturing_executions(campaign_id, status);

CREATE INDEX IF NOT EXISTS idx_nurturing_executions_next_execution
ON nurturing_executions(next_execution_date) WHERE status = 'active';

-- ============================================================================
-- CRM INTEGRATION OPTIMIZATION INDEXES
-- ============================================================================

-- CRM integrations optimization
CREATE INDEX IF NOT EXISTS idx_crm_integrations_tenant_provider
ON crm_integrations(tenant_id, provider);

CREATE INDEX IF NOT EXISTS idx_crm_integrations_tenant_sync_status
ON crm_integrations(tenant_id, "syncStatus", last_sync_at);

-- CRM sync logs optimization
CREATE INDEX IF NOT EXISTS idx_crm_sync_logs_tenant_operation_date
ON crm_sync_logs(tenant_id, operation, synced_at);

CREATE INDEX IF NOT EXISTS idx_crm_sync_logs_tenant_entity
ON crm_sync_logs(tenant_id, entity_type, entity_id);

-- ============================================================================
-- PERFORMANCE MONITORING INDEXES
-- ============================================================================

-- General performance monitoring
CREATE INDEX IF NOT EXISTS idx_tenants_status_created
ON tenants(status, created_at);

CREATE INDEX IF NOT EXISTS idx_users_status_last_login
ON users(status, last_login) WHERE last_login IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_users_platform_admin
ON users(is_platform_admin) WHERE is_platform_admin = true;

-- Feature flags optimization
CREATE INDEX IF NOT EXISTS idx_feature_flags_enabled
ON feature_flags(is_enabled, required_plan_level) WHERE is_enabled = true;

-- ============================================================================
-- COMMENT DOCUMENTATION
-- ============================================================================

COMMENT ON INDEX idx_contacts_tenant_created IS 'Optimizes contact listing by tenant with date sorting';
COMMENT ON INDEX idx_leads_tenant_status_created IS 'Optimizes lead queries with soft delete consideration and status filtering';
COMMENT ON INDEX idx_opportunities_tenant_stage_created IS 'Optimizes opportunity pipeline views by stage and creation date';
COMMENT ON INDEX idx_contacts_search IS 'Full-text search optimization for contacts across multiple fields';
COMMENT ON INDEX idx_companies_search IS 'Full-text search optimization for companies';
COMMENT ON INDEX idx_activities_tenant_related IS 'Optimizes activity queries related to specific entities';
COMMENT ON INDEX idx_user_roles_tenant_user IS 'Optimizes RBAC permission checking queries';
COMMENT ON INDEX idx_notifications_unread_tenant IS 'Optimizes unread notification queries for dashboard';
COMMENT ON INDEX idx_leads_source_conversion IS 'Optimizes lead source effectiveness reporting';
COMMENT ON INDEX idx_opportunities_high_value_tenant IS 'Optimizes high-value opportunity tracking';
