'use client';

import React from 'react';
import {
  Calendar,
  Clock,
  Video,
  Phone,
  MapPin,
  Users,
  TrendingUp,
  RefreshCw
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { useMeetingsOverview, TimePeriod } from '@/hooks/use-meetings-overview';
import { ScheduledMeetingWithRelations } from '@/services/scheduled-meeting-service';
import { formatMeetingDate, formatMeetingTime, getPeriodLabel } from '@/lib/date-utils';
import { cn } from '@/lib/utils';

interface MeetingsOverviewCardProps {
  className?: string;
  onMeetingClick?: (meeting: ScheduledMeetingWithRelations) => void;
}

export function MeetingsOverviewCard({ 
  className, 
  onMeetingClick 
}: MeetingsOverviewCardProps) {
  const {
    upcomingMeetings,
    pastMeetings,
    loading,
    error,
    timePeriod,
    setTimePeriod,
    refetch
  } = useMeetingsOverview();

  const getMeetingTypeIcon = (type: string) => {
    switch (type) {
      case 'video':
        return <Video className="w-4 h-4" />;
      case 'phone':
        return <Phone className="w-4 h-4" />;
      case 'in-person':
        return <MapPin className="w-4 h-4" />;
      default:
        return <Calendar className="w-4 h-4" />;
    }
  };



  const renderMeetingItem = (meeting: ScheduledMeetingWithRelations, isPast: boolean = false) => {
    const meetingDate = new Date(meeting.scheduledAt);
    const contactName = meeting.lead?.contact 
      ? `${meeting.lead.contact.firstName} ${meeting.lead.contact.lastName || ''}`.trim()
      : meeting.contact 
        ? `${meeting.contact.firstName} ${meeting.contact.lastName || ''}`.trim()
        : 'Unknown Contact';

    return (
      <div
        key={meeting.id}
        className={cn(
          "flex items-center justify-between p-3 rounded-lg border transition-colors cursor-pointer",
          isPast 
            ? "bg-gray-50/50 dark:bg-gray-800/30 border-gray-200 dark:border-gray-700 opacity-75 hover:opacity-90" 
            : "bg-blue-50/30 dark:bg-blue-900/10 border-blue-200 dark:border-blue-800 hover:bg-blue-50/50 dark:hover:bg-blue-900/20"
        )}
        onClick={() => onMeetingClick?.(meeting)}
      >
        <div className="flex items-center space-x-3 flex-1 min-w-0">
          <div className={cn(
            "p-2 rounded-lg",
            isPast 
              ? "bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400"
              : "bg-blue-100 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400"
          )}>
            {getMeetingTypeIcon(meeting.meetingType)}
          </div>
          
          <div className="flex-1 min-w-0">
            <div className="flex items-center space-x-2">
              <p className="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
                {meeting.title}
              </p>
              {meeting.status === 'completed' && (
                <Badge variant="secondary" className="text-xs">
                  Completed
                </Badge>
              )}
            </div>
            <p className="text-xs text-gray-600 dark:text-gray-400 truncate">
              {contactName}
            </p>
            {meeting.meetingType === 'in-person' && meeting.location && (
              <p className="text-xs text-gray-500 dark:text-gray-500 truncate">
                📍 {meeting.location}
              </p>
            )}
          </div>
        </div>

        <div className="text-right">
          <p className="text-xs font-medium text-gray-900 dark:text-gray-100">
            {formatMeetingDate(meetingDate)}
          </p>
          <p className="text-xs text-gray-600 dark:text-gray-400">
            {formatMeetingTime(meetingDate)}
          </p>
        </div>
      </div>
    );
  };

  const renderEmptyState = () => (
    <div className="text-center py-8">
      <Calendar className="w-12 h-12 text-gray-400 dark:text-gray-600 mx-auto mb-4" />
      <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
        No meetings scheduled for {getPeriodLabel(timePeriod).toLowerCase()}
      </p>
      <p className="text-xs text-gray-500 dark:text-gray-500">
        Schedule a meeting to get started
      </p>
    </div>
  );

  const renderLoadingSkeleton = () => (
    <div className="space-y-3">
      {Array.from({ length: 3 }).map((_, i) => (
        <div key={i} className="flex items-center space-x-3 p-3">
          <Skeleton className="w-10 h-10 rounded-lg" />
          <div className="flex-1 space-y-2">
            <Skeleton className="h-4 w-3/4" />
            <Skeleton className="h-3 w-1/2" />
          </div>
          <div className="text-right space-y-1">
            <Skeleton className="h-3 w-16" />
            <Skeleton className="h-3 w-12" />
          </div>
        </div>
      ))}
    </div>
  );

  return (
    <Card className={cn("h-fit", className)}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <div className="p-2 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
              <Calendar className="w-5 h-5 text-blue-600 dark:text-blue-400" />
            </div>
            <div>
              <CardTitle className="text-lg">Scheduled Meetings</CardTitle>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                {getPeriodLabel(timePeriod)}
              </p>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={refetch}
              disabled={loading}
              className="p-2"
            >
              <RefreshCw className={cn("w-4 h-4", loading && "animate-spin")} />
            </Button>
          </div>
        </div>

        {/* Time Period Toggle */}
        <div className="flex space-x-1 bg-gray-100 dark:bg-gray-800 rounded-lg p-1 border border-gray-200 dark:border-gray-700">
          {(['day', 'week', 'month'] as TimePeriod[]).map((period) => (
            <Button
              key={period}
              variant={timePeriod === period ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setTimePeriod(period)}
              className={cn(
                "flex-1 text-xs font-medium transition-all duration-200",
                timePeriod === period
                  ? "bg-white dark:bg-gray-700 shadow-md text-gray-900 dark:text-gray-100 border border-gray-200 dark:border-gray-600"
                  : "text-gray-600 dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-gray-200"
              )}
            >
              {period === 'day' ? 'Day' : period === 'week' ? 'Week' : 'Month'}
            </Button>
          ))}
        </div>
      </CardHeader>

      <CardContent className="pt-0">
        {loading ? (
          renderLoadingSkeleton()
        ) : error ? (
          <div className="text-center py-8">
            <p className="text-sm text-red-600 dark:text-red-400 mb-2">
              Failed to load meetings
            </p>
            <Button variant="outline" size="sm" onClick={refetch}>
              Try Again
            </Button>
          </div>
        ) : upcomingMeetings.length === 0 && pastMeetings.length === 0 ? (
          renderEmptyState()
        ) : (
          <div className="space-y-4">
            {/* Upcoming Meetings */}
            {upcomingMeetings.length > 0 && (
              <div>
                <div className="flex items-center justify-between mb-3">
                  <h4 className="text-sm font-semibold text-gray-900 dark:text-gray-100 flex items-center space-x-2">
                    <Clock className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                    <span>Upcoming</span>
                    <Badge variant="outline" className="text-xs">
                      {upcomingMeetings.length}
                    </Badge>
                  </h4>
                </div>
                <div className="space-y-2">
                  {upcomingMeetings.slice(0, 3).map((meeting) => 
                    renderMeetingItem(meeting, false)
                  )}
                  {upcomingMeetings.length > 3 && (
                    <p className="text-xs text-gray-500 dark:text-gray-500 text-center py-2">
                      +{upcomingMeetings.length - 3} more upcoming
                    </p>
                  )}
                </div>
              </div>
            )}

            {/* Past Meetings */}
            {pastMeetings.length > 0 && (
              <div>
                <div className="flex items-center justify-between mb-3">
                  <h4 className="text-sm font-semibold text-gray-900 dark:text-gray-100 flex items-center space-x-2">
                    <Calendar className="w-4 h-4 text-gray-600 dark:text-gray-400" />
                    <span>Recent</span>
                    <Badge variant="outline" className="text-xs">
                      {pastMeetings.length}
                    </Badge>
                  </h4>
                </div>
                <div className="space-y-2">
                  {pastMeetings.slice(0, 2).map((meeting) => 
                    renderMeetingItem(meeting, true)
                  )}
                  {pastMeetings.length > 2 && (
                    <p className="text-xs text-gray-500 dark:text-gray-500 text-center py-2">
                      +{pastMeetings.length - 2} more recent
                    </p>
                  )}
                </div>
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
