import { useState, useEffect, useCallback } from 'react';
import { useSession } from 'next-auth/react';
import { TenantContext } from '@/types';

interface UseTenantReturn {
  currentTenant: TenantContext | null;
  isLoading: boolean;
  error: string | null;
  refreshTenant: () => Promise<void>;
}

export function useTenant(): UseTenantReturn {
  const { data: session, status } = useSession();
  const [currentTenant, setCurrentTenant] = useState<TenantContext | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Load user's single tenant
  const loadTenant = useCallback(async () => {
    if (status !== 'authenticated' || !session?.user) {
      setIsLoading(false);
      return;
    }

    try {
      setError(null);
      const response = await fetch('/api/tenants');

      if (!response.ok) {
        throw new Error('Failed to load tenant');
      }

      const data = await response.json();
      const tenants = data.data || [];

      // Each user should have exactly one tenant
      if (tenants.length > 0) {
        const tenant = tenants[0]; // Take the first (and should be only) tenant

        const tenantContext: TenantContext = {
          id: tenant.id,
          name: tenant.name,
          slug: tenant.slug,
          subscription: {
            planId: tenant.subscription?.planName || '',
            status: tenant.subscription?.status || 'inactive',
            features: tenant.subscription?.features || [],
            limits: tenant.subscription?.limits || {}
          },
          settings: tenant.settings || {},
          branding: tenant.branding || {}
        };

        setCurrentTenant(tenantContext);
      } else {
        // No tenant found - user needs to create one
        setCurrentTenant(null);
      }
    } catch (err) {
      console.error('Error loading tenant:', err);
      setError(err instanceof Error ? err.message : 'Failed to load tenant');
    } finally {
      setIsLoading(false);
    }
  }, [status, session]);

  // Refresh tenant data
  const refreshTenant = useCallback(async () => {
    await loadTenant();
  }, [loadTenant]);

  // Load tenant on mount and when session changes
  useEffect(() => {
    if (status === 'authenticated') {
      loadTenant();
    } else if (status === 'unauthenticated') {
      setCurrentTenant(null);
      setIsLoading(false);
    }
  }, [status, loadTenant]);

  return {
    currentTenant,
    isLoading,
    error,
    refreshTenant
  };
}

// Hook for checking tenant permissions
export function useTenantPermissions() {
  const { currentTenant } = useTenant();
  
  const hasFeature = useCallback((feature: string): boolean => {
    if (!currentTenant?.subscription?.features) return false;
    return currentTenant.subscription.features.includes(feature);
  }, [currentTenant]);

  const checkLimit = useCallback((resource: string): { current: number; limit: number; isExceeded: boolean } => {
    const limit = currentTenant?.subscription?.limits?.[resource] || 0;
    // TODO: Get current usage from API
    const current = 0;
    
    return {
      current,
      limit,
      isExceeded: current >= limit
    };
  }, [currentTenant]);

  const canCreateResource = useCallback((resource: string): boolean => {
    const { isExceeded } = checkLimit(resource);
    return !isExceeded;
  }, [checkLimit]);

  return {
    hasFeature,
    checkLimit,
    canCreateResource,
    subscription: currentTenant?.subscription
  };
}
