#!/bin/bash
# Script to make all shell scripts executable in the CRM project

# Set the script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

echo "Making scripts executable..."

# Make all shell scripts in the scripts directory executable
find "$SCRIPT_DIR" -name "*.sh" -exec chmod +x {} \;

echo "The following scripts are now executable:"
find "$SCRIPT_DIR" -name "*.sh" | sort

echo "Done!"
