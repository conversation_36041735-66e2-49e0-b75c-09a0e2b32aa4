'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { useTenant } from '@/hooks/use-tenant';
import { UserWithTenantInfo } from '@/services/user-management';
import { PermissionGate } from '@/components/ui/permission-gate';
import { PermissionResource, PermissionAction } from '@/types';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { ConfirmationDialog } from '@/components/ui/confirmation-dialog';
import {
  UserIcon,
  MagnifyingGlassIcon,
  PencilIcon,
  TrashIcon,
  ExclamationTriangleIcon,
  ChevronDownIcon,
  XMarkIcon
} from '@heroicons/react/24/outline';

interface UserListResponse {
  success: boolean;
  data: UserWithTenantInfo[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

interface UserManagementProps {
  className?: string;
}

export function UserManagement({ className }: UserManagementProps) {
  const { currentTenant } = useTenant();
  const router = useRouter();
  
  // State management
  const [users, setUsers] = useState<UserWithTenantInfo[]>([]);
  const [filteredUsers, setFilteredUsers] = useState<UserWithTenantInfo[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [roleFilter, setRoleFilter] = useState('all');
  const [statusFilter, setStatusFilter] = useState('all');
  const [teamFilter, setTeamFilter] = useState('all');
  const [selectedUsers, setSelectedUsers] = useState<Set<string>>(new Set());
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [userToDelete, setUserToDelete] = useState<UserWithTenantInfo | null>(null);
  const [availableRoles, setAvailableRoles] = useState<string[]>([]);
  const [availableTeams, setAvailableTeams] = useState<Array<{id: string, name: string}>>([]);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    total: 0,
    totalPages: 0
  });

  // Fetch users function
  const fetchUsers = useCallback(async () => {
    if (!currentTenant) return;

    try {
      setIsLoading(true);
      setError(null);

      const params = new URLSearchParams({
        page: pagination.page.toString(),
        limit: pagination.limit.toString(),
        ...(searchTerm && { search: searchTerm }),
        ...(roleFilter !== 'all' && { role: roleFilter }),
        ...(statusFilter !== 'all' && { status: statusFilter })
      });

      const response = await fetch(`/api/users?${params}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch users');
      }

      const data: UserListResponse = await response.json();
      
      if (data.success) {
        setUsers(data.data);
        setPagination(data.pagination);
      } else {
        throw new Error('Failed to fetch users');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch users');
    } finally {
      setIsLoading(false);
    }
  }, [currentTenant, pagination.page, searchTerm, roleFilter, statusFilter]);

  // Fetch filter options
  const fetchFilterOptions = useCallback(async () => {
    if (!currentTenant) return;

    try {
      const [rolesResponse, teamsResponse] = await Promise.all([
        fetch('/api/roles'),
        fetch('/api/teams')
      ]);

      if (rolesResponse.ok) {
        const rolesData = await rolesResponse.json();
        if (rolesData.success && rolesData.data?.roles) {
          setAvailableRoles(rolesData.data.roles.map((role: any) => role.name));
        } else {
          console.warn('Roles API returned unexpected format:', rolesData);
          setAvailableRoles([]);
        }
      } else {
        console.error('Failed to fetch roles:', rolesResponse.status);
        setAvailableRoles([]);
      }

      if (teamsResponse.ok) {
        const teamsData = await teamsResponse.json();
        if (teamsData.success && teamsData.data?.teams) {
          setAvailableTeams(teamsData.data.teams);
        } else {
          console.warn('Teams API returned unexpected format:', teamsData);
          setAvailableTeams([]);
        }
      } else {
        console.error('Failed to fetch teams:', teamsResponse.status);
        setAvailableTeams([]);
      }
    } catch (error) {
      console.error('Failed to fetch filter options:', error);
    }
  }, [currentTenant]);

  // Apply filters
  const applyFilters = useCallback(() => {
    let filtered = [...users];

    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(user =>
        user.firstName?.toLowerCase().includes(term) ||
        user.lastName?.toLowerCase().includes(term) ||
        user.email.toLowerCase().includes(term) ||
        user.tenantUser.department?.toLowerCase().includes(term) ||
        user.tenantUser.jobTitle?.toLowerCase().includes(term)
      );
    }

    if (roleFilter !== 'all') {
      filtered = filtered.filter(user => user.tenantUser.role === roleFilter);
    }

    if (statusFilter !== 'all') {
      filtered = filtered.filter(user => user.tenantUser.status === statusFilter);
    }

    if (teamFilter !== 'all') {
      filtered = filtered.filter(user => user.tenantUser.teamId === teamFilter);
    }

    setFilteredUsers(filtered);
  }, [users, searchTerm, roleFilter, statusFilter, teamFilter]);

  // Effects
  useEffect(() => {
    if (currentTenant) {
      fetchUsers();
      fetchFilterOptions();
    }
  }, [currentTenant, pagination.page]);

  useEffect(() => {
    applyFilters();
  }, [users, searchTerm, roleFilter, statusFilter, teamFilter, applyFilters]);

  // Event handlers
  const handleEditUser = (userId: string) => {
    router.push(`/settings/users/${userId}/edit`);
  };

  const handleViewUser = (userId: string) => {
    router.push(`/settings/users/${userId}`);
  };

  const handleDeleteUser = (user: UserWithTenantInfo) => {
    setUserToDelete(user);
    setShowDeleteDialog(true);
  };

  const confirmDeleteUser = async () => {
    if (!userToDelete) return;

    try {
      const response = await fetch(`/api/users/${userToDelete.id}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        await fetchUsers();
        setShowDeleteDialog(false);
        setUserToDelete(null);
      } else {
        setError('Failed to delete user');
      }
    } catch (err) {
      setError('Failed to delete user');
    }
  };

  const toggleUserSelection = (userId: string) => {
    const newSelection = new Set(selectedUsers);
    if (newSelection.has(userId)) {
      newSelection.delete(userId);
    } else {
      newSelection.add(userId);
    }
    setSelectedUsers(newSelection);
  };

  const toggleSelectAll = () => {
    if (selectedUsers.size === filteredUsers.length) {
      setSelectedUsers(new Set());
    } else {
      setSelectedUsers(new Set(filteredUsers.map(user => user.id)));
    }
  };

  if (!currentTenant) {
    return (
      <div className="flex justify-center py-8">
        <LoadingSpinner />
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className || ''}`}>
      {/* Search and Filters */}
      <Card>
        <CardContent className="p-6">
          <div className="space-y-4">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <input
                    type="text"
                    placeholder="Search users by name, email, department, or job title..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
              </div>
            </div>

            <div className="flex flex-wrap gap-4">
              {/* Role Filter */}
              <div className="relative">
                <select
                  value={roleFilter}
                  onChange={(e) => setRoleFilter(e.target.value)}
                  className="appearance-none bg-white border border-gray-300 rounded-md px-4 py-2 pr-8 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="all">All Roles</option>
                  {availableRoles.map(role => (
                    <option key={role} value={role}>{role}</option>
                  ))}
                </select>
                <ChevronDownIcon className="absolute right-2 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 pointer-events-none" />
              </div>

              {/* Status Filter */}
              <div className="relative">
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="appearance-none bg-white border border-gray-300 rounded-md px-4 py-2 pr-8 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="all">All Statuses</option>
                  <option value="active">Active</option>
                  <option value="inactive">Inactive</option>
                  <option value="pending">Pending</option>
                </select>
                <ChevronDownIcon className="absolute right-2 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 pointer-events-none" />
              </div>

              {/* Team Filter */}
              <div className="relative">
                <select
                  value={teamFilter}
                  onChange={(e) => setTeamFilter(e.target.value)}
                  className="appearance-none bg-white border border-gray-300 rounded-md px-4 py-2 pr-8 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="all">All Teams</option>
                  {availableTeams.map(team => (
                    <option key={team.id} value={team.id}>{team.name}</option>
                  ))}
                </select>
                <ChevronDownIcon className="absolute right-2 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 pointer-events-none" />
              </div>

              {/* Clear Filters */}
              {(searchTerm || roleFilter !== 'all' || statusFilter !== 'all' || teamFilter !== 'all') && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    setSearchTerm('');
                    setRoleFilter('all');
                    setStatusFilter('all');
                    setTeamFilter('all');
                  }}
                  className="flex items-center gap-2"
                >
                  <XMarkIcon className="w-4 h-4" />
                  Clear Filters
                </Button>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Users List */}
      {isLoading ? (
        <div className="flex justify-center py-8">
          <LoadingSpinner />
        </div>
      ) : error ? (
        <div className="text-center py-8">
          <ExclamationTriangleIcon className="w-12 h-12 text-red-400 mx-auto mb-4" />
          <p className="text-red-600">{error}</p>
          <Button onClick={fetchUsers} className="mt-4">
            Try Again
          </Button>
        </div>
      ) : (
        <UsersList
          users={filteredUsers}
          selectedUsers={selectedUsers}
          onEdit={handleEditUser}
          onView={handleViewUser}
          onDelete={handleDeleteUser}
          onToggleSelection={toggleUserSelection}
          onToggleSelectAll={toggleSelectAll}
        />
      )}

      {/* Delete Confirmation Dialog */}
      <ConfirmationDialog
        isOpen={showDeleteDialog}
        onClose={() => setShowDeleteDialog(false)}
        onConfirm={confirmDeleteUser}
        title="Delete User"
        description={`Are you sure you want to delete ${userToDelete?.firstName} ${userToDelete?.lastName}? This action cannot be undone.`}
        confirmText="Delete"
        variant="danger"
      />
    </div>
  );
}

interface UsersListProps {
  users: UserWithTenantInfo[];
  selectedUsers: Set<string>;
  onEdit: (userId: string) => void;
  onView: (userId: string) => void;
  onDelete: (user: UserWithTenantInfo) => void;
  onToggleSelection: (userId: string) => void;
  onToggleSelectAll: () => void;
}

function UsersList({
  users,
  selectedUsers,
  onEdit,
  onView,
  onDelete,
  onToggleSelection,
  onToggleSelectAll
}: UsersListProps) {
  if (users.length === 0) {
    return (
      <Card>
        <CardContent className="p-12 text-center">
          <UserIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No users found</h3>
          <p className="text-gray-500">Get started by creating your first user or adjusting your filters.</p>
        </CardContent>
      </Card>
    );
  }

  const allSelected = users.length > 0 && selectedUsers.size === users.length;
  const someSelected = selectedUsers.size > 0 && selectedUsers.size < users.length;

  return (
    <Card>
      <CardContent className="p-0">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  <input
                    type="checkbox"
                    checked={allSelected}
                    ref={(input) => {
                      if (input) input.indeterminate = someSelected;
                    }}
                    onChange={onToggleSelectAll}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  User
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Role
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Department
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Last Active
                </th>
                <th className="relative px-6 py-3">
                  <span className="sr-only">Actions</span>
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {users.map((user) => (
                <UserRow
                  key={user.id}
                  user={user}
                  isSelected={selectedUsers.has(user.id)}
                  onEdit={() => onEdit(user.id)}
                  onView={() => onView(user.id)}
                  onDelete={() => onDelete(user)}
                  onToggleSelection={() => onToggleSelection(user.id)}
                />
              ))}
            </tbody>
          </table>
        </div>
      </CardContent>
    </Card>
  );
}

interface UserRowProps {
  user: UserWithTenantInfo;
  isSelected: boolean;
  onEdit: () => void;
  onView: () => void;
  onDelete: () => void;
  onToggleSelection: () => void;
}

function UserRow({ user, isSelected, onEdit, onView, onDelete, onToggleSelection }: UserRowProps) {
  const getStatusBadge = (status: string) => {
    const statusConfig = {
      active: 'bg-green-100 text-green-800',
      inactive: 'bg-gray-100 text-gray-800',
      pending: 'bg-yellow-100 text-yellow-800',
    };
    return statusConfig[status as keyof typeof statusConfig] || statusConfig.active;
  };

  const formatLastActive = (date: Date | string | null) => {
    if (!date) return 'Never';
    const lastActive = date instanceof Date ? date : new Date(date);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - lastActive.getTime()) / (1000 * 60 * 60));

    if (diffInHours < 1) return 'Just now';
    if (diffInHours < 24) return `${diffInHours}h ago`;
    if (diffInHours < 168) return `${Math.floor(diffInHours / 24)}d ago`;
    return lastActive.toLocaleDateString();
  };

  return (
    <tr className={isSelected ? 'bg-blue-50' : 'hover:bg-gray-50'}>
      <td className="px-6 py-4 whitespace-nowrap">
        <input
          type="checkbox"
          checked={isSelected}
          onChange={onToggleSelection}
          className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
        />
      </td>
      <td className="px-6 py-4 whitespace-nowrap">
        <div className="flex items-center">
          <div className="flex-shrink-0 h-10 w-10">
            <div className="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
              <UserIcon className="h-6 w-6 text-gray-600" />
            </div>
          </div>
          <div className="ml-4">
            <div className="text-sm font-medium text-gray-900">
              {user.firstName} {user.lastName}
            </div>
            <div className="text-sm text-gray-500">{user.email}</div>
          </div>
        </div>
      </td>
      <td className="px-6 py-4 whitespace-nowrap">
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
          {user.tenantUser.role}
        </span>
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
        <div>
          <div className="font-medium">{user.tenantUser.department || 'Not assigned'}</div>
          <div className="text-gray-500">{user.tenantUser.jobTitle || 'No title'}</div>
        </div>
      </td>
      <td className="px-6 py-4 whitespace-nowrap">
        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusBadge(user.tenantUser.status)}`}>
          {user.tenantUser.status}
        </span>
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
        {formatLastActive(user.lastLogin)}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
        <div className="flex items-center justify-end space-x-2">
          <PermissionGate
            resource={PermissionResource.USERS}
            action={PermissionAction.READ}
          >
            <button
              onClick={onView}
              className="text-blue-600 hover:text-blue-900 p-1 rounded"
              title="View user"
            >
              <UserIcon className="h-4 w-4" />
            </button>
          </PermissionGate>

          <PermissionGate
            resource={PermissionResource.USERS}
            action={PermissionAction.UPDATE}
          >
            <button
              onClick={onEdit}
              className="text-gray-600 hover:text-gray-900 p-1 rounded"
              title="Edit user"
            >
              <PencilIcon className="h-4 w-4" />
            </button>
          </PermissionGate>

          <PermissionGate
            resource={PermissionResource.USERS}
            action={PermissionAction.DELETE}
          >
            <button
              onClick={onDelete}
              className="text-red-600 hover:text-red-900 p-1 rounded"
              title="Delete user"
            >
              <TrashIcon className="h-4 w-4" />
            </button>
          </PermissionGate>
        </div>
      </td>
    </tr>
  );
}
