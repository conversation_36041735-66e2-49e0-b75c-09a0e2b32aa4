import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { TaskManagementService, createTaskSchema } from '@/services/task-management';
import { withPermission, AuthenticatedRequest } from '@/lib/permission-middleware';
import { PermissionResource, PermissionAction } from '@/types';

const taskService = new TaskManagementService();

// Query parameters schema for GET requests
const getTasksQuerySchema = z.object({
  page: z.string().optional().transform(val => val ? parseInt(val, 10) : 1),
  limit: z.string().optional().transform(val => val ? parseInt(val, 10) : 20),
  status: z.string().optional(),
  assigneeId: z.string().optional(),
  priority: z.string().optional(),
  search: z.string().optional(),
  includeSubTasks: z.string().optional().transform(val => val !== 'false'),
});

interface RouteParams {
  params: Promise<{
    id: string;
  }>;
}

/**
 * GET /api/projects/[id]/tasks
 * Get all tasks for a specific project
 */
export const GET = withPermission({
  resource: PermissionResource.PROJECTS,
  action: PermissionAction.READ,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest, context: RouteParams) => {
  try {
    const { id: projectId } = await context.params;
    const { searchParams } = new URL(req.url);
    const queryParams = Object.fromEntries(searchParams.entries());
    
    const validatedQuery = getTasksQuerySchema.parse(queryParams);

    const result = await taskService.getProjectTasks(
      projectId,
      req.tenantId,
      validatedQuery
    );

    return NextResponse.json({
      success: true,
      data: result,
      message: 'Tasks retrieved successfully'
    });
  } catch (error) {
    console.error('Get project tasks error:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid query parameters', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to fetch tasks' },
      { status: 500 }
    );
  }
});

/**
 * POST /api/projects/[id]/tasks
 * Create a new task for a specific project
 */
export const POST = withPermission({
  resource: PermissionResource.PROJECTS,
  action: PermissionAction.UPDATE, // Creating tasks requires project update permission
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest, context: RouteParams) => {
  try {
    const { id: projectId } = await context.params;
    const body = await req.json();
    const validatedData = createTaskSchema.parse(body);

    const task = await taskService.createTask(
      projectId,
      req.tenantId,
      validatedData,
      req.userId
    );

    return NextResponse.json({
      success: true,
      data: { task },
      message: 'Task created successfully'
    }, { status: 201 });
  } catch (error) {
    console.error('Create task error:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid task data', details: error.errors },
        { status: 400 }
      );
    }

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to create task' },
      { status: 500 }
    );
  }
});
