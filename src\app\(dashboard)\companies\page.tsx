'use client';

import { CompanyManagement } from '@/components/companies/company-management';
import { PermissionGate } from '@/components/ui/permission-gate';
import { PermissionResource, PermissionAction } from '@/types';
import { Card, CardContent } from '@/components/ui/card';
import { EmptyState } from '@/components/ui/empty-state';
import { ExclamationTriangleIcon, PlusIcon, ArrowDownTrayIcon, ArrowUpTrayIcon } from '@heroicons/react/24/outline';
import { PageLayout } from '@/components/layout/page-layout';
import { Button } from '@/components/ui/button';
import { useRouter } from 'next/navigation';

export default function CompaniesPage() {
  const router = useRouter();

  const handleExport = async () => {
    try {
      const response = await fetch('/api/companies/export?format=csv');
      if (response.ok) {
        const blob = await response.blob();
        const filename = `companies-export-${new Date().toISOString().split('T')[0]}.csv`;

        if (typeof window !== 'undefined') {
          const url = window.URL.createObjectURL(blob);
          const a = window.document.createElement('a');
          a.href = url;
          a.download = filename;
          window.document.body.appendChild(a);
          a.click();
          window.URL.revokeObjectURL(url);
          window.document.body.removeChild(a);
        }
      }
    } catch (err) {
      console.error('Export error:', err);
    }
  };

  const actions = (
    <div className="flex items-center gap-2">
      <Button variant="outline" size="sm" onClick={handleExport}>
        <ArrowDownTrayIcon className="h-4 w-4 mr-2" />
        Export
      </Button>
      <Button variant="outline" size="sm" onClick={() => router.push('/companies/import')}>
        <ArrowUpTrayIcon className="h-4 w-4 mr-2" />
        Import
      </Button>
      <PermissionGate
        resource={PermissionResource.COMPANIES}
        action={PermissionAction.CREATE}
      >
        <Button size="sm" onClick={() => router.push('/companies/new')}>
          <PlusIcon className="h-4 w-4 mr-2" />
          Add Company
        </Button>
      </PermissionGate>
    </div>
  );

  return (
    <PermissionGate
      resource={PermissionResource.COMPANIES}
      action={PermissionAction.READ}
      fallback={
        <PageLayout>
          <Card>
            <CardContent className="p-8">
              <EmptyState
                icon={<ExclamationTriangleIcon className="w-12 h-12 text-muted-foreground" />}
                title="Access Denied"
                description="You don't have permission to view companies. Please contact your administrator for access."
                action={{
                  label: 'Back to Dashboard',
                  onClick: () => window.location.href = '/dashboard',
                  variant: 'primary'
                }}
              />
            </CardContent>
          </Card>
        </PageLayout>
      }
    >
      <PageLayout
        title="Companies"
        description="Manage your company relationships and business accounts"
        actions={actions}
      >
        <CompanyManagement />
      </PageLayout>
    </PermissionGate>
  );
}
