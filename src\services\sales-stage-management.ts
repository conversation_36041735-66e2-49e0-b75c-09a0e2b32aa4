import { prisma } from '@/lib/prisma';
import { SalesStage } from '@prisma/client';
import { z } from 'zod';

// Validation schemas
export const createSalesStageSchema = z.object({
  name: z.string().min(1, 'Name is required').max(100),
  description: z.string().optional().nullable(),
  orderIndex: z.number().min(1, 'Order index must be positive'),
  color: z.string().regex(/^#[0-9A-F]{6}$/i, 'Color must be a valid hex color').default('#6B7280'),
  probabilityMin: z.number().min(0).max(100).default(0),
  probabilityMax: z.number().min(0).max(100).default(100),
  isClosed: z.boolean().default(false),
  isWon: z.boolean().default(false),
});

export const updateSalesStageSchema = createSalesStageSchema.partial();

export const reorderStagesSchema = z.object({
  stageOrders: z.array(z.object({
    id: z.string(),
    orderIndex: z.number().min(1)
  }))
});

// Types
export interface SalesStageWithStats extends SalesStage {
  _count?: {
    opportunities: number;
  };
  totalValue?: number;
  averageValue?: number;
}

export class SalesStageManagementService {
  /**
   * Get all sales stages for a tenant
   */
  async getSalesStages(tenantId: string): Promise<SalesStageWithStats[]> {
    // Ensure default stages exist
    await this.ensureDefaultStagesExist(tenantId);

    const stages = await prisma.salesStage.findMany({
      where: { tenantId },
      orderBy: { orderIndex: 'asc' }
    });

    // Get opportunity counts and values for each stage
    const stagesWithStats = await Promise.all(
      stages.map(async (stage) => {
        const opportunities = await prisma.opportunity.findMany({
          where: { tenantId, stage: stage.name },
          select: { value: true }
        });

        const totalValue = opportunities.reduce((sum, opp) =>
          sum + (opp.value ? Number(opp.value) : 0), 0
        );

        return {
          ...stage,
          _count: { opportunities: opportunities.length },
          totalValue,
          averageValue: opportunities.length > 0 ? totalValue / opportunities.length : 0
        };
      })
    );

    return stagesWithStats;
  }

  /**
   * Get a single sales stage
   */
  async getSalesStage(stageId: string, tenantId: string): Promise<SalesStageWithStats | null> {
    const stage = await prisma.salesStage.findFirst({
      where: { id: stageId, tenantId }
    });

    if (!stage) return null;

    // Get opportunity stats
    const opportunities = await prisma.opportunity.findMany({
      where: { tenantId, stage: stage.name },
      select: { value: true }
    });

    const totalValue = opportunities.reduce((sum, opp) => 
      sum + (opp.value ? Number(opp.value) : 0), 0
    );

    return {
      ...stage,
      _count: { opportunities: opportunities.length },
      totalValue,
      averageValue: opportunities.length > 0 ? totalValue / opportunities.length : 0
    };
  }

  /**
   * Create a new sales stage
   */
  async createSalesStage(
    tenantId: string,
    stageData: z.infer<typeof createSalesStageSchema>
  ): Promise<SalesStage> {
    const validatedData = createSalesStageSchema.parse(stageData);

    // Validate probability range
    if (validatedData.probabilityMin > validatedData.probabilityMax) {
      throw new Error('Minimum probability cannot be greater than maximum probability');
    }

    // Check if name already exists for tenant
    const existingStage = await prisma.salesStage.findFirst({
      where: { tenantId, name: validatedData.name }
    });

    if (existingStage) {
      throw new Error('A stage with this name already exists');
    }

    // Check if order index already exists
    const existingOrder = await prisma.salesStage.findFirst({
      where: { tenantId, orderIndex: validatedData.orderIndex }
    });

    if (existingOrder) {
      // Shift existing stages to make room
      await this.shiftStagesOrder(tenantId, validatedData.orderIndex, 'increment');
    }

    return await prisma.salesStage.create({
      data: {
        ...validatedData,
        tenantId
      }
    });
  }

  /**
   * Update a sales stage
   */
  async updateSalesStage(
    stageId: string,
    tenantId: string,
    updateData: z.infer<typeof updateSalesStageSchema>
  ): Promise<SalesStage> {
    const validatedData = updateSalesStageSchema.parse(updateData);

    const existingStage = await prisma.salesStage.findFirst({
      where: { id: stageId, tenantId }
    });

    if (!existingStage) {
      throw new Error('Sales stage not found');
    }

    // Validate probability range if both are provided
    if (validatedData.probabilityMin !== undefined && validatedData.probabilityMax !== undefined) {
      if (validatedData.probabilityMin > validatedData.probabilityMax) {
        throw new Error('Minimum probability cannot be greater than maximum probability');
      }
    }

    // Check if name already exists for another stage
    if (validatedData.name && validatedData.name !== existingStage.name) {
      const nameExists = await prisma.salesStage.findFirst({
        where: { 
          tenantId, 
          name: validatedData.name,
          id: { not: stageId }
        }
      });

      if (nameExists) {
        throw new Error('A stage with this name already exists');
      }
    }

    // Handle order index change
    if (validatedData.orderIndex && validatedData.orderIndex !== existingStage.orderIndex) {
      const orderExists = await prisma.salesStage.findFirst({
        where: { 
          tenantId, 
          orderIndex: validatedData.orderIndex,
          id: { not: stageId }
        }
      });

      if (orderExists) {
        // Reorder stages
        await this.reorderStagesForUpdate(tenantId, stageId, existingStage.orderIndex, validatedData.orderIndex);
      }
    }

    const updatedStage = await prisma.salesStage.update({
      where: { id: stageId, tenantId },
      data: validatedData
    });

    // If stage name changed, update all opportunities using this stage
    if (validatedData.name && validatedData.name !== existingStage.name) {
      await prisma.opportunity.updateMany({
        where: { tenantId, stage: existingStage.name },
        data: { stage: validatedData.name }
      });
    }

    return updatedStage;
  }

  /**
   * Delete a sales stage
   */
  async deleteSalesStage(stageId: string, tenantId: string): Promise<void> {
    const stage = await prisma.salesStage.findFirst({
      where: { id: stageId, tenantId }
    });

    if (!stage) {
      throw new Error('Sales stage not found');
    }

    // Check if stage has opportunities
    const opportunityCount = await prisma.opportunity.count({
      where: { tenantId, stage: stage.name }
    });

    if (opportunityCount > 0) {
      throw new Error(`Cannot delete stage '${stage.name}' because it has ${opportunityCount} opportunities. Move or delete the opportunities first.`);
    }

    await prisma.salesStage.delete({
      where: { id: stageId, tenantId }
    });

    // Reorder remaining stages to fill the gap
    await this.shiftStagesOrder(tenantId, stage.orderIndex, 'decrement');
  }

  /**
   * Reorder sales stages
   */
  async reorderSalesStages(
    tenantId: string,
    reorderData: z.infer<typeof reorderStagesSchema>
  ): Promise<SalesStage[]> {
    const { stageOrders } = reorderStagesSchema.parse(reorderData);

    // Verify all stages belong to the tenant
    const stageIds = stageOrders.map(s => s.id);
    const existingStages = await prisma.salesStage.findMany({
      where: { tenantId, id: { in: stageIds } }
    });

    if (existingStages.length !== stageIds.length) {
      throw new Error('One or more stages not found');
    }

    // Update order indexes
    await Promise.all(
      stageOrders.map(({ id, orderIndex }) =>
        prisma.salesStage.update({
          where: { id, tenantId },
          data: { orderIndex }
        })
      )
    );

    // Return updated stages
    return await prisma.salesStage.findMany({
      where: { tenantId },
      orderBy: { orderIndex: 'asc' }
    });
  }

  /**
   * Get stage progression analytics
   */
  async getStageProgressionAnalytics(tenantId: string, dateRange?: { from: Date; to: Date }) {
    const where: any = { tenantId };
    
    if (dateRange) {
      where.changedAt = {
        gte: dateRange.from,
        lte: dateRange.to
      };
    }

    const stageHistory = await prisma.opportunityStageHistory.findMany({
      where,
      include: {
        opportunity: {
          select: {
            id: true,
            title: true,
            value: true
          }
        }
      },
      orderBy: { changedAt: 'asc' }
    });

    // Analyze stage progression patterns
    const progressionMap = new Map<string, { count: number; totalValue: number; averageTime: number }>();
    
    stageHistory.forEach(history => {
      if (history.fromStage && history.toStage) {
        const key = `${history.fromStage} → ${history.toStage}`;
        const existing = progressionMap.get(key) || { count: 0, totalValue: 0, averageTime: 0 };
        
        progressionMap.set(key, {
          count: existing.count + 1,
          totalValue: existing.totalValue + (Number(history.opportunity?.value) || 0),
          averageTime: existing.averageTime // TODO: Calculate time between stages
        });
      }
    });

    return Array.from(progressionMap.entries()).map(([progression, stats]) => ({
      progression,
      ...stats,
      averageValue: stats.count > 0 ? stats.totalValue / stats.count : 0
    }));
  }

  /**
   * Ensure default stages exist for a tenant
   */
  async ensureDefaultStagesExist(tenantId: string): Promise<void> {
    const existingStages = await prisma.salesStage.findMany({
      where: { tenantId }
    });

    // If no stages exist, create default ones
    if (existingStages.length === 0) {
      console.log(`No sales stages found for tenant ${tenantId}, creating defaults...`);

      // Import default stages from seed script
      const { DEFAULT_SALES_STAGES } = require('../../scripts/seed-sales-stages.js');

      const stagesToCreate = DEFAULT_SALES_STAGES.map((stage: any) => ({
        ...stage,
        tenantId
      }));

      await prisma.salesStage.createMany({
        data: stagesToCreate
      });

      console.log(`Created ${stagesToCreate.length} default sales stages for tenant ${tenantId}`);
    }
  }

  /**
   * Reset stages to default configuration
   */
  async resetToDefaultStages(tenantId: string): Promise<SalesStage[]> {
    // Delete existing stages (this will fail if they have opportunities)
    const existingStages = await prisma.salesStage.findMany({
      where: { tenantId }
    });

    for (const stage of existingStages) {
      const opportunityCount = await prisma.opportunity.count({
        where: { tenantId, stage: stage.name }
      });

      if (opportunityCount > 0) {
        throw new Error(`Cannot reset stages because stage '${stage.name}' has ${opportunityCount} opportunities`);
      }
    }

    // Delete all existing stages
    await prisma.salesStage.deleteMany({
      where: { tenantId }
    });

    // Import default stages from seed script
    const { DEFAULT_SALES_STAGES } = require('../../scripts/seed-sales-stages.js');
    
    const stagesToCreate = DEFAULT_SALES_STAGES.map((stage: any) => ({
      ...stage,
      tenantId
    }));

    await prisma.salesStage.createMany({
      data: stagesToCreate
    });

    return await prisma.salesStage.findMany({
      where: { tenantId },
      orderBy: { orderIndex: 'asc' }
    });
  }

  // Helper methods
  private async shiftStagesOrder(tenantId: string, fromIndex: number, direction: 'increment' | 'decrement') {
    const stages = await prisma.salesStage.findMany({
      where: { 
        tenantId,
        orderIndex: direction === 'increment' ? { gte: fromIndex } : { gt: fromIndex }
      }
    });

    await Promise.all(
      stages.map(stage =>
        prisma.salesStage.update({
          where: { id: stage.id },
          data: { 
            orderIndex: direction === 'increment' 
              ? stage.orderIndex + 1 
              : stage.orderIndex - 1 
          }
        })
      )
    );
  }

  private async reorderStagesForUpdate(
    tenantId: string, 
    stageId: string, 
    oldIndex: number, 
    newIndex: number
  ) {
    if (oldIndex < newIndex) {
      // Moving down: shift stages between old and new position up
      await prisma.salesStage.updateMany({
        where: {
          tenantId,
          orderIndex: { gt: oldIndex, lte: newIndex },
          id: { not: stageId }
        },
        data: {
          orderIndex: { decrement: 1 }
        }
      });
    } else {
      // Moving up: shift stages between new and old position down
      await prisma.salesStage.updateMany({
        where: {
          tenantId,
          orderIndex: { gte: newIndex, lt: oldIndex },
          id: { not: stageId }
        },
        data: {
          orderIndex: { increment: 1 }
        }
      });
    }
  }
}

export const salesStageManagementService = new SalesStageManagementService();
